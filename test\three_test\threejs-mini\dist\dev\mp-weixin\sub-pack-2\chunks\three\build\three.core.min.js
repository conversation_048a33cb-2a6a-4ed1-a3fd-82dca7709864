"use strict";
/**
 * @license
 * Copyright 2010-2024 Three.js Authors
 * SPDX-License-Identifier: MIT
 */
const t = "172", e = { ROTATE: 0, DOLLY: 1, PAN: 2 }, s = { ROTATE: 0, PAN: 1, DOLLY_PAN: 2, DOLLY_ROTATE: 3 }, i = 0, r = 1, n = 2, h = 1, l = 2, c = 3, u = 0, d = 1, p = 2, m = 0, y = 1, f = 2, g = 3, x = 4, b = 5, v = 100, w = 101, M = 102, S = 103, _ = 104, A = 200, T = 201, z = 202, C = 203, I = 204, B = 205, k = 206, R = 207, E = 208, P = 209, O = 210, F = 211, N = 212, L = 213, V = 214, W = 0, j = 1, U = 2, D = 3, H = 4, q = 5, J = 6, X = 7, Y = 0, Z = 1, G = 2, $ = 0, Q = 1, K = 2, tt = 3, et = 4, st = 5, it = 6, rt = 7, nt = "attached", ot = "detached", at = 300, ht = 301, lt = 302, ct = 303, ut = 304, dt = 306, pt = 1e3, mt = 1001, yt = 1002, ft = 1003, gt = 1004, bt = 1005, wt = 1006, Mt = 1007, _t = 1008, Tt = 1009, zt = 1010, Ct = 1011, It = 1012, Bt = 1013, kt = 1014, Rt = 1015, Et = 1016, Pt = 1017, Ot = 1018, Ft = 1020, Nt = 35902, Lt = 1021, Vt = 1022, Wt = 1023, jt = 1024, Ut = 1025, Dt = 1026, Ht = 1027, qt = 1028, Jt = 1029, Xt = 1030, Yt = 1031, Gt = 1033, $t = 33776, Qt = 33777, Kt = 33778, te = 33779, ee = 35840, se = 35841, ie = 35842, re = 35843, ne = 36196, oe = 37492, ae = 37496, he = 37808, le = 37809, ce = 37810, ue = 37811, de = 37812, pe = 37813, me = 37814, ye = 37815, fe = 37816, ge = 37817, xe = 37818, be = 37819, ve = 37820, we = 37821, Me = 36492, Se = 36494, _e = 36495, Ae = 36283, Te = 36284, ze = 36285, Ce = 36286, Re = 2300, Ee = 2301, Pe = 2302, Oe = 2400, Fe = 2401, Ne = 2402, We = 0, je = 1, Ue = 2, He = 3201, Xe = 0, Ye = 1, Ze = "", Ge = "srgb", $e = "srgb-linear", Qe = "linear", Ke = "srgb", es = 7680, fs = 512, gs = 513, xs = 514, bs = 515, vs = 516, ws = 517, Ms = 518, Ss = 519, _s = 35044, Ps = "300 es", Os = 2e3, Fs = 2001;
class Ns {
  addEventListener(t2, e2) {
    void 0 === this._listeners && (this._listeners = {});
    const s2 = this._listeners;
    void 0 === s2[t2] && (s2[t2] = []), -1 === s2[t2].indexOf(e2) && s2[t2].push(e2);
  }
  hasEventListener(t2, e2) {
    if (void 0 === this._listeners) return false;
    const s2 = this._listeners;
    return void 0 !== s2[t2] && -1 !== s2[t2].indexOf(e2);
  }
  removeEventListener(t2, e2) {
    if (void 0 === this._listeners) return;
    const s2 = this._listeners[t2];
    if (void 0 !== s2) {
      const t3 = s2.indexOf(e2);
      -1 !== t3 && s2.splice(t3, 1);
    }
  }
  dispatchEvent(t2) {
    if (void 0 === this._listeners) return;
    const e2 = this._listeners[t2.type];
    if (void 0 !== e2) {
      t2.target = this;
      const s2 = e2.slice(0);
      for (let e3 = 0, i2 = s2.length; e3 < i2; e3++) s2[e3].call(this, t2);
      t2.target = null;
    }
  }
}
const Ls = ["00", "01", "02", "03", "04", "05", "06", "07", "08", "09", "0a", "0b", "0c", "0d", "0e", "0f", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "1a", "1b", "1c", "1d", "1e", "1f", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "2a", "2b", "2c", "2d", "2e", "2f", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "3a", "3b", "3c", "3d", "3e", "3f", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "4a", "4b", "4c", "4d", "4e", "4f", "50", "51", "52", "53", "54", "55", "56", "57", "58", "59", "5a", "5b", "5c", "5d", "5e", "5f", "60", "61", "62", "63", "64", "65", "66", "67", "68", "69", "6a", "6b", "6c", "6d", "6e", "6f", "70", "71", "72", "73", "74", "75", "76", "77", "78", "79", "7a", "7b", "7c", "7d", "7e", "7f", "80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "8a", "8b", "8c", "8d", "8e", "8f", "90", "91", "92", "93", "94", "95", "96", "97", "98", "99", "9a", "9b", "9c", "9d", "9e", "9f", "a0", "a1", "a2", "a3", "a4", "a5", "a6", "a7", "a8", "a9", "aa", "ab", "ac", "ad", "ae", "af", "b0", "b1", "b2", "b3", "b4", "b5", "b6", "b7", "b8", "b9", "ba", "bb", "bc", "bd", "be", "bf", "c0", "c1", "c2", "c3", "c4", "c5", "c6", "c7", "c8", "c9", "ca", "cb", "cc", "cd", "ce", "cf", "d0", "d1", "d2", "d3", "d4", "d5", "d6", "d7", "d8", "d9", "da", "db", "dc", "dd", "de", "df", "e0", "e1", "e2", "e3", "e4", "e5", "e6", "e7", "e8", "e9", "ea", "eb", "ec", "ed", "ee", "ef", "f0", "f1", "f2", "f3", "f4", "f5", "f6", "f7", "f8", "f9", "fa", "fb", "fc", "fd", "fe", "ff"];
let Vs = 1234567;
const Ws = Math.PI / 180, js = 180 / Math.PI;
function Us() {
  const t2 = 4294967295 * Math.random() | 0, e2 = 4294967295 * Math.random() | 0, s2 = 4294967295 * Math.random() | 0, i2 = 4294967295 * Math.random() | 0;
  return (Ls[255 & t2] + Ls[t2 >> 8 & 255] + Ls[t2 >> 16 & 255] + Ls[t2 >> 24 & 255] + "-" + Ls[255 & e2] + Ls[e2 >> 8 & 255] + "-" + Ls[e2 >> 16 & 15 | 64] + Ls[e2 >> 24 & 255] + "-" + Ls[63 & s2 | 128] + Ls[s2 >> 8 & 255] + "-" + Ls[s2 >> 16 & 255] + Ls[s2 >> 24 & 255] + Ls[255 & i2] + Ls[i2 >> 8 & 255] + Ls[i2 >> 16 & 255] + Ls[i2 >> 24 & 255]).toLowerCase();
}
function Ds(t2, e2, s2) {
  return Math.max(e2, Math.min(s2, t2));
}
function Hs(t2, e2) {
  return (t2 % e2 + e2) % e2;
}
function qs(t2, e2, s2) {
  return (1 - s2) * t2 + s2 * e2;
}
function Js(t2, e2) {
  switch (e2.constructor) {
    case Float32Array:
      return t2;
    case Uint32Array:
      return t2 / 4294967295;
    case Uint16Array:
      return t2 / 65535;
    case Uint8Array:
      return t2 / 255;
    case Int32Array:
      return Math.max(t2 / 2147483647, -1);
    case Int16Array:
      return Math.max(t2 / 32767, -1);
    case Int8Array:
      return Math.max(t2 / 127, -1);
    default:
      throw new Error("Invalid component type.");
  }
}
function Xs(t2, e2) {
  switch (e2.constructor) {
    case Float32Array:
      return t2;
    case Uint32Array:
      return Math.round(4294967295 * t2);
    case Uint16Array:
      return Math.round(65535 * t2);
    case Uint8Array:
      return Math.round(255 * t2);
    case Int32Array:
      return Math.round(2147483647 * t2);
    case Int16Array:
      return Math.round(32767 * t2);
    case Int8Array:
      return Math.round(127 * t2);
    default:
      throw new Error("Invalid component type.");
  }
}
const Ys = { DEG2RAD: Ws, RAD2DEG: js, generateUUID: Us, clamp: Ds, euclideanModulo: Hs, mapLinear: function(t2, e2, s2, i2, r2) {
  return i2 + (t2 - e2) * (r2 - i2) / (s2 - e2);
}, inverseLerp: function(t2, e2, s2) {
  return t2 !== e2 ? (s2 - t2) / (e2 - t2) : 0;
}, lerp: qs, damp: function(t2, e2, s2, i2) {
  return qs(t2, e2, 1 - Math.exp(-s2 * i2));
}, pingpong: function(t2, e2 = 1) {
  return e2 - Math.abs(Hs(t2, 2 * e2) - e2);
}, smoothstep: function(t2, e2, s2) {
  return t2 <= e2 ? 0 : t2 >= s2 ? 1 : (t2 = (t2 - e2) / (s2 - e2)) * t2 * (3 - 2 * t2);
}, smootherstep: function(t2, e2, s2) {
  return t2 <= e2 ? 0 : t2 >= s2 ? 1 : (t2 = (t2 - e2) / (s2 - e2)) * t2 * t2 * (t2 * (6 * t2 - 15) + 10);
}, randInt: function(t2, e2) {
  return t2 + Math.floor(Math.random() * (e2 - t2 + 1));
}, randFloat: function(t2, e2) {
  return t2 + Math.random() * (e2 - t2);
}, randFloatSpread: function(t2) {
  return t2 * (0.5 - Math.random());
}, seededRandom: function(t2) {
  void 0 !== t2 && (Vs = t2);
  let e2 = Vs += 1831565813;
  return e2 = Math.imul(e2 ^ e2 >>> 15, 1 | e2), e2 ^= e2 + Math.imul(e2 ^ e2 >>> 7, 61 | e2), ((e2 ^ e2 >>> 14) >>> 0) / 4294967296;
}, degToRad: function(t2) {
  return t2 * Ws;
}, radToDeg: function(t2) {
  return t2 * js;
}, isPowerOfTwo: function(t2) {
  return !(t2 & t2 - 1) && 0 !== t2;
}, ceilPowerOfTwo: function(t2) {
  return Math.pow(2, Math.ceil(Math.log(t2) / Math.LN2));
}, floorPowerOfTwo: function(t2) {
  return Math.pow(2, Math.floor(Math.log(t2) / Math.LN2));
}, setQuaternionFromProperEuler: function(t2, e2, s2, i2, r2) {
  const n2 = Math.cos, o = Math.sin, a = n2(s2 / 2), h2 = o(s2 / 2), l2 = n2((e2 + i2) / 2), c2 = o((e2 + i2) / 2), u2 = n2((e2 - i2) / 2), d2 = o((e2 - i2) / 2), p2 = n2((i2 - e2) / 2), m2 = o((i2 - e2) / 2);
  switch (r2) {
    case "XYX":
      t2.set(a * c2, h2 * u2, h2 * d2, a * l2);
      break;
    case "YZY":
      t2.set(h2 * d2, a * c2, h2 * u2, a * l2);
      break;
    case "ZXZ":
      t2.set(h2 * u2, h2 * d2, a * c2, a * l2);
      break;
    case "XZX":
      t2.set(a * c2, h2 * m2, h2 * p2, a * l2);
      break;
    case "YXY":
      t2.set(h2 * p2, a * c2, h2 * m2, a * l2);
      break;
    case "ZYZ":
      t2.set(h2 * m2, h2 * p2, a * c2, a * l2);
      break;
    default:
      console.warn("THREE.MathUtils: .setQuaternionFromProperEuler() encountered an unknown order: " + r2);
  }
}, normalize: Xs, denormalize: Js };
class Zs {
  constructor(t2 = 0, e2 = 0) {
    Zs.prototype.isVector2 = true, this.x = t2, this.y = e2;
  }
  get width() {
    return this.x;
  }
  set width(t2) {
    this.x = t2;
  }
  get height() {
    return this.y;
  }
  set height(t2) {
    this.y = t2;
  }
  set(t2, e2) {
    return this.x = t2, this.y = e2, this;
  }
  setScalar(t2) {
    return this.x = t2, this.y = t2, this;
  }
  setX(t2) {
    return this.x = t2, this;
  }
  setY(t2) {
    return this.y = t2, this;
  }
  setComponent(t2, e2) {
    switch (t2) {
      case 0:
        this.x = e2;
        break;
      case 1:
        this.y = e2;
        break;
      default:
        throw new Error("index is out of range: " + t2);
    }
    return this;
  }
  getComponent(t2) {
    switch (t2) {
      case 0:
        return this.x;
      case 1:
        return this.y;
      default:
        throw new Error("index is out of range: " + t2);
    }
  }
  clone() {
    return new this.constructor(this.x, this.y);
  }
  copy(t2) {
    return this.x = t2.x, this.y = t2.y, this;
  }
  add(t2) {
    return this.x += t2.x, this.y += t2.y, this;
  }
  addScalar(t2) {
    return this.x += t2, this.y += t2, this;
  }
  addVectors(t2, e2) {
    return this.x = t2.x + e2.x, this.y = t2.y + e2.y, this;
  }
  addScaledVector(t2, e2) {
    return this.x += t2.x * e2, this.y += t2.y * e2, this;
  }
  sub(t2) {
    return this.x -= t2.x, this.y -= t2.y, this;
  }
  subScalar(t2) {
    return this.x -= t2, this.y -= t2, this;
  }
  subVectors(t2, e2) {
    return this.x = t2.x - e2.x, this.y = t2.y - e2.y, this;
  }
  multiply(t2) {
    return this.x *= t2.x, this.y *= t2.y, this;
  }
  multiplyScalar(t2) {
    return this.x *= t2, this.y *= t2, this;
  }
  divide(t2) {
    return this.x /= t2.x, this.y /= t2.y, this;
  }
  divideScalar(t2) {
    return this.multiplyScalar(1 / t2);
  }
  applyMatrix3(t2) {
    const e2 = this.x, s2 = this.y, i2 = t2.elements;
    return this.x = i2[0] * e2 + i2[3] * s2 + i2[6], this.y = i2[1] * e2 + i2[4] * s2 + i2[7], this;
  }
  min(t2) {
    return this.x = Math.min(this.x, t2.x), this.y = Math.min(this.y, t2.y), this;
  }
  max(t2) {
    return this.x = Math.max(this.x, t2.x), this.y = Math.max(this.y, t2.y), this;
  }
  clamp(t2, e2) {
    return this.x = Ds(this.x, t2.x, e2.x), this.y = Ds(this.y, t2.y, e2.y), this;
  }
  clampScalar(t2, e2) {
    return this.x = Ds(this.x, t2, e2), this.y = Ds(this.y, t2, e2), this;
  }
  clampLength(t2, e2) {
    const s2 = this.length();
    return this.divideScalar(s2 || 1).multiplyScalar(Ds(s2, t2, e2));
  }
  floor() {
    return this.x = Math.floor(this.x), this.y = Math.floor(this.y), this;
  }
  ceil() {
    return this.x = Math.ceil(this.x), this.y = Math.ceil(this.y), this;
  }
  round() {
    return this.x = Math.round(this.x), this.y = Math.round(this.y), this;
  }
  roundToZero() {
    return this.x = Math.trunc(this.x), this.y = Math.trunc(this.y), this;
  }
  negate() {
    return this.x = -this.x, this.y = -this.y, this;
  }
  dot(t2) {
    return this.x * t2.x + this.y * t2.y;
  }
  cross(t2) {
    return this.x * t2.y - this.y * t2.x;
  }
  lengthSq() {
    return this.x * this.x + this.y * this.y;
  }
  length() {
    return Math.sqrt(this.x * this.x + this.y * this.y);
  }
  manhattanLength() {
    return Math.abs(this.x) + Math.abs(this.y);
  }
  normalize() {
    return this.divideScalar(this.length() || 1);
  }
  angle() {
    return Math.atan2(-this.y, -this.x) + Math.PI;
  }
  angleTo(t2) {
    const e2 = Math.sqrt(this.lengthSq() * t2.lengthSq());
    if (0 === e2) return Math.PI / 2;
    const s2 = this.dot(t2) / e2;
    return Math.acos(Ds(s2, -1, 1));
  }
  distanceTo(t2) {
    return Math.sqrt(this.distanceToSquared(t2));
  }
  distanceToSquared(t2) {
    const e2 = this.x - t2.x, s2 = this.y - t2.y;
    return e2 * e2 + s2 * s2;
  }
  manhattanDistanceTo(t2) {
    return Math.abs(this.x - t2.x) + Math.abs(this.y - t2.y);
  }
  setLength(t2) {
    return this.normalize().multiplyScalar(t2);
  }
  lerp(t2, e2) {
    return this.x += (t2.x - this.x) * e2, this.y += (t2.y - this.y) * e2, this;
  }
  lerpVectors(t2, e2, s2) {
    return this.x = t2.x + (e2.x - t2.x) * s2, this.y = t2.y + (e2.y - t2.y) * s2, this;
  }
  equals(t2) {
    return t2.x === this.x && t2.y === this.y;
  }
  fromArray(t2, e2 = 0) {
    return this.x = t2[e2], this.y = t2[e2 + 1], this;
  }
  toArray(t2 = [], e2 = 0) {
    return t2[e2] = this.x, t2[e2 + 1] = this.y, t2;
  }
  fromBufferAttribute(t2, e2) {
    return this.x = t2.getX(e2), this.y = t2.getY(e2), this;
  }
  rotateAround(t2, e2) {
    const s2 = Math.cos(e2), i2 = Math.sin(e2), r2 = this.x - t2.x, n2 = this.y - t2.y;
    return this.x = r2 * s2 - n2 * i2 + t2.x, this.y = r2 * i2 + n2 * s2 + t2.y, this;
  }
  random() {
    return this.x = Math.random(), this.y = Math.random(), this;
  }
  *[Symbol.iterator]() {
    yield this.x, yield this.y;
  }
}
class Gs {
  constructor(t2, e2, s2, i2, r2, n2, o, a, h2) {
    Gs.prototype.isMatrix3 = true, this.elements = [1, 0, 0, 0, 1, 0, 0, 0, 1], void 0 !== t2 && this.set(t2, e2, s2, i2, r2, n2, o, a, h2);
  }
  set(t2, e2, s2, i2, r2, n2, o, a, h2) {
    const l2 = this.elements;
    return l2[0] = t2, l2[1] = i2, l2[2] = o, l2[3] = e2, l2[4] = r2, l2[5] = a, l2[6] = s2, l2[7] = n2, l2[8] = h2, this;
  }
  identity() {
    return this.set(1, 0, 0, 0, 1, 0, 0, 0, 1), this;
  }
  copy(t2) {
    const e2 = this.elements, s2 = t2.elements;
    return e2[0] = s2[0], e2[1] = s2[1], e2[2] = s2[2], e2[3] = s2[3], e2[4] = s2[4], e2[5] = s2[5], e2[6] = s2[6], e2[7] = s2[7], e2[8] = s2[8], this;
  }
  extractBasis(t2, e2, s2) {
    return t2.setFromMatrix3Column(this, 0), e2.setFromMatrix3Column(this, 1), s2.setFromMatrix3Column(this, 2), this;
  }
  setFromMatrix4(t2) {
    const e2 = t2.elements;
    return this.set(e2[0], e2[4], e2[8], e2[1], e2[5], e2[9], e2[2], e2[6], e2[10]), this;
  }
  multiply(t2) {
    return this.multiplyMatrices(this, t2);
  }
  premultiply(t2) {
    return this.multiplyMatrices(t2, this);
  }
  multiplyMatrices(t2, e2) {
    const s2 = t2.elements, i2 = e2.elements, r2 = this.elements, n2 = s2[0], o = s2[3], a = s2[6], h2 = s2[1], l2 = s2[4], c2 = s2[7], u2 = s2[2], d2 = s2[5], p2 = s2[8], m2 = i2[0], y2 = i2[3], f2 = i2[6], g2 = i2[1], x2 = i2[4], b2 = i2[7], v2 = i2[2], w2 = i2[5], M2 = i2[8];
    return r2[0] = n2 * m2 + o * g2 + a * v2, r2[3] = n2 * y2 + o * x2 + a * w2, r2[6] = n2 * f2 + o * b2 + a * M2, r2[1] = h2 * m2 + l2 * g2 + c2 * v2, r2[4] = h2 * y2 + l2 * x2 + c2 * w2, r2[7] = h2 * f2 + l2 * b2 + c2 * M2, r2[2] = u2 * m2 + d2 * g2 + p2 * v2, r2[5] = u2 * y2 + d2 * x2 + p2 * w2, r2[8] = u2 * f2 + d2 * b2 + p2 * M2, this;
  }
  multiplyScalar(t2) {
    const e2 = this.elements;
    return e2[0] *= t2, e2[3] *= t2, e2[6] *= t2, e2[1] *= t2, e2[4] *= t2, e2[7] *= t2, e2[2] *= t2, e2[5] *= t2, e2[8] *= t2, this;
  }
  determinant() {
    const t2 = this.elements, e2 = t2[0], s2 = t2[1], i2 = t2[2], r2 = t2[3], n2 = t2[4], o = t2[5], a = t2[6], h2 = t2[7], l2 = t2[8];
    return e2 * n2 * l2 - e2 * o * h2 - s2 * r2 * l2 + s2 * o * a + i2 * r2 * h2 - i2 * n2 * a;
  }
  invert() {
    const t2 = this.elements, e2 = t2[0], s2 = t2[1], i2 = t2[2], r2 = t2[3], n2 = t2[4], o = t2[5], a = t2[6], h2 = t2[7], l2 = t2[8], c2 = l2 * n2 - o * h2, u2 = o * a - l2 * r2, d2 = h2 * r2 - n2 * a, p2 = e2 * c2 + s2 * u2 + i2 * d2;
    if (0 === p2) return this.set(0, 0, 0, 0, 0, 0, 0, 0, 0);
    const m2 = 1 / p2;
    return t2[0] = c2 * m2, t2[1] = (i2 * h2 - l2 * s2) * m2, t2[2] = (o * s2 - i2 * n2) * m2, t2[3] = u2 * m2, t2[4] = (l2 * e2 - i2 * a) * m2, t2[5] = (i2 * r2 - o * e2) * m2, t2[6] = d2 * m2, t2[7] = (s2 * a - h2 * e2) * m2, t2[8] = (n2 * e2 - s2 * r2) * m2, this;
  }
  transpose() {
    let t2;
    const e2 = this.elements;
    return t2 = e2[1], e2[1] = e2[3], e2[3] = t2, t2 = e2[2], e2[2] = e2[6], e2[6] = t2, t2 = e2[5], e2[5] = e2[7], e2[7] = t2, this;
  }
  getNormalMatrix(t2) {
    return this.setFromMatrix4(t2).invert().transpose();
  }
  transposeIntoArray(t2) {
    const e2 = this.elements;
    return t2[0] = e2[0], t2[1] = e2[3], t2[2] = e2[6], t2[3] = e2[1], t2[4] = e2[4], t2[5] = e2[7], t2[6] = e2[2], t2[7] = e2[5], t2[8] = e2[8], this;
  }
  setUvTransform(t2, e2, s2, i2, r2, n2, o) {
    const a = Math.cos(r2), h2 = Math.sin(r2);
    return this.set(s2 * a, s2 * h2, -s2 * (a * n2 + h2 * o) + n2 + t2, -i2 * h2, i2 * a, -i2 * (-h2 * n2 + a * o) + o + e2, 0, 0, 1), this;
  }
  scale(t2, e2) {
    return this.premultiply($s.makeScale(t2, e2)), this;
  }
  rotate(t2) {
    return this.premultiply($s.makeRotation(-t2)), this;
  }
  translate(t2, e2) {
    return this.premultiply($s.makeTranslation(t2, e2)), this;
  }
  makeTranslation(t2, e2) {
    return t2.isVector2 ? this.set(1, 0, t2.x, 0, 1, t2.y, 0, 0, 1) : this.set(1, 0, t2, 0, 1, e2, 0, 0, 1), this;
  }
  makeRotation(t2) {
    const e2 = Math.cos(t2), s2 = Math.sin(t2);
    return this.set(e2, -s2, 0, s2, e2, 0, 0, 0, 1), this;
  }
  makeScale(t2, e2) {
    return this.set(t2, 0, 0, 0, e2, 0, 0, 0, 1), this;
  }
  equals(t2) {
    const e2 = this.elements, s2 = t2.elements;
    for (let t3 = 0; t3 < 9; t3++) if (e2[t3] !== s2[t3]) return false;
    return true;
  }
  fromArray(t2, e2 = 0) {
    for (let s2 = 0; s2 < 9; s2++) this.elements[s2] = t2[s2 + e2];
    return this;
  }
  toArray(t2 = [], e2 = 0) {
    const s2 = this.elements;
    return t2[e2] = s2[0], t2[e2 + 1] = s2[1], t2[e2 + 2] = s2[2], t2[e2 + 3] = s2[3], t2[e2 + 4] = s2[4], t2[e2 + 5] = s2[5], t2[e2 + 6] = s2[6], t2[e2 + 7] = s2[7], t2[e2 + 8] = s2[8], t2;
  }
  clone() {
    return new this.constructor().fromArray(this.elements);
  }
}
const $s = new Gs();
function Qs(t2) {
  for (let e2 = t2.length - 1; e2 >= 0; --e2) if (t2[e2] >= 65535) return true;
  return false;
}
function ei(t2) {
  return THREEGlobals["document"].createElementNS("http://www.w3.org/1999/xhtml", t2);
}
function si() {
  const t2 = ei("canvas");
  return t2.style.display = "block", t2;
}
const ii = {};
function ri(t2) {
  t2 in ii || (ii[t2] = true, console.warn(t2));
}
function ni(t2, e2, s2) {
  return new Promise(function(i2, r2) {
    setTimeout(function n2() {
      switch (t2.clientWaitSync(e2, t2.SYNC_FLUSH_COMMANDS_BIT, 0)) {
        case t2.WAIT_FAILED:
          r2();
          break;
        case t2.TIMEOUT_EXPIRED:
          setTimeout(n2, s2);
          break;
        default:
          i2();
      }
    }, s2);
  });
}
function oi(t2) {
  const e2 = t2.elements;
  e2[2] = 0.5 * e2[2] + 0.5 * e2[3], e2[6] = 0.5 * e2[6] + 0.5 * e2[7], e2[10] = 0.5 * e2[10] + 0.5 * e2[11], e2[14] = 0.5 * e2[14] + 0.5 * e2[15];
}
function ai(t2) {
  const e2 = t2.elements;
  -1 === e2[11] ? (e2[10] = -e2[10] - 1, e2[14] = -e2[14]) : (e2[10] = -e2[10], e2[14] = 1 - e2[14]);
}
const hi = new Gs().set(0.4123908, 0.3575843, 0.1804808, 0.212639, 0.7151687, 0.0721923, 0.0193308, 0.1191948, 0.9505322), li = new Gs().set(3.2409699, -1.5373832, -0.4986108, -0.9692436, 1.8759675, 0.0415551, 0.0556301, -0.203977, 1.0569715);
function ci() {
  const t2 = { enabled: true, workingColorSpace: $e, spaces: {}, convert: function(t3, e3, s3) {
    return false !== this.enabled && e3 !== s3 && e3 && s3 ? (this.spaces[e3].transfer === Ke && (t3.r = di(t3.r), t3.g = di(t3.g), t3.b = di(t3.b)), this.spaces[e3].primaries !== this.spaces[s3].primaries && (t3.applyMatrix3(this.spaces[e3].toXYZ), t3.applyMatrix3(this.spaces[s3].fromXYZ)), this.spaces[s3].transfer === Ke && (t3.r = pi(t3.r), t3.g = pi(t3.g), t3.b = pi(t3.b)), t3) : t3;
  }, fromWorkingColorSpace: function(t3, e3) {
    return this.convert(t3, this.workingColorSpace, e3);
  }, toWorkingColorSpace: function(t3, e3) {
    return this.convert(t3, e3, this.workingColorSpace);
  }, getPrimaries: function(t3) {
    return this.spaces[t3].primaries;
  }, getTransfer: function(t3) {
    return "" === t3 ? Qe : this.spaces[t3].transfer;
  }, getLuminanceCoefficients: function(t3, e3 = this.workingColorSpace) {
    return t3.fromArray(this.spaces[e3].luminanceCoefficients);
  }, define: function(t3) {
    Object.assign(this.spaces, t3);
  }, _getMatrix: function(t3, e3, s3) {
    return t3.copy(this.spaces[e3].toXYZ).multiply(this.spaces[s3].fromXYZ);
  }, _getDrawingBufferColorSpace: function(t3) {
    return this.spaces[t3].outputColorSpaceConfig.drawingBufferColorSpace;
  }, _getUnpackColorSpace: function(t3 = this.workingColorSpace) {
    return this.spaces[t3].workingColorSpaceConfig.unpackColorSpace;
  } }, e2 = [0.64, 0.33, 0.3, 0.6, 0.15, 0.06], s2 = [0.2126, 0.7152, 0.0722], i2 = [0.3127, 0.329];
  return t2.define({ [$e]: { primaries: e2, whitePoint: i2, transfer: Qe, toXYZ: hi, fromXYZ: li, luminanceCoefficients: s2, workingColorSpaceConfig: { unpackColorSpace: Ge }, outputColorSpaceConfig: { drawingBufferColorSpace: Ge } }, [Ge]: { primaries: e2, whitePoint: i2, transfer: Ke, toXYZ: hi, fromXYZ: li, luminanceCoefficients: s2, outputColorSpaceConfig: { drawingBufferColorSpace: Ge } } }), t2;
}
const ui = ci();
function di(t2) {
  return t2 < 0.04045 ? 0.0773993808 * t2 : Math.pow(0.9478672986 * t2 + 0.0521327014, 2.4);
}
function pi(t2) {
  return t2 < 31308e-7 ? 12.92 * t2 : 1.055 * Math.pow(t2, 0.41666) - 0.055;
}
let mi;
class yi {
  static getDataURL(t2) {
    if (/^data:/i.test(t2.src)) return t2.src;
    if ("undefined" == typeof THREEGlobals["HTMLCanvasElement"]) return t2.src;
    let e2;
    if (t2 instanceof THREEGlobals["HTMLCanvasElement"]) e2 = t2;
    else {
      void 0 === mi && (mi = ei("canvas")), mi.width = t2.width, mi.height = t2.height;
      const s2 = mi.getContext("2d");
      t2 instanceof THREEGlobals["ImageData"] ? s2.putImageData(t2, 0, 0) : s2.drawImage(t2, 0, 0, t2.width, t2.height), e2 = mi;
    }
    return e2.width > 2048 || e2.height > 2048 ? (console.warn("THREE.ImageUtils.getDataURL: Image converted to jpg for performance reasons", t2), e2.toDataURL("image/jpeg", 0.6)) : e2.toDataURL("image/png");
  }
  static sRGBToLinear(t2) {
    if ("undefined" != typeof THREEGlobals["HTMLImageElement"] && t2 instanceof THREEGlobals["HTMLImageElement"] || "undefined" != typeof THREEGlobals["HTMLCanvasElement"] && t2 instanceof THREEGlobals["HTMLCanvasElement"] || "undefined" != typeof THREEGlobals["ImageBitmap"] && t2 instanceof THREEGlobals["ImageBitmap"]) {
      const e2 = ei("canvas");
      e2.width = t2.width, e2.height = t2.height;
      const s2 = e2.getContext("2d");
      s2.drawImage(t2, 0, 0, t2.width, t2.height);
      const i2 = s2.getImageData(0, 0, t2.width, t2.height), r2 = i2.data;
      for (let t3 = 0; t3 < r2.length; t3++) r2[t3] = 255 * di(r2[t3] / 255);
      return s2.putImageData(i2, 0, 0), e2;
    }
    if (t2.data) {
      const e2 = t2.data.slice(0);
      for (let t3 = 0; t3 < e2.length; t3++) e2 instanceof Uint8Array || e2 instanceof Uint8ClampedArray ? e2[t3] = Math.floor(255 * di(e2[t3] / 255)) : e2[t3] = di(e2[t3]);
      return { data: e2, width: t2.width, height: t2.height };
    }
    return console.warn("THREE.ImageUtils.sRGBToLinear(): Unsupported image type. No color space conversion applied."), t2;
  }
}
let fi = 0;
class gi {
  constructor(t2 = null) {
    this.isSource = true, Object.defineProperty(this, "id", { value: fi++ }), this.uuid = Us(), this.data = t2, this.dataReady = true, this.version = 0;
  }
  set needsUpdate(t2) {
    true === t2 && this.version++;
  }
  toJSON(t2) {
    const e2 = void 0 === t2 || "string" == typeof t2;
    if (!e2 && void 0 !== t2.images[this.uuid]) return t2.images[this.uuid];
    const s2 = { uuid: this.uuid, url: "" }, i2 = this.data;
    if (null !== i2) {
      let t3;
      if (Array.isArray(i2)) {
        t3 = [];
        for (let e3 = 0, s3 = i2.length; e3 < s3; e3++) i2[e3].isDataTexture ? t3.push(xi(i2[e3].image)) : t3.push(xi(i2[e3]));
      } else t3 = xi(i2);
      s2.url = t3;
    }
    return e2 || (t2.images[this.uuid] = s2), s2;
  }
}
function xi(t2) {
  return "undefined" != typeof THREEGlobals["HTMLImageElement"] && t2 instanceof THREEGlobals["HTMLImageElement"] || "undefined" != typeof THREEGlobals["HTMLCanvasElement"] && t2 instanceof THREEGlobals["HTMLCanvasElement"] || "undefined" != typeof THREEGlobals["ImageBitmap"] && t2 instanceof THREEGlobals["ImageBitmap"] ? yi.getDataURL(t2) : t2.data ? { data: Array.from(t2.data), width: t2.width, height: t2.height, type: t2.data.constructor.name } : (console.warn("THREE.Texture: Unable to serialize Texture."), {});
}
let bi = 0;
class vi extends Ns {
  constructor(t2 = vi.DEFAULT_IMAGE, e2 = vi.DEFAULT_MAPPING, s2 = 1001, i2 = 1001, r2 = 1006, n2 = 1008, o = 1023, a = 1009, h2 = vi.DEFAULT_ANISOTROPY, l2 = "") {
    super(), this.isTexture = true, Object.defineProperty(this, "id", { value: bi++ }), this.uuid = Us(), this.name = "", this.source = new gi(t2), this.mipmaps = [], this.mapping = e2, this.channel = 0, this.wrapS = s2, this.wrapT = i2, this.magFilter = r2, this.minFilter = n2, this.anisotropy = h2, this.format = o, this.internalFormat = null, this.type = a, this.offset = new Zs(0, 0), this.repeat = new Zs(1, 1), this.center = new Zs(0, 0), this.rotation = 0, this.matrixAutoUpdate = true, this.matrix = new Gs(), this.generateMipmaps = true, this.premultiplyAlpha = false, this.flipY = true, this.unpackAlignment = 4, this.colorSpace = l2, this.userData = {}, this.version = 0, this.onUpdate = null, this.renderTarget = null, this.isRenderTargetTexture = false, this.pmremVersion = 0;
  }
  get image() {
    return this.source.data;
  }
  set image(t2 = null) {
    this.source.data = t2;
  }
  updateMatrix() {
    this.matrix.setUvTransform(this.offset.x, this.offset.y, this.repeat.x, this.repeat.y, this.rotation, this.center.x, this.center.y);
  }
  clone() {
    return new this.constructor().copy(this);
  }
  copy(t2) {
    return this.name = t2.name, this.source = t2.source, this.mipmaps = t2.mipmaps.slice(0), this.mapping = t2.mapping, this.channel = t2.channel, this.wrapS = t2.wrapS, this.wrapT = t2.wrapT, this.magFilter = t2.magFilter, this.minFilter = t2.minFilter, this.anisotropy = t2.anisotropy, this.format = t2.format, this.internalFormat = t2.internalFormat, this.type = t2.type, this.offset.copy(t2.offset), this.repeat.copy(t2.repeat), this.center.copy(t2.center), this.rotation = t2.rotation, this.matrixAutoUpdate = t2.matrixAutoUpdate, this.matrix.copy(t2.matrix), this.generateMipmaps = t2.generateMipmaps, this.premultiplyAlpha = t2.premultiplyAlpha, this.flipY = t2.flipY, this.unpackAlignment = t2.unpackAlignment, this.colorSpace = t2.colorSpace, this.renderTarget = t2.renderTarget, this.isRenderTargetTexture = t2.isRenderTargetTexture, this.userData = JSON.parse(JSON.stringify(t2.userData)), this.needsUpdate = true, this;
  }
  toJSON(t2) {
    const e2 = void 0 === t2 || "string" == typeof t2;
    if (!e2 && void 0 !== t2.textures[this.uuid]) return t2.textures[this.uuid];
    const s2 = { metadata: { version: 4.6, type: "Texture", generator: "Texture.toJSON" }, uuid: this.uuid, name: this.name, image: this.source.toJSON(t2).uuid, mapping: this.mapping, channel: this.channel, repeat: [this.repeat.x, this.repeat.y], offset: [this.offset.x, this.offset.y], center: [this.center.x, this.center.y], rotation: this.rotation, wrap: [this.wrapS, this.wrapT], format: this.format, internalFormat: this.internalFormat, type: this.type, colorSpace: this.colorSpace, minFilter: this.minFilter, magFilter: this.magFilter, anisotropy: this.anisotropy, flipY: this.flipY, generateMipmaps: this.generateMipmaps, premultiplyAlpha: this.premultiplyAlpha, unpackAlignment: this.unpackAlignment };
    return Object.keys(this.userData).length > 0 && (s2.userData = this.userData), e2 || (t2.textures[this.uuid] = s2), s2;
  }
  dispose() {
    this.dispatchEvent({ type: "dispose" });
  }
  transformUv(t2) {
    if (this.mapping !== at) return t2;
    if (t2.applyMatrix3(this.matrix), t2.x < 0 || t2.x > 1) switch (this.wrapS) {
      case pt:
        t2.x = t2.x - Math.floor(t2.x);
        break;
      case mt:
        t2.x = t2.x < 0 ? 0 : 1;
        break;
      case yt:
        1 === Math.abs(Math.floor(t2.x) % 2) ? t2.x = Math.ceil(t2.x) - t2.x : t2.x = t2.x - Math.floor(t2.x);
    }
    if (t2.y < 0 || t2.y > 1) switch (this.wrapT) {
      case pt:
        t2.y = t2.y - Math.floor(t2.y);
        break;
      case mt:
        t2.y = t2.y < 0 ? 0 : 1;
        break;
      case yt:
        1 === Math.abs(Math.floor(t2.y) % 2) ? t2.y = Math.ceil(t2.y) - t2.y : t2.y = t2.y - Math.floor(t2.y);
    }
    return this.flipY && (t2.y = 1 - t2.y), t2;
  }
  set needsUpdate(t2) {
    true === t2 && (this.version++, this.source.needsUpdate = true);
  }
  set needsPMREMUpdate(t2) {
    true === t2 && this.pmremVersion++;
  }
}
vi.DEFAULT_IMAGE = null, vi.DEFAULT_MAPPING = at, vi.DEFAULT_ANISOTROPY = 1;
class wi {
  constructor(t2 = 0, e2 = 0, s2 = 0, i2 = 1) {
    wi.prototype.isVector4 = true, this.x = t2, this.y = e2, this.z = s2, this.w = i2;
  }
  get width() {
    return this.z;
  }
  set width(t2) {
    this.z = t2;
  }
  get height() {
    return this.w;
  }
  set height(t2) {
    this.w = t2;
  }
  set(t2, e2, s2, i2) {
    return this.x = t2, this.y = e2, this.z = s2, this.w = i2, this;
  }
  setScalar(t2) {
    return this.x = t2, this.y = t2, this.z = t2, this.w = t2, this;
  }
  setX(t2) {
    return this.x = t2, this;
  }
  setY(t2) {
    return this.y = t2, this;
  }
  setZ(t2) {
    return this.z = t2, this;
  }
  setW(t2) {
    return this.w = t2, this;
  }
  setComponent(t2, e2) {
    switch (t2) {
      case 0:
        this.x = e2;
        break;
      case 1:
        this.y = e2;
        break;
      case 2:
        this.z = e2;
        break;
      case 3:
        this.w = e2;
        break;
      default:
        throw new Error("index is out of range: " + t2);
    }
    return this;
  }
  getComponent(t2) {
    switch (t2) {
      case 0:
        return this.x;
      case 1:
        return this.y;
      case 2:
        return this.z;
      case 3:
        return this.w;
      default:
        throw new Error("index is out of range: " + t2);
    }
  }
  clone() {
    return new this.constructor(this.x, this.y, this.z, this.w);
  }
  copy(t2) {
    return this.x = t2.x, this.y = t2.y, this.z = t2.z, this.w = void 0 !== t2.w ? t2.w : 1, this;
  }
  add(t2) {
    return this.x += t2.x, this.y += t2.y, this.z += t2.z, this.w += t2.w, this;
  }
  addScalar(t2) {
    return this.x += t2, this.y += t2, this.z += t2, this.w += t2, this;
  }
  addVectors(t2, e2) {
    return this.x = t2.x + e2.x, this.y = t2.y + e2.y, this.z = t2.z + e2.z, this.w = t2.w + e2.w, this;
  }
  addScaledVector(t2, e2) {
    return this.x += t2.x * e2, this.y += t2.y * e2, this.z += t2.z * e2, this.w += t2.w * e2, this;
  }
  sub(t2) {
    return this.x -= t2.x, this.y -= t2.y, this.z -= t2.z, this.w -= t2.w, this;
  }
  subScalar(t2) {
    return this.x -= t2, this.y -= t2, this.z -= t2, this.w -= t2, this;
  }
  subVectors(t2, e2) {
    return this.x = t2.x - e2.x, this.y = t2.y - e2.y, this.z = t2.z - e2.z, this.w = t2.w - e2.w, this;
  }
  multiply(t2) {
    return this.x *= t2.x, this.y *= t2.y, this.z *= t2.z, this.w *= t2.w, this;
  }
  multiplyScalar(t2) {
    return this.x *= t2, this.y *= t2, this.z *= t2, this.w *= t2, this;
  }
  applyMatrix4(t2) {
    const e2 = this.x, s2 = this.y, i2 = this.z, r2 = this.w, n2 = t2.elements;
    return this.x = n2[0] * e2 + n2[4] * s2 + n2[8] * i2 + n2[12] * r2, this.y = n2[1] * e2 + n2[5] * s2 + n2[9] * i2 + n2[13] * r2, this.z = n2[2] * e2 + n2[6] * s2 + n2[10] * i2 + n2[14] * r2, this.w = n2[3] * e2 + n2[7] * s2 + n2[11] * i2 + n2[15] * r2, this;
  }
  divide(t2) {
    return this.x /= t2.x, this.y /= t2.y, this.z /= t2.z, this.w /= t2.w, this;
  }
  divideScalar(t2) {
    return this.multiplyScalar(1 / t2);
  }
  setAxisAngleFromQuaternion(t2) {
    this.w = 2 * Math.acos(t2.w);
    const e2 = Math.sqrt(1 - t2.w * t2.w);
    return e2 < 1e-4 ? (this.x = 1, this.y = 0, this.z = 0) : (this.x = t2.x / e2, this.y = t2.y / e2, this.z = t2.z / e2), this;
  }
  setAxisAngleFromRotationMatrix(t2) {
    let e2, s2, i2, r2;
    const n2 = 0.01, o = 0.1, a = t2.elements, h2 = a[0], l2 = a[4], c2 = a[8], u2 = a[1], d2 = a[5], p2 = a[9], m2 = a[2], y2 = a[6], f2 = a[10];
    if (Math.abs(l2 - u2) < n2 && Math.abs(c2 - m2) < n2 && Math.abs(p2 - y2) < n2) {
      if (Math.abs(l2 + u2) < o && Math.abs(c2 + m2) < o && Math.abs(p2 + y2) < o && Math.abs(h2 + d2 + f2 - 3) < o) return this.set(1, 0, 0, 0), this;
      e2 = Math.PI;
      const t3 = (h2 + 1) / 2, a2 = (d2 + 1) / 2, g3 = (f2 + 1) / 2, x2 = (l2 + u2) / 4, b2 = (c2 + m2) / 4, v2 = (p2 + y2) / 4;
      return t3 > a2 && t3 > g3 ? t3 < n2 ? (s2 = 0, i2 = 0.*********, r2 = 0.*********) : (s2 = Math.sqrt(t3), i2 = x2 / s2, r2 = b2 / s2) : a2 > g3 ? a2 < n2 ? (s2 = 0.*********, i2 = 0, r2 = 0.*********) : (i2 = Math.sqrt(a2), s2 = x2 / i2, r2 = v2 / i2) : g3 < n2 ? (s2 = 0.*********, i2 = 0.*********, r2 = 0) : (r2 = Math.sqrt(g3), s2 = b2 / r2, i2 = v2 / r2), this.set(s2, i2, r2, e2), this;
    }
    let g2 = Math.sqrt((y2 - p2) * (y2 - p2) + (c2 - m2) * (c2 - m2) + (u2 - l2) * (u2 - l2));
    return Math.abs(g2) < 1e-3 && (g2 = 1), this.x = (y2 - p2) / g2, this.y = (c2 - m2) / g2, this.z = (u2 - l2) / g2, this.w = Math.acos((h2 + d2 + f2 - 1) / 2), this;
  }
  setFromMatrixPosition(t2) {
    const e2 = t2.elements;
    return this.x = e2[12], this.y = e2[13], this.z = e2[14], this.w = e2[15], this;
  }
  min(t2) {
    return this.x = Math.min(this.x, t2.x), this.y = Math.min(this.y, t2.y), this.z = Math.min(this.z, t2.z), this.w = Math.min(this.w, t2.w), this;
  }
  max(t2) {
    return this.x = Math.max(this.x, t2.x), this.y = Math.max(this.y, t2.y), this.z = Math.max(this.z, t2.z), this.w = Math.max(this.w, t2.w), this;
  }
  clamp(t2, e2) {
    return this.x = Ds(this.x, t2.x, e2.x), this.y = Ds(this.y, t2.y, e2.y), this.z = Ds(this.z, t2.z, e2.z), this.w = Ds(this.w, t2.w, e2.w), this;
  }
  clampScalar(t2, e2) {
    return this.x = Ds(this.x, t2, e2), this.y = Ds(this.y, t2, e2), this.z = Ds(this.z, t2, e2), this.w = Ds(this.w, t2, e2), this;
  }
  clampLength(t2, e2) {
    const s2 = this.length();
    return this.divideScalar(s2 || 1).multiplyScalar(Ds(s2, t2, e2));
  }
  floor() {
    return this.x = Math.floor(this.x), this.y = Math.floor(this.y), this.z = Math.floor(this.z), this.w = Math.floor(this.w), this;
  }
  ceil() {
    return this.x = Math.ceil(this.x), this.y = Math.ceil(this.y), this.z = Math.ceil(this.z), this.w = Math.ceil(this.w), this;
  }
  round() {
    return this.x = Math.round(this.x), this.y = Math.round(this.y), this.z = Math.round(this.z), this.w = Math.round(this.w), this;
  }
  roundToZero() {
    return this.x = Math.trunc(this.x), this.y = Math.trunc(this.y), this.z = Math.trunc(this.z), this.w = Math.trunc(this.w), this;
  }
  negate() {
    return this.x = -this.x, this.y = -this.y, this.z = -this.z, this.w = -this.w, this;
  }
  dot(t2) {
    return this.x * t2.x + this.y * t2.y + this.z * t2.z + this.w * t2.w;
  }
  lengthSq() {
    return this.x * this.x + this.y * this.y + this.z * this.z + this.w * this.w;
  }
  length() {
    return Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z + this.w * this.w);
  }
  manhattanLength() {
    return Math.abs(this.x) + Math.abs(this.y) + Math.abs(this.z) + Math.abs(this.w);
  }
  normalize() {
    return this.divideScalar(this.length() || 1);
  }
  setLength(t2) {
    return this.normalize().multiplyScalar(t2);
  }
  lerp(t2, e2) {
    return this.x += (t2.x - this.x) * e2, this.y += (t2.y - this.y) * e2, this.z += (t2.z - this.z) * e2, this.w += (t2.w - this.w) * e2, this;
  }
  lerpVectors(t2, e2, s2) {
    return this.x = t2.x + (e2.x - t2.x) * s2, this.y = t2.y + (e2.y - t2.y) * s2, this.z = t2.z + (e2.z - t2.z) * s2, this.w = t2.w + (e2.w - t2.w) * s2, this;
  }
  equals(t2) {
    return t2.x === this.x && t2.y === this.y && t2.z === this.z && t2.w === this.w;
  }
  fromArray(t2, e2 = 0) {
    return this.x = t2[e2], this.y = t2[e2 + 1], this.z = t2[e2 + 2], this.w = t2[e2 + 3], this;
  }
  toArray(t2 = [], e2 = 0) {
    return t2[e2] = this.x, t2[e2 + 1] = this.y, t2[e2 + 2] = this.z, t2[e2 + 3] = this.w, t2;
  }
  fromBufferAttribute(t2, e2) {
    return this.x = t2.getX(e2), this.y = t2.getY(e2), this.z = t2.getZ(e2), this.w = t2.getW(e2), this;
  }
  random() {
    return this.x = Math.random(), this.y = Math.random(), this.z = Math.random(), this.w = Math.random(), this;
  }
  *[Symbol.iterator]() {
    yield this.x, yield this.y, yield this.z, yield this.w;
  }
}
class Mi extends Ns {
  constructor(t2 = 1, e2 = 1, s2 = {}) {
    super(), this.isRenderTarget = true, this.width = t2, this.height = e2, this.depth = 1, this.scissor = new wi(0, 0, t2, e2), this.scissorTest = false, this.viewport = new wi(0, 0, t2, e2);
    const i2 = { width: t2, height: e2, depth: 1 };
    s2 = Object.assign({ generateMipmaps: false, internalFormat: null, minFilter: wt, depthBuffer: true, stencilBuffer: false, resolveDepthBuffer: true, resolveStencilBuffer: true, depthTexture: null, samples: 0, count: 1 }, s2);
    const r2 = new vi(i2, s2.mapping, s2.wrapS, s2.wrapT, s2.magFilter, s2.minFilter, s2.format, s2.type, s2.anisotropy, s2.colorSpace);
    r2.flipY = false, r2.generateMipmaps = s2.generateMipmaps, r2.internalFormat = s2.internalFormat, this.textures = [];
    const n2 = s2.count;
    for (let t3 = 0; t3 < n2; t3++) this.textures[t3] = r2.clone(), this.textures[t3].isRenderTargetTexture = true, this.textures[t3].renderTarget = this;
    this.depthBuffer = s2.depthBuffer, this.stencilBuffer = s2.stencilBuffer, this.resolveDepthBuffer = s2.resolveDepthBuffer, this.resolveStencilBuffer = s2.resolveStencilBuffer, this._depthTexture = null, this.depthTexture = s2.depthTexture, this.samples = s2.samples;
  }
  get texture() {
    return this.textures[0];
  }
  set texture(t2) {
    this.textures[0] = t2;
  }
  set depthTexture(t2) {
    null !== this._depthTexture && (this._depthTexture.renderTarget = null), null !== t2 && (t2.renderTarget = this), this._depthTexture = t2;
  }
  get depthTexture() {
    return this._depthTexture;
  }
  setSize(t2, e2, s2 = 1) {
    if (this.width !== t2 || this.height !== e2 || this.depth !== s2) {
      this.width = t2, this.height = e2, this.depth = s2;
      for (let i2 = 0, r2 = this.textures.length; i2 < r2; i2++) this.textures[i2].image.width = t2, this.textures[i2].image.height = e2, this.textures[i2].image.depth = s2;
      this.dispose();
    }
    this.viewport.set(0, 0, t2, e2), this.scissor.set(0, 0, t2, e2);
  }
  clone() {
    return new this.constructor().copy(this);
  }
  copy(t2) {
    this.width = t2.width, this.height = t2.height, this.depth = t2.depth, this.scissor.copy(t2.scissor), this.scissorTest = t2.scissorTest, this.viewport.copy(t2.viewport), this.textures.length = 0;
    for (let e3 = 0, s2 = t2.textures.length; e3 < s2; e3++) this.textures[e3] = t2.textures[e3].clone(), this.textures[e3].isRenderTargetTexture = true, this.textures[e3].renderTarget = this;
    const e2 = Object.assign({}, t2.texture.image);
    return this.texture.source = new gi(e2), this.depthBuffer = t2.depthBuffer, this.stencilBuffer = t2.stencilBuffer, this.resolveDepthBuffer = t2.resolveDepthBuffer, this.resolveStencilBuffer = t2.resolveStencilBuffer, null !== t2.depthTexture && (this.depthTexture = t2.depthTexture.clone()), this.samples = t2.samples, this;
  }
  dispose() {
    this.dispatchEvent({ type: "dispose" });
  }
}
class Si extends Mi {
  constructor(t2 = 1, e2 = 1, s2 = {}) {
    super(t2, e2, s2), this.isWebGLRenderTarget = true;
  }
}
class _i extends vi {
  constructor(t2 = null, e2 = 1, s2 = 1, i2 = 1) {
    super(null), this.isDataArrayTexture = true, this.image = { data: t2, width: e2, height: s2, depth: i2 }, this.magFilter = ft, this.minFilter = ft, this.wrapR = mt, this.generateMipmaps = false, this.flipY = false, this.unpackAlignment = 1, this.layerUpdates = /* @__PURE__ */ new Set();
  }
  addLayerUpdate(t2) {
    this.layerUpdates.add(t2);
  }
  clearLayerUpdates() {
    this.layerUpdates.clear();
  }
}
class Ti extends vi {
  constructor(t2 = null, e2 = 1, s2 = 1, i2 = 1) {
    super(null), this.isData3DTexture = true, this.image = { data: t2, width: e2, height: s2, depth: i2 }, this.magFilter = ft, this.minFilter = ft, this.wrapR = mt, this.generateMipmaps = false, this.flipY = false, this.unpackAlignment = 1;
  }
}
class Ci {
  constructor(t2 = 0, e2 = 0, s2 = 0, i2 = 1) {
    this.isQuaternion = true, this._x = t2, this._y = e2, this._z = s2, this._w = i2;
  }
  static slerpFlat(t2, e2, s2, i2, r2, n2, o) {
    let a = s2[i2 + 0], h2 = s2[i2 + 1], l2 = s2[i2 + 2], c2 = s2[i2 + 3];
    const u2 = r2[n2 + 0], d2 = r2[n2 + 1], p2 = r2[n2 + 2], m2 = r2[n2 + 3];
    if (0 === o) return t2[e2 + 0] = a, t2[e2 + 1] = h2, t2[e2 + 2] = l2, void (t2[e2 + 3] = c2);
    if (1 === o) return t2[e2 + 0] = u2, t2[e2 + 1] = d2, t2[e2 + 2] = p2, void (t2[e2 + 3] = m2);
    if (c2 !== m2 || a !== u2 || h2 !== d2 || l2 !== p2) {
      let t3 = 1 - o;
      const e3 = a * u2 + h2 * d2 + l2 * p2 + c2 * m2, s3 = e3 >= 0 ? 1 : -1, i3 = 1 - e3 * e3;
      if (i3 > Number.EPSILON) {
        const r4 = Math.sqrt(i3), n3 = Math.atan2(r4, e3 * s3);
        t3 = Math.sin(t3 * n3) / r4, o = Math.sin(o * n3) / r4;
      }
      const r3 = o * s3;
      if (a = a * t3 + u2 * r3, h2 = h2 * t3 + d2 * r3, l2 = l2 * t3 + p2 * r3, c2 = c2 * t3 + m2 * r3, t3 === 1 - o) {
        const t4 = 1 / Math.sqrt(a * a + h2 * h2 + l2 * l2 + c2 * c2);
        a *= t4, h2 *= t4, l2 *= t4, c2 *= t4;
      }
    }
    t2[e2] = a, t2[e2 + 1] = h2, t2[e2 + 2] = l2, t2[e2 + 3] = c2;
  }
  static multiplyQuaternionsFlat(t2, e2, s2, i2, r2, n2) {
    const o = s2[i2], a = s2[i2 + 1], h2 = s2[i2 + 2], l2 = s2[i2 + 3], c2 = r2[n2], u2 = r2[n2 + 1], d2 = r2[n2 + 2], p2 = r2[n2 + 3];
    return t2[e2] = o * p2 + l2 * c2 + a * d2 - h2 * u2, t2[e2 + 1] = a * p2 + l2 * u2 + h2 * c2 - o * d2, t2[e2 + 2] = h2 * p2 + l2 * d2 + o * u2 - a * c2, t2[e2 + 3] = l2 * p2 - o * c2 - a * u2 - h2 * d2, t2;
  }
  get x() {
    return this._x;
  }
  set x(t2) {
    this._x = t2, this._onChangeCallback();
  }
  get y() {
    return this._y;
  }
  set y(t2) {
    this._y = t2, this._onChangeCallback();
  }
  get z() {
    return this._z;
  }
  set z(t2) {
    this._z = t2, this._onChangeCallback();
  }
  get w() {
    return this._w;
  }
  set w(t2) {
    this._w = t2, this._onChangeCallback();
  }
  set(t2, e2, s2, i2) {
    return this._x = t2, this._y = e2, this._z = s2, this._w = i2, this._onChangeCallback(), this;
  }
  clone() {
    return new this.constructor(this._x, this._y, this._z, this._w);
  }
  copy(t2) {
    return this._x = t2.x, this._y = t2.y, this._z = t2.z, this._w = t2.w, this._onChangeCallback(), this;
  }
  setFromEuler(t2, e2 = true) {
    const s2 = t2._x, i2 = t2._y, r2 = t2._z, n2 = t2._order, o = Math.cos, a = Math.sin, h2 = o(s2 / 2), l2 = o(i2 / 2), c2 = o(r2 / 2), u2 = a(s2 / 2), d2 = a(i2 / 2), p2 = a(r2 / 2);
    switch (n2) {
      case "XYZ":
        this._x = u2 * l2 * c2 + h2 * d2 * p2, this._y = h2 * d2 * c2 - u2 * l2 * p2, this._z = h2 * l2 * p2 + u2 * d2 * c2, this._w = h2 * l2 * c2 - u2 * d2 * p2;
        break;
      case "YXZ":
        this._x = u2 * l2 * c2 + h2 * d2 * p2, this._y = h2 * d2 * c2 - u2 * l2 * p2, this._z = h2 * l2 * p2 - u2 * d2 * c2, this._w = h2 * l2 * c2 + u2 * d2 * p2;
        break;
      case "ZXY":
        this._x = u2 * l2 * c2 - h2 * d2 * p2, this._y = h2 * d2 * c2 + u2 * l2 * p2, this._z = h2 * l2 * p2 + u2 * d2 * c2, this._w = h2 * l2 * c2 - u2 * d2 * p2;
        break;
      case "ZYX":
        this._x = u2 * l2 * c2 - h2 * d2 * p2, this._y = h2 * d2 * c2 + u2 * l2 * p2, this._z = h2 * l2 * p2 - u2 * d2 * c2, this._w = h2 * l2 * c2 + u2 * d2 * p2;
        break;
      case "YZX":
        this._x = u2 * l2 * c2 + h2 * d2 * p2, this._y = h2 * d2 * c2 + u2 * l2 * p2, this._z = h2 * l2 * p2 - u2 * d2 * c2, this._w = h2 * l2 * c2 - u2 * d2 * p2;
        break;
      case "XZY":
        this._x = u2 * l2 * c2 - h2 * d2 * p2, this._y = h2 * d2 * c2 - u2 * l2 * p2, this._z = h2 * l2 * p2 + u2 * d2 * c2, this._w = h2 * l2 * c2 + u2 * d2 * p2;
        break;
      default:
        console.warn("THREE.Quaternion: .setFromEuler() encountered an unknown order: " + n2);
    }
    return true === e2 && this._onChangeCallback(), this;
  }
  setFromAxisAngle(t2, e2) {
    const s2 = e2 / 2, i2 = Math.sin(s2);
    return this._x = t2.x * i2, this._y = t2.y * i2, this._z = t2.z * i2, this._w = Math.cos(s2), this._onChangeCallback(), this;
  }
  setFromRotationMatrix(t2) {
    const e2 = t2.elements, s2 = e2[0], i2 = e2[4], r2 = e2[8], n2 = e2[1], o = e2[5], a = e2[9], h2 = e2[2], l2 = e2[6], c2 = e2[10], u2 = s2 + o + c2;
    if (u2 > 0) {
      const t3 = 0.5 / Math.sqrt(u2 + 1);
      this._w = 0.25 / t3, this._x = (l2 - a) * t3, this._y = (r2 - h2) * t3, this._z = (n2 - i2) * t3;
    } else if (s2 > o && s2 > c2) {
      const t3 = 2 * Math.sqrt(1 + s2 - o - c2);
      this._w = (l2 - a) / t3, this._x = 0.25 * t3, this._y = (i2 + n2) / t3, this._z = (r2 + h2) / t3;
    } else if (o > c2) {
      const t3 = 2 * Math.sqrt(1 + o - s2 - c2);
      this._w = (r2 - h2) / t3, this._x = (i2 + n2) / t3, this._y = 0.25 * t3, this._z = (a + l2) / t3;
    } else {
      const t3 = 2 * Math.sqrt(1 + c2 - s2 - o);
      this._w = (n2 - i2) / t3, this._x = (r2 + h2) / t3, this._y = (a + l2) / t3, this._z = 0.25 * t3;
    }
    return this._onChangeCallback(), this;
  }
  setFromUnitVectors(t2, e2) {
    let s2 = t2.dot(e2) + 1;
    return s2 < Number.EPSILON ? (s2 = 0, Math.abs(t2.x) > Math.abs(t2.z) ? (this._x = -t2.y, this._y = t2.x, this._z = 0, this._w = s2) : (this._x = 0, this._y = -t2.z, this._z = t2.y, this._w = s2)) : (this._x = t2.y * e2.z - t2.z * e2.y, this._y = t2.z * e2.x - t2.x * e2.z, this._z = t2.x * e2.y - t2.y * e2.x, this._w = s2), this.normalize();
  }
  angleTo(t2) {
    return 2 * Math.acos(Math.abs(Ds(this.dot(t2), -1, 1)));
  }
  rotateTowards(t2, e2) {
    const s2 = this.angleTo(t2);
    if (0 === s2) return this;
    const i2 = Math.min(1, e2 / s2);
    return this.slerp(t2, i2), this;
  }
  identity() {
    return this.set(0, 0, 0, 1);
  }
  invert() {
    return this.conjugate();
  }
  conjugate() {
    return this._x *= -1, this._y *= -1, this._z *= -1, this._onChangeCallback(), this;
  }
  dot(t2) {
    return this._x * t2._x + this._y * t2._y + this._z * t2._z + this._w * t2._w;
  }
  lengthSq() {
    return this._x * this._x + this._y * this._y + this._z * this._z + this._w * this._w;
  }
  length() {
    return Math.sqrt(this._x * this._x + this._y * this._y + this._z * this._z + this._w * this._w);
  }
  normalize() {
    let t2 = this.length();
    return 0 === t2 ? (this._x = 0, this._y = 0, this._z = 0, this._w = 1) : (t2 = 1 / t2, this._x = this._x * t2, this._y = this._y * t2, this._z = this._z * t2, this._w = this._w * t2), this._onChangeCallback(), this;
  }
  multiply(t2) {
    return this.multiplyQuaternions(this, t2);
  }
  premultiply(t2) {
    return this.multiplyQuaternions(t2, this);
  }
  multiplyQuaternions(t2, e2) {
    const s2 = t2._x, i2 = t2._y, r2 = t2._z, n2 = t2._w, o = e2._x, a = e2._y, h2 = e2._z, l2 = e2._w;
    return this._x = s2 * l2 + n2 * o + i2 * h2 - r2 * a, this._y = i2 * l2 + n2 * a + r2 * o - s2 * h2, this._z = r2 * l2 + n2 * h2 + s2 * a - i2 * o, this._w = n2 * l2 - s2 * o - i2 * a - r2 * h2, this._onChangeCallback(), this;
  }
  slerp(t2, e2) {
    if (0 === e2) return this;
    if (1 === e2) return this.copy(t2);
    const s2 = this._x, i2 = this._y, r2 = this._z, n2 = this._w;
    let o = n2 * t2._w + s2 * t2._x + i2 * t2._y + r2 * t2._z;
    if (o < 0 ? (this._w = -t2._w, this._x = -t2._x, this._y = -t2._y, this._z = -t2._z, o = -o) : this.copy(t2), o >= 1) return this._w = n2, this._x = s2, this._y = i2, this._z = r2, this;
    const a = 1 - o * o;
    if (a <= Number.EPSILON) {
      const t3 = 1 - e2;
      return this._w = t3 * n2 + e2 * this._w, this._x = t3 * s2 + e2 * this._x, this._y = t3 * i2 + e2 * this._y, this._z = t3 * r2 + e2 * this._z, this.normalize(), this;
    }
    const h2 = Math.sqrt(a), l2 = Math.atan2(h2, o), c2 = Math.sin((1 - e2) * l2) / h2, u2 = Math.sin(e2 * l2) / h2;
    return this._w = n2 * c2 + this._w * u2, this._x = s2 * c2 + this._x * u2, this._y = i2 * c2 + this._y * u2, this._z = r2 * c2 + this._z * u2, this._onChangeCallback(), this;
  }
  slerpQuaternions(t2, e2, s2) {
    return this.copy(t2).slerp(e2, s2);
  }
  random() {
    const t2 = 2 * Math.PI * Math.random(), e2 = 2 * Math.PI * Math.random(), s2 = Math.random(), i2 = Math.sqrt(1 - s2), r2 = Math.sqrt(s2);
    return this.set(i2 * Math.sin(t2), i2 * Math.cos(t2), r2 * Math.sin(e2), r2 * Math.cos(e2));
  }
  equals(t2) {
    return t2._x === this._x && t2._y === this._y && t2._z === this._z && t2._w === this._w;
  }
  fromArray(t2, e2 = 0) {
    return this._x = t2[e2], this._y = t2[e2 + 1], this._z = t2[e2 + 2], this._w = t2[e2 + 3], this._onChangeCallback(), this;
  }
  toArray(t2 = [], e2 = 0) {
    return t2[e2] = this._x, t2[e2 + 1] = this._y, t2[e2 + 2] = this._z, t2[e2 + 3] = this._w, t2;
  }
  fromBufferAttribute(t2, e2) {
    return this._x = t2.getX(e2), this._y = t2.getY(e2), this._z = t2.getZ(e2), this._w = t2.getW(e2), this._onChangeCallback(), this;
  }
  toJSON() {
    return this.toArray();
  }
  _onChange(t2) {
    return this._onChangeCallback = t2, this;
  }
  _onChangeCallback() {
  }
  *[Symbol.iterator]() {
    yield this._x, yield this._y, yield this._z, yield this._w;
  }
}
class Ii {
  constructor(t2 = 0, e2 = 0, s2 = 0) {
    Ii.prototype.isVector3 = true, this.x = t2, this.y = e2, this.z = s2;
  }
  set(t2, e2, s2) {
    return void 0 === s2 && (s2 = this.z), this.x = t2, this.y = e2, this.z = s2, this;
  }
  setScalar(t2) {
    return this.x = t2, this.y = t2, this.z = t2, this;
  }
  setX(t2) {
    return this.x = t2, this;
  }
  setY(t2) {
    return this.y = t2, this;
  }
  setZ(t2) {
    return this.z = t2, this;
  }
  setComponent(t2, e2) {
    switch (t2) {
      case 0:
        this.x = e2;
        break;
      case 1:
        this.y = e2;
        break;
      case 2:
        this.z = e2;
        break;
      default:
        throw new Error("index is out of range: " + t2);
    }
    return this;
  }
  getComponent(t2) {
    switch (t2) {
      case 0:
        return this.x;
      case 1:
        return this.y;
      case 2:
        return this.z;
      default:
        throw new Error("index is out of range: " + t2);
    }
  }
  clone() {
    return new this.constructor(this.x, this.y, this.z);
  }
  copy(t2) {
    return this.x = t2.x, this.y = t2.y, this.z = t2.z, this;
  }
  add(t2) {
    return this.x += t2.x, this.y += t2.y, this.z += t2.z, this;
  }
  addScalar(t2) {
    return this.x += t2, this.y += t2, this.z += t2, this;
  }
  addVectors(t2, e2) {
    return this.x = t2.x + e2.x, this.y = t2.y + e2.y, this.z = t2.z + e2.z, this;
  }
  addScaledVector(t2, e2) {
    return this.x += t2.x * e2, this.y += t2.y * e2, this.z += t2.z * e2, this;
  }
  sub(t2) {
    return this.x -= t2.x, this.y -= t2.y, this.z -= t2.z, this;
  }
  subScalar(t2) {
    return this.x -= t2, this.y -= t2, this.z -= t2, this;
  }
  subVectors(t2, e2) {
    return this.x = t2.x - e2.x, this.y = t2.y - e2.y, this.z = t2.z - e2.z, this;
  }
  multiply(t2) {
    return this.x *= t2.x, this.y *= t2.y, this.z *= t2.z, this;
  }
  multiplyScalar(t2) {
    return this.x *= t2, this.y *= t2, this.z *= t2, this;
  }
  multiplyVectors(t2, e2) {
    return this.x = t2.x * e2.x, this.y = t2.y * e2.y, this.z = t2.z * e2.z, this;
  }
  applyEuler(t2) {
    return this.applyQuaternion(ki.setFromEuler(t2));
  }
  applyAxisAngle(t2, e2) {
    return this.applyQuaternion(ki.setFromAxisAngle(t2, e2));
  }
  applyMatrix3(t2) {
    const e2 = this.x, s2 = this.y, i2 = this.z, r2 = t2.elements;
    return this.x = r2[0] * e2 + r2[3] * s2 + r2[6] * i2, this.y = r2[1] * e2 + r2[4] * s2 + r2[7] * i2, this.z = r2[2] * e2 + r2[5] * s2 + r2[8] * i2, this;
  }
  applyNormalMatrix(t2) {
    return this.applyMatrix3(t2).normalize();
  }
  applyMatrix4(t2) {
    const e2 = this.x, s2 = this.y, i2 = this.z, r2 = t2.elements, n2 = 1 / (r2[3] * e2 + r2[7] * s2 + r2[11] * i2 + r2[15]);
    return this.x = (r2[0] * e2 + r2[4] * s2 + r2[8] * i2 + r2[12]) * n2, this.y = (r2[1] * e2 + r2[5] * s2 + r2[9] * i2 + r2[13]) * n2, this.z = (r2[2] * e2 + r2[6] * s2 + r2[10] * i2 + r2[14]) * n2, this;
  }
  applyQuaternion(t2) {
    const e2 = this.x, s2 = this.y, i2 = this.z, r2 = t2.x, n2 = t2.y, o = t2.z, a = t2.w, h2 = 2 * (n2 * i2 - o * s2), l2 = 2 * (o * e2 - r2 * i2), c2 = 2 * (r2 * s2 - n2 * e2);
    return this.x = e2 + a * h2 + n2 * c2 - o * l2, this.y = s2 + a * l2 + o * h2 - r2 * c2, this.z = i2 + a * c2 + r2 * l2 - n2 * h2, this;
  }
  project(t2) {
    return this.applyMatrix4(t2.matrixWorldInverse).applyMatrix4(t2.projectionMatrix);
  }
  unproject(t2) {
    return this.applyMatrix4(t2.projectionMatrixInverse).applyMatrix4(t2.matrixWorld);
  }
  transformDirection(t2) {
    const e2 = this.x, s2 = this.y, i2 = this.z, r2 = t2.elements;
    return this.x = r2[0] * e2 + r2[4] * s2 + r2[8] * i2, this.y = r2[1] * e2 + r2[5] * s2 + r2[9] * i2, this.z = r2[2] * e2 + r2[6] * s2 + r2[10] * i2, this.normalize();
  }
  divide(t2) {
    return this.x /= t2.x, this.y /= t2.y, this.z /= t2.z, this;
  }
  divideScalar(t2) {
    return this.multiplyScalar(1 / t2);
  }
  min(t2) {
    return this.x = Math.min(this.x, t2.x), this.y = Math.min(this.y, t2.y), this.z = Math.min(this.z, t2.z), this;
  }
  max(t2) {
    return this.x = Math.max(this.x, t2.x), this.y = Math.max(this.y, t2.y), this.z = Math.max(this.z, t2.z), this;
  }
  clamp(t2, e2) {
    return this.x = Ds(this.x, t2.x, e2.x), this.y = Ds(this.y, t2.y, e2.y), this.z = Ds(this.z, t2.z, e2.z), this;
  }
  clampScalar(t2, e2) {
    return this.x = Ds(this.x, t2, e2), this.y = Ds(this.y, t2, e2), this.z = Ds(this.z, t2, e2), this;
  }
  clampLength(t2, e2) {
    const s2 = this.length();
    return this.divideScalar(s2 || 1).multiplyScalar(Ds(s2, t2, e2));
  }
  floor() {
    return this.x = Math.floor(this.x), this.y = Math.floor(this.y), this.z = Math.floor(this.z), this;
  }
  ceil() {
    return this.x = Math.ceil(this.x), this.y = Math.ceil(this.y), this.z = Math.ceil(this.z), this;
  }
  round() {
    return this.x = Math.round(this.x), this.y = Math.round(this.y), this.z = Math.round(this.z), this;
  }
  roundToZero() {
    return this.x = Math.trunc(this.x), this.y = Math.trunc(this.y), this.z = Math.trunc(this.z), this;
  }
  negate() {
    return this.x = -this.x, this.y = -this.y, this.z = -this.z, this;
  }
  dot(t2) {
    return this.x * t2.x + this.y * t2.y + this.z * t2.z;
  }
  lengthSq() {
    return this.x * this.x + this.y * this.y + this.z * this.z;
  }
  length() {
    return Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z);
  }
  manhattanLength() {
    return Math.abs(this.x) + Math.abs(this.y) + Math.abs(this.z);
  }
  normalize() {
    return this.divideScalar(this.length() || 1);
  }
  setLength(t2) {
    return this.normalize().multiplyScalar(t2);
  }
  lerp(t2, e2) {
    return this.x += (t2.x - this.x) * e2, this.y += (t2.y - this.y) * e2, this.z += (t2.z - this.z) * e2, this;
  }
  lerpVectors(t2, e2, s2) {
    return this.x = t2.x + (e2.x - t2.x) * s2, this.y = t2.y + (e2.y - t2.y) * s2, this.z = t2.z + (e2.z - t2.z) * s2, this;
  }
  cross(t2) {
    return this.crossVectors(this, t2);
  }
  crossVectors(t2, e2) {
    const s2 = t2.x, i2 = t2.y, r2 = t2.z, n2 = e2.x, o = e2.y, a = e2.z;
    return this.x = i2 * a - r2 * o, this.y = r2 * n2 - s2 * a, this.z = s2 * o - i2 * n2, this;
  }
  projectOnVector(t2) {
    const e2 = t2.lengthSq();
    if (0 === e2) return this.set(0, 0, 0);
    const s2 = t2.dot(this) / e2;
    return this.copy(t2).multiplyScalar(s2);
  }
  projectOnPlane(t2) {
    return Bi.copy(this).projectOnVector(t2), this.sub(Bi);
  }
  reflect(t2) {
    return this.sub(Bi.copy(t2).multiplyScalar(2 * this.dot(t2)));
  }
  angleTo(t2) {
    const e2 = Math.sqrt(this.lengthSq() * t2.lengthSq());
    if (0 === e2) return Math.PI / 2;
    const s2 = this.dot(t2) / e2;
    return Math.acos(Ds(s2, -1, 1));
  }
  distanceTo(t2) {
    return Math.sqrt(this.distanceToSquared(t2));
  }
  distanceToSquared(t2) {
    const e2 = this.x - t2.x, s2 = this.y - t2.y, i2 = this.z - t2.z;
    return e2 * e2 + s2 * s2 + i2 * i2;
  }
  manhattanDistanceTo(t2) {
    return Math.abs(this.x - t2.x) + Math.abs(this.y - t2.y) + Math.abs(this.z - t2.z);
  }
  setFromSpherical(t2) {
    return this.setFromSphericalCoords(t2.radius, t2.phi, t2.theta);
  }
  setFromSphericalCoords(t2, e2, s2) {
    const i2 = Math.sin(e2) * t2;
    return this.x = i2 * Math.sin(s2), this.y = Math.cos(e2) * t2, this.z = i2 * Math.cos(s2), this;
  }
  setFromCylindrical(t2) {
    return this.setFromCylindricalCoords(t2.radius, t2.theta, t2.y);
  }
  setFromCylindricalCoords(t2, e2, s2) {
    return this.x = t2 * Math.sin(e2), this.y = s2, this.z = t2 * Math.cos(e2), this;
  }
  setFromMatrixPosition(t2) {
    const e2 = t2.elements;
    return this.x = e2[12], this.y = e2[13], this.z = e2[14], this;
  }
  setFromMatrixScale(t2) {
    const e2 = this.setFromMatrixColumn(t2, 0).length(), s2 = this.setFromMatrixColumn(t2, 1).length(), i2 = this.setFromMatrixColumn(t2, 2).length();
    return this.x = e2, this.y = s2, this.z = i2, this;
  }
  setFromMatrixColumn(t2, e2) {
    return this.fromArray(t2.elements, 4 * e2);
  }
  setFromMatrix3Column(t2, e2) {
    return this.fromArray(t2.elements, 3 * e2);
  }
  setFromEuler(t2) {
    return this.x = t2._x, this.y = t2._y, this.z = t2._z, this;
  }
  setFromColor(t2) {
    return this.x = t2.r, this.y = t2.g, this.z = t2.b, this;
  }
  equals(t2) {
    return t2.x === this.x && t2.y === this.y && t2.z === this.z;
  }
  fromArray(t2, e2 = 0) {
    return this.x = t2[e2], this.y = t2[e2 + 1], this.z = t2[e2 + 2], this;
  }
  toArray(t2 = [], e2 = 0) {
    return t2[e2] = this.x, t2[e2 + 1] = this.y, t2[e2 + 2] = this.z, t2;
  }
  fromBufferAttribute(t2, e2) {
    return this.x = t2.getX(e2), this.y = t2.getY(e2), this.z = t2.getZ(e2), this;
  }
  random() {
    return this.x = Math.random(), this.y = Math.random(), this.z = Math.random(), this;
  }
  randomDirection() {
    const t2 = Math.random() * Math.PI * 2, e2 = 2 * Math.random() - 1, s2 = Math.sqrt(1 - e2 * e2);
    return this.x = s2 * Math.cos(t2), this.y = e2, this.z = s2 * Math.sin(t2), this;
  }
  *[Symbol.iterator]() {
    yield this.x, yield this.y, yield this.z;
  }
}
const Bi = new Ii(), ki = new Ci();
class Ri {
  constructor(t2 = new Ii(1 / 0, 1 / 0, 1 / 0), e2 = new Ii(-1 / 0, -1 / 0, -1 / 0)) {
    this.isBox3 = true, this.min = t2, this.max = e2;
  }
  set(t2, e2) {
    return this.min.copy(t2), this.max.copy(e2), this;
  }
  setFromArray(t2) {
    this.makeEmpty();
    for (let e2 = 0, s2 = t2.length; e2 < s2; e2 += 3) this.expandByPoint(Pi.fromArray(t2, e2));
    return this;
  }
  setFromBufferAttribute(t2) {
    this.makeEmpty();
    for (let e2 = 0, s2 = t2.count; e2 < s2; e2++) this.expandByPoint(Pi.fromBufferAttribute(t2, e2));
    return this;
  }
  setFromPoints(t2) {
    this.makeEmpty();
    for (let e2 = 0, s2 = t2.length; e2 < s2; e2++) this.expandByPoint(t2[e2]);
    return this;
  }
  setFromCenterAndSize(t2, e2) {
    const s2 = Pi.copy(e2).multiplyScalar(0.5);
    return this.min.copy(t2).sub(s2), this.max.copy(t2).add(s2), this;
  }
  setFromObject(t2, e2 = false) {
    return this.makeEmpty(), this.expandByObject(t2, e2);
  }
  clone() {
    return new this.constructor().copy(this);
  }
  copy(t2) {
    return this.min.copy(t2.min), this.max.copy(t2.max), this;
  }
  makeEmpty() {
    return this.min.x = this.min.y = this.min.z = 1 / 0, this.max.x = this.max.y = this.max.z = -1 / 0, this;
  }
  isEmpty() {
    return this.max.x < this.min.x || this.max.y < this.min.y || this.max.z < this.min.z;
  }
  getCenter(t2) {
    return this.isEmpty() ? t2.set(0, 0, 0) : t2.addVectors(this.min, this.max).multiplyScalar(0.5);
  }
  getSize(t2) {
    return this.isEmpty() ? t2.set(0, 0, 0) : t2.subVectors(this.max, this.min);
  }
  expandByPoint(t2) {
    return this.min.min(t2), this.max.max(t2), this;
  }
  expandByVector(t2) {
    return this.min.sub(t2), this.max.add(t2), this;
  }
  expandByScalar(t2) {
    return this.min.addScalar(-t2), this.max.addScalar(t2), this;
  }
  expandByObject(t2, e2 = false) {
    t2.updateWorldMatrix(false, false);
    const s2 = t2.geometry;
    if (void 0 !== s2) {
      const i3 = s2.getAttribute("position");
      if (true === e2 && void 0 !== i3 && true !== t2.isInstancedMesh) for (let e3 = 0, s3 = i3.count; e3 < s3; e3++) true === t2.isMesh ? t2.getVertexPosition(e3, Pi) : Pi.fromBufferAttribute(i3, e3), Pi.applyMatrix4(t2.matrixWorld), this.expandByPoint(Pi);
      else void 0 !== t2.boundingBox ? (null === t2.boundingBox && t2.computeBoundingBox(), Oi.copy(t2.boundingBox)) : (null === s2.boundingBox && s2.computeBoundingBox(), Oi.copy(s2.boundingBox)), Oi.applyMatrix4(t2.matrixWorld), this.union(Oi);
    }
    const i2 = t2.children;
    for (let t3 = 0, s3 = i2.length; t3 < s3; t3++) this.expandByObject(i2[t3], e2);
    return this;
  }
  containsPoint(t2) {
    return t2.x >= this.min.x && t2.x <= this.max.x && t2.y >= this.min.y && t2.y <= this.max.y && t2.z >= this.min.z && t2.z <= this.max.z;
  }
  containsBox(t2) {
    return this.min.x <= t2.min.x && t2.max.x <= this.max.x && this.min.y <= t2.min.y && t2.max.y <= this.max.y && this.min.z <= t2.min.z && t2.max.z <= this.max.z;
  }
  getParameter(t2, e2) {
    return e2.set((t2.x - this.min.x) / (this.max.x - this.min.x), (t2.y - this.min.y) / (this.max.y - this.min.y), (t2.z - this.min.z) / (this.max.z - this.min.z));
  }
  intersectsBox(t2) {
    return t2.max.x >= this.min.x && t2.min.x <= this.max.x && t2.max.y >= this.min.y && t2.min.y <= this.max.y && t2.max.z >= this.min.z && t2.min.z <= this.max.z;
  }
  intersectsSphere(t2) {
    return this.clampPoint(t2.center, Pi), Pi.distanceToSquared(t2.center) <= t2.radius * t2.radius;
  }
  intersectsPlane(t2) {
    let e2, s2;
    return t2.normal.x > 0 ? (e2 = t2.normal.x * this.min.x, s2 = t2.normal.x * this.max.x) : (e2 = t2.normal.x * this.max.x, s2 = t2.normal.x * this.min.x), t2.normal.y > 0 ? (e2 += t2.normal.y * this.min.y, s2 += t2.normal.y * this.max.y) : (e2 += t2.normal.y * this.max.y, s2 += t2.normal.y * this.min.y), t2.normal.z > 0 ? (e2 += t2.normal.z * this.min.z, s2 += t2.normal.z * this.max.z) : (e2 += t2.normal.z * this.max.z, s2 += t2.normal.z * this.min.z), e2 <= -t2.constant && s2 >= -t2.constant;
  }
  intersectsTriangle(t2) {
    if (this.isEmpty()) return false;
    this.getCenter(Ui), Di.subVectors(this.max, Ui), Fi.subVectors(t2.a, Ui), Ni.subVectors(t2.b, Ui), Li.subVectors(t2.c, Ui), Vi.subVectors(Ni, Fi), Wi.subVectors(Li, Ni), ji.subVectors(Fi, Li);
    let e2 = [0, -Vi.z, Vi.y, 0, -Wi.z, Wi.y, 0, -ji.z, ji.y, Vi.z, 0, -Vi.x, Wi.z, 0, -Wi.x, ji.z, 0, -ji.x, -Vi.y, Vi.x, 0, -Wi.y, Wi.x, 0, -ji.y, ji.x, 0];
    return !!Ji(e2, Fi, Ni, Li, Di) && (e2 = [1, 0, 0, 0, 1, 0, 0, 0, 1], !!Ji(e2, Fi, Ni, Li, Di) && (Hi.crossVectors(Vi, Wi), e2 = [Hi.x, Hi.y, Hi.z], Ji(e2, Fi, Ni, Li, Di)));
  }
  clampPoint(t2, e2) {
    return e2.copy(t2).clamp(this.min, this.max);
  }
  distanceToPoint(t2) {
    return this.clampPoint(t2, Pi).distanceTo(t2);
  }
  getBoundingSphere(t2) {
    return this.isEmpty() ? t2.makeEmpty() : (this.getCenter(t2.center), t2.radius = 0.5 * this.getSize(Pi).length()), t2;
  }
  intersect(t2) {
    return this.min.max(t2.min), this.max.min(t2.max), this.isEmpty() && this.makeEmpty(), this;
  }
  union(t2) {
    return this.min.min(t2.min), this.max.max(t2.max), this;
  }
  applyMatrix4(t2) {
    return this.isEmpty() || (Ei[0].set(this.min.x, this.min.y, this.min.z).applyMatrix4(t2), Ei[1].set(this.min.x, this.min.y, this.max.z).applyMatrix4(t2), Ei[2].set(this.min.x, this.max.y, this.min.z).applyMatrix4(t2), Ei[3].set(this.min.x, this.max.y, this.max.z).applyMatrix4(t2), Ei[4].set(this.max.x, this.min.y, this.min.z).applyMatrix4(t2), Ei[5].set(this.max.x, this.min.y, this.max.z).applyMatrix4(t2), Ei[6].set(this.max.x, this.max.y, this.min.z).applyMatrix4(t2), Ei[7].set(this.max.x, this.max.y, this.max.z).applyMatrix4(t2), this.setFromPoints(Ei)), this;
  }
  translate(t2) {
    return this.min.add(t2), this.max.add(t2), this;
  }
  equals(t2) {
    return t2.min.equals(this.min) && t2.max.equals(this.max);
  }
}
const Ei = [new Ii(), new Ii(), new Ii(), new Ii(), new Ii(), new Ii(), new Ii(), new Ii()], Pi = new Ii(), Oi = new Ri(), Fi = new Ii(), Ni = new Ii(), Li = new Ii(), Vi = new Ii(), Wi = new Ii(), ji = new Ii(), Ui = new Ii(), Di = new Ii(), Hi = new Ii(), qi = new Ii();
function Ji(t2, e2, s2, i2, r2) {
  for (let n2 = 0, o = t2.length - 3; n2 <= o; n2 += 3) {
    qi.fromArray(t2, n2);
    const o2 = r2.x * Math.abs(qi.x) + r2.y * Math.abs(qi.y) + r2.z * Math.abs(qi.z), a = e2.dot(qi), h2 = s2.dot(qi), l2 = i2.dot(qi);
    if (Math.max(-Math.max(a, h2, l2), Math.min(a, h2, l2)) > o2) return false;
  }
  return true;
}
const Xi = new Ri(), Yi = new Ii(), Zi = new Ii();
class Gi {
  constructor(t2 = new Ii(), e2 = -1) {
    this.isSphere = true, this.center = t2, this.radius = e2;
  }
  set(t2, e2) {
    return this.center.copy(t2), this.radius = e2, this;
  }
  setFromPoints(t2, e2) {
    const s2 = this.center;
    void 0 !== e2 ? s2.copy(e2) : Xi.setFromPoints(t2).getCenter(s2);
    let i2 = 0;
    for (let e3 = 0, r2 = t2.length; e3 < r2; e3++) i2 = Math.max(i2, s2.distanceToSquared(t2[e3]));
    return this.radius = Math.sqrt(i2), this;
  }
  copy(t2) {
    return this.center.copy(t2.center), this.radius = t2.radius, this;
  }
  isEmpty() {
    return this.radius < 0;
  }
  makeEmpty() {
    return this.center.set(0, 0, 0), this.radius = -1, this;
  }
  containsPoint(t2) {
    return t2.distanceToSquared(this.center) <= this.radius * this.radius;
  }
  distanceToPoint(t2) {
    return t2.distanceTo(this.center) - this.radius;
  }
  intersectsSphere(t2) {
    const e2 = this.radius + t2.radius;
    return t2.center.distanceToSquared(this.center) <= e2 * e2;
  }
  intersectsBox(t2) {
    return t2.intersectsSphere(this);
  }
  intersectsPlane(t2) {
    return Math.abs(t2.distanceToPoint(this.center)) <= this.radius;
  }
  clampPoint(t2, e2) {
    const s2 = this.center.distanceToSquared(t2);
    return e2.copy(t2), s2 > this.radius * this.radius && (e2.sub(this.center).normalize(), e2.multiplyScalar(this.radius).add(this.center)), e2;
  }
  getBoundingBox(t2) {
    return this.isEmpty() ? (t2.makeEmpty(), t2) : (t2.set(this.center, this.center), t2.expandByScalar(this.radius), t2);
  }
  applyMatrix4(t2) {
    return this.center.applyMatrix4(t2), this.radius = this.radius * t2.getMaxScaleOnAxis(), this;
  }
  translate(t2) {
    return this.center.add(t2), this;
  }
  expandByPoint(t2) {
    if (this.isEmpty()) return this.center.copy(t2), this.radius = 0, this;
    Yi.subVectors(t2, this.center);
    const e2 = Yi.lengthSq();
    if (e2 > this.radius * this.radius) {
      const t3 = Math.sqrt(e2), s2 = 0.5 * (t3 - this.radius);
      this.center.addScaledVector(Yi, s2 / t3), this.radius += s2;
    }
    return this;
  }
  union(t2) {
    return t2.isEmpty() ? this : this.isEmpty() ? (this.copy(t2), this) : (true === this.center.equals(t2.center) ? this.radius = Math.max(this.radius, t2.radius) : (Zi.subVectors(t2.center, this.center).setLength(t2.radius), this.expandByPoint(Yi.copy(t2.center).add(Zi)), this.expandByPoint(Yi.copy(t2.center).sub(Zi))), this);
  }
  equals(t2) {
    return t2.center.equals(this.center) && t2.radius === this.radius;
  }
  clone() {
    return new this.constructor().copy(this);
  }
}
const $i = new Ii(), Qi = new Ii(), Ki = new Ii(), tr = new Ii(), er = new Ii(), sr = new Ii(), ir = new Ii();
class rr {
  constructor(t2 = new Ii(), e2 = new Ii(0, 0, -1)) {
    this.origin = t2, this.direction = e2;
  }
  set(t2, e2) {
    return this.origin.copy(t2), this.direction.copy(e2), this;
  }
  copy(t2) {
    return this.origin.copy(t2.origin), this.direction.copy(t2.direction), this;
  }
  at(t2, e2) {
    return e2.copy(this.origin).addScaledVector(this.direction, t2);
  }
  lookAt(t2) {
    return this.direction.copy(t2).sub(this.origin).normalize(), this;
  }
  recast(t2) {
    return this.origin.copy(this.at(t2, $i)), this;
  }
  closestPointToPoint(t2, e2) {
    e2.subVectors(t2, this.origin);
    const s2 = e2.dot(this.direction);
    return s2 < 0 ? e2.copy(this.origin) : e2.copy(this.origin).addScaledVector(this.direction, s2);
  }
  distanceToPoint(t2) {
    return Math.sqrt(this.distanceSqToPoint(t2));
  }
  distanceSqToPoint(t2) {
    const e2 = $i.subVectors(t2, this.origin).dot(this.direction);
    return e2 < 0 ? this.origin.distanceToSquared(t2) : ($i.copy(this.origin).addScaledVector(this.direction, e2), $i.distanceToSquared(t2));
  }
  distanceSqToSegment(t2, e2, s2, i2) {
    Qi.copy(t2).add(e2).multiplyScalar(0.5), Ki.copy(e2).sub(t2).normalize(), tr.copy(this.origin).sub(Qi);
    const r2 = 0.5 * t2.distanceTo(e2), n2 = -this.direction.dot(Ki), o = tr.dot(this.direction), a = -tr.dot(Ki), h2 = tr.lengthSq(), l2 = Math.abs(1 - n2 * n2);
    let c2, u2, d2, p2;
    if (l2 > 0) if (c2 = n2 * a - o, u2 = n2 * o - a, p2 = r2 * l2, c2 >= 0) if (u2 >= -p2) if (u2 <= p2) {
      const t3 = 1 / l2;
      c2 *= t3, u2 *= t3, d2 = c2 * (c2 + n2 * u2 + 2 * o) + u2 * (n2 * c2 + u2 + 2 * a) + h2;
    } else u2 = r2, c2 = Math.max(0, -(n2 * u2 + o)), d2 = -c2 * c2 + u2 * (u2 + 2 * a) + h2;
    else u2 = -r2, c2 = Math.max(0, -(n2 * u2 + o)), d2 = -c2 * c2 + u2 * (u2 + 2 * a) + h2;
    else u2 <= -p2 ? (c2 = Math.max(0, -(-n2 * r2 + o)), u2 = c2 > 0 ? -r2 : Math.min(Math.max(-r2, -a), r2), d2 = -c2 * c2 + u2 * (u2 + 2 * a) + h2) : u2 <= p2 ? (c2 = 0, u2 = Math.min(Math.max(-r2, -a), r2), d2 = u2 * (u2 + 2 * a) + h2) : (c2 = Math.max(0, -(n2 * r2 + o)), u2 = c2 > 0 ? r2 : Math.min(Math.max(-r2, -a), r2), d2 = -c2 * c2 + u2 * (u2 + 2 * a) + h2);
    else u2 = n2 > 0 ? -r2 : r2, c2 = Math.max(0, -(n2 * u2 + o)), d2 = -c2 * c2 + u2 * (u2 + 2 * a) + h2;
    return s2 && s2.copy(this.origin).addScaledVector(this.direction, c2), i2 && i2.copy(Qi).addScaledVector(Ki, u2), d2;
  }
  intersectSphere(t2, e2) {
    $i.subVectors(t2.center, this.origin);
    const s2 = $i.dot(this.direction), i2 = $i.dot($i) - s2 * s2, r2 = t2.radius * t2.radius;
    if (i2 > r2) return null;
    const n2 = Math.sqrt(r2 - i2), o = s2 - n2, a = s2 + n2;
    return a < 0 ? null : o < 0 ? this.at(a, e2) : this.at(o, e2);
  }
  intersectsSphere(t2) {
    return this.distanceSqToPoint(t2.center) <= t2.radius * t2.radius;
  }
  distanceToPlane(t2) {
    const e2 = t2.normal.dot(this.direction);
    if (0 === e2) return 0 === t2.distanceToPoint(this.origin) ? 0 : null;
    const s2 = -(this.origin.dot(t2.normal) + t2.constant) / e2;
    return s2 >= 0 ? s2 : null;
  }
  intersectPlane(t2, e2) {
    const s2 = this.distanceToPlane(t2);
    return null === s2 ? null : this.at(s2, e2);
  }
  intersectsPlane(t2) {
    const e2 = t2.distanceToPoint(this.origin);
    if (0 === e2) return true;
    return t2.normal.dot(this.direction) * e2 < 0;
  }
  intersectBox(t2, e2) {
    let s2, i2, r2, n2, o, a;
    const h2 = 1 / this.direction.x, l2 = 1 / this.direction.y, c2 = 1 / this.direction.z, u2 = this.origin;
    return h2 >= 0 ? (s2 = (t2.min.x - u2.x) * h2, i2 = (t2.max.x - u2.x) * h2) : (s2 = (t2.max.x - u2.x) * h2, i2 = (t2.min.x - u2.x) * h2), l2 >= 0 ? (r2 = (t2.min.y - u2.y) * l2, n2 = (t2.max.y - u2.y) * l2) : (r2 = (t2.max.y - u2.y) * l2, n2 = (t2.min.y - u2.y) * l2), s2 > n2 || r2 > i2 ? null : ((r2 > s2 || isNaN(s2)) && (s2 = r2), (n2 < i2 || isNaN(i2)) && (i2 = n2), c2 >= 0 ? (o = (t2.min.z - u2.z) * c2, a = (t2.max.z - u2.z) * c2) : (o = (t2.max.z - u2.z) * c2, a = (t2.min.z - u2.z) * c2), s2 > a || o > i2 ? null : ((o > s2 || s2 != s2) && (s2 = o), (a < i2 || i2 != i2) && (i2 = a), i2 < 0 ? null : this.at(s2 >= 0 ? s2 : i2, e2)));
  }
  intersectsBox(t2) {
    return null !== this.intersectBox(t2, $i);
  }
  intersectTriangle(t2, e2, s2, i2, r2) {
    er.subVectors(e2, t2), sr.subVectors(s2, t2), ir.crossVectors(er, sr);
    let n2, o = this.direction.dot(ir);
    if (o > 0) {
      if (i2) return null;
      n2 = 1;
    } else {
      if (!(o < 0)) return null;
      n2 = -1, o = -o;
    }
    tr.subVectors(this.origin, t2);
    const a = n2 * this.direction.dot(sr.crossVectors(tr, sr));
    if (a < 0) return null;
    const h2 = n2 * this.direction.dot(er.cross(tr));
    if (h2 < 0) return null;
    if (a + h2 > o) return null;
    const l2 = -n2 * tr.dot(ir);
    return l2 < 0 ? null : this.at(l2 / o, r2);
  }
  applyMatrix4(t2) {
    return this.origin.applyMatrix4(t2), this.direction.transformDirection(t2), this;
  }
  equals(t2) {
    return t2.origin.equals(this.origin) && t2.direction.equals(this.direction);
  }
  clone() {
    return new this.constructor().copy(this);
  }
}
class nr {
  constructor(t2, e2, s2, i2, r2, n2, o, a, h2, l2, c2, u2, d2, p2, m2, y2) {
    nr.prototype.isMatrix4 = true, this.elements = [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1], void 0 !== t2 && this.set(t2, e2, s2, i2, r2, n2, o, a, h2, l2, c2, u2, d2, p2, m2, y2);
  }
  set(t2, e2, s2, i2, r2, n2, o, a, h2, l2, c2, u2, d2, p2, m2, y2) {
    const f2 = this.elements;
    return f2[0] = t2, f2[4] = e2, f2[8] = s2, f2[12] = i2, f2[1] = r2, f2[5] = n2, f2[9] = o, f2[13] = a, f2[2] = h2, f2[6] = l2, f2[10] = c2, f2[14] = u2, f2[3] = d2, f2[7] = p2, f2[11] = m2, f2[15] = y2, this;
  }
  identity() {
    return this.set(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1), this;
  }
  clone() {
    return new nr().fromArray(this.elements);
  }
  copy(t2) {
    const e2 = this.elements, s2 = t2.elements;
    return e2[0] = s2[0], e2[1] = s2[1], e2[2] = s2[2], e2[3] = s2[3], e2[4] = s2[4], e2[5] = s2[5], e2[6] = s2[6], e2[7] = s2[7], e2[8] = s2[8], e2[9] = s2[9], e2[10] = s2[10], e2[11] = s2[11], e2[12] = s2[12], e2[13] = s2[13], e2[14] = s2[14], e2[15] = s2[15], this;
  }
  copyPosition(t2) {
    const e2 = this.elements, s2 = t2.elements;
    return e2[12] = s2[12], e2[13] = s2[13], e2[14] = s2[14], this;
  }
  setFromMatrix3(t2) {
    const e2 = t2.elements;
    return this.set(e2[0], e2[3], e2[6], 0, e2[1], e2[4], e2[7], 0, e2[2], e2[5], e2[8], 0, 0, 0, 0, 1), this;
  }
  extractBasis(t2, e2, s2) {
    return t2.setFromMatrixColumn(this, 0), e2.setFromMatrixColumn(this, 1), s2.setFromMatrixColumn(this, 2), this;
  }
  makeBasis(t2, e2, s2) {
    return this.set(t2.x, e2.x, s2.x, 0, t2.y, e2.y, s2.y, 0, t2.z, e2.z, s2.z, 0, 0, 0, 0, 1), this;
  }
  extractRotation(t2) {
    const e2 = this.elements, s2 = t2.elements, i2 = 1 / or.setFromMatrixColumn(t2, 0).length(), r2 = 1 / or.setFromMatrixColumn(t2, 1).length(), n2 = 1 / or.setFromMatrixColumn(t2, 2).length();
    return e2[0] = s2[0] * i2, e2[1] = s2[1] * i2, e2[2] = s2[2] * i2, e2[3] = 0, e2[4] = s2[4] * r2, e2[5] = s2[5] * r2, e2[6] = s2[6] * r2, e2[7] = 0, e2[8] = s2[8] * n2, e2[9] = s2[9] * n2, e2[10] = s2[10] * n2, e2[11] = 0, e2[12] = 0, e2[13] = 0, e2[14] = 0, e2[15] = 1, this;
  }
  makeRotationFromEuler(t2) {
    const e2 = this.elements, s2 = t2.x, i2 = t2.y, r2 = t2.z, n2 = Math.cos(s2), o = Math.sin(s2), a = Math.cos(i2), h2 = Math.sin(i2), l2 = Math.cos(r2), c2 = Math.sin(r2);
    if ("XYZ" === t2.order) {
      const t3 = n2 * l2, s3 = n2 * c2, i3 = o * l2, r3 = o * c2;
      e2[0] = a * l2, e2[4] = -a * c2, e2[8] = h2, e2[1] = s3 + i3 * h2, e2[5] = t3 - r3 * h2, e2[9] = -o * a, e2[2] = r3 - t3 * h2, e2[6] = i3 + s3 * h2, e2[10] = n2 * a;
    } else if ("YXZ" === t2.order) {
      const t3 = a * l2, s3 = a * c2, i3 = h2 * l2, r3 = h2 * c2;
      e2[0] = t3 + r3 * o, e2[4] = i3 * o - s3, e2[8] = n2 * h2, e2[1] = n2 * c2, e2[5] = n2 * l2, e2[9] = -o, e2[2] = s3 * o - i3, e2[6] = r3 + t3 * o, e2[10] = n2 * a;
    } else if ("ZXY" === t2.order) {
      const t3 = a * l2, s3 = a * c2, i3 = h2 * l2, r3 = h2 * c2;
      e2[0] = t3 - r3 * o, e2[4] = -n2 * c2, e2[8] = i3 + s3 * o, e2[1] = s3 + i3 * o, e2[5] = n2 * l2, e2[9] = r3 - t3 * o, e2[2] = -n2 * h2, e2[6] = o, e2[10] = n2 * a;
    } else if ("ZYX" === t2.order) {
      const t3 = n2 * l2, s3 = n2 * c2, i3 = o * l2, r3 = o * c2;
      e2[0] = a * l2, e2[4] = i3 * h2 - s3, e2[8] = t3 * h2 + r3, e2[1] = a * c2, e2[5] = r3 * h2 + t3, e2[9] = s3 * h2 - i3, e2[2] = -h2, e2[6] = o * a, e2[10] = n2 * a;
    } else if ("YZX" === t2.order) {
      const t3 = n2 * a, s3 = n2 * h2, i3 = o * a, r3 = o * h2;
      e2[0] = a * l2, e2[4] = r3 - t3 * c2, e2[8] = i3 * c2 + s3, e2[1] = c2, e2[5] = n2 * l2, e2[9] = -o * l2, e2[2] = -h2 * l2, e2[6] = s3 * c2 + i3, e2[10] = t3 - r3 * c2;
    } else if ("XZY" === t2.order) {
      const t3 = n2 * a, s3 = n2 * h2, i3 = o * a, r3 = o * h2;
      e2[0] = a * l2, e2[4] = -c2, e2[8] = h2 * l2, e2[1] = t3 * c2 + r3, e2[5] = n2 * l2, e2[9] = s3 * c2 - i3, e2[2] = i3 * c2 - s3, e2[6] = o * l2, e2[10] = r3 * c2 + t3;
    }
    return e2[3] = 0, e2[7] = 0, e2[11] = 0, e2[12] = 0, e2[13] = 0, e2[14] = 0, e2[15] = 1, this;
  }
  makeRotationFromQuaternion(t2) {
    return this.compose(hr, t2, lr);
  }
  lookAt(t2, e2, s2) {
    const i2 = this.elements;
    return dr.subVectors(t2, e2), 0 === dr.lengthSq() && (dr.z = 1), dr.normalize(), cr.crossVectors(s2, dr), 0 === cr.lengthSq() && (1 === Math.abs(s2.z) ? dr.x += 1e-4 : dr.z += 1e-4, dr.normalize(), cr.crossVectors(s2, dr)), cr.normalize(), ur.crossVectors(dr, cr), i2[0] = cr.x, i2[4] = ur.x, i2[8] = dr.x, i2[1] = cr.y, i2[5] = ur.y, i2[9] = dr.y, i2[2] = cr.z, i2[6] = ur.z, i2[10] = dr.z, this;
  }
  multiply(t2) {
    return this.multiplyMatrices(this, t2);
  }
  premultiply(t2) {
    return this.multiplyMatrices(t2, this);
  }
  multiplyMatrices(t2, e2) {
    const s2 = t2.elements, i2 = e2.elements, r2 = this.elements, n2 = s2[0], o = s2[4], a = s2[8], h2 = s2[12], l2 = s2[1], c2 = s2[5], u2 = s2[9], d2 = s2[13], p2 = s2[2], m2 = s2[6], y2 = s2[10], f2 = s2[14], g2 = s2[3], x2 = s2[7], b2 = s2[11], v2 = s2[15], w2 = i2[0], M2 = i2[4], S2 = i2[8], _2 = i2[12], A2 = i2[1], T2 = i2[5], z2 = i2[9], C2 = i2[13], I2 = i2[2], B2 = i2[6], k2 = i2[10], R2 = i2[14], E2 = i2[3], P2 = i2[7], O2 = i2[11], F2 = i2[15];
    return r2[0] = n2 * w2 + o * A2 + a * I2 + h2 * E2, r2[4] = n2 * M2 + o * T2 + a * B2 + h2 * P2, r2[8] = n2 * S2 + o * z2 + a * k2 + h2 * O2, r2[12] = n2 * _2 + o * C2 + a * R2 + h2 * F2, r2[1] = l2 * w2 + c2 * A2 + u2 * I2 + d2 * E2, r2[5] = l2 * M2 + c2 * T2 + u2 * B2 + d2 * P2, r2[9] = l2 * S2 + c2 * z2 + u2 * k2 + d2 * O2, r2[13] = l2 * _2 + c2 * C2 + u2 * R2 + d2 * F2, r2[2] = p2 * w2 + m2 * A2 + y2 * I2 + f2 * E2, r2[6] = p2 * M2 + m2 * T2 + y2 * B2 + f2 * P2, r2[10] = p2 * S2 + m2 * z2 + y2 * k2 + f2 * O2, r2[14] = p2 * _2 + m2 * C2 + y2 * R2 + f2 * F2, r2[3] = g2 * w2 + x2 * A2 + b2 * I2 + v2 * E2, r2[7] = g2 * M2 + x2 * T2 + b2 * B2 + v2 * P2, r2[11] = g2 * S2 + x2 * z2 + b2 * k2 + v2 * O2, r2[15] = g2 * _2 + x2 * C2 + b2 * R2 + v2 * F2, this;
  }
  multiplyScalar(t2) {
    const e2 = this.elements;
    return e2[0] *= t2, e2[4] *= t2, e2[8] *= t2, e2[12] *= t2, e2[1] *= t2, e2[5] *= t2, e2[9] *= t2, e2[13] *= t2, e2[2] *= t2, e2[6] *= t2, e2[10] *= t2, e2[14] *= t2, e2[3] *= t2, e2[7] *= t2, e2[11] *= t2, e2[15] *= t2, this;
  }
  determinant() {
    const t2 = this.elements, e2 = t2[0], s2 = t2[4], i2 = t2[8], r2 = t2[12], n2 = t2[1], o = t2[5], a = t2[9], h2 = t2[13], l2 = t2[2], c2 = t2[6], u2 = t2[10], d2 = t2[14];
    return t2[3] * (+r2 * a * c2 - i2 * h2 * c2 - r2 * o * u2 + s2 * h2 * u2 + i2 * o * d2 - s2 * a * d2) + t2[7] * (+e2 * a * d2 - e2 * h2 * u2 + r2 * n2 * u2 - i2 * n2 * d2 + i2 * h2 * l2 - r2 * a * l2) + t2[11] * (+e2 * h2 * c2 - e2 * o * d2 - r2 * n2 * c2 + s2 * n2 * d2 + r2 * o * l2 - s2 * h2 * l2) + t2[15] * (-i2 * o * l2 - e2 * a * c2 + e2 * o * u2 + i2 * n2 * c2 - s2 * n2 * u2 + s2 * a * l2);
  }
  transpose() {
    const t2 = this.elements;
    let e2;
    return e2 = t2[1], t2[1] = t2[4], t2[4] = e2, e2 = t2[2], t2[2] = t2[8], t2[8] = e2, e2 = t2[6], t2[6] = t2[9], t2[9] = e2, e2 = t2[3], t2[3] = t2[12], t2[12] = e2, e2 = t2[7], t2[7] = t2[13], t2[13] = e2, e2 = t2[11], t2[11] = t2[14], t2[14] = e2, this;
  }
  setPosition(t2, e2, s2) {
    const i2 = this.elements;
    return t2.isVector3 ? (i2[12] = t2.x, i2[13] = t2.y, i2[14] = t2.z) : (i2[12] = t2, i2[13] = e2, i2[14] = s2), this;
  }
  invert() {
    const t2 = this.elements, e2 = t2[0], s2 = t2[1], i2 = t2[2], r2 = t2[3], n2 = t2[4], o = t2[5], a = t2[6], h2 = t2[7], l2 = t2[8], c2 = t2[9], u2 = t2[10], d2 = t2[11], p2 = t2[12], m2 = t2[13], y2 = t2[14], f2 = t2[15], g2 = c2 * y2 * h2 - m2 * u2 * h2 + m2 * a * d2 - o * y2 * d2 - c2 * a * f2 + o * u2 * f2, x2 = p2 * u2 * h2 - l2 * y2 * h2 - p2 * a * d2 + n2 * y2 * d2 + l2 * a * f2 - n2 * u2 * f2, b2 = l2 * m2 * h2 - p2 * c2 * h2 + p2 * o * d2 - n2 * m2 * d2 - l2 * o * f2 + n2 * c2 * f2, v2 = p2 * c2 * a - l2 * m2 * a - p2 * o * u2 + n2 * m2 * u2 + l2 * o * y2 - n2 * c2 * y2, w2 = e2 * g2 + s2 * x2 + i2 * b2 + r2 * v2;
    if (0 === w2) return this.set(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
    const M2 = 1 / w2;
    return t2[0] = g2 * M2, t2[1] = (m2 * u2 * r2 - c2 * y2 * r2 - m2 * i2 * d2 + s2 * y2 * d2 + c2 * i2 * f2 - s2 * u2 * f2) * M2, t2[2] = (o * y2 * r2 - m2 * a * r2 + m2 * i2 * h2 - s2 * y2 * h2 - o * i2 * f2 + s2 * a * f2) * M2, t2[3] = (c2 * a * r2 - o * u2 * r2 - c2 * i2 * h2 + s2 * u2 * h2 + o * i2 * d2 - s2 * a * d2) * M2, t2[4] = x2 * M2, t2[5] = (l2 * y2 * r2 - p2 * u2 * r2 + p2 * i2 * d2 - e2 * y2 * d2 - l2 * i2 * f2 + e2 * u2 * f2) * M2, t2[6] = (p2 * a * r2 - n2 * y2 * r2 - p2 * i2 * h2 + e2 * y2 * h2 + n2 * i2 * f2 - e2 * a * f2) * M2, t2[7] = (n2 * u2 * r2 - l2 * a * r2 + l2 * i2 * h2 - e2 * u2 * h2 - n2 * i2 * d2 + e2 * a * d2) * M2, t2[8] = b2 * M2, t2[9] = (p2 * c2 * r2 - l2 * m2 * r2 - p2 * s2 * d2 + e2 * m2 * d2 + l2 * s2 * f2 - e2 * c2 * f2) * M2, t2[10] = (n2 * m2 * r2 - p2 * o * r2 + p2 * s2 * h2 - e2 * m2 * h2 - n2 * s2 * f2 + e2 * o * f2) * M2, t2[11] = (l2 * o * r2 - n2 * c2 * r2 - l2 * s2 * h2 + e2 * c2 * h2 + n2 * s2 * d2 - e2 * o * d2) * M2, t2[12] = v2 * M2, t2[13] = (l2 * m2 * i2 - p2 * c2 * i2 + p2 * s2 * u2 - e2 * m2 * u2 - l2 * s2 * y2 + e2 * c2 * y2) * M2, t2[14] = (p2 * o * i2 - n2 * m2 * i2 - p2 * s2 * a + e2 * m2 * a + n2 * s2 * y2 - e2 * o * y2) * M2, t2[15] = (n2 * c2 * i2 - l2 * o * i2 + l2 * s2 * a - e2 * c2 * a - n2 * s2 * u2 + e2 * o * u2) * M2, this;
  }
  scale(t2) {
    const e2 = this.elements, s2 = t2.x, i2 = t2.y, r2 = t2.z;
    return e2[0] *= s2, e2[4] *= i2, e2[8] *= r2, e2[1] *= s2, e2[5] *= i2, e2[9] *= r2, e2[2] *= s2, e2[6] *= i2, e2[10] *= r2, e2[3] *= s2, e2[7] *= i2, e2[11] *= r2, this;
  }
  getMaxScaleOnAxis() {
    const t2 = this.elements, e2 = t2[0] * t2[0] + t2[1] * t2[1] + t2[2] * t2[2], s2 = t2[4] * t2[4] + t2[5] * t2[5] + t2[6] * t2[6], i2 = t2[8] * t2[8] + t2[9] * t2[9] + t2[10] * t2[10];
    return Math.sqrt(Math.max(e2, s2, i2));
  }
  makeTranslation(t2, e2, s2) {
    return t2.isVector3 ? this.set(1, 0, 0, t2.x, 0, 1, 0, t2.y, 0, 0, 1, t2.z, 0, 0, 0, 1) : this.set(1, 0, 0, t2, 0, 1, 0, e2, 0, 0, 1, s2, 0, 0, 0, 1), this;
  }
  makeRotationX(t2) {
    const e2 = Math.cos(t2), s2 = Math.sin(t2);
    return this.set(1, 0, 0, 0, 0, e2, -s2, 0, 0, s2, e2, 0, 0, 0, 0, 1), this;
  }
  makeRotationY(t2) {
    const e2 = Math.cos(t2), s2 = Math.sin(t2);
    return this.set(e2, 0, s2, 0, 0, 1, 0, 0, -s2, 0, e2, 0, 0, 0, 0, 1), this;
  }
  makeRotationZ(t2) {
    const e2 = Math.cos(t2), s2 = Math.sin(t2);
    return this.set(e2, -s2, 0, 0, s2, e2, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1), this;
  }
  makeRotationAxis(t2, e2) {
    const s2 = Math.cos(e2), i2 = Math.sin(e2), r2 = 1 - s2, n2 = t2.x, o = t2.y, a = t2.z, h2 = r2 * n2, l2 = r2 * o;
    return this.set(h2 * n2 + s2, h2 * o - i2 * a, h2 * a + i2 * o, 0, h2 * o + i2 * a, l2 * o + s2, l2 * a - i2 * n2, 0, h2 * a - i2 * o, l2 * a + i2 * n2, r2 * a * a + s2, 0, 0, 0, 0, 1), this;
  }
  makeScale(t2, e2, s2) {
    return this.set(t2, 0, 0, 0, 0, e2, 0, 0, 0, 0, s2, 0, 0, 0, 0, 1), this;
  }
  makeShear(t2, e2, s2, i2, r2, n2) {
    return this.set(1, s2, r2, 0, t2, 1, n2, 0, e2, i2, 1, 0, 0, 0, 0, 1), this;
  }
  compose(t2, e2, s2) {
    const i2 = this.elements, r2 = e2._x, n2 = e2._y, o = e2._z, a = e2._w, h2 = r2 + r2, l2 = n2 + n2, c2 = o + o, u2 = r2 * h2, d2 = r2 * l2, p2 = r2 * c2, m2 = n2 * l2, y2 = n2 * c2, f2 = o * c2, g2 = a * h2, x2 = a * l2, b2 = a * c2, v2 = s2.x, w2 = s2.y, M2 = s2.z;
    return i2[0] = (1 - (m2 + f2)) * v2, i2[1] = (d2 + b2) * v2, i2[2] = (p2 - x2) * v2, i2[3] = 0, i2[4] = (d2 - b2) * w2, i2[5] = (1 - (u2 + f2)) * w2, i2[6] = (y2 + g2) * w2, i2[7] = 0, i2[8] = (p2 + x2) * M2, i2[9] = (y2 - g2) * M2, i2[10] = (1 - (u2 + m2)) * M2, i2[11] = 0, i2[12] = t2.x, i2[13] = t2.y, i2[14] = t2.z, i2[15] = 1, this;
  }
  decompose(t2, e2, s2) {
    const i2 = this.elements;
    let r2 = or.set(i2[0], i2[1], i2[2]).length();
    const n2 = or.set(i2[4], i2[5], i2[6]).length(), o = or.set(i2[8], i2[9], i2[10]).length();
    this.determinant() < 0 && (r2 = -r2), t2.x = i2[12], t2.y = i2[13], t2.z = i2[14], ar.copy(this);
    const a = 1 / r2, h2 = 1 / n2, l2 = 1 / o;
    return ar.elements[0] *= a, ar.elements[1] *= a, ar.elements[2] *= a, ar.elements[4] *= h2, ar.elements[5] *= h2, ar.elements[6] *= h2, ar.elements[8] *= l2, ar.elements[9] *= l2, ar.elements[10] *= l2, e2.setFromRotationMatrix(ar), s2.x = r2, s2.y = n2, s2.z = o, this;
  }
  makePerspective(t2, e2, s2, i2, r2, n2, o = 2e3) {
    const a = this.elements, h2 = 2 * r2 / (e2 - t2), l2 = 2 * r2 / (s2 - i2), c2 = (e2 + t2) / (e2 - t2), u2 = (s2 + i2) / (s2 - i2);
    let d2, p2;
    if (o === Os) d2 = -(n2 + r2) / (n2 - r2), p2 = -2 * n2 * r2 / (n2 - r2);
    else {
      if (o !== Fs) throw new Error("THREE.Matrix4.makePerspective(): Invalid coordinate system: " + o);
      d2 = -n2 / (n2 - r2), p2 = -n2 * r2 / (n2 - r2);
    }
    return a[0] = h2, a[4] = 0, a[8] = c2, a[12] = 0, a[1] = 0, a[5] = l2, a[9] = u2, a[13] = 0, a[2] = 0, a[6] = 0, a[10] = d2, a[14] = p2, a[3] = 0, a[7] = 0, a[11] = -1, a[15] = 0, this;
  }
  makeOrthographic(t2, e2, s2, i2, r2, n2, o = 2e3) {
    const a = this.elements, h2 = 1 / (e2 - t2), l2 = 1 / (s2 - i2), c2 = 1 / (n2 - r2), u2 = (e2 + t2) * h2, d2 = (s2 + i2) * l2;
    let p2, m2;
    if (o === Os) p2 = (n2 + r2) * c2, m2 = -2 * c2;
    else {
      if (o !== Fs) throw new Error("THREE.Matrix4.makeOrthographic(): Invalid coordinate system: " + o);
      p2 = r2 * c2, m2 = -1 * c2;
    }
    return a[0] = 2 * h2, a[4] = 0, a[8] = 0, a[12] = -u2, a[1] = 0, a[5] = 2 * l2, a[9] = 0, a[13] = -d2, a[2] = 0, a[6] = 0, a[10] = m2, a[14] = -p2, a[3] = 0, a[7] = 0, a[11] = 0, a[15] = 1, this;
  }
  equals(t2) {
    const e2 = this.elements, s2 = t2.elements;
    for (let t3 = 0; t3 < 16; t3++) if (e2[t3] !== s2[t3]) return false;
    return true;
  }
  fromArray(t2, e2 = 0) {
    for (let s2 = 0; s2 < 16; s2++) this.elements[s2] = t2[s2 + e2];
    return this;
  }
  toArray(t2 = [], e2 = 0) {
    const s2 = this.elements;
    return t2[e2] = s2[0], t2[e2 + 1] = s2[1], t2[e2 + 2] = s2[2], t2[e2 + 3] = s2[3], t2[e2 + 4] = s2[4], t2[e2 + 5] = s2[5], t2[e2 + 6] = s2[6], t2[e2 + 7] = s2[7], t2[e2 + 8] = s2[8], t2[e2 + 9] = s2[9], t2[e2 + 10] = s2[10], t2[e2 + 11] = s2[11], t2[e2 + 12] = s2[12], t2[e2 + 13] = s2[13], t2[e2 + 14] = s2[14], t2[e2 + 15] = s2[15], t2;
  }
}
const or = new Ii(), ar = new nr(), hr = new Ii(0, 0, 0), lr = new Ii(1, 1, 1), cr = new Ii(), ur = new Ii(), dr = new Ii(), pr = new nr(), mr = new Ci();
class yr {
  constructor(t2 = 0, e2 = 0, s2 = 0, i2 = yr.DEFAULT_ORDER) {
    this.isEuler = true, this._x = t2, this._y = e2, this._z = s2, this._order = i2;
  }
  get x() {
    return this._x;
  }
  set x(t2) {
    this._x = t2, this._onChangeCallback();
  }
  get y() {
    return this._y;
  }
  set y(t2) {
    this._y = t2, this._onChangeCallback();
  }
  get z() {
    return this._z;
  }
  set z(t2) {
    this._z = t2, this._onChangeCallback();
  }
  get order() {
    return this._order;
  }
  set order(t2) {
    this._order = t2, this._onChangeCallback();
  }
  set(t2, e2, s2, i2 = this._order) {
    return this._x = t2, this._y = e2, this._z = s2, this._order = i2, this._onChangeCallback(), this;
  }
  clone() {
    return new this.constructor(this._x, this._y, this._z, this._order);
  }
  copy(t2) {
    return this._x = t2._x, this._y = t2._y, this._z = t2._z, this._order = t2._order, this._onChangeCallback(), this;
  }
  setFromRotationMatrix(t2, e2 = this._order, s2 = true) {
    const i2 = t2.elements, r2 = i2[0], n2 = i2[4], o = i2[8], a = i2[1], h2 = i2[5], l2 = i2[9], c2 = i2[2], u2 = i2[6], d2 = i2[10];
    switch (e2) {
      case "XYZ":
        this._y = Math.asin(Ds(o, -1, 1)), Math.abs(o) < 0.9999999 ? (this._x = Math.atan2(-l2, d2), this._z = Math.atan2(-n2, r2)) : (this._x = Math.atan2(u2, h2), this._z = 0);
        break;
      case "YXZ":
        this._x = Math.asin(-Ds(l2, -1, 1)), Math.abs(l2) < 0.9999999 ? (this._y = Math.atan2(o, d2), this._z = Math.atan2(a, h2)) : (this._y = Math.atan2(-c2, r2), this._z = 0);
        break;
      case "ZXY":
        this._x = Math.asin(Ds(u2, -1, 1)), Math.abs(u2) < 0.9999999 ? (this._y = Math.atan2(-c2, d2), this._z = Math.atan2(-n2, h2)) : (this._y = 0, this._z = Math.atan2(a, r2));
        break;
      case "ZYX":
        this._y = Math.asin(-Ds(c2, -1, 1)), Math.abs(c2) < 0.9999999 ? (this._x = Math.atan2(u2, d2), this._z = Math.atan2(a, r2)) : (this._x = 0, this._z = Math.atan2(-n2, h2));
        break;
      case "YZX":
        this._z = Math.asin(Ds(a, -1, 1)), Math.abs(a) < 0.9999999 ? (this._x = Math.atan2(-l2, h2), this._y = Math.atan2(-c2, r2)) : (this._x = 0, this._y = Math.atan2(o, d2));
        break;
      case "XZY":
        this._z = Math.asin(-Ds(n2, -1, 1)), Math.abs(n2) < 0.9999999 ? (this._x = Math.atan2(u2, h2), this._y = Math.atan2(o, r2)) : (this._x = Math.atan2(-l2, d2), this._y = 0);
        break;
      default:
        console.warn("THREE.Euler: .setFromRotationMatrix() encountered an unknown order: " + e2);
    }
    return this._order = e2, true === s2 && this._onChangeCallback(), this;
  }
  setFromQuaternion(t2, e2, s2) {
    return pr.makeRotationFromQuaternion(t2), this.setFromRotationMatrix(pr, e2, s2);
  }
  setFromVector3(t2, e2 = this._order) {
    return this.set(t2.x, t2.y, t2.z, e2);
  }
  reorder(t2) {
    return mr.setFromEuler(this), this.setFromQuaternion(mr, t2);
  }
  equals(t2) {
    return t2._x === this._x && t2._y === this._y && t2._z === this._z && t2._order === this._order;
  }
  fromArray(t2) {
    return this._x = t2[0], this._y = t2[1], this._z = t2[2], void 0 !== t2[3] && (this._order = t2[3]), this._onChangeCallback(), this;
  }
  toArray(t2 = [], e2 = 0) {
    return t2[e2] = this._x, t2[e2 + 1] = this._y, t2[e2 + 2] = this._z, t2[e2 + 3] = this._order, t2;
  }
  _onChange(t2) {
    return this._onChangeCallback = t2, this;
  }
  _onChangeCallback() {
  }
  *[Symbol.iterator]() {
    yield this._x, yield this._y, yield this._z, yield this._order;
  }
}
yr.DEFAULT_ORDER = "XYZ";
class fr {
  constructor() {
    this.mask = 1;
  }
  set(t2) {
    this.mask = 1 << t2 >>> 0;
  }
  enable(t2) {
    this.mask |= 1 << t2;
  }
  enableAll() {
    this.mask = -1;
  }
  toggle(t2) {
    this.mask ^= 1 << t2;
  }
  disable(t2) {
    this.mask &= ~(1 << t2);
  }
  disableAll() {
    this.mask = 0;
  }
  test(t2) {
    return !!(this.mask & t2.mask);
  }
  isEnabled(t2) {
    return !!(this.mask & 1 << t2);
  }
}
let gr = 0;
const xr = new Ii(), br = new Ci(), vr = new nr(), wr = new Ii(), Mr = new Ii(), Sr = new Ii(), _r = new Ci(), Ar = new Ii(1, 0, 0), Tr = new Ii(0, 1, 0), zr = new Ii(0, 0, 1), Cr = { type: "added" }, Ir = { type: "removed" }, Br = { type: "childadded", child: null }, kr = { type: "childremoved", child: null };
class Rr extends Ns {
  constructor() {
    super(), this.isObject3D = true, Object.defineProperty(this, "id", { value: gr++ }), this.uuid = Us(), this.name = "", this.type = "Object3D", this.parent = null, this.children = [], this.up = Rr.DEFAULT_UP.clone();
    const t2 = new Ii(), e2 = new yr(), s2 = new Ci(), i2 = new Ii(1, 1, 1);
    e2._onChange(function() {
      s2.setFromEuler(e2, false);
    }), s2._onChange(function() {
      e2.setFromQuaternion(s2, void 0, false);
    }), Object.defineProperties(this, { position: { configurable: true, enumerable: true, value: t2 }, rotation: { configurable: true, enumerable: true, value: e2 }, quaternion: { configurable: true, enumerable: true, value: s2 }, scale: { configurable: true, enumerable: true, value: i2 }, modelViewMatrix: { value: new nr() }, normalMatrix: { value: new Gs() } }), this.matrix = new nr(), this.matrixWorld = new nr(), this.matrixAutoUpdate = Rr.DEFAULT_MATRIX_AUTO_UPDATE, this.matrixWorldAutoUpdate = Rr.DEFAULT_MATRIX_WORLD_AUTO_UPDATE, this.matrixWorldNeedsUpdate = false, this.layers = new fr(), this.visible = true, this.castShadow = false, this.receiveShadow = false, this.frustumCulled = true, this.renderOrder = 0, this.animations = [], this.userData = {};
  }
  onBeforeShadow() {
  }
  onAfterShadow() {
  }
  onBeforeRender() {
  }
  onAfterRender() {
  }
  applyMatrix4(t2) {
    this.matrixAutoUpdate && this.updateMatrix(), this.matrix.premultiply(t2), this.matrix.decompose(this.position, this.quaternion, this.scale);
  }
  applyQuaternion(t2) {
    return this.quaternion.premultiply(t2), this;
  }
  setRotationFromAxisAngle(t2, e2) {
    this.quaternion.setFromAxisAngle(t2, e2);
  }
  setRotationFromEuler(t2) {
    this.quaternion.setFromEuler(t2, true);
  }
  setRotationFromMatrix(t2) {
    this.quaternion.setFromRotationMatrix(t2);
  }
  setRotationFromQuaternion(t2) {
    this.quaternion.copy(t2);
  }
  rotateOnAxis(t2, e2) {
    return br.setFromAxisAngle(t2, e2), this.quaternion.multiply(br), this;
  }
  rotateOnWorldAxis(t2, e2) {
    return br.setFromAxisAngle(t2, e2), this.quaternion.premultiply(br), this;
  }
  rotateX(t2) {
    return this.rotateOnAxis(Ar, t2);
  }
  rotateY(t2) {
    return this.rotateOnAxis(Tr, t2);
  }
  rotateZ(t2) {
    return this.rotateOnAxis(zr, t2);
  }
  translateOnAxis(t2, e2) {
    return xr.copy(t2).applyQuaternion(this.quaternion), this.position.add(xr.multiplyScalar(e2)), this;
  }
  translateX(t2) {
    return this.translateOnAxis(Ar, t2);
  }
  translateY(t2) {
    return this.translateOnAxis(Tr, t2);
  }
  translateZ(t2) {
    return this.translateOnAxis(zr, t2);
  }
  localToWorld(t2) {
    return this.updateWorldMatrix(true, false), t2.applyMatrix4(this.matrixWorld);
  }
  worldToLocal(t2) {
    return this.updateWorldMatrix(true, false), t2.applyMatrix4(vr.copy(this.matrixWorld).invert());
  }
  lookAt(t2, e2, s2) {
    t2.isVector3 ? wr.copy(t2) : wr.set(t2, e2, s2);
    const i2 = this.parent;
    this.updateWorldMatrix(true, false), Mr.setFromMatrixPosition(this.matrixWorld), this.isCamera || this.isLight ? vr.lookAt(Mr, wr, this.up) : vr.lookAt(wr, Mr, this.up), this.quaternion.setFromRotationMatrix(vr), i2 && (vr.extractRotation(i2.matrixWorld), br.setFromRotationMatrix(vr), this.quaternion.premultiply(br.invert()));
  }
  add(t2) {
    if (arguments.length > 1) {
      for (let t3 = 0; t3 < arguments.length; t3++) this.add(arguments[t3]);
      return this;
    }
    return t2 === this ? (console.error("THREE.Object3D.add: object can't be added as a child of itself.", t2), this) : (t2 && t2.isObject3D ? (t2.removeFromParent(), t2.parent = this, this.children.push(t2), t2.dispatchEvent(Cr), Br.child = t2, this.dispatchEvent(Br), Br.child = null) : console.error("THREE.Object3D.add: object not an instance of THREE.Object3D.", t2), this);
  }
  remove(t2) {
    if (arguments.length > 1) {
      for (let t3 = 0; t3 < arguments.length; t3++) this.remove(arguments[t3]);
      return this;
    }
    const e2 = this.children.indexOf(t2);
    return -1 !== e2 && (t2.parent = null, this.children.splice(e2, 1), t2.dispatchEvent(Ir), kr.child = t2, this.dispatchEvent(kr), kr.child = null), this;
  }
  removeFromParent() {
    const t2 = this.parent;
    return null !== t2 && t2.remove(this), this;
  }
  clear() {
    return this.remove(...this.children);
  }
  attach(t2) {
    return this.updateWorldMatrix(true, false), vr.copy(this.matrixWorld).invert(), null !== t2.parent && (t2.parent.updateWorldMatrix(true, false), vr.multiply(t2.parent.matrixWorld)), t2.applyMatrix4(vr), t2.removeFromParent(), t2.parent = this, this.children.push(t2), t2.updateWorldMatrix(false, true), t2.dispatchEvent(Cr), Br.child = t2, this.dispatchEvent(Br), Br.child = null, this;
  }
  getObjectById(t2) {
    return this.getObjectByProperty("id", t2);
  }
  getObjectByName(t2) {
    return this.getObjectByProperty("name", t2);
  }
  getObjectByProperty(t2, e2) {
    if (this[t2] === e2) return this;
    for (let s2 = 0, i2 = this.children.length; s2 < i2; s2++) {
      const i3 = this.children[s2].getObjectByProperty(t2, e2);
      if (void 0 !== i3) return i3;
    }
  }
  getObjectsByProperty(t2, e2, s2 = []) {
    this[t2] === e2 && s2.push(this);
    const i2 = this.children;
    for (let r2 = 0, n2 = i2.length; r2 < n2; r2++) i2[r2].getObjectsByProperty(t2, e2, s2);
    return s2;
  }
  getWorldPosition(t2) {
    return this.updateWorldMatrix(true, false), t2.setFromMatrixPosition(this.matrixWorld);
  }
  getWorldQuaternion(t2) {
    return this.updateWorldMatrix(true, false), this.matrixWorld.decompose(Mr, t2, Sr), t2;
  }
  getWorldScale(t2) {
    return this.updateWorldMatrix(true, false), this.matrixWorld.decompose(Mr, _r, t2), t2;
  }
  getWorldDirection(t2) {
    this.updateWorldMatrix(true, false);
    const e2 = this.matrixWorld.elements;
    return t2.set(e2[8], e2[9], e2[10]).normalize();
  }
  raycast() {
  }
  traverse(t2) {
    t2(this);
    const e2 = this.children;
    for (let s2 = 0, i2 = e2.length; s2 < i2; s2++) e2[s2].traverse(t2);
  }
  traverseVisible(t2) {
    if (false === this.visible) return;
    t2(this);
    const e2 = this.children;
    for (let s2 = 0, i2 = e2.length; s2 < i2; s2++) e2[s2].traverseVisible(t2);
  }
  traverseAncestors(t2) {
    const e2 = this.parent;
    null !== e2 && (t2(e2), e2.traverseAncestors(t2));
  }
  updateMatrix() {
    this.matrix.compose(this.position, this.quaternion, this.scale), this.matrixWorldNeedsUpdate = true;
  }
  updateMatrixWorld(t2) {
    this.matrixAutoUpdate && this.updateMatrix(), (this.matrixWorldNeedsUpdate || t2) && (true === this.matrixWorldAutoUpdate && (null === this.parent ? this.matrixWorld.copy(this.matrix) : this.matrixWorld.multiplyMatrices(this.parent.matrixWorld, this.matrix)), this.matrixWorldNeedsUpdate = false, t2 = true);
    const e2 = this.children;
    for (let s2 = 0, i2 = e2.length; s2 < i2; s2++) {
      e2[s2].updateMatrixWorld(t2);
    }
  }
  updateWorldMatrix(t2, e2) {
    const s2 = this.parent;
    if (true === t2 && null !== s2 && s2.updateWorldMatrix(true, false), this.matrixAutoUpdate && this.updateMatrix(), true === this.matrixWorldAutoUpdate && (null === this.parent ? this.matrixWorld.copy(this.matrix) : this.matrixWorld.multiplyMatrices(this.parent.matrixWorld, this.matrix)), true === e2) {
      const t3 = this.children;
      for (let e3 = 0, s3 = t3.length; e3 < s3; e3++) {
        t3[e3].updateWorldMatrix(false, true);
      }
    }
  }
  toJSON(t2) {
    const e2 = void 0 === t2 || "string" == typeof t2, s2 = {};
    e2 && (t2 = { geometries: {}, materials: {}, textures: {}, images: {}, shapes: {}, skeletons: {}, animations: {}, nodes: {} }, s2.metadata = { version: 4.6, type: "Object", generator: "Object3D.toJSON" });
    const i2 = {};
    function r2(e3, s3) {
      return void 0 === e3[s3.uuid] && (e3[s3.uuid] = s3.toJSON(t2)), s3.uuid;
    }
    if (i2.uuid = this.uuid, i2.type = this.type, "" !== this.name && (i2.name = this.name), true === this.castShadow && (i2.castShadow = true), true === this.receiveShadow && (i2.receiveShadow = true), false === this.visible && (i2.visible = false), false === this.frustumCulled && (i2.frustumCulled = false), 0 !== this.renderOrder && (i2.renderOrder = this.renderOrder), Object.keys(this.userData).length > 0 && (i2.userData = this.userData), i2.layers = this.layers.mask, i2.matrix = this.matrix.toArray(), i2.up = this.up.toArray(), false === this.matrixAutoUpdate && (i2.matrixAutoUpdate = false), this.isInstancedMesh && (i2.type = "InstancedMesh", i2.count = this.count, i2.instanceMatrix = this.instanceMatrix.toJSON(), null !== this.instanceColor && (i2.instanceColor = this.instanceColor.toJSON())), this.isBatchedMesh && (i2.type = "BatchedMesh", i2.perObjectFrustumCulled = this.perObjectFrustumCulled, i2.sortObjects = this.sortObjects, i2.drawRanges = this._drawRanges, i2.reservedRanges = this._reservedRanges, i2.visibility = this._visibility, i2.active = this._active, i2.bounds = this._bounds.map((t3) => ({ boxInitialized: t3.boxInitialized, boxMin: t3.box.min.toArray(), boxMax: t3.box.max.toArray(), sphereInitialized: t3.sphereInitialized, sphereRadius: t3.sphere.radius, sphereCenter: t3.sphere.center.toArray() })), i2.maxInstanceCount = this._maxInstanceCount, i2.maxVertexCount = this._maxVertexCount, i2.maxIndexCount = this._maxIndexCount, i2.geometryInitialized = this._geometryInitialized, i2.geometryCount = this._geometryCount, i2.matricesTexture = this._matricesTexture.toJSON(t2), null !== this._colorsTexture && (i2.colorsTexture = this._colorsTexture.toJSON(t2)), null !== this.boundingSphere && (i2.boundingSphere = { center: i2.boundingSphere.center.toArray(), radius: i2.boundingSphere.radius }), null !== this.boundingBox && (i2.boundingBox = { min: i2.boundingBox.min.toArray(), max: i2.boundingBox.max.toArray() })), this.isScene) this.background && (this.background.isColor ? i2.background = this.background.toJSON() : this.background.isTexture && (i2.background = this.background.toJSON(t2).uuid)), this.environment && this.environment.isTexture && true !== this.environment.isRenderTargetTexture && (i2.environment = this.environment.toJSON(t2).uuid);
    else if (this.isMesh || this.isLine || this.isPoints) {
      i2.geometry = r2(t2.geometries, this.geometry);
      const e3 = this.geometry.parameters;
      if (void 0 !== e3 && void 0 !== e3.shapes) {
        const s3 = e3.shapes;
        if (Array.isArray(s3)) for (let e4 = 0, i3 = s3.length; e4 < i3; e4++) {
          const i4 = s3[e4];
          r2(t2.shapes, i4);
        }
        else r2(t2.shapes, s3);
      }
    }
    if (this.isSkinnedMesh && (i2.bindMode = this.bindMode, i2.bindMatrix = this.bindMatrix.toArray(), void 0 !== this.skeleton && (r2(t2.skeletons, this.skeleton), i2.skeleton = this.skeleton.uuid)), void 0 !== this.material) if (Array.isArray(this.material)) {
      const e3 = [];
      for (let s3 = 0, i3 = this.material.length; s3 < i3; s3++) e3.push(r2(t2.materials, this.material[s3]));
      i2.material = e3;
    } else i2.material = r2(t2.materials, this.material);
    if (this.children.length > 0) {
      i2.children = [];
      for (let e3 = 0; e3 < this.children.length; e3++) i2.children.push(this.children[e3].toJSON(t2).object);
    }
    if (this.animations.length > 0) {
      i2.animations = [];
      for (let e3 = 0; e3 < this.animations.length; e3++) {
        const s3 = this.animations[e3];
        i2.animations.push(r2(t2.animations, s3));
      }
    }
    if (e2) {
      const e3 = n2(t2.geometries), i3 = n2(t2.materials), r3 = n2(t2.textures), o = n2(t2.images), a = n2(t2.shapes), h2 = n2(t2.skeletons), l2 = n2(t2.animations), c2 = n2(t2.nodes);
      e3.length > 0 && (s2.geometries = e3), i3.length > 0 && (s2.materials = i3), r3.length > 0 && (s2.textures = r3), o.length > 0 && (s2.images = o), a.length > 0 && (s2.shapes = a), h2.length > 0 && (s2.skeletons = h2), l2.length > 0 && (s2.animations = l2), c2.length > 0 && (s2.nodes = c2);
    }
    return s2.object = i2, s2;
    function n2(t3) {
      const e3 = [];
      for (const s3 in t3) {
        const i3 = t3[s3];
        delete i3.metadata, e3.push(i3);
      }
      return e3;
    }
  }
  clone(t2) {
    return new this.constructor().copy(this, t2);
  }
  copy(t2, e2 = true) {
    if (this.name = t2.name, this.up.copy(t2.up), this.position.copy(t2.position), this.rotation.order = t2.rotation.order, this.quaternion.copy(t2.quaternion), this.scale.copy(t2.scale), this.matrix.copy(t2.matrix), this.matrixWorld.copy(t2.matrixWorld), this.matrixAutoUpdate = t2.matrixAutoUpdate, this.matrixWorldAutoUpdate = t2.matrixWorldAutoUpdate, this.matrixWorldNeedsUpdate = t2.matrixWorldNeedsUpdate, this.layers.mask = t2.layers.mask, this.visible = t2.visible, this.castShadow = t2.castShadow, this.receiveShadow = t2.receiveShadow, this.frustumCulled = t2.frustumCulled, this.renderOrder = t2.renderOrder, this.animations = t2.animations.slice(), this.userData = JSON.parse(JSON.stringify(t2.userData)), true === e2) for (let e3 = 0; e3 < t2.children.length; e3++) {
      const s2 = t2.children[e3];
      this.add(s2.clone());
    }
    return this;
  }
}
Rr.DEFAULT_UP = new Ii(0, 1, 0), Rr.DEFAULT_MATRIX_AUTO_UPDATE = true, Rr.DEFAULT_MATRIX_WORLD_AUTO_UPDATE = true;
const Er = new Ii(), Pr = new Ii(), Or = new Ii(), Fr = new Ii(), Nr = new Ii(), Lr = new Ii(), Vr = new Ii(), Wr = new Ii(), jr = new Ii(), Ur = new Ii(), Dr = new wi(), Hr = new wi(), qr = new wi();
class Jr {
  constructor(t2 = new Ii(), e2 = new Ii(), s2 = new Ii()) {
    this.a = t2, this.b = e2, this.c = s2;
  }
  static getNormal(t2, e2, s2, i2) {
    i2.subVectors(s2, e2), Er.subVectors(t2, e2), i2.cross(Er);
    const r2 = i2.lengthSq();
    return r2 > 0 ? i2.multiplyScalar(1 / Math.sqrt(r2)) : i2.set(0, 0, 0);
  }
  static getBarycoord(t2, e2, s2, i2, r2) {
    Er.subVectors(i2, e2), Pr.subVectors(s2, e2), Or.subVectors(t2, e2);
    const n2 = Er.dot(Er), o = Er.dot(Pr), a = Er.dot(Or), h2 = Pr.dot(Pr), l2 = Pr.dot(Or), c2 = n2 * h2 - o * o;
    if (0 === c2) return r2.set(0, 0, 0), null;
    const u2 = 1 / c2, d2 = (h2 * a - o * l2) * u2, p2 = (n2 * l2 - o * a) * u2;
    return r2.set(1 - d2 - p2, p2, d2);
  }
  static containsPoint(t2, e2, s2, i2) {
    return null !== this.getBarycoord(t2, e2, s2, i2, Fr) && (Fr.x >= 0 && Fr.y >= 0 && Fr.x + Fr.y <= 1);
  }
  static getInterpolation(t2, e2, s2, i2, r2, n2, o, a) {
    return null === this.getBarycoord(t2, e2, s2, i2, Fr) ? (a.x = 0, a.y = 0, "z" in a && (a.z = 0), "w" in a && (a.w = 0), null) : (a.setScalar(0), a.addScaledVector(r2, Fr.x), a.addScaledVector(n2, Fr.y), a.addScaledVector(o, Fr.z), a);
  }
  static getInterpolatedAttribute(t2, e2, s2, i2, r2, n2) {
    return Dr.setScalar(0), Hr.setScalar(0), qr.setScalar(0), Dr.fromBufferAttribute(t2, e2), Hr.fromBufferAttribute(t2, s2), qr.fromBufferAttribute(t2, i2), n2.setScalar(0), n2.addScaledVector(Dr, r2.x), n2.addScaledVector(Hr, r2.y), n2.addScaledVector(qr, r2.z), n2;
  }
  static isFrontFacing(t2, e2, s2, i2) {
    return Er.subVectors(s2, e2), Pr.subVectors(t2, e2), Er.cross(Pr).dot(i2) < 0;
  }
  set(t2, e2, s2) {
    return this.a.copy(t2), this.b.copy(e2), this.c.copy(s2), this;
  }
  setFromPointsAndIndices(t2, e2, s2, i2) {
    return this.a.copy(t2[e2]), this.b.copy(t2[s2]), this.c.copy(t2[i2]), this;
  }
  setFromAttributeAndIndices(t2, e2, s2, i2) {
    return this.a.fromBufferAttribute(t2, e2), this.b.fromBufferAttribute(t2, s2), this.c.fromBufferAttribute(t2, i2), this;
  }
  clone() {
    return new this.constructor().copy(this);
  }
  copy(t2) {
    return this.a.copy(t2.a), this.b.copy(t2.b), this.c.copy(t2.c), this;
  }
  getArea() {
    return Er.subVectors(this.c, this.b), Pr.subVectors(this.a, this.b), 0.5 * Er.cross(Pr).length();
  }
  getMidpoint(t2) {
    return t2.addVectors(this.a, this.b).add(this.c).multiplyScalar(1 / 3);
  }
  getNormal(t2) {
    return Jr.getNormal(this.a, this.b, this.c, t2);
  }
  getPlane(t2) {
    return t2.setFromCoplanarPoints(this.a, this.b, this.c);
  }
  getBarycoord(t2, e2) {
    return Jr.getBarycoord(t2, this.a, this.b, this.c, e2);
  }
  getInterpolation(t2, e2, s2, i2, r2) {
    return Jr.getInterpolation(t2, this.a, this.b, this.c, e2, s2, i2, r2);
  }
  containsPoint(t2) {
    return Jr.containsPoint(t2, this.a, this.b, this.c);
  }
  isFrontFacing(t2) {
    return Jr.isFrontFacing(this.a, this.b, this.c, t2);
  }
  intersectsBox(t2) {
    return t2.intersectsTriangle(this);
  }
  closestPointToPoint(t2, e2) {
    const s2 = this.a, i2 = this.b, r2 = this.c;
    let n2, o;
    Nr.subVectors(i2, s2), Lr.subVectors(r2, s2), Wr.subVectors(t2, s2);
    const a = Nr.dot(Wr), h2 = Lr.dot(Wr);
    if (a <= 0 && h2 <= 0) return e2.copy(s2);
    jr.subVectors(t2, i2);
    const l2 = Nr.dot(jr), c2 = Lr.dot(jr);
    if (l2 >= 0 && c2 <= l2) return e2.copy(i2);
    const u2 = a * c2 - l2 * h2;
    if (u2 <= 0 && a >= 0 && l2 <= 0) return n2 = a / (a - l2), e2.copy(s2).addScaledVector(Nr, n2);
    Ur.subVectors(t2, r2);
    const d2 = Nr.dot(Ur), p2 = Lr.dot(Ur);
    if (p2 >= 0 && d2 <= p2) return e2.copy(r2);
    const m2 = d2 * h2 - a * p2;
    if (m2 <= 0 && h2 >= 0 && p2 <= 0) return o = h2 / (h2 - p2), e2.copy(s2).addScaledVector(Lr, o);
    const y2 = l2 * p2 - d2 * c2;
    if (y2 <= 0 && c2 - l2 >= 0 && d2 - p2 >= 0) return Vr.subVectors(r2, i2), o = (c2 - l2) / (c2 - l2 + (d2 - p2)), e2.copy(i2).addScaledVector(Vr, o);
    const f2 = 1 / (y2 + m2 + u2);
    return n2 = m2 * f2, o = u2 * f2, e2.copy(s2).addScaledVector(Nr, n2).addScaledVector(Lr, o);
  }
  equals(t2) {
    return t2.a.equals(this.a) && t2.b.equals(this.b) && t2.c.equals(this.c);
  }
}
const Xr = { aliceblue: 15792383, antiquewhite: 16444375, aqua: 65535, aquamarine: 8388564, azure: 15794175, beige: 16119260, bisque: 16770244, black: 0, blanchedalmond: 16772045, blue: 255, blueviolet: 9055202, brown: 10824234, burlywood: 14596231, cadetblue: 6266528, chartreuse: 8388352, chocolate: 13789470, coral: 16744272, cornflowerblue: 6591981, cornsilk: 16775388, crimson: 14423100, cyan: 65535, darkblue: 139, darkcyan: 35723, darkgoldenrod: 12092939, darkgray: 11119017, darkgreen: 25600, darkgrey: 11119017, darkkhaki: 12433259, darkmagenta: 9109643, darkolivegreen: 5597999, darkorange: 16747520, darkorchid: 10040012, darkred: 9109504, darksalmon: 15308410, darkseagreen: 9419919, darkslateblue: 4734347, darkslategray: 3100495, darkslategrey: 3100495, darkturquoise: 52945, darkviolet: 9699539, deeppink: 16716947, deepskyblue: 49151, dimgray: 6908265, dimgrey: 6908265, dodgerblue: 2003199, firebrick: 11674146, floralwhite: 16775920, forestgreen: 2263842, fuchsia: 16711935, gainsboro: 14474460, ghostwhite: 16316671, gold: 16766720, goldenrod: 14329120, gray: 8421504, green: 32768, greenyellow: 11403055, grey: 8421504, honeydew: 15794160, hotpink: 16738740, indianred: 13458524, indigo: 4915330, ivory: 16777200, khaki: 15787660, lavender: 15132410, lavenderblush: 16773365, lawngreen: 8190976, lemonchiffon: 16775885, lightblue: 11393254, lightcoral: 15761536, lightcyan: 14745599, lightgoldenrodyellow: 16448210, lightgray: 13882323, lightgreen: 9498256, lightgrey: 13882323, lightpink: 16758465, lightsalmon: 16752762, lightseagreen: 2142890, lightskyblue: 8900346, lightslategray: 7833753, lightslategrey: 7833753, lightsteelblue: 11584734, lightyellow: 16777184, lime: 65280, limegreen: 3329330, linen: 16445670, magenta: 16711935, maroon: 8388608, mediumaquamarine: 6737322, mediumblue: 205, mediumorchid: 12211667, mediumpurple: 9662683, mediumseagreen: 3978097, mediumslateblue: 8087790, mediumspringgreen: 64154, mediumturquoise: 4772300, mediumvioletred: 13047173, midnightblue: 1644912, mintcream: 16121850, mistyrose: 16770273, moccasin: 16770229, navajowhite: 16768685, navy: 128, oldlace: 16643558, olive: 8421376, olivedrab: 7048739, orange: 16753920, orangered: 16729344, orchid: 14315734, palegoldenrod: 15657130, palegreen: 10025880, paleturquoise: 11529966, palevioletred: 14381203, papayawhip: 16773077, peachpuff: 16767673, peru: 13468991, pink: 16761035, plum: 14524637, powderblue: 11591910, purple: 8388736, rebeccapurple: 6697881, red: 16711680, rosybrown: 12357519, royalblue: 4286945, saddlebrown: 9127187, salmon: 16416882, sandybrown: 16032864, seagreen: 3050327, seashell: 16774638, sienna: 10506797, silver: 12632256, skyblue: 8900331, slateblue: 6970061, slategray: 7372944, slategrey: 7372944, snow: 16775930, springgreen: 65407, steelblue: 4620980, tan: 13808780, teal: 32896, thistle: 14204888, tomato: 16737095, turquoise: 4251856, violet: 15631086, wheat: 16113331, white: 16777215, whitesmoke: 16119285, yellow: 16776960, yellowgreen: 10145074 }, Yr = { h: 0, s: 0, l: 0 }, Zr = { h: 0, s: 0, l: 0 };
function Gr(t2, e2, s2) {
  return s2 < 0 && (s2 += 1), s2 > 1 && (s2 -= 1), s2 < 1 / 6 ? t2 + 6 * (e2 - t2) * s2 : s2 < 0.5 ? e2 : s2 < 2 / 3 ? t2 + 6 * (e2 - t2) * (2 / 3 - s2) : t2;
}
class $r {
  constructor(t2, e2, s2) {
    return this.isColor = true, this.r = 1, this.g = 1, this.b = 1, this.set(t2, e2, s2);
  }
  set(t2, e2, s2) {
    if (void 0 === e2 && void 0 === s2) {
      const e3 = t2;
      e3 && e3.isColor ? this.copy(e3) : "number" == typeof e3 ? this.setHex(e3) : "string" == typeof e3 && this.setStyle(e3);
    } else this.setRGB(t2, e2, s2);
    return this;
  }
  setScalar(t2) {
    return this.r = t2, this.g = t2, this.b = t2, this;
  }
  setHex(t2, e2 = Ge) {
    return t2 = Math.floor(t2), this.r = (t2 >> 16 & 255) / 255, this.g = (t2 >> 8 & 255) / 255, this.b = (255 & t2) / 255, ui.toWorkingColorSpace(this, e2), this;
  }
  setRGB(t2, e2, s2, i2 = ui.workingColorSpace) {
    return this.r = t2, this.g = e2, this.b = s2, ui.toWorkingColorSpace(this, i2), this;
  }
  setHSL(t2, e2, s2, i2 = ui.workingColorSpace) {
    if (t2 = Hs(t2, 1), e2 = Ds(e2, 0, 1), s2 = Ds(s2, 0, 1), 0 === e2) this.r = this.g = this.b = s2;
    else {
      const i3 = s2 <= 0.5 ? s2 * (1 + e2) : s2 + e2 - s2 * e2, r2 = 2 * s2 - i3;
      this.r = Gr(r2, i3, t2 + 1 / 3), this.g = Gr(r2, i3, t2), this.b = Gr(r2, i3, t2 - 1 / 3);
    }
    return ui.toWorkingColorSpace(this, i2), this;
  }
  setStyle(t2, e2 = Ge) {
    function s2(e3) {
      void 0 !== e3 && parseFloat(e3) < 1 && console.warn("THREE.Color: Alpha component of " + t2 + " will be ignored.");
    }
    let i2;
    if (i2 = /^(\w+)\(([^\)]*)\)/.exec(t2)) {
      let r2;
      const n2 = i2[1], o = i2[2];
      switch (n2) {
        case "rgb":
        case "rgba":
          if (r2 = /^\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(o)) return s2(r2[4]), this.setRGB(Math.min(255, parseInt(r2[1], 10)) / 255, Math.min(255, parseInt(r2[2], 10)) / 255, Math.min(255, parseInt(r2[3], 10)) / 255, e2);
          if (r2 = /^\s*(\d+)\%\s*,\s*(\d+)\%\s*,\s*(\d+)\%\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(o)) return s2(r2[4]), this.setRGB(Math.min(100, parseInt(r2[1], 10)) / 100, Math.min(100, parseInt(r2[2], 10)) / 100, Math.min(100, parseInt(r2[3], 10)) / 100, e2);
          break;
        case "hsl":
        case "hsla":
          if (r2 = /^\s*(\d*\.?\d+)\s*,\s*(\d*\.?\d+)\%\s*,\s*(\d*\.?\d+)\%\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(o)) return s2(r2[4]), this.setHSL(parseFloat(r2[1]) / 360, parseFloat(r2[2]) / 100, parseFloat(r2[3]) / 100, e2);
          break;
        default:
          console.warn("THREE.Color: Unknown color model " + t2);
      }
    } else if (i2 = /^\#([A-Fa-f\d]+)$/.exec(t2)) {
      const s3 = i2[1], r2 = s3.length;
      if (3 === r2) return this.setRGB(parseInt(s3.charAt(0), 16) / 15, parseInt(s3.charAt(1), 16) / 15, parseInt(s3.charAt(2), 16) / 15, e2);
      if (6 === r2) return this.setHex(parseInt(s3, 16), e2);
      console.warn("THREE.Color: Invalid hex color " + t2);
    } else if (t2 && t2.length > 0) return this.setColorName(t2, e2);
    return this;
  }
  setColorName(t2, e2 = Ge) {
    const s2 = Xr[t2.toLowerCase()];
    return void 0 !== s2 ? this.setHex(s2, e2) : console.warn("THREE.Color: Unknown color " + t2), this;
  }
  clone() {
    return new this.constructor(this.r, this.g, this.b);
  }
  copy(t2) {
    return this.r = t2.r, this.g = t2.g, this.b = t2.b, this;
  }
  copySRGBToLinear(t2) {
    return this.r = di(t2.r), this.g = di(t2.g), this.b = di(t2.b), this;
  }
  copyLinearToSRGB(t2) {
    return this.r = pi(t2.r), this.g = pi(t2.g), this.b = pi(t2.b), this;
  }
  convertSRGBToLinear() {
    return this.copySRGBToLinear(this), this;
  }
  convertLinearToSRGB() {
    return this.copyLinearToSRGB(this), this;
  }
  getHex(t2 = Ge) {
    return ui.fromWorkingColorSpace(Qr.copy(this), t2), 65536 * Math.round(Ds(255 * Qr.r, 0, 255)) + 256 * Math.round(Ds(255 * Qr.g, 0, 255)) + Math.round(Ds(255 * Qr.b, 0, 255));
  }
  getHexString(t2 = Ge) {
    return ("000000" + this.getHex(t2).toString(16)).slice(-6);
  }
  getHSL(t2, e2 = ui.workingColorSpace) {
    ui.fromWorkingColorSpace(Qr.copy(this), e2);
    const s2 = Qr.r, i2 = Qr.g, r2 = Qr.b, n2 = Math.max(s2, i2, r2), o = Math.min(s2, i2, r2);
    let a, h2;
    const l2 = (o + n2) / 2;
    if (o === n2) a = 0, h2 = 0;
    else {
      const t3 = n2 - o;
      switch (h2 = l2 <= 0.5 ? t3 / (n2 + o) : t3 / (2 - n2 - o), n2) {
        case s2:
          a = (i2 - r2) / t3 + (i2 < r2 ? 6 : 0);
          break;
        case i2:
          a = (r2 - s2) / t3 + 2;
          break;
        case r2:
          a = (s2 - i2) / t3 + 4;
      }
      a /= 6;
    }
    return t2.h = a, t2.s = h2, t2.l = l2, t2;
  }
  getRGB(t2, e2 = ui.workingColorSpace) {
    return ui.fromWorkingColorSpace(Qr.copy(this), e2), t2.r = Qr.r, t2.g = Qr.g, t2.b = Qr.b, t2;
  }
  getStyle(t2 = Ge) {
    ui.fromWorkingColorSpace(Qr.copy(this), t2);
    const e2 = Qr.r, s2 = Qr.g, i2 = Qr.b;
    return t2 !== Ge ? `color(${t2} ${e2.toFixed(3)} ${s2.toFixed(3)} ${i2.toFixed(3)})` : `rgb(${Math.round(255 * e2)},${Math.round(255 * s2)},${Math.round(255 * i2)})`;
  }
  offsetHSL(t2, e2, s2) {
    return this.getHSL(Yr), this.setHSL(Yr.h + t2, Yr.s + e2, Yr.l + s2);
  }
  add(t2) {
    return this.r += t2.r, this.g += t2.g, this.b += t2.b, this;
  }
  addColors(t2, e2) {
    return this.r = t2.r + e2.r, this.g = t2.g + e2.g, this.b = t2.b + e2.b, this;
  }
  addScalar(t2) {
    return this.r += t2, this.g += t2, this.b += t2, this;
  }
  sub(t2) {
    return this.r = Math.max(0, this.r - t2.r), this.g = Math.max(0, this.g - t2.g), this.b = Math.max(0, this.b - t2.b), this;
  }
  multiply(t2) {
    return this.r *= t2.r, this.g *= t2.g, this.b *= t2.b, this;
  }
  multiplyScalar(t2) {
    return this.r *= t2, this.g *= t2, this.b *= t2, this;
  }
  lerp(t2, e2) {
    return this.r += (t2.r - this.r) * e2, this.g += (t2.g - this.g) * e2, this.b += (t2.b - this.b) * e2, this;
  }
  lerpColors(t2, e2, s2) {
    return this.r = t2.r + (e2.r - t2.r) * s2, this.g = t2.g + (e2.g - t2.g) * s2, this.b = t2.b + (e2.b - t2.b) * s2, this;
  }
  lerpHSL(t2, e2) {
    this.getHSL(Yr), t2.getHSL(Zr);
    const s2 = qs(Yr.h, Zr.h, e2), i2 = qs(Yr.s, Zr.s, e2), r2 = qs(Yr.l, Zr.l, e2);
    return this.setHSL(s2, i2, r2), this;
  }
  setFromVector3(t2) {
    return this.r = t2.x, this.g = t2.y, this.b = t2.z, this;
  }
  applyMatrix3(t2) {
    const e2 = this.r, s2 = this.g, i2 = this.b, r2 = t2.elements;
    return this.r = r2[0] * e2 + r2[3] * s2 + r2[6] * i2, this.g = r2[1] * e2 + r2[4] * s2 + r2[7] * i2, this.b = r2[2] * e2 + r2[5] * s2 + r2[8] * i2, this;
  }
  equals(t2) {
    return t2.r === this.r && t2.g === this.g && t2.b === this.b;
  }
  fromArray(t2, e2 = 0) {
    return this.r = t2[e2], this.g = t2[e2 + 1], this.b = t2[e2 + 2], this;
  }
  toArray(t2 = [], e2 = 0) {
    return t2[e2] = this.r, t2[e2 + 1] = this.g, t2[e2 + 2] = this.b, t2;
  }
  fromBufferAttribute(t2, e2) {
    return this.r = t2.getX(e2), this.g = t2.getY(e2), this.b = t2.getZ(e2), this;
  }
  toJSON() {
    return this.getHex();
  }
  *[Symbol.iterator]() {
    yield this.r, yield this.g, yield this.b;
  }
}
const Qr = new $r();
$r.NAMES = Xr;
let Kr = 0;
class tn extends Ns {
  constructor() {
    super(), this.isMaterial = true, Object.defineProperty(this, "id", { value: Kr++ }), this.uuid = Us(), this.name = "", this.type = "Material", this.blending = 1, this.side = 0, this.vertexColors = false, this.opacity = 1, this.transparent = false, this.alphaHash = false, this.blendSrc = 204, this.blendDst = 205, this.blendEquation = 100, this.blendSrcAlpha = null, this.blendDstAlpha = null, this.blendEquationAlpha = null, this.blendColor = new $r(0, 0, 0), this.blendAlpha = 0, this.depthFunc = 3, this.depthTest = true, this.depthWrite = true, this.stencilWriteMask = 255, this.stencilFunc = 519, this.stencilRef = 0, this.stencilFuncMask = 255, this.stencilFail = es, this.stencilZFail = es, this.stencilZPass = es, this.stencilWrite = false, this.clippingPlanes = null, this.clipIntersection = false, this.clipShadows = false, this.shadowSide = null, this.colorWrite = true, this.precision = null, this.polygonOffset = false, this.polygonOffsetFactor = 0, this.polygonOffsetUnits = 0, this.dithering = false, this.alphaToCoverage = false, this.premultipliedAlpha = false, this.forceSinglePass = false, this.visible = true, this.toneMapped = true, this.userData = {}, this.version = 0, this._alphaTest = 0;
  }
  get alphaTest() {
    return this._alphaTest;
  }
  set alphaTest(t2) {
    this._alphaTest > 0 != t2 > 0 && this.version++, this._alphaTest = t2;
  }
  onBeforeRender() {
  }
  onBeforeCompile() {
  }
  customProgramCacheKey() {
    return this.onBeforeCompile.toString();
  }
  setValues(t2) {
    if (void 0 !== t2) for (const e2 in t2) {
      const s2 = t2[e2];
      if (void 0 === s2) {
        console.warn(`THREE.Material: parameter '${e2}' has value of undefined.`);
        continue;
      }
      const i2 = this[e2];
      void 0 !== i2 ? i2 && i2.isColor ? i2.set(s2) : i2 && i2.isVector3 && s2 && s2.isVector3 ? i2.copy(s2) : this[e2] = s2 : console.warn(`THREE.Material: '${e2}' is not a property of THREE.${this.type}.`);
    }
  }
  toJSON(t2) {
    const e2 = void 0 === t2 || "string" == typeof t2;
    e2 && (t2 = { textures: {}, images: {} });
    const s2 = { metadata: { version: 4.6, type: "Material", generator: "Material.toJSON" } };
    function i2(t3) {
      const e3 = [];
      for (const s3 in t3) {
        const i3 = t3[s3];
        delete i3.metadata, e3.push(i3);
      }
      return e3;
    }
    if (s2.uuid = this.uuid, s2.type = this.type, "" !== this.name && (s2.name = this.name), this.color && this.color.isColor && (s2.color = this.color.getHex()), void 0 !== this.roughness && (s2.roughness = this.roughness), void 0 !== this.metalness && (s2.metalness = this.metalness), void 0 !== this.sheen && (s2.sheen = this.sheen), this.sheenColor && this.sheenColor.isColor && (s2.sheenColor = this.sheenColor.getHex()), void 0 !== this.sheenRoughness && (s2.sheenRoughness = this.sheenRoughness), this.emissive && this.emissive.isColor && (s2.emissive = this.emissive.getHex()), void 0 !== this.emissiveIntensity && 1 !== this.emissiveIntensity && (s2.emissiveIntensity = this.emissiveIntensity), this.specular && this.specular.isColor && (s2.specular = this.specular.getHex()), void 0 !== this.specularIntensity && (s2.specularIntensity = this.specularIntensity), this.specularColor && this.specularColor.isColor && (s2.specularColor = this.specularColor.getHex()), void 0 !== this.shininess && (s2.shininess = this.shininess), void 0 !== this.clearcoat && (s2.clearcoat = this.clearcoat), void 0 !== this.clearcoatRoughness && (s2.clearcoatRoughness = this.clearcoatRoughness), this.clearcoatMap && this.clearcoatMap.isTexture && (s2.clearcoatMap = this.clearcoatMap.toJSON(t2).uuid), this.clearcoatRoughnessMap && this.clearcoatRoughnessMap.isTexture && (s2.clearcoatRoughnessMap = this.clearcoatRoughnessMap.toJSON(t2).uuid), this.clearcoatNormalMap && this.clearcoatNormalMap.isTexture && (s2.clearcoatNormalMap = this.clearcoatNormalMap.toJSON(t2).uuid, s2.clearcoatNormalScale = this.clearcoatNormalScale.toArray()), void 0 !== this.dispersion && (s2.dispersion = this.dispersion), void 0 !== this.iridescence && (s2.iridescence = this.iridescence), void 0 !== this.iridescenceIOR && (s2.iridescenceIOR = this.iridescenceIOR), void 0 !== this.iridescenceThicknessRange && (s2.iridescenceThicknessRange = this.iridescenceThicknessRange), this.iridescenceMap && this.iridescenceMap.isTexture && (s2.iridescenceMap = this.iridescenceMap.toJSON(t2).uuid), this.iridescenceThicknessMap && this.iridescenceThicknessMap.isTexture && (s2.iridescenceThicknessMap = this.iridescenceThicknessMap.toJSON(t2).uuid), void 0 !== this.anisotropy && (s2.anisotropy = this.anisotropy), void 0 !== this.anisotropyRotation && (s2.anisotropyRotation = this.anisotropyRotation), this.anisotropyMap && this.anisotropyMap.isTexture && (s2.anisotropyMap = this.anisotropyMap.toJSON(t2).uuid), this.map && this.map.isTexture && (s2.map = this.map.toJSON(t2).uuid), this.matcap && this.matcap.isTexture && (s2.matcap = this.matcap.toJSON(t2).uuid), this.alphaMap && this.alphaMap.isTexture && (s2.alphaMap = this.alphaMap.toJSON(t2).uuid), this.lightMap && this.lightMap.isTexture && (s2.lightMap = this.lightMap.toJSON(t2).uuid, s2.lightMapIntensity = this.lightMapIntensity), this.aoMap && this.aoMap.isTexture && (s2.aoMap = this.aoMap.toJSON(t2).uuid, s2.aoMapIntensity = this.aoMapIntensity), this.bumpMap && this.bumpMap.isTexture && (s2.bumpMap = this.bumpMap.toJSON(t2).uuid, s2.bumpScale = this.bumpScale), this.normalMap && this.normalMap.isTexture && (s2.normalMap = this.normalMap.toJSON(t2).uuid, s2.normalMapType = this.normalMapType, s2.normalScale = this.normalScale.toArray()), this.displacementMap && this.displacementMap.isTexture && (s2.displacementMap = this.displacementMap.toJSON(t2).uuid, s2.displacementScale = this.displacementScale, s2.displacementBias = this.displacementBias), this.roughnessMap && this.roughnessMap.isTexture && (s2.roughnessMap = this.roughnessMap.toJSON(t2).uuid), this.metalnessMap && this.metalnessMap.isTexture && (s2.metalnessMap = this.metalnessMap.toJSON(t2).uuid), this.emissiveMap && this.emissiveMap.isTexture && (s2.emissiveMap = this.emissiveMap.toJSON(t2).uuid), this.specularMap && this.specularMap.isTexture && (s2.specularMap = this.specularMap.toJSON(t2).uuid), this.specularIntensityMap && this.specularIntensityMap.isTexture && (s2.specularIntensityMap = this.specularIntensityMap.toJSON(t2).uuid), this.specularColorMap && this.specularColorMap.isTexture && (s2.specularColorMap = this.specularColorMap.toJSON(t2).uuid), this.envMap && this.envMap.isTexture && (s2.envMap = this.envMap.toJSON(t2).uuid, void 0 !== this.combine && (s2.combine = this.combine)), void 0 !== this.envMapRotation && (s2.envMapRotation = this.envMapRotation.toArray()), void 0 !== this.envMapIntensity && (s2.envMapIntensity = this.envMapIntensity), void 0 !== this.reflectivity && (s2.reflectivity = this.reflectivity), void 0 !== this.refractionRatio && (s2.refractionRatio = this.refractionRatio), this.gradientMap && this.gradientMap.isTexture && (s2.gradientMap = this.gradientMap.toJSON(t2).uuid), void 0 !== this.transmission && (s2.transmission = this.transmission), this.transmissionMap && this.transmissionMap.isTexture && (s2.transmissionMap = this.transmissionMap.toJSON(t2).uuid), void 0 !== this.thickness && (s2.thickness = this.thickness), this.thicknessMap && this.thicknessMap.isTexture && (s2.thicknessMap = this.thicknessMap.toJSON(t2).uuid), void 0 !== this.attenuationDistance && this.attenuationDistance !== 1 / 0 && (s2.attenuationDistance = this.attenuationDistance), void 0 !== this.attenuationColor && (s2.attenuationColor = this.attenuationColor.getHex()), void 0 !== this.size && (s2.size = this.size), null !== this.shadowSide && (s2.shadowSide = this.shadowSide), void 0 !== this.sizeAttenuation && (s2.sizeAttenuation = this.sizeAttenuation), 1 !== this.blending && (s2.blending = this.blending), 0 !== this.side && (s2.side = this.side), true === this.vertexColors && (s2.vertexColors = true), this.opacity < 1 && (s2.opacity = this.opacity), true === this.transparent && (s2.transparent = true), 204 !== this.blendSrc && (s2.blendSrc = this.blendSrc), 205 !== this.blendDst && (s2.blendDst = this.blendDst), 100 !== this.blendEquation && (s2.blendEquation = this.blendEquation), null !== this.blendSrcAlpha && (s2.blendSrcAlpha = this.blendSrcAlpha), null !== this.blendDstAlpha && (s2.blendDstAlpha = this.blendDstAlpha), null !== this.blendEquationAlpha && (s2.blendEquationAlpha = this.blendEquationAlpha), this.blendColor && this.blendColor.isColor && (s2.blendColor = this.blendColor.getHex()), 0 !== this.blendAlpha && (s2.blendAlpha = this.blendAlpha), 3 !== this.depthFunc && (s2.depthFunc = this.depthFunc), false === this.depthTest && (s2.depthTest = this.depthTest), false === this.depthWrite && (s2.depthWrite = this.depthWrite), false === this.colorWrite && (s2.colorWrite = this.colorWrite), 255 !== this.stencilWriteMask && (s2.stencilWriteMask = this.stencilWriteMask), 519 !== this.stencilFunc && (s2.stencilFunc = this.stencilFunc), 0 !== this.stencilRef && (s2.stencilRef = this.stencilRef), 255 !== this.stencilFuncMask && (s2.stencilFuncMask = this.stencilFuncMask), this.stencilFail !== es && (s2.stencilFail = this.stencilFail), this.stencilZFail !== es && (s2.stencilZFail = this.stencilZFail), this.stencilZPass !== es && (s2.stencilZPass = this.stencilZPass), true === this.stencilWrite && (s2.stencilWrite = this.stencilWrite), void 0 !== this.rotation && 0 !== this.rotation && (s2.rotation = this.rotation), true === this.polygonOffset && (s2.polygonOffset = true), 0 !== this.polygonOffsetFactor && (s2.polygonOffsetFactor = this.polygonOffsetFactor), 0 !== this.polygonOffsetUnits && (s2.polygonOffsetUnits = this.polygonOffsetUnits), void 0 !== this.linewidth && 1 !== this.linewidth && (s2.linewidth = this.linewidth), void 0 !== this.dashSize && (s2.dashSize = this.dashSize), void 0 !== this.gapSize && (s2.gapSize = this.gapSize), void 0 !== this.scale && (s2.scale = this.scale), true === this.dithering && (s2.dithering = true), this.alphaTest > 0 && (s2.alphaTest = this.alphaTest), true === this.alphaHash && (s2.alphaHash = true), true === this.alphaToCoverage && (s2.alphaToCoverage = true), true === this.premultipliedAlpha && (s2.premultipliedAlpha = true), true === this.forceSinglePass && (s2.forceSinglePass = true), true === this.wireframe && (s2.wireframe = true), this.wireframeLinewidth > 1 && (s2.wireframeLinewidth = this.wireframeLinewidth), "round" !== this.wireframeLinecap && (s2.wireframeLinecap = this.wireframeLinecap), "round" !== this.wireframeLinejoin && (s2.wireframeLinejoin = this.wireframeLinejoin), true === this.flatShading && (s2.flatShading = true), false === this.visible && (s2.visible = false), false === this.toneMapped && (s2.toneMapped = false), false === this.fog && (s2.fog = false), Object.keys(this.userData).length > 0 && (s2.userData = this.userData), e2) {
      const e3 = i2(t2.textures), r2 = i2(t2.images);
      e3.length > 0 && (s2.textures = e3), r2.length > 0 && (s2.images = r2);
    }
    return s2;
  }
  clone() {
    return new this.constructor().copy(this);
  }
  copy(t2) {
    this.name = t2.name, this.blending = t2.blending, this.side = t2.side, this.vertexColors = t2.vertexColors, this.opacity = t2.opacity, this.transparent = t2.transparent, this.blendSrc = t2.blendSrc, this.blendDst = t2.blendDst, this.blendEquation = t2.blendEquation, this.blendSrcAlpha = t2.blendSrcAlpha, this.blendDstAlpha = t2.blendDstAlpha, this.blendEquationAlpha = t2.blendEquationAlpha, this.blendColor.copy(t2.blendColor), this.blendAlpha = t2.blendAlpha, this.depthFunc = t2.depthFunc, this.depthTest = t2.depthTest, this.depthWrite = t2.depthWrite, this.stencilWriteMask = t2.stencilWriteMask, this.stencilFunc = t2.stencilFunc, this.stencilRef = t2.stencilRef, this.stencilFuncMask = t2.stencilFuncMask, this.stencilFail = t2.stencilFail, this.stencilZFail = t2.stencilZFail, this.stencilZPass = t2.stencilZPass, this.stencilWrite = t2.stencilWrite;
    const e2 = t2.clippingPlanes;
    let s2 = null;
    if (null !== e2) {
      const t3 = e2.length;
      s2 = new Array(t3);
      for (let i2 = 0; i2 !== t3; ++i2) s2[i2] = e2[i2].clone();
    }
    return this.clippingPlanes = s2, this.clipIntersection = t2.clipIntersection, this.clipShadows = t2.clipShadows, this.shadowSide = t2.shadowSide, this.colorWrite = t2.colorWrite, this.precision = t2.precision, this.polygonOffset = t2.polygonOffset, this.polygonOffsetFactor = t2.polygonOffsetFactor, this.polygonOffsetUnits = t2.polygonOffsetUnits, this.dithering = t2.dithering, this.alphaTest = t2.alphaTest, this.alphaHash = t2.alphaHash, this.alphaToCoverage = t2.alphaToCoverage, this.premultipliedAlpha = t2.premultipliedAlpha, this.forceSinglePass = t2.forceSinglePass, this.visible = t2.visible, this.toneMapped = t2.toneMapped, this.userData = JSON.parse(JSON.stringify(t2.userData)), this;
  }
  dispose() {
    this.dispatchEvent({ type: "dispose" });
  }
  set needsUpdate(t2) {
    true === t2 && this.version++;
  }
  onBuild() {
    console.warn("Material: onBuild() has been removed.");
  }
}
class en extends tn {
  constructor(t2) {
    super(), this.isMeshBasicMaterial = true, this.type = "MeshBasicMaterial", this.color = new $r(16777215), this.map = null, this.lightMap = null, this.lightMapIntensity = 1, this.aoMap = null, this.aoMapIntensity = 1, this.specularMap = null, this.alphaMap = null, this.envMap = null, this.envMapRotation = new yr(), this.combine = 0, this.reflectivity = 1, this.refractionRatio = 0.98, this.wireframe = false, this.wireframeLinewidth = 1, this.wireframeLinecap = "round", this.wireframeLinejoin = "round", this.fog = true, this.setValues(t2);
  }
  copy(t2) {
    return super.copy(t2), this.color.copy(t2.color), this.map = t2.map, this.lightMap = t2.lightMap, this.lightMapIntensity = t2.lightMapIntensity, this.aoMap = t2.aoMap, this.aoMapIntensity = t2.aoMapIntensity, this.specularMap = t2.specularMap, this.alphaMap = t2.alphaMap, this.envMap = t2.envMap, this.envMapRotation.copy(t2.envMapRotation), this.combine = t2.combine, this.reflectivity = t2.reflectivity, this.refractionRatio = t2.refractionRatio, this.wireframe = t2.wireframe, this.wireframeLinewidth = t2.wireframeLinewidth, this.wireframeLinecap = t2.wireframeLinecap, this.wireframeLinejoin = t2.wireframeLinejoin, this.fog = t2.fog, this;
  }
}
rn();
function rn() {
  const t2 = new ArrayBuffer(4), e2 = new Float32Array(t2), s2 = new Uint32Array(t2), i2 = new Uint32Array(512), r2 = new Uint32Array(512);
  for (let t3 = 0; t3 < 256; ++t3) {
    const e3 = t3 - 127;
    e3 < -27 ? (i2[t3] = 0, i2[256 | t3] = 32768, r2[t3] = 24, r2[256 | t3] = 24) : e3 < -14 ? (i2[t3] = 1024 >> -e3 - 14, i2[256 | t3] = 1024 >> -e3 - 14 | 32768, r2[t3] = -e3 - 1, r2[256 | t3] = -e3 - 1) : e3 <= 15 ? (i2[t3] = e3 + 15 << 10, i2[256 | t3] = e3 + 15 << 10 | 32768, r2[t3] = 13, r2[256 | t3] = 13) : e3 < 128 ? (i2[t3] = 31744, i2[256 | t3] = 64512, r2[t3] = 24, r2[256 | t3] = 24) : (i2[t3] = 31744, i2[256 | t3] = 64512, r2[t3] = 13, r2[256 | t3] = 13);
  }
  const n2 = new Uint32Array(2048), o = new Uint32Array(64), a = new Uint32Array(64);
  for (let t3 = 1; t3 < 1024; ++t3) {
    let e3 = t3 << 13, s3 = 0;
    for (; !(8388608 & e3); ) e3 <<= 1, s3 -= 8388608;
    e3 &= -8388609, s3 += 947912704, n2[t3] = e3 | s3;
  }
  for (let t3 = 1024; t3 < 2048; ++t3) n2[t3] = 939524096 + (t3 - 1024 << 13);
  for (let t3 = 1; t3 < 31; ++t3) o[t3] = t3 << 23;
  o[31] = 1199570944, o[32] = 2147483648;
  for (let t3 = 33; t3 < 63; ++t3) o[t3] = 2147483648 + (t3 - 32 << 23);
  o[63] = 3347054592;
  for (let t3 = 1; t3 < 64; ++t3) 32 !== t3 && (a[t3] = 1024);
  return { floatView: e2, uint32View: s2, baseTable: i2, shiftTable: r2, mantissaTable: n2, exponentTable: o, offsetTable: a };
}
const hn = new Ii(), ln = new Zs();
class cn {
  constructor(t2, e2, s2 = false) {
    if (Array.isArray(t2)) throw new TypeError("THREE.BufferAttribute: array should be a Typed Array.");
    this.isBufferAttribute = true, this.name = "", this.array = t2, this.itemSize = e2, this.count = void 0 !== t2 ? t2.length / e2 : 0, this.normalized = s2, this.usage = _s, this.updateRanges = [], this.gpuType = Rt, this.version = 0;
  }
  onUploadCallback() {
  }
  set needsUpdate(t2) {
    true === t2 && this.version++;
  }
  setUsage(t2) {
    return this.usage = t2, this;
  }
  addUpdateRange(t2, e2) {
    this.updateRanges.push({ start: t2, count: e2 });
  }
  clearUpdateRanges() {
    this.updateRanges.length = 0;
  }
  copy(t2) {
    return this.name = t2.name, this.array = new t2.array.constructor(t2.array), this.itemSize = t2.itemSize, this.count = t2.count, this.normalized = t2.normalized, this.usage = t2.usage, this.gpuType = t2.gpuType, this;
  }
  copyAt(t2, e2, s2) {
    t2 *= this.itemSize, s2 *= e2.itemSize;
    for (let i2 = 0, r2 = this.itemSize; i2 < r2; i2++) this.array[t2 + i2] = e2.array[s2 + i2];
    return this;
  }
  copyArray(t2) {
    return this.array.set(t2), this;
  }
  applyMatrix3(t2) {
    if (2 === this.itemSize) for (let e2 = 0, s2 = this.count; e2 < s2; e2++) ln.fromBufferAttribute(this, e2), ln.applyMatrix3(t2), this.setXY(e2, ln.x, ln.y);
    else if (3 === this.itemSize) for (let e2 = 0, s2 = this.count; e2 < s2; e2++) hn.fromBufferAttribute(this, e2), hn.applyMatrix3(t2), this.setXYZ(e2, hn.x, hn.y, hn.z);
    return this;
  }
  applyMatrix4(t2) {
    for (let e2 = 0, s2 = this.count; e2 < s2; e2++) hn.fromBufferAttribute(this, e2), hn.applyMatrix4(t2), this.setXYZ(e2, hn.x, hn.y, hn.z);
    return this;
  }
  applyNormalMatrix(t2) {
    for (let e2 = 0, s2 = this.count; e2 < s2; e2++) hn.fromBufferAttribute(this, e2), hn.applyNormalMatrix(t2), this.setXYZ(e2, hn.x, hn.y, hn.z);
    return this;
  }
  transformDirection(t2) {
    for (let e2 = 0, s2 = this.count; e2 < s2; e2++) hn.fromBufferAttribute(this, e2), hn.transformDirection(t2), this.setXYZ(e2, hn.x, hn.y, hn.z);
    return this;
  }
  set(t2, e2 = 0) {
    return this.array.set(t2, e2), this;
  }
  getComponent(t2, e2) {
    let s2 = this.array[t2 * this.itemSize + e2];
    return this.normalized && (s2 = Js(s2, this.array)), s2;
  }
  setComponent(t2, e2, s2) {
    return this.normalized && (s2 = Xs(s2, this.array)), this.array[t2 * this.itemSize + e2] = s2, this;
  }
  getX(t2) {
    let e2 = this.array[t2 * this.itemSize];
    return this.normalized && (e2 = Js(e2, this.array)), e2;
  }
  setX(t2, e2) {
    return this.normalized && (e2 = Xs(e2, this.array)), this.array[t2 * this.itemSize] = e2, this;
  }
  getY(t2) {
    let e2 = this.array[t2 * this.itemSize + 1];
    return this.normalized && (e2 = Js(e2, this.array)), e2;
  }
  setY(t2, e2) {
    return this.normalized && (e2 = Xs(e2, this.array)), this.array[t2 * this.itemSize + 1] = e2, this;
  }
  getZ(t2) {
    let e2 = this.array[t2 * this.itemSize + 2];
    return this.normalized && (e2 = Js(e2, this.array)), e2;
  }
  setZ(t2, e2) {
    return this.normalized && (e2 = Xs(e2, this.array)), this.array[t2 * this.itemSize + 2] = e2, this;
  }
  getW(t2) {
    let e2 = this.array[t2 * this.itemSize + 3];
    return this.normalized && (e2 = Js(e2, this.array)), e2;
  }
  setW(t2, e2) {
    return this.normalized && (e2 = Xs(e2, this.array)), this.array[t2 * this.itemSize + 3] = e2, this;
  }
  setXY(t2, e2, s2) {
    return t2 *= this.itemSize, this.normalized && (e2 = Xs(e2, this.array), s2 = Xs(s2, this.array)), this.array[t2 + 0] = e2, this.array[t2 + 1] = s2, this;
  }
  setXYZ(t2, e2, s2, i2) {
    return t2 *= this.itemSize, this.normalized && (e2 = Xs(e2, this.array), s2 = Xs(s2, this.array), i2 = Xs(i2, this.array)), this.array[t2 + 0] = e2, this.array[t2 + 1] = s2, this.array[t2 + 2] = i2, this;
  }
  setXYZW(t2, e2, s2, i2, r2) {
    return t2 *= this.itemSize, this.normalized && (e2 = Xs(e2, this.array), s2 = Xs(s2, this.array), i2 = Xs(i2, this.array), r2 = Xs(r2, this.array)), this.array[t2 + 0] = e2, this.array[t2 + 1] = s2, this.array[t2 + 2] = i2, this.array[t2 + 3] = r2, this;
  }
  onUpload(t2) {
    return this.onUploadCallback = t2, this;
  }
  clone() {
    return new this.constructor(this.array, this.itemSize).copy(this);
  }
  toJSON() {
    const t2 = { itemSize: this.itemSize, type: this.array.constructor.name, array: Array.from(this.array), normalized: this.normalized };
    return "" !== this.name && (t2.name = this.name), this.usage !== _s && (t2.usage = this.usage), t2;
  }
}
class yn extends cn {
  constructor(t2, e2, s2) {
    super(new Uint16Array(t2), e2, s2);
  }
}
class gn extends cn {
  constructor(t2, e2, s2) {
    super(new Uint32Array(t2), e2, s2);
  }
}
class bn extends cn {
  constructor(t2, e2, s2) {
    super(new Float32Array(t2), e2, s2);
  }
}
let vn = 0;
const wn = new nr(), Mn = new Rr(), Sn = new Ii(), _n = new Ri(), An = new Ri(), Tn = new Ii();
class zn extends Ns {
  constructor() {
    super(), this.isBufferGeometry = true, Object.defineProperty(this, "id", { value: vn++ }), this.uuid = Us(), this.name = "", this.type = "BufferGeometry", this.index = null, this.indirect = null, this.attributes = {}, this.morphAttributes = {}, this.morphTargetsRelative = false, this.groups = [], this.boundingBox = null, this.boundingSphere = null, this.drawRange = { start: 0, count: 1 / 0 }, this.userData = {};
  }
  getIndex() {
    return this.index;
  }
  setIndex(t2) {
    return Array.isArray(t2) ? this.index = new (Qs(t2) ? gn : yn)(t2, 1) : this.index = t2, this;
  }
  setIndirect(t2) {
    return this.indirect = t2, this;
  }
  getIndirect() {
    return this.indirect;
  }
  getAttribute(t2) {
    return this.attributes[t2];
  }
  setAttribute(t2, e2) {
    return this.attributes[t2] = e2, this;
  }
  deleteAttribute(t2) {
    return delete this.attributes[t2], this;
  }
  hasAttribute(t2) {
    return void 0 !== this.attributes[t2];
  }
  addGroup(t2, e2, s2 = 0) {
    this.groups.push({ start: t2, count: e2, materialIndex: s2 });
  }
  clearGroups() {
    this.groups = [];
  }
  setDrawRange(t2, e2) {
    this.drawRange.start = t2, this.drawRange.count = e2;
  }
  applyMatrix4(t2) {
    const e2 = this.attributes.position;
    void 0 !== e2 && (e2.applyMatrix4(t2), e2.needsUpdate = true);
    const s2 = this.attributes.normal;
    if (void 0 !== s2) {
      const e3 = new Gs().getNormalMatrix(t2);
      s2.applyNormalMatrix(e3), s2.needsUpdate = true;
    }
    const i2 = this.attributes.tangent;
    return void 0 !== i2 && (i2.transformDirection(t2), i2.needsUpdate = true), null !== this.boundingBox && this.computeBoundingBox(), null !== this.boundingSphere && this.computeBoundingSphere(), this;
  }
  applyQuaternion(t2) {
    return wn.makeRotationFromQuaternion(t2), this.applyMatrix4(wn), this;
  }
  rotateX(t2) {
    return wn.makeRotationX(t2), this.applyMatrix4(wn), this;
  }
  rotateY(t2) {
    return wn.makeRotationY(t2), this.applyMatrix4(wn), this;
  }
  rotateZ(t2) {
    return wn.makeRotationZ(t2), this.applyMatrix4(wn), this;
  }
  translate(t2, e2, s2) {
    return wn.makeTranslation(t2, e2, s2), this.applyMatrix4(wn), this;
  }
  scale(t2, e2, s2) {
    return wn.makeScale(t2, e2, s2), this.applyMatrix4(wn), this;
  }
  lookAt(t2) {
    return Mn.lookAt(t2), Mn.updateMatrix(), this.applyMatrix4(Mn.matrix), this;
  }
  center() {
    return this.computeBoundingBox(), this.boundingBox.getCenter(Sn).negate(), this.translate(Sn.x, Sn.y, Sn.z), this;
  }
  setFromPoints(t2) {
    const e2 = this.getAttribute("position");
    if (void 0 === e2) {
      const e3 = [];
      for (let s2 = 0, i2 = t2.length; s2 < i2; s2++) {
        const i3 = t2[s2];
        e3.push(i3.x, i3.y, i3.z || 0);
      }
      this.setAttribute("position", new bn(e3, 3));
    } else {
      const s2 = Math.min(t2.length, e2.count);
      for (let i2 = 0; i2 < s2; i2++) {
        const s3 = t2[i2];
        e2.setXYZ(i2, s3.x, s3.y, s3.z || 0);
      }
      t2.length > e2.count && console.warn("THREE.BufferGeometry: Buffer size too small for points data. Use .dispose() and create a new geometry."), e2.needsUpdate = true;
    }
    return this;
  }
  computeBoundingBox() {
    null === this.boundingBox && (this.boundingBox = new Ri());
    const t2 = this.attributes.position, e2 = this.morphAttributes.position;
    if (t2 && t2.isGLBufferAttribute) return console.error("THREE.BufferGeometry.computeBoundingBox(): GLBufferAttribute requires a manual bounding box.", this), void this.boundingBox.set(new Ii(-1 / 0, -1 / 0, -1 / 0), new Ii(1 / 0, 1 / 0, 1 / 0));
    if (void 0 !== t2) {
      if (this.boundingBox.setFromBufferAttribute(t2), e2) for (let t3 = 0, s2 = e2.length; t3 < s2; t3++) {
        const s3 = e2[t3];
        _n.setFromBufferAttribute(s3), this.morphTargetsRelative ? (Tn.addVectors(this.boundingBox.min, _n.min), this.boundingBox.expandByPoint(Tn), Tn.addVectors(this.boundingBox.max, _n.max), this.boundingBox.expandByPoint(Tn)) : (this.boundingBox.expandByPoint(_n.min), this.boundingBox.expandByPoint(_n.max));
      }
    } else this.boundingBox.makeEmpty();
    (isNaN(this.boundingBox.min.x) || isNaN(this.boundingBox.min.y) || isNaN(this.boundingBox.min.z)) && console.error('THREE.BufferGeometry.computeBoundingBox(): Computed min/max have NaN values. The "position" attribute is likely to have NaN values.', this);
  }
  computeBoundingSphere() {
    null === this.boundingSphere && (this.boundingSphere = new Gi());
    const t2 = this.attributes.position, e2 = this.morphAttributes.position;
    if (t2 && t2.isGLBufferAttribute) return console.error("THREE.BufferGeometry.computeBoundingSphere(): GLBufferAttribute requires a manual bounding sphere.", this), void this.boundingSphere.set(new Ii(), 1 / 0);
    if (t2) {
      const s2 = this.boundingSphere.center;
      if (_n.setFromBufferAttribute(t2), e2) for (let t3 = 0, s3 = e2.length; t3 < s3; t3++) {
        const s4 = e2[t3];
        An.setFromBufferAttribute(s4), this.morphTargetsRelative ? (Tn.addVectors(_n.min, An.min), _n.expandByPoint(Tn), Tn.addVectors(_n.max, An.max), _n.expandByPoint(Tn)) : (_n.expandByPoint(An.min), _n.expandByPoint(An.max));
      }
      _n.getCenter(s2);
      let i2 = 0;
      for (let e3 = 0, r2 = t2.count; e3 < r2; e3++) Tn.fromBufferAttribute(t2, e3), i2 = Math.max(i2, s2.distanceToSquared(Tn));
      if (e2) for (let r2 = 0, n2 = e2.length; r2 < n2; r2++) {
        const n3 = e2[r2], o = this.morphTargetsRelative;
        for (let e3 = 0, r3 = n3.count; e3 < r3; e3++) Tn.fromBufferAttribute(n3, e3), o && (Sn.fromBufferAttribute(t2, e3), Tn.add(Sn)), i2 = Math.max(i2, s2.distanceToSquared(Tn));
      }
      this.boundingSphere.radius = Math.sqrt(i2), isNaN(this.boundingSphere.radius) && console.error('THREE.BufferGeometry.computeBoundingSphere(): Computed radius is NaN. The "position" attribute is likely to have NaN values.', this);
    }
  }
  computeTangents() {
    const t2 = this.index, e2 = this.attributes;
    if (null === t2 || void 0 === e2.position || void 0 === e2.normal || void 0 === e2.uv) return void console.error("THREE.BufferGeometry: .computeTangents() failed. Missing required attributes (index, position, normal or uv)");
    const s2 = e2.position, i2 = e2.normal, r2 = e2.uv;
    false === this.hasAttribute("tangent") && this.setAttribute("tangent", new cn(new Float32Array(4 * s2.count), 4));
    const n2 = this.getAttribute("tangent"), o = [], a = [];
    for (let t3 = 0; t3 < s2.count; t3++) o[t3] = new Ii(), a[t3] = new Ii();
    const h2 = new Ii(), l2 = new Ii(), c2 = new Ii(), u2 = new Zs(), d2 = new Zs(), p2 = new Zs(), m2 = new Ii(), y2 = new Ii();
    function f2(t3, e3, i3) {
      h2.fromBufferAttribute(s2, t3), l2.fromBufferAttribute(s2, e3), c2.fromBufferAttribute(s2, i3), u2.fromBufferAttribute(r2, t3), d2.fromBufferAttribute(r2, e3), p2.fromBufferAttribute(r2, i3), l2.sub(h2), c2.sub(h2), d2.sub(u2), p2.sub(u2);
      const n3 = 1 / (d2.x * p2.y - p2.x * d2.y);
      isFinite(n3) && (m2.copy(l2).multiplyScalar(p2.y).addScaledVector(c2, -d2.y).multiplyScalar(n3), y2.copy(c2).multiplyScalar(d2.x).addScaledVector(l2, -p2.x).multiplyScalar(n3), o[t3].add(m2), o[e3].add(m2), o[i3].add(m2), a[t3].add(y2), a[e3].add(y2), a[i3].add(y2));
    }
    let g2 = this.groups;
    0 === g2.length && (g2 = [{ start: 0, count: t2.count }]);
    for (let e3 = 0, s3 = g2.length; e3 < s3; ++e3) {
      const s4 = g2[e3], i3 = s4.start;
      for (let e4 = i3, r3 = i3 + s4.count; e4 < r3; e4 += 3) f2(t2.getX(e4 + 0), t2.getX(e4 + 1), t2.getX(e4 + 2));
    }
    const x2 = new Ii(), b2 = new Ii(), v2 = new Ii(), w2 = new Ii();
    function M2(t3) {
      v2.fromBufferAttribute(i2, t3), w2.copy(v2);
      const e3 = o[t3];
      x2.copy(e3), x2.sub(v2.multiplyScalar(v2.dot(e3))).normalize(), b2.crossVectors(w2, e3);
      const s3 = b2.dot(a[t3]) < 0 ? -1 : 1;
      n2.setXYZW(t3, x2.x, x2.y, x2.z, s3);
    }
    for (let e3 = 0, s3 = g2.length; e3 < s3; ++e3) {
      const s4 = g2[e3], i3 = s4.start;
      for (let e4 = i3, r3 = i3 + s4.count; e4 < r3; e4 += 3) M2(t2.getX(e4 + 0)), M2(t2.getX(e4 + 1)), M2(t2.getX(e4 + 2));
    }
  }
  computeVertexNormals() {
    const t2 = this.index, e2 = this.getAttribute("position");
    if (void 0 !== e2) {
      let s2 = this.getAttribute("normal");
      if (void 0 === s2) s2 = new cn(new Float32Array(3 * e2.count), 3), this.setAttribute("normal", s2);
      else for (let t3 = 0, e3 = s2.count; t3 < e3; t3++) s2.setXYZ(t3, 0, 0, 0);
      const i2 = new Ii(), r2 = new Ii(), n2 = new Ii(), o = new Ii(), a = new Ii(), h2 = new Ii(), l2 = new Ii(), c2 = new Ii();
      if (t2) for (let u2 = 0, d2 = t2.count; u2 < d2; u2 += 3) {
        const d3 = t2.getX(u2 + 0), p2 = t2.getX(u2 + 1), m2 = t2.getX(u2 + 2);
        i2.fromBufferAttribute(e2, d3), r2.fromBufferAttribute(e2, p2), n2.fromBufferAttribute(e2, m2), l2.subVectors(n2, r2), c2.subVectors(i2, r2), l2.cross(c2), o.fromBufferAttribute(s2, d3), a.fromBufferAttribute(s2, p2), h2.fromBufferAttribute(s2, m2), o.add(l2), a.add(l2), h2.add(l2), s2.setXYZ(d3, o.x, o.y, o.z), s2.setXYZ(p2, a.x, a.y, a.z), s2.setXYZ(m2, h2.x, h2.y, h2.z);
      }
      else for (let t3 = 0, o2 = e2.count; t3 < o2; t3 += 3) i2.fromBufferAttribute(e2, t3 + 0), r2.fromBufferAttribute(e2, t3 + 1), n2.fromBufferAttribute(e2, t3 + 2), l2.subVectors(n2, r2), c2.subVectors(i2, r2), l2.cross(c2), s2.setXYZ(t3 + 0, l2.x, l2.y, l2.z), s2.setXYZ(t3 + 1, l2.x, l2.y, l2.z), s2.setXYZ(t3 + 2, l2.x, l2.y, l2.z);
      this.normalizeNormals(), s2.needsUpdate = true;
    }
  }
  normalizeNormals() {
    const t2 = this.attributes.normal;
    for (let e2 = 0, s2 = t2.count; e2 < s2; e2++) Tn.fromBufferAttribute(t2, e2), Tn.normalize(), t2.setXYZ(e2, Tn.x, Tn.y, Tn.z);
  }
  toNonIndexed() {
    function t2(t3, e3) {
      const s3 = t3.array, i3 = t3.itemSize, r3 = t3.normalized, n3 = new s3.constructor(e3.length * i3);
      let o = 0, a = 0;
      for (let r4 = 0, h2 = e3.length; r4 < h2; r4++) {
        o = t3.isInterleavedBufferAttribute ? e3[r4] * t3.data.stride + t3.offset : e3[r4] * i3;
        for (let t4 = 0; t4 < i3; t4++) n3[a++] = s3[o++];
      }
      return new cn(n3, i3, r3);
    }
    if (null === this.index) return console.warn("THREE.BufferGeometry.toNonIndexed(): BufferGeometry is already non-indexed."), this;
    const e2 = new zn(), s2 = this.index.array, i2 = this.attributes;
    for (const r3 in i2) {
      const n3 = t2(i2[r3], s2);
      e2.setAttribute(r3, n3);
    }
    const r2 = this.morphAttributes;
    for (const i3 in r2) {
      const n3 = [], o = r2[i3];
      for (let e3 = 0, i4 = o.length; e3 < i4; e3++) {
        const i5 = t2(o[e3], s2);
        n3.push(i5);
      }
      e2.morphAttributes[i3] = n3;
    }
    e2.morphTargetsRelative = this.morphTargetsRelative;
    const n2 = this.groups;
    for (let t3 = 0, s3 = n2.length; t3 < s3; t3++) {
      const s4 = n2[t3];
      e2.addGroup(s4.start, s4.count, s4.materialIndex);
    }
    return e2;
  }
  toJSON() {
    const t2 = { metadata: { version: 4.6, type: "BufferGeometry", generator: "BufferGeometry.toJSON" } };
    if (t2.uuid = this.uuid, t2.type = this.type, "" !== this.name && (t2.name = this.name), Object.keys(this.userData).length > 0 && (t2.userData = this.userData), void 0 !== this.parameters) {
      const e3 = this.parameters;
      for (const s3 in e3) void 0 !== e3[s3] && (t2[s3] = e3[s3]);
      return t2;
    }
    t2.data = { attributes: {} };
    const e2 = this.index;
    null !== e2 && (t2.data.index = { type: e2.array.constructor.name, array: Array.prototype.slice.call(e2.array) });
    const s2 = this.attributes;
    for (const e3 in s2) {
      const i3 = s2[e3];
      t2.data.attributes[e3] = i3.toJSON(t2.data);
    }
    const i2 = {};
    let r2 = false;
    for (const e3 in this.morphAttributes) {
      const s3 = this.morphAttributes[e3], n3 = [];
      for (let e4 = 0, i3 = s3.length; e4 < i3; e4++) {
        const i4 = s3[e4];
        n3.push(i4.toJSON(t2.data));
      }
      n3.length > 0 && (i2[e3] = n3, r2 = true);
    }
    r2 && (t2.data.morphAttributes = i2, t2.data.morphTargetsRelative = this.morphTargetsRelative);
    const n2 = this.groups;
    n2.length > 0 && (t2.data.groups = JSON.parse(JSON.stringify(n2)));
    const o = this.boundingSphere;
    return null !== o && (t2.data.boundingSphere = { center: o.center.toArray(), radius: o.radius }), t2;
  }
  clone() {
    return new this.constructor().copy(this);
  }
  copy(t2) {
    this.index = null, this.attributes = {}, this.morphAttributes = {}, this.groups = [], this.boundingBox = null, this.boundingSphere = null;
    const e2 = {};
    this.name = t2.name;
    const s2 = t2.index;
    null !== s2 && this.setIndex(s2.clone(e2));
    const i2 = t2.attributes;
    for (const t3 in i2) {
      const s3 = i2[t3];
      this.setAttribute(t3, s3.clone(e2));
    }
    const r2 = t2.morphAttributes;
    for (const t3 in r2) {
      const s3 = [], i3 = r2[t3];
      for (let t4 = 0, r3 = i3.length; t4 < r3; t4++) s3.push(i3[t4].clone(e2));
      this.morphAttributes[t3] = s3;
    }
    this.morphTargetsRelative = t2.morphTargetsRelative;
    const n2 = t2.groups;
    for (let t3 = 0, e3 = n2.length; t3 < e3; t3++) {
      const e4 = n2[t3];
      this.addGroup(e4.start, e4.count, e4.materialIndex);
    }
    const o = t2.boundingBox;
    null !== o && (this.boundingBox = o.clone());
    const a = t2.boundingSphere;
    return null !== a && (this.boundingSphere = a.clone()), this.drawRange.start = t2.drawRange.start, this.drawRange.count = t2.drawRange.count, this.userData = t2.userData, this;
  }
  dispose() {
    this.dispatchEvent({ type: "dispose" });
  }
}
const Cn = new nr(), In = new rr(), Bn = new Gi(), kn = new Ii(), Rn = new Ii(), En = new Ii(), Pn = new Ii(), On = new Ii(), Fn = new Ii(), Nn = new Ii(), Ln = new Ii();
class Vn extends Rr {
  constructor(t2 = new zn(), e2 = new en()) {
    super(), this.isMesh = true, this.type = "Mesh", this.geometry = t2, this.material = e2, this.updateMorphTargets();
  }
  copy(t2, e2) {
    return super.copy(t2, e2), void 0 !== t2.morphTargetInfluences && (this.morphTargetInfluences = t2.morphTargetInfluences.slice()), void 0 !== t2.morphTargetDictionary && (this.morphTargetDictionary = Object.assign({}, t2.morphTargetDictionary)), this.material = Array.isArray(t2.material) ? t2.material.slice() : t2.material, this.geometry = t2.geometry, this;
  }
  updateMorphTargets() {
    const t2 = this.geometry.morphAttributes, e2 = Object.keys(t2);
    if (e2.length > 0) {
      const s2 = t2[e2[0]];
      if (void 0 !== s2) {
        this.morphTargetInfluences = [], this.morphTargetDictionary = {};
        for (let t3 = 0, e3 = s2.length; t3 < e3; t3++) {
          const e4 = s2[t3].name || String(t3);
          this.morphTargetInfluences.push(0), this.morphTargetDictionary[e4] = t3;
        }
      }
    }
  }
  getVertexPosition(t2, e2) {
    const s2 = this.geometry, i2 = s2.attributes.position, r2 = s2.morphAttributes.position, n2 = s2.morphTargetsRelative;
    e2.fromBufferAttribute(i2, t2);
    const o = this.morphTargetInfluences;
    if (r2 && o) {
      Fn.set(0, 0, 0);
      for (let s3 = 0, i3 = r2.length; s3 < i3; s3++) {
        const i4 = o[s3], a = r2[s3];
        0 !== i4 && (On.fromBufferAttribute(a, t2), n2 ? Fn.addScaledVector(On, i4) : Fn.addScaledVector(On.sub(e2), i4));
      }
      e2.add(Fn);
    }
    return e2;
  }
  raycast(t2, e2) {
    const s2 = this.geometry, i2 = this.material, r2 = this.matrixWorld;
    if (void 0 !== i2) {
      if (null === s2.boundingSphere && s2.computeBoundingSphere(), Bn.copy(s2.boundingSphere), Bn.applyMatrix4(r2), In.copy(t2.ray).recast(t2.near), false === Bn.containsPoint(In.origin)) {
        if (null === In.intersectSphere(Bn, kn)) return;
        if (In.origin.distanceToSquared(kn) > (t2.far - t2.near) ** 2) return;
      }
      Cn.copy(r2).invert(), In.copy(t2.ray).applyMatrix4(Cn), null !== s2.boundingBox && false === In.intersectsBox(s2.boundingBox) || this._computeIntersections(t2, e2, In);
    }
  }
  _computeIntersections(t2, e2, s2) {
    let i2;
    const r2 = this.geometry, n2 = this.material, o = r2.index, a = r2.attributes.position, h2 = r2.attributes.uv, l2 = r2.attributes.uv1, c2 = r2.attributes.normal, u2 = r2.groups, d2 = r2.drawRange;
    if (null !== o) if (Array.isArray(n2)) for (let r3 = 0, a2 = u2.length; r3 < a2; r3++) {
      const a3 = u2[r3], p2 = n2[a3.materialIndex];
      for (let r4 = Math.max(a3.start, d2.start), n3 = Math.min(o.count, Math.min(a3.start + a3.count, d2.start + d2.count)); r4 < n3; r4 += 3) {
        i2 = Wn(this, p2, t2, s2, h2, l2, c2, o.getX(r4), o.getX(r4 + 1), o.getX(r4 + 2)), i2 && (i2.faceIndex = Math.floor(r4 / 3), i2.face.materialIndex = a3.materialIndex, e2.push(i2));
      }
    }
    else {
      for (let r3 = Math.max(0, d2.start), a2 = Math.min(o.count, d2.start + d2.count); r3 < a2; r3 += 3) {
        i2 = Wn(this, n2, t2, s2, h2, l2, c2, o.getX(r3), o.getX(r3 + 1), o.getX(r3 + 2)), i2 && (i2.faceIndex = Math.floor(r3 / 3), e2.push(i2));
      }
    }
    else if (void 0 !== a) if (Array.isArray(n2)) for (let r3 = 0, o2 = u2.length; r3 < o2; r3++) {
      const o3 = u2[r3], p2 = n2[o3.materialIndex];
      for (let r4 = Math.max(o3.start, d2.start), n3 = Math.min(a.count, Math.min(o3.start + o3.count, d2.start + d2.count)); r4 < n3; r4 += 3) {
        i2 = Wn(this, p2, t2, s2, h2, l2, c2, r4, r4 + 1, r4 + 2), i2 && (i2.faceIndex = Math.floor(r4 / 3), i2.face.materialIndex = o3.materialIndex, e2.push(i2));
      }
    }
    else {
      for (let r3 = Math.max(0, d2.start), o2 = Math.min(a.count, d2.start + d2.count); r3 < o2; r3 += 3) {
        i2 = Wn(this, n2, t2, s2, h2, l2, c2, r3, r3 + 1, r3 + 2), i2 && (i2.faceIndex = Math.floor(r3 / 3), e2.push(i2));
      }
    }
  }
}
function Wn(t2, e2, s2, i2, r2, n2, o, a, h2, l2) {
  t2.getVertexPosition(a, Rn), t2.getVertexPosition(h2, En), t2.getVertexPosition(l2, Pn);
  const c2 = function(t3, e3, s3, i3, r3, n3, o2, a2) {
    let h3;
    if (h3 = 1 === e3.side ? i3.intersectTriangle(o2, n3, r3, true, a2) : i3.intersectTriangle(r3, n3, o2, 0 === e3.side, a2), null === h3) return null;
    Ln.copy(a2), Ln.applyMatrix4(t3.matrixWorld);
    const l3 = s3.ray.origin.distanceTo(Ln);
    return l3 < s3.near || l3 > s3.far ? null : { distance: l3, point: Ln.clone(), object: t3 };
  }(t2, e2, s2, i2, Rn, En, Pn, Nn);
  if (c2) {
    const t3 = new Ii();
    Jr.getBarycoord(Nn, Rn, En, Pn, t3), r2 && (c2.uv = Jr.getInterpolatedAttribute(r2, a, h2, l2, t3, new Zs())), n2 && (c2.uv1 = Jr.getInterpolatedAttribute(n2, a, h2, l2, t3, new Zs())), o && (c2.normal = Jr.getInterpolatedAttribute(o, a, h2, l2, t3, new Ii()), c2.normal.dot(i2.direction) > 0 && c2.normal.multiplyScalar(-1));
    const e3 = { a, b: h2, c: l2, normal: new Ii(), materialIndex: 0 };
    Jr.getNormal(Rn, En, Pn, e3.normal), c2.face = e3, c2.barycoord = t3;
  }
  return c2;
}
class jn extends zn {
  constructor(t2 = 1, e2 = 1, s2 = 1, i2 = 1, r2 = 1, n2 = 1) {
    super(), this.type = "BoxGeometry", this.parameters = { width: t2, height: e2, depth: s2, widthSegments: i2, heightSegments: r2, depthSegments: n2 };
    const o = this;
    i2 = Math.floor(i2), r2 = Math.floor(r2), n2 = Math.floor(n2);
    const a = [], h2 = [], l2 = [], c2 = [];
    let u2 = 0, d2 = 0;
    function p2(t3, e3, s3, i3, r3, n3, p3, m2, y2, f2, g2) {
      const x2 = n3 / y2, b2 = p3 / f2, v2 = n3 / 2, w2 = p3 / 2, M2 = m2 / 2, S2 = y2 + 1, _2 = f2 + 1;
      let A2 = 0, T2 = 0;
      const z2 = new Ii();
      for (let n4 = 0; n4 < _2; n4++) {
        const o2 = n4 * b2 - w2;
        for (let a2 = 0; a2 < S2; a2++) {
          const u3 = a2 * x2 - v2;
          z2[t3] = u3 * i3, z2[e3] = o2 * r3, z2[s3] = M2, h2.push(z2.x, z2.y, z2.z), z2[t3] = 0, z2[e3] = 0, z2[s3] = m2 > 0 ? 1 : -1, l2.push(z2.x, z2.y, z2.z), c2.push(a2 / y2), c2.push(1 - n4 / f2), A2 += 1;
        }
      }
      for (let t4 = 0; t4 < f2; t4++) for (let e4 = 0; e4 < y2; e4++) {
        const s4 = u2 + e4 + S2 * t4, i4 = u2 + e4 + S2 * (t4 + 1), r4 = u2 + (e4 + 1) + S2 * (t4 + 1), n4 = u2 + (e4 + 1) + S2 * t4;
        a.push(s4, i4, n4), a.push(i4, r4, n4), T2 += 6;
      }
      o.addGroup(d2, T2, g2), d2 += T2, u2 += A2;
    }
    p2("z", "y", "x", -1, -1, s2, e2, t2, n2, r2, 0), p2("z", "y", "x", 1, -1, s2, e2, -t2, n2, r2, 1), p2("x", "z", "y", 1, 1, t2, s2, e2, i2, n2, 2), p2("x", "z", "y", 1, -1, t2, s2, -e2, i2, n2, 3), p2("x", "y", "z", 1, -1, t2, e2, s2, i2, r2, 4), p2("x", "y", "z", -1, -1, t2, e2, -s2, i2, r2, 5), this.setIndex(a), this.setAttribute("position", new bn(h2, 3)), this.setAttribute("normal", new bn(l2, 3)), this.setAttribute("uv", new bn(c2, 2));
  }
  copy(t2) {
    return super.copy(t2), this.parameters = Object.assign({}, t2.parameters), this;
  }
  static fromJSON(t2) {
    return new jn(t2.width, t2.height, t2.depth, t2.widthSegments, t2.heightSegments, t2.depthSegments);
  }
}
function Un(t2) {
  const e2 = {};
  for (const s2 in t2) {
    e2[s2] = {};
    for (const i2 in t2[s2]) {
      const r2 = t2[s2][i2];
      r2 && (r2.isColor || r2.isMatrix3 || r2.isMatrix4 || r2.isVector2 || r2.isVector3 || r2.isVector4 || r2.isTexture || r2.isQuaternion) ? r2.isRenderTargetTexture ? (console.warn("UniformsUtils: Textures of render targets cannot be cloned via cloneUniforms() or mergeUniforms()."), e2[s2][i2] = null) : e2[s2][i2] = r2.clone() : Array.isArray(r2) ? e2[s2][i2] = r2.slice() : e2[s2][i2] = r2;
    }
  }
  return e2;
}
function Dn(t2) {
  const e2 = {};
  for (let s2 = 0; s2 < t2.length; s2++) {
    const i2 = Un(t2[s2]);
    for (const t3 in i2) e2[t3] = i2[t3];
  }
  return e2;
}
function Hn(t2) {
  const e2 = t2.getRenderTarget();
  return null === e2 ? t2.outputColorSpace : true === e2.isXRRenderTarget ? e2.texture.colorSpace : ui.workingColorSpace;
}
const qn = { clone: Un, merge: Dn };
class Jn extends tn {
  constructor(t2) {
    super(), this.isShaderMaterial = true, this.type = "ShaderMaterial", this.defines = {}, this.uniforms = {}, this.uniformsGroups = [], this.vertexShader = "void main() {\n	gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n}", this.fragmentShader = "void main() {\n	gl_FragColor = vec4( 1.0, 0.0, 0.0, 1.0 );\n}", this.linewidth = 1, this.wireframe = false, this.wireframeLinewidth = 1, this.fog = false, this.lights = false, this.clipping = false, this.forceSinglePass = true, this.extensions = { clipCullDistance: false, multiDraw: false }, this.defaultAttributeValues = { color: [1, 1, 1], uv: [0, 0], uv1: [0, 0] }, this.index0AttributeName = void 0, this.uniformsNeedUpdate = false, this.glslVersion = null, void 0 !== t2 && this.setValues(t2);
  }
  copy(t2) {
    return super.copy(t2), this.fragmentShader = t2.fragmentShader, this.vertexShader = t2.vertexShader, this.uniforms = Un(t2.uniforms), this.uniformsGroups = function(t3) {
      const e2 = [];
      for (let s2 = 0; s2 < t3.length; s2++) e2.push(t3[s2].clone());
      return e2;
    }(t2.uniformsGroups), this.defines = Object.assign({}, t2.defines), this.wireframe = t2.wireframe, this.wireframeLinewidth = t2.wireframeLinewidth, this.fog = t2.fog, this.lights = t2.lights, this.clipping = t2.clipping, this.extensions = Object.assign({}, t2.extensions), this.glslVersion = t2.glslVersion, this;
  }
  toJSON(t2) {
    const e2 = super.toJSON(t2);
    e2.glslVersion = this.glslVersion, e2.uniforms = {};
    for (const s3 in this.uniforms) {
      const i2 = this.uniforms[s3].value;
      i2 && i2.isTexture ? e2.uniforms[s3] = { type: "t", value: i2.toJSON(t2).uuid } : i2 && i2.isColor ? e2.uniforms[s3] = { type: "c", value: i2.getHex() } : i2 && i2.isVector2 ? e2.uniforms[s3] = { type: "v2", value: i2.toArray() } : i2 && i2.isVector3 ? e2.uniforms[s3] = { type: "v3", value: i2.toArray() } : i2 && i2.isVector4 ? e2.uniforms[s3] = { type: "v4", value: i2.toArray() } : i2 && i2.isMatrix3 ? e2.uniforms[s3] = { type: "m3", value: i2.toArray() } : i2 && i2.isMatrix4 ? e2.uniforms[s3] = { type: "m4", value: i2.toArray() } : e2.uniforms[s3] = { value: i2 };
    }
    Object.keys(this.defines).length > 0 && (e2.defines = this.defines), e2.vertexShader = this.vertexShader, e2.fragmentShader = this.fragmentShader, e2.lights = this.lights, e2.clipping = this.clipping;
    const s2 = {};
    for (const t3 in this.extensions) true === this.extensions[t3] && (s2[t3] = true);
    return Object.keys(s2).length > 0 && (e2.extensions = s2), e2;
  }
}
class Xn extends Rr {
  constructor() {
    super(), this.isCamera = true, this.type = "Camera", this.matrixWorldInverse = new nr(), this.projectionMatrix = new nr(), this.projectionMatrixInverse = new nr(), this.coordinateSystem = Os;
  }
  copy(t2, e2) {
    return super.copy(t2, e2), this.matrixWorldInverse.copy(t2.matrixWorldInverse), this.projectionMatrix.copy(t2.projectionMatrix), this.projectionMatrixInverse.copy(t2.projectionMatrixInverse), this.coordinateSystem = t2.coordinateSystem, this;
  }
  getWorldDirection(t2) {
    return super.getWorldDirection(t2).negate();
  }
  updateMatrixWorld(t2) {
    super.updateMatrixWorld(t2), this.matrixWorldInverse.copy(this.matrixWorld).invert();
  }
  updateWorldMatrix(t2, e2) {
    super.updateWorldMatrix(t2, e2), this.matrixWorldInverse.copy(this.matrixWorld).invert();
  }
  clone() {
    return new this.constructor().copy(this);
  }
}
const Yn = new Ii(), Zn = new Zs(), Gn = new Zs();
class $n extends Xn {
  constructor(t2 = 50, e2 = 1, s2 = 0.1, i2 = 2e3) {
    super(), this.isPerspectiveCamera = true, this.type = "PerspectiveCamera", this.fov = t2, this.zoom = 1, this.near = s2, this.far = i2, this.focus = 10, this.aspect = e2, this.view = null, this.filmGauge = 35, this.filmOffset = 0, this.updateProjectionMatrix();
  }
  copy(t2, e2) {
    return super.copy(t2, e2), this.fov = t2.fov, this.zoom = t2.zoom, this.near = t2.near, this.far = t2.far, this.focus = t2.focus, this.aspect = t2.aspect, this.view = null === t2.view ? null : Object.assign({}, t2.view), this.filmGauge = t2.filmGauge, this.filmOffset = t2.filmOffset, this;
  }
  setFocalLength(t2) {
    const e2 = 0.5 * this.getFilmHeight() / t2;
    this.fov = 2 * js * Math.atan(e2), this.updateProjectionMatrix();
  }
  getFocalLength() {
    const t2 = Math.tan(0.5 * Ws * this.fov);
    return 0.5 * this.getFilmHeight() / t2;
  }
  getEffectiveFOV() {
    return 2 * js * Math.atan(Math.tan(0.5 * Ws * this.fov) / this.zoom);
  }
  getFilmWidth() {
    return this.filmGauge * Math.min(this.aspect, 1);
  }
  getFilmHeight() {
    return this.filmGauge / Math.max(this.aspect, 1);
  }
  getViewBounds(t2, e2, s2) {
    Yn.set(-1, -1, 0.5).applyMatrix4(this.projectionMatrixInverse), e2.set(Yn.x, Yn.y).multiplyScalar(-t2 / Yn.z), Yn.set(1, 1, 0.5).applyMatrix4(this.projectionMatrixInverse), s2.set(Yn.x, Yn.y).multiplyScalar(-t2 / Yn.z);
  }
  getViewSize(t2, e2) {
    return this.getViewBounds(t2, Zn, Gn), e2.subVectors(Gn, Zn);
  }
  setViewOffset(t2, e2, s2, i2, r2, n2) {
    this.aspect = t2 / e2, null === this.view && (this.view = { enabled: true, fullWidth: 1, fullHeight: 1, offsetX: 0, offsetY: 0, width: 1, height: 1 }), this.view.enabled = true, this.view.fullWidth = t2, this.view.fullHeight = e2, this.view.offsetX = s2, this.view.offsetY = i2, this.view.width = r2, this.view.height = n2, this.updateProjectionMatrix();
  }
  clearViewOffset() {
    null !== this.view && (this.view.enabled = false), this.updateProjectionMatrix();
  }
  updateProjectionMatrix() {
    const t2 = this.near;
    let e2 = t2 * Math.tan(0.5 * Ws * this.fov) / this.zoom, s2 = 2 * e2, i2 = this.aspect * s2, r2 = -0.5 * i2;
    const n2 = this.view;
    if (null !== this.view && this.view.enabled) {
      const t3 = n2.fullWidth, o2 = n2.fullHeight;
      r2 += n2.offsetX * i2 / t3, e2 -= n2.offsetY * s2 / o2, i2 *= n2.width / t3, s2 *= n2.height / o2;
    }
    const o = this.filmOffset;
    0 !== o && (r2 += t2 * o / this.getFilmWidth()), this.projectionMatrix.makePerspective(r2, r2 + i2, e2, e2 - s2, t2, this.far, this.coordinateSystem), this.projectionMatrixInverse.copy(this.projectionMatrix).invert();
  }
  toJSON(t2) {
    const e2 = super.toJSON(t2);
    return e2.object.fov = this.fov, e2.object.zoom = this.zoom, e2.object.near = this.near, e2.object.far = this.far, e2.object.focus = this.focus, e2.object.aspect = this.aspect, null !== this.view && (e2.object.view = Object.assign({}, this.view)), e2.object.filmGauge = this.filmGauge, e2.object.filmOffset = this.filmOffset, e2;
  }
}
const Qn = -90;
class Kn extends Rr {
  constructor(t2, e2, s2) {
    super(), this.type = "CubeCamera", this.renderTarget = s2, this.coordinateSystem = null, this.activeMipmapLevel = 0;
    const i2 = new $n(Qn, 1, t2, e2);
    i2.layers = this.layers, this.add(i2);
    const r2 = new $n(Qn, 1, t2, e2);
    r2.layers = this.layers, this.add(r2);
    const n2 = new $n(Qn, 1, t2, e2);
    n2.layers = this.layers, this.add(n2);
    const o = new $n(Qn, 1, t2, e2);
    o.layers = this.layers, this.add(o);
    const a = new $n(Qn, 1, t2, e2);
    a.layers = this.layers, this.add(a);
    const h2 = new $n(Qn, 1, t2, e2);
    h2.layers = this.layers, this.add(h2);
  }
  updateCoordinateSystem() {
    const t2 = this.coordinateSystem, e2 = this.children.concat(), [s2, i2, r2, n2, o, a] = e2;
    for (const t3 of e2) this.remove(t3);
    if (t2 === Os) s2.up.set(0, 1, 0), s2.lookAt(1, 0, 0), i2.up.set(0, 1, 0), i2.lookAt(-1, 0, 0), r2.up.set(0, 0, -1), r2.lookAt(0, 1, 0), n2.up.set(0, 0, 1), n2.lookAt(0, -1, 0), o.up.set(0, 1, 0), o.lookAt(0, 0, 1), a.up.set(0, 1, 0), a.lookAt(0, 0, -1);
    else {
      if (t2 !== Fs) throw new Error("THREE.CubeCamera.updateCoordinateSystem(): Invalid coordinate system: " + t2);
      s2.up.set(0, -1, 0), s2.lookAt(-1, 0, 0), i2.up.set(0, -1, 0), i2.lookAt(1, 0, 0), r2.up.set(0, 0, 1), r2.lookAt(0, 1, 0), n2.up.set(0, 0, -1), n2.lookAt(0, -1, 0), o.up.set(0, -1, 0), o.lookAt(0, 0, 1), a.up.set(0, -1, 0), a.lookAt(0, 0, -1);
    }
    for (const t3 of e2) this.add(t3), t3.updateMatrixWorld();
  }
  update(t2, e2) {
    null === this.parent && this.updateMatrixWorld();
    const { renderTarget: s2, activeMipmapLevel: i2 } = this;
    this.coordinateSystem !== t2.coordinateSystem && (this.coordinateSystem = t2.coordinateSystem, this.updateCoordinateSystem());
    const [r2, n2, o, a, h2, l2] = this.children, c2 = t2.getRenderTarget(), u2 = t2.getActiveCubeFace(), d2 = t2.getActiveMipmapLevel(), p2 = t2.xr.enabled;
    t2.xr.enabled = false;
    const m2 = s2.texture.generateMipmaps;
    s2.texture.generateMipmaps = false, t2.setRenderTarget(s2, 0, i2), t2.render(e2, r2), t2.setRenderTarget(s2, 1, i2), t2.render(e2, n2), t2.setRenderTarget(s2, 2, i2), t2.render(e2, o), t2.setRenderTarget(s2, 3, i2), t2.render(e2, a), t2.setRenderTarget(s2, 4, i2), t2.render(e2, h2), s2.texture.generateMipmaps = m2, t2.setRenderTarget(s2, 5, i2), t2.render(e2, l2), t2.setRenderTarget(c2, u2, d2), t2.xr.enabled = p2, s2.texture.needsPMREMUpdate = true;
  }
}
class to extends vi {
  constructor(t2, e2, s2, i2, r2, n2, o, a, h2, l2) {
    super(t2 = void 0 !== t2 ? t2 : [], e2 = void 0 !== e2 ? e2 : ht, s2, i2, r2, n2, o, a, h2, l2), this.isCubeTexture = true, this.flipY = false;
  }
  get images() {
    return this.image;
  }
  set images(t2) {
    this.image = t2;
  }
}
class eo extends Si {
  constructor(t2 = 1, e2 = {}) {
    super(t2, t2, e2), this.isWebGLCubeRenderTarget = true;
    const s2 = { width: t2, height: t2, depth: 1 }, i2 = [s2, s2, s2, s2, s2, s2];
    this.texture = new to(i2, e2.mapping, e2.wrapS, e2.wrapT, e2.magFilter, e2.minFilter, e2.format, e2.type, e2.anisotropy, e2.colorSpace), this.texture.isRenderTargetTexture = true, this.texture.generateMipmaps = void 0 !== e2.generateMipmaps && e2.generateMipmaps, this.texture.minFilter = void 0 !== e2.minFilter ? e2.minFilter : wt;
  }
  fromEquirectangularTexture(t2, e2) {
    this.texture.type = e2.type, this.texture.colorSpace = e2.colorSpace, this.texture.generateMipmaps = e2.generateMipmaps, this.texture.minFilter = e2.minFilter, this.texture.magFilter = e2.magFilter;
    const s2 = { uniforms: { tEquirect: { value: null } }, vertexShader: "\n\n				varying vec3 vWorldDirection;\n\n				vec3 transformDirection( in vec3 dir, in mat4 matrix ) {\n\n					return normalize( ( matrix * vec4( dir, 0.0 ) ).xyz );\n\n				}\n\n				void main() {\n\n					vWorldDirection = transformDirection( position, modelMatrix );\n\n					#include <begin_vertex>\n					#include <project_vertex>\n\n				}\n			", fragmentShader: "\n\n				uniform sampler2D tEquirect;\n\n				varying vec3 vWorldDirection;\n\n				#include <common>\n\n				void main() {\n\n					vec3 direction = normalize( vWorldDirection );\n\n					vec2 sampleUV = equirectUv( direction );\n\n					gl_FragColor = texture2D( tEquirect, sampleUV );\n\n				}\n			" }, i2 = new jn(5, 5, 5), r2 = new Jn({ name: "CubemapFromEquirect", uniforms: Un(s2.uniforms), vertexShader: s2.vertexShader, fragmentShader: s2.fragmentShader, side: 1, blending: 0 });
    r2.uniforms.tEquirect.value = e2;
    const n2 = new Vn(i2, r2), o = e2.minFilter;
    e2.minFilter === _t && (e2.minFilter = wt);
    return new Kn(1, 10, this).update(t2, n2), e2.minFilter = o, n2.geometry.dispose(), n2.material.dispose(), this;
  }
  clear(t2, e2, s2, i2) {
    const r2 = t2.getRenderTarget();
    for (let r3 = 0; r3 < 6; r3++) t2.setRenderTarget(this, r3), t2.clear(e2, s2, i2);
    t2.setRenderTarget(r2);
  }
}
class so {
  constructor(t2, e2 = 25e-5) {
    this.isFogExp2 = true, this.name = "", this.color = new $r(t2), this.density = e2;
  }
  clone() {
    return new so(this.color, this.density);
  }
  toJSON() {
    return { type: "FogExp2", name: this.name, color: this.color.getHex(), density: this.density };
  }
}
class ro extends Rr {
  constructor() {
    super(), this.isScene = true, this.type = "Scene", this.background = null, this.environment = null, this.fog = null, this.backgroundBlurriness = 0, this.backgroundIntensity = 1, this.backgroundRotation = new yr(), this.environmentIntensity = 1, this.environmentRotation = new yr(), this.overrideMaterial = null, "undefined" != typeof __THREE_DEVTOOLS__ && __THREE_DEVTOOLS__.dispatchEvent(new THREEGlobals["CustomEvent"]("observe", { detail: this }));
  }
  copy(t2, e2) {
    return super.copy(t2, e2), null !== t2.background && (this.background = t2.background.clone()), null !== t2.environment && (this.environment = t2.environment.clone()), null !== t2.fog && (this.fog = t2.fog.clone()), this.backgroundBlurriness = t2.backgroundBlurriness, this.backgroundIntensity = t2.backgroundIntensity, this.backgroundRotation.copy(t2.backgroundRotation), this.environmentIntensity = t2.environmentIntensity, this.environmentRotation.copy(t2.environmentRotation), null !== t2.overrideMaterial && (this.overrideMaterial = t2.overrideMaterial.clone()), this.matrixAutoUpdate = t2.matrixAutoUpdate, this;
  }
  toJSON(t2) {
    const e2 = super.toJSON(t2);
    return null !== this.fog && (e2.object.fog = this.fog.toJSON()), this.backgroundBlurriness > 0 && (e2.object.backgroundBlurriness = this.backgroundBlurriness), 1 !== this.backgroundIntensity && (e2.object.backgroundIntensity = this.backgroundIntensity), e2.object.backgroundRotation = this.backgroundRotation.toArray(), 1 !== this.environmentIntensity && (e2.object.environmentIntensity = this.environmentIntensity), e2.object.environmentRotation = this.environmentRotation.toArray(), e2;
  }
}
class no {
  constructor(t2, e2) {
    this.isInterleavedBuffer = true, this.array = t2, this.stride = e2, this.count = void 0 !== t2 ? t2.length / e2 : 0, this.usage = _s, this.updateRanges = [], this.version = 0, this.uuid = Us();
  }
  onUploadCallback() {
  }
  set needsUpdate(t2) {
    true === t2 && this.version++;
  }
  setUsage(t2) {
    return this.usage = t2, this;
  }
  addUpdateRange(t2, e2) {
    this.updateRanges.push({ start: t2, count: e2 });
  }
  clearUpdateRanges() {
    this.updateRanges.length = 0;
  }
  copy(t2) {
    return this.array = new t2.array.constructor(t2.array), this.count = t2.count, this.stride = t2.stride, this.usage = t2.usage, this;
  }
  copyAt(t2, e2, s2) {
    t2 *= this.stride, s2 *= e2.stride;
    for (let i2 = 0, r2 = this.stride; i2 < r2; i2++) this.array[t2 + i2] = e2.array[s2 + i2];
    return this;
  }
  set(t2, e2 = 0) {
    return this.array.set(t2, e2), this;
  }
  clone(t2) {
    void 0 === t2.arrayBuffers && (t2.arrayBuffers = {}), void 0 === this.array.buffer._uuid && (this.array.buffer._uuid = Us()), void 0 === t2.arrayBuffers[this.array.buffer._uuid] && (t2.arrayBuffers[this.array.buffer._uuid] = this.array.slice(0).buffer);
    const e2 = new this.array.constructor(t2.arrayBuffers[this.array.buffer._uuid]), s2 = new this.constructor(e2, this.stride);
    return s2.setUsage(this.usage), s2;
  }
  onUpload(t2) {
    return this.onUploadCallback = t2, this;
  }
  toJSON(t2) {
    return void 0 === t2.arrayBuffers && (t2.arrayBuffers = {}), void 0 === this.array.buffer._uuid && (this.array.buffer._uuid = Us()), void 0 === t2.arrayBuffers[this.array.buffer._uuid] && (t2.arrayBuffers[this.array.buffer._uuid] = Array.from(new Uint32Array(this.array.buffer))), { uuid: this.uuid, buffer: this.array.buffer._uuid, type: this.array.constructor.name, stride: this.stride };
  }
}
const oo = new Ii();
class ao {
  constructor(t2, e2, s2, i2 = false) {
    this.isInterleavedBufferAttribute = true, this.name = "", this.data = t2, this.itemSize = e2, this.offset = s2, this.normalized = i2;
  }
  get count() {
    return this.data.count;
  }
  get array() {
    return this.data.array;
  }
  set needsUpdate(t2) {
    this.data.needsUpdate = t2;
  }
  applyMatrix4(t2) {
    for (let e2 = 0, s2 = this.data.count; e2 < s2; e2++) oo.fromBufferAttribute(this, e2), oo.applyMatrix4(t2), this.setXYZ(e2, oo.x, oo.y, oo.z);
    return this;
  }
  applyNormalMatrix(t2) {
    for (let e2 = 0, s2 = this.count; e2 < s2; e2++) oo.fromBufferAttribute(this, e2), oo.applyNormalMatrix(t2), this.setXYZ(e2, oo.x, oo.y, oo.z);
    return this;
  }
  transformDirection(t2) {
    for (let e2 = 0, s2 = this.count; e2 < s2; e2++) oo.fromBufferAttribute(this, e2), oo.transformDirection(t2), this.setXYZ(e2, oo.x, oo.y, oo.z);
    return this;
  }
  getComponent(t2, e2) {
    let s2 = this.array[t2 * this.data.stride + this.offset + e2];
    return this.normalized && (s2 = Js(s2, this.array)), s2;
  }
  setComponent(t2, e2, s2) {
    return this.normalized && (s2 = Xs(s2, this.array)), this.data.array[t2 * this.data.stride + this.offset + e2] = s2, this;
  }
  setX(t2, e2) {
    return this.normalized && (e2 = Xs(e2, this.array)), this.data.array[t2 * this.data.stride + this.offset] = e2, this;
  }
  setY(t2, e2) {
    return this.normalized && (e2 = Xs(e2, this.array)), this.data.array[t2 * this.data.stride + this.offset + 1] = e2, this;
  }
  setZ(t2, e2) {
    return this.normalized && (e2 = Xs(e2, this.array)), this.data.array[t2 * this.data.stride + this.offset + 2] = e2, this;
  }
  setW(t2, e2) {
    return this.normalized && (e2 = Xs(e2, this.array)), this.data.array[t2 * this.data.stride + this.offset + 3] = e2, this;
  }
  getX(t2) {
    let e2 = this.data.array[t2 * this.data.stride + this.offset];
    return this.normalized && (e2 = Js(e2, this.array)), e2;
  }
  getY(t2) {
    let e2 = this.data.array[t2 * this.data.stride + this.offset + 1];
    return this.normalized && (e2 = Js(e2, this.array)), e2;
  }
  getZ(t2) {
    let e2 = this.data.array[t2 * this.data.stride + this.offset + 2];
    return this.normalized && (e2 = Js(e2, this.array)), e2;
  }
  getW(t2) {
    let e2 = this.data.array[t2 * this.data.stride + this.offset + 3];
    return this.normalized && (e2 = Js(e2, this.array)), e2;
  }
  setXY(t2, e2, s2) {
    return t2 = t2 * this.data.stride + this.offset, this.normalized && (e2 = Xs(e2, this.array), s2 = Xs(s2, this.array)), this.data.array[t2 + 0] = e2, this.data.array[t2 + 1] = s2, this;
  }
  setXYZ(t2, e2, s2, i2) {
    return t2 = t2 * this.data.stride + this.offset, this.normalized && (e2 = Xs(e2, this.array), s2 = Xs(s2, this.array), i2 = Xs(i2, this.array)), this.data.array[t2 + 0] = e2, this.data.array[t2 + 1] = s2, this.data.array[t2 + 2] = i2, this;
  }
  setXYZW(t2, e2, s2, i2, r2) {
    return t2 = t2 * this.data.stride + this.offset, this.normalized && (e2 = Xs(e2, this.array), s2 = Xs(s2, this.array), i2 = Xs(i2, this.array), r2 = Xs(r2, this.array)), this.data.array[t2 + 0] = e2, this.data.array[t2 + 1] = s2, this.data.array[t2 + 2] = i2, this.data.array[t2 + 3] = r2, this;
  }
  clone(t2) {
    if (void 0 === t2) {
      console.log("THREE.InterleavedBufferAttribute.clone(): Cloning an interleaved buffer attribute will de-interleave buffer data.");
      const t3 = [];
      for (let e2 = 0; e2 < this.count; e2++) {
        const s2 = e2 * this.data.stride + this.offset;
        for (let e3 = 0; e3 < this.itemSize; e3++) t3.push(this.data.array[s2 + e3]);
      }
      return new cn(new this.array.constructor(t3), this.itemSize, this.normalized);
    }
    return void 0 === t2.interleavedBuffers && (t2.interleavedBuffers = {}), void 0 === t2.interleavedBuffers[this.data.uuid] && (t2.interleavedBuffers[this.data.uuid] = this.data.clone(t2)), new ao(t2.interleavedBuffers[this.data.uuid], this.itemSize, this.offset, this.normalized);
  }
  toJSON(t2) {
    if (void 0 === t2) {
      console.log("THREE.InterleavedBufferAttribute.toJSON(): Serializing an interleaved buffer attribute will de-interleave buffer data.");
      const t3 = [];
      for (let e2 = 0; e2 < this.count; e2++) {
        const s2 = e2 * this.data.stride + this.offset;
        for (let e3 = 0; e3 < this.itemSize; e3++) t3.push(this.data.array[s2 + e3]);
      }
      return { itemSize: this.itemSize, type: this.array.constructor.name, array: t3, normalized: this.normalized };
    }
    return void 0 === t2.interleavedBuffers && (t2.interleavedBuffers = {}), void 0 === t2.interleavedBuffers[this.data.uuid] && (t2.interleavedBuffers[this.data.uuid] = this.data.toJSON(t2)), { isInterleavedBufferAttribute: true, itemSize: this.itemSize, data: this.data.uuid, offset: this.offset, normalized: this.normalized };
  }
}
new Ii();
new Ii();
new Ii();
new Zs();
new Zs();
new nr();
new Ii();
new Ii();
new Ii();
new Zs();
new Zs();
new Zs();
new Ii();
new Ii();
const Co = new Ii(), Io = new wi(), Bo = new wi(), ko = new Ii(), Ro = new nr(), Eo = new Ii(), Po = new Gi(), Oo = new nr(), Fo = new rr();
class No extends Vn {
  constructor(t2, e2) {
    super(t2, e2), this.isSkinnedMesh = true, this.type = "SkinnedMesh", this.bindMode = nt, this.bindMatrix = new nr(), this.bindMatrixInverse = new nr(), this.boundingBox = null, this.boundingSphere = null;
  }
  computeBoundingBox() {
    const t2 = this.geometry;
    null === this.boundingBox && (this.boundingBox = new Ri()), this.boundingBox.makeEmpty();
    const e2 = t2.getAttribute("position");
    for (let t3 = 0; t3 < e2.count; t3++) this.getVertexPosition(t3, Eo), this.boundingBox.expandByPoint(Eo);
  }
  computeBoundingSphere() {
    const t2 = this.geometry;
    null === this.boundingSphere && (this.boundingSphere = new Gi()), this.boundingSphere.makeEmpty();
    const e2 = t2.getAttribute("position");
    for (let t3 = 0; t3 < e2.count; t3++) this.getVertexPosition(t3, Eo), this.boundingSphere.expandByPoint(Eo);
  }
  copy(t2, e2) {
    return super.copy(t2, e2), this.bindMode = t2.bindMode, this.bindMatrix.copy(t2.bindMatrix), this.bindMatrixInverse.copy(t2.bindMatrixInverse), this.skeleton = t2.skeleton, null !== t2.boundingBox && (this.boundingBox = t2.boundingBox.clone()), null !== t2.boundingSphere && (this.boundingSphere = t2.boundingSphere.clone()), this;
  }
  raycast(t2, e2) {
    const s2 = this.material, i2 = this.matrixWorld;
    void 0 !== s2 && (null === this.boundingSphere && this.computeBoundingSphere(), Po.copy(this.boundingSphere), Po.applyMatrix4(i2), false !== t2.ray.intersectsSphere(Po) && (Oo.copy(i2).invert(), Fo.copy(t2.ray).applyMatrix4(Oo), null !== this.boundingBox && false === Fo.intersectsBox(this.boundingBox) || this._computeIntersections(t2, e2, Fo)));
  }
  getVertexPosition(t2, e2) {
    return super.getVertexPosition(t2, e2), this.applyBoneTransform(t2, e2), e2;
  }
  bind(t2, e2) {
    this.skeleton = t2, void 0 === e2 && (this.updateMatrixWorld(true), this.skeleton.calculateInverses(), e2 = this.matrixWorld), this.bindMatrix.copy(e2), this.bindMatrixInverse.copy(e2).invert();
  }
  pose() {
    this.skeleton.pose();
  }
  normalizeSkinWeights() {
    const t2 = new wi(), e2 = this.geometry.attributes.skinWeight;
    for (let s2 = 0, i2 = e2.count; s2 < i2; s2++) {
      t2.fromBufferAttribute(e2, s2);
      const i3 = 1 / t2.manhattanLength();
      i3 !== 1 / 0 ? t2.multiplyScalar(i3) : t2.set(1, 0, 0, 0), e2.setXYZW(s2, t2.x, t2.y, t2.z, t2.w);
    }
  }
  updateMatrixWorld(t2) {
    super.updateMatrixWorld(t2), this.bindMode === nt ? this.bindMatrixInverse.copy(this.matrixWorld).invert() : this.bindMode === ot ? this.bindMatrixInverse.copy(this.bindMatrix).invert() : console.warn("THREE.SkinnedMesh: Unrecognized bindMode: " + this.bindMode);
  }
  applyBoneTransform(t2, e2) {
    const s2 = this.skeleton, i2 = this.geometry;
    Io.fromBufferAttribute(i2.attributes.skinIndex, t2), Bo.fromBufferAttribute(i2.attributes.skinWeight, t2), Co.copy(e2).applyMatrix4(this.bindMatrix), e2.set(0, 0, 0);
    for (let t3 = 0; t3 < 4; t3++) {
      const i3 = Bo.getComponent(t3);
      if (0 !== i3) {
        const r2 = Io.getComponent(t3);
        Ro.multiplyMatrices(s2.bones[r2].matrixWorld, s2.boneInverses[r2]), e2.addScaledVector(ko.copy(Co).applyMatrix4(Ro), i3);
      }
    }
    return e2.applyMatrix4(this.bindMatrixInverse);
  }
}
class Lo extends Rr {
  constructor() {
    super(), this.isBone = true, this.type = "Bone";
  }
}
class Vo extends vi {
  constructor(t2 = null, e2 = 1, s2 = 1, i2, r2, n2, o, a, h2 = 1003, l2 = 1003, c2, u2) {
    super(null, n2, o, a, h2, l2, i2, r2, c2, u2), this.isDataTexture = true, this.image = { data: t2, width: e2, height: s2 }, this.generateMipmaps = false, this.flipY = false, this.unpackAlignment = 1;
  }
}
const Wo = new nr(), jo = new nr();
class Uo {
  constructor(t2 = [], e2 = []) {
    this.uuid = Us(), this.bones = t2.slice(0), this.boneInverses = e2, this.boneMatrices = null, this.boneTexture = null, this.init();
  }
  init() {
    const t2 = this.bones, e2 = this.boneInverses;
    if (this.boneMatrices = new Float32Array(16 * t2.length), 0 === e2.length) this.calculateInverses();
    else if (t2.length !== e2.length) {
      console.warn("THREE.Skeleton: Number of inverse bone matrices does not match amount of bones."), this.boneInverses = [];
      for (let t3 = 0, e3 = this.bones.length; t3 < e3; t3++) this.boneInverses.push(new nr());
    }
  }
  calculateInverses() {
    this.boneInverses.length = 0;
    for (let t2 = 0, e2 = this.bones.length; t2 < e2; t2++) {
      const e3 = new nr();
      this.bones[t2] && e3.copy(this.bones[t2].matrixWorld).invert(), this.boneInverses.push(e3);
    }
  }
  pose() {
    for (let t2 = 0, e2 = this.bones.length; t2 < e2; t2++) {
      const e3 = this.bones[t2];
      e3 && e3.matrixWorld.copy(this.boneInverses[t2]).invert();
    }
    for (let t2 = 0, e2 = this.bones.length; t2 < e2; t2++) {
      const e3 = this.bones[t2];
      e3 && (e3.parent && e3.parent.isBone ? (e3.matrix.copy(e3.parent.matrixWorld).invert(), e3.matrix.multiply(e3.matrixWorld)) : e3.matrix.copy(e3.matrixWorld), e3.matrix.decompose(e3.position, e3.quaternion, e3.scale));
    }
  }
  update() {
    const t2 = this.bones, e2 = this.boneInverses, s2 = this.boneMatrices, i2 = this.boneTexture;
    for (let i3 = 0, r2 = t2.length; i3 < r2; i3++) {
      const r3 = t2[i3] ? t2[i3].matrixWorld : jo;
      Wo.multiplyMatrices(r3, e2[i3]), Wo.toArray(s2, 16 * i3);
    }
    null !== i2 && (i2.needsUpdate = true);
  }
  clone() {
    return new Uo(this.bones, this.boneInverses);
  }
  computeBoneTexture() {
    let t2 = Math.sqrt(4 * this.bones.length);
    t2 = 4 * Math.ceil(t2 / 4), t2 = Math.max(t2, 4);
    const e2 = new Float32Array(t2 * t2 * 4);
    e2.set(this.boneMatrices);
    const s2 = new Vo(e2, t2, t2, Wt, Rt);
    return s2.needsUpdate = true, this.boneMatrices = e2, this.boneTexture = s2, this;
  }
  getBoneByName(t2) {
    for (let e2 = 0, s2 = this.bones.length; e2 < s2; e2++) {
      const s3 = this.bones[e2];
      if (s3.name === t2) return s3;
    }
  }
  dispose() {
    null !== this.boneTexture && (this.boneTexture.dispose(), this.boneTexture = null);
  }
  fromJSON(t2, e2) {
    this.uuid = t2.uuid;
    for (let s2 = 0, i2 = t2.bones.length; s2 < i2; s2++) {
      const i3 = t2.bones[s2];
      let r2 = e2[i3];
      void 0 === r2 && (console.warn("THREE.Skeleton: No bone found with UUID:", i3), r2 = new Lo()), this.bones.push(r2), this.boneInverses.push(new nr().fromArray(t2.boneInverses[s2]));
    }
    return this.init(), this;
  }
  toJSON() {
    const t2 = { metadata: { version: 4.6, type: "Skeleton", generator: "Skeleton.toJSON" }, bones: [], boneInverses: [] };
    t2.uuid = this.uuid;
    const e2 = this.bones, s2 = this.boneInverses;
    for (let i2 = 0, r2 = e2.length; i2 < r2; i2++) {
      const r3 = e2[i2];
      t2.bones.push(r3.uuid);
      const n2 = s2[i2];
      t2.boneInverses.push(n2.toArray());
    }
    return t2;
  }
}
class Do extends cn {
  constructor(t2, e2, s2, i2 = 1) {
    super(t2, e2, s2), this.isInstancedBufferAttribute = true, this.meshPerAttribute = i2;
  }
  copy(t2) {
    return super.copy(t2), this.meshPerAttribute = t2.meshPerAttribute, this;
  }
  toJSON() {
    const t2 = super.toJSON();
    return t2.meshPerAttribute = this.meshPerAttribute, t2.isInstancedBufferAttribute = true, t2;
  }
}
const Ho = new nr(), qo = new nr(), Jo = [], Xo = new Ri(), Yo = new nr(), Zo = new Vn(), Go = new Gi();
class $o extends Vn {
  constructor(t2, e2, s2) {
    super(t2, e2), this.isInstancedMesh = true, this.instanceMatrix = new Do(new Float32Array(16 * s2), 16), this.instanceColor = null, this.morphTexture = null, this.count = s2, this.boundingBox = null, this.boundingSphere = null;
    for (let t3 = 0; t3 < s2; t3++) this.setMatrixAt(t3, Yo);
  }
  computeBoundingBox() {
    const t2 = this.geometry, e2 = this.count;
    null === this.boundingBox && (this.boundingBox = new Ri()), null === t2.boundingBox && t2.computeBoundingBox(), this.boundingBox.makeEmpty();
    for (let s2 = 0; s2 < e2; s2++) this.getMatrixAt(s2, Ho), Xo.copy(t2.boundingBox).applyMatrix4(Ho), this.boundingBox.union(Xo);
  }
  computeBoundingSphere() {
    const t2 = this.geometry, e2 = this.count;
    null === this.boundingSphere && (this.boundingSphere = new Gi()), null === t2.boundingSphere && t2.computeBoundingSphere(), this.boundingSphere.makeEmpty();
    for (let s2 = 0; s2 < e2; s2++) this.getMatrixAt(s2, Ho), Go.copy(t2.boundingSphere).applyMatrix4(Ho), this.boundingSphere.union(Go);
  }
  copy(t2, e2) {
    return super.copy(t2, e2), this.instanceMatrix.copy(t2.instanceMatrix), null !== t2.morphTexture && (this.morphTexture = t2.morphTexture.clone()), null !== t2.instanceColor && (this.instanceColor = t2.instanceColor.clone()), this.count = t2.count, null !== t2.boundingBox && (this.boundingBox = t2.boundingBox.clone()), null !== t2.boundingSphere && (this.boundingSphere = t2.boundingSphere.clone()), this;
  }
  getColorAt(t2, e2) {
    e2.fromArray(this.instanceColor.array, 3 * t2);
  }
  getMatrixAt(t2, e2) {
    e2.fromArray(this.instanceMatrix.array, 16 * t2);
  }
  getMorphAt(t2, e2) {
    const s2 = e2.morphTargetInfluences, i2 = this.morphTexture.source.data.data, r2 = t2 * (s2.length + 1) + 1;
    for (let t3 = 0; t3 < s2.length; t3++) s2[t3] = i2[r2 + t3];
  }
  raycast(t2, e2) {
    const s2 = this.matrixWorld, i2 = this.count;
    if (Zo.geometry = this.geometry, Zo.material = this.material, void 0 !== Zo.material && (null === this.boundingSphere && this.computeBoundingSphere(), Go.copy(this.boundingSphere), Go.applyMatrix4(s2), false !== t2.ray.intersectsSphere(Go))) for (let r2 = 0; r2 < i2; r2++) {
      this.getMatrixAt(r2, Ho), qo.multiplyMatrices(s2, Ho), Zo.matrixWorld = qo, Zo.raycast(t2, Jo);
      for (let t3 = 0, s3 = Jo.length; t3 < s3; t3++) {
        const s4 = Jo[t3];
        s4.instanceId = r2, s4.object = this, e2.push(s4);
      }
      Jo.length = 0;
    }
  }
  setColorAt(t2, e2) {
    null === this.instanceColor && (this.instanceColor = new Do(new Float32Array(3 * this.instanceMatrix.count).fill(1), 3)), e2.toArray(this.instanceColor.array, 3 * t2);
  }
  setMatrixAt(t2, e2) {
    e2.toArray(this.instanceMatrix.array, 16 * t2);
  }
  setMorphAt(t2, e2) {
    const s2 = e2.morphTargetInfluences, i2 = s2.length + 1;
    null === this.morphTexture && (this.morphTexture = new Vo(new Float32Array(i2 * this.count), i2, this.count, qt, Rt));
    const r2 = this.morphTexture.source.data.data;
    let n2 = 0;
    for (let t3 = 0; t3 < s2.length; t3++) n2 += s2[t3];
    const o = this.geometry.morphTargetsRelative ? 1 : 1 - n2, a = i2 * t2;
    r2[a] = o, r2.set(s2, a + 1);
  }
  updateMorphTargets() {
  }
  dispose() {
    return this.dispatchEvent({ type: "dispose" }), null !== this.morphTexture && (this.morphTexture.dispose(), this.morphTexture = null), this;
  }
}
const Qo = new Ii(), Ko = new Ii(), ta = new Gs();
class ea {
  constructor(t2 = new Ii(1, 0, 0), e2 = 0) {
    this.isPlane = true, this.normal = t2, this.constant = e2;
  }
  set(t2, e2) {
    return this.normal.copy(t2), this.constant = e2, this;
  }
  setComponents(t2, e2, s2, i2) {
    return this.normal.set(t2, e2, s2), this.constant = i2, this;
  }
  setFromNormalAndCoplanarPoint(t2, e2) {
    return this.normal.copy(t2), this.constant = -e2.dot(this.normal), this;
  }
  setFromCoplanarPoints(t2, e2, s2) {
    const i2 = Qo.subVectors(s2, e2).cross(Ko.subVectors(t2, e2)).normalize();
    return this.setFromNormalAndCoplanarPoint(i2, t2), this;
  }
  copy(t2) {
    return this.normal.copy(t2.normal), this.constant = t2.constant, this;
  }
  normalize() {
    const t2 = 1 / this.normal.length();
    return this.normal.multiplyScalar(t2), this.constant *= t2, this;
  }
  negate() {
    return this.constant *= -1, this.normal.negate(), this;
  }
  distanceToPoint(t2) {
    return this.normal.dot(t2) + this.constant;
  }
  distanceToSphere(t2) {
    return this.distanceToPoint(t2.center) - t2.radius;
  }
  projectPoint(t2, e2) {
    return e2.copy(t2).addScaledVector(this.normal, -this.distanceToPoint(t2));
  }
  intersectLine(t2, e2) {
    const s2 = t2.delta(Qo), i2 = this.normal.dot(s2);
    if (0 === i2) return 0 === this.distanceToPoint(t2.start) ? e2.copy(t2.start) : null;
    const r2 = -(t2.start.dot(this.normal) + this.constant) / i2;
    return r2 < 0 || r2 > 1 ? null : e2.copy(t2.start).addScaledVector(s2, r2);
  }
  intersectsLine(t2) {
    const e2 = this.distanceToPoint(t2.start), s2 = this.distanceToPoint(t2.end);
    return e2 < 0 && s2 > 0 || s2 < 0 && e2 > 0;
  }
  intersectsBox(t2) {
    return t2.intersectsPlane(this);
  }
  intersectsSphere(t2) {
    return t2.intersectsPlane(this);
  }
  coplanarPoint(t2) {
    return t2.copy(this.normal).multiplyScalar(-this.constant);
  }
  applyMatrix4(t2, e2) {
    const s2 = e2 || ta.getNormalMatrix(t2), i2 = this.coplanarPoint(Qo).applyMatrix4(t2), r2 = this.normal.applyMatrix3(s2).normalize();
    return this.constant = -i2.dot(r2), this;
  }
  translate(t2) {
    return this.constant -= t2.dot(this.normal), this;
  }
  equals(t2) {
    return t2.normal.equals(this.normal) && t2.constant === this.constant;
  }
  clone() {
    return new this.constructor().copy(this);
  }
}
const sa = new Gi(), ia = new Ii();
class ra {
  constructor(t2 = new ea(), e2 = new ea(), s2 = new ea(), i2 = new ea(), r2 = new ea(), n2 = new ea()) {
    this.planes = [t2, e2, s2, i2, r2, n2];
  }
  set(t2, e2, s2, i2, r2, n2) {
    const o = this.planes;
    return o[0].copy(t2), o[1].copy(e2), o[2].copy(s2), o[3].copy(i2), o[4].copy(r2), o[5].copy(n2), this;
  }
  copy(t2) {
    const e2 = this.planes;
    for (let s2 = 0; s2 < 6; s2++) e2[s2].copy(t2.planes[s2]);
    return this;
  }
  setFromProjectionMatrix(t2, e2 = 2e3) {
    const s2 = this.planes, i2 = t2.elements, r2 = i2[0], n2 = i2[1], o = i2[2], a = i2[3], h2 = i2[4], l2 = i2[5], c2 = i2[6], u2 = i2[7], d2 = i2[8], p2 = i2[9], m2 = i2[10], y2 = i2[11], f2 = i2[12], g2 = i2[13], x2 = i2[14], b2 = i2[15];
    if (s2[0].setComponents(a - r2, u2 - h2, y2 - d2, b2 - f2).normalize(), s2[1].setComponents(a + r2, u2 + h2, y2 + d2, b2 + f2).normalize(), s2[2].setComponents(a + n2, u2 + l2, y2 + p2, b2 + g2).normalize(), s2[3].setComponents(a - n2, u2 - l2, y2 - p2, b2 - g2).normalize(), s2[4].setComponents(a - o, u2 - c2, y2 - m2, b2 - x2).normalize(), e2 === Os) s2[5].setComponents(a + o, u2 + c2, y2 + m2, b2 + x2).normalize();
    else {
      if (e2 !== Fs) throw new Error("THREE.Frustum.setFromProjectionMatrix(): Invalid coordinate system: " + e2);
      s2[5].setComponents(o, c2, m2, x2).normalize();
    }
    return this;
  }
  intersectsObject(t2) {
    if (void 0 !== t2.boundingSphere) null === t2.boundingSphere && t2.computeBoundingSphere(), sa.copy(t2.boundingSphere).applyMatrix4(t2.matrixWorld);
    else {
      const e2 = t2.geometry;
      null === e2.boundingSphere && e2.computeBoundingSphere(), sa.copy(e2.boundingSphere).applyMatrix4(t2.matrixWorld);
    }
    return this.intersectsSphere(sa);
  }
  intersectsSprite(t2) {
    return sa.center.set(0, 0, 0), sa.radius = 0.*********1865476, sa.applyMatrix4(t2.matrixWorld), this.intersectsSphere(sa);
  }
  intersectsSphere(t2) {
    const e2 = this.planes, s2 = t2.center, i2 = -t2.radius;
    for (let t3 = 0; t3 < 6; t3++) {
      if (e2[t3].distanceToPoint(s2) < i2) return false;
    }
    return true;
  }
  intersectsBox(t2) {
    const e2 = this.planes;
    for (let s2 = 0; s2 < 6; s2++) {
      const i2 = e2[s2];
      if (ia.x = i2.normal.x > 0 ? t2.max.x : t2.min.x, ia.y = i2.normal.y > 0 ? t2.max.y : t2.min.y, ia.z = i2.normal.z > 0 ? t2.max.z : t2.min.z, i2.distanceToPoint(ia) < 0) return false;
    }
    return true;
  }
  containsPoint(t2) {
    const e2 = this.planes;
    for (let s2 = 0; s2 < 6; s2++) if (e2[s2].distanceToPoint(t2) < 0) return false;
    return true;
  }
  clone() {
    return new this.constructor().copy(this);
  }
}
new nr();
new $r(1, 1, 1);
new ra();
new Ri();
new Gi();
new Ii();
new Ii();
new Ii();
new Vn();
class Sa extends tn {
  constructor(t2) {
    super(), this.isLineBasicMaterial = true, this.type = "LineBasicMaterial", this.color = new $r(16777215), this.map = null, this.linewidth = 1, this.linecap = "round", this.linejoin = "round", this.fog = true, this.setValues(t2);
  }
  copy(t2) {
    return super.copy(t2), this.color.copy(t2.color), this.map = t2.map, this.linewidth = t2.linewidth, this.linecap = t2.linecap, this.linejoin = t2.linejoin, this.fog = t2.fog, this;
  }
}
const _a = new Ii(), Aa = new Ii(), Ta = new nr(), za = new rr(), Ca = new Gi(), Ia = new Ii(), Ba = new Ii();
class ka extends Rr {
  constructor(t2 = new zn(), e2 = new Sa()) {
    super(), this.isLine = true, this.type = "Line", this.geometry = t2, this.material = e2, this.updateMorphTargets();
  }
  copy(t2, e2) {
    return super.copy(t2, e2), this.material = Array.isArray(t2.material) ? t2.material.slice() : t2.material, this.geometry = t2.geometry, this;
  }
  computeLineDistances() {
    const t2 = this.geometry;
    if (null === t2.index) {
      const e2 = t2.attributes.position, s2 = [0];
      for (let t3 = 1, i2 = e2.count; t3 < i2; t3++) _a.fromBufferAttribute(e2, t3 - 1), Aa.fromBufferAttribute(e2, t3), s2[t3] = s2[t3 - 1], s2[t3] += _a.distanceTo(Aa);
      t2.setAttribute("lineDistance", new bn(s2, 1));
    } else console.warn("THREE.Line.computeLineDistances(): Computation only possible with non-indexed BufferGeometry.");
    return this;
  }
  raycast(t2, e2) {
    const s2 = this.geometry, i2 = this.matrixWorld, r2 = t2.params.Line.threshold, n2 = s2.drawRange;
    if (null === s2.boundingSphere && s2.computeBoundingSphere(), Ca.copy(s2.boundingSphere), Ca.applyMatrix4(i2), Ca.radius += r2, false === t2.ray.intersectsSphere(Ca)) return;
    Ta.copy(i2).invert(), za.copy(t2.ray).applyMatrix4(Ta);
    const o = r2 / ((this.scale.x + this.scale.y + this.scale.z) / 3), a = o * o, h2 = this.isLineSegments ? 2 : 1, l2 = s2.index, c2 = s2.attributes.position;
    if (null !== l2) {
      const s3 = Math.max(0, n2.start), i3 = Math.min(l2.count, n2.start + n2.count);
      for (let r3 = s3, n3 = i3 - 1; r3 < n3; r3 += h2) {
        const s4 = l2.getX(r3), i4 = l2.getX(r3 + 1), n4 = Ra(this, t2, za, a, s4, i4);
        n4 && e2.push(n4);
      }
      if (this.isLineLoop) {
        const r3 = l2.getX(i3 - 1), n3 = l2.getX(s3), o2 = Ra(this, t2, za, a, r3, n3);
        o2 && e2.push(o2);
      }
    } else {
      const s3 = Math.max(0, n2.start), i3 = Math.min(c2.count, n2.start + n2.count);
      for (let r3 = s3, n3 = i3 - 1; r3 < n3; r3 += h2) {
        const s4 = Ra(this, t2, za, a, r3, r3 + 1);
        s4 && e2.push(s4);
      }
      if (this.isLineLoop) {
        const r3 = Ra(this, t2, za, a, i3 - 1, s3);
        r3 && e2.push(r3);
      }
    }
  }
  updateMorphTargets() {
    const t2 = this.geometry.morphAttributes, e2 = Object.keys(t2);
    if (e2.length > 0) {
      const s2 = t2[e2[0]];
      if (void 0 !== s2) {
        this.morphTargetInfluences = [], this.morphTargetDictionary = {};
        for (let t3 = 0, e3 = s2.length; t3 < e3; t3++) {
          const e4 = s2[t3].name || String(t3);
          this.morphTargetInfluences.push(0), this.morphTargetDictionary[e4] = t3;
        }
      }
    }
  }
}
function Ra(t2, e2, s2, i2, r2, n2) {
  const o = t2.geometry.attributes.position;
  _a.fromBufferAttribute(o, r2), Aa.fromBufferAttribute(o, n2);
  if (s2.distanceSqToSegment(_a, Aa, Ia, Ba) > i2) return;
  Ia.applyMatrix4(t2.matrixWorld);
  const a = e2.ray.origin.distanceTo(Ia);
  return a < e2.near || a > e2.far ? void 0 : { distance: a, point: Ba.clone().applyMatrix4(t2.matrixWorld), index: r2, face: null, faceIndex: null, barycoord: null, object: t2 };
}
const Ea = new Ii(), Pa = new Ii();
class Oa extends ka {
  constructor(t2, e2) {
    super(t2, e2), this.isLineSegments = true, this.type = "LineSegments";
  }
  computeLineDistances() {
    const t2 = this.geometry;
    if (null === t2.index) {
      const e2 = t2.attributes.position, s2 = [];
      for (let t3 = 0, i2 = e2.count; t3 < i2; t3 += 2) Ea.fromBufferAttribute(e2, t3), Pa.fromBufferAttribute(e2, t3 + 1), s2[t3] = 0 === t3 ? 0 : s2[t3 - 1], s2[t3 + 1] = s2[t3] + Ea.distanceTo(Pa);
      t2.setAttribute("lineDistance", new bn(s2, 1));
    } else console.warn("THREE.LineSegments.computeLineDistances(): Computation only possible with non-indexed BufferGeometry.");
    return this;
  }
}
class Fa extends ka {
  constructor(t2, e2) {
    super(t2, e2), this.isLineLoop = true, this.type = "LineLoop";
  }
}
class Na extends tn {
  constructor(t2) {
    super(), this.isPointsMaterial = true, this.type = "PointsMaterial", this.color = new $r(16777215), this.map = null, this.alphaMap = null, this.size = 1, this.sizeAttenuation = true, this.fog = true, this.setValues(t2);
  }
  copy(t2) {
    return super.copy(t2), this.color.copy(t2.color), this.map = t2.map, this.alphaMap = t2.alphaMap, this.size = t2.size, this.sizeAttenuation = t2.sizeAttenuation, this.fog = t2.fog, this;
  }
}
const La = new nr(), Va = new rr(), Wa = new Gi(), ja = new Ii();
class Ua extends Rr {
  constructor(t2 = new zn(), e2 = new Na()) {
    super(), this.isPoints = true, this.type = "Points", this.geometry = t2, this.material = e2, this.updateMorphTargets();
  }
  copy(t2, e2) {
    return super.copy(t2, e2), this.material = Array.isArray(t2.material) ? t2.material.slice() : t2.material, this.geometry = t2.geometry, this;
  }
  raycast(t2, e2) {
    const s2 = this.geometry, i2 = this.matrixWorld, r2 = t2.params.Points.threshold, n2 = s2.drawRange;
    if (null === s2.boundingSphere && s2.computeBoundingSphere(), Wa.copy(s2.boundingSphere), Wa.applyMatrix4(i2), Wa.radius += r2, false === t2.ray.intersectsSphere(Wa)) return;
    La.copy(i2).invert(), Va.copy(t2.ray).applyMatrix4(La);
    const o = r2 / ((this.scale.x + this.scale.y + this.scale.z) / 3), a = o * o, h2 = s2.index, l2 = s2.attributes.position;
    if (null !== h2) {
      for (let s3 = Math.max(0, n2.start), r3 = Math.min(h2.count, n2.start + n2.count); s3 < r3; s3++) {
        const r4 = h2.getX(s3);
        ja.fromBufferAttribute(l2, r4), Da(ja, r4, a, i2, t2, e2, this);
      }
    } else {
      for (let s3 = Math.max(0, n2.start), r3 = Math.min(l2.count, n2.start + n2.count); s3 < r3; s3++) ja.fromBufferAttribute(l2, s3), Da(ja, s3, a, i2, t2, e2, this);
    }
  }
  updateMorphTargets() {
    const t2 = this.geometry.morphAttributes, e2 = Object.keys(t2);
    if (e2.length > 0) {
      const s2 = t2[e2[0]];
      if (void 0 !== s2) {
        this.morphTargetInfluences = [], this.morphTargetDictionary = {};
        for (let t3 = 0, e3 = s2.length; t3 < e3; t3++) {
          const e4 = s2[t3].name || String(t3);
          this.morphTargetInfluences.push(0), this.morphTargetDictionary[e4] = t3;
        }
      }
    }
  }
}
function Da(t2, e2, s2, i2, r2, n2, o) {
  const a = Va.distanceSqToPoint(t2);
  if (a < s2) {
    const s3 = new Ii();
    Va.closestPointToPoint(t2, s3), s3.applyMatrix4(i2);
    const h2 = r2.ray.origin.distanceTo(s3);
    if (h2 < r2.near || h2 > r2.far) return;
    n2.push({ distance: h2, distanceToRay: Math.sqrt(a), point: s3, index: e2, face: null, faceIndex: null, barycoord: null, object: o });
  }
}
class Ha extends Rr {
  constructor() {
    super(), this.isGroup = true, this.type = "Group";
  }
}
class Xa extends vi {
  constructor(t2, e2, s2, i2, r2, n2, o, a, h2, l2, c2, u2) {
    super(null, n2, o, a, h2, l2, i2, r2, c2, u2), this.isCompressedTexture = true, this.image = { width: e2, height: s2 }, this.mipmaps = t2, this.flipY = false, this.generateMipmaps = false;
  }
}
class Ya extends Xa {
  constructor(t2, e2, s2, i2, r2, n2) {
    super(t2, e2, s2, r2, n2), this.isCompressedArrayTexture = true, this.image.depth = i2, this.wrapR = mt, this.layerUpdates = /* @__PURE__ */ new Set();
  }
  addLayerUpdate(t2) {
    this.layerUpdates.add(t2);
  }
  clearLayerUpdates() {
    this.layerUpdates.clear();
  }
}
class Za extends Xa {
  constructor(t2, e2, s2) {
    super(void 0, t2[0].width, t2[0].height, e2, s2, ht), this.isCompressedCubeTexture = true, this.isCubeTexture = true, this.image = t2;
  }
}
class $a extends vi {
  constructor(t2, e2, s2, i2, r2, n2, o, a, h2, l2 = 1026) {
    if (l2 !== Dt && l2 !== Ht) throw new Error("DepthTexture format must be either THREE.DepthFormat or THREE.DepthStencilFormat");
    void 0 === s2 && l2 === Dt && (s2 = kt), void 0 === s2 && l2 === Ht && (s2 = 1020), super(null, i2, r2, n2, o, a, l2, s2, h2), this.isDepthTexture = true, this.image = { width: t2, height: e2 }, this.magFilter = void 0 !== o ? o : ft, this.minFilter = void 0 !== a ? a : ft, this.flipY = false, this.generateMipmaps = false, this.compareFunction = null;
  }
  copy(t2) {
    return super.copy(t2), this.compareFunction = t2.compareFunction, this;
  }
  toJSON(t2) {
    const e2 = super.toJSON(t2);
    return null !== this.compareFunction && (e2.compareFunction = this.compareFunction), e2;
  }
}
new Ii();
new Ii();
new Ii();
new Ii();
new Jr();
class ul extends zn {
  constructor(t2 = 1, e2 = 1, s2 = 1, i2 = 1) {
    super(), this.type = "PlaneGeometry", this.parameters = { width: t2, height: e2, widthSegments: s2, heightSegments: i2 };
    const r2 = t2 / 2, n2 = e2 / 2, o = Math.floor(s2), a = Math.floor(i2), h2 = o + 1, l2 = a + 1, c2 = t2 / o, u2 = e2 / a, d2 = [], p2 = [], m2 = [], y2 = [];
    for (let t3 = 0; t3 < l2; t3++) {
      const e3 = t3 * u2 - n2;
      for (let s3 = 0; s3 < h2; s3++) {
        const i3 = s3 * c2 - r2;
        p2.push(i3, -e3, 0), m2.push(0, 0, 1), y2.push(s3 / o), y2.push(1 - t3 / a);
      }
    }
    for (let t3 = 0; t3 < a; t3++) for (let e3 = 0; e3 < o; e3++) {
      const s3 = e3 + h2 * t3, i3 = e3 + h2 * (t3 + 1), r3 = e3 + 1 + h2 * (t3 + 1), n3 = e3 + 1 + h2 * t3;
      d2.push(s3, i3, n3), d2.push(i3, r3, n3);
    }
    this.setIndex(d2), this.setAttribute("position", new bn(p2, 3)), this.setAttribute("normal", new bn(m2, 3)), this.setAttribute("uv", new bn(y2, 2));
  }
  copy(t2) {
    return super.copy(t2), this.parameters = Object.assign({}, t2.parameters), this;
  }
  static fromJSON(t2) {
    return new ul(t2.width, t2.height, t2.widthSegments, t2.heightSegments);
  }
}
class Sl extends Jn {
  constructor(t2) {
    super(t2), this.isRawShaderMaterial = true, this.type = "RawShaderMaterial";
  }
}
class _l extends tn {
  constructor(t2) {
    super(), this.isMeshStandardMaterial = true, this.type = "MeshStandardMaterial", this.defines = { STANDARD: "" }, this.color = new $r(16777215), this.roughness = 1, this.metalness = 0, this.map = null, this.lightMap = null, this.lightMapIntensity = 1, this.aoMap = null, this.aoMapIntensity = 1, this.emissive = new $r(0), this.emissiveIntensity = 1, this.emissiveMap = null, this.bumpMap = null, this.bumpScale = 1, this.normalMap = null, this.normalMapType = 0, this.normalScale = new Zs(1, 1), this.displacementMap = null, this.displacementScale = 1, this.displacementBias = 0, this.roughnessMap = null, this.metalnessMap = null, this.alphaMap = null, this.envMap = null, this.envMapRotation = new yr(), this.envMapIntensity = 1, this.wireframe = false, this.wireframeLinewidth = 1, this.wireframeLinecap = "round", this.wireframeLinejoin = "round", this.flatShading = false, this.fog = true, this.setValues(t2);
  }
  copy(t2) {
    return super.copy(t2), this.defines = { STANDARD: "" }, this.color.copy(t2.color), this.roughness = t2.roughness, this.metalness = t2.metalness, this.map = t2.map, this.lightMap = t2.lightMap, this.lightMapIntensity = t2.lightMapIntensity, this.aoMap = t2.aoMap, this.aoMapIntensity = t2.aoMapIntensity, this.emissive.copy(t2.emissive), this.emissiveMap = t2.emissiveMap, this.emissiveIntensity = t2.emissiveIntensity, this.bumpMap = t2.bumpMap, this.bumpScale = t2.bumpScale, this.normalMap = t2.normalMap, this.normalMapType = t2.normalMapType, this.normalScale.copy(t2.normalScale), this.displacementMap = t2.displacementMap, this.displacementScale = t2.displacementScale, this.displacementBias = t2.displacementBias, this.roughnessMap = t2.roughnessMap, this.metalnessMap = t2.metalnessMap, this.alphaMap = t2.alphaMap, this.envMap = t2.envMap, this.envMapRotation.copy(t2.envMapRotation), this.envMapIntensity = t2.envMapIntensity, this.wireframe = t2.wireframe, this.wireframeLinewidth = t2.wireframeLinewidth, this.wireframeLinecap = t2.wireframeLinecap, this.wireframeLinejoin = t2.wireframeLinejoin, this.flatShading = t2.flatShading, this.fog = t2.fog, this;
  }
}
class Al extends _l {
  constructor(t2) {
    super(), this.isMeshPhysicalMaterial = true, this.defines = { STANDARD: "", PHYSICAL: "" }, this.type = "MeshPhysicalMaterial", this.anisotropyRotation = 0, this.anisotropyMap = null, this.clearcoatMap = null, this.clearcoatRoughness = 0, this.clearcoatRoughnessMap = null, this.clearcoatNormalScale = new Zs(1, 1), this.clearcoatNormalMap = null, this.ior = 1.5, Object.defineProperty(this, "reflectivity", { get: function() {
      return Ds(2.5 * (this.ior - 1) / (this.ior + 1), 0, 1);
    }, set: function(t3) {
      this.ior = (1 + 0.4 * t3) / (1 - 0.4 * t3);
    } }), this.iridescenceMap = null, this.iridescenceIOR = 1.3, this.iridescenceThicknessRange = [100, 400], this.iridescenceThicknessMap = null, this.sheenColor = new $r(0), this.sheenColorMap = null, this.sheenRoughness = 1, this.sheenRoughnessMap = null, this.transmissionMap = null, this.thickness = 0, this.thicknessMap = null, this.attenuationDistance = 1 / 0, this.attenuationColor = new $r(1, 1, 1), this.specularIntensity = 1, this.specularIntensityMap = null, this.specularColor = new $r(1, 1, 1), this.specularColorMap = null, this._anisotropy = 0, this._clearcoat = 0, this._dispersion = 0, this._iridescence = 0, this._sheen = 0, this._transmission = 0, this.setValues(t2);
  }
  get anisotropy() {
    return this._anisotropy;
  }
  set anisotropy(t2) {
    this._anisotropy > 0 != t2 > 0 && this.version++, this._anisotropy = t2;
  }
  get clearcoat() {
    return this._clearcoat;
  }
  set clearcoat(t2) {
    this._clearcoat > 0 != t2 > 0 && this.version++, this._clearcoat = t2;
  }
  get iridescence() {
    return this._iridescence;
  }
  set iridescence(t2) {
    this._iridescence > 0 != t2 > 0 && this.version++, this._iridescence = t2;
  }
  get dispersion() {
    return this._dispersion;
  }
  set dispersion(t2) {
    this._dispersion > 0 != t2 > 0 && this.version++, this._dispersion = t2;
  }
  get sheen() {
    return this._sheen;
  }
  set sheen(t2) {
    this._sheen > 0 != t2 > 0 && this.version++, this._sheen = t2;
  }
  get transmission() {
    return this._transmission;
  }
  set transmission(t2) {
    this._transmission > 0 != t2 > 0 && this.version++, this._transmission = t2;
  }
  copy(t2) {
    return super.copy(t2), this.defines = { STANDARD: "", PHYSICAL: "" }, this.anisotropy = t2.anisotropy, this.anisotropyRotation = t2.anisotropyRotation, this.anisotropyMap = t2.anisotropyMap, this.clearcoat = t2.clearcoat, this.clearcoatMap = t2.clearcoatMap, this.clearcoatRoughness = t2.clearcoatRoughness, this.clearcoatRoughnessMap = t2.clearcoatRoughnessMap, this.clearcoatNormalMap = t2.clearcoatNormalMap, this.clearcoatNormalScale.copy(t2.clearcoatNormalScale), this.dispersion = t2.dispersion, this.ior = t2.ior, this.iridescence = t2.iridescence, this.iridescenceMap = t2.iridescenceMap, this.iridescenceIOR = t2.iridescenceIOR, this.iridescenceThicknessRange = [...t2.iridescenceThicknessRange], this.iridescenceThicknessMap = t2.iridescenceThicknessMap, this.sheen = t2.sheen, this.sheenColor.copy(t2.sheenColor), this.sheenColorMap = t2.sheenColorMap, this.sheenRoughness = t2.sheenRoughness, this.sheenRoughnessMap = t2.sheenRoughnessMap, this.transmission = t2.transmission, this.transmissionMap = t2.transmissionMap, this.thickness = t2.thickness, this.thicknessMap = t2.thicknessMap, this.attenuationDistance = t2.attenuationDistance, this.attenuationColor.copy(t2.attenuationColor), this.specularIntensity = t2.specularIntensity, this.specularIntensityMap = t2.specularIntensityMap, this.specularColor.copy(t2.specularColor), this.specularColorMap = t2.specularColorMap, this;
  }
}
class Bl extends tn {
  constructor(t2) {
    super(), this.isMeshDepthMaterial = true, this.type = "MeshDepthMaterial", this.depthPacking = 3200, this.map = null, this.alphaMap = null, this.displacementMap = null, this.displacementScale = 1, this.displacementBias = 0, this.wireframe = false, this.wireframeLinewidth = 1, this.setValues(t2);
  }
  copy(t2) {
    return super.copy(t2), this.depthPacking = t2.depthPacking, this.map = t2.map, this.alphaMap = t2.alphaMap, this.displacementMap = t2.displacementMap, this.displacementScale = t2.displacementScale, this.displacementBias = t2.displacementBias, this.wireframe = t2.wireframe, this.wireframeLinewidth = t2.wireframeLinewidth, this;
  }
}
class kl extends tn {
  constructor(t2) {
    super(), this.isMeshDistanceMaterial = true, this.type = "MeshDistanceMaterial", this.map = null, this.alphaMap = null, this.displacementMap = null, this.displacementScale = 1, this.displacementBias = 0, this.setValues(t2);
  }
  copy(t2) {
    return super.copy(t2), this.map = t2.map, this.alphaMap = t2.alphaMap, this.displacementMap = t2.displacementMap, this.displacementScale = t2.displacementScale, this.displacementBias = t2.displacementBias, this;
  }
}
function Pl(t2, e2, s2) {
  return !t2 || !s2 && t2.constructor === e2 ? t2 : "number" == typeof e2.BYTES_PER_ELEMENT ? new e2(t2) : Array.prototype.slice.call(t2);
}
function Ol(t2) {
  return ArrayBuffer.isView(t2) && !(t2 instanceof DataView);
}
function Fl(t2) {
  const e2 = t2.length, s2 = new Array(e2);
  for (let t3 = 0; t3 !== e2; ++t3) s2[t3] = t3;
  return s2.sort(function(e3, s3) {
    return t2[e3] - t2[s3];
  }), s2;
}
function Nl(t2, e2, s2) {
  const i2 = t2.length, r2 = new t2.constructor(i2);
  for (let n2 = 0, o = 0; o !== i2; ++n2) {
    const i3 = s2[n2] * e2;
    for (let s3 = 0; s3 !== e2; ++s3) r2[o++] = t2[i3 + s3];
  }
  return r2;
}
function Ll(t2, e2, s2, i2) {
  let r2 = 1, n2 = t2[0];
  for (; void 0 !== n2 && void 0 === n2[i2]; ) n2 = t2[r2++];
  if (void 0 === n2) return;
  let o = n2[i2];
  if (void 0 !== o) if (Array.isArray(o)) do {
    o = n2[i2], void 0 !== o && (e2.push(n2.time), s2.push.apply(s2, o)), n2 = t2[r2++];
  } while (void 0 !== n2);
  else if (void 0 !== o.toArray) do {
    o = n2[i2], void 0 !== o && (e2.push(n2.time), o.toArray(s2, s2.length)), n2 = t2[r2++];
  } while (void 0 !== n2);
  else do {
    o = n2[i2], void 0 !== o && (e2.push(n2.time), s2.push(o)), n2 = t2[r2++];
  } while (void 0 !== n2);
}
class Wl {
  constructor(t2, e2, s2, i2) {
    this.parameterPositions = t2, this._cachedIndex = 0, this.resultBuffer = void 0 !== i2 ? i2 : new e2.constructor(s2), this.sampleValues = e2, this.valueSize = s2, this.settings = null, this.DefaultSettings_ = {};
  }
  evaluate(t2) {
    const e2 = this.parameterPositions;
    let s2 = this._cachedIndex, i2 = e2[s2], r2 = e2[s2 - 1];
    t: {
      e: {
        let n2;
        s: {
          i: if (!(t2 < i2)) {
            for (let n3 = s2 + 2; ; ) {
              if (void 0 === i2) {
                if (t2 < r2) break i;
                return s2 = e2.length, this._cachedIndex = s2, this.copySampleValue_(s2 - 1);
              }
              if (s2 === n3) break;
              if (r2 = i2, i2 = e2[++s2], t2 < i2) break e;
            }
            n2 = e2.length;
            break s;
          }
          if (t2 >= r2) break t;
          {
            const o = e2[1];
            t2 < o && (s2 = 2, r2 = o);
            for (let n3 = s2 - 2; ; ) {
              if (void 0 === r2) return this._cachedIndex = 0, this.copySampleValue_(0);
              if (s2 === n3) break;
              if (i2 = r2, r2 = e2[--s2 - 1], t2 >= r2) break e;
            }
            n2 = s2, s2 = 0;
          }
        }
        for (; s2 < n2; ) {
          const i3 = s2 + n2 >>> 1;
          t2 < e2[i3] ? n2 = i3 : s2 = i3 + 1;
        }
        if (i2 = e2[s2], r2 = e2[s2 - 1], void 0 === r2) return this._cachedIndex = 0, this.copySampleValue_(0);
        if (void 0 === i2) return s2 = e2.length, this._cachedIndex = s2, this.copySampleValue_(s2 - 1);
      }
      this._cachedIndex = s2, this.intervalChanged_(s2, r2, i2);
    }
    return this.interpolate_(s2, r2, t2, i2);
  }
  getSettings_() {
    return this.settings || this.DefaultSettings_;
  }
  copySampleValue_(t2) {
    const e2 = this.resultBuffer, s2 = this.sampleValues, i2 = this.valueSize, r2 = t2 * i2;
    for (let t3 = 0; t3 !== i2; ++t3) e2[t3] = s2[r2 + t3];
    return e2;
  }
  interpolate_() {
    throw new Error("call to abstract method");
  }
  intervalChanged_() {
  }
}
class jl extends Wl {
  constructor(t2, e2, s2, i2) {
    super(t2, e2, s2, i2), this._weightPrev = -0, this._offsetPrev = -0, this._weightNext = -0, this._offsetNext = -0, this.DefaultSettings_ = { endingStart: Oe, endingEnd: Oe };
  }
  intervalChanged_(t2, e2, s2) {
    const i2 = this.parameterPositions;
    let r2 = t2 - 2, n2 = t2 + 1, o = i2[r2], a = i2[n2];
    if (void 0 === o) switch (this.getSettings_().endingStart) {
      case Fe:
        r2 = t2, o = 2 * e2 - s2;
        break;
      case Ne:
        r2 = i2.length - 2, o = e2 + i2[r2] - i2[r2 + 1];
        break;
      default:
        r2 = t2, o = s2;
    }
    if (void 0 === a) switch (this.getSettings_().endingEnd) {
      case Fe:
        n2 = t2, a = 2 * s2 - e2;
        break;
      case Ne:
        n2 = 1, a = s2 + i2[1] - i2[0];
        break;
      default:
        n2 = t2 - 1, a = e2;
    }
    const h2 = 0.5 * (s2 - e2), l2 = this.valueSize;
    this._weightPrev = h2 / (e2 - o), this._weightNext = h2 / (a - s2), this._offsetPrev = r2 * l2, this._offsetNext = n2 * l2;
  }
  interpolate_(t2, e2, s2, i2) {
    const r2 = this.resultBuffer, n2 = this.sampleValues, o = this.valueSize, a = t2 * o, h2 = a - o, l2 = this._offsetPrev, c2 = this._offsetNext, u2 = this._weightPrev, d2 = this._weightNext, p2 = (s2 - e2) / (i2 - e2), m2 = p2 * p2, y2 = m2 * p2, f2 = -u2 * y2 + 2 * u2 * m2 - u2 * p2, g2 = (1 + u2) * y2 + (-1.5 - 2 * u2) * m2 + (-0.5 + u2) * p2 + 1, x2 = (-1 - d2) * y2 + (1.5 + d2) * m2 + 0.5 * p2, b2 = d2 * y2 - d2 * m2;
    for (let t3 = 0; t3 !== o; ++t3) r2[t3] = f2 * n2[l2 + t3] + g2 * n2[h2 + t3] + x2 * n2[a + t3] + b2 * n2[c2 + t3];
    return r2;
  }
}
class Ul extends Wl {
  constructor(t2, e2, s2, i2) {
    super(t2, e2, s2, i2);
  }
  interpolate_(t2, e2, s2, i2) {
    const r2 = this.resultBuffer, n2 = this.sampleValues, o = this.valueSize, a = t2 * o, h2 = a - o, l2 = (s2 - e2) / (i2 - e2), c2 = 1 - l2;
    for (let t3 = 0; t3 !== o; ++t3) r2[t3] = n2[h2 + t3] * c2 + n2[a + t3] * l2;
    return r2;
  }
}
class Dl extends Wl {
  constructor(t2, e2, s2, i2) {
    super(t2, e2, s2, i2);
  }
  interpolate_(t2) {
    return this.copySampleValue_(t2 - 1);
  }
}
class Hl {
  constructor(t2, e2, s2, i2) {
    if (void 0 === t2) throw new Error("THREE.KeyframeTrack: track name is undefined");
    if (void 0 === e2 || 0 === e2.length) throw new Error("THREE.KeyframeTrack: no keyframes in track named " + t2);
    this.name = t2, this.times = Pl(e2, this.TimeBufferType), this.values = Pl(s2, this.ValueBufferType), this.setInterpolation(i2 || this.DefaultInterpolation);
  }
  static toJSON(t2) {
    const e2 = t2.constructor;
    let s2;
    if (e2.toJSON !== this.toJSON) s2 = e2.toJSON(t2);
    else {
      s2 = { name: t2.name, times: Pl(t2.times, Array), values: Pl(t2.values, Array) };
      const e3 = t2.getInterpolation();
      e3 !== t2.DefaultInterpolation && (s2.interpolation = e3);
    }
    return s2.type = t2.ValueTypeName, s2;
  }
  InterpolantFactoryMethodDiscrete(t2) {
    return new Dl(this.times, this.values, this.getValueSize(), t2);
  }
  InterpolantFactoryMethodLinear(t2) {
    return new Ul(this.times, this.values, this.getValueSize(), t2);
  }
  InterpolantFactoryMethodSmooth(t2) {
    return new jl(this.times, this.values, this.getValueSize(), t2);
  }
  setInterpolation(t2) {
    let e2;
    switch (t2) {
      case Re:
        e2 = this.InterpolantFactoryMethodDiscrete;
        break;
      case Ee:
        e2 = this.InterpolantFactoryMethodLinear;
        break;
      case Pe:
        e2 = this.InterpolantFactoryMethodSmooth;
    }
    if (void 0 === e2) {
      const e3 = "unsupported interpolation for " + this.ValueTypeName + " keyframe track named " + this.name;
      if (void 0 === this.createInterpolant) {
        if (t2 === this.DefaultInterpolation) throw new Error(e3);
        this.setInterpolation(this.DefaultInterpolation);
      }
      return console.warn("THREE.KeyframeTrack:", e3), this;
    }
    return this.createInterpolant = e2, this;
  }
  getInterpolation() {
    switch (this.createInterpolant) {
      case this.InterpolantFactoryMethodDiscrete:
        return Re;
      case this.InterpolantFactoryMethodLinear:
        return Ee;
      case this.InterpolantFactoryMethodSmooth:
        return Pe;
    }
  }
  getValueSize() {
    return this.values.length / this.times.length;
  }
  shift(t2) {
    if (0 !== t2) {
      const e2 = this.times;
      for (let s2 = 0, i2 = e2.length; s2 !== i2; ++s2) e2[s2] += t2;
    }
    return this;
  }
  scale(t2) {
    if (1 !== t2) {
      const e2 = this.times;
      for (let s2 = 0, i2 = e2.length; s2 !== i2; ++s2) e2[s2] *= t2;
    }
    return this;
  }
  trim(t2, e2) {
    const s2 = this.times, i2 = s2.length;
    let r2 = 0, n2 = i2 - 1;
    for (; r2 !== i2 && s2[r2] < t2; ) ++r2;
    for (; -1 !== n2 && s2[n2] > e2; ) --n2;
    if (++n2, 0 !== r2 || n2 !== i2) {
      r2 >= n2 && (n2 = Math.max(n2, 1), r2 = n2 - 1);
      const t3 = this.getValueSize();
      this.times = s2.slice(r2, n2), this.values = this.values.slice(r2 * t3, n2 * t3);
    }
    return this;
  }
  validate() {
    let t2 = true;
    const e2 = this.getValueSize();
    e2 - Math.floor(e2) != 0 && (console.error("THREE.KeyframeTrack: Invalid value size in track.", this), t2 = false);
    const s2 = this.times, i2 = this.values, r2 = s2.length;
    0 === r2 && (console.error("THREE.KeyframeTrack: Track is empty.", this), t2 = false);
    let n2 = null;
    for (let e3 = 0; e3 !== r2; e3++) {
      const i3 = s2[e3];
      if ("number" == typeof i3 && isNaN(i3)) {
        console.error("THREE.KeyframeTrack: Time is not a valid number.", this, e3, i3), t2 = false;
        break;
      }
      if (null !== n2 && n2 > i3) {
        console.error("THREE.KeyframeTrack: Out of order keys.", this, e3, i3, n2), t2 = false;
        break;
      }
      n2 = i3;
    }
    if (void 0 !== i2 && Ol(i2)) for (let e3 = 0, s3 = i2.length; e3 !== s3; ++e3) {
      const s4 = i2[e3];
      if (isNaN(s4)) {
        console.error("THREE.KeyframeTrack: Value is not a valid number.", this, e3, s4), t2 = false;
        break;
      }
    }
    return t2;
  }
  optimize() {
    const t2 = this.times.slice(), e2 = this.values.slice(), s2 = this.getValueSize(), i2 = this.getInterpolation() === Pe, r2 = t2.length - 1;
    let n2 = 1;
    for (let o = 1; o < r2; ++o) {
      let r3 = false;
      const a = t2[o];
      if (a !== t2[o + 1] && (1 !== o || a !== t2[0])) if (i2) r3 = true;
      else {
        const t3 = o * s2, i3 = t3 - s2, n3 = t3 + s2;
        for (let o2 = 0; o2 !== s2; ++o2) {
          const s3 = e2[t3 + o2];
          if (s3 !== e2[i3 + o2] || s3 !== e2[n3 + o2]) {
            r3 = true;
            break;
          }
        }
      }
      if (r3) {
        if (o !== n2) {
          t2[n2] = t2[o];
          const i3 = o * s2, r4 = n2 * s2;
          for (let t3 = 0; t3 !== s2; ++t3) e2[r4 + t3] = e2[i3 + t3];
        }
        ++n2;
      }
    }
    if (r2 > 0) {
      t2[n2] = t2[r2];
      for (let t3 = r2 * s2, i3 = n2 * s2, o = 0; o !== s2; ++o) e2[i3 + o] = e2[t3 + o];
      ++n2;
    }
    return n2 !== t2.length ? (this.times = t2.slice(0, n2), this.values = e2.slice(0, n2 * s2)) : (this.times = t2, this.values = e2), this;
  }
  clone() {
    const t2 = this.times.slice(), e2 = this.values.slice(), s2 = new this.constructor(this.name, t2, e2);
    return s2.createInterpolant = this.createInterpolant, s2;
  }
}
Hl.prototype.TimeBufferType = Float32Array, Hl.prototype.ValueBufferType = Float32Array, Hl.prototype.DefaultInterpolation = Ee;
class ql extends Hl {
  constructor(t2, e2, s2) {
    super(t2, e2, s2);
  }
}
ql.prototype.ValueTypeName = "bool", ql.prototype.ValueBufferType = Array, ql.prototype.DefaultInterpolation = Re, ql.prototype.InterpolantFactoryMethodLinear = void 0, ql.prototype.InterpolantFactoryMethodSmooth = void 0;
class Jl extends Hl {
}
Jl.prototype.ValueTypeName = "color";
class Xl extends Hl {
}
Xl.prototype.ValueTypeName = "number";
class Yl extends Wl {
  constructor(t2, e2, s2, i2) {
    super(t2, e2, s2, i2);
  }
  interpolate_(t2, e2, s2, i2) {
    const r2 = this.resultBuffer, n2 = this.sampleValues, o = this.valueSize, a = (s2 - e2) / (i2 - e2);
    let h2 = t2 * o;
    for (let t3 = h2 + o; h2 !== t3; h2 += 4) Ci.slerpFlat(r2, 0, n2, h2 - o, n2, h2, a);
    return r2;
  }
}
class Zl extends Hl {
  InterpolantFactoryMethodLinear(t2) {
    return new Yl(this.times, this.values, this.getValueSize(), t2);
  }
}
Zl.prototype.ValueTypeName = "quaternion", Zl.prototype.InterpolantFactoryMethodSmooth = void 0;
class Gl extends Hl {
  constructor(t2, e2, s2) {
    super(t2, e2, s2);
  }
}
Gl.prototype.ValueTypeName = "string", Gl.prototype.ValueBufferType = Array, Gl.prototype.DefaultInterpolation = Re, Gl.prototype.InterpolantFactoryMethodLinear = void 0, Gl.prototype.InterpolantFactoryMethodSmooth = void 0;
class $l extends Hl {
}
$l.prototype.ValueTypeName = "vector";
class Ql {
  constructor(t2 = "", e2 = -1, s2 = [], i2 = 2500) {
    this.name = t2, this.tracks = s2, this.duration = e2, this.blendMode = i2, this.uuid = Us(), this.duration < 0 && this.resetDuration();
  }
  static parse(t2) {
    const e2 = [], s2 = t2.tracks, i2 = 1 / (t2.fps || 1);
    for (let t3 = 0, r3 = s2.length; t3 !== r3; ++t3) e2.push(Kl(s2[t3]).scale(i2));
    const r2 = new this(t2.name, t2.duration, e2, t2.blendMode);
    return r2.uuid = t2.uuid, r2;
  }
  static toJSON(t2) {
    const e2 = [], s2 = t2.tracks, i2 = { name: t2.name, duration: t2.duration, tracks: e2, uuid: t2.uuid, blendMode: t2.blendMode };
    for (let t3 = 0, i3 = s2.length; t3 !== i3; ++t3) e2.push(Hl.toJSON(s2[t3]));
    return i2;
  }
  static CreateFromMorphTargetSequence(t2, e2, s2, i2) {
    const r2 = e2.length, n2 = [];
    for (let t3 = 0; t3 < r2; t3++) {
      let o = [], a = [];
      o.push((t3 + r2 - 1) % r2, t3, (t3 + 1) % r2), a.push(0, 1, 0);
      const h2 = Fl(o);
      o = Nl(o, 1, h2), a = Nl(a, 1, h2), i2 || 0 !== o[0] || (o.push(r2), a.push(a[0])), n2.push(new Xl(".morphTargetInfluences[" + e2[t3].name + "]", o, a).scale(1 / s2));
    }
    return new this(t2, -1, n2);
  }
  static findByName(t2, e2) {
    let s2 = t2;
    if (!Array.isArray(t2)) {
      const e3 = t2;
      s2 = e3.geometry && e3.geometry.animations || e3.animations;
    }
    for (let t3 = 0; t3 < s2.length; t3++) if (s2[t3].name === e2) return s2[t3];
    return null;
  }
  static CreateClipsFromMorphTargetSequences(t2, e2, s2) {
    const i2 = {}, r2 = /^([\w-]*?)([\d]+)$/;
    for (let e3 = 0, s3 = t2.length; e3 < s3; e3++) {
      const s4 = t2[e3], n3 = s4.name.match(r2);
      if (n3 && n3.length > 1) {
        const t3 = n3[1];
        let e4 = i2[t3];
        e4 || (i2[t3] = e4 = []), e4.push(s4);
      }
    }
    const n2 = [];
    for (const t3 in i2) n2.push(this.CreateFromMorphTargetSequence(t3, i2[t3], e2, s2));
    return n2;
  }
  static parseAnimation(t2, e2) {
    if (!t2) return console.error("THREE.AnimationClip: No animation in JSONLoader data."), null;
    const s2 = function(t3, e3, s3, i3, r3) {
      if (0 !== s3.length) {
        const n3 = [], o2 = [];
        Ll(s3, n3, o2, i3), 0 !== n3.length && r3.push(new t3(e3, n3, o2));
      }
    }, i2 = [], r2 = t2.name || "default", n2 = t2.fps || 30, o = t2.blendMode;
    let a = t2.length || -1;
    const h2 = t2.hierarchy || [];
    for (let t3 = 0; t3 < h2.length; t3++) {
      const r3 = h2[t3].keys;
      if (r3 && 0 !== r3.length) if (r3[0].morphTargets) {
        const t4 = {};
        let e3;
        for (e3 = 0; e3 < r3.length; e3++) if (r3[e3].morphTargets) for (let s3 = 0; s3 < r3[e3].morphTargets.length; s3++) t4[r3[e3].morphTargets[s3]] = -1;
        for (const s3 in t4) {
          const t5 = [], n3 = [];
          for (let i3 = 0; i3 !== r3[e3].morphTargets.length; ++i3) {
            const i4 = r3[e3];
            t5.push(i4.time), n3.push(i4.morphTarget === s3 ? 1 : 0);
          }
          i2.push(new Xl(".morphTargetInfluence[" + s3 + "]", t5, n3));
        }
        a = t4.length * n2;
      } else {
        const n3 = ".bones[" + e2[t3].name + "]";
        s2($l, n3 + ".position", r3, "pos", i2), s2(Zl, n3 + ".quaternion", r3, "rot", i2), s2($l, n3 + ".scale", r3, "scl", i2);
      }
    }
    if (0 === i2.length) return null;
    return new this(r2, a, i2, o);
  }
  resetDuration() {
    let t2 = 0;
    for (let e2 = 0, s2 = this.tracks.length; e2 !== s2; ++e2) {
      const s3 = this.tracks[e2];
      t2 = Math.max(t2, s3.times[s3.times.length - 1]);
    }
    return this.duration = t2, this;
  }
  trim() {
    for (let t2 = 0; t2 < this.tracks.length; t2++) this.tracks[t2].trim(0, this.duration);
    return this;
  }
  validate() {
    let t2 = true;
    for (let e2 = 0; e2 < this.tracks.length; e2++) t2 = t2 && this.tracks[e2].validate();
    return t2;
  }
  optimize() {
    for (let t2 = 0; t2 < this.tracks.length; t2++) this.tracks[t2].optimize();
    return this;
  }
  clone() {
    const t2 = [];
    for (let e2 = 0; e2 < this.tracks.length; e2++) t2.push(this.tracks[e2].clone());
    return new this.constructor(this.name, this.duration, t2, this.blendMode);
  }
  toJSON() {
    return this.constructor.toJSON(this);
  }
}
function Kl(t2) {
  if (void 0 === t2.type) throw new Error("THREE.KeyframeTrack: track type undefined, can not parse");
  const e2 = function(t3) {
    switch (t3.toLowerCase()) {
      case "scalar":
      case "double":
      case "float":
      case "number":
      case "integer":
        return Xl;
      case "vector":
      case "vector2":
      case "vector3":
      case "vector4":
        return $l;
      case "color":
        return Jl;
      case "quaternion":
        return Zl;
      case "bool":
      case "boolean":
        return ql;
      case "string":
        return Gl;
    }
    throw new Error("THREE.KeyframeTrack: Unsupported typeName: " + t3);
  }(t2.type);
  if (void 0 === t2.times) {
    const e3 = [], s2 = [];
    Ll(t2.keys, e3, s2, "value"), t2.times = e3, t2.values = s2;
  }
  return void 0 !== e2.parse ? e2.parse(t2) : new e2(t2.name, t2.times, t2.values, t2.interpolation);
}
const tc = { enabled: false, files: {}, add: function(t2, e2) {
  false !== this.enabled && (this.files[t2] = e2);
}, get: function(t2) {
  if (false !== this.enabled) return this.files[t2];
}, remove: function(t2) {
  delete this.files[t2];
}, clear: function() {
  this.files = {};
} };
class ec {
  constructor(t2, e2, s2) {
    const i2 = this;
    let r2, n2 = false, o = 0, a = 0;
    const h2 = [];
    this.onStart = void 0, this.onLoad = t2, this.onProgress = e2, this.onError = s2, this.itemStart = function(t3) {
      a++, false === n2 && void 0 !== i2.onStart && i2.onStart(t3, o, a), n2 = true;
    }, this.itemEnd = function(t3) {
      o++, void 0 !== i2.onProgress && i2.onProgress(t3, o, a), o === a && (n2 = false, void 0 !== i2.onLoad && i2.onLoad());
    }, this.itemError = function(t3) {
      void 0 !== i2.onError && i2.onError(t3);
    }, this.resolveURL = function(t3) {
      return r2 ? r2(t3) : t3;
    }, this.setURLModifier = function(t3) {
      return r2 = t3, this;
    }, this.addHandler = function(t3, e3) {
      return h2.push(t3, e3), this;
    }, this.removeHandler = function(t3) {
      const e3 = h2.indexOf(t3);
      return -1 !== e3 && h2.splice(e3, 2), this;
    }, this.getHandler = function(t3) {
      for (let e3 = 0, s3 = h2.length; e3 < s3; e3 += 2) {
        const s4 = h2[e3], i3 = h2[e3 + 1];
        if (s4.global && (s4.lastIndex = 0), s4.test(t3)) return i3;
      }
      return null;
    };
  }
}
const sc = new ec();
class ic {
  constructor(t2) {
    this.manager = void 0 !== t2 ? t2 : sc, this.crossOrigin = "anonymous", this.withCredentials = false, this.path = "", this.resourcePath = "", this.requestHeader = {};
  }
  load() {
  }
  loadAsync(t2, e2) {
    const s2 = this;
    return new Promise(function(i2, r2) {
      s2.load(t2, i2, e2, r2);
    });
  }
  parse() {
  }
  setCrossOrigin(t2) {
    return this.crossOrigin = t2, this;
  }
  setWithCredentials(t2) {
    return this.withCredentials = t2, this;
  }
  setPath(t2) {
    return this.path = t2, this;
  }
  setResourcePath(t2) {
    return this.resourcePath = t2, this;
  }
  setRequestHeader(t2) {
    return this.requestHeader = t2, this;
  }
}
ic.DEFAULT_MATERIAL_NAME = "__DEFAULT";
const rc = {};
class nc extends Error {
  constructor(t2, e2) {
    super(t2), this.response = e2;
  }
}
class oc extends ic {
  constructor(t2) {
    super(t2);
  }
  load(t2, e2, s2, i2) {
    void 0 === t2 && (t2 = ""), void 0 !== this.path && (t2 = this.path + t2), t2 = this.manager.resolveURL(t2);
    const r2 = tc.get(t2);
    if (void 0 !== r2) return this.manager.itemStart(t2), setTimeout(() => {
      e2 && e2(r2), this.manager.itemEnd(t2);
    }, 0), r2;
    if (void 0 !== rc[t2]) return void rc[t2].push({ onLoad: e2, onProgress: s2, onError: i2 });
    rc[t2] = [], rc[t2].push({ onLoad: e2, onProgress: s2, onError: i2 });
    const n2 = new THREEGlobals["Request"](t2, { headers: new THREEGlobals["Headers"](this.requestHeader), credentials: this.withCredentials ? "include" : "same-origin" }), o = this.mimeType, a = this.responseType;
    THREEGlobals["fetch"](n2).then((e3) => {
      if (200 === e3.status || 0 === e3.status) {
        if (0 === e3.status && console.warn("THREE.FileLoader: HTTP Status 0 received."), "undefined" == typeof THREEGlobals["ReadableStream"] || void 0 === e3.body || void 0 === e3.body.getReader) return e3;
        const s3 = rc[t2], i3 = e3.body.getReader(), r3 = e3.headers.get("X-File-Size") || e3.headers.get("Content-Length"), n3 = r3 ? parseInt(r3) : 0, o2 = 0 !== n3;
        let a2 = 0;
        const h2 = new THREEGlobals["ReadableStream"]({ start(t3) {
          !function e4() {
            i3.read().then(({ done: i4, value: r4 }) => {
              if (i4) t3.close();
              else {
                a2 += r4.byteLength;
                const i5 = new THREEGlobals["ProgressEvent"]("progress", { lengthComputable: o2, loaded: a2, total: n3 });
                for (let t4 = 0, e5 = s3.length; t4 < e5; t4++) {
                  const e6 = s3[t4];
                  e6.onProgress && e6.onProgress(i5);
                }
                t3.enqueue(r4), e4();
              }
            }, (e5) => {
              t3.error(e5);
            });
          }();
        } });
        return new THREEGlobals["Response"](h2);
      }
      throw new nc(`fetch for "${e3.url}" responded with ${e3.status}: ${e3.statusText}`, e3);
    }).then((t3) => {
      switch (a) {
        case "arraybuffer":
          return t3.arrayBuffer();
        case "blob":
          return t3.blob();
        case "document":
          return t3.text().then((t4) => new THREEGlobals["DOMParser"]().parseFromString(t4, o));
        case "json":
          return t3.json();
        default:
          if (void 0 === o) return t3.text();
          {
            const e3 = /charset="?([^;"\s]*)"?/i.exec(o), s3 = e3 && e3[1] ? e3[1].toLowerCase() : void 0, i3 = new THREEGlobals["TextDecoder"](s3);
            return t3.arrayBuffer().then((t4) => i3.decode(t4));
          }
      }
    }).then((e3) => {
      tc.add(t2, e3);
      const s3 = rc[t2];
      delete rc[t2];
      for (let t3 = 0, i3 = s3.length; t3 < i3; t3++) {
        const i4 = s3[t3];
        i4.onLoad && i4.onLoad(e3);
      }
    }).catch((e3) => {
      const s3 = rc[t2];
      if (void 0 === s3) throw this.manager.itemError(t2), e3;
      delete rc[t2];
      for (let t3 = 0, i3 = s3.length; t3 < i3; t3++) {
        const i4 = s3[t3];
        i4.onError && i4.onError(e3);
      }
      this.manager.itemError(t2);
    }).finally(() => {
      this.manager.itemEnd(t2);
    }), this.manager.itemStart(t2);
  }
  setResponseType(t2) {
    return this.responseType = t2, this;
  }
  setMimeType(t2) {
    return this.mimeType = t2, this;
  }
}
class lc extends ic {
  constructor(t2) {
    super(t2);
  }
  load(t2, e2, s2, i2) {
    void 0 !== this.path && (t2 = this.path + t2), t2 = this.manager.resolveURL(t2);
    const r2 = this, n2 = tc.get(t2);
    if (void 0 !== n2) return r2.manager.itemStart(t2), setTimeout(function() {
      e2 && e2(n2), r2.manager.itemEnd(t2);
    }, 0), n2;
    const o = ei("img");
    function a() {
      l2(), tc.add(t2, this), e2 && e2(this), r2.manager.itemEnd(t2);
    }
    function h2(e3) {
      l2(), i2 && i2(e3), r2.manager.itemError(t2), r2.manager.itemEnd(t2);
    }
    function l2() {
      o.removeEventListener("load", a, false), o.removeEventListener("error", h2, false);
    }
    return o.addEventListener("load", a, false), o.addEventListener("error", h2, false), "data:" !== t2.slice(0, 5) && void 0 !== this.crossOrigin && (o.crossOrigin = this.crossOrigin), r2.manager.itemStart(t2), o.src = t2, o;
  }
}
class dc extends ic {
  constructor(t2) {
    super(t2);
  }
  load(t2, e2, s2, i2) {
    const r2 = new vi(), n2 = new lc(this.manager);
    return n2.setCrossOrigin(this.crossOrigin), n2.setPath(this.path), n2.load(t2, function(t3) {
      r2.image = t3, r2.needsUpdate = true, void 0 !== e2 && e2(r2);
    }, s2, i2), r2;
  }
}
class pc extends Rr {
  constructor(t2, e2 = 1) {
    super(), this.isLight = true, this.type = "Light", this.color = new $r(t2), this.intensity = e2;
  }
  dispose() {
  }
  copy(t2, e2) {
    return super.copy(t2, e2), this.color.copy(t2.color), this.intensity = t2.intensity, this;
  }
  toJSON(t2) {
    const e2 = super.toJSON(t2);
    return e2.object.color = this.color.getHex(), e2.object.intensity = this.intensity, void 0 !== this.groundColor && (e2.object.groundColor = this.groundColor.getHex()), void 0 !== this.distance && (e2.object.distance = this.distance), void 0 !== this.angle && (e2.object.angle = this.angle), void 0 !== this.decay && (e2.object.decay = this.decay), void 0 !== this.penumbra && (e2.object.penumbra = this.penumbra), void 0 !== this.shadow && (e2.object.shadow = this.shadow.toJSON()), void 0 !== this.target && (e2.object.target = this.target.uuid), e2;
  }
}
const yc = new nr(), fc = new Ii(), gc = new Ii();
class xc {
  constructor(t2) {
    this.camera = t2, this.intensity = 1, this.bias = 0, this.normalBias = 0, this.radius = 1, this.blurSamples = 8, this.mapSize = new Zs(512, 512), this.map = null, this.mapPass = null, this.matrix = new nr(), this.autoUpdate = true, this.needsUpdate = false, this._frustum = new ra(), this._frameExtents = new Zs(1, 1), this._viewportCount = 1, this._viewports = [new wi(0, 0, 1, 1)];
  }
  getViewportCount() {
    return this._viewportCount;
  }
  getFrustum() {
    return this._frustum;
  }
  updateMatrices(t2) {
    const e2 = this.camera, s2 = this.matrix;
    fc.setFromMatrixPosition(t2.matrixWorld), e2.position.copy(fc), gc.setFromMatrixPosition(t2.target.matrixWorld), e2.lookAt(gc), e2.updateMatrixWorld(), yc.multiplyMatrices(e2.projectionMatrix, e2.matrixWorldInverse), this._frustum.setFromProjectionMatrix(yc), s2.set(0.5, 0, 0, 0.5, 0, 0.5, 0, 0.5, 0, 0, 0.5, 0.5, 0, 0, 0, 1), s2.multiply(yc);
  }
  getViewport(t2) {
    return this._viewports[t2];
  }
  getFrameExtents() {
    return this._frameExtents;
  }
  dispose() {
    this.map && this.map.dispose(), this.mapPass && this.mapPass.dispose();
  }
  copy(t2) {
    return this.camera = t2.camera.clone(), this.intensity = t2.intensity, this.bias = t2.bias, this.radius = t2.radius, this.mapSize.copy(t2.mapSize), this;
  }
  clone() {
    return new this.constructor().copy(this);
  }
  toJSON() {
    const t2 = {};
    return 1 !== this.intensity && (t2.intensity = this.intensity), 0 !== this.bias && (t2.bias = this.bias), 0 !== this.normalBias && (t2.normalBias = this.normalBias), 1 !== this.radius && (t2.radius = this.radius), 512 === this.mapSize.x && 512 === this.mapSize.y || (t2.mapSize = this.mapSize.toArray()), t2.camera = this.camera.toJSON(false).object, delete t2.camera.matrix, t2;
  }
}
class bc extends xc {
  constructor() {
    super(new $n(50, 1, 0.5, 500)), this.isSpotLightShadow = true, this.focus = 1;
  }
  updateMatrices(t2) {
    const e2 = this.camera, s2 = 2 * js * t2.angle * this.focus, i2 = this.mapSize.width / this.mapSize.height, r2 = t2.distance || e2.far;
    s2 === e2.fov && i2 === e2.aspect && r2 === e2.far || (e2.fov = s2, e2.aspect = i2, e2.far = r2, e2.updateProjectionMatrix()), super.updateMatrices(t2);
  }
  copy(t2) {
    return super.copy(t2), this.focus = t2.focus, this;
  }
}
class vc extends pc {
  constructor(t2, e2, s2 = 0, i2 = Math.PI / 3, r2 = 0, n2 = 2) {
    super(t2, e2), this.isSpotLight = true, this.type = "SpotLight", this.position.copy(Rr.DEFAULT_UP), this.updateMatrix(), this.target = new Rr(), this.distance = s2, this.angle = i2, this.penumbra = r2, this.decay = n2, this.map = null, this.shadow = new bc();
  }
  get power() {
    return this.intensity * Math.PI;
  }
  set power(t2) {
    this.intensity = t2 / Math.PI;
  }
  dispose() {
    this.shadow.dispose();
  }
  copy(t2, e2) {
    return super.copy(t2, e2), this.distance = t2.distance, this.angle = t2.angle, this.penumbra = t2.penumbra, this.decay = t2.decay, this.target = t2.target.clone(), this.shadow = t2.shadow.clone(), this;
  }
}
const wc = new nr(), Mc = new Ii(), Sc = new Ii();
class _c extends xc {
  constructor() {
    super(new $n(90, 1, 0.5, 500)), this.isPointLightShadow = true, this._frameExtents = new Zs(4, 2), this._viewportCount = 6, this._viewports = [new wi(2, 1, 1, 1), new wi(0, 1, 1, 1), new wi(3, 1, 1, 1), new wi(1, 1, 1, 1), new wi(3, 0, 1, 1), new wi(1, 0, 1, 1)], this._cubeDirections = [new Ii(1, 0, 0), new Ii(-1, 0, 0), new Ii(0, 0, 1), new Ii(0, 0, -1), new Ii(0, 1, 0), new Ii(0, -1, 0)], this._cubeUps = [new Ii(0, 1, 0), new Ii(0, 1, 0), new Ii(0, 1, 0), new Ii(0, 1, 0), new Ii(0, 0, 1), new Ii(0, 0, -1)];
  }
  updateMatrices(t2, e2 = 0) {
    const s2 = this.camera, i2 = this.matrix, r2 = t2.distance || s2.far;
    r2 !== s2.far && (s2.far = r2, s2.updateProjectionMatrix()), Mc.setFromMatrixPosition(t2.matrixWorld), s2.position.copy(Mc), Sc.copy(s2.position), Sc.add(this._cubeDirections[e2]), s2.up.copy(this._cubeUps[e2]), s2.lookAt(Sc), s2.updateMatrixWorld(), i2.makeTranslation(-Mc.x, -Mc.y, -Mc.z), wc.multiplyMatrices(s2.projectionMatrix, s2.matrixWorldInverse), this._frustum.setFromProjectionMatrix(wc);
  }
}
class Ac extends pc {
  constructor(t2, e2, s2 = 0, i2 = 2) {
    super(t2, e2), this.isPointLight = true, this.type = "PointLight", this.distance = s2, this.decay = i2, this.shadow = new _c();
  }
  get power() {
    return 4 * this.intensity * Math.PI;
  }
  set power(t2) {
    this.intensity = t2 / (4 * Math.PI);
  }
  dispose() {
    this.shadow.dispose();
  }
  copy(t2, e2) {
    return super.copy(t2, e2), this.distance = t2.distance, this.decay = t2.decay, this.shadow = t2.shadow.clone(), this;
  }
}
class Tc extends Xn {
  constructor(t2 = -1, e2 = 1, s2 = 1, i2 = -1, r2 = 0.1, n2 = 2e3) {
    super(), this.isOrthographicCamera = true, this.type = "OrthographicCamera", this.zoom = 1, this.view = null, this.left = t2, this.right = e2, this.top = s2, this.bottom = i2, this.near = r2, this.far = n2, this.updateProjectionMatrix();
  }
  copy(t2, e2) {
    return super.copy(t2, e2), this.left = t2.left, this.right = t2.right, this.top = t2.top, this.bottom = t2.bottom, this.near = t2.near, this.far = t2.far, this.zoom = t2.zoom, this.view = null === t2.view ? null : Object.assign({}, t2.view), this;
  }
  setViewOffset(t2, e2, s2, i2, r2, n2) {
    null === this.view && (this.view = { enabled: true, fullWidth: 1, fullHeight: 1, offsetX: 0, offsetY: 0, width: 1, height: 1 }), this.view.enabled = true, this.view.fullWidth = t2, this.view.fullHeight = e2, this.view.offsetX = s2, this.view.offsetY = i2, this.view.width = r2, this.view.height = n2, this.updateProjectionMatrix();
  }
  clearViewOffset() {
    null !== this.view && (this.view.enabled = false), this.updateProjectionMatrix();
  }
  updateProjectionMatrix() {
    const t2 = (this.right - this.left) / (2 * this.zoom), e2 = (this.top - this.bottom) / (2 * this.zoom), s2 = (this.right + this.left) / 2, i2 = (this.top + this.bottom) / 2;
    let r2 = s2 - t2, n2 = s2 + t2, o = i2 + e2, a = i2 - e2;
    if (null !== this.view && this.view.enabled) {
      const t3 = (this.right - this.left) / this.view.fullWidth / this.zoom, e3 = (this.top - this.bottom) / this.view.fullHeight / this.zoom;
      r2 += t3 * this.view.offsetX, n2 = r2 + t3 * this.view.width, o -= e3 * this.view.offsetY, a = o - e3 * this.view.height;
    }
    this.projectionMatrix.makeOrthographic(r2, n2, o, a, this.near, this.far, this.coordinateSystem), this.projectionMatrixInverse.copy(this.projectionMatrix).invert();
  }
  toJSON(t2) {
    const e2 = super.toJSON(t2);
    return e2.object.zoom = this.zoom, e2.object.left = this.left, e2.object.right = this.right, e2.object.top = this.top, e2.object.bottom = this.bottom, e2.object.near = this.near, e2.object.far = this.far, null !== this.view && (e2.object.view = Object.assign({}, this.view)), e2;
  }
}
class zc extends xc {
  constructor() {
    super(new Tc(-5, 5, 5, -5, 0.5, 500)), this.isDirectionalLightShadow = true;
  }
}
class Cc extends pc {
  constructor(t2, e2) {
    super(t2, e2), this.isDirectionalLight = true, this.type = "DirectionalLight", this.position.copy(Rr.DEFAULT_UP), this.updateMatrix(), this.target = new Rr(), this.shadow = new zc();
  }
  dispose() {
    this.shadow.dispose();
  }
  copy(t2) {
    return super.copy(t2), this.target = t2.target.clone(), this.shadow = t2.shadow.clone(), this;
  }
}
class Ic extends pc {
  constructor(t2, e2) {
    super(t2, e2), this.isAmbientLight = true, this.type = "AmbientLight";
  }
}
class Pc {
  static decodeText(t2) {
    if (console.warn("THREE.LoaderUtils: decodeText() has been deprecated with r165 and will be removed with r175. Use TextDecoder instead."), "undefined" != typeof THREEGlobals["TextDecoder"]) return new THREEGlobals["TextDecoder"]().decode(t2);
    let e2 = "";
    for (let s2 = 0, i2 = t2.length; s2 < i2; s2++) e2 += String.fromCharCode(t2[s2]);
    try {
      return decodeURIComponent(escape(e2));
    } catch (t3) {
      return e2;
    }
  }
  static extractUrlBase(t2) {
    const e2 = t2.lastIndexOf("/");
    return -1 === e2 ? "./" : t2.slice(0, e2 + 1);
  }
  static resolveURL(t2, e2) {
    return "string" != typeof t2 || "" === t2 ? "" : (/^https?:\/\//i.test(e2) && /^\//.test(t2) && (e2 = e2.replace(/(^https?:\/\/[^\/]+).*/i, "$1")), /^(https?:)?\/\//i.test(t2) || /^data:.*,.*$/i.test(t2) || /^blob:.*$/i.test(t2) ? t2 : e2 + t2);
  }
}
class jc extends ic {
  constructor(t2) {
    super(t2), this.isImageBitmapLoader = true, "undefined" == typeof THREEGlobals["createImageBitmap"] && console.warn("THREE.ImageBitmapLoader: createImageBitmap() not supported."), "undefined" == typeof THREEGlobals["fetch"] && console.warn("THREE.ImageBitmapLoader: fetch() not supported."), this.options = { premultiplyAlpha: "none" };
  }
  setOptions(t2) {
    return this.options = t2, this;
  }
  load(t2, e2, s2, i2) {
    void 0 === t2 && (t2 = ""), void 0 !== this.path && (t2 = this.path + t2), t2 = this.manager.resolveURL(t2);
    const r2 = this, n2 = tc.get(t2);
    if (void 0 !== n2) return r2.manager.itemStart(t2), n2.then ? void n2.then((s3) => {
      e2 && e2(s3), r2.manager.itemEnd(t2);
    }).catch((t3) => {
      i2 && i2(t3);
    }) : (setTimeout(function() {
      e2 && e2(n2), r2.manager.itemEnd(t2);
    }, 0), n2);
    const o = {};
    o.credentials = "anonymous" === this.crossOrigin ? "same-origin" : "include", o.headers = this.requestHeader;
    const a = THREEGlobals["fetch"](t2, o).then(function(t3) {
      return t3.blob();
    }).then(function(t3) {
      return THREEGlobals["createImageBitmap"](t3, Object.assign(r2.options, { colorSpaceConversion: "none" }));
    }).then(function(s3) {
      return tc.add(t2, s3), e2 && e2(s3), r2.manager.itemEnd(t2), s3;
    }).catch(function(e3) {
      i2 && i2(e3), tc.remove(t2), r2.manager.itemError(t2), r2.manager.itemEnd(t2);
    });
    tc.add(t2, a), r2.manager.itemStart(t2);
  }
}
new nr();
new nr();
new nr();
class Zc extends $n {
  constructor(t2 = []) {
    super(), this.isArrayCamera = true, this.cameras = t2;
  }
}
new Ii();
new Ii();
new Ii();
new Ii();
new Ii();
new Ii();
const uu = "\\[\\]\\.:\\/", du = new RegExp("[" + uu + "]", "g"), pu = "[^" + uu + "]", mu = "[^" + uu.replace("\\.", "") + "]", yu = new RegExp("^" + /((?:WC+[\/:])*)/.source.replace("WC", pu) + /(WCOD+)?/.source.replace("WCOD", mu) + /(?:\.(WC+)(?:\[(.+)\])?)?/.source.replace("WC", pu) + /\.(WC+)(?:\[(.+)\])?/.source.replace("WC", pu) + "$"), fu = ["material", "materials", "bones", "map"];
class gu {
  constructor(t2, e2, s2) {
    this.path = e2, this.parsedPath = s2 || gu.parseTrackName(e2), this.node = gu.findNode(t2, this.parsedPath.nodeName), this.rootNode = t2, this.getValue = this._getValue_unbound, this.setValue = this._setValue_unbound;
  }
  static create(t2, e2, s2) {
    return t2 && t2.isAnimationObjectGroup ? new gu.Composite(t2, e2, s2) : new gu(t2, e2, s2);
  }
  static sanitizeNodeName(t2) {
    return t2.replace(/\s/g, "_").replace(du, "");
  }
  static parseTrackName(t2) {
    const e2 = yu.exec(t2);
    if (null === e2) throw new Error("PropertyBinding: Cannot parse trackName: " + t2);
    const s2 = { nodeName: e2[2], objectName: e2[3], objectIndex: e2[4], propertyName: e2[5], propertyIndex: e2[6] }, i2 = s2.nodeName && s2.nodeName.lastIndexOf(".");
    if (void 0 !== i2 && -1 !== i2) {
      const t3 = s2.nodeName.substring(i2 + 1);
      -1 !== fu.indexOf(t3) && (s2.nodeName = s2.nodeName.substring(0, i2), s2.objectName = t3);
    }
    if (null === s2.propertyName || 0 === s2.propertyName.length) throw new Error("PropertyBinding: can not parse propertyName from trackName: " + t2);
    return s2;
  }
  static findNode(t2, e2) {
    if (void 0 === e2 || "" === e2 || "." === e2 || -1 === e2 || e2 === t2.name || e2 === t2.uuid) return t2;
    if (t2.skeleton) {
      const s2 = t2.skeleton.getBoneByName(e2);
      if (void 0 !== s2) return s2;
    }
    if (t2.children) {
      const s2 = function(t3) {
        for (let i3 = 0; i3 < t3.length; i3++) {
          const r2 = t3[i3];
          if (r2.name === e2 || r2.uuid === e2) return r2;
          const n2 = s2(r2.children);
          if (n2) return n2;
        }
        return null;
      }, i2 = s2(t2.children);
      if (i2) return i2;
    }
    return null;
  }
  _getValue_unavailable() {
  }
  _setValue_unavailable() {
  }
  _getValue_direct(t2, e2) {
    t2[e2] = this.targetObject[this.propertyName];
  }
  _getValue_array(t2, e2) {
    const s2 = this.resolvedProperty;
    for (let i2 = 0, r2 = s2.length; i2 !== r2; ++i2) t2[e2++] = s2[i2];
  }
  _getValue_arrayElement(t2, e2) {
    t2[e2] = this.resolvedProperty[this.propertyIndex];
  }
  _getValue_toArray(t2, e2) {
    this.resolvedProperty.toArray(t2, e2);
  }
  _setValue_direct(t2, e2) {
    this.targetObject[this.propertyName] = t2[e2];
  }
  _setValue_direct_setNeedsUpdate(t2, e2) {
    this.targetObject[this.propertyName] = t2[e2], this.targetObject.needsUpdate = true;
  }
  _setValue_direct_setMatrixWorldNeedsUpdate(t2, e2) {
    this.targetObject[this.propertyName] = t2[e2], this.targetObject.matrixWorldNeedsUpdate = true;
  }
  _setValue_array(t2, e2) {
    const s2 = this.resolvedProperty;
    for (let i2 = 0, r2 = s2.length; i2 !== r2; ++i2) s2[i2] = t2[e2++];
  }
  _setValue_array_setNeedsUpdate(t2, e2) {
    const s2 = this.resolvedProperty;
    for (let i2 = 0, r2 = s2.length; i2 !== r2; ++i2) s2[i2] = t2[e2++];
    this.targetObject.needsUpdate = true;
  }
  _setValue_array_setMatrixWorldNeedsUpdate(t2, e2) {
    const s2 = this.resolvedProperty;
    for (let i2 = 0, r2 = s2.length; i2 !== r2; ++i2) s2[i2] = t2[e2++];
    this.targetObject.matrixWorldNeedsUpdate = true;
  }
  _setValue_arrayElement(t2, e2) {
    this.resolvedProperty[this.propertyIndex] = t2[e2];
  }
  _setValue_arrayElement_setNeedsUpdate(t2, e2) {
    this.resolvedProperty[this.propertyIndex] = t2[e2], this.targetObject.needsUpdate = true;
  }
  _setValue_arrayElement_setMatrixWorldNeedsUpdate(t2, e2) {
    this.resolvedProperty[this.propertyIndex] = t2[e2], this.targetObject.matrixWorldNeedsUpdate = true;
  }
  _setValue_fromArray(t2, e2) {
    this.resolvedProperty.fromArray(t2, e2);
  }
  _setValue_fromArray_setNeedsUpdate(t2, e2) {
    this.resolvedProperty.fromArray(t2, e2), this.targetObject.needsUpdate = true;
  }
  _setValue_fromArray_setMatrixWorldNeedsUpdate(t2, e2) {
    this.resolvedProperty.fromArray(t2, e2), this.targetObject.matrixWorldNeedsUpdate = true;
  }
  _getValue_unbound(t2, e2) {
    this.bind(), this.getValue(t2, e2);
  }
  _setValue_unbound(t2, e2) {
    this.bind(), this.setValue(t2, e2);
  }
  bind() {
    let t2 = this.node;
    const e2 = this.parsedPath, s2 = e2.objectName, i2 = e2.propertyName;
    let r2 = e2.propertyIndex;
    if (t2 || (t2 = gu.findNode(this.rootNode, e2.nodeName), this.node = t2), this.getValue = this._getValue_unavailable, this.setValue = this._setValue_unavailable, !t2) return void console.warn("THREE.PropertyBinding: No target node found for track: " + this.path + ".");
    if (s2) {
      let i3 = e2.objectIndex;
      switch (s2) {
        case "materials":
          if (!t2.material) return void console.error("THREE.PropertyBinding: Can not bind to material as node does not have a material.", this);
          if (!t2.material.materials) return void console.error("THREE.PropertyBinding: Can not bind to material.materials as node.material does not have a materials array.", this);
          t2 = t2.material.materials;
          break;
        case "bones":
          if (!t2.skeleton) return void console.error("THREE.PropertyBinding: Can not bind to bones as node does not have a skeleton.", this);
          t2 = t2.skeleton.bones;
          for (let e3 = 0; e3 < t2.length; e3++) if (t2[e3].name === i3) {
            i3 = e3;
            break;
          }
          break;
        case "map":
          if ("map" in t2) {
            t2 = t2.map;
            break;
          }
          if (!t2.material) return void console.error("THREE.PropertyBinding: Can not bind to material as node does not have a material.", this);
          if (!t2.material.map) return void console.error("THREE.PropertyBinding: Can not bind to material.map as node.material does not have a map.", this);
          t2 = t2.material.map;
          break;
        default:
          if (void 0 === t2[s2]) return void console.error("THREE.PropertyBinding: Can not bind to objectName of node undefined.", this);
          t2 = t2[s2];
      }
      if (void 0 !== i3) {
        if (void 0 === t2[i3]) return void console.error("THREE.PropertyBinding: Trying to bind to objectIndex of objectName, but is undefined.", this, t2);
        t2 = t2[i3];
      }
    }
    const n2 = t2[i2];
    if (void 0 === n2) {
      const s3 = e2.nodeName;
      return void console.error("THREE.PropertyBinding: Trying to update property for track: " + s3 + "." + i2 + " but it wasn't found.", t2);
    }
    let o = this.Versioning.None;
    this.targetObject = t2, true === t2.isMaterial ? o = this.Versioning.NeedsUpdate : true === t2.isObject3D && (o = this.Versioning.MatrixWorldNeedsUpdate);
    let a = this.BindingType.Direct;
    if (void 0 !== r2) {
      if ("morphTargetInfluences" === i2) {
        if (!t2.geometry) return void console.error("THREE.PropertyBinding: Can not bind to morphTargetInfluences because node does not have a geometry.", this);
        if (!t2.geometry.morphAttributes) return void console.error("THREE.PropertyBinding: Can not bind to morphTargetInfluences because node does not have a geometry.morphAttributes.", this);
        void 0 !== t2.morphTargetDictionary[r2] && (r2 = t2.morphTargetDictionary[r2]);
      }
      a = this.BindingType.ArrayElement, this.resolvedProperty = n2, this.propertyIndex = r2;
    } else void 0 !== n2.fromArray && void 0 !== n2.toArray ? (a = this.BindingType.HasFromToArray, this.resolvedProperty = n2) : Array.isArray(n2) ? (a = this.BindingType.EntireArray, this.resolvedProperty = n2) : this.propertyName = i2;
    this.getValue = this.GetterByBindingType[a], this.setValue = this.SetterByBindingTypeAndVersioning[a][o];
  }
  unbind() {
    this.node = null, this.getValue = this._getValue_unbound, this.setValue = this._setValue_unbound;
  }
}
gu.Composite = class {
  constructor(t2, e2, s2) {
    const i2 = s2 || gu.parseTrackName(e2);
    this._targetGroup = t2, this._bindings = t2.subscribe_(e2, i2);
  }
  getValue(t2, e2) {
    this.bind();
    const s2 = this._targetGroup.nCachedObjects_, i2 = this._bindings[s2];
    void 0 !== i2 && i2.getValue(t2, e2);
  }
  setValue(t2, e2) {
    const s2 = this._bindings;
    for (let i2 = this._targetGroup.nCachedObjects_, r2 = s2.length; i2 !== r2; ++i2) s2[i2].setValue(t2, e2);
  }
  bind() {
    const t2 = this._bindings;
    for (let e2 = this._targetGroup.nCachedObjects_, s2 = t2.length; e2 !== s2; ++e2) t2[e2].bind();
  }
  unbind() {
    const t2 = this._bindings;
    for (let e2 = this._targetGroup.nCachedObjects_, s2 = t2.length; e2 !== s2; ++e2) t2[e2].unbind();
  }
}, gu.prototype.BindingType = { Direct: 0, EntireArray: 1, ArrayElement: 2, HasFromToArray: 3 }, gu.prototype.Versioning = { None: 0, NeedsUpdate: 1, MatrixWorldNeedsUpdate: 2 }, gu.prototype.GetterByBindingType = [gu.prototype._getValue_direct, gu.prototype._getValue_array, gu.prototype._getValue_arrayElement, gu.prototype._getValue_toArray], gu.prototype.SetterByBindingTypeAndVersioning = [[gu.prototype._setValue_direct, gu.prototype._setValue_direct_setNeedsUpdate, gu.prototype._setValue_direct_setMatrixWorldNeedsUpdate], [gu.prototype._setValue_array, gu.prototype._setValue_array_setNeedsUpdate, gu.prototype._setValue_array_setMatrixWorldNeedsUpdate], [gu.prototype._setValue_arrayElement, gu.prototype._setValue_arrayElement_setNeedsUpdate, gu.prototype._setValue_arrayElement_setMatrixWorldNeedsUpdate], [gu.prototype._setValue_fromArray, gu.prototype._setValue_fromArray_setNeedsUpdate, gu.prototype._setValue_fromArray_setMatrixWorldNeedsUpdate]];
new nr();
class Eu {
  constructor(t2 = 1, e2 = 0, s2 = 0) {
    return this.radius = t2, this.phi = e2, this.theta = s2, this;
  }
  set(t2, e2, s2) {
    return this.radius = t2, this.phi = e2, this.theta = s2, this;
  }
  copy(t2) {
    return this.radius = t2.radius, this.phi = t2.phi, this.theta = t2.theta, this;
  }
  makeSafe() {
    const t2 = 1e-6;
    return this.phi = Ds(this.phi, t2, Math.PI - t2), this;
  }
  setFromVector3(t2) {
    return this.setFromCartesianCoords(t2.x, t2.y, t2.z);
  }
  setFromCartesianCoords(t2, e2, s2) {
    return this.radius = Math.sqrt(t2 * t2 + e2 * e2 + s2 * s2), 0 === this.radius ? (this.theta = 0, this.phi = 0) : (this.theta = Math.atan2(t2, s2), this.phi = Math.acos(Ds(e2 / this.radius, -1, 1))), this;
  }
  clone() {
    return new this.constructor().copy(this);
  }
}
new Zs();
new Ii();
new Ii();
new Ii();
new Ii();
new nr();
new nr();
new Ii();
new $r();
new $r();
class Ku extends Oa {
  constructor(t2 = 10, e2 = 10, s2 = 4473924, i2 = 8947848) {
    s2 = new $r(s2), i2 = new $r(i2);
    const r2 = e2 / 2, n2 = t2 / e2, o = t2 / 2, a = [], h2 = [];
    for (let t3 = 0, l3 = 0, c2 = -o; t3 <= e2; t3++, c2 += n2) {
      a.push(-o, 0, c2, o, 0, c2), a.push(c2, 0, -o, c2, 0, o);
      const e3 = t3 === r2 ? s2 : i2;
      e3.toArray(h2, l3), l3 += 3, e3.toArray(h2, l3), l3 += 3, e3.toArray(h2, l3), l3 += 3, e3.toArray(h2, l3), l3 += 3;
    }
    const l2 = new zn();
    l2.setAttribute("position", new bn(a, 3)), l2.setAttribute("color", new bn(h2, 3));
    super(l2, new Sa({ vertexColors: true, toneMapped: false })), this.type = "GridHelper";
  }
  dispose() {
    this.geometry.dispose(), this.material.dispose();
  }
}
new Ii();
new Ii();
new Ii();
new Ii();
new Xn();
new Ri();
new Ii();
class bd extends Ns {
  constructor(t2, e2 = null) {
    super(), this.object = t2, this.domElement = e2, this.enabled = true, this.state = -1, this.keys = {}, this.mouseButtons = { LEFT: null, MIDDLE: null, RIGHT: null }, this.touches = { ONE: null, TWO: null };
  }
  connect() {
  }
  disconnect() {
  }
  dispose() {
  }
  update() {
  }
}
function vd(t2, e2, s2, i2) {
  const r2 = function(t3) {
    switch (t3) {
      case Tt:
      case zt:
        return { byteLength: 1, components: 1 };
      case It:
      case Ct:
      case Et:
        return { byteLength: 2, components: 1 };
      case Pt:
      case Ot:
        return { byteLength: 2, components: 4 };
      case kt:
      case Bt:
      case Rt:
        return { byteLength: 4, components: 1 };
      case Nt:
        return { byteLength: 4, components: 3 };
    }
    throw new Error(`Unknown texture type ${t3}.`);
  }(i2);
  switch (s2) {
    case 1021:
    case 1024:
      return t2 * e2;
    case 1025:
      return t2 * e2 * 2;
    case qt:
    case Jt:
      return t2 * e2 / r2.components * r2.byteLength;
    case 1030:
    case 1031:
      return t2 * e2 * 2 / r2.components * r2.byteLength;
    case 1022:
      return t2 * e2 * 3 / r2.components * r2.byteLength;
    case Wt:
    case 1033:
      return t2 * e2 * 4 / r2.components * r2.byteLength;
    case 33776:
    case 33777:
      return Math.floor((t2 + 3) / 4) * Math.floor((e2 + 3) / 4) * 8;
    case 33778:
    case 33779:
      return Math.floor((t2 + 3) / 4) * Math.floor((e2 + 3) / 4) * 16;
    case 35841:
    case 35843:
      return Math.max(t2, 16) * Math.max(e2, 8) / 4;
    case 35840:
    case 35842:
      return Math.max(t2, 8) * Math.max(e2, 8) / 2;
    case 36196:
    case 37492:
      return Math.floor((t2 + 3) / 4) * Math.floor((e2 + 3) / 4) * 8;
    case 37496:
    case 37808:
      return Math.floor((t2 + 3) / 4) * Math.floor((e2 + 3) / 4) * 16;
    case 37809:
      return Math.floor((t2 + 4) / 5) * Math.floor((e2 + 3) / 4) * 16;
    case 37810:
      return Math.floor((t2 + 4) / 5) * Math.floor((e2 + 4) / 5) * 16;
    case 37811:
      return Math.floor((t2 + 5) / 6) * Math.floor((e2 + 4) / 5) * 16;
    case 37812:
      return Math.floor((t2 + 5) / 6) * Math.floor((e2 + 5) / 6) * 16;
    case 37813:
      return Math.floor((t2 + 7) / 8) * Math.floor((e2 + 4) / 5) * 16;
    case 37814:
      return Math.floor((t2 + 7) / 8) * Math.floor((e2 + 5) / 6) * 16;
    case 37815:
      return Math.floor((t2 + 7) / 8) * Math.floor((e2 + 7) / 8) * 16;
    case 37816:
      return Math.floor((t2 + 9) / 10) * Math.floor((e2 + 4) / 5) * 16;
    case 37817:
      return Math.floor((t2 + 9) / 10) * Math.floor((e2 + 5) / 6) * 16;
    case 37818:
      return Math.floor((t2 + 9) / 10) * Math.floor((e2 + 7) / 8) * 16;
    case 37819:
      return Math.floor((t2 + 9) / 10) * Math.floor((e2 + 9) / 10) * 16;
    case 37820:
      return Math.floor((t2 + 11) / 12) * Math.floor((e2 + 9) / 10) * 16;
    case 37821:
      return Math.floor((t2 + 11) / 12) * Math.floor((e2 + 11) / 12) * 16;
    case 36492:
    case 36494:
    case 36495:
      return Math.ceil(t2 / 4) * Math.ceil(e2 / 4) * 16;
    case 36283:
    case 36284:
      return Math.ceil(t2 / 4) * Math.ceil(e2 / 4) * 8;
    case 36285:
    case 36286:
      return Math.ceil(t2 / 4) * Math.ceil(e2 / 4) * 16;
  }
  throw new Error(`Unable to determine texture byte length for ${s2} format.`);
}
"undefined" != typeof __THREE_DEVTOOLS__ && __THREE_DEVTOOLS__.dispatchEvent(new THREEGlobals["CustomEvent"]("register", { detail: { revision: t } })), "undefined" != typeof THREEGlobals["window"] && (THREEGlobals["window"].__THREE__ ? console.warn("WARNING: Multiple instances of Three.js being imported.") : THREEGlobals["window"].__THREE__ = t);
exports.$ = $;
exports.$a = $a;
exports.$e = $e;
exports.$l = $l;
exports.$n = $n;
exports.$o = $o;
exports.$r = $r;
exports.$t = $t;
exports.A = A;
exports.Ac = Ac;
exports.Ae = Ae;
exports.Al = Al;
exports.B = B;
exports.Bl = Bl;
exports.Bt = Bt;
exports.C = C;
exports.Cc = Cc;
exports.Ce = Ce;
exports.Ci = Ci;
exports.Ct = Ct;
exports.D = D;
exports.Dn = Dn;
exports.Do = Do;
exports.Dt = Dt;
exports.E = E;
exports.Ee = Ee;
exports.Et = Et;
exports.Eu = Eu;
exports.F = F;
exports.Fa = Fa;
exports.Ft = Ft;
exports.G = G;
exports.Ge = Ge;
exports.Gi = Gi;
exports.Gs = Gs;
exports.Gt = Gt;
exports.H = H;
exports.Ha = Ha;
exports.He = He;
exports.Hn = Hn;
exports.Ht = Ht;
exports.I = I;
exports.Ic = Ic;
exports.Ii = Ii;
exports.It = It;
exports.J = J;
exports.Jn = Jn;
exports.Jt = Jt;
exports.K = K;
exports.Ke = Ke;
exports.Kt = Kt;
exports.Ku = Ku;
exports.L = L;
exports.Lo = Lo;
exports.Lt = Lt;
exports.M = M;
exports.Me = Me;
exports.Ms = Ms;
exports.Mt = Mt;
exports.N = N;
exports.Na = Na;
exports.No = No;
exports.Ns = Ns;
exports.Nt = Nt;
exports.O = O;
exports.Oa = Oa;
exports.Os = Os;
exports.Ot = Ot;
exports.P = P;
exports.Pc = Pc;
exports.Ps = Ps;
exports.Pt = Pt;
exports.Q = Q;
exports.Qe = Qe;
exports.Ql = Ql;
exports.Qs = Qs;
exports.Qt = Qt;
exports.R = R;
exports.Re = Re;
exports.Ri = Ri;
exports.Rr = Rr;
exports.Rt = Rt;
exports.S = S;
exports.Sa = Sa;
exports.Se = Se;
exports.Si = Si;
exports.Sl = Sl;
exports.Ss = Ss;
exports.T = T;
exports.Tc = Tc;
exports.Te = Te;
exports.Ti = Ti;
exports.Tt = Tt;
exports.U = U;
exports.Ua = Ua;
exports.Ue = Ue;
exports.Un = Un;
exports.Uo = Uo;
exports.Ut = Ut;
exports.V = V;
exports.Vn = Vn;
exports.Vo = Vo;
exports.Vt = Vt;
exports.W = W;
exports.We = We;
exports.Wl = Wl;
exports.Wt = Wt;
exports.X = X;
exports.Xa = Xa;
exports.Xe = Xe;
exports.Xl = Xl;
exports.Xn = Xn;
exports.Xt = Xt;
exports.Y = Y;
exports.Ya = Ya;
exports.Ye = Ye;
exports.Ys = Ys;
exports.Yt = Yt;
exports.Z = Z;
exports.Za = Za;
exports.Zc = Zc;
exports.Ze = Ze;
exports.Zl = Zl;
exports.Zs = Zs;
exports._ = _;
exports._e = _e;
exports._i = _i;
exports._l = _l;
exports._t = _t;
exports.ae = ae;
exports.ai = ai;
exports.ao = ao;
exports.b = b;
exports.bd = bd;
exports.be = be;
exports.bs = bs;
exports.bt = bt;
exports.c = c;
exports.ce = ce;
exports.cn = cn;
exports.ct = ct;
exports.d = d;
exports.dc = dc;
exports.de = de;
exports.dt = dt;
exports.e = e;
exports.ea = ea;
exports.ee = ee;
exports.ei = ei;
exports.en = en;
exports.eo = eo;
exports.et = et;
exports.f = f;
exports.fe = fe;
exports.fr = fr;
exports.fs = fs;
exports.ft = ft;
exports.g = g;
exports.ge = ge;
exports.gn = gn;
exports.gs = gs;
exports.gt = gt;
exports.gu = gu;
exports.h = h;
exports.he = he;
exports.ht = ht;
exports.i = i;
exports.ic = ic;
exports.ie = ie;
exports.it = it;
exports.j = j;
exports.jc = jc;
exports.je = je;
exports.jn = jn;
exports.js = js;
exports.jt = jt;
exports.k = k;
exports.ka = ka;
exports.kl = kl;
exports.kt = kt;
exports.l = l;
exports.le = le;
exports.lt = lt;
exports.m = m;
exports.me = me;
exports.mt = mt;
exports.n = n;
exports.ne = ne;
exports.ni = ni;
exports.no = no;
exports.nr = nr;
exports.oc = oc;
exports.oe = oe;
exports.oi = oi;
exports.p = p;
exports.pe = pe;
exports.pt = pt;
exports.q = q;
exports.qn = qn;
exports.qt = qt;
exports.r = r;
exports.ra = ra;
exports.re = re;
exports.ri = ri;
exports.ro = ro;
exports.rr = rr;
exports.rt = rt;
exports.s = s;
exports.se = se;
exports.si = si;
exports.so = so;
exports.st = st;
exports.t = t;
exports.te = te;
exports.tn = tn;
exports.to = to;
exports.tt = tt;
exports.u = u;
exports.ue = ue;
exports.ui = ui;
exports.ul = ul;
exports.ut = ut;
exports.v = v;
exports.vc = vc;
exports.vd = vd;
exports.ve = ve;
exports.vi = vi;
exports.vs = vs;
exports.w = w;
exports.we = we;
exports.wi = wi;
exports.ws = ws;
exports.wt = wt;
exports.x = x;
exports.xe = xe;
exports.xs = xs;
exports.y = y;
exports.ye = ye;
exports.yn = yn;
exports.yr = yr;
exports.yt = yt;
exports.z = z;
exports.ze = ze;
exports.zn = zn;
exports.zt = zt;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/_mpChunkDeps/three/build/three.core.min.js.map
