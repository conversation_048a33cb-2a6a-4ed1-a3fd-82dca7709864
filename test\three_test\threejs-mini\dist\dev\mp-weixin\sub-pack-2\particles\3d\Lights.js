"use strict";
require("../../chunks/three/build/three.module.min.js");
const _mpChunkDeps_three_build_three_core_min = require("../../chunks/three/build/three.core.min.js");
const SHADOW_MAP_WIDTH = 4096;
const SHADOW_MAP_HEIGHT = 2048;
class Lights extends _mpChunkDeps_three_build_three_core_min.Rr {
  constructor() {
    super();
    this.position.set(0, 500, 0);
    const ambient = new _mpChunkDeps_three_build_three_core_min.Ic(3355443);
    this.add(ambient);
    const pointLight = this.pointLight = new _mpChunkDeps_three_build_three_core_min.Ac(16777215, 1, 700, 0);
    pointLight.castShadow = true;
    pointLight.shadow.camera.near = 10;
    pointLight.shadow.camera.far = 700;
    pointLight.shadow.mapSize.width = SHADOW_MAP_WIDTH;
    pointLight.shadow.mapSize.height = SHADOW_MAP_HEIGHT;
    this.add(pointLight);
    const directionalLight = new _mpChunkDeps_three_build_three_core_min.Cc(12225419, 0.5);
    directionalLight.position.set(1, 1, 1);
    this.add(directionalLight);
    const directionalLight2 = new _mpChunkDeps_three_build_three_core_min.Cc(9157300, 0.3);
    directionalLight2.position.set(1, 1, -1);
    this.add(directionalLight2);
  }
  update(renderer) {
  }
}
exports.Lights = Lights;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/sub-pack-2/particles/3d/Lights.js.map
