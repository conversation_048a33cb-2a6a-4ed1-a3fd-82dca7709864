{"version": 3, "file": "wechat.js", "sources": ["../node_modules/@minisheep/three-platform-adapter/dist/wechat.mjs"], "sourcesContent": ["import{p as e,a as t,M as n,b as i,c as o,d as r,e as a,f as s,g as l,h as c,i as h,j as d,k as m,W as f}from\"./eventManager.mjs\";import{c as g,B as w,_ as p,a as v}from\"./Adapter.mjs\";export{b as extend,u as useExtend}from\"./Adapter.mjs\";export{base642Uint8,blobUrlPrefix,getDataURlByBlobUrl}from\"@minisheep/mini-program-polyfill-core\";function x(e,t=wx){return new Promise((n=>{setTimeout((()=>{t.createSelectorQuery().select(e).fields({node:!0,id:!0,rect:!0,size:!0}).exec((e=>{if(!e||!e[0]||e.length>1)throw new Error(\"invalid selector\");n(e[0])}))}),0)}))}const y=Date.now(),A=g(wx.getWindowInfo,wx.onWindowResize,wx.offWindowResize);const E=globalThis;\"function\"!=typeof E.Worker&&(E.Worker=f),\"object\"!=typeof E.WebAssembly&&(E.WebAssembly=WXWebAssembly);const W=new class extends w{constructor(){super(...arguments),Object.defineProperty(this,\"patchCanvas\",{enumerable:!0,configurable:!0,writable:!0,value:e}),Object.defineProperty(this,\"patchElement\",{enumerable:!0,configurable:!0,writable:!0,value:t})}getPerformance(){return{now:()=>Date.now()-y}}useCanvas(e){return p(this,arguments,void 0,(function*(e,t=wx){if(!this.document)throw new Error(\"Please use patchGlobal() before useCanvas()\");const{node:n,width:i,height:o,top:r,right:a,bottom:s,left:u}=yield x(e,t),{canvas:l,updateRectInfo:c}=this.patchCanvas(n,{width:i,height:o,top:r,right:a,bottom:s,left:u},this.document);function h(){return p(this,void 0,void 0,(function*(){const{width:n,height:i,top:o,right:r,bottom:a,left:s}=yield x(e,t);c({width:n,height:i,top:o,right:r,bottom:a,left:s})}))}const f=()=>p(this,void 0,void 0,(function*(){return yield h(),l.getBoundingClientRect()}));return{canvas:l,eventHandler:(e,t=!0,n=!1)=>{m(e,[l,t&&this.document,t&&this.window],f,!n)},requestAnimationFrame:n.requestAnimationFrame,cancelAnimationFrame:n.cancelAnimationFrame,useFrame:d(n.requestAnimationFrame,n.cancelAnimationFrame),recomputeSize:h}}))}useElement(e){return p(this,arguments,void 0,(function*(e,t=wx){if(!this.document)throw new Error(\"Please use patchGlobal() before useElement()\");const{width:n,height:i,top:o,right:r,bottom:a,left:s}=yield x(e,t),{element:u,updateRectInfo:l}=this.patchElement({},{width:n,height:i,top:o,right:r,bottom:a,left:s},this.document);function c(){return p(this,void 0,void 0,(function*(){const{width:n,height:i,top:o,right:r,bottom:a,left:s}=yield x(e,t);l({width:n,height:i,top:o,right:r,bottom:a,left:s})}))}const h=()=>p(this,void 0,void 0,(function*(){return yield c(),u.getBoundingClientRect()}));return{element:u,eventHandler:(e,t=!0,n=!1)=>{m(e,[u,t&&this.document,t&&this.window],h,!n)},recomputeSize:c}}))}getWindowInfo(){return{get innerWidth(){return A.windowInfo.windowWidth},get innerHeight(){return A.windowInfo.windowHeight},get devicePixelRatio(){return A.windowInfo.pixelRatio}}}get HTMLElement(){return n}get HTMLCanvasElement(){return i}get OffscreenCanvas(){return o}get HTMLImageElement(){return r}get Image(){return a}get ImageData(){return s}get AudioContext(){return l}get requestAnimationFrame(){return c}get cancelAnimationFrame(){return h}};v.useAdapter(W).patch(\"__THREEGlobals__\");export{W as wechat};\n"], "names": ["wx", "e", "g", "f", "w", "t", "p", "n", "i", "o", "r", "a", "s", "m", "d", "l", "c", "h", "v"], "mappings": ";;;;AAAiV,SAAS,EAAE,GAAE,IAAEA,oDAAAA,MAAG;AAAC,SAAO,IAAI,QAAS,OAAG;AAAC,eAAY,MAAI;AAAC,QAAE,oBAAmB,EAAG,OAAO,CAAC,EAAE,OAAO,EAAC,MAAK,MAAG,IAAG,MAAG,MAAK,MAAG,MAAK,KAAE,CAAC,EAAE,KAAM,CAAAC,OAAG;AAAC,YAAG,CAACA,MAAG,CAACA,GAAE,CAAC,KAAGA,GAAE,SAAO,EAAE,OAAM,IAAI,MAAM,kBAAkB;AAAE,UAAEA,GAAE,CAAC,CAAC;AAAA,MAAC,CAAC;AAAA,IAAE,GAAG,CAAC;AAAA,EAAC,CAAC;AAAE;AAAC,MAAM,IAAE,KAAK,OAAM,IAAEC,0DAAAA,EAAEF,oDAAAA,KAAG,eAAcA,oDAAAA,KAAG,gBAAeA,oDAAAA,KAAG,eAAe;AAAE,MAAM,IAAE;AAAW,cAAY,OAAO,EAAE,WAAS,EAAE,SAAOG,+DAAAA,IAAG,YAAU,OAAO,EAAE,gBAAc,EAAE,cAAY;AAAe,MAAM,IAAE,IAAI,cAAcC,0DAAAA,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,OAAO,eAAe,MAAK,eAAc,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAMH,+DAAAA,EAAC,CAAC,GAAE,OAAO,eAAe,MAAK,gBAAe,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAMI,iEAAC,CAAC;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAC,WAAM,EAAC,KAAI,MAAI,KAAK,QAAM,EAAC;AAAA,EAAC;AAAA,EAAC,UAAU,GAAE;AAAC,WAAOC,0DAAAA,EAAE,MAAK,WAAU,QAAQ,WAAUL,IAAE,IAAED,oDAAAA,MAAG;AAAC,UAAG,CAAC,KAAK,SAAS,OAAM,IAAI,MAAM,6CAA6C;AAAE,YAAK,EAAC,MAAK,GAAE,OAAM,GAAE,QAAO,GAAE,KAAI,GAAE,OAAM,GAAE,QAAO,GAAE,MAAK,EAAC,IAAE,MAAM,EAAEC,IAAE,CAAC,GAAE,EAAC,QAAO,GAAE,gBAAe,EAAC,IAAE,KAAK,YAAY,GAAE,EAAC,OAAM,GAAE,QAAO,GAAE,KAAI,GAAE,OAAM,GAAE,QAAO,GAAE,MAAK,EAAC,GAAE,KAAK,QAAQ;AAAE,eAAS,IAAG;AAAC,eAAOK,0DAAAA,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,gBAAK,EAAC,OAAMC,IAAE,QAAOC,IAAE,KAAIC,IAAE,OAAMC,IAAE,QAAOC,IAAE,MAAKC,GAAC,IAAE,MAAM,EAAEX,IAAE,CAAC;AAAE,YAAE,EAAC,OAAMM,IAAE,QAAOC,IAAE,KAAIC,IAAE,OAAMC,IAAE,QAAOC,IAAE,MAAKC,GAAC,CAAC;AAAA,QAAC,CAAC;AAAA,MAAE;AAAC,YAAM,IAAE,MAAIN,0DAAAA,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,eAAO,MAAM,EAAC,GAAG,EAAE,sBAAqB;AAAA,MAAE,CAAC;AAAG,aAAM,EAAC,QAAO,GAAE,cAAa,CAACL,IAAEI,KAAE,MAAGE,KAAE,UAAK;AAACM,uEAAAA,EAAEZ,IAAE,CAAC,GAAEI,MAAG,KAAK,UAASA,MAAG,KAAK,MAAM,GAAE,GAAE,CAACE,EAAC;AAAA,MAAC,GAAE,uBAAsB,EAAE,uBAAsB,sBAAqB,EAAE,sBAAqB,UAASO,+DAAAA,EAAE,EAAE,uBAAsB,EAAE,oBAAoB,GAAE,eAAc,EAAC;AAAA,IAAC,CAAC;AAAA,EAAE;AAAA,EAAC,WAAW,GAAE;AAAC,WAAOR,0DAAAA,EAAE,MAAK,WAAU,QAAQ,WAAUL,IAAE,IAAED,oDAAAA,MAAG;AAAC,UAAG,CAAC,KAAK,SAAS,OAAM,IAAI,MAAM,8CAA8C;AAAE,YAAK,EAAC,OAAM,GAAE,QAAO,GAAE,KAAI,GAAE,OAAM,GAAE,QAAO,GAAE,MAAK,EAAC,IAAE,MAAM,EAAEC,IAAE,CAAC,GAAE,EAAC,SAAQ,GAAE,gBAAe,EAAC,IAAE,KAAK,aAAa,CAAA,GAAG,EAAC,OAAM,GAAE,QAAO,GAAE,KAAI,GAAE,OAAM,GAAE,QAAO,GAAE,MAAK,EAAC,GAAE,KAAK,QAAQ;AAAE,eAAS,IAAG;AAAC,eAAOK,0DAAAA,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,gBAAK,EAAC,OAAMC,IAAE,QAAOC,IAAE,KAAIC,IAAE,OAAMC,IAAE,QAAOC,IAAE,MAAKC,GAAC,IAAE,MAAM,EAAEX,IAAE,CAAC;AAAE,YAAE,EAAC,OAAMM,IAAE,QAAOC,IAAE,KAAIC,IAAE,OAAMC,IAAE,QAAOC,IAAE,MAAKC,GAAC,CAAC;AAAA,QAAC,CAAC;AAAA,MAAE;AAAC,YAAM,IAAE,MAAIN,0DAAAA,EAAE,MAAK,QAAO,QAAQ,aAAW;AAAC,eAAO,MAAM,EAAC,GAAG,EAAE,sBAAqB;AAAA,MAAE,CAAC;AAAG,aAAM,EAAC,SAAQ,GAAE,cAAa,CAACL,IAAEI,KAAE,MAAGE,KAAE,UAAK;AAACM,uEAAAA,EAAEZ,IAAE,CAAC,GAAEI,MAAG,KAAK,UAASA,MAAG,KAAK,MAAM,GAAE,GAAE,CAACE,EAAC;AAAA,MAAC,GAAE,eAAc,EAAC;AAAA,IAAC,CAAC;AAAA,EAAE;AAAA,EAAC,gBAAe;AAAC,WAAM,EAAC,IAAI,aAAY;AAAC,aAAO,EAAE,WAAW;AAAA,IAAW,GAAE,IAAI,cAAa;AAAC,aAAO,EAAE,WAAW;AAAA,IAAY,GAAE,IAAI,mBAAkB;AAAC,aAAO,EAAE,WAAW;AAAA,IAAU,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAOA,+DAAAA;AAAAA,EAAC;AAAA,EAAC,IAAI,oBAAmB;AAAC,WAAOC,+DAAAA;AAAAA,EAAC;AAAA,EAAC,IAAI,kBAAiB;AAAC,WAAOC,+DAAAA;AAAAA,EAAC;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAOC,+DAAAA;AAAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAOC,+DAAAA;AAAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAOC,+DAAAA;AAAAA,EAAC;AAAA,EAAC,IAAI,eAAc;AAAC,WAAOG;EAAC;AAAA,EAAC,IAAI,wBAAuB;AAAC,WAAOC,+DAAAA;AAAAA,EAAC;AAAA,EAAC,IAAI,uBAAsB;AAAC,WAAOC,+DAAAA;AAAAA,EAAC;AAAC;AAAEC,0DAAAA,EAAE,WAAW,CAAC,EAAE,MAAM,cAAkB;", "x_google_ignoreList": [0]}