"use strict";
const _mpChunkDeps__minisheep_miniProgramPolyfillCore_dist_wechat = require("../../mini-program-polyfill-core/dist/wechat.js");
const _mpChunkDeps_eventTargetShim_index = require("../../../event-target-shim/index.js");
const c = { atob: _mpChunkDeps__minisheep_miniProgramPolyfillCore_dist_wechat.he, Event: _mpChunkDeps_eventTargetShim_index.Event, EventTarget: _mpChunkDeps_eventTargetShim_index.EventTarget, PointerEvent: _mpChunkDeps__minisheep_miniProgramPolyfillCore_dist_wechat.f, MessageEvent: _mpChunkDeps__minisheep_miniProgramPolyfillCore_dist_wechat.y, Blob: _mpChunkDeps__minisheep_miniProgramPolyfillCore_dist_wechat.C }, l = Object.assign({}, c);
function u(e) {
  if (l.hasOwnProperty(e)) return l[e];
  throw new Error(`could not find extend "${e}", please extend it by "extend(${e})"`);
}
function h(e, t, n, r) {
  return new (n || (n = Promise))(function(i, a) {
    function o(e2) {
      try {
        l2(r.next(e2));
      } catch (e3) {
        a(e3);
      }
    }
    function c2(e2) {
      try {
        l2(r.throw(e2));
      } catch (e3) {
        a(e3);
      }
    }
    function l2(e2) {
      var t2;
      e2.done ? i(e2.value) : (t2 = e2.value, t2 instanceof n ? t2 : new n(function(e3) {
        e3(t2);
      })).then(o, c2);
    }
    l2((r = r.apply(e, t || [])).next());
  });
}
function d(e, t, n, r) {
  if ("function" == typeof t ? e !== t || !r : !t.has(e)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
  return "m" === n ? r : "a" === n ? r.call(e) : r ? r.value : t.get(e);
}
"function" == typeof SuppressedError && SuppressedError;
const m = {}, f = { debug: false, defaultCanvasContextType: "2d" }, b = new Proxy({ set(e, t, n = false) {
  n ? (Object.hasOwn(m, e) && console.warn(`once change envParameter [${e}] is already set, it will be overwritten.`), m[e] = t) : (Object.hasOwn(m, e) && (console.warn(`once change envParameter [${e}] is already set, it will be delete.`), delete m[e]), f[e] = t);
} }, { get(e, t, n) {
  if (Reflect.has(e, t)) return Reflect.get(e, t, n);
  if (Reflect.has(f, t)) {
    if (Object.hasOwn(m, t)) {
      const e2 = m[t];
      return delete m[t], e2;
    }
    return Reflect.get(f, t, n);
  }
} });
function g() {
}
function w(...e) {
  b.debug && console.log(...e);
}
function v(e) {
  return h(this, arguments, void 0, function* (e2, t = "image/png", n = 0.92) {
    const r = e2.toDataURL(t, n).match(/data:(.*?);base64,(.*)/);
    if (r) {
      const e3 = u("Blob"), t2 = r[1], n2 = r[2];
      return Promise.resolve(new e3([_mpChunkDeps__minisheep_miniProgramPolyfillCore_dist_wechat.R(n2)], { type: t2 }));
    }
    return Promise.resolve(null);
  });
}
function p(e, t, n) {
  return n.has(t) || e.prototype.isPrototypeOf(t);
}
function y(e, t, n) {
  const r = Object.assign({}, e()), i = () => {
    Object.assign(r, e());
  };
  return t(i), { windowInfo: r, stopWatch() {
    n(i);
  } };
}
const E = (() => {
  const e = u("EventTarget"), t = /* @__PURE__ */ new WeakSet();
  try {
    const n = e[Symbol.hasInstance];
    e[Symbol.hasInstance] = (r) => p(e, r, t) || (null == n ? void 0 : n(r));
  } catch (e2) {
  }
  return function(n) {
    const r = new e(), i = /* @__PURE__ */ new WeakMap();
    return Object.defineProperties(n, { addEventListener: { value: function() {
      if ("function" == typeof arguments[1]) {
        const e2 = arguments[1].bind(n);
        i.set(arguments[1], e2), arguments[1] = e2;
      }
      r.addEventListener.call(r, arguments[0], arguments[1], arguments[2]);
    }, writable: false }, removeEventListener: { value: function() {
      "function" == typeof arguments[1] && i.has(arguments[1]) && (arguments[1] = i.get(arguments[1])), r.removeEventListener.call(r, arguments[0], arguments[1], arguments[2]);
    }, writable: false }, dispatchEvent: { value: r.dispatchEvent.bind(r), writable: false } }), t.add(n), n;
  };
})(), O = /* @__PURE__ */ new WeakSet();
class P {
  static [Symbol.hasInstance](e) {
    return p(this, e, O);
  }
  constructor() {
    throw new TypeError("Illegal constructor");
  }
}
function j(e) {
  O.add(e);
}
class x {
  get top() {
    return this.y;
  }
  get left() {
    return this.x;
  }
  get right() {
    return this.x + this.width;
  }
  get bottom() {
    return this.y + this.height;
  }
  constructor(e = 0, t = 0, n = 0, r = 0) {
    Object.defineProperty(this, "x", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "y", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "width", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "height", { enumerable: true, configurable: true, writable: true, value: void 0 }), this.x = e, this.y = t, this.width = n, this.height = r;
  }
  toJSON() {
    return JSON.parse(JSON.stringify(this));
  }
  static fromRect(e) {
    return new x(null == e ? void 0 : e.x, null == e ? void 0 : e.y, null == e ? void 0 : e.width, null == e ? void 0 : e.height);
  }
}
class A {
  constructor() {
    Object.defineProperty(this, "document", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "window", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "defaultCanvas", { enumerable: true, configurable: true, writable: true, value: void 0 });
  }
  init() {
  }
  patchGlobal(e) {
    const t = this.getWindowInfo(), n = { HTMLElement: this.HTMLElement, OffscreenCanvas: this.OffscreenCanvas, HTMLCanvasElement: this.HTMLCanvasElement, HTMLImageElement: this.HTMLImageElement, Image: this.Image, AudioContext: this.AudioContext, ImageData: this.ImageData, WebGL2RenderingContext: this.WebGL2RenderingContext, DOMRect: this.DOMRect, get innerWidth() {
      return t.innerWidth;
    }, get innerHeight() {
      return t.innerHeight;
    }, get devicePixelRatio() {
      return t.devicePixelRatio;
    }, requestAnimationFrame: this.requestAnimationFrame, cancelAnimationFrame: this.cancelAnimationFrame, atob: u("atob"), performance: this.getPerformance(), pageXOffset: 0, pageYOffset: 0, getComputedStyle: () => ({}), screen: E({ get availHeight() {
      return t.innerHeight;
    }, get availWidth() {
      return t.innerWidth;
    }, get height() {
      return t.innerHeight;
    }, get width() {
      return t.innerWidth;
    }, colorDepth: 24, pixelDepth: 24 }) };
    Object.assign(e, n), E(e);
    try {
      e.window = e;
    } catch (e2) {
    }
    this.window = e;
    try {
      const n2 = this.document || {};
      this.document = n2, e.document = n2, E(this.document);
      const r = this.createElement.bind(this);
      Object.defineProperties(this.document, { createElement: { get: () => r }, createElementNS: { get: () => (e2, t2) => r(t2) }, body: { value: E({}) }, documentElement: { value: { clientLeft: 0, clientTop: 0, get clientWidth() {
        return t.innerWidth;
      }, get clientHeight() {
        return t.innerHeight;
      } } } });
    } catch (t2) {
      if (e.document) {
        this.document = e.document;
        const t3 = this.document.createElement || this.createElement.bind(this);
        Object.defineProperties(this.document, { createElement: { get: () => t3 }, createElementNS: { get: () => (e2, n2) => t3.call(this.document, n2) } });
      } else console.error("patchGlobal fail, error:", t2);
    }
    return e;
  }
  createElement(e) {
    switch (e) {
      case "canvas":
        return new this.OffscreenCanvas(1, 1);
      case "img":
        return new this.Image();
      default:
        return console.warn(`createElement("${e}") is not implemented in mini-program`), {};
    }
  }
  get WebGL2RenderingContext() {
    return P;
  }
  get DOMRect() {
    return x;
  }
}
class T extends A {
  constructor() {
    super(...arguments), Object.defineProperty(this, "window", { enumerable: true, configurable: true, writable: true, value: window }), Object.defineProperty(this, "document", { enumerable: true, configurable: true, writable: true, value: document }), Object.defineProperty(this, "patchElement", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "patchCanvas", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "createElement", { enumerable: true, configurable: true, writable: true, value: document.createElement.bind(document) }), Object.defineProperty(this, "HTMLElement", { enumerable: true, configurable: true, writable: true, value: HTMLElement }), Object.defineProperty(this, "HTMLCanvasElement", { enumerable: true, configurable: true, writable: true, value: HTMLCanvasElement }), Object.defineProperty(this, "OffscreenCanvas", { enumerable: true, configurable: true, writable: true, value: OffscreenCanvas }), Object.defineProperty(this, "HTMLImageElement", { enumerable: true, configurable: true, writable: true, value: HTMLImageElement }), Object.defineProperty(this, "Image", { enumerable: true, configurable: true, writable: true, value: Image }), Object.defineProperty(this, "ImageData", { enumerable: true, configurable: true, writable: true, value: ImageData }), Object.defineProperty(this, "AudioContext", { enumerable: true, configurable: true, writable: true, value: AudioContext }), Object.defineProperty(this, "requestAnimationFrame", { enumerable: true, configurable: true, writable: true, value: requestAnimationFrame }), Object.defineProperty(this, "cancelAnimationFrame", { enumerable: true, configurable: true, writable: true, value: cancelAnimationFrame });
  }
  patchGlobal(e) {
    return window;
  }
  getPerformance() {
    return performance;
  }
  useCanvas(e) {
    const t = (() => {
      const t2 = document.querySelector(e);
      return t2 && t2 instanceof HTMLCanvasElement ? t2 : document.querySelector(`${e} canvas`);
    })();
    if (!t) throw new Error("invalid selector");
    const n = t.getBoundingClientRect();
    return t.width !== n.width && (t.width = n.width), t.height !== n.height && (t.height = n.height), Promise.resolve({ canvas: t, requestAnimationFrame, cancelAnimationFrame, useFrame: (e2) => {
      let t2, n2;
      const r = (i) => {
        if (void 0 === t2) t2 = i || performance.now();
        else {
          const n3 = i || performance.now(), r2 = n3 - t2;
          t2 = n3, e2(r2);
        }
        n2 = requestAnimationFrame(r);
      };
      return n2 = requestAnimationFrame(r), { cancel: () => cancelAnimationFrame(n2) };
    }, recomputeSize: () => Promise.resolve(), eventHandler(e2, t2 = true, n2 = false) {
    } });
  }
  useElement(e) {
    return Promise.resolve({ element: document.querySelector(e), recomputeSize: () => Promise.resolve(), eventHandler(e2, t = true) {
    } });
  }
  getWindowInfo() {
    return { devicePixelRatio: window.devicePixelRatio, innerHeight: window.innerHeight, innerWidth: window.innerWidth };
  }
}
const H = globalThis.window && "HTMLElement" in globalThis.window, C = Symbol("PatchedSymbol"), L = new Proxy({ currentAdapter: void 0, useAdapter(e) {
  var t;
  return this.currentAdapter = e, null === (t = e.init) || void 0 === t || t.call(e), this;
}, patch(e) {
  var t;
  const n = this.getExistAdapter();
  if ("string" == typeof e && (H && (globalThis[e] = globalThis), !(e = globalThis[e]))) throw new Error(`invalid patch target: ${e}`);
  if (e[C]) throw new Error(`cannot patch ${e} twice`);
  return null === (t = n.patchGlobal) || void 0 === t || t.call(n, e), e[C] = true, this;
}, getExistAdapter() {
  if (!this.currentAdapter) {
    if (H) return new T();
    throw new Error("Must call use() to set a platform adapter.");
  }
  return this.currentAdapter;
} }, { get(e, t, n) {
  if (Reflect.has(e, t)) return Reflect.get(e, t, n);
  {
    const n2 = e.getExistAdapter(), r = Reflect.get(n2, t);
    return "function" != typeof r || r.toString().startsWith("class") ? r : r.bind(n2);
  }
} });
H && L.patch("THREEGlobals");
exports.A = A;
exports.E = E;
exports.L = L;
exports.b = b;
exports.d = d;
exports.g = g;
exports.h = h;
exports.j = j;
exports.p = p;
exports.u = u;
exports.v = v;
exports.w = w;
exports.y = y;
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/_mpChunkDeps/@minisheep/three-platform-adapter/dist/Adapter.js.map
