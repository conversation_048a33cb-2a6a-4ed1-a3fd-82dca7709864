const __workerAdapter = require("worker-adapter.js");
const MessageData = __workerAdapter.MessageData
self = __workerAdapter.proxySelf
const WebAssembly = __workerAdapter.WebAssembly
"use strict";function e(e,t,n,r,a,s,o){try{var i=e[s](o),c=i.value}catch(e){return void n(e)}i.done?t(c):Promise.resolve(c).then(r,a)}var t,n,r=new Promise((e=>{n=e})).then((e=>WebAssembly.instantiate(e,{}))).then((function(e){(t=e.instance).exports.__wasm_call_ctors()}));self.addEventListener("message",function(){var a,s=(a=function*(e){var{type:a,data:s,wasmPath:o}=e.data;switch(a){case"init":n(o);break;case"decode":try{yield r;var i=new Uint8Array(s.count*s.size);!function(e,n,r,a,s,o){var i=t.exports.sbrk,c=r+3&-4,u=i(c*a),f=i(s.length),l=new Uint8Array(t.exports.memory.buffer);l.set(s,f);var d=e(u,r,a,f,s.length);if(0==d&&o&&o(u,c,a),n.set(l.subarray(u,u+r*a)),i(u-i(0)),0!=d)throw new Error("Malformed buffer data: "+d)}(t.exports[s.mode],i,s.count,s.size,s.source,t.exports[s.filter]),self.postMessage({id:s.id,count:s.count,action:"resolve",value:i})}catch(e){self.postMessage({id:s.id,count:s.count,action:"reject",value:e})}}},function(){var t=this,n=arguments;return new Promise((function(r,s){var o=a.apply(t,n);function i(t){e(o,r,s,i,c,"next",t)}function c(t){e(o,r,s,i,c,"throw",t)}i(void 0)}))});return function(e){return s.apply(this,arguments)}}());
