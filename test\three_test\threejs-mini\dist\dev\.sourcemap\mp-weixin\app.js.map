{"version": 3, "file": "app.js", "sources": ["App.vue"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport { onLaunch, onShow, onHide } from \"@dcloudio/uni-app\";\r\nonLaunch(() => {\r\n  uni.__f__('log','at App.vue:4',\"App Launch\");\r\n});\r\nonShow(() => {\r\n  uni.__f__('log','at App.vue:7',\"App Show\");\r\n});\r\nonHide(() => {\r\n  uni.__f__('log','at App.vue:10',\"App Hide\");\r\n});\r\n</script>\r\n<style>\r\n.page-container{\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n}\r\n</style>\r\n"], "names": ["onLaunch", "uni", "onShow", "onHide"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEAA,iDAAAA,SAAS,MAAM;AACbC,0DAAAA,MAAI,MAAM,OAAM,gBAAe,YAAY;AAAA,IAC7C,CAAC;AACDC,iDAAAA,OAAO,MAAM;AACXD,0DAAAA,MAAI,MAAM,OAAM,gBAAe,UAAU;AAAA,IAC3C,CAAC;AACDE,iDAAAA,OAAO,MAAM;AACXF,0DAAAA,MAAI,MAAM,OAAM,iBAAgB,UAAU;AAAA,IAC5C,CAAC;;;;;;;;;;;;;"}