<template>
    <!-- #ifndef H5 -->
    <canvas
        class="platform-canvas"
        :type="type"
        :id="canvasId"
        @touchstart="defaultHandler"
        @touchmove="defaultHandler"
        @touchcancel="defaultHandler"
        @touchend="defaultHandler"
        @tap="defaultHandler"
        disable-scroll
    >
    </canvas>
    <!-- #endif -->
    <!-- #ifdef H5 -->
    <canvas
        class="platform-canvas"
        :id="canvasId"
        @touchstart.stop="defaultHandler"
        @touchmove.stop="defaultHandler"
        @touchcancel.stop="defaultHandler"
        @touchend.stop="defaultHandler"
        @click.stop="defaultHandler"
    />
    <!-- #endif -->
</template>

<script setup>
import { getCurrentInstance, onMounted } from 'vue';

const props = defineProps({
  type: {
    type: String,
    default: 'webgl'
  },
  canvasId: {
    type: String,
    required: true
  }
});

const emit = defineEmits([
  'touchstart',
  'touchmove', 
  'touchend',
  'tap',
  'touchcancel',
  'dispatch',
  'useCanvas'
]);

let additionHandler = () => {};

const defaultHandler = (e) => {
  if (e.type === 'click') {
    e = {
      ...e,
      type: 'tap'
    };
  }
  emit(e.type, e);
  emit('dispatch', e);
  additionHandler?.(e);
};

const instance = getCurrentInstance();

onMounted(() => {
  // 简化版的canvas获取逻辑
  const query = uni.createSelectorQuery().in(instance.ctx);
  query.select(`#${props.canvasId}`).fields({
    node: true,
    size: true
  }).exec((res) => {
    if (res[0] && res[0].node) {
      const canvas = res[0].node;
      const systemInfo = uni.getSystemInfoSync();
      
      // 设置canvas尺寸
      canvas.width = systemInfo.windowWidth * (systemInfo.pixelRatio || 1);
      canvas.height = systemInfo.windowHeight * 0.8 * (systemInfo.pixelRatio || 1);
      
      // 获取WebGL上下文
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      
      if (gl) {
        const result = {
          canvas: canvas,
          gl: gl,
          width: canvas.width,
          height: canvas.height,
          devicePixelRatio: systemInfo.pixelRatio || 1,
          eventHandler: (e, preventDefault = true) => {
            if (preventDefault && e.preventDefault) {
              e.preventDefault();
            }
          },
          useFrame: (callback) => {
            const animate = () => {
              callback();
              setTimeout(animate, 16);
            };
            animate();
          },
          recomputeSize: () => {
            return Promise.resolve({
              width: canvas.width,
              height: canvas.height
            });
          }
        };
        
        additionHandler = (e) => {
          result.eventHandler(e, false);
        };
        
        emit('useCanvas', result);
      } else {
        console.error('无法获取WebGL上下文');
      }
    }
  });
});
</script>

<style scoped>
.platform-canvas {
  width: 100%;
  height: 100%;
}
</style>
