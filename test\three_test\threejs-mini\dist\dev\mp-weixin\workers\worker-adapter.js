"use strict";function e(t){switch(typeof t){case"object":if("Object"===t.constructor.name){if(2===Object.keys(t).length&&"__typedArray"in t&&"buffer"in t)return new globalThis[t.__typedArray](t.buffer);Object.entries(t).forEach((r=>{var[n,a]=r;t[n]=e(a)}))}else t instanceof Array&&t.forEach(((r,n)=>{t[n]=e(r)}));return t;case"string":if(t.startsWith("__bigint")){var r=t.slice(9);return BigInt(r)}if(t.startsWith("__symbol")){var n=t.slice(9);return Symbol.for(n)||Symbol(n)}return t.startsWith("__arraybuffer")?new ArrayBuffer(0):t;default:return t}}function t(e){switch(typeof e){case"bigint":return"__bigint:".concat(e.toString());case"object":return null===e?null:e instanceof ArrayBuffer?0===e.byteLength?"__arraybuffer:0":e:ArrayBuffer.isView(e)?((0!==e.byteOffset||e.byteLength!==e.buffer.byteLength)&&(e=e instanceof DataView?new DataView(e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength),0,e.byteLength):e.slice()),{buffer:e.buffer,__typedArray:e.constructor.name}):(Object.entries(e).forEach((r=>{var[n,a]=r;e[n]=t(a)})),e);case"symbol":return"__symbol:".concat(Symbol.keyFor(e)||e.toString());case"function":return;case"number":case"string":case"undefined":case"boolean":return e}}var r="__message_data_no_transfer";class MessageData{static toRaw(t){try{return"object"==typeof t&&r in t?(delete t[r],t):e(t)}finally{}}static format(e){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{return"object"==typeof e?n?t(e):(e[r]=!0,e):t(e)}finally{}}}function n(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach((function(t){n(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function i(e,t){if(!e){for(var r=arguments.length,n=new Array(r>2?r-2:0),a=2;a<r;a++)n[a-2]=arguments[a];throw new TypeError(s(t,n))}}function s(e,t){var r=0;return e.replace(/%[os]/g,(()=>l(t[r++])))}function l(e){return"object"!=typeof e||null===e?String(e):Object.prototype.toString.call(e)}var c="undefined"!=typeof window?window:"undefined"!=typeof self?self:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:void 0;class Warning{constructor(e,t){this.code=e,this.message=t}warn(){var e;try{for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var a=(null!==(e=(new Error).stack)&&void 0!==e?e:"").replace(/^(?:(?:[\0-\t\x0B\f\x0E-\u2027\u202A-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+?\n){2}/g,"\n");console.warn(this.message,...r,a)}catch(e){}}}var u=new Warning("W01","Unable to initialize event under dispatching."),p=new Warning("W02","Assigning any falsy value to 'cancelBubble' property has no effect."),g=new Warning("W03","Assigning any truthy value to 'returnValue' property has no effect."),f=new Warning("W04","Unable to preventDefault on non-cancelable events."),b=new Warning("W05","Unable to preventDefault inside passive event listener invocation."),v=new Warning("W06","An event listener wasn't added because it has been added already: %o, %o"),d=new Warning("W07","The %o option value was abandoned because the event listener wasn't added as duplicated."),h=new Warning("W08","The 'callback' argument must be a function or an object that has 'handleEvent' method: %o");class Event{static get NONE(){return E}static get CAPTURING_PHASE(){return m}static get AT_TARGET(){return _}static get BUBBLING_PHASE(){return w}constructor(e,t){Object.defineProperty(this,"isTrusted",{value:!1,enumerable:!0});var r=null!=t?t:{};O.set(this,{type:String(e),bubbles:Boolean(r.bubbles),cancelable:Boolean(r.cancelable),composed:Boolean(r.composed),target:null,currentTarget:null,stopPropagationFlag:!1,stopImmediatePropagationFlag:!1,canceledFlag:!1,inPassiveListenerFlag:!1,dispatchFlag:!1,timeStamp:Date.now()})}get type(){return P(this).type}get target(){return P(this).target}get srcElement(){return P(this).target}get currentTarget(){return P(this).currentTarget}composedPath(){var e=P(this).currentTarget;return e?[e]:[]}get NONE(){return E}get CAPTURING_PHASE(){return m}get AT_TARGET(){return _}get BUBBLING_PHASE(){return w}get eventPhase(){return P(this).dispatchFlag?2:0}stopPropagation(){P(this).stopPropagationFlag=!0}get cancelBubble(){return P(this).stopPropagationFlag}set cancelBubble(e){e?P(this).stopPropagationFlag=!0:p.warn()}stopImmediatePropagation(){var e=P(this);e.stopPropagationFlag=e.stopImmediatePropagationFlag=!0}get bubbles(){return P(this).bubbles}get cancelable(){return P(this).cancelable}get returnValue(){return!P(this).canceledFlag}set returnValue(e){e?g.warn():R(P(this))}preventDefault(){R(P(this))}get defaultPrevented(){return P(this).canceledFlag}get composed(){return P(this).composed}get isTrusted(){return!1}get timeStamp(){return P(this).timeStamp}initEvent(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=P(this);n.dispatchFlag?u.warn():O.set(this,o(o({},n),{},{type:String(e),bubbles:Boolean(t),cancelable:Boolean(r),target:null,currentTarget:null,stopPropagationFlag:!1,stopImmediatePropagationFlag:!1,canceledFlag:!1}))}}var E=0,m=1,_=2,w=3,O=new WeakMap;function P(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"this",r=O.get(e);return i(null!=r,"'%s' must be an object that Event constructor created, but got another one: %o",t,e),r}function R(e){e.inPassiveListenerFlag?b.warn():e.cancelable?e.canceledFlag=!0:f.warn()}Object.defineProperty(Event,"NONE",{enumerable:!0}),Object.defineProperty(Event,"CAPTURING_PHASE",{enumerable:!0}),Object.defineProperty(Event,"AT_TARGET",{enumerable:!0}),Object.defineProperty(Event,"BUBBLING_PHASE",{enumerable:!0});for(var T,A=Object.getOwnPropertyNames(Event.prototype),j=0;j<A.length;++j)"constructor"!==A[j]&&Object.defineProperty(Event.prototype,A[j],{enumerable:!0});void 0!==c&&void 0!==c.Event&&Object.setPrototypeOf(Event.prototype,c.Event.prototype);var D={INDEX_SIZE_ERR:1,DOMSTRING_SIZE_ERR:2,HIERARCHY_REQUEST_ERR:3,WRONG_DOCUMENT_ERR:4,INVALID_CHARACTER_ERR:5,NO_DATA_ALLOWED_ERR:6,NO_MODIFICATION_ALLOWED_ERR:7,NOT_FOUND_ERR:8,NOT_SUPPORTED_ERR:9,INUSE_ATTRIBUTE_ERR:10,INVALID_STATE_ERR:11,SYNTAX_ERR:12,INVALID_MODIFICATION_ERR:13,NAMESPACE_ERR:14,INVALID_ACCESS_ERR:15,VALIDATION_ERR:16,TYPE_MISMATCH_ERR:17,SECURITY_ERR:18,NETWORK_ERR:19,ABORT_ERR:20,URL_MISMATCH_ERR:21,QUOTA_EXCEEDED_ERR:22,TIMEOUT_ERR:23,INVALID_NODE_TYPE_ERR:24,DATA_CLONE_ERR:25};function F(e){for(var t=Object.keys(D),r=function(){var r=t[n],a=D[r];Object.defineProperty(e,r,{get:()=>a,configurable:!0,enumerable:!0})},n=0;n<t.length;++n)r()}class EventWrapper extends Event{static wrap(e){return new(B(e))(e)}constructor(e){super(e.type,{bubbles:e.bubbles,cancelable:e.cancelable,composed:e.composed}),e.cancelBubble&&super.stopPropagation(),e.defaultPrevented&&super.preventDefault(),S.set(this,{original:e});for(var t=Object.keys(e),r=0;r<t.length;++r){var n=t[r];n in this||Object.defineProperty(this,n,N(e,n))}}stopPropagation(){super.stopPropagation();var{original:e}=I(this);"stopPropagation"in e&&e.stopPropagation()}get cancelBubble(){return super.cancelBubble}set cancelBubble(e){super.cancelBubble=e;var{original:t}=I(this);"cancelBubble"in t&&(t.cancelBubble=e)}stopImmediatePropagation(){super.stopImmediatePropagation();var{original:e}=I(this);"stopImmediatePropagation"in e&&e.stopImmediatePropagation()}get returnValue(){return super.returnValue}set returnValue(e){super.returnValue=e;var{original:t}=I(this);"returnValue"in t&&(t.returnValue=e)}preventDefault(){super.preventDefault();var{original:e}=I(this);"preventDefault"in e&&e.preventDefault()}get timeStamp(){var{original:e}=I(this);return"timeStamp"in e?e.timeStamp:super.timeStamp}}var S=new WeakMap;function I(e){var t=S.get(e);return i(null!=t,"'this' is expected an Event object, but got",e),t}var W=new WeakMap;function B(e){var t=Object.getPrototypeOf(e);if(null==t)return EventWrapper;var r=W.get(t);return null==r&&(r=function(e,t){class CustomEventWrapper extends e{}for(var r=Object.keys(t),n=0;n<r.length;++n)Object.defineProperty(CustomEventWrapper.prototype,r[n],N(t,r[n]));return CustomEventWrapper}(B(t),t),W.set(t,r)),r}function N(e,t){var r=Object.getOwnPropertyDescriptor(e,t);return{get(){var e=I(this).original,r=e[t];return"function"==typeof r?r.bind(e):r},set(e){I(this).original[t]=e},configurable:r.configurable,enumerable:r.enumerable}}function L(e){return!(1&~e.flags)}function k(e){return!(2&~e.flags)}function C(e){return!(4&~e.flags)}function M(e){return!(8&~e.flags)}function x(e,t,r){var{callback:n}=e;try{"function"==typeof n?n.call(t,r):"function"==typeof n.handleEvent&&n.handleEvent(r)}catch(e){!function(e){try{var t=e instanceof Error?e:new Error(l(e));if("function"==typeof dispatchEvent&&"function"==typeof ErrorEvent)dispatchEvent(new ErrorEvent("error",{error:t,message:t.message}));else if("undefined"!=typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",t);console.error(t)}catch(e){}}(e)}}function U(e,t,r){for(var{listeners:n}=e,a=0;a<n.length;++a)if(n[a].callback===t&&L(n[a])===r)return a;return-1}function G(e,t,r){var n=U(e,t,r);return-1!==n&&V(e,n)}function V(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=e.listeners[t];return function(e){e.flags|=8}(n),n.signal&&n.signal.removeEventListener("abort",n.signalListener),e.cow&&!r?(e.cow=!1,e.listeners=e.listeners.filter(((e,r)=>r!==t)),!1):(e.listeners.splice(t,1),!0)}W.set(Object.prototype,EventWrapper),void 0!==c&&void 0!==c.Event&&W.set(c.Event.prototype,EventWrapper);class EventTarget{constructor(){H.set(this,Object.create(null))}addEventListener(e,t,r){var n=X(this),{callback:a,capture:o,once:i,passive:s,signal:l,type:c}=function(e,t,r){var n;if(Y(t),"object"==typeof r&&null!==r)return{type:String(e),callback:null!=t?t:void 0,capture:Boolean(r.capture),passive:Boolean(r.passive),once:Boolean(r.once),signal:null!==(n=r.signal)&&void 0!==n?n:void 0};return{type:String(e),callback:null!=t?t:void 0,capture:Boolean(r),passive:!1,once:!1,signal:void 0}}(e,t,r);if(null!=a&&!(null==l?void 0:l.aborted)){var u=function(e,t){var r;return null!==(r=e[t])&&void 0!==r?r:e[t]={attrCallback:void 0,attrListener:void 0,cow:!1,listeners:[]}}(n,c),p=U(u,a,o);-1===p?function(e,t,r,n,a,o){var i;o&&(i=G.bind(null,e,t,r),o.addEventListener("abort",i));var s=function(e,t,r,n,a,o){return{callback:e,flags:(t?1:0)|(r?2:0)|(n?4:0),signal:a,signalListener:o}}(t,r,n,a,o,i);e.cow?(e.cow=!1,e.listeners=[...e.listeners,s]):e.listeners.push(s)}(u,a,o,s,i,l):function(e,t,r,n){v.warn(L(e)?"capture":"bubble",e.callback),k(e)!==t&&d.warn("passive");C(e)!==r&&d.warn("once");e.signal!==n&&d.warn("signal")}(u.listeners[p],s,i,l)}}removeEventListener(e,t,r){var n=X(this),{callback:a,capture:o,type:i}=function(e,t,r){if(Y(t),"object"==typeof r&&null!==r)return{type:String(e),callback:null!=t?t:void 0,capture:Boolean(r.capture)};return{type:String(e),callback:null!=t?t:void 0,capture:Boolean(r)}}(e,t,r),s=n[i];null!=a&&s&&G(s,a,o)}dispatchEvent(e){var t=X(this)[String(e.type)];if(null==t)return!0;var r,n=e instanceof Event?e:EventWrapper.wrap(e),a=P(n,"event");if(a.dispatchFlag)throw r="This event has been in dispatching.",c.DOMException?new c.DOMException(r,"InvalidStateError"):(null==T&&(T=class DOMException extends Error{constructor(e){super(e),Error.captureStackTrace&&Error.captureStackTrace(this,DOMException)}get code(){return 11}get name(){return"InvalidStateError"}},Object.defineProperties(T.prototype,{code:{enumerable:!0},name:{enumerable:!0}}),F(T),F(T.prototype)),new T(r));if(a.dispatchFlag=!0,a.target=a.currentTarget=this,!a.stopPropagationFlag){var{cow:o,listeners:i}=t;t.cow=!0;for(var s=0;s<i.length;++s){var l=i[s];if(!M(l)&&(C(l)&&V(t,s,!o)&&(s-=1),a.inPassiveListenerFlag=k(l),x(l,this,n),a.inPassiveListenerFlag=!1,a.stopImmediatePropagationFlag))break}o||(t.cow=!1)}return a.target=null,a.currentTarget=null,a.stopImmediatePropagationFlag=!1,a.stopPropagationFlag=!1,a.dispatchFlag=!1,!a.canceledFlag}}var H=new WeakMap;function X(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"this",r=H.get(e);return i(null!=r,"'%s' must be an object that EventTarget constructor created, but got another one: %o",t,e),r}function Y(e){if("function"!=typeof e&&("object"!=typeof e||null===e||"function"!=typeof e.handleEvent)){if(null!=e&&"object"!=typeof e)throw new TypeError(s(h.message,[e]));h.warn(e)}}for(var K=Object.getOwnPropertyNames(EventTarget.prototype),Q=0;Q<K.length;++Q)"constructor"!==K[Q]&&Object.defineProperty(EventTarget.prototype,K[Q],{enumerable:!0});function Z(e,t){const r="undefined"!=typeof globalThis&&globalThis||void 0!==__sharedGlobals.self&&__sharedGlobals.self||"undefined"!=typeof global&&global||{};r.hasOwnProperty(e)||(r[e]=t)}void 0!==c&&void 0!==c.EventTarget&&Object.setPrototypeOf(EventTarget.prototype,c.EventTarget.prototype),globalThis.__sharedGlobals=(()=>{if(globalThis.__sharedGlobals){if(!globalThis.__sharedGlobals.__prefix_global__)throw new Error("[plugin:prefix-global] inject global property fail, key [__sharedGlobals] is already defined.");return globalThis.__sharedGlobals}const e=JSON.parse("[]"),t=new Proxy({__prefix_global__:!0},{get:(r,n)=>e.includes(n)?void 0:Object.hasOwn(r,n)?Reflect.get(r,n):"self"===n?t:Reflect.get(globalThis,n),ownKeys:e=>Array.from(new Set([Reflect.ownKeys(e),Reflect.ownKeys(globalThis)].flat())),has:(e,t)=>Reflect.has(e,t)||Reflect.has(globalThis,t)});return t})();class y extends Event{constructor(e,t={}){const{data:r,lastEventId:n,origin:a,ports:o,source:i,...s}=t;super(e,s),Object.defineProperty(this,"data",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"lastEventId",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"origin",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"ports",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"source",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.data=r,this.lastEventId=n||"",this.origin=a||"",this.ports=o||[],this.source=i||null}}Z("TextDecoder",class ie{decode(e){let t;t=e instanceof ArrayBuffer?new Uint8Array(e):e;const r=t.length,n=[];let a=0;for(;a<r;){const e=t[a];let o=null,i=e>239?4:e>223?3:e>191?2:1;if(a+i<=r){let r,n,s,l;switch(i){case 1:e<128&&(o=e);break;case 2:r=t[a+1],128==(192&r)&&(l=(31&e)<<6|63&r,l>127&&(o=l));break;case 3:r=t[a+1],n=t[a+2],128==(192&r)&&128==(192&n)&&(l=(15&e)<<12|(63&r)<<6|63&n,l>2047&&(l<55296||l>57343)&&(o=l));break;case 4:r=t[a+1],n=t[a+2],s=t[a+3],128==(192&r)&&128==(192&n)&&128==(192&s)&&(l=(15&e)<<18|(63&r)<<12|(63&n)<<6|63&s,l>65535&&l<1114112&&(o=l))}}null===o?(o=65533,i=1):o>65535&&(o-=65536,n.push(o>>>10&1023|55296),o=56320|1023&o),n.push(o),a+=i}const o=n.length;let i="";for(a=0;a<o;)i+=String.fromCharCode(...n.slice(a,a+=4096));return i}});Z("TextEncoder",class se{encode(e){let t=0;const r=e.length,n=Uint8Array;let a=0,o=Math.max(32,r+(r>>1)+7),i=new n(o>>3<<3);for(;t<r;){let n=e.charCodeAt(t++);if(n>=55296&&n<=56319){if(t<r){const r=e.charCodeAt(t);56320==(64512&r)&&(++t,n=((1023&n)<<10)+(1023&r)+65536)}if(n>=55296&&n<=56319)continue}if(a+4>i.length){o+=8,o*=1+t/e.length*2,o=o>>3<<3;const r=new Uint8Array(o);r.set(i),i=r}if(4294967168&n){if(4294965248&n)if(4294901760&n){if(4292870144&n)continue;i[a++]=n>>18&7|240,i[a++]=n>>12&63|128,i[a++]=n>>6&63|128}else i[a++]=n>>12&15|224,i[a++]=n>>6&63|128;else i[a++]=n>>6&31|192;i[a++]=63&n|128}else i[a++]=n}return i.slice(0,a)}});var z,J,q=(z=new EventTarget,J=null,z.addEventListener=z.addEventListener.bind(z),z.removeEventListener=z.removeEventListener.bind(z),z.dispatchEvent=z.dispatchEvent.bind(z),z.addEventListener("message",(e=>{J&&"function"==typeof J&&J(e)})),Object.defineProperty(z,"onmessage",{configurable:!1,set(e){J=e},get:()=>J}),Object.defineProperty(z,"postMessage",{configurable:!1,writable:!1,value:function(e,t){worker.postMessage(MessageData.format(e,!!t))}}),z);worker.onMessage((e=>{q.dispatchEvent(new y("message",{data:MessageData.toRaw(e)}))}));var $=new Proxy(globalThis,{get:(e,t,r)=>Reflect.has(q,t)?Reflect.get(q,t,r):globalThis[t],set:(e,t,r,n)=>Reflect.has(q,t)?Reflect.set(q,t,r,n):Reflect.set(globalThis,t,r,n)}),ee=null;globalThis.setWASMInstantiateInputMapper=function(e){e&&"function"==typeof e&&(ee=e)};var te=WXWebAssembly.instantiate,re=WXWebAssembly;WXWebAssembly.instantiate=(e,t)=>{var r=ee?ee(e):e;return te.call(WXWebAssembly,r,t)},WXWebAssembly.RuntimeError=Error,exports.WebAssembly=re,exports.proxySelf=$;
