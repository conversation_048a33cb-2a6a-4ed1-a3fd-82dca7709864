<template>
	<view class="container">
		<view class="head-line">
			<text>数字人演示</text>
		</view>
		<!-- #ifndef H5 -->
		<canvas
			class="webgl-canvas"
			type="webgl"
			canvas-id="webglCanvas"
			id="webglCanvas"
			@touchstart="onTouchStart"
			@touchmove="onTouchMove"
			@touchend="onTouchEnd">
		</canvas>
		<!-- #endif -->
		<!-- #ifdef H5 -->
		<canvas
			class="webgl-canvas"
			id="webglCanvas"
			@touchstart="onTouchStart"
			@touchmove="onTouchMove"
			@touchend="onTouchEnd">
		</canvas>
		<!-- #endif -->
	</view>
</template>

<script>
	// 专注于2D Canvas实现，移除Three.js依赖
	
	export default {
		data() {
			return {
				message: '数字人演示',
				canvas: null,
				animationId: null,
				ctx: null
			}
		},
		onLoad() {
			console.log('页面onLoad执行');
		},
		onReady() {
			console.log('页面onReady执行');
			console.log('当前平台:', uni.getSystemInfoSync().platform);

			// 初始化数字人场景
			setTimeout(() => {
				this.initThreeJS();
			}, 500);
		},
		onUnload() {
			// 清理资源
			this.cleanup();
		},
		methods: {
			initThreeJS() {
				console.log('开始初始化数字人场景');

				// 直接使用2D Canvas模式
				this.init2DCanvas();
			},

			init2DCanvas() {
				console.log('初始化2D Canvas数字人演示');

				// 获取系统信息
				const systemInfo = uni.getSystemInfoSync();
				const width = systemInfo.windowWidth || 375;
				const height = systemInfo.windowHeight * 0.8 || 600;

				console.log('Canvas尺寸:', width, height);

				// 使用uni.createCanvasContext创建2D上下文
				this.ctx = uni.createCanvasContext('webglCanvas', this);
				if (!this.ctx) {
					console.error('无法创建Canvas上下文');
					return;
				}

				console.log('Canvas上下文创建成功');

				// 开始2D动画
				this.animate2D(this.ctx, width, height);

				console.log('2D Canvas数字人演示启动');
			},

			cleanup() {
				if (this.animationId) {
					clearTimeout(this.animationId);
					this.animationId = null;
				}
			},

			animate2D(ctx, width, height) {
				if (!ctx) return;

				// 清除画布
				ctx.setFillStyle('#222222');
				ctx.fillRect(0, 0, width, height);

				// 绘制简单的数字人
				this.drawSimpleDigitalHuman(ctx, width, height);

				// 绘制信息
				ctx.setFillStyle('#ffffff');
				ctx.setFontSize(16);
				ctx.setTextAlign('center');
				ctx.fillText('数字人演示 (2D模式)', width / 2, 30);
				ctx.fillText('小程序Canvas模式', width / 2, 50);

				// 提交绘制
				ctx.draw();

				// 继续动画
				this.animationId = setTimeout(() => this.animate2D(ctx, width, height), 16);
			},

			drawSimpleDigitalHuman(ctx, width, height) {
				const centerX = width / 2;
				const centerY = height / 2;
				const time = Date.now() * 0.001;

				// 保存上下文
				ctx.save();

				// 移动到中心并添加轻微的摆动
				ctx.translate(centerX, centerY);
				ctx.rotate(Math.sin(time) * 0.1);

				// 绘制头部
				ctx.setFillStyle('#ffdbac');
				ctx.beginPath();
				ctx.arc(0, -80, 30, 0, Math.PI * 2);
				ctx.fill();

				// 绘制眼睛
				ctx.setFillStyle('#000000');
				ctx.beginPath();
				ctx.arc(-10, -85, 3, 0, Math.PI * 2);
				ctx.fill();
				ctx.beginPath();
				ctx.arc(10, -85, 3, 0, Math.PI * 2);
				ctx.fill();

				// 绘制嘴巴
				ctx.setStrokeStyle('#000000');
				ctx.setLineWidth(2);
				ctx.beginPath();
				ctx.arc(0, -75, 8, 0, Math.PI);
				ctx.stroke();

				// 绘制身体
				ctx.setFillStyle('#4169e1');
				ctx.fillRect(-20, -50, 40, 60);

				// 绘制手臂
				ctx.setFillStyle('#ffdbac');
				ctx.fillRect(-35, -40, 15, 40);
				ctx.fillRect(20, -40, 15, 40);

				// 绘制腿部
				ctx.setFillStyle('#2f4f4f');
				ctx.fillRect(-15, 10, 12, 50);
				ctx.fillRect(3, 10, 12, 50);

				// 恢复上下文
				ctx.restore();
			},

			onTouchStart(e) {
				console.log('触摸开始', e);
			},

			onTouchMove(e) {
				console.log('触摸移动', e);
			},

			onTouchEnd(e) {
				console.log('触摸结束', e);
			}
		}
	};
</script>

<style>
	.container {
		width: 100%;
		height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #f5f5f5;
	}

	.head-line {
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #007aff;
		color: white;
	}

	.head-line text {
		font-size: 36rpx;
		color: white;
		font-weight: bold;
	}

	.webgl-canvas {
		flex: 1;
		width: 100%;
		min-height: 400px;
		background-color: #000;
		position: relative;
	}
</style>
