<template>
	<view class="container">
		<view class="head-line">
			<text>GLTF导出器演示</text>
		</view>
		<canvas
			class="webgl-canvas"
			type="2d"
			canvas-id="webglCanvas"
			id="webglCanvas"
			@touchstart="onTouchStart"
			@touchmove="onTouchMove"
			@touchend="onTouchEnd">
		</canvas>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				message: 'GLTF导出器演示',
				canvas: null,
				scene1: null,
				scene2: null,
				camera: null,
				renderer: null,
				animationId: null,
				animationTime: 0,
				objects: [],
				sphere: null,
				gridHelper: null
			}
		},
		onLoad() {
			console.log('页面onLoad执行');
		},
		onReady() {
			console.log('页面onReady执行');
			console.log('当前平台:', uni.getSystemInfoSync().platform);

			// 尝试初始化GLTF导出器演示
			setTimeout(() => {
				this.initWebGL();
			}, 500);
		},
		onUnload() {
			// 清理动画
			if (this.animationId) {
				clearTimeout(this.animationId);
			}
		},
		methods: {
			initWebGL() {
				console.log('开始初始化GLTF导出器演示');

				// 获取canvas节点
				const query = uni.createSelectorQuery().in(this);
				query.select('#webglCanvas').fields({
					node: true,
					size: true
				}).exec((res) => {
					console.log('Canvas查询结果:', res);
					if (res[0] && res[0].node) {
						const canvas = res[0].node;
						console.log('Canvas节点获取成功:', canvas);

						// 尝试获取WebGL上下文
						const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

						if (gl) {
							console.log('WebGL上下文获取成功!');
							this.canvas = canvas;
							this.setupThreeJS();
						} else {
							console.error('无法获取WebGL上下文');
							this.fallbackTo2D(canvas);
						}
					} else {
						console.error('无法找到canvas节点');
					}
				});
			},

			setupThreeJS() {
				console.log('设置Three.js GLTF导出器场景');
				console.log('微信小程序环境不支持three.js，回退到2D模拟');
				this.fallbackTo2D(this.canvas);
			},

			createAnimationGroup(THREE) {
				console.log('创建动画组');

				// 创建动画对象组
				const animationGroup = new THREE.AnimationObjectGroup();

				// 创建几何体和材质
				const geometry = new THREE.BoxGeometry(5, 5, 5);
				const material = new THREE.MeshBasicMaterial({ transparent: true });

				// 创建5x5网格的立方体
				for (let i = 0; i < 5; i++) {
					for (let j = 0; j < 5; j++) {
						const mesh = new THREE.Mesh(geometry, material);
						mesh.position.x = 32 - 16 * i;
						mesh.position.y = 0;
						mesh.position.z = 32 - 16 * j;
						this.scene.add(mesh);
						animationGroup.add(mesh);
					}
				}

				// 创建旋转动画轨道
				const xAxis = new THREE.Vector3(1, 0, 0);
				const qInitial = new THREE.Quaternion().setFromAxisAngle(xAxis, 0);
				const qFinal = new THREE.Quaternion().setFromAxisAngle(xAxis, Math.PI);
				const quaternionKF = new THREE.QuaternionKeyframeTrack(
					".quaternion",
					[0, 1, 2],
					[
						qInitial.x, qInitial.y, qInitial.z, qInitial.w,
						qFinal.x, qFinal.y, qFinal.z, qFinal.w,
						qInitial.x, qInitial.y, qInitial.z, qInitial.w
					]
				);

				// 创建颜色动画轨道
				const colorKF = new THREE.ColorKeyframeTrack(
					".material.color",
					[0, 1, 2],
					[1, 0, 0, 0, 1, 0, 0, 0, 1],
					THREE.InterpolateDiscrete
				);

				// 创建透明度动画轨道
				const opacityKF = new THREE.NumberKeyframeTrack(".material.opacity", [0, 1, 2], [1, 0, 1]);

				// 创建动画剪辑
				const clip = new THREE.AnimationClip("default", 3, [quaternionKF, colorKF, opacityKF]);

				// 创建动画混合器
				this.mixer = new THREE.AnimationMixer(animationGroup);
				const clipAction = this.mixer.clipAction(clip);
				clipAction.play();

				console.log('动画组创建完成，包含25个立方体');
			},

			animate() {
				if (!this.renderer || !this.scene || !this.camera) return;

				const delta = this.clock.getDelta();
				if (this.mixer) {
					this.mixer.update(delta);
				}

				this.renderer.render(this.scene, this.camera);

				// 使用setTimeout代替requestAnimationFrame
				this.animationId = setTimeout(() => this.animate(), 16);
			},

			fallbackTo2D(canvas) {
				console.log('开始2D Canvas模拟GLTF导出器场景');

				// 在微信小程序中获取2D上下文的方式
				let ctx;
				try {
					// 尝试获取2D上下文
					ctx = canvas.getContext('2d');
				} catch (error) {
					console.error('getContext错误:', error);
					// 如果失败，尝试使用uni.createCanvasContext
					this.fallbackToUniCanvas();
					return;
				}

				if (!ctx) {
					console.error('无法获取2D上下文，尝试uni方式');
					this.fallbackToUniCanvas();
					return;
				}

				// 设置canvas尺寸
				const dpr = uni.getSystemInfoSync().pixelRatio;
				canvas.width = 300 * dpr;
				canvas.height = 300 * dpr;
				ctx.scale(dpr, dpr);

				// 初始化场景对象
				this.animationTime = 0;
				this.objects = [];

				// 创建多种几何体对象（模拟GLTF导出器场景）
				this.createGLTFScene();

				// 开始2D动画
				this.animate2D(ctx);
				console.log('2D GLTF导出器场景模拟启动');
			},

			createGLTFScene() {
				console.log('创建GLTF导出器场景对象');

				// 创建多种几何体对象
				this.objects = [
					// 球体 (中心)
					{
						type: 'sphere',
						x: 150,
						y: 150,
						size: 30,
						color: { r: 1, g: 1, b: 0 },
						rotation: 0,
						name: 'Sphere'
					},
					// 立方体
					{
						type: 'cube',
						x: 80,
						y: 80,
						size: 25,
						color: { r: 1, g: 0, b: 0 },
						rotation: 0,
						name: 'Cube'
					},
					// 圆柱体
					{
						type: 'cylinder',
						x: 220,
						y: 80,
						size: 20,
						color: { r: 0, g: 1, b: 0 },
						rotation: 0,
						name: 'Cylinder'
					},
					// 三角形 (模拟四面体)
					{
						type: 'triangle',
						x: 80,
						y: 220,
						size: 25,
						color: { r: 0, g: 0, b: 1 },
						rotation: 0,
						name: 'Tetrahedron'
					},
					// 八边形 (模拟八面体)
					{
						type: 'octagon',
						x: 220,
						y: 220,
						size: 20,
						color: { r: 1, g: 0, b: 1 },
						rotation: 0,
						name: 'Octahedron'
					},
					// 环形 (模拟环面结)
					{
						type: 'ring',
						x: 150,
						y: 80,
						size: 18,
						color: { r: 0, g: 1, b: 1 },
						rotation: 0,
						name: 'TorusKnot'
					}
				];

				console.log(`创建了${this.objects.length}个几何体对象`);
			},

			animate2D(ctx) {
				if (!ctx) return;

				// 更新动画时间
				this.animationTime += 0.016; // 约60fps
				const timer = this.animationTime * 0.0001;

				// 清除画布
				ctx.fillStyle = '#222';
				ctx.fillRect(0, 0, 300, 300);

				// 绘制网格背景
				this.drawGrid(ctx);

				// 更新并绘制每个几何体
				this.objects.forEach((obj, index) => {
					// 计算旋转动画
					obj.rotation = timer * (index + 1) * 0.5;

					// 绘制几何体
					this.drawGeometry2D(ctx, obj);
				});

				// 绘制信息
				ctx.fillStyle = '#fff';
				ctx.font = '12px Arial';
				ctx.textAlign = 'left';
				ctx.fillText('2D模拟 GLTF导出器场景', 10, 20);
				ctx.fillText(`${this.objects.length}个几何体对象`, 10, 35);
				ctx.fillText(`时间: ${this.animationTime.toFixed(1)}s`, 10, 50);

				// 绘制对象列表
				this.objects.forEach((obj, index) => {
					ctx.fillStyle = `rgb(${obj.color.r*255}, ${obj.color.g*255}, ${obj.color.b*255})`;
					ctx.fillText(`${obj.name}`, 10, 80 + index * 15);
				});

				// 继续动画
				this.animationId = setTimeout(() => this.animate2D(ctx), 16);
			},

			drawGrid(ctx) {
				// 绘制网格线
				ctx.strokeStyle = '#444';
				ctx.lineWidth = 1;
				ctx.beginPath();

				// 垂直线
				for (let x = 0; x <= 300; x += 30) {
					ctx.moveTo(x, 0);
					ctx.lineTo(x, 300);
				}

				// 水平线
				for (let y = 0; y <= 300; y += 30) {
					ctx.moveTo(0, y);
					ctx.lineTo(300, y);
				}

				ctx.stroke();
			},

			drawGeometry2D(ctx, obj) {
				ctx.save();

				// 移动到对象中心并旋转
				ctx.translate(obj.x, obj.y);
				ctx.rotate(obj.rotation);

				// 设置颜色
				const r = Math.floor(obj.color.r * 255);
				const g = Math.floor(obj.color.g * 255);
				const b = Math.floor(obj.color.b * 255);

				ctx.fillStyle = `rgb(${r}, ${g}, ${b})`;
				ctx.strokeStyle = `rgb(${Math.min(r+50, 255)}, ${Math.min(g+50, 255)}, ${Math.min(b+50, 255)})`;
				ctx.lineWidth = 2;

				// 根据类型绘制不同几何体
				switch (obj.type) {
					case 'sphere':
						this.drawSphere2D(ctx, obj.size);
						break;
					case 'cube':
						this.drawCube2D(ctx, obj.size);
						break;
					case 'cylinder':
						this.drawCylinder2D(ctx, obj.size);
						break;
					case 'triangle':
						this.drawTriangle2D(ctx, obj.size);
						break;
					case 'octagon':
						this.drawOctagon2D(ctx, obj.size);
						break;
					case 'ring':
						this.drawRing2D(ctx, obj.size);
						break;
				}

				ctx.restore();
			},

			drawSphere2D(ctx, size) {
				// 绘制圆形
				ctx.beginPath();
				ctx.arc(0, 0, size, 0, Math.PI * 2);
				ctx.fill();
				ctx.stroke();

				// 添加高光效果
				ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
				ctx.beginPath();
				ctx.arc(-size/3, -size/3, size/3, 0, Math.PI * 2);
				ctx.fill();
			},

			drawCube2D(ctx, size) {
				// 绘制正方形
				const half = size / 2;
				ctx.fillRect(-half, -half, size, size);
				ctx.strokeRect(-half, -half, size, size);

				// 绘制对角线增加3D感
				ctx.beginPath();
				ctx.moveTo(-half, -half);
				ctx.lineTo(half, half);
				ctx.moveTo(half, -half);
				ctx.lineTo(-half, half);
				ctx.stroke();
			},

			drawCylinder2D(ctx, size) {
				// 绘制椭圆形
				ctx.beginPath();
				ctx.ellipse(0, 0, size, size * 0.6, 0, 0, Math.PI * 2);
				ctx.fill();
				ctx.stroke();

				// 绘制顶部椭圆
				ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
				ctx.beginPath();
				ctx.ellipse(0, -size * 0.3, size * 0.8, size * 0.2, 0, 0, Math.PI * 2);
				ctx.stroke();
			},

			drawTriangle2D(ctx, size) {
				// 绘制三角形
				ctx.beginPath();
				ctx.moveTo(0, -size);
				ctx.lineTo(-size * 0.866, size * 0.5);
				ctx.lineTo(size * 0.866, size * 0.5);
				ctx.closePath();
				ctx.fill();
				ctx.stroke();
			},

			drawOctagon2D(ctx, size) {
				// 绘制八边形
				ctx.beginPath();
				for (let i = 0; i < 8; i++) {
					const angle = (i / 8) * Math.PI * 2;
					const x = Math.cos(angle) * size;
					const y = Math.sin(angle) * size;
					if (i === 0) {
						ctx.moveTo(x, y);
					} else {
						ctx.lineTo(x, y);
					}
				}
				ctx.closePath();
				ctx.fill();
				ctx.stroke();
			},

			drawRing2D(ctx, size) {
				// 绘制环形
				ctx.beginPath();
				ctx.arc(0, 0, size, 0, Math.PI * 2);
				ctx.arc(0, 0, size * 0.5, 0, Math.PI * 2, true);
				ctx.fill();
				ctx.stroke();
			},



			fallbackToUniCanvas() {
				console.log('使用uni.createCanvasContext方式');

				// 使用uni的canvas上下文
				const ctx = uni.createCanvasContext('webglCanvas', this);
				if (!ctx) {
					console.error('uni.createCanvasContext也失败了');
					this.showSimpleMessage();
					return;
				}

				// 初始化场景对象
				this.animationTime = 0;
				this.createGLTFScene();

				// 开始uni canvas动画
				this.animateUniCanvas(ctx);
				console.log('uni Canvas GLTF场景动画启动');
			},

			animateUniCanvas(ctx) {
				if (!ctx) return;

				// 更新动画时间
				this.animationTime += 0.016;
				const timer = this.animationTime * 0.0001;

				// 清除画布
				ctx.setFillStyle('#222');
				ctx.fillRect(0, 0, 300, 300);

				// 绘制网格
				this.drawGridUni(ctx);

				// 更新并绘制每个几何体
				this.objects.forEach((obj, index) => {
					obj.rotation = timer * (index + 1) * 0.5;
					this.drawGeometryUni(ctx, obj);
				});

				// 绘制信息
				ctx.setFillStyle('#fff');
				ctx.setFontSize(12);
				ctx.fillText('2D模拟 GLTF导出器场景', 10, 20);
				ctx.fillText(`${this.objects.length}个几何体对象`, 10, 35);
				ctx.fillText(`时间: ${this.animationTime.toFixed(1)}s`, 10, 50);

				// 绘制对象列表
				this.objects.forEach((obj, index) => {
					ctx.setFillStyle(`rgb(${obj.color.r*255}, ${obj.color.g*255}, ${obj.color.b*255})`);
					ctx.fillText(`${obj.name}`, 10, 80 + index * 15);
				});

				// 提交绘制
				ctx.draw();

				// 继续动画
				this.animationId = setTimeout(() => this.animateUniCanvas(ctx), 16);
			},

			drawGridUni(ctx) {
				// 绘制网格线
				ctx.setStrokeStyle('#444');
				ctx.setLineWidth(1);
				ctx.beginPath();

				// 垂直线和水平线
				for (let i = 0; i <= 300; i += 30) {
					ctx.moveTo(i, 0);
					ctx.lineTo(i, 300);
					ctx.moveTo(0, i);
					ctx.lineTo(300, i);
				}

				ctx.stroke();
			},

			drawGeometryUni(ctx, obj) {
				ctx.save();

				// 移动到对象中心并旋转
				ctx.translate(obj.x, obj.y);
				ctx.rotate(obj.rotation);

				// 设置颜色
				const r = Math.floor(obj.color.r * 255);
				const g = Math.floor(obj.color.g * 255);
				const b = Math.floor(obj.color.b * 255);

				ctx.setFillStyle(`rgb(${r}, ${g}, ${b})`);
				ctx.setStrokeStyle(`rgb(${Math.min(r+50, 255)}, ${Math.min(g+50, 255)}, ${Math.min(b+50, 255)})`);
				ctx.setLineWidth(2);

				// 根据类型绘制不同几何体（简化版）
				const size = obj.size;
				const half = size / 2;

				switch (obj.type) {
					case 'sphere':
						// 圆形
						ctx.beginPath();
						ctx.arc(0, 0, size, 0, Math.PI * 2);
						ctx.fill();
						ctx.stroke();
						break;
					case 'cube':
						// 正方形
						ctx.fillRect(-half, -half, size, size);
						ctx.strokeRect(-half, -half, size, size);
						break;
					case 'triangle':
						// 三角形
						ctx.beginPath();
						ctx.moveTo(0, -size);
						ctx.lineTo(-size * 0.866, size * 0.5);
						ctx.lineTo(size * 0.866, size * 0.5);
						ctx.closePath();
						ctx.fill();
						ctx.stroke();
						break;
					default:
						// 默认绘制圆形
						ctx.beginPath();
						ctx.arc(0, 0, size, 0, Math.PI * 2);
						ctx.fill();
						ctx.stroke();
						break;
				}

				ctx.restore();
			},

			drawCubeUni(ctx, cube) {
				ctx.save();

				// 移动到立方体中心并旋转
				ctx.translate(cube.x, cube.y);
				ctx.rotate(cube.rotation);

				// 设置颜色和透明度
				const r = Math.floor(cube.color.r * 255);
				const g = Math.floor(cube.color.g * 255);
				const b = Math.floor(cube.color.b * 255);
				const alpha = cube.opacity;

				ctx.setFillStyle(`rgba(${r}, ${g}, ${b}, ${alpha})`);
				ctx.setStrokeStyle(`rgba(${r}, ${g}, ${b}, ${Math.min(alpha + 0.3, 1)})`);
				ctx.setLineWidth(1);

				// 绘制正方形
				const halfSize = cube.size / 2;
				ctx.fillRect(-halfSize, -halfSize, cube.size, cube.size);
				ctx.strokeRect(-halfSize, -halfSize, cube.size, cube.size);

				// 绘制对角线
				ctx.beginPath();
				ctx.moveTo(-halfSize, -halfSize);
				ctx.lineTo(halfSize, halfSize);
				ctx.moveTo(halfSize, -halfSize);
				ctx.lineTo(-halfSize, halfSize);
				ctx.stroke();

				ctx.restore();
			},

			showSimpleMessage() {
				console.log('显示简单文本消息');
				// 如果canvas都不行，就在页面上显示文本
				uni.showToast({
					title: 'Animation Groups演示',
					icon: 'none',
					duration: 3000
				});
			},

			onTouchStart() {
				console.log('触摸开始');
			},

			onTouchMove() {
				console.log('触摸移动');
			},

			onTouchEnd() {
				console.log('触摸结束');
			}
		}
	};
</script>

<style>
	.container {
		width: 100%;
		height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #f5f5f5;
	}

	.head-line {
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #007aff;
		color: white;
	}

	.head-line text {
		font-size: 36rpx;
		color: white;
		font-weight: bold;
	}

	.webgl-canvas {
		flex: 1;
		width: 100%;
		min-height: 400px;
		background-color: #000;
		position: relative;
	}
</style>