<template>
	<view class="container">
		<view class="head-line">
			<text>数字人演示</text>
		</view>
		<!-- #ifndef H5 -->
		<canvas
			class="webgl-canvas"
			type="webgl"
			canvas-id="webglCanvas"
			id="webglCanvas"
			@touchstart="onTouchStart"
			@touchmove="onTouchMove"
			@touchend="onTouchEnd">
		</canvas>
		<!-- #endif -->
		<!-- #ifdef H5 -->
		<canvas
			class="webgl-canvas"
			id="webglCanvas"
			@touchstart="onTouchStart"
			@touchmove="onTouchMove"
			@touchend="onTouchEnd">
		</canvas>
		<!-- #endif -->
	</view>
</template>

<script>
	import * as THREE from 'three';
	import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
	import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
	
	export default {
		data() {
			return {
				message: '数字人演示',
				canvas: null,
				scene: null,
				camera: null,
				renderer: null,
				animationId: null,
				model: null,
				controls: null,
				mixer: null,
				clock: null
			}
		},
		onLoad() {
			console.log('页面onLoad执行');
		},
		onReady() {
			console.log('页面onReady执行');
			console.log('当前平台:', uni.getSystemInfoSync().platform);

			// 初始化数字人场景
			setTimeout(() => {
				this.initThreeJS();
			}, 500);
		},
		onUnload() {
			// 清理资源
			this.cleanup();
		},
		methods: {
			initThreeJS() {
				console.log('开始初始化数字人场景');

				// 获取canvas节点
				const query = uni.createSelectorQuery().in(this);
				query.select('#webglCanvas').fields({
					node: true,
					size: true
				}).exec((res) => {
					console.log('Canvas查询结果:', res);
					if (res[0] && res[0].node) {
						const canvas = res[0].node;
						console.log('Canvas节点获取成功:', canvas);
						this.canvas = canvas;

						// 尝试初始化Three.js场景
						try {
							this.setupThreeJSScene();
						} catch (error) {
							console.error('Three.js初始化失败:', error);
							this.fallbackTo2D(canvas);
						}
					} else {
						console.error('无法找到canvas节点');
					}
				});
			},

			setupThreeJSScene() {
				console.log('设置Three.js数字人场景');
				
				const canvas = this.canvas;
				const width = canvas.width || 300;
				const height = canvas.height || 300;

				// 初始化渲染器
				this.renderer = new THREE.WebGLRenderer({ 
					canvas: canvas,
					antialias: true,
					alpha: true
				});
				this.renderer.setSize(width, height);
				this.renderer.setPixelRatio(Math.min(window.devicePixelRatio || 1, 2));
				this.renderer.shadowMap.enabled = true;
				this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

				// 初始化场景
				this.scene = new THREE.Scene();
				this.scene.background = new THREE.Color(0x222222);

				// 初始化相机
				this.camera = new THREE.PerspectiveCamera(45, width / height, 0.1, 1000);
				this.camera.position.set(0, 1.6, 3);

				// 初始化时钟
				this.clock = new THREE.Clock();

				// 添加光照
				this.setupLights();

				// 添加网格
				this.addGrid();

				// 加载数字人模型
				this.loadDigitalHuman();

				// 开始渲染循环
				this.animate();
			},

			setupLights() {
				// 环境光
				const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
				this.scene.add(ambientLight);

				// 主光源
				const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
				directionalLight.position.set(5, 10, 5);
				directionalLight.castShadow = true;
				directionalLight.shadow.mapSize.width = 2048;
				directionalLight.shadow.mapSize.height = 2048;
				this.scene.add(directionalLight);

				// 补光
				const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
				fillLight.position.set(-5, 5, -5);
				this.scene.add(fillLight);
			},

			addGrid() {
				const gridHelper = new THREE.GridHelper(10, 10, 0x888888, 0x444444);
				gridHelper.position.y = 0;
				this.scene.add(gridHelper);
			},

			loadDigitalHuman() {
				console.log('开始加载数字人模型');
				
				const loader = new GLTFLoader();
				
				// 加载Teacher.glb模型
				loader.load(
					'./Teacher.glb',
					(gltf) => {
						console.log('数字人模型加载成功:', gltf);
						
						this.model = gltf.scene;
						
						// 调整模型大小和位置
						this.model.scale.set(1, 1, 1);
						this.model.position.set(0, 0, 0);
						
						// 启用阴影
						this.model.traverse((child) => {
							if (child.isMesh) {
								child.castShadow = true;
								child.receiveShadow = true;
							}
						});
						
						this.scene.add(this.model);
						
						// 如果有动画，设置动画混合器
						if (gltf.animations && gltf.animations.length > 0) {
							this.mixer = new THREE.AnimationMixer(this.model);
							gltf.animations.forEach((clip) => {
								const action = this.mixer.clipAction(clip);
								action.play();
							});
						}
						
						console.log('数字人模型添加到场景成功');
					},
					(progress) => {
						console.log('加载进度:', (progress.loaded / progress.total * 100) + '%');
					},
					(error) => {
						console.error('数字人模型加载失败:', error);
						// 加载失败时创建一个简单的替代模型
						this.createFallbackModel();
					}
				);
			},

			createFallbackModel() {
				console.log('创建替代数字人模型');
				
				// 创建一个简单的人形模型
				const group = new THREE.Group();
				
				// 头部
				const headGeometry = new THREE.SphereGeometry(0.15, 16, 16);
				const headMaterial = new THREE.MeshLambertMaterial({ color: 0xffdbac });
				const head = new THREE.Mesh(headGeometry, headMaterial);
				head.position.y = 1.7;
				head.castShadow = true;
				group.add(head);
				
				// 身体
				const bodyGeometry = new THREE.CylinderGeometry(0.2, 0.3, 0.8, 8);
				const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x4169e1 });
				const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
				body.position.y = 1.2;
				body.castShadow = true;
				group.add(body);
				
				// 腿部
				const legGeometry = new THREE.CylinderGeometry(0.08, 0.1, 0.6, 8);
				const legMaterial = new THREE.MeshLambertMaterial({ color: 0x2f4f4f });
				
				const leftLeg = new THREE.Mesh(legGeometry, legMaterial);
				leftLeg.position.set(-0.15, 0.5, 0);
				leftLeg.castShadow = true;
				group.add(leftLeg);
				
				const rightLeg = new THREE.Mesh(legGeometry, legMaterial);
				rightLeg.position.set(0.15, 0.5, 0);
				rightLeg.castShadow = true;
				group.add(rightLeg);
				
				this.model = group;
				this.scene.add(this.model);
				
				console.log('替代模型创建完成');
			},

			animate() {
				if (!this.renderer || !this.scene || !this.camera) return;

				const delta = this.clock.getDelta();
				
				// 更新动画混合器
				if (this.mixer) {
					this.mixer.update(delta);
				}
				
				// 简单的模型旋转动画
				if (this.model) {
					this.model.rotation.y += 0.005;
				}

				this.renderer.render(this.scene, this.camera);

				// 使用setTimeout代替requestAnimationFrame
				this.animationId = setTimeout(() => this.animate(), 16);
			},

			cleanup() {
				if (this.animationId) {
					clearTimeout(this.animationId);
					this.animationId = null;
				}
				
				if (this.mixer) {
					this.mixer.stopAllAction();
					this.mixer = null;
				}
				
				if (this.renderer) {
					this.renderer.dispose();
					this.renderer = null;
				}
			},

			fallbackTo2D(canvas) {
				console.log('Three.js初始化失败，显示提示信息');
				
				uni.showToast({
					title: '数字人加载失败，请检查设备支持',
					icon: 'none',
					duration: 3000
				});
			},

			onTouchStart(e) {
				console.log('触摸开始', e);
			},

			onTouchMove(e) {
				console.log('触摸移动', e);
			},

			onTouchEnd(e) {
				console.log('触摸结束', e);
			}
		}
	};
</script>

<style>
	.container {
		width: 100%;
		height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #f5f5f5;
	}

	.head-line {
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #007aff;
		color: white;
	}

	.head-line text {
		font-size: 36rpx;
		color: white;
		font-weight: bold;
	}

	.webgl-canvas {
		flex: 1;
		width: 100%;
		min-height: 400px;
		background-color: #000;
		position: relative;
	}
</style>
