<template>
	<view class="container">
		<view class="head-line">
			<text>数字人演示</text>
		</view>
		<PlatformCanvas
			type="webgl"
			canvas-id="webglCanvas"
			@useCanvas="useCanvas"
		/>
	</view>
</template>

<script>
	import * as THREE from 'three';
	import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
	import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
	import { KTX2Loader } from 'three/examples/jsm/loaders/KTX2Loader.js';
	import { MeshoptDecoder } from 'three/examples/jsm/libs/meshopt_decoder.module.js';
	import { RoomEnvironment } from 'three/examples/jsm/environments/RoomEnvironment.js';
	import PlatformCanvas from "@/components/PlatformCanvas.vue";

	export default {
		components: {
			PlatformCanvas
		},
		data() {
			return {
				message: '数字人演示',
				camera: null,
				scene: null,
				renderer: null,
				controls: null,
				mixer: null
			}
		},
		onLoad() {
			console.log('页面onLoad执行');
		},
		onReady() {
			console.log('页面onReady执行');
			console.log('当前平台:', uni.getSystemInfoSync().platform);
		},
		onUnload() {
			// 清理资源
			if (this.mixer) {
				this.mixer.stopAllAction();
			}
		},
		methods: {
			useCanvas({ canvas, useFrame, recomputeSize }) {
				console.log('Canvas初始化成功，开始设置Three.js场景');

				const CANVAS_WIDTH = canvas.width;
				const CANVAS_HEIGHT = canvas.height;

				this.init(canvas, CANVAS_WIDTH, CANVAS_HEIGHT);
				this.render();
			},

			init(canvas, width, height) {
				console.log('初始化Three.js渲染器和场景');

				// 初始化渲染器
				this.renderer = new THREE.WebGLRenderer({ canvas, antialias: true });
				this.renderer.setPixelRatio(uni.getSystemInfoSync().pixelRatio || 1);
				this.renderer.setSize(width, height);
				this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
				this.renderer.toneMappingExposure = 1;

				// 初始化相机
				this.camera = new THREE.PerspectiveCamera(45, width / height, 1, 2000);
				this.camera.position.set(0, 100, 0);

				// 初始化场景和环境
				const environment = new RoomEnvironment();
				const pmremGenerator = new THREE.PMREMGenerator(this.renderer);

				this.scene = new THREE.Scene();
				this.scene.background = new THREE.Color(0xbbbbbb);
				this.scene.environment = pmremGenerator.fromScene(environment).texture;
				environment.dispose();

				// 添加网格
				const grid = new THREE.GridHelper(500, 10, 0xffffff, 0xffffff);
				grid.material.opacity = 0.5;
				grid.material.depthWrite = false;
				grid.material.transparent = true;
				this.scene.add(grid);

				// 初始化控制器
				this.controls = new OrbitControls(this.camera, this.renderer.domElement);
				this.controls.addEventListener('change', () => this.render());
				this.controls.minDistance = 400;
				this.controls.maxDistance = 1000;
				this.controls.target.set(10, 90, -16);
				this.controls.update();

				// 加载网络数字人模型
				this.loadGLBModel('https://reader.c8plus.net/statix/Teacher.glb');

				console.log('Three.js场景初始化完成');
			},

			loadGLBModel(modelUrl) {
				console.log('开始加载网络数字人模型:', modelUrl);

				// 显示加载提示
				uni.showLoading({
					title: '加载数字人模型...'
				});

				// 清除现有模型
				this.scene.children.forEach(child => {
					if (child.isScene || child.type === 'Group') {
						this.scene.remove(child);
					}
				});

				const ktx2Loader = new KTX2Loader()
					.setWorkerLimit(1)
					.setTranscoderPath('https://threejs.org/examples/jsm/libs/basis/')
					.detectSupport(this.renderer);

				const loader = new GLTFLoader();
				loader.setKTX2Loader(ktx2Loader);
				loader.setMeshoptDecoder(MeshoptDecoder);

				loader.load(
					modelUrl,
					(gltf) => {
						console.log('网络数字人模型加载成功:', gltf);

						// 隐藏加载提示
						uni.hideLoading();

						const model = gltf.scene;
						const animations = gltf.animations;

						console.log('模型动画列表:', animations);
						console.log('模型详细信息:', gltf);

						// 计算模型边界盒，自动调整大小和位置
						const box = new THREE.Box3().setFromObject(model);
						const size = box.getSize(new THREE.Vector3());
						const center = box.getCenter(new THREE.Vector3());

						console.log('模型尺寸:', size);
						console.log('模型中心:', center);

						// 根据模型实际大小调整缩放
						const maxSize = Math.max(size.x, size.y, size.z);
						const targetSize = 100; // 目标大小
						const scale = targetSize / maxSize;

						model.scale.set(scale, scale, scale);

						// 将模型中心移到原点上方
						model.position.set(-center.x * scale, -center.y * scale + 50, -center.z * scale);

						this.scene.add(model);
						this.render();

						// 如果有动画，设置动画混合器
						if (animations && animations.length > 0) {
							this.mixer = new THREE.AnimationMixer(model);
							animations.forEach((clip, index) => {
								console.log(`播放动画 ${index + 1}:`, clip.name);
								const action = this.mixer.clipAction(clip);
								action.play();
							});

							// 启动动画循环
							this.startAnimationLoop();
						}

						// 调整相机位置以更好地查看模型
						this.camera.position.set(0, 100, 200);
						this.controls.target.set(0, 50, 0);
						this.controls.update();

						uni.showToast({
							title: '数字人加载成功！',
							icon: 'success'
						});

					},
					(progress) => {
						// 显示加载进度
						const percent = Math.round((progress.loaded / progress.total) * 100);
						console.log(`加载进度: ${percent}%`);
						uni.showLoading({
							title: `加载中... ${percent}%`
						});
					},
					(error) => {
						console.error("加载网络数字人模型失败:", error);
						uni.hideLoading();
						uni.showToast({
							title: '数字人加载失败',
							icon: 'error'
						});

						// 加载失败时创建一个简单的替代模型
						this.createFallbackModel();
					}
				);
			},

			startAnimationLoop() {
				const animate = () => {
					if (this.mixer) {
						this.mixer.update(0.016); // 约60fps
					}
					this.render();
					setTimeout(animate, 16);
				};
				animate();
			},

			createFallbackModel() {
				console.log('创建替代数字人模型');

				// 创建一个简单的人形模型作为备用
				const group = new THREE.Group();

				// 头部
				const headGeometry = new THREE.SphereGeometry(15, 16, 16);
				const headMaterial = new THREE.MeshLambertMaterial({ color: 0xffdbac });
				const head = new THREE.Mesh(headGeometry, headMaterial);
				head.position.y = 170;
				group.add(head);

				// 身体
				const bodyGeometry = new THREE.CylinderGeometry(20, 30, 80, 8);
				const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x4169e1 });
				const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
				body.position.y = 120;
				group.add(body);

				// 腿部
				const legGeometry = new THREE.CylinderGeometry(8, 10, 60, 8);
				const legMaterial = new THREE.MeshLambertMaterial({ color: 0x2f4f4f });

				const leftLeg = new THREE.Mesh(legGeometry, legMaterial);
				leftLeg.position.set(-15, 50, 0);
				group.add(leftLeg);

				const rightLeg = new THREE.Mesh(legGeometry, legMaterial);
				rightLeg.position.set(15, 50, 0);
				group.add(rightLeg);

				this.scene.add(group);
				this.render();

				console.log('替代模型创建完成');
			},

			render() {
				if (this.renderer && this.scene && this.camera) {
					this.renderer.render(this.scene, this.camera);
				}
			}
		}
	};
</script>

<style>
	.container {
		width: 100%;
		height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #f5f5f5;
	}

	.head-line {
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #007aff;
		color: white;
	}

	.head-line text {
		font-size: 36rpx;
		color: white;
		font-weight: bold;
	}

	.platform-canvas {
		flex: 1;
		width: 100%;
		min-height: 400px;
		background-color: #000;
	}
</style>
