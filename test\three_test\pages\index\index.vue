<template>
	<view class="container">
		<view class="head-line">
			<text>数字人演示</text>
		</view>
		<PlatformCanvas
			type="webgl"
			canvas-id="webglCanvas"
			@useCanvas="useCanvas"
		/>
	</view>
</template>

<script>
	import * as THREE from 'three';
	import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
	import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
	import { KTX2Loader } from 'three/examples/jsm/loaders/KTX2Loader.js';
	import { MeshoptDecoder } from 'three/examples/jsm/libs/meshopt_decoder.module.js';
	import { RoomEnvironment } from 'three/examples/jsm/environments/RoomEnvironment.js';
	import PlatformCanvas from "@/components/PlatformCanvas.vue";

	export default {
		components: {
			PlatformCanvas
		},
		data() {
			return {
				message: '数字人演示',
				camera: null,
				scene: null,
				renderer: null,
				controls: null,
				mixer: null
			}
		},
		onLoad() {
			console.log('页面onLoad执行');
		},
		onReady() {
			console.log('页面onReady执行');
			console.log('当前平台:', uni.getSystemInfoSync().platform);
		},
		onUnload() {
			// 清理资源
			if (this.mixer) {
				this.mixer.stopAllAction();
			}
		},
		methods: {
			useCanvas({ canvas, useFrame, recomputeSize }) {
				console.log('Canvas初始化成功，开始设置Three.js场景');

				const CANVAS_WIDTH = canvas.width;
				const CANVAS_HEIGHT = canvas.height;

				this.init(canvas, CANVAS_WIDTH, CANVAS_HEIGHT);
				this.render();
			},

			init(canvas, width, height) {
				console.log('初始化Three.js渲染器和场景');

				// 初始化渲染器
				this.renderer = new THREE.WebGLRenderer({ canvas, antialias: true });
				this.renderer.setPixelRatio(uni.getSystemInfoSync().pixelRatio || 1);
				this.renderer.setSize(width, height);
				this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
				this.renderer.toneMappingExposure = 1;

				// 初始化相机
				this.camera = new THREE.PerspectiveCamera(45, width / height, 1, 2000);
				this.camera.position.set(0, 100, 0);

				// 初始化场景和环境
				const environment = new RoomEnvironment();
				const pmremGenerator = new THREE.PMREMGenerator(this.renderer);

				this.scene = new THREE.Scene();
				this.scene.background = new THREE.Color(0xbbbbbb);
				this.scene.environment = pmremGenerator.fromScene(environment).texture;
				environment.dispose();

				// 添加网格
				const grid = new THREE.GridHelper(500, 10, 0xffffff, 0xffffff);
				grid.material.opacity = 0.5;
				grid.material.depthWrite = false;
				grid.material.transparent = true;
				this.scene.add(grid);

				// 初始化控制器
				this.controls = new OrbitControls(this.camera, this.renderer.domElement);
				this.controls.addEventListener('change', () => this.render());
				this.controls.minDistance = 400;
				this.controls.maxDistance = 1000;
				this.controls.target.set(10, 90, -16);
				this.controls.update();

				// 加载数字人模型
				this.loadGLBModel('./Teacher.glb');

				console.log('Three.js场景初始化完成');
			},

			loadGLBModel(modelUrl) {
				console.log('开始加载数字人模型:', modelUrl);

				// 清除现有模型
				this.scene.children.forEach(child => {
					if (child.isScene || child.type === 'Group') {
						this.scene.remove(child);
					}
				});

				const ktx2Loader = new KTX2Loader()
					.setWorkerLimit(1)
					.setTranscoderPath('https://threejs.org/examples/jsm/libs/basis/')
					.detectSupport(this.renderer);

				const loader = new GLTFLoader();
				loader.setKTX2Loader(ktx2Loader);
				loader.setMeshoptDecoder(MeshoptDecoder);

				loader.load(modelUrl, (gltf) => {
					console.log('数字人模型加载成功:', gltf);

					const model = gltf.scene;
					const animations = gltf.animations;

					console.log('animations', animations);
					console.log('gltf', gltf);

					// 调整模型大小和位置
					model.scale.set(220, 220, 220);
					model.position.set(10, 50, 0);

					this.scene.add(model);
					this.render();

					// 如果有动画，设置动画混合器
					if (animations && animations.length > 0) {
						this.mixer = new THREE.AnimationMixer(model);
						animations.forEach((clip) => {
							const action = this.mixer.clipAction(clip);
							action.play();
						});
					}

				}, undefined, (error) => {
					console.error("加载数字人模型失败:", error);
				});
			},

			render() {
				if (this.renderer && this.scene && this.camera) {
					this.renderer.render(this.scene, this.camera);
				}
			}
		}
	};
</script>

<style>
	.container {
		width: 100%;
		height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #f5f5f5;
	}

	.head-line {
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #007aff;
		color: white;
	}

	.head-line text {
		font-size: 36rpx;
		color: white;
		font-weight: bold;
	}

	.platform-canvas {
		flex: 1;
		width: 100%;
		min-height: 400px;
		background-color: #000;
	}
</style>
