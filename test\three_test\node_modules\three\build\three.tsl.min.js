/**
 * @license
 * Copyright 2010-2024 Three.js Authors
 * SPDX-License-Identifier: MIT
 */
import{TSL as e}from"three/webgpu";const t=e.BRDF_GGX,r=e.BRDF_Lambert,a=e.BasicShadowFilter,o=e.Break,i=e.Continue,n=e.DFGApprox,s=e.D_GGX,l=e.Discard,c=e.EPSILON,m=e.F_Schlick,p=e.Fn,d=e.INFINITY,u=e.If,g=e.Loop,h=e.NodeShaderStage,f=e.NodeType,x=e.NodeUpdateType,b=e.NodeAccess,w=e.PCFShadowFilter,v=e.PCFSoftShadowFilter,S=e.PI,T=e.PI2,_=e.Return,V=e.Schlick_to_F0,y=e.ScriptableNodeResources,D=e.ShaderNode,M=e.TBNViewMatrix,F=e.VSMShadowFilter,C=e.V_GGX_SmithCorrelated,I=e.abs,N=e.acesFilmicToneMapping,P=e.acos,R=e.add,B=e.addNodeElement,k=e.agxToneMapping,L=e.all,A=e.alphaT,G=e.and,O=e.anisotropy,W=e.anisotropyB,j=e.anisotropyT,U=e.any,q=e.append,z=e.arrayBuffer,E=e.asin,Z=e.assign,X=e.atan,Y=e.atan2,H=e.atomicAdd,J=e.atomicAnd,K=e.atomicFunc,Q=e.atomicMax,$=e.atomicMin,ee=e.atomicOr,te=e.atomicStore,re=e.atomicSub,ae=e.atomicXor,oe=e.attenuationColor,ie=e.attenuationDistance,ne=e.attribute,se=e.attributeArray,le=e.backgroundBlurriness,ce=e.backgroundIntensity,me=e.backgroundRotation,pe=e.batch,de=e.billboarding,ue=e.bitAnd,ge=e.bitNot,he=e.bitOr,fe=e.bitXor,xe=e.bitangentGeometry,be=e.bitangentLocal,we=e.bitangentView,ve=e.bitangentWorld,Se=e.bitcast,Te=e.blendBurn,_e=e.blendColor,Ve=e.blendDodge,ye=e.blendOverlay,De=e.blendScreen,Me=e.blur,Fe=e.bool,Ce=e.buffer,Ie=e.bufferAttribute,Ne=e.bumpMap,Pe=e.burn,Re=e.bvec2,Be=e.bvec3,ke=e.bvec4,Le=e.bypass,Ae=e.cache,Ge=e.call,Oe=e.cameraFar,We=e.cameraNear,je=e.cameraNormalMatrix,Ue=e.cameraPosition,qe=e.cameraProjectionMatrix,ze=e.cameraProjectionMatrixInverse,Ee=e.cameraViewMatrix,Ze=e.cameraWorldMatrix,Xe=e.cbrt,Ye=e.cdl,He=e.ceil,Je=e.checker,Ke=e.cineonToneMapping,Qe=e.clamp,$e=e.clearcoat,et=e.clearcoatRoughness,tt=e.code,rt=e.color,at=e.colorSpaceToWorking,ot=e.colorToDirection,it=e.compute,nt=e.cond,st=e.context,lt=e.convert,ct=e.convertColorSpace,mt=e.convertToTexture,pt=e.cos,dt=e.cross,ut=e.cubeTexture,gt=e.dFdx,ht=e.dFdy,ft=e.dashSize,xt=e.defaultBuildStages,bt=e.defaultShaderStages,wt=e.defined,vt=e.degrees,St=e.deltaTime,Tt=e.densityFog,_t=e.densityFogFactor,Vt=e.depth,yt=e.depthPass,Dt=e.difference,Mt=e.diffuseColor,Ft=e.directPointLight,Ct=e.directionToColor,It=e.dispersion,Nt=e.distance,Pt=e.div,Rt=e.dodge,Bt=e.dot,kt=e.drawIndex,Lt=e.dynamicBufferAttribute,At=e.element,Gt=e.emissive,Ot=e.equal,Wt=e.equals,jt=e.equirectUV,Ut=e.exp,qt=e.exp2,zt=e.expression,Et=e.faceDirection,Zt=e.faceForward,Xt=e.faceforward,Yt=e.float,Ht=e.floor,Jt=e.fog,Kt=e.fract,Qt=e.frameGroup,$t=e.frameId,er=e.frontFacing,tr=e.fwidth,rr=e.gain,ar=e.gapSize,or=e.getConstNodeType,ir=e.getCurrentStack,nr=e.getDirection,sr=e.getDistanceAttenuation,lr=e.getGeometryRoughness,cr=e.getNormalFromDepth,mr=e.getParallaxCorrectNormal,pr=e.getRoughness,dr=e.getScreenPosition,ur=e.getShIrradianceAt,gr=e.getTextureIndex,hr=e.getViewPosition,fr=e.glsl,xr=e.glslFn,br=e.grayscale,wr=e.greaterThan,vr=e.greaterThanEqual,Sr=e.hash,Tr=e.highpModelNormalViewMatrix,_r=e.highpModelViewMatrix,Vr=e.hue,yr=e.instance,Dr=e.instanceIndex,Mr=e.instancedArray,Fr=e.instancedBufferAttribute,Cr=e.instancedDynamicBufferAttribute,Ir=e.instancedMesh,Nr=e.int,Pr=e.inverseSqrt,Rr=e.inversesqrt,Br=e.invocationLocalIndex,kr=e.invocationSubgroupIndex,Lr=e.ior,Ar=e.iridescence,Gr=e.iridescenceIOR,Or=e.iridescenceThickness,Wr=e.ivec2,jr=e.ivec3,Ur=e.ivec4,qr=e.js,zr=e.label,Er=e.length,Zr=e.lengthSq,Xr=e.lessThan,Yr=e.lessThanEqual,Hr=e.lightPosition,Jr=e.lightTargetDirection,Kr=e.lightTargetPosition,Qr=e.lightViewPosition,$r=e.lightingContext,ea=e.lights,ta=e.linearDepth,ra=e.linearToneMapping,aa=e.localId,oa=e.log,ia=e.log2,na=e.logarithmicDepthToViewZ,sa=e.loop,la=e.luminance,ca=e.mediumpModelViewMatrix,ma=e.mat2,pa=e.mat3,da=e.mat4,ua=e.matcapUV,ga=e.materialAO,ha=e.materialAlphaTest,fa=e.materialAnisotropy,xa=e.materialAnisotropyVector,ba=e.materialAttenuationColor,wa=e.materialAttenuationDistance,va=e.materialClearcoat,Sa=e.materialClearcoatNormal,Ta=e.materialClearcoatRoughness,_a=e.materialColor,Va=e.materialDispersion,ya=e.materialEmissive,Da=e.materialIOR,Ma=e.materialIridescence,Fa=e.materialIridescenceIOR,Ca=e.materialIridescenceThickness,Ia=e.materialLightMap,Na=e.materialLineDashOffset,Pa=e.materialLineDashSize,Ra=e.materialLineGapSize,Ba=e.materialLineScale,ka=e.materialLineWidth,La=e.materialMetalness,Aa=e.materialNormal,Ga=e.materialOpacity,Oa=e.materialPointWidth,Wa=e.materialReference,ja=e.materialReflectivity,Ua=e.materialRefractionRatio,qa=e.materialRotation,za=e.materialRoughness,Ea=e.materialSheen,Za=e.materialSheenRoughness,Xa=e.materialShininess,Ya=e.materialSpecular,Ha=e.materialSpecularColor,Ja=e.materialSpecularIntensity,Ka=e.materialSpecularStrength,Qa=e.materialThickness,$a=e.materialTransmission,eo=e.max,to=e.maxMipLevel,ro=e.metalness,ao=e.min,oo=e.mix,io=e.mixElement,no=e.mod,so=e.modInt,lo=e.modelDirection,co=e.modelNormalMatrix,mo=e.modelPosition,po=e.modelScale,uo=e.modelViewMatrix,go=e.modelViewPosition,ho=e.modelViewProjection,fo=e.modelWorldMatrix,xo=e.modelWorldMatrixInverse,bo=e.morphReference,wo=e.mrt,vo=e.mul,So=e.mx_aastep,To=e.mx_cell_noise_float,_o=e.mx_contrast,Vo=e.mx_fractal_noise_float,yo=e.mx_fractal_noise_vec2,Do=e.mx_fractal_noise_vec3,Mo=e.mx_fractal_noise_vec4,Fo=e.mx_hsvtorgb,Co=e.mx_noise_float,Io=e.mx_noise_vec3,No=e.mx_noise_vec4,Po=e.mx_ramplr,Ro=e.mx_ramptb,Bo=e.mx_rgbtohsv,ko=e.mx_safepower,Lo=e.mx_splitlr,Ao=e.mx_splittb,Go=e.mx_srgb_texture_to_lin_rec709,Oo=e.mx_transform_uv,Wo=e.mx_worley_noise_float,jo=e.mx_worley_noise_vec2,Uo=e.mx_worley_noise_vec3,qo=e.negate,zo=e.neutralToneMapping,Eo=e.nodeArray,Zo=e.nodeImmutable,Xo=e.nodeObject,Yo=e.nodeObjects,Ho=e.nodeProxy,Jo=e.normalFlat,Ko=e.normalGeometry,Qo=e.normalLocal,$o=e.normalMap,ei=e.normalView,ti=e.normalWorld,ri=e.normalize,ai=e.not,oi=e.notEqual,ii=e.numWorkgroups,ni=e.objectDirection,si=e.objectGroup,li=e.objectPosition,ci=e.objectScale,mi=e.objectViewPosition,pi=e.objectWorldMatrix,di=e.oneMinus,ui=e.or,gi=e.orthographicDepthToViewZ,hi=e.oscSawtooth,fi=e.oscSine,xi=e.oscSquare,bi=e.oscTriangle,wi=e.output,vi=e.outputStruct,Si=e.overlay,Ti=e.overloadingFn,_i=e.parabola,Vi=e.parallaxDirection,yi=e.parallaxUV,Di=e.parameter,Mi=e.pass,Fi=e.passTexture,Ci=e.pcurve,Ii=e.perspectiveDepthToViewZ,Ni=e.pmremTexture,Pi=e.pointUV,Ri=e.pointWidth,Bi=e.positionGeometry,ki=e.positionLocal,Li=e.positionPrevious,Ai=e.positionView,Gi=e.positionViewDirection,Oi=e.positionWorld,Wi=e.positionWorldDirection,ji=e.posterize,Ui=e.pow,qi=e.pow2,zi=e.pow3,Ei=e.pow4,Zi=e.property,Xi=e.radians,Yi=e.rand,Hi=e.range,Ji=e.rangeFog,Ki=e.rangeFogFactor,Qi=e.reciprocal,$i=e.reference,en=e.referenceBuffer,tn=e.reflect,rn=e.reflectVector,an=e.reflectView,on=e.reflector,nn=e.refract,sn=e.refractVector,ln=e.refractView,cn=e.reinhardToneMapping,mn=e.remainder,pn=e.remap,dn=e.remapClamp,un=e.renderGroup,gn=e.renderOutput,hn=e.rendererReference,fn=e.rotate,xn=e.rotateUV,bn=e.roughness,wn=e.round,vn=e.rtt,Sn=e.sRGBTransferEOTF,Tn=e.sRGBTransferOETF,_n=e.sampler,Vn=e.saturate,yn=e.saturation,Dn=e.screen,Mn=e.screenCoordinate,Fn=e.screenSize,Cn=e.screenUV,In=e.scriptable,Nn=e.scriptableValue,Pn=e.select,Rn=e.setCurrentStack,Bn=e.shaderStages,kn=e.shadow,Ln=e.shadowPositionWorld,An=e.sharedUniformGroup,Gn=e.sheen,On=e.sheenRoughness,Wn=e.shiftLeft,jn=e.shiftRight,Un=e.shininess,qn=e.sign,zn=e.sin,En=e.sinc,Zn=e.skinning,Xn=e.skinningReference,Yn=e.smoothstep,Hn=e.smoothstepElement,Jn=e.specularColor,Kn=e.specularF90,Qn=e.spherizeUV,$n=e.split,es=e.spritesheetUV,ts=e.sqrt,rs=e.stack,as=e.step,os=e.storage,is=e.storageBarrier,ns=e.storageObject,ss=e.storageTexture,ls=e.string,cs=e.sub,ms=e.subgroupIndex,ps=e.subgroupSize,ds=e.tan,us=e.tangentGeometry,gs=e.tangentLocal,hs=e.tangentView,fs=e.tangentWorld,xs=e.temp,bs=e.texture,ws=e.texture3D,vs=e.textureBarrier,Ss=e.textureBicubic,Ts=e.textureCubeUV,_s=e.textureLoad,Vs=e.textureSize,ys=e.textureStore,Ds=e.thickness,Ms=e.threshold,Fs=e.time,Cs=e.timerDelta,Is=e.timerGlobal,Ns=e.timerLocal,Ps=e.toOutputColorSpace,Rs=e.toWorkingColorSpace,Bs=e.toneMapping,ks=e.toneMappingExposure,Ls=e.toonOutlinePass,As=e.transformDirection,Gs=e.transformNormal,Os=e.transformNormalToView,Ws=e.transformedBentNormalView,js=e.transformedBitangentView,Us=e.transformedBitangentWorld,qs=e.transformedClearcoatNormalView,zs=e.transformedNormalView,Es=e.transformedNormalWorld,Zs=e.transformedTangentView,Xs=e.transformedTangentWorld,Ys=e.transmission,Hs=e.transpose,Js=e.tri,Ks=e.tri3,Qs=e.triNoise3D,$s=e.triplanarTexture,el=e.triplanarTextures,tl=e.trunc,rl=e.tslFn,al=e.uint,ol=e.uniform,il=e.uniformArray,nl=e.uniformGroup,sl=e.uniforms,ll=e.userData,cl=e.uv,ml=e.uvec2,pl=e.uvec3,dl=e.uvec4,ul=e.varying,gl=e.varyingProperty,hl=e.vec2,fl=e.vec3,xl=e.vec4,bl=e.vectorComponents,wl=e.velocity,vl=e.vertexColor,Sl=e.vertexIndex,Tl=e.vibrance,_l=e.viewZToLogarithmicDepth,Vl=e.viewZToOrthographicDepth,yl=e.viewZToPerspectiveDepth,Dl=e.viewport,Ml=e.viewportBottomLeft,Fl=e.viewportCoordinate,Cl=e.viewportDepthTexture,Il=e.viewportLinearDepth,Nl=e.viewportMipTexture,Pl=e.viewportResolution,Rl=e.viewportSafeUV,Bl=e.viewportSharedTexture,kl=e.viewportSize,Ll=e.viewportTexture,Al=e.viewportTopLeft,Gl=e.viewportUV,Ol=e.wgsl,Wl=e.wgslFn,jl=e.workgroupArray,Ul=e.workgroupBarrier,ql=e.workgroupId,zl=e.workingToColorSpace,El=e.xor;export{t as BRDF_GGX,r as BRDF_Lambert,a as BasicShadowFilter,o as Break,i as Continue,n as DFGApprox,s as D_GGX,l as Discard,c as EPSILON,m as F_Schlick,p as Fn,d as INFINITY,u as If,g as Loop,b as NodeAccess,h as NodeShaderStage,f as NodeType,x as NodeUpdateType,w as PCFShadowFilter,v as PCFSoftShadowFilter,S as PI,T as PI2,_ as Return,V as Schlick_to_F0,y as ScriptableNodeResources,D as ShaderNode,M as TBNViewMatrix,F as VSMShadowFilter,C as V_GGX_SmithCorrelated,I as abs,N as acesFilmicToneMapping,P as acos,R as add,B as addNodeElement,k as agxToneMapping,L as all,A as alphaT,G as and,O as anisotropy,W as anisotropyB,j as anisotropyT,U as any,q as append,z as arrayBuffer,E as asin,Z as assign,X as atan,Y as atan2,H as atomicAdd,J as atomicAnd,K as atomicFunc,Q as atomicMax,$ as atomicMin,ee as atomicOr,te as atomicStore,re as atomicSub,ae as atomicXor,oe as attenuationColor,ie as attenuationDistance,ne as attribute,se as attributeArray,le as backgroundBlurriness,ce as backgroundIntensity,me as backgroundRotation,pe as batch,de as billboarding,ue as bitAnd,ge as bitNot,he as bitOr,fe as bitXor,xe as bitangentGeometry,be as bitangentLocal,we as bitangentView,ve as bitangentWorld,Se as bitcast,Te as blendBurn,_e as blendColor,Ve as blendDodge,ye as blendOverlay,De as blendScreen,Me as blur,Fe as bool,Ce as buffer,Ie as bufferAttribute,Ne as bumpMap,Pe as burn,Re as bvec2,Be as bvec3,ke as bvec4,Le as bypass,Ae as cache,Ge as call,Oe as cameraFar,We as cameraNear,je as cameraNormalMatrix,Ue as cameraPosition,qe as cameraProjectionMatrix,ze as cameraProjectionMatrixInverse,Ee as cameraViewMatrix,Ze as cameraWorldMatrix,Xe as cbrt,Ye as cdl,He as ceil,Je as checker,Ke as cineonToneMapping,Qe as clamp,$e as clearcoat,et as clearcoatRoughness,tt as code,rt as color,at as colorSpaceToWorking,ot as colorToDirection,it as compute,nt as cond,st as context,lt as convert,ct as convertColorSpace,mt as convertToTexture,pt as cos,dt as cross,ut as cubeTexture,gt as dFdx,ht as dFdy,ft as dashSize,xt as defaultBuildStages,bt as defaultShaderStages,wt as defined,vt as degrees,St as deltaTime,Tt as densityFog,_t as densityFogFactor,Vt as depth,yt as depthPass,Dt as difference,Mt as diffuseColor,Ft as directPointLight,Ct as directionToColor,It as dispersion,Nt as distance,Pt as div,Rt as dodge,Bt as dot,kt as drawIndex,Lt as dynamicBufferAttribute,At as element,Gt as emissive,Ot as equal,Wt as equals,jt as equirectUV,Ut as exp,qt as exp2,zt as expression,Et as faceDirection,Zt as faceForward,Xt as faceforward,Yt as float,Ht as floor,Jt as fog,Kt as fract,Qt as frameGroup,$t as frameId,er as frontFacing,tr as fwidth,rr as gain,ar as gapSize,or as getConstNodeType,ir as getCurrentStack,nr as getDirection,sr as getDistanceAttenuation,lr as getGeometryRoughness,cr as getNormalFromDepth,mr as getParallaxCorrectNormal,pr as getRoughness,dr as getScreenPosition,ur as getShIrradianceAt,gr as getTextureIndex,hr as getViewPosition,fr as glsl,xr as glslFn,br as grayscale,wr as greaterThan,vr as greaterThanEqual,Sr as hash,Tr as highpModelNormalViewMatrix,_r as highpModelViewMatrix,Vr as hue,yr as instance,Dr as instanceIndex,Mr as instancedArray,Fr as instancedBufferAttribute,Cr as instancedDynamicBufferAttribute,Ir as instancedMesh,Nr as int,Pr as inverseSqrt,Rr as inversesqrt,Br as invocationLocalIndex,kr as invocationSubgroupIndex,Lr as ior,Ar as iridescence,Gr as iridescenceIOR,Or as iridescenceThickness,Wr as ivec2,jr as ivec3,Ur as ivec4,qr as js,zr as label,Er as length,Zr as lengthSq,Xr as lessThan,Yr as lessThanEqual,Hr as lightPosition,Jr as lightTargetDirection,Kr as lightTargetPosition,Qr as lightViewPosition,$r as lightingContext,ea as lights,ta as linearDepth,ra as linearToneMapping,aa as localId,oa as log,ia as log2,na as logarithmicDepthToViewZ,sa as loop,la as luminance,ma as mat2,pa as mat3,da as mat4,ua as matcapUV,ga as materialAO,ha as materialAlphaTest,fa as materialAnisotropy,xa as materialAnisotropyVector,ba as materialAttenuationColor,wa as materialAttenuationDistance,va as materialClearcoat,Sa as materialClearcoatNormal,Ta as materialClearcoatRoughness,_a as materialColor,Va as materialDispersion,ya as materialEmissive,Da as materialIOR,Ma as materialIridescence,Fa as materialIridescenceIOR,Ca as materialIridescenceThickness,Ia as materialLightMap,Na as materialLineDashOffset,Pa as materialLineDashSize,Ra as materialLineGapSize,Ba as materialLineScale,ka as materialLineWidth,La as materialMetalness,Aa as materialNormal,Ga as materialOpacity,Oa as materialPointWidth,Wa as materialReference,ja as materialReflectivity,Ua as materialRefractionRatio,qa as materialRotation,za as materialRoughness,Ea as materialSheen,Za as materialSheenRoughness,Xa as materialShininess,Ya as materialSpecular,Ha as materialSpecularColor,Ja as materialSpecularIntensity,Ka as materialSpecularStrength,Qa as materialThickness,$a as materialTransmission,eo as max,to as maxMipLevel,ca as mediumpModelViewMatrix,ro as metalness,ao as min,oo as mix,io as mixElement,no as mod,so as modInt,lo as modelDirection,co as modelNormalMatrix,mo as modelPosition,po as modelScale,uo as modelViewMatrix,go as modelViewPosition,ho as modelViewProjection,fo as modelWorldMatrix,xo as modelWorldMatrixInverse,bo as morphReference,wo as mrt,vo as mul,So as mx_aastep,To as mx_cell_noise_float,_o as mx_contrast,Vo as mx_fractal_noise_float,yo as mx_fractal_noise_vec2,Do as mx_fractal_noise_vec3,Mo as mx_fractal_noise_vec4,Fo as mx_hsvtorgb,Co as mx_noise_float,Io as mx_noise_vec3,No as mx_noise_vec4,Po as mx_ramplr,Ro as mx_ramptb,Bo as mx_rgbtohsv,ko as mx_safepower,Lo as mx_splitlr,Ao as mx_splittb,Go as mx_srgb_texture_to_lin_rec709,Oo as mx_transform_uv,Wo as mx_worley_noise_float,jo as mx_worley_noise_vec2,Uo as mx_worley_noise_vec3,qo as negate,zo as neutralToneMapping,Eo as nodeArray,Zo as nodeImmutable,Xo as nodeObject,Yo as nodeObjects,Ho as nodeProxy,Jo as normalFlat,Ko as normalGeometry,Qo as normalLocal,$o as normalMap,ei as normalView,ti as normalWorld,ri as normalize,ai as not,oi as notEqual,ii as numWorkgroups,ni as objectDirection,si as objectGroup,li as objectPosition,ci as objectScale,mi as objectViewPosition,pi as objectWorldMatrix,di as oneMinus,ui as or,gi as orthographicDepthToViewZ,hi as oscSawtooth,fi as oscSine,xi as oscSquare,bi as oscTriangle,wi as output,vi as outputStruct,Si as overlay,Ti as overloadingFn,_i as parabola,Vi as parallaxDirection,yi as parallaxUV,Di as parameter,Mi as pass,Fi as passTexture,Ci as pcurve,Ii as perspectiveDepthToViewZ,Ni as pmremTexture,Pi as pointUV,Ri as pointWidth,Bi as positionGeometry,ki as positionLocal,Li as positionPrevious,Ai as positionView,Gi as positionViewDirection,Oi as positionWorld,Wi as positionWorldDirection,ji as posterize,Ui as pow,qi as pow2,zi as pow3,Ei as pow4,Zi as property,Xi as radians,Yi as rand,Hi as range,Ji as rangeFog,Ki as rangeFogFactor,Qi as reciprocal,$i as reference,en as referenceBuffer,tn as reflect,rn as reflectVector,an as reflectView,on as reflector,nn as refract,sn as refractVector,ln as refractView,cn as reinhardToneMapping,mn as remainder,pn as remap,dn as remapClamp,un as renderGroup,gn as renderOutput,hn as rendererReference,fn as rotate,xn as rotateUV,bn as roughness,wn as round,vn as rtt,Sn as sRGBTransferEOTF,Tn as sRGBTransferOETF,_n as sampler,Vn as saturate,yn as saturation,Dn as screen,Mn as screenCoordinate,Fn as screenSize,Cn as screenUV,In as scriptable,Nn as scriptableValue,Pn as select,Rn as setCurrentStack,Bn as shaderStages,kn as shadow,Ln as shadowPositionWorld,An as sharedUniformGroup,Gn as sheen,On as sheenRoughness,Wn as shiftLeft,jn as shiftRight,Un as shininess,qn as sign,zn as sin,En as sinc,Zn as skinning,Xn as skinningReference,Yn as smoothstep,Hn as smoothstepElement,Jn as specularColor,Kn as specularF90,Qn as spherizeUV,$n as split,es as spritesheetUV,ts as sqrt,rs as stack,as as step,os as storage,is as storageBarrier,ns as storageObject,ss as storageTexture,ls as string,cs as sub,ms as subgroupIndex,ps as subgroupSize,ds as tan,us as tangentGeometry,gs as tangentLocal,hs as tangentView,fs as tangentWorld,xs as temp,bs as texture,ws as texture3D,vs as textureBarrier,Ss as textureBicubic,Ts as textureCubeUV,_s as textureLoad,Vs as textureSize,ys as textureStore,Ds as thickness,Ms as threshold,Fs as time,Cs as timerDelta,Is as timerGlobal,Ns as timerLocal,Ps as toOutputColorSpace,Rs as toWorkingColorSpace,Bs as toneMapping,ks as toneMappingExposure,Ls as toonOutlinePass,As as transformDirection,Gs as transformNormal,Os as transformNormalToView,Ws as transformedBentNormalView,js as transformedBitangentView,Us as transformedBitangentWorld,qs as transformedClearcoatNormalView,zs as transformedNormalView,Es as transformedNormalWorld,Zs as transformedTangentView,Xs as transformedTangentWorld,Ys as transmission,Hs as transpose,Js as tri,Ks as tri3,Qs as triNoise3D,$s as triplanarTexture,el as triplanarTextures,tl as trunc,rl as tslFn,al as uint,ol as uniform,il as uniformArray,nl as uniformGroup,sl as uniforms,ll as userData,cl as uv,ml as uvec2,pl as uvec3,dl as uvec4,ul as varying,gl as varyingProperty,hl as vec2,fl as vec3,xl as vec4,bl as vectorComponents,wl as velocity,vl as vertexColor,Sl as vertexIndex,Tl as vibrance,_l as viewZToLogarithmicDepth,Vl as viewZToOrthographicDepth,yl as viewZToPerspectiveDepth,Dl as viewport,Ml as viewportBottomLeft,Fl as viewportCoordinate,Cl as viewportDepthTexture,Il as viewportLinearDepth,Nl as viewportMipTexture,Pl as viewportResolution,Rl as viewportSafeUV,Bl as viewportSharedTexture,kl as viewportSize,Ll as viewportTexture,Al as viewportTopLeft,Gl as viewportUV,Ol as wgsl,Wl as wgslFn,jl as workgroupArray,Ul as workgroupBarrier,ql as workgroupId,zl as workingToColorSpace,El as xor};
