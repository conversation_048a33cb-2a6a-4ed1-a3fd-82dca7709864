"use strict";
const _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm = require("../../common/chunks/@dcloudio/uni-mp-weixin/dist/uni.api.esm.js");
const _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm = require("../../common/chunks/@dcloudio/uni-mp-vue/dist/vue.runtime.esm.js");
const _mpChunkDeps_three_build_three_module_min = require("../chunks/three/build/three.module.min.js");
const _mpChunkDeps_three_examples_jsm_controls_OrbitControls = require("../chunks/three/examples/jsm/controls/OrbitControls.js");
const subPack2_particles_core_settings = require("./core/settings.js");
const subPack2_particles_3d_Lights = require("./3d/Lights.js");
const subPack2_particles_3d_Floor = require("./3d/Floor.js");
const subPack2_particles_utils_ease = require("./utils/ease.js");
const subPack2_particles_3d_Simulator = require("./3d/Simulator.js");
const subPack2_particles_3d_Particles = require("./3d/Particles.js");
const _mpChunkDeps_three_build_three_core_min = require("../chunks/three/build/three.core.min.js");
const common_vendor = require("../../common/vendor.js");
if (!Math) {
  PlatformCanvas();
}
const PlatformCanvas = () => "../../components/PlatformCanvas.js";
const _sfc_main = /* @__PURE__ */ _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.defineComponent({
  __name: "index",
  setup(__props) {
    const props = _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.reactive({
      play: true
    });
    const scope = _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.effectScope();
    const fps = _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.ref("0");
    const enableFPS = _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.ref(false);
    const isIOS = _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.wx$1.getDeviceInfo().platform === "ios";
    function useCanvas({ canvas, useFrame, eventHandler }) {
      const CANVAS_WIDTH = canvas.width;
      const CANVAS_HEIGHT = canvas.height;
      const _bgColor = new _mpChunkDeps_three_build_three_core_min.$r(subPack2_particles_core_settings.settings.bgColor);
      subPack2_particles_core_settings.settings.mouse = new _mpChunkDeps_three_build_three_core_min.Zs(0, 0);
      subPack2_particles_core_settings.settings.mouse3d = subPack2_particles_core_settings.ray.origin;
      const renderer = new _mpChunkDeps_three_build_three_module_min.WebGLRenderer({
        canvas,
        antialias: true
      });
      const supportedFloatType = renderer.extensions.get("EXT_color_buffer_float");
      enableFPS.value = !!supportedFloatType;
      renderer.setClearColor(subPack2_particles_core_settings.settings.bgColor);
      renderer.shadowMap.enabled = true;
      const scene = new _mpChunkDeps_three_build_three_core_min.ro();
      scene.fog = new _mpChunkDeps_three_build_three_core_min.so(subPack2_particles_core_settings.settings.bgColor, 1e-3);
      const camera = new _mpChunkDeps_three_build_three_core_min.$n(
        45,
        CANVAS_WIDTH / CANVAS_HEIGHT,
        10,
        3e3
      );
      camera.position.set(300, 60, 300).normalize().multiplyScalar(1500);
      subPack2_particles_core_settings.settings.camera = camera;
      subPack2_particles_core_settings.settings.cameraPosition = camera.position;
      const lights = new subPack2_particles_3d_Lights.Lights();
      scene.add(lights);
      const floor = new subPack2_particles_3d_Floor.Floor();
      scene.add(floor);
      let simulator, particles;
      if (supportedFloatType) {
        simulator = new subPack2_particles_3d_Simulator.Simulator(renderer);
        particles = new subPack2_particles_3d_Particles.Particles(renderer);
        scene.add(particles);
      }
      const control = new _mpChunkDeps_three_examples_jsm_controls_OrbitControls.OrbitControls(camera, renderer.domElement);
      control.target.y = 50;
      control.maxDistance = 1e3;
      control.minPolarAngle = 0.3;
      control.maxPolarAngle = Math.PI / 2 - 0.1;
      control.update();
      let initAnimation = 0;
      const colorCycle = 3;
      let frames = 0;
      let prevUpdate = Date.now();
      function render(dt) {
        const deltaTime = Date.now() - prevUpdate;
        frames++;
        if (deltaTime > 1e3) {
          fps.value = (frames / (deltaTime / 1e3)).toFixed(1);
          frames = 0;
          prevUpdate = Date.now();
        }
        const targetColor = getRainbowColor(Math.floor(Date.now() / (10 * colorCycle) % 100));
        subPack2_particles_core_settings.settings.color2 = targetColor;
        _bgColor.setStyle(subPack2_particles_core_settings.settings.bgColor);
        const tmpColor = floor.material.color;
        tmpColor.lerp(_bgColor, 0.05);
        scene.fog.color.copy(tmpColor);
        renderer.setClearColor(tmpColor);
        initAnimation = Math.min(initAnimation + dt * 25e-5, 1);
        supportedFloatType && (simulator.initAnimation = initAnimation);
        control.maxDistance = initAnimation === 1 ? 1e3 : _mpChunkDeps_three_build_three_core_min.Ys.lerp(1e3, 450, subPack2_particles_utils_ease.basic.Cubic.Out(initAnimation));
        control.update();
        camera.updateMatrixWorld();
        subPack2_particles_core_settings.ray.origin.setFromMatrixPosition(camera.matrixWorld);
        subPack2_particles_core_settings.ray.direction.set(subPack2_particles_core_settings.settings.mouse.x, subPack2_particles_core_settings.settings.mouse.y, 0.5).unproject(camera).sub(subPack2_particles_core_settings.ray.origin).normalize();
        const distance = subPack2_particles_core_settings.ray.origin.length() / Math.cos(Math.PI - subPack2_particles_core_settings.ray.direction.angleTo(subPack2_particles_core_settings.ray.origin));
        subPack2_particles_core_settings.ray.origin.add(subPack2_particles_core_settings.ray.direction.multiplyScalar(distance));
        if (supportedFloatType) {
          simulator.update(dt);
          particles.update(dt, simulator);
        }
        renderer.render(scene, camera);
        lights.update(renderer);
      }
      function onResize() {
        camera.aspect = CANVAS_WIDTH / CANVAS_HEIGHT;
        camera.updateProjectionMatrix();
        renderer.setSize(CANVAS_WIDTH, CANVAS_HEIGHT, false);
      }
      function onMouseMove(evt) {
        subPack2_particles_core_settings.settings.mouse.x = evt.pageX / CANVAS_WIDTH * 2 - 1;
        subPack2_particles_core_settings.settings.mouse.y = -(evt.pageY / CANVAS_HEIGHT) * 2 + 1;
      }
      canvas.addEventListener("resize", onResize);
      canvas.addEventListener("click", onMouseMove);
      let firstMotion;
      const throttle = 40;
      const globalScale = isIOS ? -1 : 1;
      _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.wx$1.onDeviceMotionChange((data) => {
        if (!firstMotion) {
          firstMotion = [data.beta, data.gamma];
        } else {
          const deltaY = (data.beta - firstMotion[0]) / throttle;
          const deltaX = (data.gamma - firstMotion[1]) * -1 / throttle;
          subPack2_particles_core_settings.settings.mouse.x = globalScale * Math.min(0.9, Math.max(-0.9, deltaX));
          subPack2_particles_core_settings.settings.mouse.y = globalScale * Math.min(0.9, Math.max(-0.9, deltaY));
        }
      });
      scope.run(() => {
        _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.watchEffect((onCleanup) => {
          if (props.play) {
            const { cancel } = useFrame((delta) => {
              render(delta);
            });
            _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.wx$1.startDeviceMotionListening({
              interval: "game",
              success() {
                firstMotion = void 0;
              },
              fail() {
                subPack2_particles_core_settings.settings.followMouse = false;
              }
            });
            onCleanup(() => {
              _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.wx$1.stopDeviceMotionListening({
                success() {
                },
                fail() {
                }
              });
              cancel();
            });
          }
        });
      });
    }
    function getRainbowColor(percentage) {
      percentage = Math.max(0, Math.min(100, percentage));
      const t = percentage / 100;
      const colors = [
        { r: 255, g: 0, b: 0 },
        // 红色
        { r: 255, g: 127, b: 0 },
        // 橙色
        { r: 255, g: 255, b: 0 },
        // 黄色
        { r: 0, g: 255, b: 0 },
        // 绿色
        { r: 0, g: 255, b: 255 },
        // 青色
        { r: 0, g: 0, b: 255 },
        // 蓝色
        { r: 148, g: 0, b: 211 }
        // 紫色
      ];
      const segmentCount = colors.length - 1;
      const segment = t * segmentCount;
      const segmentIndex = Math.floor(segment);
      const nextSegmentIndex = Math.min(segmentIndex + 1, segmentCount);
      const startColor = colors[segmentIndex];
      const endColor = colors[nextSegmentIndex];
      const blendFactor = segment - segmentIndex;
      const r = Math.round(startColor.r + blendFactor * (endColor.r - startColor.r));
      const g = Math.round(startColor.g + blendFactor * (endColor.g - startColor.g));
      const b = Math.round(startColor.b + blendFactor * (endColor.b - startColor.b));
      return `#${[r, g, b].map((item) => item.toString(16).padStart(2, "0")).join("")}`;
    }
    return (_ctx, _cache) => {
      return _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.e({
        a: _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.o(useCanvas),
        b: _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.p({
          type: "webgl2",
          ["canvas-id"]: "in-spirit-canvas"
        }),
        c: enableFPS.value
      }, enableFPS.value ? {
        d: _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.t(fps.value)
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-73d662c8"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/sub-pack-2/particles/index.js.map
