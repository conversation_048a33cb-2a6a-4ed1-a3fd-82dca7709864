"use strict";
const common_vendor = require("../../common/vendor.js");
const PlatformCanvas = () => "../../components/PlatformCanvas.js";
const _sfc_main = {
  components: {
    PlatformCanvas
  },
  data() {
    return {
      message: "数字人演示",
      camera: null,
      scene: null,
      renderer: null,
      controls: null,
      mixer: null
    };
  },
  onLoad() {
    console.log("页面onLoad执行");
  },
  onReady() {
    console.log("页面onReady执行");
    console.log("当前平台:", common_vendor.index.getSystemInfoSync().platform);
  },
  onUnload() {
    if (this.mixer) {
      this.mixer.stopAllAction();
    }
  },
  methods: {
    useCanvas({ canvas, useFrame, recomputeSize }) {
      console.log("Canvas初始化成功，开始设置Three.js场景");
      const CANVAS_WIDTH = canvas.width;
      const CANVAS_HEIGHT = canvas.height;
      this.init(canvas, CANVAS_WIDTH, CANVAS_HEIGHT);
      this.render();
    },
    init(canvas, width, height) {
      console.log("初始化Three.js渲染器和场景");
      this.renderer = new common_vendor.WebGLRenderer({ canvas, antialias: true });
      this.renderer.setPixelRatio(common_vendor.index.getSystemInfoSync().pixelRatio || 1);
      this.renderer.setSize(width, height);
      this.renderer.toneMapping = common_vendor.ACESFilmicToneMapping;
      this.renderer.toneMappingExposure = 1;
      this.camera = new common_vendor.PerspectiveCamera(45, width / height, 1, 2e3);
      this.camera.position.set(0, 100, 0);
      const environment = new common_vendor.RoomEnvironment();
      const pmremGenerator = new common_vendor.PMREMGenerator(this.renderer);
      this.scene = new common_vendor.Scene();
      this.scene.background = new common_vendor.Color(12303291);
      this.scene.environment = pmremGenerator.fromScene(environment).texture;
      environment.dispose();
      const grid = new common_vendor.GridHelper(500, 10, 16777215, 16777215);
      grid.material.opacity = 0.5;
      grid.material.depthWrite = false;
      grid.material.transparent = true;
      this.scene.add(grid);
      this.controls = new common_vendor.OrbitControls(this.camera, this.renderer.domElement);
      this.controls.addEventListener("change", () => this.render());
      this.controls.minDistance = 400;
      this.controls.maxDistance = 1e3;
      this.controls.target.set(10, 90, -16);
      this.controls.update();
      this.loadGLBModel("https://reader.c8plus.net/statix/Teacher.glb");
      console.log("Three.js场景初始化完成");
    },
    loadGLBModel(modelUrl) {
      console.log("开始加载网络数字人模型:", modelUrl);
      common_vendor.index.showLoading({
        title: "加载数字人模型..."
      });
      this.scene.children.forEach((child) => {
        if (child.isScene || child.type === "Group") {
          this.scene.remove(child);
        }
      });
      const ktx2Loader = new common_vendor.KTX2Loader().setWorkerLimit(1).setTranscoderPath("https://threejs.org/examples/jsm/libs/basis/").detectSupport(this.renderer);
      const loader = new common_vendor.GLTFLoader();
      loader.setKTX2Loader(ktx2Loader);
      loader.setMeshoptDecoder(common_vendor.MeshoptDecoder);
      loader.load(
        modelUrl,
        (gltf) => {
          console.log("网络数字人模型加载成功:", gltf);
          common_vendor.index.hideLoading();
          const model = gltf.scene;
          const animations = gltf.animations;
          console.log("模型动画列表:", animations);
          console.log("模型详细信息:", gltf);
          const box = new common_vendor.Box3().setFromObject(model);
          const size = box.getSize(new common_vendor.Vector3());
          const center = box.getCenter(new common_vendor.Vector3());
          console.log("模型尺寸:", size);
          console.log("模型中心:", center);
          const maxSize = Math.max(size.x, size.y, size.z);
          const targetSize = 100;
          const scale = targetSize / maxSize;
          model.scale.set(scale, scale, scale);
          model.position.set(-center.x * scale, -center.y * scale + 50, -center.z * scale);
          this.scene.add(model);
          this.render();
          if (animations && animations.length > 0) {
            this.mixer = new common_vendor.AnimationMixer(model);
            animations.forEach((clip, index) => {
              console.log(`播放动画 ${index + 1}:`, clip.name);
              const action = this.mixer.clipAction(clip);
              action.play();
            });
            this.startAnimationLoop();
          }
          this.camera.position.set(0, 100, 200);
          this.controls.target.set(0, 50, 0);
          this.controls.update();
          common_vendor.index.showToast({
            title: "数字人加载成功！",
            icon: "success"
          });
        },
        (progress) => {
          const percent = Math.round(progress.loaded / progress.total * 100);
          console.log(`加载进度: ${percent}%`);
          common_vendor.index.showLoading({
            title: `加载中... ${percent}%`
          });
        },
        (error) => {
          console.error("加载网络数字人模型失败:", error);
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "数字人加载失败",
            icon: "error"
          });
          this.createFallbackModel();
        }
      );
    },
    startAnimationLoop() {
      const animate = () => {
        if (this.mixer) {
          this.mixer.update(0.016);
        }
        this.render();
        setTimeout(animate, 16);
      };
      animate();
    },
    createFallbackModel() {
      console.log("创建替代数字人模型");
      const group = new common_vendor.Group();
      const headGeometry = new common_vendor.SphereGeometry(15, 16, 16);
      const headMaterial = new common_vendor.MeshLambertMaterial({ color: 16767916 });
      const head = new common_vendor.Mesh(headGeometry, headMaterial);
      head.position.y = 170;
      group.add(head);
      const bodyGeometry = new common_vendor.CylinderGeometry(20, 30, 80, 8);
      const bodyMaterial = new common_vendor.MeshLambertMaterial({ color: 4286945 });
      const body = new common_vendor.Mesh(bodyGeometry, bodyMaterial);
      body.position.y = 120;
      group.add(body);
      const legGeometry = new common_vendor.CylinderGeometry(8, 10, 60, 8);
      const legMaterial = new common_vendor.MeshLambertMaterial({ color: 3100495 });
      const leftLeg = new common_vendor.Mesh(legGeometry, legMaterial);
      leftLeg.position.set(-15, 50, 0);
      group.add(leftLeg);
      const rightLeg = new common_vendor.Mesh(legGeometry, legMaterial);
      rightLeg.position.set(15, 50, 0);
      group.add(rightLeg);
      this.scene.add(group);
      this.render();
      console.log("替代模型创建完成");
    },
    render() {
      if (this.renderer && this.scene && this.camera) {
        this.renderer.render(this.scene, this.camera);
      }
    }
  }
};
if (!Array) {
  const _component_PlatformCanvas = common_vendor.resolveComponent("PlatformCanvas");
  _component_PlatformCanvas();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o($options.useCanvas),
    b: common_vendor.p({
      type: "webgl",
      ["canvas-id"]: "webglCanvas"
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
