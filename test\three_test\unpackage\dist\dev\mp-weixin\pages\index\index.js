"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      message: "GLTF导出器演示",
      canvas: null,
      scene1: null,
      scene2: null,
      camera: null,
      renderer: null,
      animationId: null,
      animationTime: 0,
      objects: [],
      sphere: null,
      gridHelper: null
    };
  },
  onLoad() {
    console.log("页面onLoad执行");
  },
  onReady() {
    console.log("页面onReady执行");
    console.log("当前平台:", common_vendor.index.getSystemInfoSync().platform);
    setTimeout(() => {
      this.initWebGL();
    }, 500);
  },
  onUnload() {
    if (this.animationId) {
      clearTimeout(this.animationId);
    }
  },
  methods: {
    initWebGL() {
      console.log("开始初始化GLTF导出器演示");
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select("#webglCanvas").fields({
        node: true,
        size: true
      }).exec((res) => {
        console.log("Canvas查询结果:", res);
        if (res[0] && res[0].node) {
          const canvas = res[0].node;
          console.log("Canvas节点获取成功:", canvas);
          const gl = canvas.getContext("webgl") || canvas.getContext("experimental-webgl");
          if (gl) {
            console.log("WebGL上下文获取成功!");
            this.canvas = canvas;
            this.setupThreeJS();
          } else {
            console.error("无法获取WebGL上下文");
            this.fallbackTo2D(canvas);
          }
        } else {
          console.error("无法找到canvas节点");
        }
      });
    },
    setupThreeJS() {
      console.log("设置Three.js GLTF导出器场景");
      console.log("微信小程序环境不支持three.js，回退到2D模拟");
      this.fallbackTo2D(this.canvas);
    },
    createAnimationGroup(THREE) {
      console.log("创建动画组");
      const animationGroup = new THREE.AnimationObjectGroup();
      const geometry = new THREE.BoxGeometry(5, 5, 5);
      const material = new THREE.MeshBasicMaterial({ transparent: true });
      for (let i = 0; i < 5; i++) {
        for (let j = 0; j < 5; j++) {
          const mesh = new THREE.Mesh(geometry, material);
          mesh.position.x = 32 - 16 * i;
          mesh.position.y = 0;
          mesh.position.z = 32 - 16 * j;
          this.scene.add(mesh);
          animationGroup.add(mesh);
        }
      }
      const xAxis = new THREE.Vector3(1, 0, 0);
      const qInitial = new THREE.Quaternion().setFromAxisAngle(xAxis, 0);
      const qFinal = new THREE.Quaternion().setFromAxisAngle(xAxis, Math.PI);
      const quaternionKF = new THREE.QuaternionKeyframeTrack(
        ".quaternion",
        [0, 1, 2],
        [
          qInitial.x,
          qInitial.y,
          qInitial.z,
          qInitial.w,
          qFinal.x,
          qFinal.y,
          qFinal.z,
          qFinal.w,
          qInitial.x,
          qInitial.y,
          qInitial.z,
          qInitial.w
        ]
      );
      const colorKF = new THREE.ColorKeyframeTrack(
        ".material.color",
        [0, 1, 2],
        [1, 0, 0, 0, 1, 0, 0, 0, 1],
        THREE.InterpolateDiscrete
      );
      const opacityKF = new THREE.NumberKeyframeTrack(".material.opacity", [0, 1, 2], [1, 0, 1]);
      const clip = new THREE.AnimationClip("default", 3, [quaternionKF, colorKF, opacityKF]);
      this.mixer = new THREE.AnimationMixer(animationGroup);
      const clipAction = this.mixer.clipAction(clip);
      clipAction.play();
      console.log("动画组创建完成，包含25个立方体");
    },
    animate() {
      if (!this.renderer || !this.scene || !this.camera)
        return;
      const delta = this.clock.getDelta();
      if (this.mixer) {
        this.mixer.update(delta);
      }
      this.renderer.render(this.scene, this.camera);
      this.animationId = setTimeout(() => this.animate(), 16);
    },
    fallbackTo2D(canvas) {
      console.log("开始2D Canvas模拟GLTF导出器场景");
      let ctx;
      try {
        ctx = canvas.getContext("2d");
      } catch (error) {
        console.error("getContext错误:", error);
        this.fallbackToUniCanvas();
        return;
      }
      if (!ctx) {
        console.error("无法获取2D上下文，尝试uni方式");
        this.fallbackToUniCanvas();
        return;
      }
      const dpr = common_vendor.index.getSystemInfoSync().pixelRatio;
      canvas.width = 300 * dpr;
      canvas.height = 300 * dpr;
      ctx.scale(dpr, dpr);
      this.animationTime = 0;
      this.objects = [];
      this.createGLTFScene();
      this.animate2D(ctx);
      console.log("2D GLTF导出器场景模拟启动");
    },
    createGLTFScene() {
      console.log("创建GLTF导出器场景对象");
      this.objects = [
        // 球体 (中心)
        {
          type: "sphere",
          x: 150,
          y: 150,
          size: 30,
          color: { r: 1, g: 1, b: 0 },
          rotation: 0,
          name: "Sphere"
        },
        // 立方体
        {
          type: "cube",
          x: 80,
          y: 80,
          size: 25,
          color: { r: 1, g: 0, b: 0 },
          rotation: 0,
          name: "Cube"
        },
        // 圆柱体
        {
          type: "cylinder",
          x: 220,
          y: 80,
          size: 20,
          color: { r: 0, g: 1, b: 0 },
          rotation: 0,
          name: "Cylinder"
        },
        // 三角形 (模拟四面体)
        {
          type: "triangle",
          x: 80,
          y: 220,
          size: 25,
          color: { r: 0, g: 0, b: 1 },
          rotation: 0,
          name: "Tetrahedron"
        },
        // 八边形 (模拟八面体)
        {
          type: "octagon",
          x: 220,
          y: 220,
          size: 20,
          color: { r: 1, g: 0, b: 1 },
          rotation: 0,
          name: "Octahedron"
        },
        // 环形 (模拟环面结)
        {
          type: "ring",
          x: 150,
          y: 80,
          size: 18,
          color: { r: 0, g: 1, b: 1 },
          rotation: 0,
          name: "TorusKnot"
        }
      ];
      console.log(`创建了${this.objects.length}个几何体对象`);
    },
    animate2D(ctx) {
      if (!ctx)
        return;
      this.animationTime += 0.016;
      const timer = this.animationTime * 1e-4;
      ctx.fillStyle = "#222";
      ctx.fillRect(0, 0, 300, 300);
      this.drawGrid(ctx);
      this.objects.forEach((obj, index) => {
        obj.rotation = timer * (index + 1) * 0.5;
        this.drawGeometry2D(ctx, obj);
      });
      ctx.fillStyle = "#fff";
      ctx.font = "12px Arial";
      ctx.textAlign = "left";
      ctx.fillText("2D模拟 GLTF导出器场景", 10, 20);
      ctx.fillText(`${this.objects.length}个几何体对象`, 10, 35);
      ctx.fillText(`时间: ${this.animationTime.toFixed(1)}s`, 10, 50);
      this.objects.forEach((obj, index) => {
        ctx.fillStyle = `rgb(${obj.color.r * 255}, ${obj.color.g * 255}, ${obj.color.b * 255})`;
        ctx.fillText(`${obj.name}`, 10, 80 + index * 15);
      });
      this.animationId = setTimeout(() => this.animate2D(ctx), 16);
    },
    drawGrid(ctx) {
      ctx.strokeStyle = "#444";
      ctx.lineWidth = 1;
      ctx.beginPath();
      for (let x = 0; x <= 300; x += 30) {
        ctx.moveTo(x, 0);
        ctx.lineTo(x, 300);
      }
      for (let y = 0; y <= 300; y += 30) {
        ctx.moveTo(0, y);
        ctx.lineTo(300, y);
      }
      ctx.stroke();
    },
    drawGeometry2D(ctx, obj) {
      ctx.save();
      ctx.translate(obj.x, obj.y);
      ctx.rotate(obj.rotation);
      const r = Math.floor(obj.color.r * 255);
      const g = Math.floor(obj.color.g * 255);
      const b = Math.floor(obj.color.b * 255);
      ctx.fillStyle = `rgb(${r}, ${g}, ${b})`;
      ctx.strokeStyle = `rgb(${Math.min(r + 50, 255)}, ${Math.min(g + 50, 255)}, ${Math.min(b + 50, 255)})`;
      ctx.lineWidth = 2;
      switch (obj.type) {
        case "sphere":
          this.drawSphere2D(ctx, obj.size);
          break;
        case "cube":
          this.drawCube2D(ctx, obj.size);
          break;
        case "cylinder":
          this.drawCylinder2D(ctx, obj.size);
          break;
        case "triangle":
          this.drawTriangle2D(ctx, obj.size);
          break;
        case "octagon":
          this.drawOctagon2D(ctx, obj.size);
          break;
        case "ring":
          this.drawRing2D(ctx, obj.size);
          break;
      }
      ctx.restore();
    },
    drawSphere2D(ctx, size) {
      ctx.beginPath();
      ctx.arc(0, 0, size, 0, Math.PI * 2);
      ctx.fill();
      ctx.stroke();
      ctx.fillStyle = "rgba(255, 255, 255, 0.3)";
      ctx.beginPath();
      ctx.arc(-size / 3, -size / 3, size / 3, 0, Math.PI * 2);
      ctx.fill();
    },
    drawCube2D(ctx, size) {
      const half = size / 2;
      ctx.fillRect(-half, -half, size, size);
      ctx.strokeRect(-half, -half, size, size);
      ctx.beginPath();
      ctx.moveTo(-half, -half);
      ctx.lineTo(half, half);
      ctx.moveTo(half, -half);
      ctx.lineTo(-half, half);
      ctx.stroke();
    },
    drawCylinder2D(ctx, size) {
      ctx.beginPath();
      ctx.ellipse(0, 0, size, size * 0.6, 0, 0, Math.PI * 2);
      ctx.fill();
      ctx.stroke();
      ctx.strokeStyle = "rgba(255, 255, 255, 0.5)";
      ctx.beginPath();
      ctx.ellipse(0, -size * 0.3, size * 0.8, size * 0.2, 0, 0, Math.PI * 2);
      ctx.stroke();
    },
    drawTriangle2D(ctx, size) {
      ctx.beginPath();
      ctx.moveTo(0, -size);
      ctx.lineTo(-size * 0.866, size * 0.5);
      ctx.lineTo(size * 0.866, size * 0.5);
      ctx.closePath();
      ctx.fill();
      ctx.stroke();
    },
    drawOctagon2D(ctx, size) {
      ctx.beginPath();
      for (let i = 0; i < 8; i++) {
        const angle = i / 8 * Math.PI * 2;
        const x = Math.cos(angle) * size;
        const y = Math.sin(angle) * size;
        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }
      ctx.closePath();
      ctx.fill();
      ctx.stroke();
    },
    drawRing2D(ctx, size) {
      ctx.beginPath();
      ctx.arc(0, 0, size, 0, Math.PI * 2);
      ctx.arc(0, 0, size * 0.5, 0, Math.PI * 2, true);
      ctx.fill();
      ctx.stroke();
    },
    fallbackToUniCanvas() {
      console.log("使用uni.createCanvasContext方式");
      const ctx = common_vendor.index.createCanvasContext("webglCanvas", this);
      if (!ctx) {
        console.error("uni.createCanvasContext也失败了");
        this.showSimpleMessage();
        return;
      }
      this.animationTime = 0;
      this.createGLTFScene();
      this.animateUniCanvas(ctx);
      console.log("uni Canvas GLTF场景动画启动");
    },
    animateUniCanvas(ctx) {
      if (!ctx)
        return;
      this.animationTime += 0.016;
      const timer = this.animationTime * 1e-4;
      ctx.setFillStyle("#222");
      ctx.fillRect(0, 0, 300, 300);
      this.drawGridUni(ctx);
      this.objects.forEach((obj, index) => {
        obj.rotation = timer * (index + 1) * 0.5;
        this.drawGeometryUni(ctx, obj);
      });
      ctx.setFillStyle("#fff");
      ctx.setFontSize(12);
      ctx.fillText("2D模拟 GLTF导出器场景", 10, 20);
      ctx.fillText(`${this.objects.length}个几何体对象`, 10, 35);
      ctx.fillText(`时间: ${this.animationTime.toFixed(1)}s`, 10, 50);
      this.objects.forEach((obj, index) => {
        ctx.setFillStyle(`rgb(${obj.color.r * 255}, ${obj.color.g * 255}, ${obj.color.b * 255})`);
        ctx.fillText(`${obj.name}`, 10, 80 + index * 15);
      });
      ctx.draw();
      this.animationId = setTimeout(() => this.animateUniCanvas(ctx), 16);
    },
    drawGridUni(ctx) {
      ctx.setStrokeStyle("#444");
      ctx.setLineWidth(1);
      ctx.beginPath();
      for (let i = 0; i <= 300; i += 30) {
        ctx.moveTo(i, 0);
        ctx.lineTo(i, 300);
        ctx.moveTo(0, i);
        ctx.lineTo(300, i);
      }
      ctx.stroke();
    },
    drawGeometryUni(ctx, obj) {
      ctx.save();
      ctx.translate(obj.x, obj.y);
      ctx.rotate(obj.rotation);
      const r = Math.floor(obj.color.r * 255);
      const g = Math.floor(obj.color.g * 255);
      const b = Math.floor(obj.color.b * 255);
      ctx.setFillStyle(`rgb(${r}, ${g}, ${b})`);
      ctx.setStrokeStyle(`rgb(${Math.min(r + 50, 255)}, ${Math.min(g + 50, 255)}, ${Math.min(b + 50, 255)})`);
      ctx.setLineWidth(2);
      const size = obj.size;
      const half = size / 2;
      switch (obj.type) {
        case "sphere":
          ctx.beginPath();
          ctx.arc(0, 0, size, 0, Math.PI * 2);
          ctx.fill();
          ctx.stroke();
          break;
        case "cube":
          ctx.fillRect(-half, -half, size, size);
          ctx.strokeRect(-half, -half, size, size);
          break;
        case "triangle":
          ctx.beginPath();
          ctx.moveTo(0, -size);
          ctx.lineTo(-size * 0.866, size * 0.5);
          ctx.lineTo(size * 0.866, size * 0.5);
          ctx.closePath();
          ctx.fill();
          ctx.stroke();
          break;
        default:
          ctx.beginPath();
          ctx.arc(0, 0, size, 0, Math.PI * 2);
          ctx.fill();
          ctx.stroke();
          break;
      }
      ctx.restore();
    },
    showSimpleMessage() {
      console.log("显示简单文本消息");
      common_vendor.index.showToast({
        title: "GLTF导出器演示",
        icon: "none",
        duration: 3e3
      });
    },
    onTouchStart() {
      console.log("触摸开始");
    },
    onTouchMove() {
      console.log("触摸移动");
    },
    onTouchEnd() {
      console.log("触摸结束");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.onTouchStart && $options.onTouchStart(...args)),
    b: common_vendor.o((...args) => $options.onTouchMove && $options.onTouchMove(...args)),
    c: common_vendor.o((...args) => $options.onTouchEnd && $options.onTouchEnd(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
