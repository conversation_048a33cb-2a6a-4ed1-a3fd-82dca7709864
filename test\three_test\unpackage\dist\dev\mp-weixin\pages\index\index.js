"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      message: "数字人演示",
      canvas: null,
      scene: null,
      camera: null,
      renderer: null,
      animationId: null,
      model: null,
      controls: null,
      mixer: null,
      clock: null
    };
  },
  onLoad() {
    console.log("页面onLoad执行");
  },
  onReady() {
    console.log("页面onReady执行");
    console.log("当前平台:", common_vendor.index.getSystemInfoSync().platform);
    setTimeout(() => {
      this.initThreeJS();
    }, 500);
  },
  onUnload() {
    this.cleanup();
  },
  methods: {
    initThreeJS() {
      console.log("开始初始化数字人场景");
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select("#webglCanvas").fields({
        node: true,
        size: true
      }).exec((res) => {
        console.log("Canvas查询结果:", res);
        if (res[0] && res[0].node) {
          const canvas = res[0].node;
          console.log("Canvas节点获取成功:", canvas);
          this.canvas = canvas;
          try {
            this.setupThreeJSScene();
          } catch (error) {
            console.error("Three.js初始化失败:", error);
            this.fallbackTo2D(canvas);
          }
        } else {
          console.error("无法找到canvas节点");
        }
      });
    },
    setupThreeJSScene() {
      console.log("设置Three.js数字人场景");
      const canvas = this.canvas;
      const width = canvas.width || 300;
      const height = canvas.height || 300;
      this.renderer = new common_vendor.WebGLRenderer({
        canvas,
        antialias: true,
        alpha: true
      });
      this.renderer.setSize(width, height);
      this.renderer.setPixelRatio(Math.min(window.devicePixelRatio || 1, 2));
      this.renderer.shadowMap.enabled = true;
      this.renderer.shadowMap.type = common_vendor.PCFSoftShadowMap;
      this.scene = new common_vendor.Scene();
      this.scene.background = new common_vendor.Color(2236962);
      this.camera = new common_vendor.PerspectiveCamera(45, width / height, 0.1, 1e3);
      this.camera.position.set(0, 1.6, 3);
      this.clock = new common_vendor.Clock();
      this.setupLights();
      this.addGrid();
      this.loadDigitalHuman();
      this.animate();
    },
    setupLights() {
      const ambientLight = new common_vendor.AmbientLight(4210752, 0.6);
      this.scene.add(ambientLight);
      const directionalLight = new common_vendor.DirectionalLight(16777215, 1);
      directionalLight.position.set(5, 10, 5);
      directionalLight.castShadow = true;
      directionalLight.shadow.mapSize.width = 2048;
      directionalLight.shadow.mapSize.height = 2048;
      this.scene.add(directionalLight);
      const fillLight = new common_vendor.DirectionalLight(16777215, 0.3);
      fillLight.position.set(-5, 5, -5);
      this.scene.add(fillLight);
    },
    addGrid() {
      const gridHelper = new common_vendor.GridHelper(10, 10, 8947848, 4473924);
      gridHelper.position.y = 0;
      this.scene.add(gridHelper);
    },
    loadDigitalHuman() {
      console.log("开始加载数字人模型");
      const loader = new common_vendor.GLTFLoader();
      loader.load(
        "./Teacher.glb",
        (gltf) => {
          console.log("数字人模型加载成功:", gltf);
          this.model = gltf.scene;
          this.model.scale.set(1, 1, 1);
          this.model.position.set(0, 0, 0);
          this.model.traverse((child) => {
            if (child.isMesh) {
              child.castShadow = true;
              child.receiveShadow = true;
            }
          });
          this.scene.add(this.model);
          if (gltf.animations && gltf.animations.length > 0) {
            this.mixer = new common_vendor.AnimationMixer(this.model);
            gltf.animations.forEach((clip) => {
              const action = this.mixer.clipAction(clip);
              action.play();
            });
          }
          console.log("数字人模型添加到场景成功");
        },
        (progress) => {
          console.log("加载进度:", progress.loaded / progress.total * 100 + "%");
        },
        (error) => {
          console.error("数字人模型加载失败:", error);
          this.createFallbackModel();
        }
      );
    },
    createFallbackModel() {
      console.log("创建替代数字人模型");
      const group = new common_vendor.Group();
      const headGeometry = new common_vendor.SphereGeometry(0.15, 16, 16);
      const headMaterial = new common_vendor.MeshLambertMaterial({ color: 16767916 });
      const head = new common_vendor.Mesh(headGeometry, headMaterial);
      head.position.y = 1.7;
      head.castShadow = true;
      group.add(head);
      const bodyGeometry = new common_vendor.CylinderGeometry(0.2, 0.3, 0.8, 8);
      const bodyMaterial = new common_vendor.MeshLambertMaterial({ color: 4286945 });
      const body = new common_vendor.Mesh(bodyGeometry, bodyMaterial);
      body.position.y = 1.2;
      body.castShadow = true;
      group.add(body);
      const legGeometry = new common_vendor.CylinderGeometry(0.08, 0.1, 0.6, 8);
      const legMaterial = new common_vendor.MeshLambertMaterial({ color: 3100495 });
      const leftLeg = new common_vendor.Mesh(legGeometry, legMaterial);
      leftLeg.position.set(-0.15, 0.5, 0);
      leftLeg.castShadow = true;
      group.add(leftLeg);
      const rightLeg = new common_vendor.Mesh(legGeometry, legMaterial);
      rightLeg.position.set(0.15, 0.5, 0);
      rightLeg.castShadow = true;
      group.add(rightLeg);
      this.model = group;
      this.scene.add(this.model);
      console.log("替代模型创建完成");
    },
    animate() {
      if (!this.renderer || !this.scene || !this.camera)
        return;
      const delta = this.clock.getDelta();
      if (this.mixer) {
        this.mixer.update(delta);
      }
      if (this.model) {
        this.model.rotation.y += 5e-3;
      }
      this.renderer.render(this.scene, this.camera);
      this.animationId = setTimeout(() => this.animate(), 16);
    },
    cleanup() {
      if (this.animationId) {
        clearTimeout(this.animationId);
        this.animationId = null;
      }
      if (this.mixer) {
        this.mixer.stopAllAction();
        this.mixer = null;
      }
      if (this.renderer) {
        this.renderer.dispose();
        this.renderer = null;
      }
    },
    fallbackTo2D(canvas) {
      console.log("Three.js初始化失败，显示提示信息");
      common_vendor.index.showToast({
        title: "数字人加载失败，请检查设备支持",
        icon: "none",
        duration: 3e3
      });
    },
    onTouchStart(e) {
      console.log("触摸开始", e);
    },
    onTouchMove(e) {
      console.log("触摸移动", e);
    },
    onTouchEnd(e) {
      console.log("触摸结束", e);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.onTouchStart && $options.onTouchStart(...args)),
    b: common_vendor.o((...args) => $options.onTouchMove && $options.onTouchMove(...args)),
    c: common_vendor.o((...args) => $options.onTouchEnd && $options.onTouchEnd(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
