"use strict";
const common_vendor = require("../../common/vendor.js");
const PlatformCanvas = () => "../../components/PlatformCanvas.js";
const _sfc_main = {
  components: {
    PlatformCanvas
  },
  data() {
    return {
      message: "数字人演示",
      camera: null,
      scene: null,
      renderer: null,
      controls: null,
      mixer: null
    };
  },
  onLoad() {
    console.log("页面onLoad执行");
  },
  onReady() {
    console.log("页面onReady执行");
    console.log("当前平台:", common_vendor.index.getSystemInfoSync().platform);
  },
  onUnload() {
    if (this.mixer) {
      this.mixer.stopAllAction();
    }
  },
  methods: {
    useCanvas({ canvas, useFrame, recomputeSize }) {
      console.log("Canvas初始化成功，开始设置Three.js场景");
      const CANVAS_WIDTH = canvas.width;
      const CANVAS_HEIGHT = canvas.height;
      this.init(canvas, CANVAS_WIDTH, CANVAS_HEIGHT);
      this.render();
    },
    init(canvas, width, height) {
      console.log("初始化Three.js渲染器和场景");
      this.renderer = new common_vendor.WebGLRenderer({ canvas, antialias: true });
      this.renderer.setPixelRatio(common_vendor.index.getSystemInfoSync().pixelRatio || 1);
      this.renderer.setSize(width, height);
      this.renderer.toneMapping = common_vendor.ACESFilmicToneMapping;
      this.renderer.toneMappingExposure = 1;
      this.camera = new common_vendor.PerspectiveCamera(45, width / height, 1, 2e3);
      this.camera.position.set(0, 100, 0);
      const environment = new common_vendor.RoomEnvironment();
      const pmremGenerator = new common_vendor.PMREMGenerator(this.renderer);
      this.scene = new common_vendor.Scene();
      this.scene.background = new common_vendor.Color(12303291);
      this.scene.environment = pmremGenerator.fromScene(environment).texture;
      environment.dispose();
      const grid = new common_vendor.GridHelper(500, 10, 16777215, 16777215);
      grid.material.opacity = 0.5;
      grid.material.depthWrite = false;
      grid.material.transparent = true;
      this.scene.add(grid);
      this.controls = new common_vendor.OrbitControls(this.camera, this.renderer.domElement);
      this.controls.addEventListener("change", () => this.render());
      this.controls.minDistance = 400;
      this.controls.maxDistance = 1e3;
      this.controls.target.set(10, 90, -16);
      this.controls.update();
      this.loadGLBModel("./Teacher.glb");
      console.log("Three.js场景初始化完成");
    },
    loadGLBModel(modelUrl) {
      console.log("开始加载数字人模型:", modelUrl);
      this.scene.children.forEach((child) => {
        if (child.isScene || child.type === "Group") {
          this.scene.remove(child);
        }
      });
      const ktx2Loader = new common_vendor.KTX2Loader().setWorkerLimit(1).setTranscoderPath("https://threejs.org/examples/jsm/libs/basis/").detectSupport(this.renderer);
      const loader = new common_vendor.GLTFLoader();
      loader.setKTX2Loader(ktx2Loader);
      loader.setMeshoptDecoder(common_vendor.MeshoptDecoder);
      loader.load(modelUrl, (gltf) => {
        console.log("数字人模型加载成功:", gltf);
        const model = gltf.scene;
        const animations = gltf.animations;
        console.log("animations", animations);
        console.log("gltf", gltf);
        model.scale.set(220, 220, 220);
        model.position.set(10, 50, 0);
        this.scene.add(model);
        this.render();
        if (animations && animations.length > 0) {
          this.mixer = new common_vendor.AnimationMixer(model);
          animations.forEach((clip) => {
            const action = this.mixer.clipAction(clip);
            action.play();
          });
        }
      }, void 0, (error) => {
        console.error("加载数字人模型失败:", error);
      });
    },
    render() {
      if (this.renderer && this.scene && this.camera) {
        this.renderer.render(this.scene, this.camera);
      }
    }
  }
};
if (!Array) {
  const _component_PlatformCanvas = common_vendor.resolveComponent("PlatformCanvas");
  _component_PlatformCanvas();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o($options.useCanvas),
    b: common_vendor.p({
      type: "webgl",
      ["canvas-id"]: "webglCanvas"
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
