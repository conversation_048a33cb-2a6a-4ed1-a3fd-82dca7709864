"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      message: "数字人演示",
      canvas: null,
      animationId: null,
      ctx: null
    };
  },
  onLoad() {
    console.log("页面onLoad执行");
  },
  onReady() {
    console.log("页面onReady执行");
    console.log("当前平台:", common_vendor.index.getSystemInfoSync().platform);
    setTimeout(() => {
      this.initThreeJS();
    }, 500);
  },
  onUnload() {
    this.cleanup();
  },
  methods: {
    initThreeJS() {
      console.log("开始初始化数字人场景");
      this.init2DCanvas();
    },
    init2DCanvas() {
      console.log("初始化2D Canvas数字人演示");
      const systemInfo = common_vendor.index.getSystemInfoSync();
      const width = systemInfo.windowWidth || 375;
      const height = systemInfo.windowHeight * 0.8 || 600;
      console.log("Canvas尺寸:", width, height);
      this.ctx = common_vendor.index.createCanvasContext("webglCanvas", this);
      if (!this.ctx) {
        console.error("无法创建Canvas上下文");
        return;
      }
      console.log("Canvas上下文创建成功");
      this.animate2D(this.ctx, width, height);
      console.log("2D Canvas数字人演示启动");
    },
    cleanup() {
      if (this.animationId) {
        clearTimeout(this.animationId);
        this.animationId = null;
      }
    },
    animate2D(ctx, width, height) {
      if (!ctx)
        return;
      ctx.setFillStyle("#222222");
      ctx.fillRect(0, 0, width, height);
      this.drawSimpleDigitalHuman(ctx, width, height);
      ctx.setFillStyle("#ffffff");
      ctx.setFontSize(16);
      ctx.setTextAlign("center");
      ctx.fillText("数字人演示 (2D模式)", width / 2, 30);
      ctx.fillText("小程序Canvas模式", width / 2, 50);
      ctx.draw();
      this.animationId = setTimeout(() => this.animate2D(ctx, width, height), 16);
    },
    drawSimpleDigitalHuman(ctx, width, height) {
      const centerX = width / 2;
      const centerY = height / 2;
      const time = Date.now() * 1e-3;
      ctx.save();
      ctx.translate(centerX, centerY);
      ctx.rotate(Math.sin(time) * 0.1);
      ctx.setFillStyle("#ffdbac");
      ctx.beginPath();
      ctx.arc(0, -80, 30, 0, Math.PI * 2);
      ctx.fill();
      ctx.setFillStyle("#000000");
      ctx.beginPath();
      ctx.arc(-10, -85, 3, 0, Math.PI * 2);
      ctx.fill();
      ctx.beginPath();
      ctx.arc(10, -85, 3, 0, Math.PI * 2);
      ctx.fill();
      ctx.setStrokeStyle("#000000");
      ctx.setLineWidth(2);
      ctx.beginPath();
      ctx.arc(0, -75, 8, 0, Math.PI);
      ctx.stroke();
      ctx.setFillStyle("#4169e1");
      ctx.fillRect(-20, -50, 40, 60);
      ctx.setFillStyle("#ffdbac");
      ctx.fillRect(-35, -40, 15, 40);
      ctx.fillRect(20, -40, 15, 40);
      ctx.setFillStyle("#2f4f4f");
      ctx.fillRect(-15, 10, 12, 50);
      ctx.fillRect(3, 10, 12, 50);
      ctx.restore();
    },
    onTouchStart(e) {
      console.log("触摸开始", e);
    },
    onTouchMove(e) {
      console.log("触摸移动", e);
    },
    onTouchEnd(e) {
      console.log("触摸结束", e);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.onTouchStart && $options.onTouchStart(...args)),
    b: common_vendor.o((...args) => $options.onTouchMove && $options.onTouchMove(...args)),
    c: common_vendor.o((...args) => $options.onTouchEnd && $options.onTouchEnd(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
