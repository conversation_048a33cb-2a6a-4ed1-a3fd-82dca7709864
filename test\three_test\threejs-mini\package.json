{"name": "three-miniprogram-template", "version": "0.0.0", "type": "module", "scripts": {"dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "cross-env DEBUG=none uni -p mp-weixin", "dev:mp-xhs": "uni -p mp-xhs", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-xhs": "uni build -p mp-xhs", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-alpha-4050320250224001", "@dcloudio/uni-app-harmony": "3.0.0-alpha-4050320250224001", "@dcloudio/uni-app-plus": "3.0.0-alpha-4050320250224001", "@dcloudio/uni-components": "3.0.0-alpha-4050320250224001", "@dcloudio/uni-h5": "3.0.0-alpha-4050320250224001", "@dcloudio/uni-mp-alipay": "3.0.0-alpha-4050320250224001", "@dcloudio/uni-mp-baidu": "3.0.0-alpha-4050320250224001", "@dcloudio/uni-mp-jd": "3.0.0-alpha-4050320250224001", "@dcloudio/uni-mp-kuaishou": "3.0.0-alpha-4050320250224001", "@dcloudio/uni-mp-lark": "3.0.0-alpha-4050320250224001", "@dcloudio/uni-mp-qq": "3.0.0-alpha-4050320250224001", "@dcloudio/uni-mp-toutiao": "3.0.0-alpha-4050320250224001", "@dcloudio/uni-mp-weixin": "3.0.0-alpha-4050320250224001", "@dcloudio/uni-mp-xhs": "3.0.0-alpha-4050320250224001", "@dcloudio/uni-quickapp-webview": "3.0.0-alpha-4050320250224001", "@minisheep/mini-program-polyfill-core": "^1.1.4", "@minisheep/three-platform-adapter": "^2.0.1", "three": "^0.172.0", "vue": "^3.5.13", "vue-i18n": "^9.14.3"}, "devDependencies": {"@dcloudio/types": "^3.4.14", "@dcloudio/uni-automator": "3.0.0-alpha-4050320250224001", "@dcloudio/uni-cli-shared": "3.0.0-alpha-4050320250224001", "@dcloudio/uni-stacktracey": "3.0.0-alpha-4050320250224001", "@dcloudio/vite-plugin-uni": "3.0.0-alpha-4050320250224001", "@minisheep/vite-plugin-mp-chunk-splitter": "^1.0.2", "@tsconfig/node20": "^20.1.4", "@types/three": "^0.171.0", "@vue/runtime-core": "^3.5.13", "@vue/tsconfig": "^0.7.0", "miniprogram-api-typings": "^4.0.5", "rollup-plugin-visualizer": "^5.14.0", "sass": "^1.85.1", "typescript": "^5.8.2", "vite": "^5.4.14", "vite-plugin-glsl": "^1.3.3", "vue-tsc": "^1.8.27"}}