"use strict";
require("../../uni-mp-weixin/dist/uni.api.esm.js");
const _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm = require("../../uni-mp-vue/dist/vue.runtime.esm.js");
const _mpChunkDeps__dcloudio_uniShared_dist_uniShared_es = require("../../uni-shared/dist/uni-shared.es.js");
const createHook = (lifecycle) => (hook, target = _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.getCurrentInstance()) => {
  !_mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.isInSSRComponentSetup && _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.injectHook(lifecycle, hook, target);
};
const onShow = /* @__PURE__ */ createHook(_mpChunkDeps__dcloudio_uniShared_dist_uniShared_es.ON_SHOW);
const onHide = /* @__PURE__ */ createHook(_mpChunkDeps__dcloudio_uniShared_dist_uniShared_es.ON_HIDE);
const onLaunch = /* @__PURE__ */ createHook(_mpChunkDeps__dcloudio_uniShared_dist_uniShared_es.ON_LAUNCH);
exports.onHide = onHide;
exports.onLaunch = onLaunch;
exports.onShow = onShow;
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/_mpChunkDeps/@dcloudio/uni-app/dist/uni-app.es.js.map
