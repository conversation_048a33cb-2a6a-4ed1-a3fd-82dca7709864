"use strict";
globalThis.THREEGlobals = (() => {
  if (globalThis.THREEGlobals) {
    if (!globalThis.THREEGlobals.__prefix_global__) throw new Error("[plugin:prefix-global] inject global property fail, key [THREEGlobals] is already defined.");
    return globalThis.THREEGlobals;
  }
  const e = JSON.parse(`["createImageBitmap"]`), t = new Proxy({ __prefix_global__: true }, { get: (r, n) => e.includes(n) ? void 0 : Object.hasOwn(r, n) ? Reflect.get(r, n) : "self" === n ? t : Reflect.get(globalThis, n), ownKeys: (e2) => Array.from(new Set([Reflect.ownKeys(e2), Reflect.ownKeys(globalThis)].flat())), has: (e2, t2) => Reflect.has(e2, t2) || Reflect.has(globalThis, t2) });
  return t;
})();
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
require("./common/chunks/@dcloudio/uni-console/dist/index.esm.js");
require("./common/chunks/@dcloudio/uni-mp-weixin/dist/uni.mp.esm.js");
require("./common/chunks/@minisheep/mini-program-polyfill-core/dist/wechat-polyfill.js");
const _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm = require("./common/chunks/@dcloudio/uni-mp-vue/dist/vue.runtime.esm.js");
const _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm = require("./common/chunks/@dcloudio/uni-mp-weixin/dist/uni.api.esm.js");
const _mpChunkDeps__dcloudio_uniApp_dist_uniApp_es = require("./common/chunks/@dcloudio/uni-app/dist/uni-app.es.js");
require("./common/chunks/@minisheep/three-platform-adapter/dist/wechat.js");
if (!Math) {
  "./pages/index/index.js";
  "./sub-pack-2/particles/index.js";
  "./sub-pack-2/gltf-loader/index.js";
}
const _sfc_main = /* @__PURE__ */ _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.defineComponent({
  __name: "App",
  setup(__props) {
    _mpChunkDeps__dcloudio_uniApp_dist_uniApp_es.onLaunch(() => {
      _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.index.__f__("log", "at App.vue:4", "App Launch");
    });
    _mpChunkDeps__dcloudio_uniApp_dist_uniApp_es.onShow(() => {
      _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.index.__f__("log", "at App.vue:7", "App Show");
    });
    _mpChunkDeps__dcloudio_uniApp_dist_uniApp_es.onHide(() => {
      _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.index.__f__("log", "at App.vue:10", "App Hide");
    });
    return () => {
    };
  }
});
function createApp() {
  const app = _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.createSSRApp(_sfc_main);
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
