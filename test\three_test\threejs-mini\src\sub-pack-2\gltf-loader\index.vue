<template>
   <view class="page-container">
       <PlatformCanvas
               type="webgl2"
               canvas-id="webgl_loader_gltf_compressed"
               @useCanvas="useCanvas"
       >
       </PlatformCanvas>
	   <view class="">
	   	132123
	   </view>
   </view>
</template>

<script setup>
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { KTX2Loader } from 'three/examples/jsm/loaders/KTX2Loader.js';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { MeshoptDecoder } from 'three/examples/jsm/libs/meshopt_decoder.module.js';
import { RoomEnvironment } from 'three/examples/jsm/environments/RoomEnvironment.js';
import PlatformCanvas from "@/components/PlatformCanvas.vue";

function useCanvas( { canvas, useFrame,recomputeSize }) {
  const CANVAS_WIDTH = canvas.width;
  const CANVAS_HEIGHT = canvas.height;
  let camera, scene, renderer;

  init();
  render();

function init() {
    renderer = new THREE.WebGLRenderer({ canvas, antialias: true });
    renderer.setPixelRatio(THREEGlobals.devicePixelRatio);
    renderer.setSize(CANVAS_WIDTH, CANVAS_HEIGHT);
    renderer.toneMapping = THREE.ACESFilmicToneMapping;
    renderer.toneMappingExposure = 1;

    camera = new THREE.PerspectiveCamera(45, CANVAS_WIDTH / CANVAS_HEIGHT, 1, 2000);
    camera.position.set(0, 100, 0);

    const environment = new RoomEnvironment();
    const pmremGenerator = new THREE.PMREMGenerator(renderer);

    scene = new THREE.Scene();
    scene.background = new THREE.Color(0xbbbbbb);
    scene.environment = pmremGenerator.fromScene(environment).texture;
    environment.dispose();

    const grid = new THREE.GridHelper(500, 10, 0xffffff, 0xffffff);
    grid.material.opacity = 0.5;
    grid.material.depthWrite = false;
    grid.material.transparent = true;
    scene.add(grid);

    // 初始化控制器
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.addEventListener('change', render);
    controls.minDistance = 400;
    controls.maxDistance = 1000;
    controls.target.set(10, 90, -16);
    controls.update();

    // 加载默认模型（替换原来的 loader.load）
    loadGLBModel('http://minhandash.edu.izdax.cn/Teacher_001.glb');

    // 窗口大小调整监听
    canvas.addEventListener('resize', onWindowResize);
}

function loadGLBModel(modelUrl) {
    // 清除现有模型（如果有）
    scene.children.forEach(child => {
        if (child.isScene || child.type === 'Group') {
            scene.remove(child);
        }
    });

    const ktx2Loader = new KTX2Loader()
        .setWorkerLimit(1)
        .setTranscoderPath('https://threejs.org/examples/jsm/libs/basis/')
        .detectSupport(renderer);

    const loader = new GLTFLoader();
    loader.setKTX2Loader(ktx2Loader);
    loader.setMeshoptDecoder(MeshoptDecoder);
    
    // 判断是否是本地文件
    const url = (modelUrl instanceof Blob) ? URL.createObjectURL(modelUrl) : modelUrl;
    
    loader.load(url, function (gltf) {
        const model = gltf.scene;
          const animations = gltf.animations; // 获取动画列表
            
            console.log('animations',animations); // 查看是否有动画
            console.log('gltf',gltf); // 查看是否有动画
        // ✅ 调整模型大小（如果太小，放大 10 倍）
        model.scale.set(220, 220, 220); // 根据需要调整数值
        
        // ✅ 调整模型位置（避免掉到地面下）
        model.position.set(10, 50, 0); // 可以改成 model.position.y = 50; 等
        
        scene.add(model);
        render();
        
        // 如果是 Blob URL，记得释放内存
        if (modelUrl instanceof Blob) {
            URL.revokeObjectURL(url);
        }
    }, undefined, (error) => {
        console.error("加载模型失败:", error);
    });
}

  //手动触发resize
  function manualTriggerResize() {
    recomputeSize().then(()=>{
      canvas.dispatchEvent(new Event('resize'));
    })
  }

  function onWindowResize() {
    camera.aspect = canvas.clientWidth / canvas.clientHeight;
    camera.updateProjectionMatrix();

    renderer.setSize(canvas.clientWidth, canvas.clientHeight);

    render();
  }

  //

  function render() {
    renderer.render(scene, camera);
  }
}

</script>



<style scoped>

</style>
