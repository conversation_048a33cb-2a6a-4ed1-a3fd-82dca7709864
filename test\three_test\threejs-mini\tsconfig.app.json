{
  "extends": "@vue/tsconfig/tsconfig.json",
  "include": ["src/env.d.ts", "src/**/*", "src/**/*.vue"],
  "exclude": ["src/**/__tests__/*"],
  "compilerOptions": {
    "lib": [
      // Target ES2020 to align with Vite.
      // <https://vitejs.dev/config/build-options.html#build-target>
      // Support for newer versions of language built-ins are
      // left for the users to include, because that would require:
      //   - either the project doesn't need to support older versions of browsers;
      //   - or the project has properly included the necessary polyfills.
      "ES2020",

      "DOM",
      "DOM.Iterable"
    ],
    "types": ["@dcloudio/types","miniprogram-api-typings"],
    "composite": true,
    "allowJs": true,
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
