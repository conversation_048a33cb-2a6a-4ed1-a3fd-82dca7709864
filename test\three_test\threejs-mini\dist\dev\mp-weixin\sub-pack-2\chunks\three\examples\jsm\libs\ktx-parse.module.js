"use strict";
const t = 0, n = 2, g = 1, u = 2, T = 0, C = 1, R = 10, it = 0, ct = 9, yt = 15, xt = 16, wt = 22, Ft = 37, Ct = 43, te = 76, ae = 83, ge = 97, ue = 100, we = 103, Ae = 109, In = 165, Sn = 166;
class Ii {
  constructor() {
    this.vkFormat = 0, this.typeSize = 1, this.pixelWidth = 0, this.pixelHeight = 0, this.pixelDepth = 0, this.layerCount = 0, this.faceCount = 1, this.supercompressionScheme = 0, this.levels = [], this.dataFormatDescriptor = [{ vendorId: 0, descriptorType: 0, descriptorBlockSize: 0, versionNumber: 2, colorModel: 0, colorPrimaries: 1, transferFunction: 2, flags: 0, texelBlockDimension: [0, 0, 0, 0], bytesPlane: [0, 0, 0, 0, 0, 0, 0, 0], samples: [] }], this.keyValue = {}, this.globalData = null;
  }
}
class Si {
  constructor(t2, e, n2, i) {
    this._dataView = void 0, this._littleEndian = void 0, this._offset = void 0, this._dataView = new DataView(t2.buffer, t2.byteOffset + e, n2), this._littleEndian = i, this._offset = 0;
  }
  _nextUint8() {
    const t2 = this._dataView.getUint8(this._offset);
    return this._offset += 1, t2;
  }
  _nextUint16() {
    const t2 = this._dataView.getUint16(this._offset, this._littleEndian);
    return this._offset += 2, t2;
  }
  _nextUint32() {
    const t2 = this._dataView.getUint32(this._offset, this._littleEndian);
    return this._offset += 4, t2;
  }
  _nextUint64() {
    const t2 = this._dataView.getUint32(this._offset, this._littleEndian) + 2 ** 32 * this._dataView.getUint32(this._offset + 4, this._littleEndian);
    return this._offset += 8, t2;
  }
  _nextInt32() {
    const t2 = this._dataView.getInt32(this._offset, this._littleEndian);
    return this._offset += 4, t2;
  }
  _nextUint8Array(t2) {
    const e = new Uint8Array(this._dataView.buffer, this._dataView.byteOffset + this._offset, t2);
    return this._offset += t2, e;
  }
  _skip(t2) {
    return this._offset += t2, this;
  }
  _scan(t2, e) {
    void 0 === e && (e = 0);
    const n2 = this._offset;
    let i = 0;
    for (; this._dataView.getUint8(this._offset) !== e && i < t2; ) i++, this._offset++;
    return i < t2 && this._offset++, new Uint8Array(this._dataView.buffer, this._dataView.byteOffset + n2, i);
  }
}
const Oi = [171, 75, 84, 88, 32, 50, 48, 187, 13, 10, 26, 10];
function Ti(t2) {
  return new THREEGlobals["TextDecoder"]().decode(t2);
}
function Pi(t2) {
  const e = new Uint8Array(t2.buffer, t2.byteOffset, Oi.length);
  if (e[0] !== Oi[0] || e[1] !== Oi[1] || e[2] !== Oi[2] || e[3] !== Oi[3] || e[4] !== Oi[4] || e[5] !== Oi[5] || e[6] !== Oi[6] || e[7] !== Oi[7] || e[8] !== Oi[8] || e[9] !== Oi[9] || e[10] !== Oi[10] || e[11] !== Oi[11]) throw new Error("Missing KTX 2.0 identifier.");
  const n2 = new Ii(), i = 17 * Uint32Array.BYTES_PER_ELEMENT, s = new Si(t2, Oi.length, i, true);
  n2.vkFormat = s._nextUint32(), n2.typeSize = s._nextUint32(), n2.pixelWidth = s._nextUint32(), n2.pixelHeight = s._nextUint32(), n2.pixelDepth = s._nextUint32(), n2.layerCount = s._nextUint32(), n2.faceCount = s._nextUint32();
  const a = s._nextUint32();
  n2.supercompressionScheme = s._nextUint32();
  const r = s._nextUint32(), o = s._nextUint32(), l = s._nextUint32(), f = s._nextUint32(), h = s._nextUint64(), U = s._nextUint64(), c = new Si(t2, Oi.length + i, 3 * a * 8, true);
  for (let e2 = 0; e2 < a; e2++) n2.levels.push({ levelData: new Uint8Array(t2.buffer, t2.byteOffset + c._nextUint64(), c._nextUint64()), uncompressedByteLength: c._nextUint64() });
  const _ = new Si(t2, r, o, true), p = { vendorId: _._skip(4)._nextUint16(), descriptorType: _._nextUint16(), versionNumber: _._nextUint16(), descriptorBlockSize: _._nextUint16(), colorModel: _._nextUint8(), colorPrimaries: _._nextUint8(), transferFunction: _._nextUint8(), flags: _._nextUint8(), texelBlockDimension: [_._nextUint8(), _._nextUint8(), _._nextUint8(), _._nextUint8()], bytesPlane: [_._nextUint8(), _._nextUint8(), _._nextUint8(), _._nextUint8(), _._nextUint8(), _._nextUint8(), _._nextUint8(), _._nextUint8()], samples: [] }, g2 = (p.descriptorBlockSize / 4 - 6) / 4;
  for (let t3 = 0; t3 < g2; t3++) {
    const e2 = { bitOffset: _._nextUint16(), bitLength: _._nextUint8(), channelType: _._nextUint8(), samplePosition: [_._nextUint8(), _._nextUint8(), _._nextUint8(), _._nextUint8()], sampleLower: -Infinity, sampleUpper: Infinity };
    64 & e2.channelType ? (e2.sampleLower = _._nextInt32(), e2.sampleUpper = _._nextInt32()) : (e2.sampleLower = _._nextUint32(), e2.sampleUpper = _._nextUint32()), p.samples[t3] = e2;
  }
  n2.dataFormatDescriptor.length = 0, n2.dataFormatDescriptor.push(p);
  const y = new Si(t2, l, f, true);
  for (; y._offset < f; ) {
    const t3 = y._nextUint32(), e2 = y._scan(t3), i2 = Ti(e2);
    if (n2.keyValue[i2] = y._nextUint8Array(t3 - e2.byteLength - 1), i2.match(/^ktx/i)) {
      const t4 = Ti(n2.keyValue[i2]);
      n2.keyValue[i2] = t4.substring(0, t4.lastIndexOf("\0"));
    }
    y._skip(t3 % 4 ? 4 - t3 % 4 : 0);
  }
  if (U <= 0) return n2;
  const x = new Si(t2, h, U, true), u2 = x._nextUint16(), b = x._nextUint16(), d = x._nextUint32(), w = x._nextUint32(), m = x._nextUint32(), D = x._nextUint32(), B = [];
  for (let t3 = 0; t3 < a; t3++) B.push({ imageFlags: x._nextUint32(), rgbSliceByteOffset: x._nextUint32(), rgbSliceByteLength: x._nextUint32(), alphaSliceByteOffset: x._nextUint32(), alphaSliceByteLength: x._nextUint32() });
  const L = h + x._offset, v = L + d, A = v + w, k = A + m, V = new Uint8Array(t2.buffer, t2.byteOffset + L, d), I = new Uint8Array(t2.buffer, t2.byteOffset + v, w), S = new Uint8Array(t2.buffer, t2.byteOffset + A, m), F = new Uint8Array(t2.buffer, t2.byteOffset + k, D);
  return n2.globalData = { endpointCount: u2, selectorCount: b, imageDescs: B, endpointsData: V, selectorsData: I, tablesData: S, extendedData: F }, n2;
}
exports.Ae = Ae;
exports.C = C;
exports.Ct = Ct;
exports.Ft = Ft;
exports.In = In;
exports.Pi = Pi;
exports.R = R;
exports.Sn = Sn;
exports.T = T;
exports.ae = ae;
exports.ct = ct;
exports.g = g;
exports.ge = ge;
exports.it = it;
exports.n = n;
exports.t = t;
exports.te = te;
exports.u = u;
exports.ue = ue;
exports.we = we;
exports.wt = wt;
exports.xt = xt;
exports.yt = yt;
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/_mpChunkDeps/three/examples/jsm/libs/ktx-parse.module.js.map
