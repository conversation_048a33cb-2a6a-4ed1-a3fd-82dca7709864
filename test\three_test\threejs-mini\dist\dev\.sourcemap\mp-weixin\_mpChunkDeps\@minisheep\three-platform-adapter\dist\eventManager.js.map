{"version": 3, "file": "eventManager.js", "sources": ["../node_modules/@minisheep/three-platform-adapter/dist/eventManager.mjs"], "sourcesContent": ["import{p as e,n as t,s as n,u as r,d as a,e as o,f as s,h as i,i as c}from\"./Adapter.mjs\";import{blobUrlPrefix as l,getDataURlByBlobUrl as u}from\"@minisheep/mini-program-polyfill-core\";function h(n,r,a,o){const s=Object.assign({top:0,left:0,width:0,height:0,right:0,bottom:0},r||{});let i=s.width+\"px\",c=s.height+\"px\",l=\"block\";const u=Object.defineProperties({},{width:{get:()=>i,set:e=>{(e=\"\"+e).endsWith(\"px\")&&(i=e)}},height:{get:()=>c,set:e=>{(e=\"\"+e).endsWith(\"px\")&&(c=e)}},display:{get:()=>l,set:e=>{l=e}}}),h=Object.getPrototypeOf(n),d=Object.defineProperties(e(Object.create(h)),Object.assign({style:{value:u,writable:!1},clientTop:{get:()=>s.top},clientLeft:{get:()=>s.left},clientWidth:{get:()=>s.width},clientHeight:{get:()=>s.height},offsetTop:{get:()=>s.top},offsetLeft:{get:()=>s.left},offsetWidth:{get:()=>s.width},offsetHeight:{get:()=>s.height},getBoundingClientRect:{value:()=>s},setAttribute:{value:t},removeAttribute:{value:t},ownerDocument:{value:a},getRootNode:{value:a?()=>a:void 0},setPointerCapture:{value:t},releasePointerCapture:{value:t},requestPointerLock:{value:t},exitPointerLock:{value:t},focus:{value:t}},(null==o?void 0:o(u,h))||{}));return Object.setPrototypeOf(n,d),f.add(n),{element:n,updateRectInfo(e){Object.assign(s,e),i=e.width+\"px\",c=e.height+\"px\"}}}const f=new WeakSet;class d{static[Symbol.hasInstance](e){return n(this,e,f)}constructor(){throw new TypeError(\"Illegal constructor\")}}function g(e){switch(typeof e){case\"object\":if(\"Object\"===e.constructor.name){if(2===Object.keys(e).length&&\"__typedArray\"in e&&\"buffer\"in e)return new globalThis[e.__typedArray](e.buffer);Object.entries(e).forEach((([t,n])=>{e[t]=g(n)}))}else e instanceof Array&&e.forEach(((t,n)=>{e[n]=g(t)}));return e;case\"string\":if(e.startsWith(\"__bigint\")){const t=e.slice(9);return BigInt(t)}if(e.startsWith(\"__symbol\")){const t=e.slice(9);return Symbol.for(t)||Symbol(t)}return e.startsWith(\"__arraybuffer\")?new ArrayBuffer(0):e;default:return e}}function p(e){switch(typeof e){case\"bigint\":return`__bigint:${e.toString()}`;case\"object\":if(null===e)return null;if(e instanceof ArrayBuffer)return 0===e.byteLength?\"__arraybuffer:0\":e;if(ArrayBuffer.isView(e)){return(0!==e.byteOffset||e.byteLength!==e.buffer.byteLength)&&(e=e instanceof DataView?new DataView(e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength),0,e.byteLength):e.slice()),{buffer:e.buffer,__typedArray:e.constructor.name}}return Object.entries(e).forEach((([t,n])=>{e[t]=p(n)})),e;case\"symbol\":return`__symbol:${Symbol.keyFor(e)||e.toString()}`;case\"function\":return;case\"number\":case\"string\":case\"undefined\":case\"boolean\":return e}}const m=\"__message_data_no_transfer\";class b{static toRaw(e){try{return\"object\"==typeof e&&m in e?(delete e[m],e):g(e)}finally{}}static format(e,t=!1){try{return\"object\"==typeof e?t?p(e):(e[m]=!0,e):p(e)}finally{}}}var y,v;const w=r(\"EventTarget\"),_=r(\"Event\"),O=r(\"MessageEvent\");class x extends w{static[Symbol.hasInstance](e){return n(this,e,a(this,y,\"f\",v))}[Symbol.toStringTag](){return\"Worker\"}constructor(e,t){super(),Object.defineProperty(this,\"_worker\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"_onmessage\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"_onerror\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.addEventListener(\"message\",(e=>{var t;null===(t=this._onmessage)||void 0===t||t.call(this,e)})),this.addEventListener(\"error\",(e=>{var t;null===(t=this._onerror)||void 0===t||t.call(this,e)})),y.preWorkerInstance&&y.preWorkerInstance.terminate();const n=wx.createWorker(e,t);n?(this._worker=n,n.onMessage((e=>{const t=new O(\"message\",{data:b.toRaw(e)});this.dispatchEvent(t)})),n.onProcessKilled((e=>{this.dispatchEvent(new _(\"processkilled\"))})),y.preWorkerInstance=n):this.dispatchEvent(new _(\"error\"))}set onmessage(e){\"function\"==typeof e&&(this._onmessage=e)}set onerror(e){\"function\"==typeof e&&(this._onerror=e)}get onmessage(){return this._onmessage}get onerror(){return this._onerror}postMessage(e,t){var n;null===(n=this._worker)||void 0===n||n.postMessage(b.format(e,!!t))}terminate(){var e;null===(e=this._worker)||void 0===e||e.terminate(),y.preWorkerInstance&&(y.preWorkerInstance=void 0)}}var I,j;y=x,Object.defineProperty(x,\"MessageData\",{enumerable:!0,configurable:!0,writable:!0,value:b}),v={value:new WeakSet};let k=!1;class W{static[Symbol.hasInstance](e){return a(this,I,\"f\",j).has(e)}constructor(){k||(!function(){const e=wx.createWebAudioContext(),t=e.createGain().gain,n=Object.getPrototypeOf(t);[\"setValueAtTime\",\"setTargetAtTime\",\"linearRampToValueAtTime\"].forEach((e=>{const t=n[e];n[e]=function(...e){try{t.apply(this,e)}catch(t){this.value=e[0]}}})),e.close()}(),k=!0);const e=wx.createWebAudioContext();a(I,I,\"f\",j).add(e);const t=e.decodeAudioData;return e.decodeAudioData=function(n,r,a){return new Promise(((o,s)=>{t.call(e,n,(e=>{r&&r(e),o(e)}),(e=>{a&&a(e),s(e)}))}))},e}}I=W,j={value:new WeakSet};const A=\"triggerGC\"in wx;var E,P,S,C,T,D;const L=(()=>{const e=A?null:wx.createOffscreenCanvas({width:1,height:1,type:\"2d\"});return{createImage:()=>A?wx.createImage():e.createImage(),createImageData:(...t)=>A?wx.createImageData(...t):e.createImageData(...t),createBaseCanvas(e,t,n){if(A){const n=wx.createCanvas();return n.width=null!=e?e:1,n.height=null!=t?t:1,n}{const r=wx.createOffscreenCanvas({width:null!=e?e:1,height:null!=t?t:1,type:null!=n?n:o.defaultCanvasContextType});return r.width=null!=e?e:1,r.height=null!=t?t:1,r}},requestAnimationFrame:A?requestAnimationFrame:e.requestAnimationFrame,cancelAnimationFrame:A?cancelAnimationFrame:e.cancelAnimationFrame}})(),B=new WeakSet;function F(e){B.add(e)}const R=\"__patched_canvas__\";function M(t,n,r){const a=Object.hasOwn(t,\"getContext\")?t.getContext:null;a&&delete t.getContext;const{element:o,updateRectInfo:c}=h(t,n,r,((n,r)=>({getContext:{value:(...o)=>{const s=(a||r.getContext).call(t,...o);\"webgl2\"===o[0]&&i(s);const c=s.canvas;if(!(R in c)){try{F(c),e(c),Object.defineProperty(c,\"style\",{value:n,writable:!1})}catch(e){}c[R]=!0}return s}},toBlob:{value:(e,n,r)=>{s(t,n,r).then((t=>{e(t)}))}}}))),l=o.getBoundingClientRect();return t.width!==l.width&&(t.width=l.width),t.height!==l.height&&(t.height=l.height),F(o),{canvas:o,updateRectInfo:c}}class q{static[Symbol.hasInstance](e){return n(this,e,B)}constructor(){throw new TypeError(\"Illegal constructor\")}}class V{static[Symbol.hasInstance](e){return n(this,e,a(this,E,\"f\",P))}constructor(){const t=r(\"Event\"),n=e(L.createImage());n.onload=e=>{n.dispatchEvent(new t(\"load\"))},n.onerror=e=>{n.dispatchEvent(new t(\"\"))};const o=Object.getOwnPropertyDescriptor(Object.getPrototypeOf(n),\"src\");if(!o)throw Error(\"something wrong in get image src descriptor\");const{get:s,set:i}=o;return Object.defineProperties(n,{naturalWidth:{get:()=>n.width},naturalHeight:{get:()=>n.height},src:{enumerable:!0,configurable:!1,get:s,set(e){(e=String(e)).startsWith(l)?i.call(n,u(e)):i.call(n,e)}}}),a(E,E,\"f\",P).add(n),n}}E=V,P={value:new WeakSet};class X{static[Symbol.hasInstance](e){return n(this,e,a(this,S,\"f\",C))}constructor(e,t){const n=new V;return void 0!==e&&(n.width=e),void 0!==t&&(n.height=t),a(S,S,\"f\",C).add(n),n}}S=X,C={value:new WeakSet};class Y{static[Symbol.hasInstance](e){return n(this,e,a(this,T,\"f\",D))}constructor(...e){const t=L.createImageData(...e);return a(T,T,\"f\",D).add(t),t}}T=Y,D={value:new WeakSet};class ${static[Symbol.hasInstance](e){return n(this,e,B)}constructor(e,t){return function(e,t,n){const{canvas:r}=M(L.createBaseCanvas(e,t,n));return r}(e,t,o.defaultCanvasContextType)}}const H=L.requestAnimationFrame,G=L.cancelAnimationFrame;function K(e,t){return n=>{let r,a;const o=t=>{var s,i;if(void 0===r)r=t||(null===(s=globalThis.performance)||void 0===s?void 0:s.now())||Date.now();else{const e=t||(null===(i=globalThis.performance)||void 0===i?void 0:i.now())||Date.now(),a=e-r;r=e;try{n(a)}catch(e){console.error(\"error in frame: \",e)}}a=e(o)};return a=e(o),{cancel:()=>t(a)}}}const N=new WeakMap;function z(e,t,n,a=!0){const o=r(\"PointerEvent\"),s={touchstart:\"pointerdown\",touchmove:\"pointermove\",touchend:\"pointerup\",touchcancel:\"pointercancel\",tap:\"click\"}[e.type]||\"\",i=Array.from(e.changedTouches);if(i.length){let r,l;N.has(n)?l=N.get(n):(l={},N.set(n,l));const u=l[i[0].identifier];(!a||u&&u.expired<Date.now()||\"touchstart\"===e.type||\"tap\"===e.type&&!u)&&(r=Promise.resolve(n()),l[i[0].identifier]={p:r,expired:Date.now()+1e3},c(\"init point rectInfo \",e,N.get(n))),!r&&i[0].identifier in l&&(c(\"try use stored rectInfo\",e,l),r=l[i[0].identifier].p),r||(r=Promise.resolve(n()),l[i[0].identifier]={p:r,expired:Date.now()+1e3}),r.then((n=>{i.forEach(((r,a)=>{const i=new o(s,{pointerId:r.identifier,pointerType:\"touch\",offsetX:r.x,offsetY:r.y,clientX:\"clientX\"in r?r.clientX:(r.x||0)+n.left,clientY:\"clientY\"in r?r.clientY:(r.y||0)+n.top,button:\"pointermove\"===s?-1:0,isPrimary:0===a});[t].flat().filter((e=>!!e)).forEach((t=>{try{c(`dispatchEvent ${e.type} -> ${i.type}`,e,i,t),\"function\"==typeof t.dispatchEvent&&t.dispatchEvent(i),\"pointercancel\"===i.type&&setTimeout((()=>{\"function\"==typeof t.dispatchEvent&&t.dispatchEvent(i)}),100)}catch(e){console.error(e)}}))}))}))}}export{d as M,x as W,h as a,q as b,$ as c,V as d,X as e,Y as f,W as g,H as h,G as i,K as j,z as k,M as p};\n"], "names": ["h", "d", "e", "t", "n", "r", "a", "wx", "o", "i", "c", "s", "l", "u"], "mappings": ";;;;AAAyL,SAAS,EAAE,GAAE,GAAE,GAAE,GAAE;AAAC,QAAM,IAAE,OAAO,OAAO,EAAC,KAAI,GAAE,MAAK,GAAE,OAAM,GAAE,QAAO,GAAE,OAAM,GAAE,QAAO,EAAC,GAAE,KAAG,CAAA,CAAE;AAAE,MAAI,IAAE,EAAE,QAAM,MAAK,IAAE,EAAE,SAAO,MAAK,IAAE;AAAQ,QAAM,IAAE,OAAO,iBAAiB,CAAA,GAAG,EAAC,OAAM,EAAC,KAAI,MAAI,GAAE,KAAI,OAAG;AAAC,KAAC,IAAE,KAAG,GAAG,SAAS,IAAI,MAAI,IAAE;AAAA,EAAE,EAAC,GAAE,QAAO,EAAC,KAAI,MAAI,GAAE,KAAI,OAAG;AAAC,KAAC,IAAE,KAAG,GAAG,SAAS,IAAI,MAAI,IAAE;AAAA,EAAE,EAAC,GAAE,SAAQ,EAAC,KAAI,MAAI,GAAE,KAAI,OAAG;AAAC,QAAE;AAAA,EAAC,EAAC,EAAC,CAAC,GAAEA,KAAE,OAAO,eAAe,CAAC,GAAEC,KAAE,OAAO,iBAAiBC,4DAAE,OAAO,OAAOF,EAAC,CAAC,GAAE,OAAO,OAAO,EAAC,OAAM,EAAC,OAAM,GAAE,UAAS,MAAE,GAAE,WAAU,EAAC,KAAI,MAAI,EAAE,IAAG,GAAE,YAAW,EAAC,KAAI,MAAI,EAAE,KAAI,GAAE,aAAY,EAAC,KAAI,MAAI,EAAE,MAAK,GAAE,cAAa,EAAC,KAAI,MAAI,EAAE,OAAM,GAAE,WAAU,EAAC,KAAI,MAAI,EAAE,IAAG,GAAE,YAAW,EAAC,KAAI,MAAI,EAAE,KAAI,GAAE,aAAY,EAAC,KAAI,MAAI,EAAE,MAAK,GAAE,cAAa,EAAC,KAAI,MAAI,EAAE,OAAM,GAAE,uBAAsB,EAAC,OAAM,MAAI,EAAC,GAAE,cAAa,EAAC,OAAMG,0DAAAA,EAAC,GAAE,iBAAgB,EAAC,OAAMA,0DAAAA,EAAC,GAAE,eAAc,EAAC,OAAM,EAAC,GAAE,aAAY,EAAC,OAAM,IAAE,MAAI,IAAE,OAAM,GAAE,mBAAkB,EAAC,OAAMA,0DAAAA,EAAC,GAAE,uBAAsB,EAAC,OAAMA,0DAAAA,EAAC,GAAE,oBAAmB,EAAC,OAAMA,4DAAC,GAAE,iBAAgB,EAAC,OAAMA,0DAAAA,EAAC,GAAE,OAAM,EAAC,OAAMA,0DAAAA,EAAC,EAAC,IAAG,QAAM,IAAE,SAAO,EAAE,GAAEH,EAAC,MAAI,EAAE,CAAC;AAAE,SAAO,OAAO,eAAe,GAAEC,EAAC,GAAE,EAAE,IAAI,CAAC,GAAE,EAAC,SAAQ,GAAE,eAAe,GAAE;AAAC,WAAO,OAAO,GAAE,CAAC,GAAE,IAAE,EAAE,QAAM,MAAK,IAAE,EAAE,SAAO;AAAA,EAAI,EAAC;AAAC;AAAC,MAAM,IAAE,oBAAI;AAAQ,MAAM,EAAC;AAAA,EAAC,QAAO,OAAO,WAAW,EAAE,GAAE;AAAC,WAAOG,0DAAAA,EAAE,MAAK,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,IAAI,UAAU,qBAAqB;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,UAAO,OAAO,GAAC;AAAA,IAAE,KAAI;AAAS,UAAG,aAAW,EAAE,YAAY,MAAK;AAAC,YAAG,MAAI,OAAO,KAAK,CAAC,EAAE,UAAQ,kBAAiB,KAAG,YAAW,EAAE,QAAO,IAAI,WAAW,EAAE,YAAY,EAAE,EAAE,MAAM;AAAE,eAAO,QAAQ,CAAC,EAAE,QAAS,CAAC,CAAC,GAAE,CAAC,MAAI;AAAC,YAAE,CAAC,IAAE,EAAE,CAAC;AAAA,QAAC,CAAC;AAAA,MAAE,MAAM,cAAa,SAAO,EAAE,QAAS,CAAC,GAAE,MAAI;AAAC,UAAE,CAAC,IAAE,EAAE,CAAC;AAAA,MAAC,CAAC;AAAG,aAAO;AAAA,IAAE,KAAI;AAAS,UAAG,EAAE,WAAW,UAAU,GAAE;AAAC,cAAM,IAAE,EAAE,MAAM,CAAC;AAAE,eAAO,OAAO,CAAC;AAAA,MAAC;AAAC,UAAG,EAAE,WAAW,UAAU,GAAE;AAAC,cAAM,IAAE,EAAE,MAAM,CAAC;AAAE,eAAO,OAAO,IAAI,CAAC,KAAG,OAAO,CAAC;AAAA,MAAC;AAAC,aAAO,EAAE,WAAW,eAAe,IAAE,IAAI,YAAY,CAAC,IAAE;AAAA,IAAE;AAAQ,aAAO;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,UAAO,OAAO,GAAC;AAAA,IAAE,KAAI;AAAS,aAAM,YAAY,EAAE,SAAQ,CAAE;AAAA,IAAG,KAAI;AAAS,UAAG,SAAO,EAAE,QAAO;AAAK,UAAG,aAAa,YAAY,QAAO,MAAI,EAAE,aAAW,oBAAkB;AAAE,UAAG,YAAY,OAAO,CAAC,GAAE;AAAC,gBAAO,MAAI,EAAE,cAAY,EAAE,eAAa,EAAE,OAAO,gBAAc,IAAE,aAAa,WAAS,IAAI,SAAS,EAAE,OAAO,MAAM,EAAE,YAAW,EAAE,aAAW,EAAE,UAAU,GAAE,GAAE,EAAE,UAAU,IAAE,EAAE,MAAK,IAAI,EAAC,QAAO,EAAE,QAAO,cAAa,EAAE,YAAY,KAAI;AAAA,MAAC;AAAC,aAAO,OAAO,QAAQ,CAAC,EAAE,QAAS,CAAC,CAAC,GAAE,CAAC,MAAI;AAAC,UAAE,CAAC,IAAE,EAAE,CAAC;AAAA,MAAC,CAAC,GAAG;AAAA,IAAE,KAAI;AAAS,aAAM,YAAY,OAAO,OAAO,CAAC,KAAG,EAAE,SAAQ,CAAE;AAAA,IAAG,KAAI;AAAW;AAAA,IAAO,KAAI;AAAA,IAAS,KAAI;AAAA,IAAS,KAAI;AAAA,IAAY,KAAI;AAAU,aAAO;AAAA,EAAC;AAAC;AAAC,MAAM,IAAE;AAA6B,MAAM,EAAC;AAAA,EAAC,OAAO,MAAM,GAAE;AAAC,QAAG;AAAC,aAAM,YAAU,OAAO,KAAG,KAAK,KAAG,OAAO,EAAE,CAAC,GAAE,KAAG,EAAE,CAAC;AAAA,IAAC,UAAC;AAAA,IAAQ;AAAA,EAAC;AAAA,EAAC,OAAO,OAAO,GAAE,IAAE,OAAG;AAAC,QAAG;AAAC,aAAM,YAAU,OAAO,IAAE,IAAE,EAAE,CAAC,KAAG,EAAE,CAAC,IAAE,MAAG,KAAG,EAAE,CAAC;AAAA,IAAC,UAAC;AAAA,IAAQ;AAAA,EAAC;AAAC;AAAC,IAAI,GAAE;AAAE,MAAM,IAAEC,0DAAAA,EAAE,aAAa,GAAE,IAAEA,0DAAAA,EAAE,OAAO,GAAE,IAAEA,0DAAAA,EAAE,cAAc;AAAE,MAAM,UAAU,EAAC;AAAA,EAAC,QAAO,OAAO,WAAW,EAAE,GAAE;AAAC,WAAOD,0DAAAA,EAAE,MAAK,GAAEE,0DAAAA,EAAE,MAAK,GAAE,KAAI,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,CAAC,OAAO,WAAW,IAAG;AAAC,WAAM;AAAA,EAAQ;AAAA,EAAC,YAAY,GAAE,GAAE;AAAC,UAAK,GAAG,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,cAAa,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,YAAW,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,KAAK,iBAAiB,WAAW,CAAAJ,OAAG;AAAC,UAAIC;AAAE,gBAAQA,KAAE,KAAK,eAAa,WAASA,MAAGA,GAAE,KAAK,MAAKD,EAAC;AAAA,IAAC,IAAI,KAAK,iBAAiB,SAAS,CAAAA,OAAG;AAAC,UAAIC;AAAE,gBAAQA,KAAE,KAAK,aAAW,WAASA,MAAGA,GAAE,KAAK,MAAKD,EAAC;AAAA,IAAC,CAAC,GAAG,EAAE,qBAAmB,EAAE,kBAAkB;AAAY,UAAM,IAAEK,oDAAAA,KAAG,aAAa,GAAE,CAAC;AAAE,SAAG,KAAK,UAAQ,GAAE,EAAE,UAAW,CAAAL,OAAG;AAAC,YAAMC,KAAE,IAAI,EAAE,WAAU,EAAC,MAAK,EAAE,MAAMD,EAAC,EAAC,CAAC;AAAE,WAAK,cAAcC,EAAC;AAAA,IAAC,CAAC,GAAG,EAAE,gBAAiB,CAAAD,OAAG;AAAC,WAAK,cAAc,IAAI,EAAE,eAAe,CAAC;AAAA,IAAC,CAAC,GAAG,EAAE,oBAAkB,KAAG,KAAK,cAAc,IAAI,EAAE,OAAO,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,UAAU,GAAE;AAAC,kBAAY,OAAO,MAAI,KAAK,aAAW;AAAA,EAAE;AAAA,EAAC,IAAI,QAAQ,GAAE;AAAC,kBAAY,OAAO,MAAI,KAAK,WAAS;AAAA,EAAE;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK;AAAA,EAAU;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,YAAY,GAAE,GAAE;AAAC,QAAI;AAAE,cAAQ,IAAE,KAAK,YAAU,WAAS,KAAG,EAAE,YAAY,EAAE,OAAO,GAAE,CAAC,CAAC,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,QAAI;AAAE,cAAQ,IAAE,KAAK,YAAU,WAAS,KAAG,EAAE,aAAY,EAAE,sBAAoB,EAAE,oBAAkB;AAAA,EAAO;AAAC;AAAC,IAAI,GAAE;AAAE,IAAE,GAAE,OAAO,eAAe,GAAE,eAAc,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,EAAC,CAAC,GAAE,IAAE,EAAC,OAAM,oBAAI,UAAO;AAAE,IAAI,IAAE;AAAG,MAAM,EAAC;AAAA,EAAC,QAAO,OAAO,WAAW,EAAE,GAAE;AAAC,WAAOI,0DAAAA,EAAE,MAAK,GAAE,KAAI,CAAC,EAAE,IAAI,CAAC;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,UAAI,CAAC,WAAU;AAAC,YAAMJ,KAAEK,oDAAAA,KAAG,sBAAqB,GAAGJ,KAAED,GAAE,WAAU,EAAG,MAAK,IAAE,OAAO,eAAeC,EAAC;AAAE,OAAC,kBAAiB,mBAAkB,yBAAyB,EAAE,QAAS,CAAAD,OAAG;AAAC,cAAMC,KAAE,EAAED,EAAC;AAAE,UAAEA,EAAC,IAAE,YAAYA,IAAE;AAAC,cAAG;AAAC,YAAAC,GAAE,MAAM,MAAKD,EAAC;AAAA,UAAC,SAAOC,IAAE;AAAC,iBAAK,QAAMD,GAAE,CAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC,IAAIA,GAAE,MAAK;AAAA,IAAE,EAAC,GAAG,IAAE;AAAI,UAAM,IAAEK,oDAAAA,KAAG;AAAwBD,8DAAAA,EAAE,GAAE,GAAE,KAAI,CAAC,EAAE,IAAI,CAAC;AAAE,UAAM,IAAE,EAAE;AAAgB,WAAO,EAAE,kBAAgB,SAAS,GAAE,GAAE,GAAE;AAAC,aAAO,IAAI,QAAS,CAAC,GAAE,MAAI;AAAC,UAAE,KAAK,GAAE,GAAG,CAAAJ,OAAG;AAAC,eAAG,EAAEA,EAAC,GAAE,EAAEA,EAAC;AAAA,QAAC,GAAI,CAAAA,OAAG;AAAC,eAAG,EAAEA,EAAC,GAAE,EAAEA,EAAC;AAAA,QAAC,CAAC;AAAA,MAAE;IAAG,GAAE;AAAA,EAAC;AAAC;AAAC,IAAE,GAAE,IAAE,EAAC,OAAM,oBAAI,UAAO;AAAE,MAAM,IAAE,eAAcK,oDAAAA;AAAG,IAAI,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,MAAM,KAAG,MAAI;AAAC,QAAM,IAAE,IAAE,OAAKA,oDAAAA,KAAG,sBAAsB,EAAC,OAAM,GAAE,QAAO,GAAE,MAAK,KAAI,CAAC;AAAE,SAAM,EAAC,aAAY,MAAI,IAAEA,oDAAAA,KAAG,gBAAc,EAAE,YAAW,GAAG,iBAAgB,IAAI,MAAI,IAAEA,oDAAAA,KAAG,gBAAgB,GAAG,CAAC,IAAE,EAAE,gBAAgB,GAAG,CAAC,GAAE,iBAAiBL,IAAE,GAAE,GAAE;AAAC,QAAG,GAAE;AAAC,YAAME,KAAEG,yDAAG,aAAY;AAAG,aAAOH,GAAE,QAAM,QAAMF,KAAEA,KAAE,GAAEE,GAAE,SAAO,QAAM,IAAE,IAAE,GAAEA;AAAA,IAAC;AAAC;AAAC,YAAM,IAAEG,oDAAAA,KAAG,sBAAsB,EAAC,OAAM,QAAML,KAAEA,KAAE,GAAE,QAAO,QAAM,IAAE,IAAE,GAAE,MAAK,QAAM,IAAE,IAAEM,0DAAAA,EAAE,yBAAwB,CAAC;AAAE,aAAO,EAAE,QAAM,QAAMN,KAAEA,KAAE,GAAE,EAAE,SAAO,QAAM,IAAE,IAAE,GAAE;AAAA,IAAC;AAAA,EAAC,GAAE,uBAAsB,IAAE,wBAAsB,EAAE,uBAAsB,sBAAqB,IAAE,uBAAqB,EAAE,qBAAoB;AAAC,GAAC,GAAI,IAAE,oBAAI;AAAQ,SAAS,EAAE,GAAE;AAAC,IAAE,IAAI,CAAC;AAAC;AAAC,MAAM,IAAE;AAAqB,SAAS,EAAE,GAAE,GAAE,GAAE;AAAC,QAAM,IAAE,OAAO,OAAO,GAAE,YAAY,IAAE,EAAE,aAAW;AAAK,OAAG,OAAO,EAAE;AAAW,QAAK,EAAC,SAAQ,GAAE,gBAAe,EAAC,IAAE,EAAE,GAAE,GAAE,GAAG,CAACE,IAAEC,QAAK,EAAC,YAAW,EAAC,OAAM,IAAIG,OAAI;AAAC,UAAM,KAAG,KAAGH,GAAE,YAAY,KAAK,GAAE,GAAGG,EAAC;AAAE,iBAAWA,GAAE,CAAC,KAAGC,0DAAAA,EAAE,CAAC;AAAE,UAAMC,KAAE,EAAE;AAAO,QAAG,EAAE,KAAKA,KAAG;AAAC,UAAG;AAAC,UAAEA,EAAC,GAAER,0DAAAA,EAAEQ,EAAC,GAAE,OAAO,eAAeA,IAAE,SAAQ,EAAC,OAAMN,IAAE,UAAS,MAAE,CAAC;AAAA,MAAC,SAAO,GAAE;AAAA,MAAC;AAAC,MAAAM,GAAE,CAAC,IAAE;AAAA,IAAE;AAAC,WAAO;AAAA,EAAC,EAAC,GAAE,QAAO,EAAC,OAAM,CAAC,GAAEN,IAAEC,OAAI;AAACM,8DAAAA,EAAE,GAAEP,IAAEC,EAAC,EAAE,KAAM,CAAAF,OAAG;AAAC,QAAEA,EAAC;AAAA,IAAC,CAAC;AAAA,EAAE,EAAC,EAAC,EAAE,GAAG,IAAE,EAAE,sBAAqB;AAAG,SAAO,EAAE,UAAQ,EAAE,UAAQ,EAAE,QAAM,EAAE,QAAO,EAAE,WAAS,EAAE,WAAS,EAAE,SAAO,EAAE,SAAQ,EAAE,CAAC,GAAE,EAAC,QAAO,GAAE,gBAAe,EAAC;AAAC;AAAC,MAAM,EAAC;AAAA,EAAC,QAAO,OAAO,WAAW,EAAE,GAAE;AAAC,WAAOC,0DAAAA,EAAE,MAAK,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,IAAI,UAAU,qBAAqB;AAAA,EAAC;AAAC;AAAC,MAAM,EAAC;AAAA,EAAC,QAAO,OAAO,WAAW,EAAE,GAAE;AAAC,WAAOA,0DAAAA,EAAE,MAAK,GAAEE,0DAAAA,EAAE,MAAK,GAAE,KAAI,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,IAAED,4DAAE,OAAO,GAAE,IAAEH,4DAAE,EAAE,YAAW,CAAE;AAAE,MAAE,SAAO,OAAG;AAAC,QAAE,cAAc,IAAI,EAAE,MAAM,CAAC;AAAA,IAAC,GAAE,EAAE,UAAQ,OAAG;AAAC,QAAE,cAAc,IAAI,EAAE,EAAE,CAAC;AAAA,IAAC;AAAE,UAAM,IAAE,OAAO,yBAAyB,OAAO,eAAe,CAAC,GAAE,KAAK;AAAE,QAAG,CAAC,EAAE,OAAM,MAAM,6CAA6C;AAAE,UAAK,EAAC,KAAI,GAAE,KAAI,EAAC,IAAE;AAAE,WAAO,OAAO,iBAAiB,GAAE,EAAC,cAAa,EAAC,KAAI,MAAI,EAAE,MAAK,GAAE,eAAc,EAAC,KAAI,MAAI,EAAE,OAAM,GAAE,KAAI,EAAC,YAAW,MAAG,cAAa,OAAG,KAAI,GAAE,IAAI,GAAE;AAAC,OAAC,IAAE,OAAO,CAAC,GAAG,WAAWU,8DAAC,IAAE,EAAE,KAAK,GAAEC,4DAAAA,GAAE,CAAC,CAAC,IAAE,EAAE,KAAK,GAAE,CAAC;AAAA,IAAC,EAAC,EAAC,CAAC,GAAEP,0DAAAA,EAAE,GAAE,GAAE,KAAI,CAAC,EAAE,IAAI,CAAC,GAAE;AAAA,EAAC;AAAC;AAAC,IAAE,GAAE,IAAE,EAAC,OAAM,oBAAI,UAAO;AAAE,MAAM,EAAC;AAAA,EAAC,QAAO,OAAO,WAAW,EAAE,GAAE;AAAC,WAAOF,0DAAAA,EAAE,MAAK,GAAEE,0DAAAA,EAAE,MAAK,GAAE,KAAI,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,YAAY,GAAE,GAAE;AAAC,UAAM,IAAE,IAAI;AAAE,WAAO,WAAS,MAAI,EAAE,QAAM,IAAG,WAAS,MAAI,EAAE,SAAO,IAAGA,0DAAAA,EAAE,GAAE,GAAE,KAAI,CAAC,EAAE,IAAI,CAAC,GAAE;AAAA,EAAC;AAAC;AAAC,IAAE,GAAE,IAAE,EAAC,OAAM,oBAAI,UAAO;AAAE,MAAM,EAAC;AAAA,EAAC,QAAO,OAAO,WAAW,EAAE,GAAE;AAAC,WAAOF,0DAAAA,EAAE,MAAK,GAAEE,0DAAAA,EAAE,MAAK,GAAE,KAAI,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,eAAe,GAAE;AAAC,UAAM,IAAE,EAAE,gBAAgB,GAAG,CAAC;AAAE,WAAOA,0DAAAA,EAAE,GAAE,GAAE,KAAI,CAAC,EAAE,IAAI,CAAC,GAAE;AAAA,EAAC;AAAC;AAAC,IAAE,GAAE,IAAE,EAAC,OAAM,oBAAI,UAAO;AAAE,MAAM,EAAC;AAAA,EAAC,QAAO,OAAO,WAAW,EAAE,GAAE;AAAC,WAAOF,0DAAAA,EAAE,MAAK,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,YAAY,GAAE,GAAE;AAAC,WAAO,SAASF,IAAEC,IAAE,GAAE;AAAC,YAAK,EAAC,QAAO,EAAC,IAAE,EAAE,EAAE,iBAAiBD,IAAEC,IAAE,CAAC,CAAC;AAAE,aAAO;AAAA,IAAC,EAAE,GAAE,GAAEK,0DAAAA,EAAE,wBAAwB;AAAA,EAAC;AAAC;AAAM,MAAC,IAAE,EAAE,uBAAsB,IAAE,EAAE;AAAqB,SAAS,EAAE,GAAE,GAAE;AAAC,SAAO,OAAG;AAAC,QAAI,GAAE;AAAE,UAAM,IAAE,CAAAL,OAAG;AAAC,UAAI,GAAE;AAAE,UAAG,WAAS,EAAE,KAAEA,OAAI,UAAQ,IAAE,WAAW,gBAAc,WAAS,IAAE,SAAO,EAAE,IAAG,MAAK,KAAK,IAAG;AAAA,WAAO;AAAC,cAAMD,KAAEC,OAAI,UAAQ,IAAE,WAAW,gBAAc,WAAS,IAAE,SAAO,EAAE,IAAG,MAAK,KAAK,IAAG,GAAGG,KAAEJ,KAAE;AAAE,YAAEA;AAAE,YAAG;AAAC,YAAEI,EAAC;AAAA,QAAC,SAAOJ,IAAE;AAAC,kBAAQ,MAAM,oBAAmBA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,UAAE,EAAE,CAAC;AAAA,IAAC;AAAE,WAAO,IAAE,EAAE,CAAC,GAAE,EAAC,QAAO,MAAI,EAAE,CAAC,EAAC;AAAA,EAAC;AAAC;AAAC,MAAM,IAAE,oBAAI;AAAQ,SAAS,EAAE,GAAE,GAAE,GAAE,IAAE,MAAG;AAAC,QAAM,IAAEG,0DAAAA,EAAE,cAAc,GAAE,IAAE,EAAC,YAAW,eAAc,WAAU,eAAc,UAAS,aAAY,aAAY,iBAAgB,KAAI,QAAO,EAAE,EAAE,IAAI,KAAG,IAAG,IAAE,MAAM,KAAK,EAAE,cAAc;AAAE,MAAG,EAAE,QAAO;AAAC,QAAI,GAAE;AAAE,MAAE,IAAI,CAAC,IAAE,IAAE,EAAE,IAAI,CAAC,KAAG,IAAE,CAAA,GAAG,EAAE,IAAI,GAAE,CAAC;AAAG,UAAM,IAAE,EAAE,EAAE,CAAC,EAAE,UAAU;AAAE,KAAC,CAAC,KAAG,KAAG,EAAE,UAAQ,KAAK,IAAG,KAAI,iBAAe,EAAE,QAAM,UAAQ,EAAE,QAAM,CAAC,OAAK,IAAE,QAAQ,QAAQ,GAAG,GAAE,EAAE,EAAE,CAAC,EAAE,UAAU,IAAE,EAAC,GAAE,GAAE,SAAQ,KAAK,IAAG,IAAG,IAAG,GAAEK,0DAAAA,EAAE,wBAAuB,GAAE,EAAE,IAAI,CAAC,CAAC,IAAG,CAAC,KAAG,EAAE,CAAC,EAAE,cAAc,MAAIA,0DAAAA,EAAE,2BAA0B,GAAE,CAAC,GAAE,IAAE,EAAE,EAAE,CAAC,EAAE,UAAU,EAAE,IAAG,MAAI,IAAE,QAAQ,QAAQ,GAAG,GAAE,EAAE,EAAE,CAAC,EAAE,UAAU,IAAE,EAAC,GAAE,GAAE,SAAQ,KAAK,IAAG,IAAG,IAAG,IAAG,EAAE,KAAM,CAAAN,OAAG;AAAC,QAAE,QAAS,CAACC,IAAEC,OAAI;AAAC,cAAMG,KAAE,IAAI,EAAE,GAAE,EAAC,WAAUJ,GAAE,YAAW,aAAY,SAAQ,SAAQA,GAAE,GAAE,SAAQA,GAAE,GAAE,SAAQ,aAAYA,KAAEA,GAAE,WAASA,GAAE,KAAG,KAAGD,GAAE,MAAK,SAAQ,aAAYC,KAAEA,GAAE,WAASA,GAAE,KAAG,KAAGD,GAAE,KAAI,QAAO,kBAAgB,IAAE,KAAG,GAAE,WAAU,MAAIE,GAAC,CAAC;AAAE,SAAC,CAAC,EAAE,OAAO,OAAQ,CAAAJ,OAAG,CAAC,CAACA,EAAC,EAAG,QAAS,CAAAC,OAAG;AAAC,cAAG;AAACO,sEAAAA,EAAE,iBAAiB,EAAE,IAAI,OAAOD,GAAE,IAAI,IAAG,GAAEA,IAAEN,EAAC,GAAE,cAAY,OAAOA,GAAE,iBAAeA,GAAE,cAAcM,EAAC,GAAE,oBAAkBA,GAAE,QAAM,WAAY,MAAI;AAAC,4BAAY,OAAON,GAAE,iBAAeA,GAAE,cAAcM,EAAC;AAAA,YAAC,GAAG,GAAG;AAAA,UAAC,SAAOP,IAAE;AAAC,oBAAQ,MAAMA,EAAC;AAAA,UAAC;AAAA,QAAC;MAAG,CAAC;AAAA,IAAE,CAAC;AAAA,EAAE;AAAC;;;;;;;;;;;;;;;", "x_google_ignoreList": [0]}