{"version": 3, "file": "Simulator.js", "sources": ["sub-pack-2/particles/glsl/quad.vert", "sub-pack-2/particles/glsl/through.frag", "sub-pack-2/particles/glsl/position.frag"], "sourcesContent": ["attribute vec3 position;\r\n\r\nvoid main() {\r\n    gl_Position = vec4( position, 1.0 );\r\n}\r\n", "uniform vec2 resolution;\r\nuniform sampler2D texture;\r\n\r\nvoid main() {\r\n    vec2 uv = gl_FragCoord.xy / resolution.xy;\r\n    gl_FragColor = texture2D( texture, uv );\r\n}\r\n", "uniform vec2 resolution;\r\nuniform sampler2D texturePosition;\r\nuniform sampler2D textureDefaultPosition;\r\nuniform float time;\r\nuniform float speed;\r\nuniform float dieSpeed;\r\nuniform float radius;\r\nuniform float curlSize;\r\nuniform float attraction;\r\nuniform float initAnimation;\r\nuniform vec3 mouse3d;\r\n\r\nvec4 mod289(vec4 x) {\r\n    return x - floor(x * (1.0 / 289.0)) * 289.0;\r\n}\r\n\r\nfloat mod289(float x) {\r\n    return x - floor(x * (1.0 / 289.0)) * 289.0;\r\n}\r\n\r\nvec4 permute(vec4 x) {\r\n    return mod289(((x*34.0)+1.0)*x);\r\n}\r\n\r\nfloat permute(float x) {\r\n    return mod289(((x*34.0)+1.0)*x);\r\n}\r\n\r\nvec4 taylorInvSqrt(vec4 r) {\r\n    return 1.79284291400159 - 0.85373472095314 * r;\r\n}\r\n\r\nfloat taylorInvSqrt(float r) {\r\n    return 1.79284291400159 - 0.85373472095314 * r;\r\n}\r\n\r\nvec4 grad4(float j, vec4 ip) {\r\n    const vec4 ones = vec4(1.0, 1.0, 1.0, -1.0);\r\n    vec4 p,s;\r\n\r\n    p.xyz = floor( fract (vec3(j) * ip.xyz) * 7.0) * ip.z - 1.0;\r\n    p.w = 1.5 - dot(abs(p.xyz), ones.xyz);\r\n    s = vec4(lessThan(p, vec4(0.0)));\r\n    p.xyz = p.xyz + (s.xyz*2.0 - 1.0) * s.www;\r\n\r\n    return p;\r\n}\r\n\r\n#define F4 0.309016994374947451\r\n\r\nvec4 simplexNoiseDerivatives (vec4 v) {\r\n    const vec4  C = vec4( 0.138196601125011,0.276393202250021,0.414589803375032,-0.447213595499958);\r\n\r\n    vec4 i  = floor(v + dot(v, vec4(F4)) );\r\n    vec4 x0 = v -   i + dot(i, C.xxxx);\r\n\r\n    vec4 i0;\r\n    vec3 isX = step( x0.yzw, x0.xxx );\r\n    vec3 isYZ = step( x0.zww, x0.yyz );\r\n    i0.x = isX.x + isX.y + isX.z;\r\n    i0.yzw = 1.0 - isX;\r\n    i0.y += isYZ.x + isYZ.y;\r\n    i0.zw += 1.0 - isYZ.xy;\r\n    i0.z += isYZ.z;\r\n    i0.w += 1.0 - isYZ.z;\r\n\r\n    vec4 i3 = clamp( i0, 0.0, 1.0 );\r\n    vec4 i2 = clamp( i0-1.0, 0.0, 1.0 );\r\n    vec4 i1 = clamp( i0-2.0, 0.0, 1.0 );\r\n\r\n    vec4 x1 = x0 - i1 + C.xxxx;\r\n    vec4 x2 = x0 - i2 + C.yyyy;\r\n    vec4 x3 = x0 - i3 + C.zzzz;\r\n    vec4 x4 = x0 + C.wwww;\r\n\r\n    i = mod289(i);\r\n    float j0 = permute( permute( permute( permute(i.w) + i.z) + i.y) + i.x);\r\n    vec4 j1 = permute( permute( permute( permute (\r\n                                             i.w + vec4(i1.w, i2.w, i3.w, 1.0 ))\r\n                                         + i.z + vec4(i1.z, i2.z, i3.z, 1.0 ))\r\n                                + i.y + vec4(i1.y, i2.y, i3.y, 1.0 ))\r\n                       + i.x + vec4(i1.x, i2.x, i3.x, 1.0 ));\r\n\r\n    vec4 ip = vec4(1.0/294.0, 1.0/49.0, 1.0/7.0, 0.0) ;\r\n\r\n    vec4 p0 = grad4(j0,   ip);\r\n    vec4 p1 = grad4(j1.x, ip);\r\n    vec4 p2 = grad4(j1.y, ip);\r\n    vec4 p3 = grad4(j1.z, ip);\r\n    vec4 p4 = grad4(j1.w, ip);\r\n\r\n    vec4 norm = taylorInvSqrt(vec4(dot(p0,p0), dot(p1,p1), dot(p2, p2), dot(p3,p3)));\r\n    p0 *= norm.x;\r\n    p1 *= norm.y;\r\n    p2 *= norm.z;\r\n    p3 *= norm.w;\r\n    p4 *= taylorInvSqrt(dot(p4,p4));\r\n\r\n    vec3 values0 = vec3(dot(p0, x0), dot(p1, x1), dot(p2, x2));\r\n    vec2 values1 = vec2(dot(p3, x3), dot(p4, x4));\r\n\r\n    vec3 m0 = max(0.5 - vec3(dot(x0,x0), dot(x1,x1), dot(x2,x2)), 0.0);\r\n    vec2 m1 = max(0.5 - vec2(dot(x3,x3), dot(x4,x4)), 0.0);\r\n\r\n    vec3 temp0 = -6.0 * m0 * m0 * values0;\r\n    vec2 temp1 = -6.0 * m1 * m1 * values1;\r\n\r\n    vec3 mmm0 = m0 * m0 * m0;\r\n    vec2 mmm1 = m1 * m1 * m1;\r\n\r\n    float dx = temp0[0] * x0.x + temp0[1] * x1.x + temp0[2] * x2.x + temp1[0] * x3.x + temp1[1] * x4.x + mmm0[0] * p0.x + mmm0[1] * p1.x + mmm0[2] * p2.x + mmm1[0] * p3.x + mmm1[1] * p4.x;\r\n    float dy = temp0[0] * x0.y + temp0[1] * x1.y + temp0[2] * x2.y + temp1[0] * x3.y + temp1[1] * x4.y + mmm0[0] * p0.y + mmm0[1] * p1.y + mmm0[2] * p2.y + mmm1[0] * p3.y + mmm1[1] * p4.y;\r\n    float dz = temp0[0] * x0.z + temp0[1] * x1.z + temp0[2] * x2.z + temp1[0] * x3.z + temp1[1] * x4.z + mmm0[0] * p0.z + mmm0[1] * p1.z + mmm0[2] * p2.z + mmm1[0] * p3.z + mmm1[1] * p4.z;\r\n    float dw = temp0[0] * x0.w + temp0[1] * x1.w + temp0[2] * x2.w + temp1[0] * x3.w + temp1[1] * x4.w + mmm0[0] * p0.w + mmm0[1] * p1.w + mmm0[2] * p2.w + mmm1[0] * p3.w + mmm1[1] * p4.w;\r\n\r\n    return vec4(dx, dy, dz, dw) * 49.0;\r\n}\r\n\r\nvec3 curl( in vec3 p, in float noiseTime, in float persistence ) {\r\n\r\n    vec4 xNoisePotentialDerivatives = vec4(0.0);\r\n    vec4 yNoisePotentialDerivatives = vec4(0.0);\r\n    vec4 zNoisePotentialDerivatives = vec4(0.0);\r\n\r\n    for (int i = 0; i < 3; ++i) {\r\n\r\n        float twoPowI = pow(2.0, float(i));\r\n        float scale = 0.5 * twoPowI * pow(persistence, float(i));\r\n\r\n        xNoisePotentialDerivatives += simplexNoiseDerivatives(vec4(p * twoPowI, noiseTime)) * scale;\r\n        yNoisePotentialDerivatives += simplexNoiseDerivatives(vec4((p + vec3(123.4, 129845.6, -1239.1)) * twoPowI, noiseTime)) * scale;\r\n        zNoisePotentialDerivatives += simplexNoiseDerivatives(vec4((p + vec3(-9519.0, 9051.0, -123.0)) * twoPowI, noiseTime)) * scale;\r\n    }\r\n\r\n    return vec3(\r\n    zNoisePotentialDerivatives[1] - yNoisePotentialDerivatives[2],\r\n    xNoisePotentialDerivatives[2] - zNoisePotentialDerivatives[0],\r\n    yNoisePotentialDerivatives[0] - xNoisePotentialDerivatives[1]\r\n    );\r\n\r\n}\r\n\r\nvoid main() {\r\n\r\n    vec2 uv = gl_FragCoord.xy / resolution.xy;\r\n\r\n    vec4 positionInfo = texture2D( texturePosition, uv );\r\n    vec3 position = mix(vec3(0.0, -200.0, 0.0), positionInfo.xyz, smoothstep(0.0, 0.3, initAnimation));\r\n    float life = positionInfo.a - dieSpeed;\r\n\r\n    vec3 followPosition = mix(vec3(0.0, -(1.0 - initAnimation) * 200.0, 0.0), mouse3d, smoothstep(0.2, 0.7, initAnimation));\r\n\r\n    if(life < 0.0) {\r\n        positionInfo = texture2D( textureDefaultPosition, uv );\r\n        position = positionInfo.xyz * (1.0 + sin(time * 15.0) * 0.2 + (1.0 - initAnimation)) * 0.4 * radius;\r\n        position += followPosition;\r\n        life = 0.5 + fract(positionInfo.w * 21.4131 + time);\r\n    } else {\r\n        vec3 delta = followPosition - position;\r\n        position += delta * (0.005 + life * 0.01) * attraction * (1.0 - smoothstep(50.0, 350.0, length(delta))) *speed;\r\n        position += curl(position * curlSize, time, 0.1 + (1.0 - life) * 0.1) *speed;\r\n    }\r\n\r\n    gl_FragColor = vec4(position, life);\r\n\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA,IAAA,eAAA;ACAA,IAAA,kBAAA;ACAA,IAAA,mBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}