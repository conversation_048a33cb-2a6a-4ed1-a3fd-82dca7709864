"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
class LocalAsset {
  static resolve(s, t) {
    const _ = this.ASSET_MAP[`${s}:${t}`];
    if (_) return _;
    throw Error(`Cannot resolve local asset ${s}:${t}`);
  }
}
__publicField(LocalAsset, "ASSET_MAP", {"wasm:meshopt_decoder.wasm":"wasms/meshopt_decoder.wasm.br","wasm:basis_transcoder.wasm":"wasms/basis_transcoder.wasm.br","wasm:zstd_decoder.wasm":"wasms/zstd_decoder.wasm.br","worker:meshopt_decoder.js":"workers/meshopt_decoder.js","worker:basis/basis_transcoder.js":"workers/basis/basis_transcoder.js","worker:worker-adapter.js":"workers/worker-adapter.js"});
__publicField(LocalAsset, "USE_BROTLI", true);
exports.LocalAsset = LocalAsset;
//# sourceMappingURL=../../../../../../../../.sourcemap/mp-weixin/_mpChunkDeps/@minisheep/three-platform-adapter/dist/three-override/jsm/utils/LocalAsset.js.map
