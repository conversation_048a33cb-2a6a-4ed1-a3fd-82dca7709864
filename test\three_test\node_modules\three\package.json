{"name": "three", "version": "0.172.0", "description": "JavaScript 3D library", "type": "module", "main": "./build/three.cjs", "module": "./build/three.module.js", "exports": {".": {"import": "./build/three.module.js", "require": "./build/three.cjs"}, "./examples/fonts/*": "./examples/fonts/*", "./examples/jsm/*": "./examples/jsm/*", "./addons": "./examples/jsm/Addons.js", "./addons/*": "./examples/jsm/*", "./src/*": "./src/*", "./webgpu": "./build/three.webgpu.js", "./tsl": "./build/three.tsl.js"}, "repository": {"type": "git", "url": "https://github.com/mrdoob/three.js"}, "sideEffects": ["./src/nodes/**/*"], "files": ["build", "examples/jsm", "examples/fonts", "LICENSE", "package.json", "README.md", "src"], "directories": {"doc": "docs", "example": "examples", "test": "test"}, "browserslist": ["> 1%, not dead, not ie 11, not op_mini all"], "scripts": {"start": "npm run dev", "test": "npm run lint && npm run test-unit && npm run test-unit-addons", "build": "rollup -c utils/build/rollup.config.js", "build-module": "rollup -c utils/build/rollup.config.js --configOnlyModule", "build-docs": "jsdoc -c utils/docs/jsdoc.config.json", "dev": "concurrently --names \"ROLL<PERSON>,HTTP\" -c \"bgBlue.bold,bgGreen.bold\" \"rollup -c utils/build/rollup.config.js -w -m inline\" \"servez -p 8080\"", "dev-ssl": "concurrently --names \"ROLL<PERSON>,HTTPS\" -c \"bgBlue.bold,bgGreen.bold\" \"rollup -c utils/build/rollup.config.js -w -m inline\" \"servez -p 8080 --ssl\"", "lint-core": "eslint src", "lint-addons": "eslint examples/jsm --ext .js --ignore-pattern libs --ignore-pattern ifc", "lint-examples": "eslint examples --ext .html", "lint-docs": "eslint docs --ignore-pattern prettify.js", "lint-editor": "eslint editor --ignore-pattern libs", "lint-playground": "eslint playground --ignore-pattern libs", "lint-manual": "eslint manual --ignore-pattern 3rdparty --ignore-pattern prettify.js --ignore-pattern shapefile.js", "lint-test": "eslint test --ignore-pattern vendor", "lint-utils": "eslint utils", "lint": "npm run lint-core", "lint-fix": "npm run lint-core -- --fix && npm run lint-addons -- --fix && npm run lint-examples -- --fix && npm run lint-docs -- --fix && npm run lint-editor -- --fix && npm run lint-playground -- --fix && npm run lint-manual -- --fix && npm run lint-test -- --fix && npm run lint-utils -- --fix", "test-unit": "qunit -r failonlyreporter -f !-webonly test/unit/three.source.unit.js", "test-unit-addons": "qunit -r failonlyreporter -f !-webonly test/unit/three.addons.unit.js", "test-e2e": "node test/e2e/puppeteer.js", "test-e2e-cov": "node test/e2e/check-coverage.js", "test-e2e-webgpu": "node test/e2e/puppeteer.js --webgpu", "test-treeshake": "rollup -c test/rollup.treeshake.config.js", "test-circular-deps": "dpdm --no-warning --no-tree --exit-code circular:1 src/nodes/Nodes.js", "make-screenshot": "node test/e2e/puppeteer.js --make"}, "keywords": ["three", "three.js", "javascript", "3d", "virtual-reality", "augmented-reality", "webgl", "webgl2", "webaudio", "webgpu", "webxr", "canvas", "svg", "html5"], "author": "m<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/mrdoob/three.js/issues"}, "homepage": "https://threejs.org/", "devDependencies": {"@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-terser": "^0.4.0", "chalk": "^5.2.0", "clean-jsdoc-theme": "^4.3.0", "concurrently": "^9.0.0", "dpdm": "^3.14.0", "eslint": "^8.37.0", "eslint-config-mdcs": "^5.0.0", "eslint-plugin-compat": "^6.0.0", "eslint-plugin-html": "^8.0.0", "eslint-plugin-import": "^2.27.5", "failonlyreporter": "^1.0.0", "jimp": "^1.6.0", "jsdoc": "^4.0.4", "magic-string": "^0.30.0", "pixelmatch": "^6.0.0", "puppeteer": "^22.0.0", "qunit": "^2.19.4", "rollup": "^4.6.0", "rollup-plugin-filesize": "^10.0.0", "rollup-plugin-visualizer": "^5.9.0", "servez": "^2.2.4"}, "overrides": {"jpeg-js": "^0.4.4"}, "jspm": {"files": ["package.json", "LICENSE", "README.md", "build/three.js", "build/three.min.js", "build/three.module.js"], "directories": {}}}