"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
require("../../chunks/three/build/three.module.min.js");
const subPack2_particles_core_settings = require("../core/settings.js");
const _mpChunkDeps_three_build_three_core_min = require("../../chunks/three/build/three.core.min.js");
var quad_default = "attribute vec3 position;\n\nvoid main() {\r\n    gl_Position = vec4( position, 1.0 );\r\n}";
var through_default = "uniform vec2 resolution;\r\nuniform sampler2D texture;\n\nvoid main() {\r\n    vec2 uv = gl_FragCoord.xy / resolution.xy;\r\n    gl_FragColor = texture2D( texture, uv );\r\n}";
var position_default = "uniform vec2 resolution;\r\nuniform sampler2D texturePosition;\r\nuniform sampler2D textureDefaultPosition;\r\nuniform float time;\r\nuniform float speed;\r\nuniform float dieSpeed;\r\nuniform float radius;\r\nuniform float curlSize;\r\nuniform float attraction;\r\nuniform float initAnimation;\r\nuniform vec3 mouse3d;\n\nvec4 mod289(vec4 x) {\r\n    return x - floor(x * (1.0 / 289.0)) * 289.0;\r\n}\n\nfloat mod289(float x) {\r\n    return x - floor(x * (1.0 / 289.0)) * 289.0;\r\n}\n\nvec4 permute(vec4 x) {\r\n    return mod289(((x*34.0)+1.0)*x);\r\n}\n\nfloat permute(float x) {\r\n    return mod289(((x*34.0)+1.0)*x);\r\n}\n\nvec4 taylorInvSqrt(vec4 r) {\r\n    return 1.79284291400159 - 0.85373472095314 * r;\r\n}\n\nfloat taylorInvSqrt(float r) {\r\n    return 1.79284291400159 - 0.85373472095314 * r;\r\n}\n\nvec4 grad4(float j, vec4 ip) {\r\n    const vec4 ones = vec4(1.0, 1.0, 1.0, -1.0);\r\n    vec4 p,s;\n\n    p.xyz = floor( fract (vec3(j) * ip.xyz) * 7.0) * ip.z - 1.0;\r\n    p.w = 1.5 - dot(abs(p.xyz), ones.xyz);\r\n    s = vec4(lessThan(p, vec4(0.0)));\r\n    p.xyz = p.xyz + (s.xyz*2.0 - 1.0) * s.www;\n\n    return p;\r\n}\n\n#define F4 0.309016994374947451\n\nvec4 simplexNoiseDerivatives (vec4 v) {\r\n    const vec4  C = vec4( 0.138196601125011,0.276393202250021,0.414589803375032,-0.447213595499958);\n\n    vec4 i  = floor(v + dot(v, vec4(F4)) );\r\n    vec4 x0 = v -   i + dot(i, C.xxxx);\n\n    vec4 i0;\r\n    vec3 isX = step( x0.yzw, x0.xxx );\r\n    vec3 isYZ = step( x0.zww, x0.yyz );\r\n    i0.x = isX.x + isX.y + isX.z;\r\n    i0.yzw = 1.0 - isX;\r\n    i0.y += isYZ.x + isYZ.y;\r\n    i0.zw += 1.0 - isYZ.xy;\r\n    i0.z += isYZ.z;\r\n    i0.w += 1.0 - isYZ.z;\n\n    vec4 i3 = clamp( i0, 0.0, 1.0 );\r\n    vec4 i2 = clamp( i0-1.0, 0.0, 1.0 );\r\n    vec4 i1 = clamp( i0-2.0, 0.0, 1.0 );\n\n    vec4 x1 = x0 - i1 + C.xxxx;\r\n    vec4 x2 = x0 - i2 + C.yyyy;\r\n    vec4 x3 = x0 - i3 + C.zzzz;\r\n    vec4 x4 = x0 + C.wwww;\n\n    i = mod289(i);\r\n    float j0 = permute( permute( permute( permute(i.w) + i.z) + i.y) + i.x);\r\n    vec4 j1 = permute( permute( permute( permute (\r\n                                             i.w + vec4(i1.w, i2.w, i3.w, 1.0 ))\r\n                                         + i.z + vec4(i1.z, i2.z, i3.z, 1.0 ))\r\n                                + i.y + vec4(i1.y, i2.y, i3.y, 1.0 ))\r\n                       + i.x + vec4(i1.x, i2.x, i3.x, 1.0 ));\n\n    vec4 ip = vec4(1.0/294.0, 1.0/49.0, 1.0/7.0, 0.0) ;\n\n    vec4 p0 = grad4(j0,   ip);\r\n    vec4 p1 = grad4(j1.x, ip);\r\n    vec4 p2 = grad4(j1.y, ip);\r\n    vec4 p3 = grad4(j1.z, ip);\r\n    vec4 p4 = grad4(j1.w, ip);\n\n    vec4 norm = taylorInvSqrt(vec4(dot(p0,p0), dot(p1,p1), dot(p2, p2), dot(p3,p3)));\r\n    p0 *= norm.x;\r\n    p1 *= norm.y;\r\n    p2 *= norm.z;\r\n    p3 *= norm.w;\r\n    p4 *= taylorInvSqrt(dot(p4,p4));\n\n    vec3 values0 = vec3(dot(p0, x0), dot(p1, x1), dot(p2, x2));\r\n    vec2 values1 = vec2(dot(p3, x3), dot(p4, x4));\n\n    vec3 m0 = max(0.5 - vec3(dot(x0,x0), dot(x1,x1), dot(x2,x2)), 0.0);\r\n    vec2 m1 = max(0.5 - vec2(dot(x3,x3), dot(x4,x4)), 0.0);\n\n    vec3 temp0 = -6.0 * m0 * m0 * values0;\r\n    vec2 temp1 = -6.0 * m1 * m1 * values1;\n\n    vec3 mmm0 = m0 * m0 * m0;\r\n    vec2 mmm1 = m1 * m1 * m1;\n\n    float dx = temp0[0] * x0.x + temp0[1] * x1.x + temp0[2] * x2.x + temp1[0] * x3.x + temp1[1] * x4.x + mmm0[0] * p0.x + mmm0[1] * p1.x + mmm0[2] * p2.x + mmm1[0] * p3.x + mmm1[1] * p4.x;\r\n    float dy = temp0[0] * x0.y + temp0[1] * x1.y + temp0[2] * x2.y + temp1[0] * x3.y + temp1[1] * x4.y + mmm0[0] * p0.y + mmm0[1] * p1.y + mmm0[2] * p2.y + mmm1[0] * p3.y + mmm1[1] * p4.y;\r\n    float dz = temp0[0] * x0.z + temp0[1] * x1.z + temp0[2] * x2.z + temp1[0] * x3.z + temp1[1] * x4.z + mmm0[0] * p0.z + mmm0[1] * p1.z + mmm0[2] * p2.z + mmm1[0] * p3.z + mmm1[1] * p4.z;\r\n    float dw = temp0[0] * x0.w + temp0[1] * x1.w + temp0[2] * x2.w + temp1[0] * x3.w + temp1[1] * x4.w + mmm0[0] * p0.w + mmm0[1] * p1.w + mmm0[2] * p2.w + mmm1[0] * p3.w + mmm1[1] * p4.w;\n\n    return vec4(dx, dy, dz, dw) * 49.0;\r\n}\n\nvec3 curl( in vec3 p, in float noiseTime, in float persistence ) {\n\n    vec4 xNoisePotentialDerivatives = vec4(0.0);\r\n    vec4 yNoisePotentialDerivatives = vec4(0.0);\r\n    vec4 zNoisePotentialDerivatives = vec4(0.0);\n\n    for (int i = 0; i < 3; ++i) {\n\n        float twoPowI = pow(2.0, float(i));\r\n        float scale = 0.5 * twoPowI * pow(persistence, float(i));\n\n        xNoisePotentialDerivatives += simplexNoiseDerivatives(vec4(p * twoPowI, noiseTime)) * scale;\r\n        yNoisePotentialDerivatives += simplexNoiseDerivatives(vec4((p + vec3(123.4, 129845.6, -1239.1)) * twoPowI, noiseTime)) * scale;\r\n        zNoisePotentialDerivatives += simplexNoiseDerivatives(vec4((p + vec3(-9519.0, 9051.0, -123.0)) * twoPowI, noiseTime)) * scale;\r\n    }\n\n    return vec3(\r\n    zNoisePotentialDerivatives[1] - yNoisePotentialDerivatives[2],\r\n    xNoisePotentialDerivatives[2] - zNoisePotentialDerivatives[0],\r\n    yNoisePotentialDerivatives[0] - xNoisePotentialDerivatives[1]\r\n    );\n\n}\n\nvoid main() {\n\n    vec2 uv = gl_FragCoord.xy / resolution.xy;\n\n    vec4 positionInfo = texture2D( texturePosition, uv );\r\n    vec3 position = mix(vec3(0.0, -200.0, 0.0), positionInfo.xyz, smoothstep(0.0, 0.3, initAnimation));\r\n    float life = positionInfo.a - dieSpeed;\n\n    vec3 followPosition = mix(vec3(0.0, -(1.0 - initAnimation) * 200.0, 0.0), mouse3d, smoothstep(0.2, 0.7, initAnimation));\n\n    if(life < 0.0) {\r\n        positionInfo = texture2D( textureDefaultPosition, uv );\r\n        position = positionInfo.xyz * (1.0 + sin(time * 15.0) * 0.2 + (1.0 - initAnimation)) * 0.4 * radius;\r\n        position += followPosition;\r\n        life = 0.5 + fract(positionInfo.w * 21.4131 + time);\r\n    } else {\r\n        vec3 delta = followPosition - position;\r\n        position += delta * (0.005 + life * 0.01) * attraction * (1.0 - smoothstep(50.0, 350.0, length(delta))) *speed;\r\n        position += curl(position * curlSize, time, 0.1 + (1.0 - life) * 0.1) *speed;\r\n    }\n\n    gl_FragColor = vec4(position, life);\n\n}";
const undef = void 0;
const TEXTURE_WIDTH = subPack2_particles_core_settings.settings.simulatorTextureWidth;
const TEXTURE_HEIGHT = subPack2_particles_core_settings.settings.simulatorTextureHeight;
const AMOUNT = TEXTURE_WIDTH * TEXTURE_HEIGHT;
class Simulator {
  constructor(renderer) {
    __publicField(this, "initAnimation", 0);
    __publicField(this, "_followPointTime", 0);
    __publicField(this, "_followPoint", new _mpChunkDeps_three_build_three_core_min.Ii());
    __publicField(this, "_scene", new _mpChunkDeps_three_build_three_core_min.ro());
    __publicField(this, "_camera", new _mpChunkDeps_three_build_three_core_min.Xn());
    this._renderer = renderer;
    let rawShaderPrefix = "precision " + renderer.capabilities.precision + " float;\n";
    this._camera.position.z = 1;
    this._copyShader = new _mpChunkDeps_three_build_three_core_min.Sl({
      uniforms: {
        resolution: { type: "v2", value: new _mpChunkDeps_three_build_three_core_min.Zs(TEXTURE_WIDTH, TEXTURE_HEIGHT) },
        texture: { type: "t", value: undef }
      },
      vertexShader: rawShaderPrefix + quad_default,
      fragmentShader: rawShaderPrefix + through_default
    });
    this._positionShader = new _mpChunkDeps_three_build_three_core_min.Sl({
      uniforms: {
        resolution: { type: "v2", value: new _mpChunkDeps_three_build_three_core_min.Zs(TEXTURE_WIDTH, TEXTURE_HEIGHT) },
        texturePosition: { type: "t", value: undef },
        textureDefaultPosition: { type: "t", value: undef },
        mouse3d: { type: "v3", value: new _mpChunkDeps_three_build_three_core_min.Ii() },
        speed: { type: "f", value: 1 },
        dieSpeed: { type: "f", value: 0 },
        radius: { type: "f", value: 0 },
        curlSize: { type: "f", value: 0 },
        attraction: { type: "f", value: 0 },
        time: { type: "f", value: 0 },
        initAnimation: { type: "f", value: 0 }
      },
      vertexShader: rawShaderPrefix + quad_default,
      fragmentShader: rawShaderPrefix + position_default,
      blending: _mpChunkDeps_three_build_three_core_min.m,
      transparent: false,
      depthWrite: false,
      depthTest: false
    });
    this._mesh = new _mpChunkDeps_three_build_three_core_min.Vn(new _mpChunkDeps_three_build_three_core_min.ul(2, 2), this._copyShader);
    this._scene.add(this._mesh);
    this._positionRenderTarget = new _mpChunkDeps_three_build_three_core_min.Si(TEXTURE_WIDTH, TEXTURE_HEIGHT, {
      wrapS: _mpChunkDeps_three_build_three_core_min.mt,
      wrapT: _mpChunkDeps_three_build_three_core_min.mt,
      minFilter: _mpChunkDeps_three_build_three_core_min.ft,
      magFilter: _mpChunkDeps_three_build_three_core_min.ft,
      format: _mpChunkDeps_three_build_three_core_min.Wt,
      type: _mpChunkDeps_three_build_three_core_min.Rt,
      depthWrite: false,
      depthBuffer: false,
      stencilBuffer: false
    });
    this._positionRenderTarget2 = this._positionRenderTarget.clone();
    this._copyTexture(this._createPositionTexture(), this._positionRenderTarget);
    this._copyTexture(this._positionRenderTarget.texture, this._positionRenderTarget2);
  }
  _copyTexture(input, output) {
    this._copyShader.uniforms.texture.value = input;
    const currentRenderTarget = this._renderer.getRenderTarget();
    this._renderer.setRenderTarget(output);
    this._renderer.render(this._scene, this._camera);
    this._renderer.setRenderTarget(currentRenderTarget);
  }
  _updatePosition(dt) {
    const tmp = this._positionRenderTarget;
    this._positionRenderTarget = this._positionRenderTarget2;
    this._positionRenderTarget2 = tmp;
    this._mesh.material = this._positionShader;
    this._positionShader.uniforms.textureDefaultPosition.value = this._textureDefaultPosition;
    this._positionShader.uniforms.texturePosition.value = this._positionRenderTarget2.texture;
    this._positionShader.uniforms.time.value += dt * 1e-3;
    const currentRenderTarget = this._renderer.getRenderTarget();
    this._renderer.setRenderTarget(this._positionRenderTarget);
    this._renderer.render(this._scene, this._camera);
    this._renderer.setRenderTarget(currentRenderTarget);
  }
  _createPositionTexture() {
    const positions = new Float32Array(AMOUNT * 4);
    let i4;
    let r, phi, theta;
    for (let i = 0; i < AMOUNT; i++) {
      i4 = i * 4;
      r = (0.5 + Math.random() * 0.5) * 50;
      phi = (Math.random() - 0.5) * Math.PI;
      theta = Math.random() * Math.PI * 2;
      positions[i4 + 0] = r * Math.cos(theta) * Math.cos(phi);
      positions[i4 + 1] = r * Math.sin(phi);
      positions[i4 + 2] = r * Math.sin(theta) * Math.cos(phi);
      positions[i4 + 3] = Math.random();
    }
    const texture = new _mpChunkDeps_three_build_three_core_min.Vo(
      positions,
      TEXTURE_WIDTH,
      TEXTURE_HEIGHT,
      _mpChunkDeps_three_build_three_core_min.Wt,
      _mpChunkDeps_three_build_three_core_min.Rt
    );
    texture.minFilter = _mpChunkDeps_three_build_three_core_min.ft;
    texture.magFilter = _mpChunkDeps_three_build_three_core_min.ft;
    texture.needsUpdate = true;
    texture.generateMipmaps = false;
    texture.flipY = false;
    this._textureDefaultPosition = texture;
    return texture;
  }
  update(dt) {
    const { _followPointTime, _positionShader, _renderer } = this;
    {
      let r = 200;
      let h = 60;
      {
        r = 100;
        h = 40;
      }
      const autoClearColor = _renderer.autoClearColor;
      const clearColor = new _mpChunkDeps_three_build_three_core_min.$r();
      _renderer.getClearColor(clearColor);
      const clearAlpha = _renderer.getClearAlpha();
      _renderer.autoClearColor = false;
      const deltaRatio = dt / 16.6667;
      _positionShader.uniforms.speed.value = subPack2_particles_core_settings.settings.speed * deltaRatio;
      _positionShader.uniforms.dieSpeed.value = subPack2_particles_core_settings.settings.dieSpeed * deltaRatio;
      _positionShader.uniforms.radius.value = subPack2_particles_core_settings.settings.radius;
      _positionShader.uniforms.curlSize.value = subPack2_particles_core_settings.settings.curlSize;
      _positionShader.uniforms.attraction.value = subPack2_particles_core_settings.settings.attraction;
      _positionShader.uniforms.initAnimation.value = this.initAnimation;
      if (subPack2_particles_core_settings.settings.followMouse) {
        _positionShader.uniforms.mouse3d.value.copy(subPack2_particles_core_settings.settings.mouse3d);
      } else {
        this._followPointTime += dt * 1e-3 * subPack2_particles_core_settings.settings.speed;
        this._followPoint.set(
          Math.cos(_followPointTime) * r,
          Math.cos(_followPointTime * 4) * h,
          Math.sin(_followPointTime * 2) * r
        );
        _positionShader.uniforms.mouse3d.value.lerp(this._followPoint, 0.2);
      }
      _renderer.setClearColor(0, 0);
      this._updatePosition(dt);
      _renderer.setClearColor(clearColor, clearAlpha);
      _renderer.autoClearColor = autoClearColor;
    }
  }
  get positionRenderTarget() {
    return this._positionRenderTarget;
  }
  get prevPositionRenderTarget() {
    return this._positionRenderTarget2;
  }
}
exports.Simulator = Simulator;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/sub-pack-2/particles/3d/Simulator.js.map
