{"version": 3, "file": "Adapter.js", "sources": ["../node_modules/@minisheep/three-platform-adapter/dist/Adapter.mjs"], "sourcesContent": ["import{Blob as e,MessageEvent as t,PointerEvent as n,EventTarget as r,Event as i,atob as a,base642Uint8 as o}from\"@minisheep/mini-program-polyfill-core\";const c={atob:a,Event:i,EventTarget:r,PointerEvent:n,MessageEvent:t,Blob:e},l=Object.assign({},c);function s(e){Object.assign(l,e)}function u(e){if(l.hasOwnProperty(e))return l[e];throw new Error(`could not find extend \"${e}\", please extend it by \"extend(${e})\"`)}function h(e,t,n,r){return new(n||(n=Promise))((function(i,a){function o(e){try{l(r.next(e))}catch(e){a(e)}}function c(e){try{l(r.throw(e))}catch(e){a(e)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,c)}l((r=r.apply(e,t||[])).next())}))}function d(e,t,n,r){if(\"a\"===n&&!r)throw new TypeError(\"Private accessor was defined without a getter\");if(\"function\"==typeof t?e!==t||!r:!t.has(e))throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");return\"m\"===n?r:\"a\"===n?r.call(e):r?r.value:t.get(e)}\"function\"==typeof SuppressedError&&SuppressedError;const m={},f={debug:!1,defaultCanvasContextType:\"2d\"},b=new Proxy({set(e,t,n=!1){n?(Object.hasOwn(m,e)&&console.warn(`once change envParameter [${e}] is already set, it will be overwritten.`),m[e]=t):(Object.hasOwn(m,e)&&(console.warn(`once change envParameter [${e}] is already set, it will be delete.`),delete m[e]),f[e]=t)}},{get(e,t,n){if(Reflect.has(e,t))return Reflect.get(e,t,n);if(Reflect.has(f,t)){if(Object.hasOwn(m,t)){const e=m[t];return delete m[t],e}return Reflect.get(f,t,n)}}});function g(){}function w(...e){b.debug&&console.log(...e)}function v(e){return h(this,arguments,void 0,(function*(e,t=\"image/png\",n=.92){const r=e.toDataURL(t,n).match(/data:(.*?);base64,(.*)/);if(r){const e=u(\"Blob\"),t=r[1],n=r[2];return Promise.resolve(new e([o(n)],{type:t}))}return Promise.resolve(null)}))}function p(e,t,n){return n.has(t)||e.prototype.isPrototypeOf(t)}function y(e,t,n){const r=Object.assign({},e()),i=()=>{Object.assign(r,e())};return t(i),{windowInfo:r,stopWatch(){n(i)}}}const E=(()=>{const e=u(\"EventTarget\"),t=new WeakSet;try{const n=e[Symbol.hasInstance];e[Symbol.hasInstance]=r=>p(e,r,t)||(null==n?void 0:n(r))}catch(e){}return function(n){const r=new e,i=new WeakMap;return Object.defineProperties(n,{addEventListener:{value:function(){if(\"function\"==typeof arguments[1]){const e=arguments[1].bind(n);i.set(arguments[1],e),arguments[1]=e}r.addEventListener.call(r,arguments[0],arguments[1],arguments[2])},writable:!1},removeEventListener:{value:function(){\"function\"==typeof arguments[1]&&i.has(arguments[1])&&(arguments[1]=i.get(arguments[1])),r.removeEventListener.call(r,arguments[0],arguments[1],arguments[2])},writable:!1},dispatchEvent:{value:r.dispatchEvent.bind(r),writable:!1}}),t.add(n),n}})(),O=new WeakSet;class P{static[Symbol.hasInstance](e){return p(this,e,O)}constructor(){throw new TypeError(\"Illegal constructor\")}}function j(e){O.add(e)}class x{get top(){return this.y}get left(){return this.x}get right(){return this.x+this.width}get bottom(){return this.y+this.height}constructor(e=0,t=0,n=0,r=0){Object.defineProperty(this,\"x\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"y\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"width\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"height\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.x=e,this.y=t,this.width=n,this.height=r}toJSON(){return JSON.parse(JSON.stringify(this))}static fromRect(e){return new x(null==e?void 0:e.x,null==e?void 0:e.y,null==e?void 0:e.width,null==e?void 0:e.height)}}class A{constructor(){Object.defineProperty(this,\"document\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"window\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"defaultCanvas\",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}init(){}patchGlobal(e){const t=this.getWindowInfo(),n={HTMLElement:this.HTMLElement,OffscreenCanvas:this.OffscreenCanvas,HTMLCanvasElement:this.HTMLCanvasElement,HTMLImageElement:this.HTMLImageElement,Image:this.Image,AudioContext:this.AudioContext,ImageData:this.ImageData,WebGL2RenderingContext:this.WebGL2RenderingContext,DOMRect:this.DOMRect,get innerWidth(){return t.innerWidth},get innerHeight(){return t.innerHeight},get devicePixelRatio(){return t.devicePixelRatio},requestAnimationFrame:this.requestAnimationFrame,cancelAnimationFrame:this.cancelAnimationFrame,atob:u(\"atob\"),performance:this.getPerformance(),pageXOffset:0,pageYOffset:0,getComputedStyle:()=>({}),screen:E({get availHeight(){return t.innerHeight},get availWidth(){return t.innerWidth},get height(){return t.innerHeight},get width(){return t.innerWidth},colorDepth:24,pixelDepth:24})};Object.assign(e,n),E(e);try{e.window=e}catch(e){}this.window=e;try{const n=this.document||{};this.document=n,e.document=n,E(this.document);const r=this.createElement.bind(this);Object.defineProperties(this.document,{createElement:{get:()=>r},createElementNS:{get:()=>(e,t)=>r(t)},body:{value:E({})},documentElement:{value:{clientLeft:0,clientTop:0,get clientWidth(){return t.innerWidth},get clientHeight(){return t.innerHeight}}}})}catch(t){if(e.document){this.document=e.document;const t=this.document.createElement||this.createElement.bind(this);Object.defineProperties(this.document,{createElement:{get:()=>t},createElementNS:{get:()=>(e,n)=>t.call(this.document,n)}})}else console.error(\"patchGlobal fail, error:\",t)}return e}createElement(e){switch(e){case\"canvas\":return new this.OffscreenCanvas(1,1);case\"img\":return new this.Image;default:return console.warn(`createElement(\"${e}\") is not implemented in mini-program`),{}}}get WebGL2RenderingContext(){return P}get DOMRect(){return x}}class T extends A{constructor(){super(...arguments),Object.defineProperty(this,\"window\",{enumerable:!0,configurable:!0,writable:!0,value:window}),Object.defineProperty(this,\"document\",{enumerable:!0,configurable:!0,writable:!0,value:document}),Object.defineProperty(this,\"patchElement\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"patchCanvas\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"createElement\",{enumerable:!0,configurable:!0,writable:!0,value:document.createElement.bind(document)}),Object.defineProperty(this,\"HTMLElement\",{enumerable:!0,configurable:!0,writable:!0,value:HTMLElement}),Object.defineProperty(this,\"HTMLCanvasElement\",{enumerable:!0,configurable:!0,writable:!0,value:HTMLCanvasElement}),Object.defineProperty(this,\"OffscreenCanvas\",{enumerable:!0,configurable:!0,writable:!0,value:OffscreenCanvas}),Object.defineProperty(this,\"HTMLImageElement\",{enumerable:!0,configurable:!0,writable:!0,value:HTMLImageElement}),Object.defineProperty(this,\"Image\",{enumerable:!0,configurable:!0,writable:!0,value:Image}),Object.defineProperty(this,\"ImageData\",{enumerable:!0,configurable:!0,writable:!0,value:ImageData}),Object.defineProperty(this,\"AudioContext\",{enumerable:!0,configurable:!0,writable:!0,value:AudioContext}),Object.defineProperty(this,\"requestAnimationFrame\",{enumerable:!0,configurable:!0,writable:!0,value:requestAnimationFrame}),Object.defineProperty(this,\"cancelAnimationFrame\",{enumerable:!0,configurable:!0,writable:!0,value:cancelAnimationFrame})}patchGlobal(e){return window}getPerformance(){return performance}useCanvas(e){const t=(()=>{const t=document.querySelector(e);return t&&t instanceof HTMLCanvasElement?t:document.querySelector(`${e} canvas`)})();if(!t)throw new Error(\"invalid selector\");const n=t.getBoundingClientRect();return t.width!==n.width&&(t.width=n.width),t.height!==n.height&&(t.height=n.height),Promise.resolve({canvas:t,requestAnimationFrame:requestAnimationFrame,cancelAnimationFrame:cancelAnimationFrame,useFrame:e=>{let t,n;const r=i=>{if(void 0===t)t=i||performance.now();else{const n=i||performance.now(),r=n-t;t=n,e(r)}n=requestAnimationFrame(r)};return n=requestAnimationFrame(r),{cancel:()=>cancelAnimationFrame(n)}},recomputeSize:()=>Promise.resolve(),eventHandler(e,t=!0,n=!1){}})}useElement(e){return Promise.resolve({element:document.querySelector(e),recomputeSize:()=>Promise.resolve(),eventHandler(e,t=!0){}})}getWindowInfo(){return{devicePixelRatio:window.devicePixelRatio,innerHeight:window.innerHeight,innerWidth:window.innerWidth}}}const H=globalThis.window&&\"HTMLElement\"in globalThis.window,C=Symbol(\"PatchedSymbol\"),L=new Proxy({currentAdapter:void 0,useAdapter(e){var t;return this.currentAdapter=e,null===(t=e.init)||void 0===t||t.call(e),this},patch(e){var t;const n=this.getExistAdapter();if(\"string\"==typeof e&&(H&&(globalThis[e]=globalThis),!(e=globalThis[e])))throw new Error(`invalid patch target: ${e}`);if(e[C])throw new Error(`cannot patch ${e} twice`);return null===(t=n.patchGlobal)||void 0===t||t.call(n,e),e[C]=!0,this},getExistAdapter(){if(!this.currentAdapter){if(H)return new T;throw new Error(\"Must call use() to set a platform adapter.\")}return this.currentAdapter}},{get(e,t,n){if(Reflect.has(e,t))return Reflect.get(e,t,n);{const n=e.getExistAdapter(),r=Reflect.get(n,t);return\"function\"!=typeof r||r.toString().startsWith(\"class\")?r:r.bind(n)}}}),R=L;H&&L.patch(\"__THREEGlobals__\");export{A as B,h as _,L as a,s as b,y as c,d,b as e,v as f,R as g,j as h,w as i,g as n,E as p,p as s,u};\n"], "names": ["a", "i", "r", "n", "t", "e", "l", "c", "o"], "mappings": ";;;AAAyJ,MAAM,IAAE,EAAC,MAAKA,4DAAAA,IAAE,OAAMC,mCAAAA,OAAE,aAAYC,mCAAAA,aAAE,cAAaC,+DAAE,cAAaC,4DAAAA,GAAE,MAAKC,8DAAC,GAAE,IAAE,OAAO,OAAO,IAAG,CAAC;AAAmC,SAAS,EAAE,GAAE;AAAC,MAAG,EAAE,eAAe,CAAC,EAAE,QAAO,EAAE,CAAC;AAAE,QAAM,IAAI,MAAM,0BAA0B,CAAC,kCAAkC,CAAC,IAAI;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE,GAAE,GAAE;AAAC,SAAO,KAAI,MAAI,IAAE,UAAW,SAAS,GAAE,GAAE;AAAC,aAAS,EAAEA,IAAE;AAAC,UAAG;AAAC,QAAAC,GAAE,EAAE,KAAKD,EAAC,CAAC;AAAA,MAAC,SAAOA,IAAE;AAAC,UAAEA,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,aAASE,GAAEF,IAAE;AAAC,UAAG;AAAC,QAAAC,GAAE,EAAE,MAAMD,EAAC,CAAC;AAAA,MAAC,SAAOA,IAAE;AAAC,UAAEA,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,aAASC,GAAED,IAAE;AAAC,UAAID;AAAE,MAAAC,GAAE,OAAK,EAAEA,GAAE,KAAK,KAAGD,KAAEC,GAAE,OAAMD,cAAa,IAAEA,KAAE,IAAI,EAAG,SAASC,IAAE;AAAC,QAAAA,GAAED,EAAC;AAAA,MAAC,CAAC,GAAI,KAAK,GAAEG,EAAC;AAAA,IAAC;AAAC,IAAAD,IAAG,IAAE,EAAE,MAAM,GAAE,KAAG,EAAE,GAAG,KAAI,CAAE;AAAA,EAAC,CAAC;AAAE;AAAC,SAAS,EAAE,GAAE,GAAE,GAAE,GAAE;AAAqF,MAAG,cAAY,OAAO,IAAE,MAAI,KAAG,CAAC,IAAE,CAAC,EAAE,IAAI,CAAC,EAAE,OAAM,IAAI,UAAU,0EAA0E;AAAE,SAAM,QAAM,IAAE,IAAE,QAAM,IAAE,EAAE,KAAK,CAAC,IAAE,IAAE,EAAE,QAAM,EAAE,IAAI,CAAC;AAAC;AAAC,cAAY,OAAO,mBAAiB;AAAqB,MAAC,IAAE,CAAA,GAAG,IAAE,EAAC,OAAM,OAAG,0BAAyB,KAAI,GAAE,IAAE,IAAI,MAAM,EAAC,IAAI,GAAE,GAAE,IAAE,OAAG;AAAC,OAAG,OAAO,OAAO,GAAE,CAAC,KAAG,QAAQ,KAAK,6BAA6B,CAAC,2CAA2C,GAAE,EAAE,CAAC,IAAE,MAAI,OAAO,OAAO,GAAE,CAAC,MAAI,QAAQ,KAAK,6BAA6B,CAAC,sCAAsC,GAAE,OAAO,EAAE,CAAC,IAAG,EAAE,CAAC,IAAE;AAAE,EAAC,GAAE,EAAC,IAAI,GAAE,GAAE,GAAE;AAAC,MAAG,QAAQ,IAAI,GAAE,CAAC,EAAE,QAAO,QAAQ,IAAI,GAAE,GAAE,CAAC;AAAE,MAAG,QAAQ,IAAI,GAAE,CAAC,GAAE;AAAC,QAAG,OAAO,OAAO,GAAE,CAAC,GAAE;AAAC,YAAMD,KAAE,EAAE,CAAC;AAAE,aAAO,OAAO,EAAE,CAAC,GAAEA;AAAA,IAAC;AAAC,WAAO,QAAQ,IAAI,GAAE,GAAE,CAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAE,SAAS,IAAG;AAAC;AAAC,SAAS,KAAK,GAAE;AAAC,IAAE,SAAO,QAAQ,IAAI,GAAG,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,EAAE,MAAK,WAAU,QAAQ,WAAUA,IAAE,IAAE,aAAY,IAAE,MAAI;AAAC,UAAM,IAAEA,GAAE,UAAU,GAAE,CAAC,EAAE,MAAM,wBAAwB;AAAE,QAAG,GAAE;AAAC,YAAMA,KAAE,EAAE,MAAM,GAAED,KAAE,EAAE,CAAC,GAAED,KAAE,EAAE,CAAC;AAAE,aAAO,QAAQ,QAAQ,IAAIE,GAAE,CAACG,4DAAAA,EAAEL,EAAC,CAAC,GAAE,EAAC,MAAKC,GAAC,CAAC,CAAC;AAAA,IAAC;AAAC,WAAO,QAAQ,QAAQ,IAAI;AAAA,EAAC;AAAG;AAAC,SAAS,EAAE,GAAE,GAAE,GAAE;AAAC,SAAO,EAAE,IAAI,CAAC,KAAG,EAAE,UAAU,cAAc,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE,GAAE;AAAC,QAAM,IAAE,OAAO,OAAO,CAAA,GAAG,EAAC,CAAE,GAAE,IAAE,MAAI;AAAC,WAAO,OAAO,GAAE,EAAC,CAAE;AAAA,EAAC;AAAE,SAAO,EAAE,CAAC,GAAE,EAAC,YAAW,GAAE,YAAW;AAAC,MAAE,CAAC;AAAA,EAAC,EAAC;AAAC;AAAM,MAAC,KAAG,MAAI;AAAC,QAAM,IAAE,EAAE,aAAa,GAAE,IAAE,oBAAI;AAAQ,MAAG;AAAC,UAAM,IAAE,EAAE,OAAO,WAAW;AAAE,MAAE,OAAO,WAAW,IAAE,OAAG,EAAE,GAAE,GAAE,CAAC,MAAI,QAAM,IAAE,SAAO,EAAE,CAAC;AAAA,EAAE,SAAOC,IAAE;AAAA,EAAC;AAAC,SAAO,SAAS,GAAE;AAAC,UAAM,IAAE,IAAI,KAAE,IAAE,oBAAI;AAAQ,WAAO,OAAO,iBAAiB,GAAE,EAAC,kBAAiB,EAAC,OAAM,WAAU;AAAC,UAAG,cAAY,OAAO,UAAU,CAAC,GAAE;AAAC,cAAMA,KAAE,UAAU,CAAC,EAAE,KAAK,CAAC;AAAE,UAAE,IAAI,UAAU,CAAC,GAAEA,EAAC,GAAE,UAAU,CAAC,IAAEA;AAAA,MAAC;AAAC,QAAE,iBAAiB,KAAK,GAAE,UAAU,CAAC,GAAE,UAAU,CAAC,GAAE,UAAU,CAAC,CAAC;AAAA,IAAC,GAAE,UAAS,MAAE,GAAE,qBAAoB,EAAC,OAAM,WAAU;AAAC,oBAAY,OAAO,UAAU,CAAC,KAAG,EAAE,IAAI,UAAU,CAAC,CAAC,MAAI,UAAU,CAAC,IAAE,EAAE,IAAI,UAAU,CAAC,CAAC,IAAG,EAAE,oBAAoB,KAAK,GAAE,UAAU,CAAC,GAAE,UAAU,CAAC,GAAE,UAAU,CAAC,CAAC;AAAA,IAAC,GAAE,UAAS,MAAE,GAAE,eAAc,EAAC,OAAM,EAAE,cAAc,KAAK,CAAC,GAAE,UAAS,MAAE,EAAC,CAAC,GAAE,EAAE,IAAI,CAAC,GAAE;AAAA,EAAC;AAAC,MAAK,IAAE,oBAAI;AAAQ,MAAM,EAAC;AAAA,EAAC,QAAO,OAAO,WAAW,EAAE,GAAE;AAAC,WAAO,EAAE,MAAK,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,IAAI,UAAU,qBAAqB;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,IAAE,IAAI,CAAC;AAAC;AAAC,MAAM,EAAC;AAAA,EAAC,IAAI,MAAK;AAAC,WAAO,KAAK;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,KAAK,IAAE,KAAK;AAAA,EAAK;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK,IAAE,KAAK;AAAA,EAAM;AAAA,EAAC,YAAY,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE;AAAC,WAAO,eAAe,MAAK,KAAI,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,KAAI,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,SAAQ,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,UAAS,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,KAAK,IAAE,GAAE,KAAK,IAAE,GAAE,KAAK,QAAM,GAAE,KAAK,SAAO;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,WAAO,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC;AAAA,EAAC;AAAA,EAAC,OAAO,SAAS,GAAE;AAAC,WAAO,IAAI,EAAE,QAAM,IAAE,SAAO,EAAE,GAAE,QAAM,IAAE,SAAO,EAAE,GAAE,QAAM,IAAE,SAAO,EAAE,OAAM,QAAM,IAAE,SAAO,EAAE,MAAM;AAAA,EAAC;AAAC;AAAC,MAAM,EAAC;AAAA,EAAC,cAAa;AAAC,WAAO,eAAe,MAAK,YAAW,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,UAAS,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,iBAAgB,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAA,EAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,IAAE,KAAK,iBAAgB,IAAE,EAAC,aAAY,KAAK,aAAY,iBAAgB,KAAK,iBAAgB,mBAAkB,KAAK,mBAAkB,kBAAiB,KAAK,kBAAiB,OAAM,KAAK,OAAM,cAAa,KAAK,cAAa,WAAU,KAAK,WAAU,wBAAuB,KAAK,wBAAuB,SAAQ,KAAK,SAAQ,IAAI,aAAY;AAAC,aAAO,EAAE;AAAA,IAAU,GAAE,IAAI,cAAa;AAAC,aAAO,EAAE;AAAA,IAAW,GAAE,IAAI,mBAAkB;AAAC,aAAO,EAAE;AAAA,IAAgB,GAAE,uBAAsB,KAAK,uBAAsB,sBAAqB,KAAK,sBAAqB,MAAK,EAAE,MAAM,GAAE,aAAY,KAAK,kBAAiB,aAAY,GAAE,aAAY,GAAE,kBAAiB,OAAK,CAAA,IAAI,QAAO,EAAE,EAAC,IAAI,cAAa;AAAC,aAAO,EAAE;AAAA,IAAW,GAAE,IAAI,aAAY;AAAC,aAAO,EAAE;AAAA,IAAU,GAAE,IAAI,SAAQ;AAAC,aAAO,EAAE;AAAA,IAAW,GAAE,IAAI,QAAO;AAAC,aAAO,EAAE;AAAA,IAAU,GAAE,YAAW,IAAG,YAAW,GAAE,CAAC,EAAC;AAAE,WAAO,OAAO,GAAE,CAAC,GAAE,EAAE,CAAC;AAAE,QAAG;AAAC,QAAE,SAAO;AAAA,IAAC,SAAOA,IAAE;AAAA,IAAC;AAAC,SAAK,SAAO;AAAE,QAAG;AAAC,YAAMF,KAAE,KAAK,YAAU;AAAG,WAAK,WAASA,IAAE,EAAE,WAASA,IAAE,EAAE,KAAK,QAAQ;AAAE,YAAM,IAAE,KAAK,cAAc,KAAK,IAAI;AAAE,aAAO,iBAAiB,KAAK,UAAS,EAAC,eAAc,EAAC,KAAI,MAAI,EAAC,GAAE,iBAAgB,EAAC,KAAI,MAAI,CAACE,IAAED,OAAI,EAAEA,EAAC,EAAC,GAAE,MAAK,EAAC,OAAM,EAAE,CAAA,CAAE,EAAC,GAAE,iBAAgB,EAAC,OAAM,EAAC,YAAW,GAAE,WAAU,GAAE,IAAI,cAAa;AAAC,eAAO,EAAE;AAAA,MAAU,GAAE,IAAI,eAAc;AAAC,eAAO,EAAE;AAAA,MAAW,EAAC,EAAC,EAAC,CAAC;AAAA,IAAC,SAAOA,IAAE;AAAC,UAAG,EAAE,UAAS;AAAC,aAAK,WAAS,EAAE;AAAS,cAAMA,KAAE,KAAK,SAAS,iBAAe,KAAK,cAAc,KAAK,IAAI;AAAE,eAAO,iBAAiB,KAAK,UAAS,EAAC,eAAc,EAAC,KAAI,MAAIA,GAAC,GAAE,iBAAgB,EAAC,KAAI,MAAI,CAACC,IAAEF,OAAIC,GAAE,KAAK,KAAK,UAASD,EAAC,EAAC,EAAC,CAAC;AAAA,MAAC,MAAM,SAAQ,MAAM,4BAA2BC,EAAC;AAAA,IAAC;AAAC,WAAO;AAAA,EAAC;AAAA,EAAC,cAAc,GAAE;AAAC,YAAO;MAAG,KAAI;AAAS,eAAO,IAAI,KAAK,gBAAgB,GAAE,CAAC;AAAA,MAAE,KAAI;AAAM,eAAO,IAAI,KAAK;AAAA,MAAM;AAAQ,eAAO,QAAQ,KAAK,kBAAkB,CAAC,uCAAuC,GAAE,CAAA;AAAA,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,yBAAwB;AAAC,WAAO;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO;AAAA,EAAC;AAAC;AAAC,MAAM,UAAU,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,OAAO,eAAe,MAAK,UAAS,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,YAAW,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,SAAQ,CAAC,GAAE,OAAO,eAAe,MAAK,gBAAe,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,eAAc,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,iBAAgB,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,SAAS,cAAc,KAAK,QAAQ,EAAC,CAAC,GAAE,OAAO,eAAe,MAAK,eAAc,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,YAAW,CAAC,GAAE,OAAO,eAAe,MAAK,qBAAoB,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,kBAAiB,CAAC,GAAE,OAAO,eAAe,MAAK,mBAAkB,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,gBAAe,CAAC,GAAE,OAAO,eAAe,MAAK,oBAAmB,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,iBAAgB,CAAC,GAAE,OAAO,eAAe,MAAK,SAAQ,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,MAAK,CAAC,GAAE,OAAO,eAAe,MAAK,aAAY,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,UAAS,CAAC,GAAE,OAAO,eAAe,MAAK,gBAAe,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,aAAY,CAAC,GAAE,OAAO,eAAe,MAAK,yBAAwB,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,sBAAqB,CAAC,GAAE,OAAO,eAAe,MAAK,wBAAuB,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,qBAAoB,CAAC;AAAA,EAAC;AAAA,EAAC,YAAY,GAAE;AAAC,WAAO;AAAA,EAAM;AAAA,EAAC,iBAAgB;AAAC,WAAO;AAAA,EAAW;AAAA,EAAC,UAAU,GAAE;AAAC,UAAM,KAAG,MAAI;AAAC,YAAMA,KAAE,SAAS,cAAc,CAAC;AAAE,aAAOA,MAAGA,cAAa,oBAAkBA,KAAE,SAAS,cAAc,GAAG,CAAC,SAAS;AAAA,IAAC,GAAC;AAAI,QAAG,CAAC,EAAE,OAAM,IAAI,MAAM,kBAAkB;AAAE,UAAM,IAAE,EAAE,sBAAqB;AAAG,WAAO,EAAE,UAAQ,EAAE,UAAQ,EAAE,QAAM,EAAE,QAAO,EAAE,WAAS,EAAE,WAAS,EAAE,SAAO,EAAE,SAAQ,QAAQ,QAAQ,EAAC,QAAO,GAAE,uBAA4C,sBAA0C,UAAS,CAAAC,OAAG;AAAC,UAAID,IAAED;AAAE,YAAM,IAAE,OAAG;AAAC,YAAG,WAASC,GAAE,CAAAA,KAAE,KAAG,YAAY;aAAU;AAAC,gBAAMD,KAAE,KAAG,YAAY,IAAG,GAAGD,KAAEC,KAAEC;AAAE,UAAAA,KAAED,IAAEE,GAAEH,EAAC;AAAA,QAAC;AAAC,QAAAC,KAAE,sBAAsB,CAAC;AAAA,MAAC;AAAE,aAAOA,KAAE,sBAAsB,CAAC,GAAE,EAAC,QAAO,MAAI,qBAAqBA,EAAC,EAAC;AAAA,IAAC,GAAE,eAAc,MAAI,QAAQ,QAAO,GAAG,aAAaE,IAAED,KAAE,MAAGD,KAAE,OAAG;AAAA,IAAC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,WAAW,GAAE;AAAC,WAAO,QAAQ,QAAQ,EAAC,SAAQ,SAAS,cAAc,CAAC,GAAE,eAAc,MAAI,QAAQ,QAAO,GAAG,aAAaE,IAAE,IAAE,MAAG;AAAA,IAAC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,gBAAe;AAAC,WAAM,EAAC,kBAAiB,OAAO,kBAAiB,aAAY,OAAO,aAAY,YAAW,OAAO,WAAU;AAAA,EAAC;AAAC;AAAM,MAAC,IAAE,WAAW,UAAQ,iBAAgB,WAAW,QAAO,IAAE,OAAO,eAAe,GAAE,IAAE,IAAI,MAAM,EAAC,gBAAe,QAAO,WAAW,GAAE;AAAC,MAAI;AAAE,SAAO,KAAK,iBAAe,GAAE,UAAQ,IAAE,EAAE,SAAO,WAAS,KAAG,EAAE,KAAK,CAAC,GAAE;AAAI,GAAE,MAAM,GAAE;AAAC,MAAI;AAAE,QAAM,IAAE,KAAK,gBAAe;AAAG,MAAG,YAAU,OAAO,MAAI,MAAI,WAAW,CAAC,IAAE,aAAY,EAAE,IAAE,WAAW,CAAC,IAAI,OAAM,IAAI,MAAM,yBAAyB,CAAC,EAAE;AAAE,MAAG,EAAE,CAAC,EAAE,OAAM,IAAI,MAAM,gBAAgB,CAAC,QAAQ;AAAE,SAAO,UAAQ,IAAE,EAAE,gBAAc,WAAS,KAAG,EAAE,KAAK,GAAE,CAAC,GAAE,EAAE,CAAC,IAAE,MAAG;AAAI,GAAE,kBAAiB;AAAC,MAAG,CAAC,KAAK,gBAAe;AAAC,QAAG,EAAE,QAAO,IAAI;AAAE,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAAC;AAAC,SAAO,KAAK;AAAc,EAAC,GAAE,EAAC,IAAI,GAAE,GAAE,GAAE;AAAC,MAAG,QAAQ,IAAI,GAAE,CAAC,EAAE,QAAO,QAAQ,IAAI,GAAE,GAAE,CAAC;AAAE;AAAC,UAAMF,KAAE,EAAE,gBAAe,GAAG,IAAE,QAAQ,IAAIA,IAAE,CAAC;AAAE,WAAM,cAAY,OAAO,KAAG,EAAE,WAAW,WAAW,OAAO,IAAE,IAAE,EAAE,KAAKA,EAAC;AAAA,EAAC;AAAC,EAAC,CAAC;AAAM,KAAG,EAAE,MAAM,cAAkB;;;;;;;;;;;;;;", "x_google_ignoreList": [0]}