"use strict";
class WorkerPool {
  constructor(e = 1) {
    this.pool = e, this.queue = [], this.workers = [], this.workersResolve = [], this.workerStatus = 0;
  }
  _initWorker(e) {
    if (!this.workers[e]) {
      const s = this.workerCreator();
      s.addEventListener("message", this._onMessage.bind(this, e)), this.workers[e] = s;
    }
  }
  _getIdleWorker() {
    for (let e = 0; e < this.pool; e++) if (!(this.workerStatus & 1 << e)) return e;
    return -1;
  }
  _onMessage(e, s) {
    const r = this.workersResolve[e];
    if (r && r(s), this.queue.length) {
      const { resolve: s2, msg: r2, transfer: t } = this.queue.shift();
      this.workersResolve[e] = s2, this.workers[e].postMessage(r2, t);
    } else this.workerStatus ^= 1 << e;
  }
  setWorkerCreator(e) {
    this.workerCreator = e;
  }
  setWorkerLimit(e) {
    this.pool = e;
  }
  postMessage(e, s) {
    return new Promise((r) => {
      const t = this._getIdleWorker();
      -1 !== t ? (this._initWorker(t), this.workerStatus |= 1 << t, this.workersResolve[t] = r, this.workers[t].postMessage(e, s)) : this.queue.push({ resolve: r, msg: e, transfer: s });
    });
  }
  dispose() {
    this.workers.forEach((e) => e.terminate()), this.workersResolve.length = 0, this.workers.length = 0, this.queue.length = 0, this.workerStatus = 0;
  }
}
exports.WorkerPool = WorkerPool;
//# sourceMappingURL=../../../../../../../../.sourcemap/mp-weixin/_mpChunkDeps/@minisheep/three-platform-adapter/dist/three-override/jsm/utils/WorkerPool.js.map
