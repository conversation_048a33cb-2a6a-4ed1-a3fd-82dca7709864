{"version": 3, "file": "KTX2Loader.js", "sources": ["../node_modules/@minisheep/three-platform-adapter/dist/three-override/jsm/loaders/KTX2Loader.js"], "sourcesContent": ["import{RGBA_S3TC_DXT1_Format as e,RGB_PVRTC_4BPPV1_Format as t,RGB_ETC2_Format as r,RGB_ETC1_Format as s,RGBA_S3TC_DXT5_Format as o,RGBA_PVRTC_4BPPV1_Format as a,RGBA_ETC2_EAC_Format as i,RGBA_BPTC_Format as n,RGBA_ASTC_4x4_Format as p,RGBAFormat as c,Lo<PERSON> as u,FileLoader as d,CompressedCubeTexture as h,UnsignedByteType as l,CompressedArrayTexture as m,CompressedTexture as _,LinearFilter as w,LinearMipmapLinearFilter as T,SRGBColorSpace as x,LinearSRGBColorSpace as f,NoColorSpace as E,RGBA_ASTC_6x6_Format as B,RedFormat as R,RGFormat as S,HalfFloatType as C,FloatType as y,DataTexture as F,Data3DTexture as A}from\"three\";import{WorkerPool as P}from\"../utils/WorkerPool.js\";import{KHR_DF_FLAG_ALPHA_PREMULTIPLIED as g,read as L,VK_FORMAT_UNDEFINED as k,KHR_DF_PRIMARIES_BT709 as G,KHR_DF_TRANSFER_SRGB as b,KHR_DF_PRIMARIES_DISPLAYP3 as v,KHR_DF_PRIMARIES_UNSPECIFIED as W,KHR_SUPERCOMPRESSION_ZSTD as U,KHR_SUPERCOMPRESSION_NONE as X,VK_FORMAT_ASTC_6x6_UNORM_BLOCK as K,VK_FORMAT_ASTC_6x6_SRGB_BLOCK as M,VK_FORMAT_R8_UNORM as D,VK_FORMAT_R8_SRGB as H,VK_FORMAT_R16_SFLOAT as j,VK_FORMAT_R32_SFLOAT as O,VK_FORMAT_R8G8_SRGB as V,VK_FORMAT_R8G8_UNORM as N,VK_FORMAT_R16G16_SFLOAT as z,VK_FORMAT_R32G32_SFLOAT as Y,VK_FORMAT_R8G8B8A8_SRGB as I,VK_FORMAT_R8G8B8A8_UNORM as Q,VK_FORMAT_R16G16B16A16_SFLOAT as $,VK_FORMAT_R32G32B32A32_SFLOAT as q}from\"three/examples/jsm/libs/ktx-parse.module.js\";import{ZSTDDecoder as J}from\"three/examples/jsm/libs/zstddec.module.js\";import{LocalAsset as Z}from\"../utils/LocalAsset.js\";const ee=function(){const ee=new WeakMap;let te,re=0;class KTX2Loader extends u{constructor(e){super(e),this.transcoderPath=\"\",this.transcoderBinary=null,this.transcoderPending=null,this.workerPool=new P,this.workerSourceURL=\"\",this.workerConfig=null,\"undefined\"!=typeof MSC_TRANSCODER&&console.warn('THREE.KTX2Loader: Please update to latest \"basis_transcoder\". \"msc_basis_transcoder\" is no longer supported in three.js r125+.')}setTranscoderPath(e){return this.transcoderPath=e,this}setWorkerLimit(e){return this.workerPool.setWorkerLimit(e),this}async detectSupportAsync(e){return this.workerConfig={astcSupported:await e.hasFeatureAsync(\"texture-compression-astc\"),etc1Supported:await e.hasFeatureAsync(\"texture-compression-etc1\"),etc2Supported:await e.hasFeatureAsync(\"texture-compression-etc2\"),dxtSupported:await e.hasFeatureAsync(\"texture-compression-bc\"),bptcSupported:await e.hasFeatureAsync(\"texture-compression-bptc\"),pvrtcSupported:await e.hasFeatureAsync(\"texture-compression-pvrtc\")},this}detectSupport(e){return!0===e.isWebGPURenderer?this.workerConfig={astcSupported:e.hasFeature(\"texture-compression-astc\"),etc1Supported:e.hasFeature(\"texture-compression-etc1\"),etc2Supported:e.hasFeature(\"texture-compression-etc2\"),dxtSupported:e.hasFeature(\"texture-compression-bc\"),bptcSupported:e.hasFeature(\"texture-compression-bptc\"),pvrtcSupported:e.hasFeature(\"texture-compression-pvrtc\")}:this.workerConfig={astcSupported:e.extensions.has(\"WEBGL_compressed_texture_astc\"),etc1Supported:e.extensions.has(\"WEBGL_compressed_texture_etc1\"),etc2Supported:e.extensions.has(\"WEBGL_compressed_texture_etc\"),dxtSupported:e.extensions.has(\"WEBGL_compressed_texture_s3tc\"),bptcSupported:e.extensions.has(\"EXT_texture_compression_bptc\"),pvrtcSupported:e.extensions.has(\"WEBGL_compressed_texture_pvrtc\")||e.extensions.has(\"WEBKIT_WEBGL_compressed_texture_pvrtc\")},this}init(){return this.transcoderPending||(this.transcoderPending=Promise.resolve().then((()=>{this.transcoderBinary=new ArrayBuffer(1),this.workerPool.setWorkerCreator((()=>{const e=new Worker(Z.resolve(\"worker\",\"basis/basis_transcoder.js\")),t=this.transcoderBinary.slice(0);return e.postMessage({type:\"init\",config:this.workerConfig,transcoderBinary:t},[t]),e}))})),re>0&&console.warn(\"THREE.KTX2Loader: Multiple active KTX2 loaders may cause performance issues. Use a single KTX2Loader instance, or call .dispose() on old instances.\"),re++),this.transcoderPending}load(e,t,r,s){if(null===this.workerConfig)throw new Error(\"THREE.KTX2Loader: Missing initialization with `.detectSupport( renderer )`.\");const o=new d(this.manager);o.setResponseType(\"arraybuffer\"),o.setWithCredentials(this.withCredentials),o.load(e,(e=>{this.parse(e,t,s)}),r,s)}parse(e,t,r){if(null===this.workerConfig)throw new Error(\"THREE.KTX2Loader: Missing initialization with `.detectSupport( renderer )`.\");if(ee.has(e)){return ee.get(e).promise.then(t).catch(r)}this._createTexture(e).then((e=>t?t(e):null)).catch(r)}_createTextureFrom(e,t){const{faces:r,width:s,height:o,format:a,type:i,error:n,dfdFlags:p}=e;if(\"error\"===i)return Promise.reject(n);let c;if(6===t.faceCount)c=new h(r,a,l);else{const e=r[0].mipmaps;c=t.layerCount>1?new m(e,s,o,t.layerCount,a,l):new _(e,s,o,a,l)}return c.minFilter=1===r[0].mipmaps.length?w:T,c.magFilter=w,c.generateMipmaps=!1,c.needsUpdate=!0,c.colorSpace=ie(t),c.premultiplyAlpha=!!(p&g),c}async _createTexture(e,t={}){const r=L(new Uint8Array(e));if(r.vkFormat!==k)return async function(e){const{vkFormat:t}=e;if(void 0===oe[t])throw new Error(\"THREE.KTX2Loader: Unsupported vkFormat.\");let r;e.supercompressionScheme===U&&(te||(te=new Promise((async e=>{const t=new J;await t.init(),e(t)}))),r=await te);const s=[];for(let o=0;o<e.levels.length;o++){const a=Math.max(1,e.pixelWidth>>o),i=Math.max(1,e.pixelHeight>>o),n=e.pixelDepth?Math.max(1,e.pixelDepth>>o):0,p=e.levels[o];let c,u;if(e.supercompressionScheme===X)c=p.levelData;else{if(e.supercompressionScheme!==U)throw new Error(\"THREE.KTX2Loader: Unsupported supercompressionScheme.\");c=r.decode(p.levelData,p.uncompressedByteLength)}u=ae[t]===y?new Float32Array(c.buffer,c.byteOffset,c.byteLength/Float32Array.BYTES_PER_ELEMENT):ae[t]===C?new Uint16Array(c.buffer,c.byteOffset,c.byteLength/Uint16Array.BYTES_PER_ELEMENT):c,s.push({data:u,width:a,height:i,depth:n})}let o;if(se.has(oe[t]))o=0===e.pixelDepth?new F(s[0].data,e.pixelWidth,e.pixelHeight):new A(s[0].data,e.pixelWidth,e.pixelHeight,e.pixelDepth);else{if(e.pixelDepth>0)throw new Error(\"THREE.KTX2Loader: Unsupported pixelDepth.\");o=new _(s,e.pixelWidth,e.pixelHeight)}return o.mipmaps=s,o.type=ae[t],o.format=oe[t],o.colorSpace=ie(e),o.needsUpdate=!0,Promise.resolve(o)}(r);const s=t,o=this.init().then((()=>this.workerPool.postMessage({type:\"transcode\",buffer:e,taskConfig:s},[e]))).then((e=>this._createTextureFrom(e.data,r)));return ee.set(e,{promise:o}),o}dispose(){return this.workerPool.dispose(),this.workerSourceURL&&URL.revokeObjectURL(this.workerSourceURL),re--,this}}KTX2Loader.BasisFormat={ETC1S:0,UASTC_4x4:1},KTX2Loader.TranscoderFormat={ETC1:0,ETC2:1,BC1:2,BC3:3,BC4:4,BC5:5,BC7_M6_OPAQUE_ONLY:6,BC7_M5:7,PVRTC1_4_RGB:8,PVRTC1_4_RGBA:9,ASTC_4x4:10,ATC_RGB:11,ATC_RGBA_INTERPOLATED_ALPHA:12,RGBA32:13,RGB565:14,BGR565:15,RGBA4444:16},KTX2Loader.EngineFormat={RGBAFormat:c,RGBA_ASTC_4x4_Format:p,RGBA_BPTC_Format:n,RGBA_ETC2_EAC_Format:i,RGBA_PVRTC_4BPPV1_Format:a,RGBA_S3TC_DXT5_Format:o,RGB_ETC1_Format:s,RGB_ETC2_Format:r,RGB_PVRTC_4BPPV1_Format:t,RGBA_S3TC_DXT1_Format:e};const se=new Set([c,S,R]),oe={[q]:c,[$]:c,[Q]:c,[I]:c,[Y]:S,[z]:S,[N]:S,[V]:S,[O]:R,[j]:R,[H]:R,[D]:R,[M]:B,[K]:B},ae={[q]:y,[$]:C,[Q]:l,[I]:l,[Y]:y,[z]:C,[N]:l,[V]:l,[O]:y,[j]:C,[H]:l,[D]:l,[M]:l,[K]:l};function ie(e){const t=e.dataFormatDescriptor[0];return t.colorPrimaries===G?t.transferFunction===b?x:f:t.colorPrimaries===v?t.transferFunction===b?\"display-p3\":\"display-p3-linear\":(t.colorPrimaries===W||console.warn(`THREE.KTX2Loader: Unsupported color primaries, \"${t.colorPrimaries}\"`),E)}return KTX2Loader}();export{ee as KTX2Loader};\n"], "names": ["ee", "u", "P", "Z", "d", "e", "h", "l", "m", "_", "w", "T", "g", "L", "k", "t", "r", "U", "J", "s", "o", "X", "y", "C", "F", "A", "c", "p", "n", "i", "a", "S", "R", "q", "$", "Q", "I", "Y", "z", "N", "V", "O", "j", "H", "D", "M", "B", "K", "G", "b", "x", "f", "v", "W", "E"], "mappings": ";;;;;;;AAAy/C,MAAC,KAAG,WAAU;AAAC,QAAMA,MAAG,oBAAI;AAAQ,MAAI,IAAG,KAAG;AAAA,EAAE,MAAM,mBAAmBC,wCAAAA,GAAC;AAAA,IAAC,YAAY,GAAE;AAAC,YAAM,CAAC,GAAE,KAAK,iBAAe,IAAG,KAAK,mBAAiB,MAAK,KAAK,oBAAkB,MAAK,KAAK,aAAW,IAAIC,mGAAE,KAAK,kBAAgB,IAAG,KAAK,eAAa,MAAK,eAAa,OAAO,kBAAgB,QAAQ,KAAK,gIAAgI;AAAA,IAAC;AAAA,IAAC,kBAAkB,GAAE;AAAC,aAAO,KAAK,iBAAe,GAAE;AAAA,IAAI;AAAA,IAAC,eAAe,GAAE;AAAC,aAAO,KAAK,WAAW,eAAe,CAAC,GAAE;AAAA,IAAI;AAAA,IAAC,MAAM,mBAAmB,GAAE;AAAC,aAAO,KAAK,eAAa,EAAC,eAAc,MAAM,EAAE,gBAAgB,0BAA0B,GAAE,eAAc,MAAM,EAAE,gBAAgB,0BAA0B,GAAE,eAAc,MAAM,EAAE,gBAAgB,0BAA0B,GAAE,cAAa,MAAM,EAAE,gBAAgB,wBAAwB,GAAE,eAAc,MAAM,EAAE,gBAAgB,0BAA0B,GAAE,gBAAe,MAAM,EAAE,gBAAgB,2BAA2B,EAAC,GAAE;AAAA,IAAI;AAAA,IAAC,cAAc,GAAE;AAAC,aAAM,SAAK,EAAE,mBAAiB,KAAK,eAAa,EAAC,eAAc,EAAE,WAAW,0BAA0B,GAAE,eAAc,EAAE,WAAW,0BAA0B,GAAE,eAAc,EAAE,WAAW,0BAA0B,GAAE,cAAa,EAAE,WAAW,wBAAwB,GAAE,eAAc,EAAE,WAAW,0BAA0B,GAAE,gBAAe,EAAE,WAAW,2BAA2B,EAAC,IAAE,KAAK,eAAa,EAAC,eAAc,EAAE,WAAW,IAAI,+BAA+B,GAAE,eAAc,EAAE,WAAW,IAAI,+BAA+B,GAAE,eAAc,EAAE,WAAW,IAAI,8BAA8B,GAAE,cAAa,EAAE,WAAW,IAAI,+BAA+B,GAAE,eAAc,EAAE,WAAW,IAAI,8BAA8B,GAAE,gBAAe,EAAE,WAAW,IAAI,gCAAgC,KAAG,EAAE,WAAW,IAAI,uCAAuC,EAAC,GAAE;AAAA,IAAI;AAAA,IAAC,OAAM;AAAC,aAAO,KAAK,sBAAoB,KAAK,oBAAkB,QAAQ,QAAO,EAAG,KAAM,MAAI;AAAC,aAAK,mBAAiB,IAAI,YAAY,CAAC,GAAE,KAAK,WAAW,iBAAkB,MAAI;AAAC,gBAAM,IAAE,IAAI,aAAA,QAAA,EAAOC,qFAAAA,WAAE,QAAQ,UAAS,2BAA2B,CAAC,GAAE,IAAE,KAAK,iBAAiB,MAAM,CAAC;AAAE,iBAAO,EAAE,YAAY,EAAC,MAAK,QAAO,QAAO,KAAK,cAAa,kBAAiB,EAAC,GAAE,CAAC,CAAC,CAAC,GAAE;AAAA,QAAC;MAAG,CAAC,GAAG,KAAG,KAAG,QAAQ,KAAK,qJAAqJ,GAAE,OAAM,KAAK;AAAA,IAAiB;AAAA,IAAC,KAAK,GAAE,GAAE,GAAE,GAAE;AAAC,UAAG,SAAO,KAAK,aAAa,OAAM,IAAI,MAAM,6EAA6E;AAAE,YAAM,IAAE,IAAIC,wCAAAA,GAAE,KAAK,OAAO;AAAE,QAAE,gBAAgB,aAAa,GAAE,EAAE,mBAAmB,KAAK,eAAe,GAAE,EAAE,KAAK,GAAG,CAAAC,OAAG;AAAC,aAAK,MAAMA,IAAE,GAAE,CAAC;AAAA,MAAC,GAAG,GAAE,CAAC;AAAA,IAAC;AAAA,IAAC,MAAM,GAAE,GAAE,GAAE;AAAC,UAAG,SAAO,KAAK,aAAa,OAAM,IAAI,MAAM,6EAA6E;AAAE,UAAGL,IAAG,IAAI,CAAC,GAAE;AAAC,eAAOA,IAAG,IAAI,CAAC,EAAE,QAAQ,KAAK,CAAC,EAAE,MAAM,CAAC;AAAA,MAAC;AAAC,WAAK,eAAe,CAAC,EAAE,KAAM,CAAAK,OAAG,IAAE,EAAEA,EAAC,IAAE,IAAI,EAAG,MAAM,CAAC;AAAA,IAAC;AAAA,IAAC,mBAAmB,GAAE,GAAE;AAAC,YAAK,EAAC,OAAM,GAAE,OAAM,GAAE,QAAO,GAAE,QAAO,GAAE,MAAK,GAAE,OAAM,GAAE,UAAS,EAAC,IAAE;AAAE,UAAG,YAAU,EAAE,QAAO,QAAQ,OAAO,CAAC;AAAE,UAAI;AAAE,UAAG,MAAI,EAAE,UAAU,KAAE,IAAIC,wCAAAA,GAAE,GAAE,GAAEC,wCAAAA,EAAC;AAAA,WAAM;AAAC,cAAMF,KAAE,EAAE,CAAC,EAAE;AAAQ,YAAE,EAAE,aAAW,IAAE,IAAIG,wCAAAA,GAAEH,IAAE,GAAE,GAAE,EAAE,YAAW,GAAEE,wCAAAA,EAAC,IAAE,IAAIE,wCAAAA,GAAEJ,IAAE,GAAE,GAAE,GAAEE,wCAAAA,EAAC;AAAA,MAAC;AAAC,aAAO,EAAE,YAAU,MAAI,EAAE,CAAC,EAAE,QAAQ,SAAOG,wCAAAA,KAAEC,wCAAAA,IAAE,EAAE,YAAUD,wCAAAA,IAAE,EAAE,kBAAgB,OAAG,EAAE,cAAY,MAAG,EAAE,aAAW,GAAG,CAAC,GAAE,EAAE,mBAAiB,CAAC,EAAE,IAAEE,qDAAAA,IAAG;AAAA,IAAC;AAAA,IAAC,MAAM,eAAe,GAAE,IAAE,IAAG;AAAC,YAAM,IAAEC,qDAAAA,GAAE,IAAI,WAAW,CAAC,CAAC;AAAE,UAAG,EAAE,aAAWC,wDAAE,QAAO,eAAeT,IAAE;AAAC,cAAK,EAAC,UAASU,GAAC,IAAEV;AAAE,YAAG,WAAS,GAAGU,EAAC,EAAE,OAAM,IAAI,MAAM,yCAAyC;AAAE,YAAIC;AAAE,QAAAX,GAAE,2BAAyBY,qDAAAA,MAAI,OAAK,KAAG,IAAI,QAAS,OAAMZ,OAAG;AAAC,gBAAMU,KAAE,IAAIG;AAAE,gBAAMH,GAAE,KAAI,GAAGV,GAAEU,EAAC;AAAA,QAAC,CAAC,IAAIC,KAAE,MAAM;AAAI,cAAMG,KAAE,CAAA;AAAG,iBAAQC,KAAE,GAAEA,KAAEf,GAAE,OAAO,QAAOe,MAAI;AAAC,gBAAM,IAAE,KAAK,IAAI,GAAEf,GAAE,cAAYe,EAAC,GAAE,IAAE,KAAK,IAAI,GAAEf,GAAE,eAAae,EAAC,GAAE,IAAEf,GAAE,aAAW,KAAK,IAAI,GAAEA,GAAE,cAAYe,EAAC,IAAE,GAAE,IAAEf,GAAE,OAAOe,EAAC;AAAE,cAAI,GAAE;AAAE,cAAGf,GAAE,2BAAyBgB,uDAAE,KAAE,EAAE;AAAA,eAAc;AAAC,gBAAGhB,GAAE,2BAAyBY,qDAAAA,EAAE,OAAM,IAAI,MAAM,uDAAuD;AAAE,gBAAED,GAAE,OAAO,EAAE,WAAU,EAAE,sBAAsB;AAAA,UAAC;AAAC,cAAE,GAAGD,EAAC,MAAIO,wCAAAA,KAAE,IAAI,aAAa,EAAE,QAAO,EAAE,YAAW,EAAE,aAAW,aAAa,iBAAiB,IAAE,GAAGP,EAAC,MAAIQ,wCAAAA,KAAE,IAAI,YAAY,EAAE,QAAO,EAAE,YAAW,EAAE,aAAW,YAAY,iBAAiB,IAAE,GAAEJ,GAAE,KAAK,EAAC,MAAK,GAAE,OAAM,GAAE,QAAO,GAAE,OAAM,EAAC,CAAC;AAAA,QAAC;AAAC,YAAIC;AAAE,YAAG,GAAG,IAAI,GAAGL,EAAC,CAAC,EAAE,CAAAK,KAAE,MAAIf,GAAE,aAAW,IAAImB,wCAAAA,GAAEL,GAAE,CAAC,EAAE,MAAKd,GAAE,YAAWA,GAAE,WAAW,IAAE,IAAIoB,wCAAAA,GAAEN,GAAE,CAAC,EAAE,MAAKd,GAAE,YAAWA,GAAE,aAAYA,GAAE,UAAU;AAAA,aAAM;AAAC,cAAGA,GAAE,aAAW,EAAE,OAAM,IAAI,MAAM,2CAA2C;AAAE,UAAAe,KAAE,IAAIX,wCAAAA,GAAEU,IAAEd,GAAE,YAAWA,GAAE,WAAW;AAAA,QAAC;AAAC,eAAOe,GAAE,UAAQD,IAAEC,GAAE,OAAK,GAAGL,EAAC,GAAEK,GAAE,SAAO,GAAGL,EAAC,GAAEK,GAAE,aAAW,GAAGf,EAAC,GAAEe,GAAE,cAAY,MAAG,QAAQ,QAAQA,EAAC;AAAA,MAAC,EAAE,CAAC;AAAE,YAAM,IAAE,GAAE,IAAE,KAAK,KAAI,EAAG,KAAM,MAAI,KAAK,WAAW,YAAY,EAAC,MAAK,aAAY,QAAO,GAAE,YAAW,EAAC,GAAE,CAAC,CAAC,CAAC,CAAC,EAAG,KAAM,CAAAf,OAAG,KAAK,mBAAmBA,GAAE,MAAK,CAAC;AAAI,aAAOL,IAAG,IAAI,GAAE,EAAC,SAAQ,EAAC,CAAC,GAAE;AAAA,IAAC;AAAA,IAAC,UAAS;AAAC,aAAO,KAAK,WAAW,WAAU,KAAK,mBAAiB,aAAA,KAAA,EAAI,gBAAgB,KAAK,eAAe,GAAE,MAAK;AAAA,IAAI;AAAA,EAAC;AAAC,aAAW,cAAY,EAAC,OAAM,GAAE,WAAU,EAAC,GAAE,WAAW,mBAAiB,EAAC,MAAK,GAAE,MAAK,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,GAAE,KAAI,GAAE,oBAAmB,GAAE,QAAO,GAAE,cAAa,GAAE,eAAc,GAAE,UAAS,IAAG,SAAQ,IAAG,6BAA4B,IAAG,QAAO,IAAG,QAAO,IAAG,QAAO,IAAG,UAAS,GAAE,GAAE,WAAW,eAAa,EAAC,YAAW0B,wCAAAA,IAAE,sBAAqBC,wCAAAA,IAAE,kBAAiBC,4CAAE,sBAAqBC,wCAAAA,IAAE,0BAAyBC,wCAAAA,IAAE,uBAAsBV,wCAAAA,IAAE,iBAAgBD,wCAAAA,IAAE,iBAAgBH,wCAAAA,IAAE,yBAAwBD,4CAAE,uBAAsBV,wCAAAA,GAAC;AAAE,QAAM,KAAG,oBAAI,IAAI,CAACqB,4CAAEK,wCAAAA,IAAEC,wCAAAA,EAAC,CAAC,GAAE,KAAG,EAAC,CAACC,qDAAAA,EAAC,GAAEP,4CAAE,CAACQ,uDAAC,GAAER,wCAAAA,IAAE,CAACS,qDAAAA,EAAC,GAAET,wCAAAA,IAAE,CAACU,uDAAC,GAAEV,wCAAAA,IAAE,CAACW,qDAAAA,EAAC,GAAEN,4CAAE,CAACO,qDAAAA,EAAC,GAAEP,wCAAAA,IAAE,CAACQ,qDAAAA,EAAC,GAAER,4CAAE,CAACS,uDAAC,GAAET,wCAAAA,IAAE,CAACU,qDAAAA,EAAC,GAAET,4CAAE,CAACU,qDAAAA,EAAC,GAAEV,wCAAAA,IAAE,CAACW,qDAAAA,EAAC,GAAEX,wCAAAA,IAAE,CAACY,qDAAAA,EAAC,GAAEZ,wCAAAA,IAAE,CAACa,qDAAAA,EAAC,GAAEC,4CAAE,CAACC,qDAAAA,EAAC,GAAED,wCAAAA,GAAC,GAAE,KAAG,EAAC,CAACb,qDAAAA,EAAC,GAAEX,4CAAE,CAACY,qDAAAA,EAAC,GAAEX,wCAAAA,IAAE,CAACY,qDAAAA,EAAC,GAAE5B,wCAAAA,IAAE,CAAC6B,qDAAAA,EAAC,GAAE7B,wCAAAA,IAAE,CAAC8B,qDAAAA,EAAC,GAAEf,wCAAAA,IAAE,CAACgB,qDAAAA,EAAC,GAAEf,wCAAAA,IAAE,CAACgB,uDAAC,GAAEhC,wCAAAA,IAAE,CAACiC,qDAAAA,EAAC,GAAEjC,4CAAE,CAACkC,uDAAC,GAAEnB,wCAAAA,IAAE,CAACoB,qDAAAA,EAAC,GAAEnB,wCAAAA,IAAE,CAACoB,uDAAC,GAAEpC,wCAAAA,IAAE,CAACqC,qDAAAA,EAAC,GAAErC,4CAAE,CAACsC,qDAAAA,EAAC,GAAEtC,wCAAAA,IAAE,CAACwC,qDAAAA,EAAC,GAAExC,wCAAAA,GAAC;AAAE,WAAS,GAAG,GAAE;AAAC,UAAM,IAAE,EAAE,qBAAqB,CAAC;AAAE,WAAO,EAAE,mBAAiByC,qDAAAA,IAAE,EAAE,qBAAmBC,qDAAAA,IAAEC,wCAAAA,KAAEC,wCAAAA,KAAE,EAAE,mBAAiBC,qDAAAA,IAAE,EAAE,qBAAmBH,qDAAAA,IAAE,eAAa,uBAAqB,EAAE,mBAAiBI,qDAAAA,KAAG,QAAQ,KAAK,mDAAmD,EAAE,cAAc,GAAG,GAAEC;EAAE;AAAC,SAAO;AAAU,EAAC;;", "x_google_ignoreList": [0]}