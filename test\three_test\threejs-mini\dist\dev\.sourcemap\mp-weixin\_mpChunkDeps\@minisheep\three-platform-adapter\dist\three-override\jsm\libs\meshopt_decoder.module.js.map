{"version": 3, "file": "meshopt_decoder.module.js", "sources": ["../node_modules/@minisheep/three-platform-adapter/dist/three-override/jsm/libs/meshopt_decoder.module.js"], "sourcesContent": ["import{LocalAsset as e}from\"../utils/LocalAsset.js\";import{WorkerPool as t}from\"../utils/WorkerPool.js\";const o=function(){if(\"object\"!=typeof WebAssembly)return{supported:!1};const o=e.resolve(\"wasm\",\"meshopt_decoder.wasm\");let r;const s=WebAssembly.instantiate(o,{}).then((function(e){r=e.instance,r.exports.__wasm_call_ctors()}));function n(e,t,o,s,n,c){const d=r.exports.sbrk,f=o+3&-4,u=d(f*s),i=d(n.length),p=new Uint8Array(r.exports.memory.buffer);p.set(n,i);const a=e(u,o,s,i,n.length);if(0==a&&c&&c(u,f,s),t.set(p.subarray(u,u+o*s)),d(u-d(0)),0!=a)throw new Error(\"Malformed buffer data: \"+a)}const c={NONE:\"\",OCTAHEDRAL:\"meshopt_decodeFilterOct\",QUATERNION:\"meshopt_decodeFilterQuat\",EXPONENTIAL:\"meshopt_decodeFilterExp\"},d={ATTRIBUTES:\"meshopt_decodeVertexBuffer\",TRIANGLES:\"meshopt_decodeIndexBuffer\",INDICES:\"meshopt_decodeIndexSequence\"};let f=0,u=!1;const i=new t;return i.setWorkerCreator((()=>{const t=new Worker(e.resolve(\"worker\",\"meshopt_decoder.js\"));return t.postMessage({type:\"init\",wasmPath:o}),t})),{ready:s,supported:!0,dispose(){i.dispose()},useWorkers:function(e){u=e>0,i.setWorkerLimit(e)},decodeVertexBuffer:function(e,t,o,s,d){n(r.exports.meshopt_decodeVertexBuffer,e,t,o,s,r.exports[c[d]])},decodeIndexBuffer:function(e,t,o,s){n(r.exports.meshopt_decodeIndexBuffer,e,t,o,s)},decodeIndexSequence:function(e,t,o,s){n(r.exports.meshopt_decodeIndexSequence,e,t,o,s)},decodeGltfBuffer:function(e,t,o,s,f,u){n(r.exports[d[f]],e,t,o,s,r.exports[c[u]])},decodeGltfBufferAsync:function(e,t,o,p,a){return u>0?function(e,t,o,r,s){return i.postMessage({type:\"decode\",data:{id:f++,count:e,size:t,source:new Uint8Array(o),mode:r,filter:s}}).then((e=>{const{action:t,value:o}=e.data;if(\"resolve\"===t)throw o;return o}))}(e,t,o,d[p],c[a]):s.then((function(){const s=new Uint8Array(e*t);return n(r.exports[d[p]],s,e,t,o,r.exports[c[a]]),s}))}}}();export{o as MeshoptDecoder};\n"], "names": ["o", "e", "s", "n", "c", "d", "f", "u", "i", "t", "r"], "mappings": ";;;AAA6G,MAAC,IAAE,WAAU;AAAC,MAAG,YAAU,OAAO,aAAA,aAAA,EAAY,QAAM,EAAC,WAAU,MAAE;AAAE,QAAMA,KAAEC,qFAAAA,WAAE,QAAQ,QAAO,sBAAsB;AAAE,MAAI;AAAE,QAAM,IAAE,aAAA,aAAA,EAAY,YAAYD,IAAE,CAAA,CAAE,EAAE,KAAM,SAAS,GAAE;AAAC,QAAE,EAAE,UAAS,EAAE,QAAQ;EAAmB,CAAC;AAAG,WAAS,EAAE,GAAE,GAAEA,IAAEE,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,EAAE,QAAQ,MAAKC,KAAEN,KAAE,IAAE,IAAGO,KAAEF,GAAEC,KAAEJ,EAAC,GAAEM,KAAEH,GAAEF,GAAE,MAAM,GAAE,IAAE,IAAI,WAAW,EAAE,QAAQ,OAAO,MAAM;AAAE,MAAE,IAAIA,IAAEK,EAAC;AAAE,UAAM,IAAE,EAAED,IAAEP,IAAEE,IAAEM,IAAEL,GAAE,MAAM;AAAE,QAAG,KAAG,KAAGC,MAAGA,GAAEG,IAAED,IAAEJ,EAAC,GAAE,EAAE,IAAI,EAAE,SAASK,IAAEA,KAAEP,KAAEE,EAAC,CAAC,GAAEG,GAAEE,KAAEF,GAAE,CAAC,CAAC,GAAE,KAAG,EAAE,OAAM,IAAI,MAAM,4BAA0B,CAAC;AAAA,EAAC;AAAC,QAAM,IAAE,EAAC,MAAK,IAAG,YAAW,2BAA0B,YAAW,4BAA2B,aAAY,0BAAyB,GAAE,IAAE,EAAC,YAAW,8BAA6B,WAAU,6BAA4B,SAAQ,8BAA6B;AAAE,MAAI,IAAE,GAAE,IAAE;AAAG,QAAM,IAAE,IAAII,qFAAAA;AAAE,SAAO,EAAE,iBAAkB,MAAI;AAAC,UAAM,IAAE,IAAI,aAAA,QAAA,EAAOR,qFAAAA,WAAE,QAAQ,UAAS,oBAAoB,CAAC;AAAE,WAAO,EAAE,YAAY,EAAC,MAAK,QAAO,UAASD,GAAC,CAAC,GAAE;AAAA,EAAC,CAAC,GAAG,EAAC,OAAM,GAAE,WAAU,MAAG,UAAS;AAAC,MAAE,QAAO;AAAA,EAAE,GAAE,YAAW,SAAS,GAAE;AAAC,QAAE,IAAE,GAAE,EAAE,eAAe,CAAC;AAAA,EAAC,GAAE,oBAAmB,SAAS,GAAE,GAAEA,IAAEE,IAAEG,IAAE;AAAC,MAAE,EAAE,QAAQ,4BAA2B,GAAE,GAAEL,IAAEE,IAAE,EAAE,QAAQ,EAAEG,EAAC,CAAC,CAAC;AAAA,EAAC,GAAE,mBAAkB,SAAS,GAAE,GAAEL,IAAEE,IAAE;AAAC,MAAE,EAAE,QAAQ,2BAA0B,GAAE,GAAEF,IAAEE,EAAC;AAAA,EAAC,GAAE,qBAAoB,SAAS,GAAE,GAAEF,IAAEE,IAAE;AAAC,MAAE,EAAE,QAAQ,6BAA4B,GAAE,GAAEF,IAAEE,EAAC;AAAA,EAAC,GAAE,kBAAiB,SAAS,GAAE,GAAEF,IAAEE,IAAEI,IAAEC,IAAE;AAAC,MAAE,EAAE,QAAQ,EAAED,EAAC,CAAC,GAAE,GAAE,GAAEN,IAAEE,IAAE,EAAE,QAAQ,EAAEK,EAAC,CAAC,CAAC;AAAA,EAAC,GAAE,uBAAsB,SAAS,GAAE,GAAEP,IAAE,GAAE,GAAE;AAAC,WAAO,IAAE,IAAE,SAASC,IAAEQ,IAAET,IAAEU,IAAER,IAAE;AAAC,aAAO,EAAE,YAAY,EAAC,MAAK,UAAS,MAAK,EAAC,IAAG,KAAI,OAAMD,IAAE,MAAKQ,IAAE,QAAO,IAAI,WAAWT,EAAC,GAAE,MAAKU,IAAE,QAAOR,GAAC,EAAC,CAAC,EAAE,KAAM,CAAAD,OAAG;AAAC,cAAK,EAAC,QAAOQ,IAAE,OAAMT,GAAC,IAAEC,GAAE;AAAK,YAAG,cAAYQ,GAAE,OAAMT;AAAE,eAAOA;AAAA,MAAC,CAAC;AAAA,IAAE,EAAE,GAAE,GAAEA,IAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE,KAAM,WAAU;AAAC,YAAME,KAAE,IAAI,WAAW,IAAE,CAAC;AAAE,aAAO,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC,GAAEA,IAAE,GAAE,GAAEF,IAAE,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,GAAEE;AAAA,IAAC,CAAC;AAAA,EAAE,EAAC;AAAC,EAAC;;", "x_google_ignoreList": [0]}