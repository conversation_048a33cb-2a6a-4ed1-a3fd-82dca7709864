"use strict";
const common_vendor = require("../common/vendor.js");
const _sfc_main = {
  __name: "PlatformCanvas",
  props: {
    type: {
      type: String,
      default: "webgl"
    },
    canvasId: {
      type: String,
      required: true
    }
  },
  emits: [
    "touchstart",
    "touchmove",
    "touchend",
    "tap",
    "touchcancel",
    "dispatch",
    "useCanvas"
  ],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    let additionHandler = () => {
    };
    const defaultHandler = (e) => {
      if (e.type === "click") {
        e = {
          ...e,
          type: "tap"
        };
      }
      emit(e.type, e);
      emit("dispatch", e);
      additionHandler == null ? void 0 : additionHandler(e);
    };
    const instance = common_vendor.getCurrentInstance();
    common_vendor.onMounted(() => {
      const query = common_vendor.index.createSelectorQuery().in(instance.ctx);
      query.select(`#${props.canvasId}`).fields({
        node: true,
        size: true
      }).exec((res) => {
        if (res[0] && res[0].node) {
          const canvas = res[0].node;
          const systemInfo = common_vendor.index.getSystemInfoSync();
          canvas.width = systemInfo.windowWidth * (systemInfo.pixelRatio || 1);
          canvas.height = systemInfo.windowHeight * 0.8 * (systemInfo.pixelRatio || 1);
          const gl = canvas.getContext("webgl") || canvas.getContext("experimental-webgl");
          if (gl) {
            const result = {
              canvas,
              gl,
              width: canvas.width,
              height: canvas.height,
              devicePixelRatio: systemInfo.pixelRatio || 1,
              eventHandler: (e, preventDefault = true) => {
                if (preventDefault && e.preventDefault) {
                  e.preventDefault();
                }
              },
              useFrame: (callback) => {
                const animate = () => {
                  callback();
                  setTimeout(animate, 16);
                };
                animate();
              },
              recomputeSize: () => {
                return Promise.resolve({
                  width: canvas.width,
                  height: canvas.height
                });
              }
            };
            additionHandler = (e) => {
              result.eventHandler(e, false);
            };
            emit("useCanvas", result);
          } else {
            console.error("无法获取WebGL上下文");
          }
        }
      });
    });
    return (_ctx, _cache) => {
      return {
        a: __props.type,
        b: __props.canvasId,
        c: common_vendor.o(defaultHandler),
        d: common_vendor.o(defaultHandler),
        e: common_vendor.o(defaultHandler),
        f: common_vendor.o(defaultHandler),
        g: common_vendor.o(defaultHandler)
      };
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-24260282"]]);
wx.createComponent(Component);
