{"version": 3, "file": "uni-app.es.js", "sources": ["../node_modules/@dcloudio/uni-app/dist/uni-app.es.js"], "sourcesContent": ["import { shallowRef, ref, getCurrentInstance, isInSSRComponentSetup, injectHook } from 'vue';\nimport { hasOwn } from '@vue/shared';\nexport { capitalize, extend, hasOwn, isPlainObject } from '@vue/shared';\nimport { sanitise, UNI_SSR_DATA, UNI_SSR_GLOBAL_DATA, UNI_SSR, ON_SHOW, ON_HIDE, ON_LAUNCH, ON_ERROR, ON_THEME_CHANGE, ON_PAGE_NOT_FOUND, ON_UNHANDLE_REJECTION, ON_EXIT, ON_INIT, ON_LOAD, ON_READY, ON_UNLOAD, ON_RESIZE, ON_BACK_PRESS, ON_PAGE_SCROLL, ON_TAB_ITEM_TAP, ON_REACH_BOTTOM, ON_PULL_DOWN_REFRESH, ON_SAVE_EXIT_STATE, ON_SHARE_TIMELINE, ON_ADD_TO_FAVORITES, ON_SHARE_APP_MESSAGE, ON_NAVIGATION_BAR_BUTTON_TAP, ON_NAVIGATION_BAR_SEARCH_INPUT_CHANGED, ON_NAVIGATION_BAR_SEARCH_INPUT_CLICKED, ON_NAVIGATION_BAR_SEARCH_INPUT_CONFIRMED, ON_NAVIGATION_BAR_SEARCH_INPUT_FOCUS_CHANGED } from '@dcloudio/uni-shared';\n\nfunction getSSRDataType() {\n    return getCurrentInstance() ? UNI_SSR_DATA : UNI_SSR_GLOBAL_DATA;\n}\nfunction assertKey(key, shallow = false) {\n    if (!key) {\n        throw new Error(`${shallow ? 'shallowSsrRef' : 'ssrRef'}: You must provide a key.`);\n    }\n}\nconst ssrClientRef = (value, key, shallow = false) => {\n    const valRef = shallow ? shallowRef(value) : ref(value);\n    // 非 h5 平台\n    if (typeof window === 'undefined') {\n        return valRef;\n    }\n    const __uniSSR = window[UNI_SSR];\n    if (!__uniSSR) {\n        return valRef;\n    }\n    const type = getSSRDataType();\n    assertKey(key, shallow);\n    if (hasOwn(__uniSSR[type], key)) {\n        valRef.value = __uniSSR[type][key];\n        if (type === UNI_SSR_DATA) {\n            delete __uniSSR[type][key]; // TODO 非全局数据仅使用一次？否则下次还会再次使用该数据\n        }\n    }\n    return valRef;\n};\nconst globalData = {};\nconst ssrRef = (value, key) => {\n    return ssrClientRef(value, key);\n};\nconst shallowSsrRef = (value, key) => {\n    return ssrClientRef(value, key, true);\n};\nfunction getSsrGlobalData() {\n    return sanitise(globalData);\n}\n\n/**\n * uni 对象是跨实例的，而此处列的 API 均是需要跟当前实例关联的，比如 requireNativePlugin 获取 dom 时，依赖当前 weex 实例\n */\nfunction getCurrentSubNVue() {\n    return uni.getSubNVueById(plus.webview.currentWebview().id);\n}\nfunction requireNativePlugin(name) {\n    return weex.requireModule(name);\n}\n\nfunction formatAppLog(type, filename, ...args) {\n    // @ts-expect-error\n    if (uni.__log__) {\n        // @ts-expect-error\n        uni.__log__(type, filename, ...args);\n    }\n    else {\n        console[type].apply(console, [...args, filename]);\n    }\n}\nfunction formatLog(type, filename, ...args) {\n    if (filename) {\n        args.push(filename);\n    }\n    console[type].apply(console, args);\n}\n\nfunction resolveEasycom(component, easycom) {\n    return typeof component === 'string' ? easycom : component;\n}\n\n/// <reference types=\"@dcloudio/types\" />\nconst createHook = (lifecycle) => (hook, target = getCurrentInstance()) => {\n    // post-create lifecycle registrations are noops during SSR\n    !isInSSRComponentSetup && injectHook(lifecycle, hook, target);\n};\nconst onShow = /*#__PURE__*/ createHook(ON_SHOW);\nconst onHide = /*#__PURE__*/ createHook(ON_HIDE);\nconst onLaunch = \n/*#__PURE__*/ createHook(ON_LAUNCH);\nconst onError = \n/*#__PURE__*/ createHook(ON_ERROR);\nconst onThemeChange = \n/*#__PURE__*/ createHook(ON_THEME_CHANGE);\nconst onPageNotFound = \n/*#__PURE__*/ createHook(ON_PAGE_NOT_FOUND);\nconst onUnhandledRejection = /*#__PURE__*/ createHook(ON_UNHANDLE_REJECTION);\nconst onExit = /*#__PURE__*/ createHook(ON_EXIT);\nconst onInit = \n/*#__PURE__*/ createHook(ON_INIT);\n// 小程序如果想在 setup 的 props 传递页面参数，需要定义 props，故同时暴露 onLoad 吧\nconst onLoad = \n/*#__PURE__*/ createHook(ON_LOAD);\nconst onReady = /*#__PURE__*/ createHook(ON_READY);\nconst onUnload = /*#__PURE__*/ createHook(ON_UNLOAD);\nconst onResize = \n/*#__PURE__*/ createHook(ON_RESIZE);\nconst onBackPress = \n/*#__PURE__*/ createHook(ON_BACK_PRESS);\nconst onPageScroll = \n/*#__PURE__*/ createHook(ON_PAGE_SCROLL);\nconst onTabItemTap = \n/*#__PURE__*/ createHook(ON_TAB_ITEM_TAP);\nconst onReachBottom = /*#__PURE__*/ createHook(ON_REACH_BOTTOM);\nconst onPullDownRefresh = /*#__PURE__*/ createHook(ON_PULL_DOWN_REFRESH);\nconst onSaveExitState = \n/*#__PURE__*/ createHook(ON_SAVE_EXIT_STATE);\nconst onShareTimeline = \n/*#__PURE__*/ createHook(ON_SHARE_TIMELINE);\nconst onAddToFavorites = \n/*#__PURE__*/ createHook(ON_ADD_TO_FAVORITES);\nconst onShareAppMessage = \n/*#__PURE__*/ createHook(ON_SHARE_APP_MESSAGE);\nconst onNavigationBarButtonTap = /*#__PURE__*/ createHook(ON_NAVIGATION_BAR_BUTTON_TAP);\nconst onNavigationBarSearchInputChanged = /*#__PURE__*/ createHook(ON_NAVIGATION_BAR_SEARCH_INPUT_CHANGED);\nconst onNavigationBarSearchInputClicked = /*#__PURE__*/ createHook(ON_NAVIGATION_BAR_SEARCH_INPUT_CLICKED);\nconst onNavigationBarSearchInputConfirmed = /*#__PURE__*/ createHook(ON_NAVIGATION_BAR_SEARCH_INPUT_CONFIRMED);\nconst onNavigationBarSearchInputFocusChanged = \n/*#__PURE__*/ createHook(ON_NAVIGATION_BAR_SEARCH_INPUT_FOCUS_CHANGED);\n// for uni-app-x web\nconst onPageHide = onHide;\nconst onPageShow = onShow;\n\nfunction renderComponentSlot(slots, name, props = null) {\n    if (slots[name]) {\n        return slots[name](props);\n    }\n    return null;\n}\n\nexport { formatAppLog, formatLog, getCurrentSubNVue, getSsrGlobalData, onAddToFavorites, onBackPress, onError, onExit, onHide, onInit, onLaunch, onLoad, onNavigationBarButtonTap, onNavigationBarSearchInputChanged, onNavigationBarSearchInputClicked, onNavigationBarSearchInputConfirmed, onNavigationBarSearchInputFocusChanged, onPageHide, onPageNotFound, onPageScroll, onPageShow, onPullDownRefresh, onReachBottom, onReady, onResize, onSaveExitState, onShareAppMessage, onShareTimeline, onShow, onTabItemTap, onThemeChange, onUnhandledRejection, onUnload, renderComponentSlot, requireNativePlugin, resolveEasycom, shallowSsrRef, ssrRef };\n"], "names": ["getCurrentInstance", "isInSSRComponentSetup", "injectHook", "ON_SHOW", "ON_HIDE", "ON_LAUNCH"], "mappings": ";;;;AA4EA,MAAM,aAAa,CAAC,cAAc,CAAC,MAAM,SAASA,qDAAAA,mBAAkB,MAAO;AAEvE,GAACC,qDAAAA,yBAAyBC,qDAAAA,WAAW,WAAW,MAAM,MAAM;AAChE;AACK,MAAC,SAAuB,2BAAWC,mDAAAA,OAAO;AAC1C,MAAC,SAAuB,2BAAWC,mDAAAA,OAAO;AAC1C,MAAC,WACQ,2BAAWC,mDAAAA,SAAS;;;;", "x_google_ignoreList": [0]}