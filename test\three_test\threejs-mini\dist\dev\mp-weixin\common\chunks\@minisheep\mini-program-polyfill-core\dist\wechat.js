"use strict";
const _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm = require("../../../@dcloudio/uni-mp-weixin/dist/uni.api.esm.js");
const _mpChunkDeps_eventTargetShim_index = require("../../../event-target-shim/index.js");
const _mpChunkDeps_webStreamsPolyfill_dist_ponyfill = require("../../../web-streams-polyfill/dist/ponyfill.js");
globalThis.__sharedGlobals = (() => {
  if (globalThis.__sharedGlobals) {
    if (!globalThis.__sharedGlobals.__prefix_global__) throw new Error("[plugin:prefix-global] inject global property fail, key [__sharedGlobals] is already defined.");
    return globalThis.__sharedGlobals;
  }
  const e = JSON.parse("[]"), t = new Proxy({ __prefix_global__: true }, { get: (r, i) => e.includes(i) ? void 0 : Object.hasOwn(r, i) ? Reflect.get(r, i) : "self" === i ? t : Reflect.get(globalThis, i), ownKeys: (e2) => Array.from(new Set([Reflect.ownKeys(e2), Reflect.ownKeys(globalThis)].flat())), has: (e2, t2) => Reflect.has(e2, t2) || Reflect.has(globalThis, t2) });
  return t;
})();
function s(e) {
  return new __sharedGlobals.DOMException(e, "InvalidStateError");
}
function a(e) {
  const t = /* @__PURE__ */ new WeakMap();
  return { weakMap: t, get(r) {
    const i = t.get(r);
    if (null == i) throw TypeError(`Illegal invocation at ${e}.invokeGetter`);
    return i;
  } };
}
function n(e, t) {
  const r = "undefined" != typeof globalThis && globalThis || // @ts-expect-error self
  void 0 !== __sharedGlobals.self && __sharedGlobals.self || // @ts-expect-error global
  "undefined" != typeof global && global || {};
  r.hasOwnProperty(e) || (r[e] = t);
}
class o extends _mpChunkDeps_eventTargetShim_index.Event {
  constructor(e, t) {
    super(e, t), l.weakMap.set(this, { lengthComputable: false, loaded: 0, total: 0 });
  }
  get lengthComputable() {
    return l.get(this).lengthComputable;
  }
  get loaded() {
    return l.get(this).loaded;
  }
  get total() {
    return l.get(this).total;
  }
}
const l = a(o.name);
class u {
  constructor(e) {
    Object.defineProperty(this, "identifier", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "target", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "clientX", { enumerable: true, configurable: true, writable: true, value: 0 }), Object.defineProperty(this, "clientY", { enumerable: true, configurable: true, writable: true, value: 0 }), Object.defineProperty(this, "force", { enumerable: true, configurable: true, writable: true, value: 0 }), Object.defineProperty(this, "pageX", { enumerable: true, configurable: true, writable: true, value: 0 }), Object.defineProperty(this, "pageY", { enumerable: true, configurable: true, writable: true, value: 0 }), Object.defineProperty(this, "screenX", { enumerable: true, configurable: true, writable: true, value: 0 }), Object.defineProperty(this, "screenY", { enumerable: true, configurable: true, writable: true, value: 0 }), Object.defineProperty(this, "radiusX", { enumerable: true, configurable: true, writable: true, value: 0 }), Object.defineProperty(this, "radiusY", { enumerable: true, configurable: true, writable: true, value: 0 }), Object.defineProperty(this, "rotationAngle", { enumerable: true, configurable: true, writable: true, value: 0 }), this.identifier = e.identifier, this.target = e.target, e.clientX && (this.clientX = e.clientX), e.clientY && (this.clientY = e.clientY), e.force && (this.force = e.force), e.pageX && (this.pageX = e.pageX), e.pageY && (this.pageY = e.pageY), e.screenX && (this.screenX = e.screenX), e.screenY && (this.screenY = e.screenY), e.radiusX && (this.radiusX = e.radiusX), e.radiusY && (this.radiusY = e.radiusY), e.rotationAngle && (this.rotationAngle = e.rotationAngle);
  }
}
class c extends _mpChunkDeps_eventTargetShim_index.Event {
  constructor(e, t = {}) {
    const { detail: r = 0, view: i = null, ...s2 } = t;
    super(e, s2), Object.defineProperty(this, "detail", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "view", { enumerable: true, configurable: true, writable: true, value: void 0 }), this.detail = r, this.view = i;
  }
}
// @__NO_SIDE_EFFECTS__
function h(e) {
  const t = e;
  return t.item || (t.item = function(e2) {
    return t[e2] || null;
  }), t;
}
class d extends c {
  constructor(e, t = {}) {
    const { changedTouches: r = [], targetTouches: i = [], touches: s2 = [], altKey: a2 = false, ctrlKey: n2 = false, metaKey: o2 = false, shiftKey: l2 = false, ...u2 } = t;
    super(e, u2), Object.defineProperty(this, "altKey", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "ctrlKey", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "metaKey", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "shiftKey", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "changedTouches", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "targetTouches", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "touches", { enumerable: true, configurable: true, writable: true, value: void 0 }), this.altKey = a2, this.ctrlKey = n2, this.metaKey = o2, this.shiftKey = l2, this.changedTouches = /* @__PURE__ */ h(r), this.targetTouches = /* @__PURE__ */ h(i), this.touches = /* @__PURE__ */ h(s2);
  }
}
class b extends c {
  constructor(e, t = {}) {
    const { button: r = 0, buttons: i = 0, clientX: s2 = 0, clientY: a2 = 0, movementX: n2 = 0, movementY: o2 = 0, relatedTarget: l2 = null, screenX: u2 = 0, screenY: c2 = 0, altKey: h2 = false, ctrlKey: d2 = false, metaKey: b2 = false, shiftKey: f2 = false, offsetY: p2 = 0, offsetX: y2 = 0, ...v2 } = t;
    super(e, v2), Object.defineProperty(this, "altKey", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "ctrlKey", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "metaKey", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "shiftKey", { enumerable: true, configurable: true, writable: true, value: void 0 }), /** alias for clientX*/
    Object.defineProperty(this, "x", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "y", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "clientX", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "clientY", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "screenX", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "screenY", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "movementX", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "movementY", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "button", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "buttons", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "relatedTarget", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "pageX", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "pageY", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "offsetX", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "offsetY", { enumerable: true, configurable: true, writable: true, value: void 0 }), this.altKey = h2, this.ctrlKey = d2, this.metaKey = b2, this.shiftKey = f2, this.x = s2, this.y = a2, this.buttons = i, this.button = r, this.clientX = s2, this.clientY = a2, this.screenX = u2, this.screenY = c2, this.movementX = n2, this.movementY = o2, this.pageX = s2, this.pageY = a2, this.offsetX = y2, this.offsetY = p2, this.relatedTarget = l2;
  }
}
class f extends b {
  constructor(e, t = {}) {
    const { pointerId: r = 0, width: i = 1, height: s2 = 1, pressure: a2 = 0, tangentialPressure: n2 = 0, tiltX: o2 = 0, tiltY: l2 = 0, twist: u2 = 0, pointerType: c2 = "", isPrimary: h2 = false, ...d2 } = t;
    super(e, d2), Object.defineProperty(this, "pointerId", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "width", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "height", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "pressure", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "tangentialPressure", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "tiltX", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "tiltY", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "twist", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "pointerType", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "isPrimary", { enumerable: true, configurable: true, writable: true, value: void 0 }), this.pointerId = r, this.width = i, this.height = s2, this.pressure = Math.min(1, Math.max(a2, 0)), this.tangentialPressure = Math.min(1, Math.max(n2, -1)), this.tiltX = o2, this.tiltY = l2, this.twist = u2, this.pointerType = c2, this.isPrimary = h2;
  }
  getCoalescedEvents() {
    return [];
  }
  getPredictedEvents() {
    return [];
  }
}
const p = class extends c {
  constructor(e, t = {}) {
    const { altKey: r = false, ctrlKey: i = false, metaKey: s2 = false, shiftKey: a2 = false, charCode: n2 = 0, code: o2 = "", isComposing: l2 = false, key: u2 = "", keyCode: c2 = 0, location: h2 = 0, repeat: d2 = false, ...b2 } = t;
    super(e, b2), Object.defineProperty(this, "altKey", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "ctrlKey", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "metaKey", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "shiftKey", { enumerable: true, configurable: true, writable: true, value: void 0 }), /**
             * @deprecated
             * [MDN Reference](https://developer.mozilla.org/docs/Web/API/KeyboardEvent/charCode)
             */
    Object.defineProperty(this, "charCode", { enumerable: true, configurable: true, writable: true, value: void 0 }), /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/KeyboardEvent/code) */
    Object.defineProperty(this, "code", { enumerable: true, configurable: true, writable: true, value: void 0 }), /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/KeyboardEvent/isComposing) */
    Object.defineProperty(this, "isComposing", { enumerable: true, configurable: true, writable: true, value: void 0 }), /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/KeyboardEvent/key) */
    Object.defineProperty(this, "key", { enumerable: true, configurable: true, writable: true, value: void 0 }), /**
             * @deprecated
             *
             * [MDN Reference](https://developer.mozilla.org/docs/Web/API/KeyboardEvent/keyCode)
             */
    Object.defineProperty(this, "keyCode", { enumerable: true, configurable: true, writable: true, value: void 0 }), /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/KeyboardEvent/location) */
    Object.defineProperty(this, "location", { enumerable: true, configurable: true, writable: true, value: void 0 }), /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/KeyboardEvent/repeat) */
    Object.defineProperty(this, "repeat", { enumerable: true, configurable: true, writable: true, value: void 0 }), this.altKey = r, this.ctrlKey = i, this.metaKey = s2, this.shiftKey = a2, this.charCode = n2, this.code = o2, this.isComposing = l2, this.key = u2, this.keyCode = c2, this.location = h2, this.repeat = d2;
  }
};
class y extends _mpChunkDeps_eventTargetShim_index.Event {
  constructor(e, t = {}) {
    const { data: r, lastEventId: i, origin: s2, ports: a2, source: n2, ...o2 } = t;
    super(e, o2), Object.defineProperty(this, "data", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "lastEventId", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "origin", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "ports", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "source", { enumerable: true, configurable: true, writable: true, value: void 0 }), this.data = r, this.lastEventId = i || "", this.origin = s2 || "", this.ports = a2 || [], this.source = n2 || null;
  }
}
class v {
  constructor() {
    g.set(this, (w = true, new _()));
  }
  get signal() {
    return m(this);
  }
  abort(t) {
    O(this.signal).reason = t, this.signal.dispatchEvent(new _mpChunkDeps_eventTargetShim_index.Event("abort"));
  }
}
const { weakMap: g, get: m } = a(v.name);
let w = false;
class _ extends _mpChunkDeps_eventTargetShim_index.EventTarget {
  constructor() {
    if (super(), Object.defineProperty(this, "onabort", { enumerable: true, configurable: true, writable: true, value: void 0 }), !w) throw new TypeError("Illegal constructor");
    w = false, P.set(this, { aborted: false, reason: new __sharedGlobals.DOMException("signal is aborted without reason", "AbortError") }), _mpChunkDeps_eventTargetShim_index.setEventAttributeValue(this, "abort", function(e) {
      var t;
      this.aborted || (O(this).aborted = true, null === (t = this.onabort) || void 0 === t || t.call(this, e));
    });
  }
  get aborted() {
    return O(this).aborted;
  }
  get reason() {
    return O(this).reason;
  }
  throwIfAborted() {
    if (this.aborted) throw this.reason;
  }
  static abort(e) {
    const t = new v();
    return t.abort(e), t.signal;
  }
  static timeout(e) {
    const t = new v();
    return setTimeout(() => t.abort(new __sharedGlobals.DOMException("signal timed out", "TimeOut")), e), t.signal;
  }
}
const { weakMap: P, get: O } = a(_.name);
const E = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=", j = {};
for (let e = 0; e < 65; e++) j[E[e]] = e;
function T(e) {
  const t = [];
  for (let r = 0; r < e.length; r += 3) {
    const i = e[r], s2 = r + 1 < e.length, a2 = s2 ? e[r + 1] : 0, n2 = r + 2 < e.length, o2 = n2 ? e[r + 2] : 0, l2 = i >> 2, u2 = (3 & i) << 4 | a2 >> 4;
    let c2 = (15 & a2) << 2 | o2 >> 6, h2 = 63 & o2;
    n2 || (h2 = 64, s2 || (c2 = 64)), t.push(E[l2], E[u2], E[c2], E[h2]);
  }
  return t.join("");
}
function R(e) {
  const t = e.replace(/[^A-Za-z0-9+/=]/g, ""), r = ("=" === t[t.length - 1] ? 1 : 0) + ("=" === t[t.length - 2] ? 1 : 0), i = 3 * t.length / 4 - r, s2 = new Uint8Array(i);
  let a2 = 0;
  for (let e2 = 0; e2 < t.length; e2 += 4) {
    const r2 = j[t[e2]], n2 = j[t[e2 + 1]], o2 = j[t[e2 + 2]] || 0, l2 = r2 << 2 | n2 >> 4, u2 = (15 & n2) << 4 | o2 >> 2, c2 = (3 & o2) << 6 | (j[t[e2 + 3]] || 0);
    s2[a2++] = l2, a2 < i && (s2[a2++] = u2), a2 < i && (s2[a2++] = c2);
  }
  return s2;
}
const A = function(e) {
  return __sharedGlobals.TextEncoder.prototype.encode.call(null, e);
};
function x(e) {
  if (e instanceof ArrayBuffer) return new Uint8Array(e.slice(0));
  if (ArrayBuffer.isView(e)) return new Uint8Array(e.buffer.slice(e.byteOffset, e.byteOffset + e.byteLength));
  throw new TypeError("Invalid buffer type");
}
class C {
  constructor(e = [], t = {}) {
    Object.defineProperty(this, "_buffer", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "size", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "type", { enumerable: true, configurable: true, writable: true, value: void 0 });
    const r = new Array(e.length);
    for (let t2 = 0, i = e.length; t2 < i; t2++) {
      const i2 = e[t2];
      i2 instanceof C ? r[t2] = i2._buffer : "string" == typeof i2 ? r[t2] = A(i2) : i2 instanceof ArrayBuffer || ArrayBuffer.isView(i2) ? r[t2] = x(i2) : r[t2] = A(String(i2));
    }
    this._buffer = function(e2) {
      const t2 = e2.reduce((e3, t3) => e3 + t3.byteLength, 0), r2 = new Uint8Array(t2);
      let i = 0;
      return e2.forEach((e3) => {
        r2.set(e3, i), i += e3.byteLength;
      }), r2;
    }(r), this.size = this._buffer.length, this.type = String(t.type) || "", /[^\u0020-\u007E]/.test(this.type) ? this.type = "" : this.type = this.type.toLowerCase();
  }
  arrayBuffer() {
    return Promise.resolve(this._buffer.buffer.slice(0));
  }
  text() {
    return Promise.resolve((e = this._buffer, __sharedGlobals.TextDecoder.prototype.decode.call(null, e)));
    var e;
  }
  slice(e, t, r) {
    const i = this._buffer.slice(e || 0, t || this._buffer.length);
    return new C([i], { type: r });
  }
  stream() {
    let e = 0;
    const t = this;
    return new __sharedGlobals.ReadableStream({ type: "bytes", autoAllocateChunkSize: 524288, pull(r) {
      const i = r.byobRequest.view;
      return t.slice(e, e + i.byteLength).arrayBuffer().then(function(s2) {
        const a2 = new Uint8Array(s2), n2 = a2.byteLength;
        e += n2, //@ts-expect-error ignore
        i.set(a2), r.byobRequest.respond(n2), e >= t.size && r.close();
      });
    } });
  }
}
class S extends C {
  constructor(e, t, r = {}) {
    super(e, r), Object.defineProperty(this, "lastModified", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "name", { enumerable: true, configurable: true, writable: true, value: void 0 }), this.name = t.replace(/\//g, ":"), this.lastModified = +(r.lastModified || 0);
  }
}
const B = 0, M = 1;
async function I(e, t, r) {
  if (!(t instanceof C)) throw new TypeError("Failed to execute '" + r + "' on 'FileReader': parameter 1 is not of type 'Blob'.");
  return new Promise((i, s2) => {
    setTimeout(async () => {
      switch (e.result = "", e.readyState = M, e.dispatchEvent(new __sharedGlobals.ProgressEvent("loadstart", { lengthComputable: true, loaded: 0, total: t._buffer.byteLength })), r) {
        case "readAsDataURL":
          e.result = "data:" + t.type + ";base64," + T(t._buffer);
          break;
        case "readAsArrayBuffer":
          e.result = await t.arrayBuffer();
          break;
        case "readAsText":
          e.result = await t.text();
      }
      const s3 = { lengthComputable: true, loaded: t._buffer.byteLength, total: t._buffer.byteLength };
      e.dispatchEvent(new __sharedGlobals.ProgressEvent("progress", s3)), e.dispatchEvent(new __sharedGlobals.ProgressEvent("load", s3)), e.dispatchEvent(new __sharedGlobals.ProgressEvent("loadend", s3)), i();
    }, 0);
  });
}
const U = class extends _mpChunkDeps_eventTargetShim_index.EventTarget {
  constructor() {
    super(), Object.defineProperty(this, "onabort", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "onerror", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "onload", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "onloadstart", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "onprogress", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "onloadend", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "readyState", { enumerable: true, configurable: true, writable: true, value: B }), Object.defineProperty(this, "error", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "result", { enumerable: true, configurable: true, writable: true, value: null }), _mpChunkDeps_eventTargetShim_index.setEventAttributeValue(this, "abort", (e) => {
      var t;
      return null === (t = this.onabort) || void 0 === t ? void 0 : t.call(this, e);
    }), _mpChunkDeps_eventTargetShim_index.setEventAttributeValue(this, "error", (e) => {
      var t;
      return null === (t = this.onerror) || void 0 === t ? void 0 : t.call(this, e);
    }), _mpChunkDeps_eventTargetShim_index.setEventAttributeValue(this, "load", (e) => {
      var t;
      return null === (t = this.onload) || void 0 === t ? void 0 : t.call(this, e);
    }), _mpChunkDeps_eventTargetShim_index.setEventAttributeValue(this, "loadstart", (e) => {
      var t;
      return null === (t = this.onloadstart) || void 0 === t ? void 0 : t.call(this, e);
    }), _mpChunkDeps_eventTargetShim_index.setEventAttributeValue(this, "progress", (e) => {
      var t;
      return null === (t = this.onprogress) || void 0 === t ? void 0 : t.call(this, e);
    }), _mpChunkDeps_eventTargetShim_index.setEventAttributeValue(this, "loadend", (e) => {
      var t;
      return null === (t = this.onloadend) || void 0 === t ? void 0 : t.call(this, e);
    });
  }
  readAsDataURL(e) {
    I(this, e, "readAsDataURL");
  }
  readAsText(e) {
    I(this, e, "readAsText");
  }
  readAsArrayBuffer(e) {
    I(this, e, "readAsArrayBuffer");
  }
  abort() {
    this.dispatchEvent(new __sharedGlobals.ProgressEvent("abort", { lengthComputable: false, loaded: 0, total: 0 })), this.result = null;
  }
}, k = {
  INDEX_SIZE_ERR: 1,
  DOMSTRING_SIZE_ERR: 2,
  HIERARCHY_REQUEST_ERR: 3,
  WRONG_DOCUMENT_ERR: 4,
  INVALID_CHARACTER_ERR: 5,
  NO_DATA_ALLOWED_ERR: 6,
  NO_MODIFICATION_ALLOWED_ERR: 7,
  NOT_FOUND_ERR: 8,
  NOT_SUPPORTED_ERR: 9,
  INUSE_ATTRIBUTE_ERR: 10,
  INVALID_STATE_ERR: 11,
  SYNTAX_ERR: 12,
  INVALID_MODIFICATION_ERR: 13,
  NAMESPACE_ERR: 14,
  INVALID_ACCESS_ERR: 15,
  VALIDATION_ERR: 16,
  /** @deprecated */
  TYPE_MISMATCH_ERR: 17,
  SECURITY_ERR: 18,
  NETWORK_ERR: 19,
  ABORT_ERR: 20,
  URL_MISMATCH_ERR: 21,
  QUOTA_EXCEEDED_ERR: 22,
  TIMEOUT_ERR: 23,
  INVALID_NODE_TYPE_ERR: 24,
  DATA_CLONE_ERR: 25
};
class D extends Error {
  constructor(e, t) {
    super(e), Object.defineProperty(this, "_name", { enumerable: true, configurable: true, writable: true, value: t }), Error.captureStackTrace && Error.captureStackTrace(this, G);
  }
  get code() {
    var e;
    return null !== (e = k[this.name.replace(/([a-z])([A-Z])/g, "$1_$2").toUpperCase()]) && void 0 !== e ? e : 0;
  }
  get name() {
    var e;
    return null !== (e = this._name) && void 0 !== e ? e : "Error";
  }
}
const G = D;
function L(e, t, r) {
  return t instanceof __sharedGlobals.Blob ? (r = void 0 !== r ? String(r + "") : t instanceof __sharedGlobals.File ? t.name : "blob", t.name === r && "[object Blob]" !== Object.prototype.toString.call(t) || (t = new __sharedGlobals.File([t], r)), [String(e), t]) : [String(e), String(t)];
}
function N(e) {
  return e.replace(/\r?\n|\r/g, "\r\n");
}
class X {
  constructor() {
    Object.defineProperty(this, "_data", { enumerable: true, configurable: true, writable: true, value: [] });
  }
  /**
       * Append a field
       *
       * @param name      field name
       * @param value     string / blob / file
       * @param filename  filename to use with blob
       */
  append(e, t, r) {
    this._data.push(L(e, t, r));
  }
  /**
       * Delete all fields values given name
       *
       * @param   name  Field name
       */
  delete(e) {
    const t = [];
    e = String(e), this._data.forEach((r) => {
      r[0] !== e && t.push(r);
    }), this._data = t;
  }
  /**
       * Return first field value given name
       * or null if non existent
       * @param   {string}   name
       */
  get(e) {
    const t = this._data;
    e = String(e);
    for (let r = 0; r < t.length; r++) if (t[r][0] === e) return t[r][1];
    return null;
  }
  /**
       * Return all fields values given name
       *
       * @param  name  Fields name
       * @return  {Array}         [{String|File}]
       */
  getAll(e) {
    const t = [];
    return e = String(e), this._data.forEach((r) => {
      r[0] === e && t.push(r[1]);
    }), t;
  }
  /**
       * Check for field name existence
       *
       * @param   {string}   name  Field name
       * @return  {boolean}
       */
  has(e) {
    e = String(e);
    for (let t = 0; t < this._data.length; t++) if (this._data[t][0] === e) return true;
    return false;
  }
  /**
       * Overwrite all values given name
       *
       * @param name      Filed name
       * @param value     Field value
       * @param filename  Filename (optional)
       */
  set(e, t, r) {
    e = String(e);
    const i = [], s2 = L(e, t, r);
    let a2 = true;
    this._data.forEach((t2) => {
      t2[0] === e ? a2 && (a2 = !i.push(s2)) : i.push(t2);
    }), a2 && i.push(s2), this._data = i;
  }
  /**
       * need manual called in xhr.send() or fetch()
       *
       */
  _blob() {
    const e = "----formdata-polyfill-" + Math.random(), t = [], r = `--${e}\r
Content-Disposition: form-data; name="`;
    return this.forEach((e2, i) => "string" == typeof e2 ? t.push(r + encodeURIComponent(N(i)) + `"\r
\r
${N(e2)}\r
`) : t.push(r + encodeURIComponent(N(i)) + `"; filename="${encodeURIComponent(e2.name)}"\r
Content-Type: ${e2.type || "application/octet-stream"}\r
\r
`, e2, "\r\n")), t.push(`--${e}--`), new __sharedGlobals.Blob(t, { type: "multipart/form-data; boundary=" + e });
  }
  /**
       * Iterate over all fields
       *
       * @param callback  Executed for each item with parameters (value, name, thisArg)
       * @param thisArg   `this` context for callback function
       */
  forEach(e, t) {
    for (const [r, i] of this) e.call(t, i, r, this);
  }
  *keys() {
    for (const [e] of this) yield e;
  }
  *values() {
    for (const [, e] of this) yield e;
  }
  *entries() {
    yield* this._data.values();
  }
  [Symbol.iterator]() {
    return this.entries();
  }
}
function K(e) {
  if ("string" != typeof e && (e = String(e)), /[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(e) || "" === e) throw new TypeError('Invalid character in header field name: "' + e + '"');
  return e.toLowerCase();
}
function Y(e) {
  return "string" != typeof e && (e = String(e)), e;
}
const F = ["CONNECT", "DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT", "TRACE"];
class q {
  constructor(e) {
    Object.defineProperty(this, "__map", { enumerable: true, configurable: true, writable: true, value: {} }), e instanceof q ? e.forEach((e2, t) => {
      this.append(t, e2);
    }, this) : Array.isArray(e) ? e.forEach((e2) => {
      if (2 != e2.length) throw new TypeError("Headers constructor: expected name/value pair to be length 2, found" + e2.length);
      this.append(e2[0], e2[1]);
    }, this) : e && Object.getOwnPropertyNames(e).forEach((t) => {
      this.append(t, e[t]);
    }, this);
  }
  append(e, t) {
    e = K(e), t = Y(t);
    const r = this.__map[e];
    this.__map[e] = r ? r + ", " + t : t;
  }
  delete(e) {
    delete this.__map[K(e)];
  }
  get(e) {
    return e = K(e), this.has(e) ? this.__map[e] : null;
  }
  has(e) {
    return this.__map.hasOwnProperty(K(e));
  }
  set(e, t) {
    this.__map[K(e)] = Y(t);
  }
  forEach(e, t) {
    for (const r of Object.entries(this.__map)) e.call(t, r[1], r[0], this);
  }
  *keys() {
    yield* Object.keys(this.__map);
  }
  *values() {
    yield* Object.values(this.__map);
  }
  *entries() {
    yield* Object.entries(this.__map);
  }
  [Symbol.iterator]() {
    return this.entries();
  }
}
function H(e) {
  if (e.slice) return e.slice(0);
  {
    const t = new Uint8Array(e.byteLength);
    return t.set(new Uint8Array(e)), t.buffer;
  }
}
function W(e) {
  if (!e._noBody) return e.bodyUsed ? Promise.reject(new TypeError("Already read")) : void (e.bodyUsed = true);
}
function z(e) {
  return new Promise(function(t, r) {
    e.onload = function() {
      t(e.result);
    }, e.onerror = function() {
      r(e.error);
    };
  });
}
function $(e) {
  const t = new __sharedGlobals.FileReader(), r = z(t);
  return t.readAsArrayBuffer(e), r;
}
function V(e = "") {
  const t = new __sharedGlobals.FormData();
  return e.trim().split("&").forEach(function(e2) {
    if (e2) {
      const r = e2.split("="), i = r.shift().replace(/\+/g, " "), s2 = r.join("=").replace(/\+/g, " ");
      t.append(decodeURIComponent(i), decodeURIComponent(s2));
    }
  }), t;
}
class Z {
  constructor() {
    Object.defineProperty(this, "_bodyInit", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "_noBody", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "_bodyText", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "_bodyBlob", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "_bodyFormData", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "_bodyArrayBuffer", { enumerable: true, configurable: true, writable: true, value: void 0 });
  }
  _initBody(e) {
    var t;
    this._bodyInit = e, e ? "string" == typeof e ? this._bodyText = e : e instanceof __sharedGlobals.Blob ? this._bodyBlob = e : e instanceof __sharedGlobals.FormData ? this._bodyFormData = e : (t = e) && DataView.prototype.isPrototypeOf(t) ? (this._bodyArrayBuffer = H(e.buffer), // IE 10-11 can't handle a DataView body.
    this._bodyInit = new __sharedGlobals.Blob([this._bodyArrayBuffer])) : e instanceof ArrayBuffer || ArrayBuffer.isView(e) ? this._bodyArrayBuffer = H(e) : this._bodyText = e = Object.prototype.toString.call(e) : (this._noBody = true, this._bodyText = ""), this.headers.get("content-type") || ("string" == typeof e ? this.headers.set("content-type", "text/plain;charset=UTF-8") : this._bodyBlob && this._bodyBlob.type && this.headers.set("content-type", this._bodyBlob.type));
  }
  _setBodyUsed(e) {
    this.bodyUsed = e;
  }
  blob() {
    const e = W(this);
    if (e) return e;
    if (this._bodyBlob) return Promise.resolve(this._bodyBlob);
    if (this._bodyArrayBuffer) return Promise.resolve(new __sharedGlobals.Blob([this._bodyArrayBuffer]));
    if (this._bodyFormData) throw new Error("could not read FormData body as blob");
    return Promise.resolve(new __sharedGlobals.Blob([this._bodyText]));
  }
  arrayBuffer() {
    if (this._bodyArrayBuffer) {
      const e = W(this);
      return e || (ArrayBuffer.isView(this._bodyArrayBuffer) ? Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset, this._bodyArrayBuffer.byteOffset + this._bodyArrayBuffer.byteLength)) : Promise.resolve(this._bodyArrayBuffer));
    }
    return this.blob().then($);
  }
  text() {
    const e = W(this);
    if (e) return e;
    if (this._bodyBlob) return function(e2) {
      const t = new __sharedGlobals.FileReader(), r = z(t);
      return t.readAsText(e2), r;
    }(this._bodyBlob);
    if (this._bodyArrayBuffer) return Promise.resolve(function(e2) {
      const t = new Uint8Array(e2), r = new Array(t.length);
      for (let e3 = 0; e3 < t.length; e3++) r[e3] = String.fromCharCode(t[e3]);
      return r.join("");
    }(this._bodyArrayBuffer));
    if (this._bodyFormData) throw new Error("could not read FormData body as text");
    return Promise.resolve(this._bodyText);
  }
  formData() {
    return this.text().then(V);
  }
  json() {
    return this.text().then((e = "") => JSON.parse(e));
  }
}
class J extends Z {
  constructor(e, t = {}) {
    super(), Object.defineProperty(this, "body", { enumerable: true, configurable: true, writable: true, value: null }), Object.defineProperty(this, "bodyUsed", { enumerable: true, configurable: true, writable: true, value: false }), Object.defineProperty(this, "cache", { enumerable: true, configurable: true, writable: true, value: "default" }), Object.defineProperty(this, "credentials", { enumerable: true, configurable: true, writable: true, value: "same-origin" }), Object.defineProperty(this, "headers", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "integrity", { enumerable: true, configurable: true, writable: true, value: "" }), Object.defineProperty(this, "method", { enumerable: true, configurable: true, writable: true, value: "GET" }), Object.defineProperty(this, "mode", { enumerable: true, configurable: true, writable: true, value: "cors" }), Object.defineProperty(this, "redirect", { enumerable: true, configurable: true, writable: true, value: "follow" }), Object.defineProperty(this, "referrer", { enumerable: true, configurable: true, writable: true, value: "about:client" }), Object.defineProperty(this, "referrerPolicy", { enumerable: true, configurable: true, writable: true, value: "" }), Object.defineProperty(this, "signal", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "url", { enumerable: true, configurable: true, writable: true, value: void 0 });
    let r = t.body;
    if (e instanceof J) {
      if (e.bodyUsed) throw new TypeError("Already read");
      this.url = e.url, this.credentials = e.credentials, t.headers || (this.headers = new q(e.headers)), this.method = e.method, this.mode = e.mode, this.signal = e.signal, r || null == e._bodyInit || (r = e._bodyInit, e._setBodyUsed(true));
    } else this.url = String(e);
    if (this.credentials = t.credentials || this.credentials || "same-origin", this.headers = this.headers || new q(t.headers), this.method = function(e2) {
      const t2 = e2.toUpperCase();
      return F.indexOf(t2) > -1 ? t2 : e2;
    }(t.method || this.method || "GET"), this.mode = t.mode || this.mode || null, this.signal = t.signal || new __sharedGlobals.AbortController().signal, ("GET" === this.method || "HEAD" === this.method) && r) throw new TypeError("Body not allowed for GET or HEAD requests");
    if (this._initBody(r), !("GET" !== this.method && "HEAD" !== this.method || "no-store" !== t.cache && "no-cache" !== t.cache)) {
      const e2 = /([?&])_=[^&]*/;
      if (e2.test(this.url))
        this.url = this.url.replace(e2, "$1_=" + (/* @__PURE__ */ new Date()).getTime());
      else {
        const e3 = /\?/;
        this.url += (e3.test(this.url) ? "&" : "?") + "_=" + (/* @__PURE__ */ new Date()).getTime();
      }
    }
  }
  clone() {
    return new J(this, { body: this._bodyInit });
  }
}
const Q = [301, 302, 303, 307, 308];
class ee extends Z {
  constructor(e, t) {
    if (super(), Object.defineProperty(this, "bodyUsed", { enumerable: true, configurable: true, writable: true, value: false }), Object.defineProperty(this, "headers", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "ok", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "redirected", { enumerable: true, configurable: true, writable: true, value: false }), Object.defineProperty(this, "status", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "statusText", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "type", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "url", { enumerable: true, configurable: true, writable: true, value: "" }), !(this instanceof ee)) throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');
    if (t || (t = {}), this.type = "default", this.status = void 0 === t.status ? 200 : t.status, this.status < 200 || this.status > 599) throw new RangeError("Failed to construct 'Response': The status provided (0) is outside the range [200, 599].");
    this.ok = this.status >= 200 && this.status < 300, this.statusText = void 0 === t.statusText ? "" : "" + t.statusText, this.headers = new q(t.headers), this.url = t.url || "", this._initBody(e);
  }
  clone() {
    return new ee(this._bodyInit, { status: this.status, statusText: this.statusText, headers: new q(this.headers), url: this.url });
  }
  static error() {
    const e = new ee(null, { status: 200, statusText: "" });
    return e.ok = false, e.status = 0, e.type = "error", e;
  }
  static redirect(e, t) {
    if (t && -1 === Q.indexOf(t)) throw new RangeError("Invalid status code");
    return new ee(null, { status: t, headers: { location: e } });
  }
}
function te(e) {
  const t = new q();
  return e.replace(/\r?\n[\t ]+/g, " ").split("\r").map(function(e2) {
    return 0 === e2.indexOf("\n") ? e2.substr(1, e2.length) : e2;
  }).forEach(function(e2) {
    const r = e2.split(":"), i = r.shift().trim();
    if (i) {
      const e3 = r.join(":").trim();
      try {
        t.append(i, e3);
      } catch (e4) {
        console.warn("Response " + e4.message);
      }
    }
  }), t;
}
function re(e, t) {
  return new Promise(function(r, i) {
    const s2 = new J(e, t);
    if (s2.signal && s2.signal.aborted) return i(new __sharedGlobals.DOMException("Aborted", "AbortError"));
    const a2 = new __sharedGlobals.XMLHttpRequest();
    function n2() {
      a2.abort();
    }
    a2.onload = function() {
      const e2 = { statusText: a2.statusText, headers: te(a2.getAllResponseHeaders() || "") };
      0 === s2.url.indexOf("file://") && (a2.status < 200 || a2.status > 599) ? e2.status = 200 : e2.status = a2.status, e2.url = a2.responseURL;
      const t2 = a2.response;
      setTimeout(function() {
        r(new ee(t2, e2));
      }, 0);
    }, a2.onerror = function() {
      setTimeout(function() {
        i(new TypeError("Network request failed"));
      }, 0);
    }, a2.ontimeout = function() {
      setTimeout(function() {
        i(new TypeError("Network request timed out"));
      }, 0);
    }, a2.onabort = function() {
      setTimeout(function() {
        i(new __sharedGlobals.DOMException("Aborted", "AbortError"));
      }, 0);
    }, a2.open(s2.method, s2.url, true), "include" === s2.credentials ? a2.withCredentials = true : "omit" === s2.credentials && (a2.withCredentials = false), a2.responseType = "blob", s2.headers.forEach(function(e2, t2) {
      a2.setRequestHeader(t2, e2);
    }), s2.signal && (s2.signal.addEventListener("abort", n2), a2.onreadystatechange = function() {
      4 === a2.readyState && s2.signal.removeEventListener("abort", n2);
    }), //@ts-expect-error ignore type
    a2.send(void 0 === s2._bodyInit ? null : s2._bodyInit);
  });
}
class ie {
  decode(e) {
    let t;
    t = e instanceof ArrayBuffer ? new Uint8Array(e) : e;
    const r = t.length, i = [];
    let s2 = 0;
    for (; s2 < r; ) {
      const e2 = t[s2];
      let a3 = null, n3 = e2 > 239 ? 4 : e2 > 223 ? 3 : e2 > 191 ? 2 : 1;
      if (s2 + n3 <= r) {
        let r2, i2, o2, l2;
        switch (n3) {
          case 1:
            e2 < 128 && (a3 = e2);
            break;
          case 2:
            r2 = t[s2 + 1], 128 == (192 & r2) && (l2 = (31 & e2) << 6 | 63 & r2, l2 > 127 && (a3 = l2));
            break;
          case 3:
            r2 = t[s2 + 1], i2 = t[s2 + 2], 128 == (192 & r2) && 128 == (192 & i2) && (l2 = (15 & e2) << 12 | (63 & r2) << 6 | 63 & i2, l2 > 2047 && (l2 < 55296 || l2 > 57343) && (a3 = l2));
            break;
          case 4:
            r2 = t[s2 + 1], i2 = t[s2 + 2], o2 = t[s2 + 3], 128 == (192 & r2) && 128 == (192 & i2) && 128 == (192 & o2) && (l2 = (15 & e2) << 18 | (63 & r2) << 12 | (63 & i2) << 6 | 63 & o2, l2 > 65535 && l2 < 1114112 && (a3 = l2));
        }
      }
      null === a3 ? (
        // we did not generate a valid codePoint so insert a
        // replacement char (U+FFFD) and advance only 1 byte
        (a3 = 65533, n3 = 1)
      ) : a3 > 65535 && // encode to utf16 (surrogate pair dance)
      (a3 -= 65536, i.push(a3 >>> 10 & 1023 | 55296), a3 = 56320 | 1023 & a3), i.push(a3), s2 += n3;
    }
    const a2 = i.length;
    let n2 = "";
    for (s2 = 0; s2 < a2; ) n2 += String.fromCharCode(...i.slice(s2, s2 += 4096));
    return n2;
  }
}
n("TextDecoder", ie);
class se {
  encode(e) {
    let t = 0;
    const r = e.length, i = Uint8Array;
    let s2 = 0, a2 = Math.max(32, r + (r >> 1) + 7), n2 = new i(a2 >> 3 << 3);
    for (; t < r; ) {
      let i2 = e.charCodeAt(t++);
      if (i2 >= 55296 && i2 <= 56319) {
        if (t < r) {
          const r2 = e.charCodeAt(t);
          56320 == (64512 & r2) && (++t, i2 = ((1023 & i2) << 10) + (1023 & r2) + 65536);
        }
        if (i2 >= 55296 && i2 <= 56319) continue;
      }
      if (s2 + 4 > n2.length) {
        a2 += 8, // minimum extra
        a2 *= 1 + t / e.length * 2, // take 2x the remaining
        a2 = a2 >> 3 << 3;
        const r2 = new Uint8Array(a2);
        r2.set(n2), n2 = r2;
      }
      if (4294967168 & i2) {
        if (4294965248 & i2) if (4294901760 & i2) {
          if (4292870144 & i2)
            continue;
          n2[s2++] = i2 >> 18 & 7 | 240, n2[s2++] = i2 >> 12 & 63 | 128, n2[s2++] = i2 >> 6 & 63 | 128;
        } else
          n2[s2++] = i2 >> 12 & 15 | 224, n2[s2++] = i2 >> 6 & 63 | 128;
        else
          n2[s2++] = i2 >> 6 & 31 | 192;
        n2[s2++] = 63 & i2 | 128;
      } else
        n2[s2++] = i2;
    }
    return n2.slice(0, s2);
  }
}
n("TextEncoder", se);
class ae {
  constructor() {
  }
  static createObjectURL(e) {
    if (!(e instanceof C)) throw new TypeError("Failed to execute 'createObjectURL' on 'URL': parameter 1 is not of type 'Blob'.");
    return le(e);
  }
  static revokeObjectURL(e) {
    oe.has(e) && oe.delete(e);
  }
}
const ne = "blob:https://mock.by.createObjectURL", oe = /* @__PURE__ */ new Map(), le = (e) => {
  const t = `${ne}/${(() => {
    const e2 = Date.now(), t2 = Math.random().toString(16).slice(2);
    return `${e2.toString(16)}-${t2.slice(0, 4)}-${t2.slice(4, 8)}-${t2.slice(8, 12)}-${t2.slice(12, 16)}`;
  })()}`;
  return oe.set(t, e), t;
};
function ue(e) {
  return oe.get(e) || null;
}
function ce(e) {
  const t = ue(e);
  return t ? "data:" + t.type + ";base64," + T(t._buffer) : null;
}
function he(e) {
  const t = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
  let r, i, s2, a2, n2, o2, l2, u2 = "", c2 = 0;
  e = e.replace(/[^A-Za-z0-9\+\/\=]/g, "");
  do {
    a2 = t.indexOf(e.charAt(c2++)), n2 = t.indexOf(e.charAt(c2++)), o2 = t.indexOf(e.charAt(c2++)), l2 = t.indexOf(e.charAt(c2++)), r = a2 << 2 | n2 >> 4, i = (15 & n2) << 4 | o2 >> 2, s2 = (3 & o2) << 6 | l2, u2 += String.fromCharCode(r), 64 !== o2 && (u2 += String.fromCharCode(i)), 64 !== l2 && (u2 += String.fromCharCode(s2));
  } while (c2 < e.length);
  return u2;
}
function de(e) {
  e = String(e);
  const t = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
  let r = "", i = "", s2 = 0, a2 = 0;
  for (let i2 = 0; i2 < e.length; i2++)
    for (s2 = s2 << 8 | e.charCodeAt(i2), // 将字符的 ASCII 码放入缓冲区
    a2 += 8; a2 >= 6; ) {
      r += t[s2 >> a2 - 6 & 63], a2 -= 6;
    }
  return a2 > 0 && (r += t[s2 << 6 - a2 & 63], // 补齐剩余位数并添加到输出
  i = "=".repeat((6 - a2) / 2)), r + i;
}
let be = null;
class fe {
  constructor() {
    if (Object.defineProperty(this, "borderBoxSize", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "contentBoxSize", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "contentRect", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "devicePixelContentBoxSize", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "target", { enumerable: true, configurable: true, writable: true, value: void 0 }), !be) throw new TypeError("Illegal constructor");
    this.borderBoxSize = be.borderBoxSize, this.devicePixelContentBoxSize = be.devicePixelContentBoxSize, this.contentBoxSize = be.devicePixelContentBoxSize, this.contentRect = be.contentRect, this.target = be.target, be = null;
  }
}
const pe = /* @__PURE__ */ new WeakMap();
class ye {
  static getResizeObserver(e) {
    return pe.has(e) && pe.get(e) || [];
  }
  constructor(e) {
    Object.defineProperty(this, "polyfill", { enumerable: true, configurable: true, writable: true, value: "polyfill by @minisheep/mini-program-polyfill-core" }), Object.defineProperty(this, "callback", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "elements", { enumerable: true, configurable: true, writable: true, value: /* @__PURE__ */ new Map() }), this.callback = e;
  }
  observe(e, t = {}) {
    if (!this.elements.has(e)) if (this.elements.set(e, t), pe.has(e)) {
      const t2 = pe.get(e) || [];
      pe.set(e, [...t2, this]);
    } else pe.set(e, [this]);
  }
  unobserve(e) {
    if (this.elements.has(e) && (this.elements.delete(e), pe.has(e))) {
      const t = pe.get(e) || [];
      pe.set(e, t.filter((e2) => e2 !== this));
    }
  }
  disconnect() {
    this.elements.clear();
  }
  _getElements() {
    return this.elements.keys();
  }
  _getObserveOptions(e) {
    if (this.elements.has(e)) return this.elements.get(e);
  }
  _trigger(e) {
    const t = this.elements, r = Array.from(t.keys()), i = e.filter((e2) => r.includes(e2.target)).map((e2) => (be = e2, new fe()));
    i.length && this.callback(i, this);
  }
}
const ve = { AbortController: v, AbortSignal: _, Blob: C, DOMException: G, Event: _mpChunkDeps_eventTargetShim_index.Event, EventTarget: _mpChunkDeps_eventTargetShim_index.EventTarget, File: S, FileReader: U, FormData: X, Headers: q, KeyboardEvent: p, MouseEvent: b, PointerEvent: f, ProgressEvent: o, Request: J, ReadableStream: _mpChunkDeps_webStreamsPolyfill_dist_ponyfill.ReadableStream, Response: ee, TextDecoder: ie, TextEncoder: se, Touch: u, TouchEvent: d, UIEvent: c, URL: ae, atob: __sharedGlobals.atob, btoa: de, fetch: re, ResizeObserver: ye, ResizeObserverEntry: fe };
function ge(e, t, r, i) {
  if ("function" == typeof t ? e !== t || !i : !t.has(e)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
  return "m" === r ? i : "a" === r ? i.call(e) : i ? i.value : t.get(e);
}
function me(e, t, r, i, s2) {
  if ("function" == typeof t ? e !== t || true : !t.has(e)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
  return t.set(e, r), r;
}
"function" == typeof SuppressedError && SuppressedError;
class we extends _mpChunkDeps_eventTargetShim_index.EventTarget {
  constructor() {
    super(), Object.defineProperty(this, "onabort", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "onerror", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "onload", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "onloadstart", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "onprogress", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "onloadend", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "ontimeout", { enumerable: true, configurable: true, writable: true, value: void 0 }), _mpChunkDeps_eventTargetShim_index.setEventAttributeValue(this, "abort", (e) => {
      var t;
      return null === (t = this.onabort) || void 0 === t ? void 0 : t.call(this, e);
    }), _mpChunkDeps_eventTargetShim_index.setEventAttributeValue(this, "error", (e) => {
      var t;
      return null === (t = this.onerror) || void 0 === t ? void 0 : t.call(this, e);
    }), _mpChunkDeps_eventTargetShim_index.setEventAttributeValue(this, "load", (e) => {
      var t;
      return null === (t = this.onload) || void 0 === t ? void 0 : t.call(this, e);
    }), _mpChunkDeps_eventTargetShim_index.setEventAttributeValue(this, "loadstart", (e) => {
      var t;
      return null === (t = this.onloadstart) || void 0 === t ? void 0 : t.call(this, e);
    }), _mpChunkDeps_eventTargetShim_index.setEventAttributeValue(this, "progress", (e) => {
      var t;
      return null === (t = this.onprogress) || void 0 === t ? void 0 : t.call(this, e);
    }), _mpChunkDeps_eventTargetShim_index.setEventAttributeValue(this, "loadend", (e) => {
      var t;
      return null === (t = this.onloadend) || void 0 === t ? void 0 : t.call(this, e);
    }), _mpChunkDeps_eventTargetShim_index.setEventAttributeValue(this, "timeout", (e) => {
      var t;
      return null === (t = this.ontimeout) || void 0 === t ? void 0 : t.call(this, e);
    });
  }
}
var _e, Pe, Oe, Ee, je, Te, Re, Ae, xe, Ce, Se, Be, Me, Ie, Ue;
const ke = 0, De = 1, Ge = 2, Le = 3, Ne = 4, Xe = { 100: "Continue", 101: "Switching Protocol", 102: "Processing", 200: "OK", 201: "Created", 202: "Accepted", 203: "Non-Authoritative Information", 204: "No Content", 205: "Reset Content", 206: "Partial Content", 207: "Multi-Status", 208: "Multi-Status", 226: "IM Used", 300: "Multiple Choice", 301: "Moved Permanently", 302: "Found", 303: "See Other", 304: "Not Modified", 305: "Use Proxy", 306: "unused", 307: "Temporary Redirect", 308: "Permanent Redirect", 400: "Bad Request", 401: "Unauthorized", 402: "Payment Required", 403: "Forbidden", 404: "Not Found", 405: "Method Not Allowed", 406: "Not Acceptable", 407: "Proxy Authentication Required", 408: "Request Timeout", 409: "Conflict", 410: "Gone", 411: "Length Required", 412: "Precondition Failed", 413: "Payload Too Large", 414: "URI Too Long", 415: "Unsupported Media Type", 416: "Requested Range Not Satisfiable", 417: "Expectation Failed", 418: "I'm a teapot", 421: "Misdirected Request", 422: "Unprocessable Entity", 423: "Locked", 424: "Failed Dependency", 426: "Upgrade Required", 428: "Precondition Required", 429: "Too Many Requests", 431: "Request Header Fields Too Large", 451: "Unavailable For Legal Reasons", 500: "Internal Server Error", 501: "Not Implemented", 502: "Bad Gateway", 503: "Service Unavailable", 504: "Gateway Timeout", 505: "HTTP Version Not Supported", 506: "Variant Also Negotiates", 507: "Insufficient Storage", 508: "Loop Detected", 510: "Not Extended", 511: "Network Authentication Required" };
function Ke(e) {
  const t = {};
  for (const r in e) e.hasOwnProperty(r) && (t[r.toLowerCase()] = e[r]);
  return t;
}
const Ye = /* @__PURE__ */ ["Accept-Charset", "Accept-Encoding", "Access-Control-Request-Headers", "Access-Control-Request-Method", "Connection", "Content-Length", "Cookie", "Cookie2", "Date", "DNT", "Expect", "Permissions-Policy", "Host", "Keep-Alive", "Origin", "Proxy-", "Sec-", "Referer", "TE", "Trailer", "Transfer-Encoding", "Upgrade", "Via"].map((e) => e.toLowerCase()).map((e) => e.trim());
_e = /* @__PURE__ */ new WeakMap(), Pe = /* @__PURE__ */ new WeakMap(), Oe = /* @__PURE__ */ new WeakMap(), Ee = /* @__PURE__ */ new WeakMap(), je = /* @__PURE__ */ new WeakMap(), Te = /* @__PURE__ */ new WeakMap(), Re = /* @__PURE__ */ new WeakMap(), Ae = /* @__PURE__ */ new WeakMap(), xe = /* @__PURE__ */ new WeakMap(), Ce = /* @__PURE__ */ new WeakMap(), Se = /* @__PURE__ */ new WeakMap(), Be = /* @__PURE__ */ new WeakMap(), Me = /* @__PURE__ */ new WeakMap(), Ie = /* @__PURE__ */ new WeakMap(), Ue = /* @__PURE__ */ new WeakMap();
const Fe = class extends we {
  constructor() {
    super(), // not standard prop
    _e.set(this, void 0), Pe.set(this, void 0), Oe.set(this, {}), Ee.set(this, {}), je.set(this, void 0), Te.set(this, false), Re.set(this, ke), Ae.set(this, false), xe.set(this, ""), Ce.set(this, 0), // this is WeChat app's request task return, for abort the request
    Se.set(this, null), // be like readystate 1.5
    Be.set(this, false), Me.set(this, null), Ie.set(this, 0), Ue.set(this, false), Object.defineProperty(this, "onreadystatechange", { enumerable: true, configurable: true, writable: true, value: void 0 }), // The browser does not support "addEventListener('readystatechange')", But will support here
    _mpChunkDeps_eventTargetShim_index.setEventAttributeValue(this, "readystatechange", (e) => {
      var t;
      return null === (t = this.onreadystatechange) || void 0 === t ? void 0 : t.call(this, e);
    });
  }
  get readyState() {
    return ge(this, Re, "f");
  }
  get withCredentials() {
    return ge(this, Ae, "f");
  }
  set withCredentials(e) {
    console.warn("withCredentials is unuseful in mini-program env"), me(this, Ae, e);
  }
  get response() {
    return ge(this, Me, "f");
  }
  get responseText() {
    if ("" !== ge(this, xe, "f") && "text" !== ge(this, xe, "f")) throw s(`Failed to read the 'responseText' property from 'XMLHttpRequest': The value is only accessible if the object's 'responseType' is '' or 'text' (was '${ge(this, xe, "f")}').`);
    return "object" == typeof ge(this, Me, "f") ? JSON.stringify(ge(this, Me, "f")) : ge(this, Me, "f");
  }
  get responseURL() {
    return ge(this, _e, "f");
  }
  get responseXML() {
    if ("" !== ge(this, xe, "f") && "document" !== ge(this, xe, "f")) throw s(`Failed to read the 'responseXML' property from 'XMLHttpRequest': The value is only accessible if the object's 'responseType' is '' or 'document' (was '${ge(this, xe, "f")}').`);
    throw Error("not supported in mini-program");
  }
  get timeout() {
    return ge(this, Ce, "f");
  }
  set timeout(e) {
    me(this, Ce, e);
  }
  get status() {
    return ge(this, Ie, "f");
  }
  get statusText() {
    return Xe[this.status] || "unknown";
  }
  set responseType(e) {
    if (this.readyState != De || this.readyState == De && !ge(this, Be, "f")) throw s("Failed to set 'responseType' on 'XMLHttpRequest': The object's state must be OPENED.");
    me(this, xe, e);
  }
  get responseType() {
    return ge(this, xe, "f");
  }
  get upload() {
    return ge(this, je, "f");
  }
  /**
       * todo
       * override mime type, not support yet
       * @param mimetype
       */
  overrideMimeType(e) {
    this.readyState;
  }
  /**
       * fake to open the server
       * @param method
       * @param url
       * will ignore in mini-program ↓
       * @param async
       * @param user
       * @param password
       */
  open(t, r, i = true, s2, a2) {
    this.readyState >= De && this.abort(), me(this, Pe, t), me(this, _e, r), me(this, Re, De), me(this, Be, true), this.dispatchEvent(new _mpChunkDeps_eventTargetShim_index.Event("readystatechange"));
  }
  /**
       * send data
       * @param data
       * application/json use wx.request
       * application/x-www-form-urlencoded use wx.request
       * multipart/form-data use wx.upload
       */
  send(t) {
    var r, i;
    if (ge(this, Re, "f") !== De) throw s("Failed to execute 'send' on 'XMLHttpRequest': The object's state must be OPENED.");
    if (true === ge(this, Te, "f")) return;
    if (ge(this, Ue, "f")) return;
    me(this, Be, false);
    const a2 = ge(this, _e, "f"), n2 = null === (r = ge(this, Pe, "f")) || void 0 === r ? void 0 : r.toUpperCase(), o2 = { ...ge(this, Oe, "f"), accept: null !== (i = ge(this, Oe, "f").accept) && void 0 !== i ? i : "*/*" }, l2 = 0 === ge(this, Ce, "f") ? 1 / 0 : ge(this, Ce, "f");
    let u2, c2 = "其他";
    switch (ge(this, xe, "f")) {
      case "":
      case "text":
      case "json":
        u2 = "text", "json" === ge(this, xe, "f") && (c2 = "json");
        break;
      case "arraybuffer":
      case "blob":
        u2 = "arraybuffer";
        break;
      case "document":
        throw s("Unsupported responseType 'document' in mini-program");
    }
    if (me(this, Ue, false), this.dispatchEvent(new __sharedGlobals.ProgressEvent("loadstart")), a2.startsWith(ne)) {
      const t2 = ue(a2);
      return void (t2 ? setTimeout(() => {
        me(this, Re, Ge), this.dispatchEvent(new _mpChunkDeps_eventTargetShim_index.Event("readystatechange")), me(this, Ie, 200), me(this, Ee, { "content-type": t2.type }), this.dispatchEvent(new __sharedGlobals.ProgressEvent("progress")), me(this, Re, Le), this.dispatchEvent(new _mpChunkDeps_eventTargetShim_index.Event("readystatechange")), me(this, Me, t2.slice()), me(this, Re, Ne), this.dispatchEvent(new _mpChunkDeps_eventTargetShim_index.Event("readystatechange")), this.dispatchEvent(new __sharedGlobals.ProgressEvent("load")), this.dispatchEvent(new __sharedGlobals.ProgressEvent("loadend")), me(this, Ue, true);
      }, 0) : (me(this, Re, Ne), this.dispatchEvent(new _mpChunkDeps_eventTargetShim_index.Event("readystatechange")), this.dispatchEvent(new __sharedGlobals.ProgressEvent("error")), this.dispatchEvent(new __sharedGlobals.ProgressEvent("loadend")), me(this, Ue, true)));
    }
    let h2 = false;
    const d2 = new Promise((r2, i2) => {
      const s2 = _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.wx$1.request({ url: a2, method: n2, header: o2, data: t, dataType: c2, responseType: u2, timeout: l2, success: r2, fail: i2 }), d3 = (t2) => {
        var r3;
        ge(this, Te, "f") || ge(this, Ue, "f") || (me(this, Re, Ge), this.dispatchEvent(new _mpChunkDeps_eventTargetShim_index.Event("readystatechange")), me(this, Ie, t2.statusCode), me(this, Ee, Ke(t2.header)), null === (r3 = ge(this, Se, "f")) || void 0 === r3 || r3.offHeadersReceived(d3), h2 = true);
      };
      s2.onHeadersReceived(d3), me(this, Se, s2);
    });
    d2.then((t2) => {
      ge(this, Te, "f") || (h2 || (me(this, Ie, t2.statusCode), me(this, Ee, Ke(t2.header))), this.dispatchEvent(new __sharedGlobals.ProgressEvent("progress")), me(this, Re, Le), this.dispatchEvent(new _mpChunkDeps_eventTargetShim_index.Event("readystatechange")), "blob" === ge(this, xe, "f") ? me(this, Me, new C([t2.data], { type: ge(this, Ee, "f")["content-type"] })) : me(this, Me, void 0 === t2.data ? null : t2.data), t2.statusCode >= 400 && console.error(`${ge(this, Pe, "f")} ${ge(this, _e, "f")} ${t2.statusCode}`), me(this, Re, Ne), this.dispatchEvent(new _mpChunkDeps_eventTargetShim_index.Event("readystatechange")), this.dispatchEvent(new __sharedGlobals.ProgressEvent("load")));
    }).catch((t2) => {
      switch (me(this, Re, Ne), this.dispatchEvent(new _mpChunkDeps_eventTargetShim_index.Event("readystatechange")), t2.errno) {
        case 1:
          this.dispatchEvent(new __sharedGlobals.ProgressEvent("abort"));
          break;
        case 5:
          this.dispatchEvent(new __sharedGlobals.ProgressEvent("timeout"));
          break;
        default:
          if (ge(this, Te, "f")) return;
          me(this, Me, t2), this.dispatchEvent(new __sharedGlobals.ProgressEvent("error"));
      }
    }).finally(() => {
      me(this, Ue, true), me(this, Se, null), this.dispatchEvent(new __sharedGlobals.ProgressEvent("loadend"));
    });
  }
  /**
       * abort the request after send
       */
  abort() {
    ge(this, Te, "f") || ge(this, Ue, "f") || (ge(this, Se, "f") && ge(this, Se, "f").abort(), me(this, Te, true), this.dispatchEvent(new _mpChunkDeps_eventTargetShim_index.Event("abort")));
  }
  /**
       * set request header
       * @param header
       * @param value
       */
  setRequestHeader(e, t) {
    if (this.readyState != De || this.readyState == De && !ge(this, Be, "f")) throw s("Failed to execute 'setRequestHeader' on 'XMLHttpRequest': The object's state must be OPENED.");
    if (Ye.findIndex((t2) => t2.trim() === e) >= 0)
      return;
    const r = e.toLowerCase();
    ge(this, Oe, "f")[r] = (ge(this, Oe, "f")[r] ? ge(this, Oe, "f")[r] + "," : "") + t;
  }
  /**
       * get response header
       * @param header
       */
  getResponseHeader(e) {
    var t;
    return null !== (t = ge(this, Ee, "f")[e.toLowerCase()]) && void 0 !== t ? t : null;
  }
  /**
       * get all response header string
       */
  getAllResponseHeaders() {
    return Object.entries(Ke(ge(this, Ee, "f"))).reduce((e, [t, r]) => (e.push(`${t.toLowerCase()}: ${r}`), e), []).join("\r\n");
  }
}, qe = /* @__PURE__ */ Object.assign(__sharedGlobals, { ...ve, XMLHttpRequest: Fe });
exports.C = C;
exports.R = R;
exports.ce = ce;
exports.f = f;
exports.he = he;
exports.ne = ne;
exports.qe = qe;
exports.y = y;
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/_mpChunkDeps/@minisheep/mini-program-polyfill-core/dist/wechat.js.map
