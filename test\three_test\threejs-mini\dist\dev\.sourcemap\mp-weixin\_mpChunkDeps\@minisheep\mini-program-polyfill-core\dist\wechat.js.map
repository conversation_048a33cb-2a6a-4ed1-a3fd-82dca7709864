{"version": 3, "file": "wechat.js", "sources": ["../node_modules/@minisheep/mini-program-polyfill-core/dist/wechat.js"], "sourcesContent": ["/* inject by plugin [minisheep:prefix-global]*/globalThis.__sharedGlobals=(()=>{if(globalThis.__sharedGlobals){if(!globalThis.__sharedGlobals.__prefix_global__)throw new Error(\"[plugin:prefix-global] inject global property fail, key [__sharedGlobals] is already defined.\");return globalThis.__sharedGlobals}const e=JSON.parse(\"[]\"),t=new Proxy({__prefix_global__:!0},{get:(r,i)=>e.includes(i)?void 0:Object.hasOwn(r,i)?Reflect.get(r,i):\"self\"===i?t:Reflect.get(globalThis,i),ownKeys:e=>Array.from(new Set([Reflect.ownKeys(e),Reflect.ownKeys(globalThis)].flat())),has:(e,t)=>Reflect.has(e,t)||Reflect.has(globalThis,t)});return t})();import{Event as e,EventTarget as t,setEventAttributeValue as r}from\"event-target-shim\";export{Event,EventTarget,setEventAttributeValue}from\"event-target-shim\";import{ReadableStream as i}from\"web-streams-polyfill\";export{ReadableStream}from\"web-streams-polyfill\";function s(e){return new __sharedGlobals.DOMException(e,\"InvalidStateError\")}function a(e){const t=new WeakMap;return{weakMap:t,get(r){const i=t.get(r);if(null==i)throw TypeError(`Illegal invocation at ${e}.invokeGetter`);return i}}}function n(e,t){const r=\"undefined\"!=typeof globalThis&&globalThis||\n// @ts-expect-error self\nvoid 0!==__sharedGlobals.self&&__sharedGlobals.self||\n// @ts-expect-error global\n\"undefined\"!=typeof global&&global||{};r.hasOwnProperty(e)||(r[e]=t)}class o extends e{constructor(e,t){super(e,t),l.weakMap.set(this,{lengthComputable:!1,loaded:0,total:0})}get lengthComputable(){return l.get(this).lengthComputable}get loaded(){return l.get(this).loaded}get total(){return l.get(this).total}}const l=a(o.name);class u{constructor(e){Object.defineProperty(this,\"identifier\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"target\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"clientX\",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,\"clientY\",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,\"force\",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,\"pageX\",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,\"pageY\",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,\"screenX\",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,\"screenY\",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,\"radiusX\",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,\"radiusY\",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,\"rotationAngle\",{enumerable:!0,configurable:!0,writable:!0,value:0}),this.identifier=e.identifier,this.target=e.target,e.clientX&&(this.clientX=e.clientX),e.clientY&&(this.clientY=e.clientY),e.force&&(this.force=e.force),e.pageX&&(this.pageX=e.pageX),e.pageY&&(this.pageY=e.pageY),e.screenX&&(this.screenX=e.screenX),e.screenY&&(this.screenY=e.screenY),e.radiusX&&(this.radiusX=e.radiusX),e.radiusY&&(this.radiusY=e.radiusY),e.rotationAngle&&(this.rotationAngle=e.rotationAngle)}}class c extends e{constructor(e,t={}){const{detail:r=0,view:i=null,...s}=t;super(e,s),Object.defineProperty(this,\"detail\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"view\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.detail=r,this.view=i}}\n/** @__NO_SIDE_EFFECTS__ */function h(e){const t=e;return t.item||(t.item=function(e){return t[e]||null}),t}class d extends c{constructor(e,t={}){const{changedTouches:r=[],targetTouches:i=[],touches:s=[],altKey:a=!1,ctrlKey:n=!1,metaKey:o=!1,shiftKey:l=!1,...u}=t;super(e,u),Object.defineProperty(this,\"altKey\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"ctrlKey\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"metaKey\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"shiftKey\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"changedTouches\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"targetTouches\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"touches\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.altKey=a,this.ctrlKey=n,this.metaKey=o,this.shiftKey=l,this.changedTouches=h(r),this.targetTouches=h(i),this.touches=h(s)}}class b extends c{constructor(e,t={}){const{button:r=0,buttons:i=0,clientX:s=0,clientY:a=0,movementX:n=0,movementY:o=0,relatedTarget:l=null,screenX:u=0,screenY:c=0,altKey:h=!1,ctrlKey:d=!1,metaKey:b=!1,shiftKey:f=!1,offsetY:p=0,offsetX:y=0,...v}=t;super(e,v),Object.defineProperty(this,\"altKey\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"ctrlKey\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"metaKey\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"shiftKey\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),\n/** alias for clientX*/\nObject.defineProperty(this,\"x\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"y\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"clientX\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"clientY\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"screenX\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"screenY\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"movementX\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"movementY\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"button\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"buttons\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"relatedTarget\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"pageX\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"pageY\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"offsetX\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"offsetY\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.altKey=h,this.ctrlKey=d,this.metaKey=b,this.shiftKey=f,this.x=s,this.y=a,this.buttons=i,this.button=r,this.clientX=s,this.clientY=a,this.screenX=u,this.screenY=c,this.movementX=n,this.movementY=o,this.pageX=s,this.pageY=a,this.offsetX=y,this.offsetY=p,this.relatedTarget=l}}class f extends b{constructor(e,t={}){const{pointerId:r=0,width:i=1,height:s=1,pressure:a=0,tangentialPressure:n=0,tiltX:o=0,tiltY:l=0,twist:u=0,pointerType:c=\"\",isPrimary:h=!1,...d}=t;super(e,d),Object.defineProperty(this,\"pointerId\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"width\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"height\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"pressure\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"tangentialPressure\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"tiltX\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"tiltY\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"twist\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"pointerType\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"isPrimary\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.pointerId=r,this.width=i,this.height=s,this.pressure=Math.min(1,Math.max(a,0)),this.tangentialPressure=Math.min(1,Math.max(n,-1)),this.tiltX=o,this.tiltY=l,this.twist=u,this.pointerType=c,this.isPrimary=h}getCoalescedEvents(){return[]}getPredictedEvents(){return[]}}const p=class extends c{constructor(e,t={}){const{altKey:r=!1,ctrlKey:i=!1,metaKey:s=!1,shiftKey:a=!1,charCode:n=0,code:o=\"\",isComposing:l=!1,key:u=\"\",keyCode:c=0,location:h=0,repeat:d=!1,...b}=t;super(e,b),Object.defineProperty(this,\"altKey\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"ctrlKey\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"metaKey\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"shiftKey\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),\n/**\n         * @deprecated\n         * [MDN Reference](https://developer.mozilla.org/docs/Web/API/KeyboardEvent/charCode)\n         */\nObject.defineProperty(this,\"charCode\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),\n/** [MDN Reference](https://developer.mozilla.org/docs/Web/API/KeyboardEvent/code) */\nObject.defineProperty(this,\"code\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),\n/** [MDN Reference](https://developer.mozilla.org/docs/Web/API/KeyboardEvent/isComposing) */\nObject.defineProperty(this,\"isComposing\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),\n/** [MDN Reference](https://developer.mozilla.org/docs/Web/API/KeyboardEvent/key) */\nObject.defineProperty(this,\"key\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),\n/**\n         * @deprecated\n         *\n         * [MDN Reference](https://developer.mozilla.org/docs/Web/API/KeyboardEvent/keyCode)\n         */\nObject.defineProperty(this,\"keyCode\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),\n/** [MDN Reference](https://developer.mozilla.org/docs/Web/API/KeyboardEvent/location) */\nObject.defineProperty(this,\"location\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),\n/** [MDN Reference](https://developer.mozilla.org/docs/Web/API/KeyboardEvent/repeat) */\nObject.defineProperty(this,\"repeat\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.altKey=r,this.ctrlKey=i,this.metaKey=s,this.shiftKey=a,this.charCode=n,this.code=o,this.isComposing=l,this.key=u,this.keyCode=c,this.location=h,this.repeat=d}};class y extends e{constructor(e,t={}){const{data:r,lastEventId:i,origin:s,ports:a,source:n,...o}=t;super(e,o),Object.defineProperty(this,\"data\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"lastEventId\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"origin\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"ports\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"source\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.data=r,this.lastEventId=i||\"\",this.origin=s||\"\",this.ports=a||[],this.source=n||null}}class v{constructor(){g.set(this,(w=!0,new _))}get signal(){return m(this)}abort(t){O(this.signal).reason=t,this.signal.dispatchEvent(new e(\"abort\"))}}const{weakMap:g,get:m}=a(v.name);let w=!1;class _ extends t{constructor(){if(super(),Object.defineProperty(this,\"onabort\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),!w)throw new TypeError(\"Illegal constructor\");w=!1,P.set(this,{aborted:!1,reason:new __sharedGlobals.DOMException(\"signal is aborted without reason\",\"AbortError\")}),r(this,\"abort\",(function(e){var t;this.aborted||(O(this).aborted=!0,null===(t=this.onabort)||void 0===t||t.call(this,e))}))}get aborted(){return O(this).aborted}get reason(){return O(this).reason}throwIfAborted(){if(this.aborted)throw this.reason}static abort(e){const t=new v;return t.abort(e),t.signal}static timeout(e){const t=new v;return setTimeout((()=>t.abort(new __sharedGlobals.DOMException(\"signal timed out\",\"TimeOut\"))),e),t.signal}}const{weakMap:P,get:O}=a(_.name);const E=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\",j={};for(let e=0;e<65;e++)j[E[e]]=e;function T(e){const t=[];for(let r=0;r<e.length;r+=3){const i=e[r],s=r+1<e.length,a=s?e[r+1]:0,n=r+2<e.length,o=n?e[r+2]:0,l=i>>2,u=(3&i)<<4|a>>4;let c=(15&a)<<2|o>>6,h=63&o;n||(h=64,s||(c=64)),t.push(E[l],E[u],E[c],E[h])}return t.join(\"\")}function R(e){\n// 移除可能的换行符和空白\nconst t=e.replace(/[^A-Za-z0-9+/=]/g,\"\"),r=(\"=\"===t[t.length-1]?1:0)+(\"=\"===t[t.length-2]?1:0),i=3*t.length/4-r,s=new Uint8Array(i);\n// 计算实际字节数（忽略 '=' 填充）\nlet a=0;for(let e=0;e<t.length;e+=4){const r=j[t[e]],n=j[t[e+1]],o=j[t[e+2]]||0,l=r<<2|n>>4,u=(15&n)<<4|o>>2,c=(3&o)<<6|(j[t[e+3]]||0);s[a++]=l,a<i&&(s[a++]=u),a<i&&(s[a++]=c)}return s}\n// string -> buffer\nconst A=function(e){return __sharedGlobals.TextEncoder.prototype.encode.call(null,e)};\n// buffer -> string\nfunction x(e){if(e instanceof ArrayBuffer)return new Uint8Array(e.slice(0));if(ArrayBuffer.isView(e))return new Uint8Array(e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength));throw new TypeError(\"Invalid buffer type\")}class C{constructor(e=[],t={}){Object.defineProperty(this,\"_buffer\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"size\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"type\",{enumerable:!0,configurable:!0,writable:!0,value:void 0});const r=new Array(e.length);for(let t=0,i=e.length;t<i;t++){const i=e[t];i instanceof C?r[t]=i._buffer:\"string\"==typeof i?r[t]=A(i):i instanceof ArrayBuffer||ArrayBuffer.isView(i)?r[t]=x(i):r[t]=A(String(i))}this._buffer=function(e){const t=e.reduce(((e,t)=>e+t.byteLength),0),r=new Uint8Array(t);let i=0;return e.forEach((e=>{r.set(e,i),i+=e.byteLength})),r}(r),this.size=this._buffer.length,this.type=String(t.type)||\"\",/[^\\u0020-\\u007E]/.test(this.type)?this.type=\"\":this.type=this.type.toLowerCase()}arrayBuffer(){return Promise.resolve(this._buffer.buffer.slice(0))}text(){return Promise.resolve((e=this._buffer,__sharedGlobals.TextDecoder.prototype.decode.call(null,e)));var e}slice(e,t,r){const i=this._buffer.slice(e||0,t||this._buffer.length);return new C([i],{type:r})}stream(){let e=0;const t=this;return new __sharedGlobals.ReadableStream({type:\"bytes\",autoAllocateChunkSize:524288,pull(r){const i=r.byobRequest.view;return t.slice(e,e+i.byteLength).arrayBuffer().then((function(s){const a=new Uint8Array(s),n=a.byteLength;e+=n,\n//@ts-expect-error ignore\ni.set(a),r.byobRequest.respond(n),e>=t.size&&r.close()}))}})}}class S extends C{constructor(e,t,r={}){super(e,r),Object.defineProperty(this,\"lastModified\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"name\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name=t.replace(/\\//g,\":\"),this.lastModified=+(r.lastModified||0)}}const B=0,M=1;async function I(e,t,r){if(!(t instanceof C))throw new TypeError(\"Failed to execute '\"+r+\"' on 'FileReader': parameter 1 is not of type 'Blob'.\");return new Promise(((i,s)=>{setTimeout((async()=>{switch(e.result=\"\",e.readyState=M,e.dispatchEvent(new __sharedGlobals.ProgressEvent(\"loadstart\",{lengthComputable:!0,loaded:0,total:t._buffer.byteLength})),r){case\"readAsDataURL\":e.result=\"data:\"+t.type+\";base64,\"+T(t._buffer);break;case\"readAsArrayBuffer\":e.result=await t.arrayBuffer();break;case\"readAsText\":e.result=await t.text()}const s={lengthComputable:!0,loaded:t._buffer.byteLength,total:t._buffer.byteLength};e.dispatchEvent(new __sharedGlobals.ProgressEvent(\"progress\",s)),e.dispatchEvent(new __sharedGlobals.ProgressEvent(\"load\",s)),e.dispatchEvent(new __sharedGlobals.ProgressEvent(\"loadend\",s)),i()}),0)}))}const U=class extends t{constructor(){super(),Object.defineProperty(this,\"onabort\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"onerror\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"onload\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"onloadstart\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"onprogress\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"onloadend\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"readyState\",{enumerable:!0,configurable:!0,writable:!0,value:B}),Object.defineProperty(this,\"error\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"result\",{enumerable:!0,configurable:!0,writable:!0,value:null}),r(this,\"abort\",(e=>{var t;return null===(t=this.onabort)||void 0===t?void 0:t.call(this,e)})),r(this,\"error\",(e=>{var t;return null===(t=this.onerror)||void 0===t?void 0:t.call(this,e)})),r(this,\"load\",(e=>{var t;return null===(t=this.onload)||void 0===t?void 0:t.call(this,e)})),r(this,\"loadstart\",(e=>{var t;return null===(t=this.onloadstart)||void 0===t?void 0:t.call(this,e)})),r(this,\"progress\",(e=>{var t;return null===(t=this.onprogress)||void 0===t?void 0:t.call(this,e)})),r(this,\"loadend\",(e=>{var t;return null===(t=this.onloadend)||void 0===t?void 0:t.call(this,e)}))}readAsDataURL(e){I(this,e,\"readAsDataURL\")}readAsText(e){I(this,e,\"readAsText\")}readAsArrayBuffer(e){I(this,e,\"readAsArrayBuffer\")}abort(){this.dispatchEvent(new __sharedGlobals.ProgressEvent(\"abort\",{lengthComputable:!1,loaded:0,total:0})),this.result=null}},k={INDEX_SIZE_ERR:1,DOMSTRING_SIZE_ERR:2,HIERARCHY_REQUEST_ERR:3,WRONG_DOCUMENT_ERR:4,INVALID_CHARACTER_ERR:5,NO_DATA_ALLOWED_ERR:6,NO_MODIFICATION_ALLOWED_ERR:7,NOT_FOUND_ERR:8,NOT_SUPPORTED_ERR:9,INUSE_ATTRIBUTE_ERR:10,INVALID_STATE_ERR:11,SYNTAX_ERR:12,INVALID_MODIFICATION_ERR:13,NAMESPACE_ERR:14,INVALID_ACCESS_ERR:15,VALIDATION_ERR:16,\n/** @deprecated */\nTYPE_MISMATCH_ERR:17,SECURITY_ERR:18,NETWORK_ERR:19,ABORT_ERR:20,URL_MISMATCH_ERR:21,QUOTA_EXCEEDED_ERR:22,TIMEOUT_ERR:23,INVALID_NODE_TYPE_ERR:24,DATA_CLONE_ERR:25};class D extends Error{constructor(e,t){super(e),Object.defineProperty(this,\"_name\",{enumerable:!0,configurable:!0,writable:!0,value:t}),Error.captureStackTrace&&Error.captureStackTrace(this,G)}get code(){var e;return null!==(e=k[this.name.replace(/([a-z])([A-Z])/g,\"$1_$2\").toUpperCase()])&&void 0!==e?e:0}get name(){var e;return null!==(e=this._name)&&void 0!==e?e:\"Error\"}}const G=D;\n/* formdata-polyfill. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> */\n/* reference： https://github.com/jimmywarting/FormData/blob/master/FormData.js */\n/**\n * @param name\n * @param value\n * @param filename\n */function L(e,t,r){return t instanceof __sharedGlobals.Blob?(r=void 0!==r?String(r+\"\"):t instanceof __sharedGlobals.File?t.name:\"blob\",t.name===r&&\"[object Blob]\"!==Object.prototype.toString.call(t)||(t=new __sharedGlobals.File([t],r)),[String(e),t]):[String(e),String(t)]}\n// normalize line feeds for textarea\n// https://html.spec.whatwg.org/multipage/form-elements.html#textarea-line-break-normalisation-transformation\nfunction N(e){return e.replace(/\\r?\\n|\\r/g,\"\\r\\n\")}\n/**\n * @implements {Iterable}\n */class X{constructor(){\n/**\n         * FormData class\n         * @param form never use in mini-program\n         */\nObject.defineProperty(this,\"_data\",{enumerable:!0,configurable:!0,writable:!0,value:[]})}\n/**\n     * Append a field\n     *\n     * @param name      field name\n     * @param value     string / blob / file\n     * @param filename  filename to use with blob\n     */append(e,t,r){this._data.push(L(e,t,r))}\n/**\n     * Delete all fields values given name\n     *\n     * @param   name  Field name\n     */delete(e){const t=[];e=String(e),this._data.forEach((r=>{r[0]!==e&&t.push(r)})),this._data=t}\n/**\n     * Return first field value given name\n     * or null if non existent\n     * @param   {string}   name\n     */get(e){const t=this._data;e=String(e);for(let r=0;r<t.length;r++)if(t[r][0]===e)return t[r][1];return null}\n/**\n     * Return all fields values given name\n     *\n     * @param  name  Fields name\n     * @return  {Array}         [{String|File}]\n     */getAll(e){const t=[];return e=String(e),this._data.forEach((r=>{r[0]===e&&t.push(r[1])})),t}\n/**\n     * Check for field name existence\n     *\n     * @param   {string}   name  Field name\n     * @return  {boolean}\n     */has(e){e=String(e);for(let t=0;t<this._data.length;t++)if(this._data[t][0]===e)return!0;return!1}\n/**\n     * Overwrite all values given name\n     *\n     * @param name      Filed name\n     * @param value     Field value\n     * @param filename  Filename (optional)\n     */set(e,t,r){e=String(e);const i=[],s=L(e,t,r);let a=!0;\n// - replace the first occurrence with same name\n// - discards the remaining with same name\n// - while keeping the same order items where added\nthis._data.forEach((t=>{t[0]===e?a&&(a=!i.push(s)):i.push(t)})),a&&i.push(s),this._data=i}\n/**\n     * need manual called in xhr.send() or fetch()\n     *\n     */_blob(){const e=\"----formdata-polyfill-\"+Math.random(),t=[],r=`--${e}\\r\\nContent-Disposition: form-data; name=\"`;return this.forEach(((e,i)=>\"string\"==typeof e?t.push(r+encodeURIComponent(N(i))+`\"\\r\\n\\r\\n${N(e)}\\r\\n`):t.push(r+encodeURIComponent(N(i))+`\"; filename=\"${encodeURIComponent(e.name)}\"\\r\\nContent-Type: ${e.type||\"application/octet-stream\"}\\r\\n\\r\\n`,e,\"\\r\\n\"))),t.push(`--${e}--`),new __sharedGlobals.Blob(t,{type:\"multipart/form-data; boundary=\"+e})}\n/**\n     * Iterate over all fields\n     *\n     * @param callback  Executed for each item with parameters (value, name, thisArg)\n     * @param thisArg   `this` context for callback function\n     */forEach(e,t){for(const[r,i]of this)e.call(t,i,r,this)}*keys(){for(const[e]of this)yield e}*values(){for(const[,e]of this)yield e}*entries(){yield*this._data.values()}[Symbol.iterator](){return this.entries()}}function K(e){if(\"string\"!=typeof e&&(e=String(e)),/[^a-z0-9\\-#$%&'*+.^_`|~!]/i.test(e)||\"\"===e)throw new TypeError('Invalid character in header field name: \"'+e+'\"');return e.toLowerCase()}function Y(e){return\"string\"!=typeof e&&(e=String(e)),e}\n// HTTP methods whose capitalization should be normalized\nconst F=[\"CONNECT\",\"DELETE\",\"GET\",\"HEAD\",\"OPTIONS\",\"PATCH\",\"POST\",\"PUT\",\"TRACE\"];class q{constructor(e){Object.defineProperty(this,\"__map\",{enumerable:!0,configurable:!0,writable:!0,value:{}}),e instanceof q?e.forEach(((e,t)=>{this.append(t,e)}),this):Array.isArray(e)?e.forEach((e=>{if(2!=e.length)throw new TypeError(\"Headers constructor: expected name/value pair to be length 2, found\"+e.length);this.append(e[0],e[1])}),this):e&&Object.getOwnPropertyNames(e).forEach((t=>{this.append(t,e[t])}),this)}append(e,t){e=K(e),t=Y(t);const r=this.__map[e];this.__map[e]=r?r+\", \"+t:t}delete(e){delete this.__map[K(e)]}get(e){return e=K(e),this.has(e)?this.__map[e]:null}has(e){return this.__map.hasOwnProperty(K(e))}set(e,t){this.__map[K(e)]=Y(t)}forEach(e,t){for(const r of Object.entries(this.__map))e.call(t,r[1],r[0],this)}*keys(){yield*Object.keys(this.__map)}*values(){yield*Object.values(this.__map)}*entries(){yield*Object.entries(this.__map)}[Symbol.iterator](){return this.entries()}}function H(e){if(e.slice)return e.slice(0);{const t=new Uint8Array(e.byteLength);return t.set(new Uint8Array(e)),t.buffer}}function W(e){if(!e._noBody)return e.bodyUsed?Promise.reject(new TypeError(\"Already read\")):void(e.bodyUsed=!0)}function z(e){return new Promise((function(t,r){e.onload=function(){t(e.result)},e.onerror=function(){r(e.error)}}))}function $(e){const t=new __sharedGlobals.FileReader,r=z(t);return t.readAsArrayBuffer(e),r}function V(e=\"\"){const t=new __sharedGlobals.FormData;return e.trim().split(\"&\").forEach((function(e){if(e){const r=e.split(\"=\"),i=r.shift().replace(/\\+/g,\" \"),s=r.join(\"=\").replace(/\\+/g,\" \");t.append(decodeURIComponent(i),decodeURIComponent(s))}})),t}class Z{constructor(){Object.defineProperty(this,\"_bodyInit\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"_noBody\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"_bodyText\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"_bodyBlob\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"_bodyFormData\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"_bodyArrayBuffer\",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}_initBody(e){var t;this._bodyInit=e,e?\"string\"==typeof e?this._bodyText=e:e instanceof __sharedGlobals.Blob?this._bodyBlob=e:e instanceof __sharedGlobals.FormData?this._bodyFormData=e:(t=e)&&DataView.prototype.isPrototypeOf(t)?(this._bodyArrayBuffer=H(e.buffer),\n// IE 10-11 can't handle a DataView body.\nthis._bodyInit=new __sharedGlobals.Blob([this._bodyArrayBuffer])):e instanceof ArrayBuffer||ArrayBuffer.isView(e)?this._bodyArrayBuffer=H(e):this._bodyText=e=Object.prototype.toString.call(e):(this._noBody=!0,this._bodyText=\"\"),this.headers.get(\"content-type\")||(\"string\"==typeof e?this.headers.set(\"content-type\",\"text/plain;charset=UTF-8\"):this._bodyBlob&&this._bodyBlob.type&&this.headers.set(\"content-type\",this._bodyBlob.type))}_setBodyUsed(e){this.bodyUsed=e}blob(){const e=W(this);if(e)return e;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new __sharedGlobals.Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error(\"could not read FormData body as blob\");return Promise.resolve(new __sharedGlobals.Blob([this._bodyText]))}arrayBuffer(){if(this._bodyArrayBuffer){const e=W(this);return e||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer))}return this.blob().then($)}text(){const e=W(this);if(e)return e;if(this._bodyBlob)return function(e){const t=new __sharedGlobals.FileReader,r=z(t);\n// const match = /charset=([A-Za-z0-9_-]+)/.exec(blob.type);\n// const encoding = match ? match[1] : 'utf-8';\nreturn t.readAsText(e),r}(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(function(e){const t=new Uint8Array(e),r=new Array(t.length);for(let e=0;e<t.length;e++)r[e]=String.fromCharCode(t[e]);return r.join(\"\")}(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error(\"could not read FormData body as text\");return Promise.resolve(this._bodyText)}formData(){return this.text().then(V)}json(){return this.text().then(((e=\"\")=>JSON.parse(e)))}}class J extends Z{constructor(e,t={}){super(),Object.defineProperty(this,\"body\",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,\"bodyUsed\",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,\"cache\",{enumerable:!0,configurable:!0,writable:!0,value:\"default\"}),Object.defineProperty(this,\"credentials\",{enumerable:!0,configurable:!0,writable:!0,value:\"same-origin\"}),Object.defineProperty(this,\"headers\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"integrity\",{enumerable:!0,configurable:!0,writable:!0,value:\"\"}),Object.defineProperty(this,\"method\",{enumerable:!0,configurable:!0,writable:!0,value:\"GET\"}),Object.defineProperty(this,\"mode\",{enumerable:!0,configurable:!0,writable:!0,value:\"cors\"}),Object.defineProperty(this,\"redirect\",{enumerable:!0,configurable:!0,writable:!0,value:\"follow\"}),Object.defineProperty(this,\"referrer\",{enumerable:!0,configurable:!0,writable:!0,value:\"about:client\"}),Object.defineProperty(this,\"referrerPolicy\",{enumerable:!0,configurable:!0,writable:!0,value:\"\"}),Object.defineProperty(this,\"signal\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"url\",{enumerable:!0,configurable:!0,writable:!0,value:void 0});let r=t.body;if(e instanceof J){if(e.bodyUsed)throw new TypeError(\"Already read\");this.url=e.url,this.credentials=e.credentials,t.headers||(this.headers=new q(e.headers)),this.method=e.method,this.mode=e.mode,this.signal=e.signal,r||null==e._bodyInit||(r=e._bodyInit,e._setBodyUsed(!0))}else this.url=String(e);if(this.credentials=t.credentials||this.credentials||\"same-origin\",this.headers=this.headers||new q(t.headers),this.method=function(e){const t=e.toUpperCase();return F.indexOf(t)>-1?t:e}(t.method||this.method||\"GET\"),this.mode=t.mode||this.mode||null,this.signal=t.signal||(new __sharedGlobals.AbortController).signal,(\"GET\"===this.method||\"HEAD\"===this.method)&&r)throw new TypeError(\"Body not allowed for GET or HEAD requests\");if(this._initBody(r),!(\"GET\"!==this.method&&\"HEAD\"!==this.method||\"no-store\"!==t.cache&&\"no-cache\"!==t.cache)){\n// Search for a '_' parameter in the query string\nconst e=/([?&])_=[^&]*/;if(e.test(this.url))\n// If it already exists then set the value with the current time\nthis.url=this.url.replace(e,\"$1_=\"+(new Date).getTime());else{\n// Otherwise add a new '_' parameter to the end with the current time\nconst e=/\\?/;this.url+=(e.test(this.url)?\"&\":\"?\")+\"_=\"+(new Date).getTime()}}}clone(){return new J(this,{body:this._bodyInit})}}const Q=[301,302,303,307,308];class ee extends Z{constructor(e,t){if(super(),Object.defineProperty(this,\"bodyUsed\",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,\"headers\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"ok\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"redirected\",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,\"status\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"statusText\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"type\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"url\",{enumerable:!0,configurable:!0,writable:!0,value:\"\"}),!(this instanceof ee))throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.');if(t||(t={}),this.type=\"default\",this.status=void 0===t.status?200:t.status,this.status<200||this.status>599)throw new RangeError(\"Failed to construct 'Response': The status provided (0) is outside the range [200, 599].\");this.ok=this.status>=200&&this.status<300,this.statusText=void 0===t.statusText?\"\":\"\"+t.statusText,this.headers=new q(t.headers),this.url=t.url||\"\",this._initBody(e)}clone(){return new ee(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new q(this.headers),url:this.url})}static error(){const e=new ee(null,{status:200,statusText:\"\"});return e.ok=!1,e.status=0,e.type=\"error\",e}static redirect(e,t){if(t&&-1===Q.indexOf(t))throw new RangeError(\"Invalid status code\");return new ee(null,{status:t,headers:{location:e}})}}function te(e){const t=new q;\n// Replace instances of \\r\\n and \\n followed by at least one space or horizontal tab with a space\n// https://tools.ietf.org/html/rfc7230#section-3.2\n// Avoiding split via regex to work around a common IE11 bug with the core-js 3.6.0 regex polyfill\n// https://github.com/github/fetch/issues/748\n// https://github.com/zloirock/core-js/issues/751\nreturn e.replace(/\\r?\\n[\\t ]+/g,\" \").split(\"\\r\").map((function(e){return 0===e.indexOf(\"\\n\")?e.substr(1,e.length):e})).forEach((function(e){const r=e.split(\":\"),i=r.shift().trim();if(i){const e=r.join(\":\").trim();try{t.append(i,e)}catch(e){console.warn(\"Response \"+e.message)}}})),t}function re(e,t){return new Promise((function(r,i){const s=new J(e,t);if(s.signal&&s.signal.aborted)return i(new __sharedGlobals.DOMException(\"Aborted\",\"AbortError\"));const a=new __sharedGlobals.XMLHttpRequest;function n(){a.abort()}a.onload=function(){const e={statusText:a.statusText,headers:te(a.getAllResponseHeaders()||\"\")};\n// This check if specifically for when a user fetches a file locally from the file system\n// Only if the status is out of a normal range\n0===s.url.indexOf(\"file://\")&&(a.status<200||a.status>599)?e.status=200:e.status=a.status,e.url=a.responseURL;const t=a.response;setTimeout((function(){r(new ee(t,e))}),0)},a.onerror=function(){setTimeout((function(){i(new TypeError(\"Network request failed\"))}),0)},a.ontimeout=function(){setTimeout((function(){i(new TypeError(\"Network request timed out\"))}),0)},a.onabort=function(){setTimeout((function(){i(new __sharedGlobals.DOMException(\"Aborted\",\"AbortError\"))}),0)},a.open(s.method,s.url,!0),\"include\"===s.credentials?a.withCredentials=!0:\"omit\"===s.credentials&&(a.withCredentials=!1),a.responseType=\"blob\",s.headers.forEach((function(e,t){a.setRequestHeader(t,e)})),s.signal&&(s.signal.addEventListener(\"abort\",n),a.onreadystatechange=function(){\n// DONE (success or failure)\n4===a.readyState&&s.signal.removeEventListener(\"abort\",n)}),\n//@ts-expect-error ignore type\na.send(void 0===s._bodyInit?null:s._bodyInit)}))}\n/**\n * base on Blob.js\n * reference: https://github.com/eligrey/Blob.js\n * */class ie{decode(e){let t;t=e instanceof ArrayBuffer?new Uint8Array(e):e;const r=t.length,i=[];let s=0;for(;s<r;){const e=t[s];let a=null,n=e>239?4:e>223?3:e>191?2:1;if(s+n<=r){let r,i,o,l;switch(n){case 1:e<128&&(a=e);break;case 2:r=t[s+1],128==(192&r)&&(l=(31&e)<<6|63&r,l>127&&(a=l));break;case 3:r=t[s+1],i=t[s+2],128==(192&r)&&128==(192&i)&&(l=(15&e)<<12|(63&r)<<6|63&i,l>2047&&(l<55296||l>57343)&&(a=l));break;case 4:r=t[s+1],i=t[s+2],o=t[s+3],128==(192&r)&&128==(192&i)&&128==(192&o)&&(l=(15&e)<<18|(63&r)<<12|(63&i)<<6|63&o,l>65535&&l<1114112&&(a=l))}}null===a?(\n// we did not generate a valid codePoint so insert a\n// replacement char (U+FFFD) and advance only 1 byte\na=65533,n=1):a>65535&&(\n// encode to utf16 (surrogate pair dance)\na-=65536,i.push(a>>>10&1023|55296),a=56320|1023&a),i.push(a),s+=n}const a=i.length;let n=\"\";for(s=0;s<a;)n+=String.fromCharCode(...i.slice(s,s+=4096));return n}}n(\"TextDecoder\",ie);\n/**\n * base on Blob.js\n * reference: https://github.com/eligrey/Blob.js\n * */\nclass se{encode(e){let t=0;const r=e.length,i=Uint8Array;// Use byte array when possible\nlet s=0,a=Math.max(32,r+(r>>1)+7),n=new i(a>>3<<3);// output position\n// ... but at 8 byte offset\nfor(;t<r;){let i=e.charCodeAt(t++);if(i>=55296&&i<=56319){\n// high surrogate\nif(t<r){const r=e.charCodeAt(t);56320==(64512&r)&&(++t,i=((1023&i)<<10)+(1023&r)+65536)}if(i>=55296&&i<=56319)continue;// drop lone surrogate\n}\n// expand the buffer if we couldn't write 4 bytes\nif(s+4>n.length){a+=8,// minimum extra\na*=1+t/e.length*2,// take 2x the remaining\na=a>>3<<3;// 8 byte offset\nconst r=new Uint8Array(a);r.set(n),n=r}if(4294967168&i){if(4294965248&i)if(4294901760&i){if(4292870144&i)\n// FIXME: do we care\ncontinue;\n// 4-byte\nn[s++]=i>>18&7|240,n[s++]=i>>12&63|128,n[s++]=i>>6&63|128}else\n// 3-byte\nn[s++]=i>>12&15|224,n[s++]=i>>6&63|128;else\n// 2-byte\nn[s++]=i>>6&31|192;n[s++]=63&i|128}else\n// 1-byte\nn[s++]=i}return n.slice(0,s)}}n(\"TextEncoder\",se);\n// import { URL as RawURL } from '@minisheep/whatwg-url-without-unicode';\n// export type BrowserURL = typeof RawURL & {\n//   createObjectURL(blob: Blob): string;\n//   revokeObjectURL(url: string): void;\n//   parse(url: string, base?: string | RawURL): RawURL | null;\n// };\n// export const blobUrlPrefix = `blob:https://mock.by.createObjectURL`;\n// const blobUrlCache = new Map<string, Blob>();\n// const generateUUID = () => {\n//   const timestamp = Date.now(); // 获取当前时间戳\n//   const randomNum = Math.random().toString(16).slice(2); // 生成随机数并转为十六进制\n//   // 拼接时间戳和随机数，确保 UUID 符合格式\n//   const uuid = `${timestamp.toString(16)}-${randomNum.slice(0, 4)}-${randomNum.slice(4, 8)}-${randomNum.slice(8, 12)}-${randomNum.slice(12, 16)}`;\n//   return uuid;\n// };\n// const createRandomBlobUrl = (blob: Blob) => {\n//   const url = `${blobUrlPrefix}/${generateUUID()}`;\n//   blobUrlCache.set(url, blob);\n//   return url;\n// };\n// export function getBlobByBlobUrl(url: string) {\n//   return blobUrlCache.get(url) || null;\n// }\n// export function getDataURlByBlobUrl(url: string) {\n//   const blob = getBlobByBlobUrl(url);\n//   return blob ? 'data:' + blob.type + ';base64,' + uint82base64(blob._buffer) : null;\n// }\n// (RawURL as BrowserURL).createObjectURL = function (blob: Blob) {\n//   if (!(blob instanceof Blob)) {\n//     throw new TypeError(\n//       \"Failed to execute 'createObjectURL' on 'URL': parameter 1 is not of type 'Blob'.\"\n//     );\n//   }\n//   return createRandomBlobUrl(blob);\n//   // return 'data:' + blob.type + ';base64,' + array2base64(blob._buffer)\n// };\n// (RawURL as BrowserURL).revokeObjectURL = function (url: string) {\n//   if (blobUrlCache.has(url)) {\n//     blobUrlCache.delete(url);\n//   }\n// };\n// (RawURL as BrowserURL).parse = function (url: string, base?: RawURL | string) {\n//   return RawURL.canParse(url, base) ? new RawURL(url, base) : null;\n// };\n// export const URL = RawURL as BrowserURL;\n// export { URLSearchParams } from '@minisheep/whatwg-url-without-unicode';\n/**\n * 只提供简单版本的 URL, 如下考虑/限制：\n * 包含全功能的 URL polyfill 实现库 whatwg-url 库非常大，即使使用仅支持ascii的版本,压缩后也有 67kb\n * 并且它相关的依赖工具使用了 形如： Object.getPrototypeOf(Object.getPrototypeOf(async function* () {}).prototype);\n * 的代码会导致 开发者工具使用 es6 转 es5 情况下报错 （使用 skyline 手势必须开启）\n * */\nclass ae{constructor(){}static createObjectURL(e){if(!(e instanceof C))throw new TypeError(\"Failed to execute 'createObjectURL' on 'URL': parameter 1 is not of type 'Blob'.\");return le(e);\n// return 'data:' + blob.type + ';base64,' + array2base64(blob._buffer)\n}static revokeObjectURL(e){oe.has(e)&&oe.delete(e)}}const ne=\"blob:https://mock.by.createObjectURL\",oe=new Map,le=e=>{const t=`${ne}/${(()=>{const e=Date.now(),t=Math.random().toString(16).slice(2);// 获取当前时间戳\nreturn`${e.toString(16)}-${t.slice(0,4)}-${t.slice(4,8)}-${t.slice(8,12)}-${t.slice(12,16)}`})()}`;return oe.set(t,e),t};function ue(e){return oe.get(e)||null}function ce(e){const t=ue(e);return t?\"data:\"+t.type+\";base64,\"+T(t._buffer):null}\n/**\n * from threejs/examples/jsm/libs/draco/draco_decoder.js\n * */function he(e){const t=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";let r,i,s,a,n,o,l,u=\"\",c=0;e=e.replace(/[^A-Za-z0-9\\+\\/\\=]/g,\"\");do{a=t.indexOf(e.charAt(c++)),n=t.indexOf(e.charAt(c++)),o=t.indexOf(e.charAt(c++)),l=t.indexOf(e.charAt(c++)),r=a<<2|n>>4,i=(15&n)<<4|o>>2,s=(3&o)<<6|l,u+=String.fromCharCode(r),64!==o&&(u+=String.fromCharCode(i)),64!==l&&(u+=String.fromCharCode(s))}while(c<e.length);return u}function de(e){e=String(e);const t=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";// Base64 字符表\nlet r=\"\",i=\"\",s=0,a=0;// 输出字符串\n// 当前缓冲区中的有效位数\nfor(let i=0;i<e.length;i++)\n// 当缓冲区中有 6 位以上数据时，将其转化为 Base64 字符\nfor(s=s<<8|e.charCodeAt(i),// 将字符的 ASCII 码放入缓冲区\na+=8;a>=6;){// 取最高的 6 位\nr+=t[s>>a-6&63],a-=6}\n// 如果缓冲区中有剩余的不足 6 位的数据\nreturn a>0&&(r+=t[s<<6-a&63],// 补齐剩余位数并添加到输出\ni=\"=\".repeat((6-a)/2)),r+i;// 返回最终结果\n}\n/**\n * 主要用于支持 @vueuse 相关使用\n * */let be=null;class fe{constructor(){if(Object.defineProperty(this,\"borderBoxSize\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"contentBoxSize\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"contentRect\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"devicePixelContentBoxSize\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"target\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),!be)throw new TypeError(\"Illegal constructor\");this.borderBoxSize=be.borderBoxSize,this.devicePixelContentBoxSize=be.devicePixelContentBoxSize,this.contentBoxSize=be.devicePixelContentBoxSize,this.contentRect=be.contentRect,this.target=be.target,be=null}}const pe=new WeakMap;class ye{static getResizeObserver(e){return pe.has(e)&&pe.get(e)||[]}constructor(e){Object.defineProperty(this,\"polyfill\",{enumerable:!0,configurable:!0,writable:!0,value:\"polyfill by @minisheep/mini-program-polyfill-core\"}),Object.defineProperty(this,\"callback\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"elements\",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),this.callback=e}observe(e,t={}){if(!this.elements.has(e))if(this.elements.set(e,t),pe.has(e)){const t=pe.get(e)||[];pe.set(e,[...t,this])}else pe.set(e,[this])}unobserve(e){if(this.elements.has(e)&&(this.elements.delete(e),pe.has(e))){const t=pe.get(e)||[];pe.set(e,t.filter((e=>e!==this)))}}disconnect(){this.elements.clear()}_getElements(){return this.elements.keys()}_getObserveOptions(e){if(this.elements.has(e))return this.elements.get(e)}_trigger(e){const t=this.elements,r=Array.from(t.keys()),i=e.filter((e=>r.includes(e.target))).map((e=>(be=e,new fe)));i.length&&this.callback(i,this)}}const ve={AbortController:v,AbortSignal:_,Blob:C,DOMException:G,Event:e,EventTarget:t,File:S,FileReader:U,FormData:X,Headers:q,KeyboardEvent:p,MouseEvent:b,PointerEvent:f,ProgressEvent:o,Request:J,ReadableStream:i,Response:ee,TextDecoder:ie,TextEncoder:se,Touch:u,TouchEvent:d,UIEvent:c,URL:ae,atob:__sharedGlobals.atob,btoa:de,fetch:re,ResizeObserver:ye,ResizeObserverEntry:fe};\n/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */function ge(e,t,r,i){if(\"a\"===r&&!i)throw new TypeError(\"Private accessor was defined without a getter\");if(\"function\"==typeof t?e!==t||!i:!t.has(e))throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");return\"m\"===r?i:\"a\"===r?i.call(e):i?i.value:t.get(e)}function me(e,t,r,i,s){if(\"m\"===i)throw new TypeError(\"Private method is not writable\");if(\"a\"===i&&!s)throw new TypeError(\"Private accessor was defined without a setter\");if(\"function\"==typeof t?e!==t||!s:!t.has(e))throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");return\"a\"===i?s.call(e,r):s?s.value=r:t.set(e,r),r}\"function\"==typeof SuppressedError&&SuppressedError;class we extends t{constructor(){super(),Object.defineProperty(this,\"onabort\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"onerror\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"onload\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"onloadstart\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"onprogress\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"onloadend\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,\"ontimeout\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),r(this,\"abort\",(e=>{var t;return null===(t=this.onabort)||void 0===t?void 0:t.call(this,e)})),r(this,\"error\",(e=>{var t;return null===(t=this.onerror)||void 0===t?void 0:t.call(this,e)})),r(this,\"load\",(e=>{var t;return null===(t=this.onload)||void 0===t?void 0:t.call(this,e)})),r(this,\"loadstart\",(e=>{var t;return null===(t=this.onloadstart)||void 0===t?void 0:t.call(this,e)})),r(this,\"progress\",(e=>{var t;return null===(t=this.onprogress)||void 0===t?void 0:t.call(this,e)})),r(this,\"loadend\",(e=>{var t;return null===(t=this.onloadend)||void 0===t?void 0:t.call(this,e)})),r(this,\"timeout\",(e=>{var t;return null===(t=this.ontimeout)||void 0===t?void 0:t.call(this,e)}))}}var _e,Pe,Oe,Ee,je,Te,Re,Ae,xe,Ce,Se,Be,Me,Ie,Ue;const ke=0,De=1,Ge=2,Le=3,Ne=4,Xe={100:\"Continue\",101:\"Switching Protocol\",102:\"Processing\",200:\"OK\",201:\"Created\",202:\"Accepted\",203:\"Non-Authoritative Information\",204:\"No Content\",205:\"Reset Content\",206:\"Partial Content\",207:\"Multi-Status\",208:\"Multi-Status\",226:\"IM Used\",300:\"Multiple Choice\",301:\"Moved Permanently\",302:\"Found\",303:\"See Other\",304:\"Not Modified\",305:\"Use Proxy\",306:\"unused\",307:\"Temporary Redirect\",308:\"Permanent Redirect\",400:\"Bad Request\",401:\"Unauthorized\",402:\"Payment Required\",403:\"Forbidden\",404:\"Not Found\",405:\"Method Not Allowed\",406:\"Not Acceptable\",407:\"Proxy Authentication Required\",408:\"Request Timeout\",409:\"Conflict\",410:\"Gone\",411:\"Length Required\",412:\"Precondition Failed\",413:\"Payload Too Large\",414:\"URI Too Long\",415:\"Unsupported Media Type\",416:\"Requested Range Not Satisfiable\",417:\"Expectation Failed\",418:\"I'm a teapot\",421:\"Misdirected Request\",422:\"Unprocessable Entity\",423:\"Locked\",424:\"Failed Dependency\",426:\"Upgrade Required\",428:\"Precondition Required\",429:\"Too Many Requests\",431:\"Request Header Fields Too Large\",451:\"Unavailable For Legal Reasons\",500:\"Internal Server Error\",501:\"Not Implemented\",502:\"Bad Gateway\",503:\"Service Unavailable\",504:\"Gateway Timeout\",505:\"HTTP Version Not Supported\",506:\"Variant Also Negotiates\",507:\"Insufficient Storage\",508:\"Loop Detected\",510:\"Not Extended\",511:\"Network Authentication Required\"};\n// http status code and text\nfunction Ke(e){const t={};for(const r in e)e.hasOwnProperty(r)&&(t[r.toLowerCase()]=e[r]);return t}\n// https://developer.mozilla.org/zh-CN/docs/Glossary/Forbidden_header_name\nconst Ye=/* @__PURE__ */[\"Accept-Charset\",\"Accept-Encoding\",\"Access-Control-Request-Headers\",\"Access-Control-Request-Method\",\"Connection\",\"Content-Length\",\"Cookie\",\"Cookie2\",\"Date\",\"DNT\",\"Expect\",\"Permissions-Policy\",\"Host\",\"Keep-Alive\",\"Origin\",\"Proxy-\",\"Sec-\",\"Referer\",\"TE\",\"Trailer\",\"Transfer-Encoding\",\"Upgrade\",\"Via\"].map((e=>e.toLowerCase())).map((e=>e.trim()));_e=new WeakMap,Pe=new WeakMap,Oe=new WeakMap,Ee=new WeakMap,je=new WeakMap,Te=new WeakMap,Re=new WeakMap,Ae=new WeakMap,xe=new WeakMap,Ce=new WeakMap,Se=new WeakMap,Be=new WeakMap,Me=new WeakMap,Ie=new WeakMap,Ue=new WeakMap;const Fe=class extends we{constructor(){super(),\n// not standard prop\n_e.set(this,void 0),Pe.set(this,void 0),Oe.set(this,{}),Ee.set(this,{}),je.set(this,void 0),Te.set(this,!1),Re.set(this,ke),Ae.set(this,!1),xe.set(this,\"\"),Ce.set(this,0),\n// this is WeChat app's request task return, for abort the request\nSe.set(this,null),\n// be like readystate 1.5\nBe.set(this,!1),Me.set(this,null),Ie.set(this,0),Ue.set(this,!1),Object.defineProperty(this,\"onreadystatechange\",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),\n// The browser does not support \"addEventListener('readystatechange')\", But will support here\nr(this,\"readystatechange\",(e=>{var t;return null===(t=this.onreadystatechange)||void 0===t?void 0:t.call(this,e)}))}get readyState(){return ge(this,Re,\"f\")}get withCredentials(){return ge(this,Ae,\"f\")}set withCredentials(e){console.warn(\"withCredentials is unuseful in mini-program env\"),me(this,Ae,e,\"f\")}get response(){return ge(this,Me,\"f\")}get responseText(){if(\"\"!==ge(this,xe,\"f\")&&\"text\"!==ge(this,xe,\"f\"))throw s(`Failed to read the 'responseText' property from 'XMLHttpRequest': The value is only accessible if the object's 'responseType' is '' or 'text' (was '${ge(this,xe,\"f\")}').`);return\"object\"==typeof ge(this,Me,\"f\")?JSON.stringify(ge(this,Me,\"f\")):ge(this,Me,\"f\")}get responseURL(){return ge(this,_e,\"f\")}get responseXML(){if(\"\"!==ge(this,xe,\"f\")&&\"document\"!==ge(this,xe,\"f\"))throw s(`Failed to read the 'responseXML' property from 'XMLHttpRequest': The value is only accessible if the object's 'responseType' is '' or 'document' (was '${ge(this,xe,\"f\")}').`);throw Error(\"not supported in mini-program\")}get timeout(){return ge(this,Ce,\"f\")}set timeout(e){me(this,Ce,e,\"f\")}get status(){return ge(this,Ie,\"f\")}get statusText(){return Xe[this.status]||\"unknown\"}set responseType(e){\n// not call .open() yet\nif(this.readyState!=De||this.readyState==De&&!ge(this,Be,\"f\"))throw s(\"Failed to set 'responseType' on 'XMLHttpRequest': The object's state must be OPENED.\");me(this,xe,e,\"f\")}get responseType(){return ge(this,xe,\"f\")}get upload(){return ge(this,je,\"f\")}\n/**\n     * todo\n     * override mime type, not support yet\n     * @param mimetype\n     */overrideMimeType(e){this.readyState}\n/**\n     * fake to open the server\n     * @param method\n     * @param url\n     * will ignore in mini-program ↓\n     * @param async\n     * @param user\n     * @param password\n     */open(t,r,i=!0,s,a){\n// if open over 2 time, then close connection\nthis.readyState>=De&&this.abort(),me(this,Pe,t,\"f\"),me(this,_e,r,\"f\"),me(this,Re,De,\"f\"),me(this,Be,!0,\"f\"),this.dispatchEvent(new e(\"readystatechange\"))}\n/**\n     * send data\n     * @param data\n     * application/json use wx.request\n     * application/x-www-form-urlencoded use wx.request\n     * multipart/form-data use wx.upload\n     */send(t){var r,i;if(ge(this,Re,\"f\")!==De)throw s(\"Failed to execute 'send' on 'XMLHttpRequest': The object's state must be OPENED.\");\n// if the request have been aborted before send data\nif(!0===ge(this,Te,\"f\"))return;\n// cannot resend\nif(ge(this,Ue,\"f\"))return;\n// cannot setRequestHeader/set responseType after called send\nme(this,Be,!1,\"f\");const a=ge(this,_e,\"f\"),n=null===(r=ge(this,Pe,\"f\"))||void 0===r?void 0:r.toUpperCase(),o={...ge(this,Oe,\"f\"),accept:null!==(i=ge(this,Oe,\"f\").accept)&&void 0!==i?i:\"*/*\"},l=0===ge(this,Ce,\"f\")?1/0:ge(this,Ce,\"f\");let u,c=\"其他\";switch(ge(this,xe,\"f\")){case\"\":case\"text\":case\"json\":u=\"text\",\"json\"===ge(this,xe,\"f\")&&(c=\"json\");break;case\"arraybuffer\":case\"blob\":u=\"arraybuffer\";break;case\"document\":throw s(\"Unsupported responseType 'document' in mini-program\")}\n/*\n         * 触发事件的顺序应该是\n         * loadstart\n         * [readystatechange HEADERS_RECEIVED]\n         * [progress & readystatechange LOADING]\n         * readystatechange DONE\n         * [timeout | abort | error] | [load]\n         * loadend\n         * */\n//处理 URL.createObjectURL 相关逻辑\nif(me(this,Ue,!1,\"f\"),this.dispatchEvent(new __sharedGlobals.ProgressEvent(\"loadstart\")),a.startsWith(ne)){const t=ue(a);return void(t?setTimeout((()=>{me(this,Re,Ge,\"f\"),this.dispatchEvent(new e(\"readystatechange\")),me(this,Ie,200,\"f\"),me(this,Ee,{\"content-type\":t.type},\"f\"),this.dispatchEvent(new __sharedGlobals.ProgressEvent(\"progress\")),me(this,Re,Le,\"f\"),this.dispatchEvent(new e(\"readystatechange\")),me(this,Me,t.slice(),\"f\"),me(this,Re,Ne,\"f\"),this.dispatchEvent(new e(\"readystatechange\")),this.dispatchEvent(new __sharedGlobals.ProgressEvent(\"load\")),this.dispatchEvent(new __sharedGlobals.ProgressEvent(\"loadend\")),me(this,Ue,!0,\"f\")}),0):(me(this,Re,Ne,\"f\"),this.dispatchEvent(new e(\"readystatechange\")),this.dispatchEvent(new __sharedGlobals.ProgressEvent(\"error\")),this.dispatchEvent(new __sharedGlobals.ProgressEvent(\"loadend\")),me(this,Ue,!0,\"f\")))}let h=!1;const d=new Promise(((r,i)=>{const s=wx.request({url:a,method:n,header:o,data:t,dataType:c,responseType:u,timeout:l,success:r,fail:i}),d=t=>{var r;ge(this,Te,\"f\")||ge(this,Ue,\"f\")||(me(this,Re,Ge,\"f\"),this.dispatchEvent(new e(\"readystatechange\")),me(this,Ie,t.statusCode,\"f\"),me(this,Ee,Ke(t.header),\"f\"),null===(r=ge(this,Se,\"f\"))||void 0===r||r.offHeadersReceived(d),h=!0)};\n// ios 某些机型(iphone12)偶现不会触发 onHeadersReceived\ns.onHeadersReceived(d),me(this,Se,s,\"f\")}));d.then((t=>{ge(this,Te,\"f\")||(h||(me(this,Ie,t.statusCode,\"f\"),me(this,Ee,Ke(t.header),\"f\")),this.dispatchEvent(new __sharedGlobals.ProgressEvent(\"progress\")),me(this,Re,Le,\"f\"),this.dispatchEvent(new e(\"readystatechange\")),\"blob\"===ge(this,xe,\"f\")?me(this,Me,new C([t.data],{type:ge(this,Ee,\"f\")[\"content-type\"]}),\"f\"):me(this,Me,void 0===t.data?null:t.data,\"f\"),t.statusCode>=400&&console.error(`${ge(this,Pe,\"f\")} ${ge(this,_e,\"f\")} ${t.statusCode}`),me(this,Re,Ne,\"f\"),this.dispatchEvent(new e(\"readystatechange\")),this.dispatchEvent(new __sharedGlobals.ProgressEvent(\"load\")))})).catch((t=>{switch(me(this,Re,Ne,\"f\"),this.dispatchEvent(new e(\"readystatechange\")),t.errno){\n//cancel\ncase 1:this.dispatchEvent(new __sharedGlobals.ProgressEvent(\"abort\"));break;\n//timeout\ncase 5:this.dispatchEvent(new __sharedGlobals.ProgressEvent(\"timeout\"));break;\n//other\ndefault:if(ge(this,Te,\"f\"))return;me(this,Me,t,\"f\"),this.dispatchEvent(new __sharedGlobals.ProgressEvent(\"error\"))}})).finally((()=>{me(this,Ue,!0,\"f\"),me(this,Se,null,\"f\"),this.dispatchEvent(new __sharedGlobals.ProgressEvent(\"loadend\"))}))}\n/**\n     * abort the request after send\n     */abort(){\n// if the request have been aborted or have finish the quest\n// do nothing and return void\nge(this,Te,\"f\")||ge(this,Ue,\"f\")||(ge(this,Se,\"f\")&&ge(this,Se,\"f\").abort(),me(this,Te,!0,\"f\"),this.dispatchEvent(new e(\"abort\")))}\n/**\n     * set request header\n     * @param header\n     * @param value\n     */setRequestHeader(e,t){\n// not call .open() yet\nif(this.readyState!=De||this.readyState==De&&!ge(this,Be,\"f\"))throw s(\"Failed to execute 'setRequestHeader' on 'XMLHttpRequest': The object's state must be OPENED.\");if(Ye.findIndex((t=>t.trim()===e))>=0)\n// ignore set\nreturn;const r=e.toLowerCase();ge(this,Oe,\"f\")[r]=(ge(this,Oe,\"f\")[r]?ge(this,Oe,\"f\")[r]+\",\":\"\")+t}\n/**\n     * get response header\n     * @param header\n     */getResponseHeader(e){var t;return null!==(t=ge(this,Ee,\"f\")[e.toLowerCase()])&&void 0!==t?t:null}\n/**\n     * get all response header string\n     */getAllResponseHeaders(){return Object.entries(Ke(ge(this,Ee,\"f\"))).reduce(((e,[t,r])=>(e.push(`${t.toLowerCase()}: ${r}`),e)),[]).join(\"\\r\\n\")}},qe=/* @__PURE__ */Object.assign(__sharedGlobals,{...ve,XMLHttpRequest:Fe});export{v as AbortController,_ as AbortSignal,C as Blob,G as DOMException,S as File,U as FileReader,X as FormData,q as Headers,p as KeyboardEvent,y as MessageEvent,b as MouseEvent,f as PointerEvent,o as ProgressEvent,J as Request,ye as ResizeObserver,fe as ResizeObserverEntry,ee as Response,ie as TextDecoder,se as TextEncoder,u as Touch,d as TouchEvent,c as UIEvent,ae as URL,Fe as XMLHttpRequest,he as atob,R as base642Uint8,ne as blobUrlPrefix,de as btoa,ve as commonLibs,re as fetch,ue as getBlobByBlobUrl,ce as getDataURlByBlobUrl,qe as sharedGlobals,T as uint82base64};\n"], "names": ["e", "t", "s", "a", "n", "o", "l", "u", "c", "h", "d", "b", "f", "p", "y", "v", "r", "i", "wx"], "mappings": ";;;;AAA+C,WAAW,mBAAiB,MAAI;AAAC,MAAG,WAAW,iBAAgB;AAAC,QAAG,CAAC,WAAW,gBAAgB,kBAAkB,OAAM,IAAI,MAAM,+FAA+F;AAAE,WAAO,WAAW;AAAA,EAAe;AAAC,QAAM,IAAE,KAAK,MAAM,IAAI,GAAE,IAAE,IAAI,MAAM,EAAC,mBAAkB,KAAE,GAAE,EAAC,KAAI,CAAC,GAAE,MAAI,EAAE,SAAS,CAAC,IAAE,SAAO,OAAO,OAAO,GAAE,CAAC,IAAE,QAAQ,IAAI,GAAE,CAAC,IAAE,WAAS,IAAE,IAAE,QAAQ,IAAI,YAAW,CAAC,GAAE,SAAQ,CAAAA,OAAG,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,QAAQA,EAAC,GAAE,QAAQ,QAAQ,UAAU,CAAC,EAAE,KAAI,CAAE,CAAC,GAAE,KAAI,CAACA,IAAEC,OAAI,QAAQ,IAAID,IAAEC,EAAC,KAAG,QAAQ,IAAI,YAAWA,EAAC,EAAC,CAAC;AAAE,SAAO;AAAC,GAAC;AAA0Q,SAAS,EAAE,GAAE;AAAC,SAAO,IAAI,gBAAgB,aAAa,GAAE,mBAAmB;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,QAAM,IAAE,oBAAI;AAAQ,SAAM,EAAC,SAAQ,GAAE,IAAI,GAAE;AAAC,UAAM,IAAE,EAAE,IAAI,CAAC;AAAE,QAAG,QAAM,EAAE,OAAM,UAAU,yBAAyB,CAAC,eAAe;AAAE,WAAO;AAAA,EAAC,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,QAAM,IAAE,eAAa,OAAO,cAAY;AAAA,EAEhqC,WAAS,gBAAgB,QAAM,gBAAgB;AAAA,EAE/C,eAAa,OAAO,UAAQ,UAAQ;AAAG,IAAE,eAAe,CAAC,MAAI,EAAE,CAAC,IAAE;AAAE;AAAC,MAAM,UAAUD,mCAAAA,MAAC;AAAA,EAAC,YAAY,GAAE,GAAE;AAAC,UAAM,GAAE,CAAC,GAAE,EAAE,QAAQ,IAAI,MAAK,EAAC,kBAAiB,OAAG,QAAO,GAAE,OAAM,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,EAAE,IAAI,IAAI,EAAE;AAAA,EAAgB;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,EAAE,IAAI,IAAI,EAAE;AAAA,EAAM;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,EAAE,IAAI,IAAI,EAAE;AAAA,EAAK;AAAC;AAAC,MAAM,IAAE,EAAE,EAAE,IAAI;AAAE,MAAM,EAAC;AAAA,EAAC,YAAY,GAAE;AAAC,WAAO,eAAe,MAAK,cAAa,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,UAAS,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,EAAC,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,EAAC,CAAC,GAAE,OAAO,eAAe,MAAK,SAAQ,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,EAAC,CAAC,GAAE,OAAO,eAAe,MAAK,SAAQ,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,EAAC,CAAC,GAAE,OAAO,eAAe,MAAK,SAAQ,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,EAAC,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,EAAC,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,EAAC,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,EAAC,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,EAAC,CAAC,GAAE,OAAO,eAAe,MAAK,iBAAgB,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,EAAC,CAAC,GAAE,KAAK,aAAW,EAAE,YAAW,KAAK,SAAO,EAAE,QAAO,EAAE,YAAU,KAAK,UAAQ,EAAE,UAAS,EAAE,YAAU,KAAK,UAAQ,EAAE,UAAS,EAAE,UAAQ,KAAK,QAAM,EAAE,QAAO,EAAE,UAAQ,KAAK,QAAM,EAAE,QAAO,EAAE,UAAQ,KAAK,QAAM,EAAE,QAAO,EAAE,YAAU,KAAK,UAAQ,EAAE,UAAS,EAAE,YAAU,KAAK,UAAQ,EAAE,UAAS,EAAE,YAAU,KAAK,UAAQ,EAAE,UAAS,EAAE,YAAU,KAAK,UAAQ,EAAE,UAAS,EAAE,kBAAgB,KAAK,gBAAc,EAAE;AAAA,EAAc;AAAC;AAAC,MAAM,UAAUA,mCAAAA,MAAC;AAAA,EAAC,YAAY,GAAE,IAAE,CAAA,GAAG;AAAC,UAAK,EAAC,QAAO,IAAE,GAAE,MAAK,IAAE,MAAK,GAAGE,GAAC,IAAE;AAAE,UAAM,GAAEA,EAAC,GAAE,OAAO,eAAe,MAAK,UAAS,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,QAAO,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,KAAK,SAAO,GAAE,KAAK,OAAK;AAAA,EAAC;AAAC;AAAA;AAC7kE,SAAS,EAAE,GAAE;AAAC,QAAM,IAAE;AAAE,SAAO,EAAE,SAAO,EAAE,OAAK,SAASF,IAAE;AAAC,WAAO,EAAEA,EAAC,KAAG;AAAA,EAAI,IAAG;AAAC;AAAC,MAAM,UAAU,EAAC;AAAA,EAAC,YAAY,GAAE,IAAE,CAAA,GAAG;AAAC,UAAK,EAAC,gBAAe,IAAE,CAAA,GAAG,eAAc,IAAE,CAAA,GAAG,SAAQE,KAAE,CAAA,GAAG,QAAOC,KAAE,OAAG,SAAQC,KAAE,OAAG,SAAQC,KAAE,OAAG,UAASC,KAAE,OAAG,GAAGC,GAAC,IAAE;AAAE,UAAM,GAAEA,EAAC,GAAE,OAAO,eAAe,MAAK,UAAS,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,YAAW,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,kBAAiB,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,iBAAgB,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,KAAK,SAAOJ,IAAE,KAAK,UAAQC,IAAE,KAAK,UAAQC,IAAE,KAAK,WAASC,IAAE,KAAK,iBAAe,kBAAE,CAAC,GAAE,KAAK,gBAAc,kBAAE,CAAC,GAAE,KAAK,UAAQ,kBAAEJ,EAAC;AAAA,EAAC;AAAC;AAAC,MAAM,UAAU,EAAC;AAAA,EAAC,YAAY,GAAE,IAAE,CAAA,GAAG;AAAC,UAAK,EAAC,QAAO,IAAE,GAAE,SAAQ,IAAE,GAAE,SAAQA,KAAE,GAAE,SAAQC,KAAE,GAAE,WAAUC,KAAE,GAAE,WAAUC,KAAE,GAAE,eAAcC,KAAE,MAAK,SAAQC,KAAE,GAAE,SAAQC,KAAE,GAAE,QAAOC,KAAE,OAAG,SAAQC,KAAE,OAAG,SAAQC,KAAE,OAAG,UAASC,KAAE,OAAG,SAAQC,KAAE,GAAE,SAAQC,KAAE,GAAE,GAAGC,GAAC,IAAE;AAAE,UAAM,GAAEA,EAAC,GAAE,OAAO,eAAe,MAAK,UAAS,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,YAAW,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC;AAAA,IAEtrD,OAAO,eAAe,MAAK,KAAI,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,KAAI,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,aAAY,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,aAAY,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,UAAS,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,iBAAgB,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,SAAQ,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,SAAQ,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,KAAK,SAAON,IAAE,KAAK,UAAQC,IAAE,KAAK,UAAQC,IAAE,KAAK,WAASC,IAAE,KAAK,IAAEV,IAAE,KAAK,IAAEC,IAAE,KAAK,UAAQ,GAAE,KAAK,SAAO,GAAE,KAAK,UAAQD,IAAE,KAAK,UAAQC,IAAE,KAAK,UAAQI,IAAE,KAAK,UAAQC,IAAE,KAAK,YAAUJ,IAAE,KAAK,YAAUC,IAAE,KAAK,QAAMH,IAAE,KAAK,QAAMC,IAAE,KAAK,UAAQW,IAAE,KAAK,UAAQD,IAAE,KAAK,gBAAcP;AAAA,EAAC;AAAC;AAAC,MAAM,UAAU,EAAC;AAAA,EAAC,YAAY,GAAE,IAAE,CAAA,GAAG;AAAC,UAAK,EAAC,WAAU,IAAE,GAAE,OAAM,IAAE,GAAE,QAAOJ,KAAE,GAAE,UAASC,KAAE,GAAE,oBAAmBC,KAAE,GAAE,OAAMC,KAAE,GAAE,OAAMC,KAAE,GAAE,OAAMC,KAAE,GAAE,aAAYC,KAAE,IAAG,WAAUC,KAAE,OAAG,GAAGC,GAAC,IAAE;AAAE,UAAM,GAAEA,EAAC,GAAE,OAAO,eAAe,MAAK,aAAY,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,SAAQ,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,UAAS,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,YAAW,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,sBAAqB,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,SAAQ,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,SAAQ,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,SAAQ,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,eAAc,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,aAAY,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,KAAK,YAAU,GAAE,KAAK,QAAM,GAAE,KAAK,SAAOR,IAAE,KAAK,WAAS,KAAK,IAAI,GAAE,KAAK,IAAIC,IAAE,CAAC,CAAC,GAAE,KAAK,qBAAmB,KAAK,IAAI,GAAE,KAAK,IAAIC,IAAE,EAAE,CAAC,GAAE,KAAK,QAAMC,IAAE,KAAK,QAAMC,IAAE,KAAK,QAAMC,IAAE,KAAK,cAAYC,IAAE,KAAK,YAAUC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,WAAM,CAAA;AAAA,EAAE;AAAA,EAAC,qBAAoB;AAAC,WAAM,CAAA;AAAA,EAAE;AAAC;AAAC,MAAM,IAAE,cAAc,EAAC;AAAA,EAAC,YAAY,GAAE,IAAE,CAAA,GAAG;AAAC,UAAK,EAAC,QAAO,IAAE,OAAG,SAAQ,IAAE,OAAG,SAAQP,KAAE,OAAG,UAASC,KAAE,OAAG,UAASC,KAAE,GAAE,MAAKC,KAAE,IAAG,aAAYC,KAAE,OAAG,KAAIC,KAAE,IAAG,SAAQC,KAAE,GAAE,UAASC,KAAE,GAAE,QAAOC,KAAE,OAAG,GAAGC,GAAC,IAAE;AAAE,UAAM,GAAEA,EAAC,GAAE,OAAO,eAAe,MAAK,UAAS,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,YAAW,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC;AAAA;AAAA;AAAA;AAAA,IAK9nH,OAAO,eAAe,MAAK,YAAW,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC;AAAA,IAE9F,OAAO,eAAe,MAAK,QAAO,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC;AAAA,IAE1F,OAAO,eAAe,MAAK,eAAc,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC;AAAA,IAEjG,OAAO,eAAe,MAAK,OAAM,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,IAMzF,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC;AAAA,IAE7F,OAAO,eAAe,MAAK,YAAW,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC;AAAA,IAE9F,OAAO,eAAe,MAAK,UAAS,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,KAAK,SAAO,GAAE,KAAK,UAAQ,GAAE,KAAK,UAAQT,IAAE,KAAK,WAASC,IAAE,KAAK,WAASC,IAAE,KAAK,OAAKC,IAAE,KAAK,cAAYC,IAAE,KAAK,MAAIC,IAAE,KAAK,UAAQC,IAAE,KAAK,WAASC,IAAE,KAAK,SAAOC;AAAA,EAAC;AAAC;AAAE,MAAM,UAAUV,yCAAC;AAAA,EAAC,YAAY,GAAE,IAAE,CAAA,GAAG;AAAC,UAAK,EAAC,MAAK,GAAE,aAAY,GAAE,QAAOE,IAAE,OAAMC,IAAE,QAAOC,IAAE,GAAGC,GAAC,IAAE;AAAE,UAAM,GAAEA,EAAC,GAAE,OAAO,eAAe,MAAK,QAAO,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,eAAc,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,UAAS,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,SAAQ,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,UAAS,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,KAAK,OAAK,GAAE,KAAK,cAAY,KAAG,IAAG,KAAK,SAAOH,MAAG,IAAG,KAAK,QAAMC,MAAG,CAAA,GAAG,KAAK,SAAOC,MAAG;AAAA,EAAI;AAAC;AAAC,MAAM,EAAC;AAAA,EAAC,cAAa;AAAC,MAAE,IAAI,OAAM,IAAE,MAAG,IAAI,IAAC;AAAA,EAAE;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,EAAE,IAAI;AAAA,EAAC;AAAA,EAAC,MAAM,GAAE;AAAC,MAAE,KAAK,MAAM,EAAE,SAAO,GAAE,KAAK,OAAO,cAAc,IAAIJ,mCAAAA,MAAE,OAAO,CAAC;AAAA,EAAC;AAAC;AAAC,MAAK,EAAC,SAAQ,GAAE,KAAI,EAAC,IAAE,EAAE,EAAE,IAAI;AAAE,IAAI,IAAE;AAAG,MAAM,UAAUC,mCAAAA,YAAC;AAAA,EAAC,cAAa;AAAC,QAAG,MAAK,GAAG,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,CAAC,EAAE,OAAM,IAAI,UAAU,qBAAqB;AAAE,QAAE,OAAG,EAAE,IAAI,MAAK,EAAC,SAAQ,OAAG,QAAO,IAAI,gBAAgB,aAAa,oCAAmC,YAAY,EAAC,CAAC,GAAEe,0DAAE,MAAK,SAAS,SAAS,GAAE;AAAC,UAAI;AAAE,WAAK,YAAU,EAAE,IAAI,EAAE,UAAQ,MAAG,UAAQ,IAAE,KAAK,YAAU,WAAS,KAAG,EAAE,KAAK,MAAK,CAAC;AAAA,IAAE,CAAC;AAAA,EAAE;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,EAAE,IAAI,EAAE;AAAA,EAAO;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,EAAE,IAAI,EAAE;AAAA,EAAM;AAAA,EAAC,iBAAgB;AAAC,QAAG,KAAK,QAAQ,OAAM,KAAK;AAAA,EAAM;AAAA,EAAC,OAAO,MAAM,GAAE;AAAC,UAAM,IAAE,IAAI;AAAE,WAAO,EAAE,MAAM,CAAC,GAAE,EAAE;AAAA,EAAM;AAAA,EAAC,OAAO,QAAQ,GAAE;AAAC,UAAM,IAAE,IAAI;AAAE,WAAO,WAAY,MAAI,EAAE,MAAM,IAAI,gBAAgB,aAAa,oBAAmB,SAAS,CAAC,GAAG,CAAC,GAAE,EAAE;AAAA,EAAM;AAAC;AAAC,MAAK,EAAC,SAAQ,GAAE,KAAI,EAAC,IAAE,EAAE,EAAE,IAAI;AAAE,MAAM,IAAE,qEAAoE,IAAE,CAAA;AAAG,SAAQ,IAAE,GAAE,IAAE,IAAG,IAAI,GAAE,EAAE,CAAC,CAAC,IAAE;AAAE,SAAS,EAAE,GAAE;AAAC,QAAM,IAAE,CAAA;AAAG,WAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,KAAG,GAAE;AAAC,UAAM,IAAE,EAAE,CAAC,GAAEd,KAAE,IAAE,IAAE,EAAE,QAAOC,KAAED,KAAE,EAAE,IAAE,CAAC,IAAE,GAAEE,KAAE,IAAE,IAAE,EAAE,QAAOC,KAAED,KAAE,EAAE,IAAE,CAAC,IAAE,GAAEE,KAAE,KAAG,GAAEC,MAAG,IAAE,MAAI,IAAEJ,MAAG;AAAE,QAAIK,MAAG,KAAGL,OAAI,IAAEE,MAAG,GAAEI,KAAE,KAAGJ;AAAE,IAAAD,OAAIK,KAAE,IAAGP,OAAIM,KAAE,MAAK,EAAE,KAAK,EAAEF,EAAC,GAAE,EAAEC,EAAC,GAAE,EAAEC,EAAC,GAAE,EAAEC,EAAC,CAAC;AAAA,EAAC;AAAC,SAAO,EAAE,KAAK,EAAE;AAAC;AAAC,SAAS,EAAE,GAAE;AAE/tE,QAAM,IAAE,EAAE,QAAQ,oBAAmB,EAAE,GAAE,KAAG,QAAM,EAAE,EAAE,SAAO,CAAC,IAAE,IAAE,MAAI,QAAM,EAAE,EAAE,SAAO,CAAC,IAAE,IAAE,IAAG,IAAE,IAAE,EAAE,SAAO,IAAE,GAAEP,KAAE,IAAI,WAAW,CAAC;AAElI,MAAIC,KAAE;AAAE,WAAQH,KAAE,GAAEA,KAAE,EAAE,QAAOA,MAAG,GAAE;AAAC,UAAMgB,KAAE,EAAE,EAAEhB,EAAC,CAAC,GAAEI,KAAE,EAAE,EAAEJ,KAAE,CAAC,CAAC,GAAEK,KAAE,EAAE,EAAEL,KAAE,CAAC,CAAC,KAAG,GAAEM,KAAEU,MAAG,IAAEZ,MAAG,GAAEG,MAAG,KAAGH,OAAI,IAAEC,MAAG,GAAEG,MAAG,IAAEH,OAAI,KAAG,EAAE,EAAEL,KAAE,CAAC,CAAC,KAAG;AAAG,IAAAE,GAAEC,IAAG,IAAEG,IAAEH,KAAE,MAAID,GAAEC,IAAG,IAAEI,KAAGJ,KAAE,MAAID,GAAEC,IAAG,IAAEK;AAAA,EAAE;AAAC,SAAON;AAAC;AAExL,MAAM,IAAE,SAAS,GAAE;AAAC,SAAO,gBAAgB,YAAY,UAAU,OAAO,KAAK,MAAK,CAAC;AAAC;AAEpF,SAAS,EAAE,GAAE;AAAC,MAAG,aAAa,YAAY,QAAO,IAAI,WAAW,EAAE,MAAM,CAAC,CAAC;AAAE,MAAG,YAAY,OAAO,CAAC,EAAE,QAAO,IAAI,WAAW,EAAE,OAAO,MAAM,EAAE,YAAW,EAAE,aAAW,EAAE,UAAU,CAAC;AAAE,QAAM,IAAI,UAAU,qBAAqB;AAAC;AAAC,MAAM,EAAC;AAAA,EAAC,YAAY,IAAE,CAAA,GAAG,IAAE,CAAA,GAAG;AAAC,WAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,QAAO,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,QAAO,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC;AAAE,UAAM,IAAE,IAAI,MAAM,EAAE,MAAM;AAAE,aAAQD,KAAE,GAAE,IAAE,EAAE,QAAOA,KAAE,GAAEA,MAAI;AAAC,YAAMgB,KAAE,EAAEhB,EAAC;AAAE,MAAAgB,cAAa,IAAE,EAAEhB,EAAC,IAAEgB,GAAE,UAAQ,YAAU,OAAOA,KAAE,EAAEhB,EAAC,IAAE,EAAEgB,EAAC,IAAEA,cAAa,eAAa,YAAY,OAAOA,EAAC,IAAE,EAAEhB,EAAC,IAAE,EAAEgB,EAAC,IAAE,EAAEhB,EAAC,IAAE,EAAE,OAAOgB,EAAC,CAAC;AAAA,IAAC;AAAC,SAAK,UAAQ,SAASjB,IAAE;AAAC,YAAMC,KAAED,GAAE,OAAQ,CAACA,IAAEC,OAAID,KAAEC,GAAE,YAAY,CAAC,GAAEe,KAAE,IAAI,WAAWf,EAAC;AAAE,UAAI,IAAE;AAAE,aAAOD,GAAE,QAAS,CAAAA,OAAG;AAAC,QAAAgB,GAAE,IAAIhB,IAAE,CAAC,GAAE,KAAGA,GAAE;AAAA,MAAU,CAAC,GAAGgB;AAAA,IAAC,EAAE,CAAC,GAAE,KAAK,OAAK,KAAK,QAAQ,QAAO,KAAK,OAAK,OAAO,EAAE,IAAI,KAAG,IAAG,mBAAmB,KAAK,KAAK,IAAI,IAAE,KAAK,OAAK,KAAG,KAAK,OAAK,KAAK,KAAK,YAAW;AAAA,EAAE;AAAA,EAAC,cAAa;AAAC,WAAO,QAAQ,QAAQ,KAAK,QAAQ,OAAO,MAAM,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAO,QAAQ,SAAS,IAAE,KAAK,SAAQ,gBAAgB,YAAY,UAAU,OAAO,KAAK,MAAK,CAAC,EAAC;AAAG,QAAI;AAAA,EAAC;AAAA,EAAC,MAAM,GAAE,GAAE,GAAE;AAAC,UAAM,IAAE,KAAK,QAAQ,MAAM,KAAG,GAAE,KAAG,KAAK,QAAQ,MAAM;AAAE,WAAO,IAAI,EAAE,CAAC,CAAC,GAAE,EAAC,MAAK,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,QAAI,IAAE;AAAE,UAAM,IAAE;AAAK,WAAO,IAAI,gBAAgB,eAAe,EAAC,MAAK,SAAQ,uBAAsB,QAAO,KAAK,GAAE;AAAC,YAAM,IAAE,EAAE,YAAY;AAAK,aAAO,EAAE,MAAM,GAAE,IAAE,EAAE,UAAU,EAAE,YAAW,EAAG,KAAM,SAASd,IAAE;AAAC,cAAMC,KAAE,IAAI,WAAWD,EAAC,GAAEE,KAAED,GAAE;AAAW,aAAGC;AAAA,QAEliD,EAAE,IAAID,EAAC,GAAE,EAAE,YAAY,QAAQC,EAAC,GAAE,KAAG,EAAE,QAAM,EAAE,MAAK;AAAA,MAAE,CAAC;AAAA,IAAE,EAAC,CAAC;AAAA,EAAC;AAAC;AAAC,MAAM,UAAU,EAAC;AAAA,EAAC,YAAY,GAAE,GAAE,IAAE,CAAA,GAAG;AAAC,UAAM,GAAE,CAAC,GAAE,OAAO,eAAe,MAAK,gBAAe,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,QAAO,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,KAAK,OAAK,EAAE,QAAQ,OAAM,GAAG,GAAE,KAAK,eAAa,EAAE,EAAE,gBAAc;AAAA,EAAE;AAAC;AAAC,MAAM,IAAE,GAAE,IAAE;AAAE,eAAe,EAAE,GAAE,GAAE,GAAE;AAAC,MAAG,EAAE,aAAa,GAAG,OAAM,IAAI,UAAU,wBAAsB,IAAE,uDAAuD;AAAE,SAAO,IAAI,QAAS,CAAC,GAAEF,OAAI;AAAC,eAAY,YAAS;AAAC,cAAO,EAAE,SAAO,IAAG,EAAE,aAAW,GAAE,EAAE,cAAc,IAAI,gBAAgB,cAAc,aAAY,EAAC,kBAAiB,MAAG,QAAO,GAAE,OAAM,EAAE,QAAQ,WAAU,CAAC,CAAC,GAAE,GAAC;AAAA,QAAE,KAAI;AAAgB,YAAE,SAAO,UAAQ,EAAE,OAAK,aAAW,EAAE,EAAE,OAAO;AAAE;AAAA,QAAM,KAAI;AAAoB,YAAE,SAAO,MAAM,EAAE,YAAW;AAAG;AAAA,QAAM,KAAI;AAAa,YAAE,SAAO,MAAM,EAAE,KAAI;AAAA,MAAE;AAAC,YAAMA,KAAE,EAAC,kBAAiB,MAAG,QAAO,EAAE,QAAQ,YAAW,OAAM,EAAE,QAAQ,WAAU;AAAE,QAAE,cAAc,IAAI,gBAAgB,cAAc,YAAWA,EAAC,CAAC,GAAE,EAAE,cAAc,IAAI,gBAAgB,cAAc,QAAOA,EAAC,CAAC,GAAE,EAAE,cAAc,IAAI,gBAAgB,cAAc,WAAUA,EAAC,CAAC,GAAE,EAAC;AAAA,IAAE,GAAG,CAAC;AAAA,EAAC,CAAC;AAAE;AAAC,MAAM,IAAE,cAAcD,mCAAAA,YAAC;AAAA,EAAC,cAAa;AAAC,UAAK,GAAG,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,UAAS,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,eAAc,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,cAAa,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,aAAY,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,cAAa,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,EAAC,CAAC,GAAE,OAAO,eAAe,MAAK,SAAQ,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,UAAS,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,KAAI,CAAC,GAAEe,mCAAAA,uBAAE,MAAK,SAAS,OAAG;AAAC,UAAI;AAAE,aAAO,UAAQ,IAAE,KAAK,YAAU,WAAS,IAAE,SAAO,EAAE,KAAK,MAAK,CAAC;AAAA,IAAC,IAAIA,mCAAAA,uBAAE,MAAK,SAAS,OAAG;AAAC,UAAI;AAAE,aAAO,UAAQ,IAAE,KAAK,YAAU,WAAS,IAAE,SAAO,EAAE,KAAK,MAAK,CAAC;AAAA,IAAC,CAAC,GAAGA,mCAAAA,uBAAE,MAAK,QAAQ,OAAG;AAAC,UAAI;AAAE,aAAO,UAAQ,IAAE,KAAK,WAAS,WAAS,IAAE,SAAO,EAAE,KAAK,MAAK,CAAC;AAAA,IAAC,CAAC,GAAGA,mCAAAA,uBAAE,MAAK,aAAa,OAAG;AAAC,UAAI;AAAE,aAAO,UAAQ,IAAE,KAAK,gBAAc,WAAS,IAAE,SAAO,EAAE,KAAK,MAAK,CAAC;AAAA,IAAC,CAAC,GAAGA,mCAAAA,uBAAE,MAAK,YAAY,OAAG;AAAC,UAAI;AAAE,aAAO,UAAQ,IAAE,KAAK,eAAa,WAAS,IAAE,SAAO,EAAE,KAAK,MAAK,CAAC;AAAA,IAAC,IAAIA,mCAAAA,uBAAE,MAAK,WAAW,OAAG;AAAC,UAAI;AAAE,aAAO,UAAQ,IAAE,KAAK,cAAY,WAAS,IAAE,SAAO,EAAE,KAAK,MAAK,CAAC;AAAA,IAAC,CAAC;AAAA,EAAE;AAAA,EAAC,cAAc,GAAE;AAAC,MAAE,MAAK,GAAE,eAAe;AAAA,EAAC;AAAA,EAAC,WAAW,GAAE;AAAC,MAAE,MAAK,GAAE,YAAY;AAAA,EAAC;AAAA,EAAC,kBAAkB,GAAE;AAAC,MAAE,MAAK,GAAE,mBAAmB;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,cAAc,IAAI,gBAAgB,cAAc,SAAQ,EAAC,kBAAiB,OAAG,QAAO,GAAE,OAAM,EAAC,CAAC,CAAC,GAAE,KAAK,SAAO;AAAA,EAAI;AAAC,GAAE,IAAE;AAAA,EAAC,gBAAe;AAAA,EAAE,oBAAmB;AAAA,EAAE,uBAAsB;AAAA,EAAE,oBAAmB;AAAA,EAAE,uBAAsB;AAAA,EAAE,qBAAoB;AAAA,EAAE,6BAA4B;AAAA,EAAE,eAAc;AAAA,EAAE,mBAAkB;AAAA,EAAE,qBAAoB;AAAA,EAAG,mBAAkB;AAAA,EAAG,YAAW;AAAA,EAAG,0BAAyB;AAAA,EAAG,eAAc;AAAA,EAAG,oBAAmB;AAAA,EAAG,gBAAe;AAAA;AAAA,EAExtG,mBAAkB;AAAA,EAAG,cAAa;AAAA,EAAG,aAAY;AAAA,EAAG,WAAU;AAAA,EAAG,kBAAiB;AAAA,EAAG,oBAAmB;AAAA,EAAG,aAAY;AAAA,EAAG,uBAAsB;AAAA,EAAG,gBAAe;AAAE;AAAE,MAAM,UAAU,MAAK;AAAA,EAAC,YAAY,GAAE,GAAE;AAAC,UAAM,CAAC,GAAE,OAAO,eAAe,MAAK,SAAQ,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,EAAC,CAAC,GAAE,MAAM,qBAAmB,MAAM,kBAAkB,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,QAAI;AAAE,WAAO,UAAQ,IAAE,EAAE,KAAK,KAAK,QAAQ,mBAAkB,OAAO,EAAE,YAAW,CAAE,MAAI,WAAS,IAAE,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,QAAI;AAAE,WAAO,UAAQ,IAAE,KAAK,UAAQ,WAAS,IAAE,IAAE;AAAA,EAAO;AAAC;AAAC,MAAM,IAAE;AAOliB,SAAS,EAAE,GAAE,GAAE,GAAE;AAAC,SAAO,aAAa,gBAAgB,QAAM,IAAE,WAAS,IAAE,OAAO,IAAE,EAAE,IAAE,aAAa,gBAAgB,OAAK,EAAE,OAAK,QAAO,EAAE,SAAO,KAAG,oBAAkB,OAAO,UAAU,SAAS,KAAK,CAAC,MAAI,IAAE,IAAI,gBAAgB,KAAK,CAAC,CAAC,GAAE,CAAC,IAAG,CAAC,OAAO,CAAC,GAAE,CAAC,KAAG,CAAC,OAAO,CAAC,GAAE,OAAO,CAAC,CAAC;AAAC;AAGlR,SAAS,EAAE,GAAE;AAAC,SAAO,EAAE,QAAQ,aAAY,MAAM;AAAC;AAG/C,MAAM,EAAC;AAAA,EAAC,cAAa;AAKxB,WAAO,eAAe,MAAK,SAAQ,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,CAAA,EAAE,CAAC;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOjF,OAAO,GAAE,GAAE,GAAE;AAAC,SAAK,MAAM,KAAK,EAAE,GAAE,GAAE,CAAC,CAAC;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvC,OAAO,GAAE;AAAC,UAAM,IAAE,CAAA;AAAG,QAAE,OAAO,CAAC,GAAE,KAAK,MAAM,QAAS,OAAG;AAAC,QAAE,CAAC,MAAI,KAAG,EAAE,KAAK,CAAC;AAAA,IAAC,CAAC,GAAG,KAAK,QAAM;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5F,IAAI,GAAE;AAAC,UAAM,IAAE,KAAK;AAAM,QAAE,OAAO,CAAC;AAAE,aAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,IAAI,KAAG,EAAE,CAAC,EAAE,CAAC,MAAI,EAAE,QAAO,EAAE,CAAC,EAAE,CAAC;AAAE,WAAO;AAAA,EAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1G,OAAO,GAAE;AAAC,UAAM,IAAE,CAAA;AAAG,WAAO,IAAE,OAAO,CAAC,GAAE,KAAK,MAAM,QAAS,OAAG;AAAC,QAAE,CAAC,MAAI,KAAG,EAAE,KAAK,EAAE,CAAC,CAAC;AAAA,IAAC,CAAC,GAAG;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM3F,IAAI,GAAE;AAAC,QAAE,OAAO,CAAC;AAAE,aAAQ,IAAE,GAAE,IAAE,KAAK,MAAM,QAAO,IAAI,KAAG,KAAK,MAAM,CAAC,EAAE,CAAC,MAAI,EAAE,QAAM;AAAG,WAAM;AAAA,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOhG,IAAI,GAAE,GAAE,GAAE;AAAC,QAAE,OAAO,CAAC;AAAE,UAAM,IAAE,IAAGd,KAAE,EAAE,GAAE,GAAE,CAAC;AAAE,QAAIC,KAAE;AAI1D,SAAK,MAAM,QAAS,CAAAF,OAAG;AAAC,MAAAA,GAAE,CAAC,MAAI,IAAEE,OAAIA,KAAE,CAAC,EAAE,KAAKD,EAAC,KAAG,EAAE,KAAKD,EAAC;AAAA,IAAC,CAAC,GAAGE,MAAG,EAAE,KAAKD,EAAC,GAAE,KAAK,QAAM;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAIlF,QAAO;AAAC,UAAM,IAAE,2BAAyB,KAAK,OAAM,GAAG,IAAE,CAAA,GAAG,IAAE,KAAK,CAAC;AAAA;AAA6C,WAAO,KAAK,QAAS,CAACF,IAAE,MAAI,YAAU,OAAOA,KAAE,EAAE,KAAK,IAAE,mBAAmB,EAAE,CAAC,CAAC,IAAE;AAAA;AAAA,EAAY,EAAEA,EAAC,CAAC;AAAA,CAAM,IAAE,EAAE,KAAK,IAAE,mBAAmB,EAAE,CAAC,CAAC,IAAE,gBAAgB,mBAAmBA,GAAE,IAAI,CAAC;AAAA,gBAAsBA,GAAE,QAAM,0BAA0B;AAAA;AAAA,GAAWA,IAAE,MAAM,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC,IAAI,GAAE,IAAI,gBAAgB,KAAK,GAAE,EAAC,MAAK,mCAAiC,EAAC,CAAC;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM7c,QAAQ,GAAE,GAAE;AAAC,eAAS,CAAC,GAAE,CAAC,KAAI,KAAK,GAAE,KAAK,GAAE,GAAE,GAAE,IAAI;AAAA,EAAC;AAAA,EAAC,CAAC,OAAM;AAAC,eAAS,CAAC,CAAC,KAAI,KAAK,OAAM;AAAA,EAAC;AAAA,EAAC,CAAC,SAAQ;AAAC,eAAS,CAAA,EAAE,CAAC,KAAI,KAAK,OAAM;AAAA,EAAC;AAAA,EAAC,CAAC,UAAS;AAAC,WAAM,KAAK,MAAM,OAAM;AAAA,EAAE;AAAA,EAAC,CAAC,OAAO,QAAQ,IAAG;AAAC,WAAO,KAAK;EAAS;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,MAAG,YAAU,OAAO,MAAI,IAAE,OAAO,CAAC,IAAG,6BAA6B,KAAK,CAAC,KAAG,OAAK,EAAE,OAAM,IAAI,UAAU,8CAA4C,IAAE,GAAG;AAAE,SAAO,EAAE,YAAW;AAAE;AAAC,SAAS,EAAE,GAAE;AAAC,SAAM,YAAU,OAAO,MAAI,IAAE,OAAO,CAAC,IAAG;AAAC;AAE7c,MAAM,IAAE,CAAC,WAAU,UAAS,OAAM,QAAO,WAAU,SAAQ,QAAO,OAAM,OAAO;AAAE,MAAM,EAAC;AAAA,EAAC,YAAY,GAAE;AAAC,WAAO,eAAe,MAAK,SAAQ,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,CAAA,EAAE,CAAC,GAAE,aAAa,IAAE,EAAE,QAAS,CAACA,IAAE,MAAI;AAAC,WAAK,OAAO,GAAEA,EAAC;AAAA,IAAC,GAAG,IAAI,IAAE,MAAM,QAAQ,CAAC,IAAE,EAAE,QAAS,CAAAA,OAAG;AAAC,UAAG,KAAGA,GAAE,OAAO,OAAM,IAAI,UAAU,wEAAsEA,GAAE,MAAM;AAAE,WAAK,OAAOA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,IAAC,GAAG,IAAI,IAAE,KAAG,OAAO,oBAAoB,CAAC,EAAE,QAAS,OAAG;AAAC,WAAK,OAAO,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,OAAO,GAAE,GAAE;AAAC,QAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC;AAAE,UAAM,IAAE,KAAK,MAAM,CAAC;AAAE,SAAK,MAAM,CAAC,IAAE,IAAE,IAAE,OAAK,IAAE;AAAA,EAAC;AAAA,EAAC,OAAO,GAAE;AAAC,WAAO,KAAK,MAAM,EAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,GAAE;AAAC,WAAO,IAAE,EAAE,CAAC,GAAE,KAAK,IAAI,CAAC,IAAE,KAAK,MAAM,CAAC,IAAE;AAAA,EAAI;AAAA,EAAC,IAAI,GAAE;AAAC,WAAO,KAAK,MAAM,eAAe,EAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,GAAE,GAAE;AAAC,SAAK,MAAM,EAAE,CAAC,CAAC,IAAE,EAAE,CAAC;AAAA,EAAC;AAAA,EAAC,QAAQ,GAAE,GAAE;AAAC,eAAU,KAAK,OAAO,QAAQ,KAAK,KAAK,EAAE,GAAE,KAAK,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,IAAI;AAAA,EAAC;AAAA,EAAC,CAAC,OAAM;AAAC,WAAM,OAAO,KAAK,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,CAAC,SAAQ;AAAC,WAAM,OAAO,OAAO,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,CAAC,UAAS;AAAC,WAAM,OAAO,QAAQ,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,CAAC,OAAO,QAAQ,IAAG;AAAC,WAAO,KAAK;EAAS;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,MAAG,EAAE,MAAM,QAAO,EAAE,MAAM,CAAC;AAAE;AAAC,UAAM,IAAE,IAAI,WAAW,EAAE,UAAU;AAAE,WAAO,EAAE,IAAI,IAAI,WAAW,CAAC,CAAC,GAAE,EAAE;AAAA,EAAM;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,MAAG,CAAC,EAAE,QAAQ,QAAO,EAAE,WAAS,QAAQ,OAAO,IAAI,UAAU,cAAc,CAAC,IAAE,MAAK,EAAE,WAAS;AAAG;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,IAAI,QAAS,SAAS,GAAE,GAAE;AAAC,MAAE,SAAO,WAAU;AAAC,QAAE,EAAE,MAAM;AAAA,IAAC,GAAE,EAAE,UAAQ,WAAU;AAAC,QAAE,EAAE,KAAK;AAAA,IAAC;AAAA,EAAC,CAAC;AAAE;AAAC,SAAS,EAAE,GAAE;AAAC,QAAM,IAAE,IAAI,gBAAgB,cAAW,IAAE,EAAE,CAAC;AAAE,SAAO,EAAE,kBAAkB,CAAC,GAAE;AAAC;AAAC,SAAS,EAAE,IAAE,IAAG;AAAC,QAAM,IAAE,IAAI,gBAAgB;AAAS,SAAO,EAAE,KAAI,EAAG,MAAM,GAAG,EAAE,QAAS,SAASA,IAAE;AAAC,QAAGA,IAAE;AAAC,YAAM,IAAEA,GAAE,MAAM,GAAG,GAAE,IAAE,EAAE,MAAK,EAAG,QAAQ,OAAM,GAAG,GAAEE,KAAE,EAAE,KAAK,GAAG,EAAE,QAAQ,OAAM,GAAG;AAAE,QAAE,OAAO,mBAAmB,CAAC,GAAE,mBAAmBA,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC,CAAC,GAAG;AAAC;AAAC,MAAM,EAAC;AAAA,EAAC,cAAa;AAAC,WAAO,eAAe,MAAK,aAAY,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,aAAY,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,aAAY,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,iBAAgB,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,oBAAmB,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC;AAAA,EAAC;AAAA,EAAC,UAAU,GAAE;AAAC,QAAI;AAAE,SAAK,YAAU,GAAE,IAAE,YAAU,OAAO,IAAE,KAAK,YAAU,IAAE,aAAa,gBAAgB,OAAK,KAAK,YAAU,IAAE,aAAa,gBAAgB,WAAS,KAAK,gBAAc,KAAG,IAAE,MAAI,SAAS,UAAU,cAAc,CAAC,KAAG,KAAK,mBAAiB,EAAE,EAAE,MAAM;AAAA,IAE//E,KAAK,YAAU,IAAI,gBAAgB,KAAK,CAAC,KAAK,gBAAgB,CAAC,KAAG,aAAa,eAAa,YAAY,OAAO,CAAC,IAAE,KAAK,mBAAiB,EAAE,CAAC,IAAE,KAAK,YAAU,IAAE,OAAO,UAAU,SAAS,KAAK,CAAC,KAAG,KAAK,UAAQ,MAAG,KAAK,YAAU,KAAI,KAAK,QAAQ,IAAI,cAAc,MAAI,YAAU,OAAO,IAAE,KAAK,QAAQ,IAAI,gBAAe,0BAA0B,IAAE,KAAK,aAAW,KAAK,UAAU,QAAM,KAAK,QAAQ,IAAI,gBAAe,KAAK,UAAU,IAAI;AAAA,EAAE;AAAA,EAAC,aAAa,GAAE;AAAC,SAAK,WAAS;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,UAAM,IAAE,EAAE,IAAI;AAAE,QAAG,EAAE,QAAO;AAAE,QAAG,KAAK,UAAU,QAAO,QAAQ,QAAQ,KAAK,SAAS;AAAE,QAAG,KAAK,iBAAiB,QAAO,QAAQ,QAAQ,IAAI,gBAAgB,KAAK,CAAC,KAAK,gBAAgB,CAAC,CAAC;AAAE,QAAG,KAAK,cAAc,OAAM,IAAI,MAAM,sCAAsC;AAAE,WAAO,QAAQ,QAAQ,IAAI,gBAAgB,KAAK,CAAC,KAAK,SAAS,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,QAAG,KAAK,kBAAiB;AAAC,YAAM,IAAE,EAAE,IAAI;AAAE,aAAO,MAAI,YAAY,OAAO,KAAK,gBAAgB,IAAE,QAAQ,QAAQ,KAAK,iBAAiB,OAAO,MAAM,KAAK,iBAAiB,YAAW,KAAK,iBAAiB,aAAW,KAAK,iBAAiB,UAAU,CAAC,IAAE,QAAQ,QAAQ,KAAK,gBAAgB;AAAA,IAAE;AAAC,WAAO,KAAK,OAAO,KAAK,CAAC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,UAAM,IAAE,EAAE,IAAI;AAAE,QAAG,EAAE,QAAO;AAAE,QAAG,KAAK,UAAU,QAAO,SAASF,IAAE;AAAC,YAAM,IAAE,IAAI,gBAAgB,cAAW,IAAE,EAAE,CAAC;AAGjuC,aAAO,EAAE,WAAWA,EAAC,GAAE;AAAA,IAAC,EAAE,KAAK,SAAS;AAAE,QAAG,KAAK,iBAAiB,QAAO,QAAQ,QAAQ,SAASA,IAAE;AAAC,YAAM,IAAE,IAAI,WAAWA,EAAC,GAAE,IAAE,IAAI,MAAM,EAAE,MAAM;AAAE,eAAQA,KAAE,GAAEA,KAAE,EAAE,QAAOA,KAAI,GAAEA,EAAC,IAAE,OAAO,aAAa,EAAEA,EAAC,CAAC;AAAE,aAAO,EAAE,KAAK,EAAE;AAAA,IAAC,EAAE,KAAK,gBAAgB,CAAC;AAAE,QAAG,KAAK,cAAc,OAAM,IAAI,MAAM,sCAAsC;AAAE,WAAO,QAAQ,QAAQ,KAAK,SAAS;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,WAAO,KAAK,OAAO,KAAK,CAAC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAO,KAAK,KAAI,EAAG,KAAM,CAAC,IAAE,OAAK,KAAK,MAAM,CAAC,CAAC;AAAA,EAAE;AAAC;AAAC,MAAM,UAAU,EAAC;AAAA,EAAC,YAAY,GAAE,IAAE,IAAG;AAAC,UAAK,GAAG,OAAO,eAAe,MAAK,QAAO,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,KAAI,CAAC,GAAE,OAAO,eAAe,MAAK,YAAW,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,MAAE,CAAC,GAAE,OAAO,eAAe,MAAK,SAAQ,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,UAAS,CAAC,GAAE,OAAO,eAAe,MAAK,eAAc,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,cAAa,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,aAAY,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,GAAE,CAAC,GAAE,OAAO,eAAe,MAAK,UAAS,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,MAAK,CAAC,GAAE,OAAO,eAAe,MAAK,QAAO,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,YAAW,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,SAAQ,CAAC,GAAE,OAAO,eAAe,MAAK,YAAW,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,eAAc,CAAC,GAAE,OAAO,eAAe,MAAK,kBAAiB,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,GAAE,CAAC,GAAE,OAAO,eAAe,MAAK,UAAS,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,OAAM,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC;AAAE,QAAI,IAAE,EAAE;AAAK,QAAG,aAAa,GAAE;AAAC,UAAG,EAAE,SAAS,OAAM,IAAI,UAAU,cAAc;AAAE,WAAK,MAAI,EAAE,KAAI,KAAK,cAAY,EAAE,aAAY,EAAE,YAAU,KAAK,UAAQ,IAAI,EAAE,EAAE,OAAO,IAAG,KAAK,SAAO,EAAE,QAAO,KAAK,OAAK,EAAE,MAAK,KAAK,SAAO,EAAE,QAAO,KAAG,QAAM,EAAE,cAAY,IAAE,EAAE,WAAU,EAAE,aAAa,IAAE;AAAA,IAAE,MAAM,MAAK,MAAI,OAAO,CAAC;AAAE,QAAG,KAAK,cAAY,EAAE,eAAa,KAAK,eAAa,eAAc,KAAK,UAAQ,KAAK,WAAS,IAAI,EAAE,EAAE,OAAO,GAAE,KAAK,SAAO,SAASA,IAAE;AAAC,YAAMC,KAAED,GAAE,YAAW;AAAG,aAAO,EAAE,QAAQC,EAAC,IAAE,KAAGA,KAAED;AAAA,IAAC,EAAE,EAAE,UAAQ,KAAK,UAAQ,KAAK,GAAE,KAAK,OAAK,EAAE,QAAM,KAAK,QAAM,MAAK,KAAK,SAAO,EAAE,UAAS,IAAI,gBAAgB,kBAAiB,SAAQ,UAAQ,KAAK,UAAQ,WAAS,KAAK,WAAS,EAAE,OAAM,IAAI,UAAU,2CAA2C;AAAE,QAAG,KAAK,UAAU,CAAC,GAAE,EAAE,UAAQ,KAAK,UAAQ,WAAS,KAAK,UAAQ,eAAa,EAAE,SAAO,eAAa,EAAE,QAAO;AAE1iF,YAAMA,KAAE;AAAgB,UAAGA,GAAE,KAAK,KAAK,GAAG;AAE1C,aAAK,MAAI,KAAK,IAAI,QAAQA,IAAE,UAAQ,oBAAI,QAAM,QAAO,CAAE;AAAA,WAAM;AAE7D,cAAMA,KAAE;AAAK,aAAK,QAAMA,GAAE,KAAK,KAAK,GAAG,IAAE,MAAI,OAAK,QAAM,oBAAI,QAAM,QAAO;AAAA,MAAE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,WAAO,IAAI,EAAE,MAAK,EAAC,MAAK,KAAK,UAAS,CAAC;AAAA,EAAC;AAAC;AAAC,MAAM,IAAE,CAAC,KAAI,KAAI,KAAI,KAAI,GAAG;AAAE,MAAM,WAAW,EAAC;AAAA,EAAC,YAAY,GAAE,GAAE;AAAC,QAAG,MAAK,GAAG,OAAO,eAAe,MAAK,YAAW,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,MAAE,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,MAAK,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,cAAa,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,MAAE,CAAC,GAAE,OAAO,eAAe,MAAK,UAAS,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,cAAa,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,QAAO,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,OAAM,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,GAAE,CAAC,GAAE,EAAE,gBAAgB,IAAI,OAAM,IAAI,UAAU,4FAA4F;AAAE,QAAG,MAAI,IAAE,CAAA,IAAI,KAAK,OAAK,WAAU,KAAK,SAAO,WAAS,EAAE,SAAO,MAAI,EAAE,QAAO,KAAK,SAAO,OAAK,KAAK,SAAO,IAAI,OAAM,IAAI,WAAW,0FAA0F;AAAE,SAAK,KAAG,KAAK,UAAQ,OAAK,KAAK,SAAO,KAAI,KAAK,aAAW,WAAS,EAAE,aAAW,KAAG,KAAG,EAAE,YAAW,KAAK,UAAQ,IAAI,EAAE,EAAE,OAAO,GAAE,KAAK,MAAI,EAAE,OAAK,IAAG,KAAK,UAAU,CAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,WAAO,IAAI,GAAG,KAAK,WAAU,EAAC,QAAO,KAAK,QAAO,YAAW,KAAK,YAAW,SAAQ,IAAI,EAAE,KAAK,OAAO,GAAE,KAAI,KAAK,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,OAAO,QAAO;AAAC,UAAM,IAAE,IAAI,GAAG,MAAK,EAAC,QAAO,KAAI,YAAW,GAAE,CAAC;AAAE,WAAO,EAAE,KAAG,OAAG,EAAE,SAAO,GAAE,EAAE,OAAK,SAAQ;AAAA,EAAC;AAAA,EAAC,OAAO,SAAS,GAAE,GAAE;AAAC,QAAG,KAAG,OAAK,EAAE,QAAQ,CAAC,EAAE,OAAM,IAAI,WAAW,qBAAqB;AAAE,WAAO,IAAI,GAAG,MAAK,EAAC,QAAO,GAAE,SAAQ,EAAC,UAAS,EAAC,EAAC,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAG,GAAE;AAAC,QAAM,IAAE,IAAI;AAMj1D,SAAO,EAAE,QAAQ,gBAAe,GAAG,EAAE,MAAM,IAAI,EAAE,IAAK,SAASA,IAAE;AAAC,WAAO,MAAIA,GAAE,QAAQ,IAAI,IAAEA,GAAE,OAAO,GAAEA,GAAE,MAAM,IAAEA;AAAA,EAAC,CAAC,EAAG,QAAS,SAASA,IAAE;AAAC,UAAM,IAAEA,GAAE,MAAM,GAAG,GAAE,IAAE,EAAE,MAAK,EAAG,KAAI;AAAG,QAAG,GAAE;AAAC,YAAMA,KAAE,EAAE,KAAK,GAAG,EAAE,KAAI;AAAG,UAAG;AAAC,UAAE,OAAO,GAAEA,EAAC;AAAA,MAAC,SAAOA,IAAE;AAAC,gBAAQ,KAAK,cAAYA,GAAE,OAAO;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC,CAAC,GAAG;AAAC;AAAC,SAAS,GAAG,GAAE,GAAE;AAAC,SAAO,IAAI,QAAS,SAAS,GAAE,GAAE;AAAC,UAAME,KAAE,IAAI,EAAE,GAAE,CAAC;AAAE,QAAGA,GAAE,UAAQA,GAAE,OAAO,QAAQ,QAAO,EAAE,IAAI,gBAAgB,aAAa,WAAU,YAAY,CAAC;AAAE,UAAMC,KAAE,IAAI,gBAAgB;AAAe,aAASC,KAAG;AAAC,MAAAD,GAAE,MAAK;AAAA,IAAE;AAAC,IAAAA,GAAE,SAAO,WAAU;AAAC,YAAMH,KAAE,EAAC,YAAWG,GAAE,YAAW,SAAQ,GAAGA,GAAE,sBAAqB,KAAI,EAAE,EAAC;AAGlmB,YAAID,GAAE,IAAI,QAAQ,SAAS,MAAIC,GAAE,SAAO,OAAKA,GAAE,SAAO,OAAKH,GAAE,SAAO,MAAIA,GAAE,SAAOG,GAAE,QAAOH,GAAE,MAAIG,GAAE;AAAY,YAAMF,KAAEE,GAAE;AAAS,iBAAY,WAAU;AAAC,UAAE,IAAI,GAAGF,IAAED,EAAC,CAAC;AAAA,MAAC,GAAG,CAAC;AAAA,IAAC,GAAEG,GAAE,UAAQ,WAAU;AAAC,iBAAY,WAAU;AAAC,UAAE,IAAI,UAAU,wBAAwB,CAAC;AAAA,MAAC,GAAG,CAAC;AAAA,IAAC,GAAEA,GAAE,YAAU,WAAU;AAAC,iBAAY,WAAU;AAAC,UAAE,IAAI,UAAU,2BAA2B,CAAC;AAAA,MAAC,GAAG,CAAC;AAAA,IAAC,GAAEA,GAAE,UAAQ,WAAU;AAAC,iBAAY,WAAU;AAAC,UAAE,IAAI,gBAAgB,aAAa,WAAU,YAAY,CAAC;AAAA,MAAC,GAAG,CAAC;AAAA,IAAC,GAAEA,GAAE,KAAKD,GAAE,QAAOA,GAAE,KAAI,IAAE,GAAE,cAAYA,GAAE,cAAYC,GAAE,kBAAgB,OAAG,WAASD,GAAE,gBAAcC,GAAE,kBAAgB,QAAIA,GAAE,eAAa,QAAOD,GAAE,QAAQ,QAAS,SAASF,IAAEC,IAAE;AAAC,MAAAE,GAAE,iBAAiBF,IAAED,EAAC;AAAA,IAAC,CAAC,GAAGE,GAAE,WAASA,GAAE,OAAO,iBAAiB,SAAQE,EAAC,GAAED,GAAE,qBAAmB,WAAU;AAEnvB,YAAIA,GAAE,cAAYD,GAAE,OAAO,oBAAoB,SAAQE,EAAC;AAAA,IAAC;AAAA,IAEzDD,GAAE,KAAK,WAASD,GAAE,YAAU,OAAKA,GAAE,SAAS;AAAA,EAAC,CAAC;AAAE;AAI3C,MAAM,GAAE;AAAA,EAAC,OAAO,GAAE;AAAC,QAAI;AAAE,QAAE,aAAa,cAAY,IAAI,WAAW,CAAC,IAAE;AAAE,UAAM,IAAE,EAAE,QAAO,IAAE,CAAA;AAAG,QAAIA,KAAE;AAAE,WAAKA,KAAE,KAAG;AAAC,YAAMF,KAAE,EAAEE,EAAC;AAAE,UAAIC,KAAE,MAAKC,KAAEJ,KAAE,MAAI,IAAEA,KAAE,MAAI,IAAEA,KAAE,MAAI,IAAE;AAAE,UAAGE,KAAEE,MAAG,GAAE;AAAC,YAAIY,IAAEC,IAAEZ,IAAEC;AAAE,gBAAOF,IAAC;AAAA,UAAE,KAAK;AAAE,YAAAJ,KAAE,QAAMG,KAAEH;AAAG;AAAA,UAAM,KAAK;AAAE,YAAAgB,KAAE,EAAEd,KAAE,CAAC,GAAE,QAAM,MAAIc,QAAKV,MAAG,KAAGN,OAAI,IAAE,KAAGgB,IAAEV,KAAE,QAAMH,KAAEG;AAAI;AAAA,UAAM,KAAK;AAAE,YAAAU,KAAE,EAAEd,KAAE,CAAC,GAAEe,KAAE,EAAEf,KAAE,CAAC,GAAE,QAAM,MAAIc,OAAI,QAAM,MAAIC,QAAKX,MAAG,KAAGN,OAAI,MAAI,KAAGgB,OAAI,IAAE,KAAGC,IAAEX,KAAE,SAAOA,KAAE,SAAOA,KAAE,WAASH,KAAEG;AAAI;AAAA,UAAM,KAAK;AAAE,YAAAU,KAAE,EAAEd,KAAE,CAAC,GAAEe,KAAE,EAAEf,KAAE,CAAC,GAAEG,KAAE,EAAEH,KAAE,CAAC,GAAE,QAAM,MAAIc,OAAI,QAAM,MAAIC,OAAI,QAAM,MAAIZ,QAAKC,MAAG,KAAGN,OAAI,MAAI,KAAGgB,OAAI,MAAI,KAAGC,OAAI,IAAE,KAAGZ,IAAEC,KAAE,SAAOA,KAAE,YAAUH,KAAEG;AAAA,QAAG;AAAA,MAAC;AAAC,eAAOH;AAAA;AAAA;AAAA,SAG3jBA,KAAE,OAAMC,KAAE;AAAA,UAAGD,KAAE;AAAA,OAEfA,MAAG,OAAM,EAAE,KAAKA,OAAI,KAAG,OAAK,KAAK,GAAEA,KAAE,QAAM,OAAKA,KAAG,EAAE,KAAKA,EAAC,GAAED,MAAGE;AAAA,IAAC;AAAC,UAAMD,KAAE,EAAE;AAAO,QAAIC,KAAE;AAAG,SAAIF,KAAE,GAAEA,KAAEC,KAAG,CAAAC,MAAG,OAAO,aAAa,GAAG,EAAE,MAAMF,IAAEA,MAAG,IAAI,CAAC;AAAE,WAAOE;AAAA,EAAC;AAAC;AAAC,EAAE,eAAc,EAAE;AAKnL,MAAM,GAAE;AAAA,EAAC,OAAO,GAAE;AAAC,QAAI,IAAE;AAAE,UAAM,IAAE,EAAE,QAAO,IAAE;AAC9C,QAAIF,KAAE,GAAEC,KAAE,KAAK,IAAI,IAAG,KAAG,KAAG,KAAG,CAAC,GAAEC,KAAE,IAAI,EAAED,MAAG,KAAG,CAAC;AAEjD,WAAK,IAAE,KAAG;AAAC,UAAIc,KAAE,EAAE,WAAW,GAAG;AAAE,UAAGA,MAAG,SAAOA,MAAG,OAAM;AAEzD,YAAG,IAAE,GAAE;AAAC,gBAAMD,KAAE,EAAE,WAAW,CAAC;AAAE,oBAAQ,QAAMA,QAAK,EAAE,GAAEC,OAAI,OAAKA,OAAI,OAAK,OAAKD,MAAG;AAAA,QAAM;AAAC,YAAGC,MAAG,SAAOA,MAAG,MAAM;AAAA,MAC9G;AAEA,UAAGf,KAAE,IAAEE,GAAE,QAAO;AAAC,QAAAD,MAAG;AAAA,QACpBA,MAAG,IAAE,IAAE,EAAE,SAAO;AAAA,QAChBA,KAAEA,MAAG,KAAG;AACR,cAAMa,KAAE,IAAI,WAAWb,EAAC;AAAE,QAAAa,GAAE,IAAIZ,EAAC,GAAEA,KAAEY;AAAA,MAAC;AAAC,UAAG,aAAWC,IAAE;AAAC,YAAG,aAAWA,GAAE,KAAG,aAAWA,IAAE;AAAC,cAAG,aAAWA;AAEvG;AAEA,UAAAb,GAAEF,IAAG,IAAEe,MAAG,KAAG,IAAE,KAAIb,GAAEF,IAAG,IAAEe,MAAG,KAAG,KAAG,KAAIb,GAAEF,IAAG,IAAEe,MAAG,IAAE,KAAG;AAAA,QAAG;AAEzD,UAAAb,GAAEF,IAAG,IAAEe,MAAG,KAAG,KAAG,KAAIb,GAAEF,IAAG,IAAEe,MAAG,IAAE,KAAG;AAAA;AAEnC,UAAAb,GAAEF,IAAG,IAAEe,MAAG,IAAE,KAAG;AAAI,QAAAb,GAAEF,IAAG,IAAE,KAAGe,KAAE;AAAA,MAAG;AAElC,QAAAb,GAAEF,IAAG,IAAEe;AAAA,IAAC;AAAC,WAAOb,GAAE,MAAM,GAAEF,EAAC;AAAA,EAAC;AAAC;AAAC,EAAE,eAAc,EAAE;AAqDhD,MAAM,GAAE;AAAA,EAAC,cAAa;AAAA,EAAC;AAAA,EAAC,OAAO,gBAAgB,GAAE;AAAC,QAAG,EAAE,aAAa,GAAG,OAAM,IAAI,UAAU,kFAAkF;AAAE,WAAO,GAAG,CAAC;AAAA,EAE1L;AAAA,EAAC,OAAO,gBAAgB,GAAE;AAAC,OAAG,IAAI,CAAC,KAAG,GAAG,OAAO,CAAC;AAAA,EAAC;AAAC;AAAM,MAAC,KAAG,wCAAuC,KAAG,oBAAI,OAAI,KAAG,OAAG;AAAC,QAAM,IAAE,GAAG,EAAE,KAAK,MAAI;AAAC,UAAMF,KAAE,KAAK,IAAG,GAAGC,KAAE,KAAK,SAAS,SAAS,EAAE,EAAE,MAAM,CAAC;AACpM,WAAM,GAAGD,GAAE,SAAS,EAAE,CAAC,IAAIC,GAAE,MAAM,GAAE,CAAC,CAAC,IAAIA,GAAE,MAAM,GAAE,CAAC,CAAC,IAAIA,GAAE,MAAM,GAAE,EAAE,CAAC,IAAIA,GAAE,MAAM,IAAG,EAAE,CAAC;AAAA,EAAE,GAAC,CAAG;AAAG,SAAO,GAAG,IAAI,GAAE,CAAC,GAAE;AAAC;AAAE,SAAS,GAAG,GAAE;AAAC,SAAO,GAAG,IAAI,CAAC,KAAG;AAAI;AAAC,SAAS,GAAG,GAAE;AAAC,QAAM,IAAE,GAAG,CAAC;AAAE,SAAO,IAAE,UAAQ,EAAE,OAAK,aAAW,EAAE,EAAE,OAAO,IAAE;AAAI;AAG3O,SAAS,GAAG,GAAE;AAAC,QAAM,IAAE;AAAoE,MAAI,GAAE,GAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,IAAGC,KAAE;AAAE,MAAE,EAAE,QAAQ,uBAAsB,EAAE;AAAE,KAAE;AAAC,IAAAL,KAAE,EAAE,QAAQ,EAAE,OAAOK,IAAG,CAAC,GAAEJ,KAAE,EAAE,QAAQ,EAAE,OAAOI,IAAG,CAAC,GAAEH,KAAE,EAAE,QAAQ,EAAE,OAAOG,IAAG,CAAC,GAAEF,KAAE,EAAE,QAAQ,EAAE,OAAOE,IAAG,CAAC,GAAE,IAAEL,MAAG,IAAEC,MAAG,GAAE,KAAG,KAAGA,OAAI,IAAEC,MAAG,GAAEH,MAAG,IAAEG,OAAI,IAAEC,IAAEC,MAAG,OAAO,aAAa,CAAC,GAAE,OAAKF,OAAIE,MAAG,OAAO,aAAa,CAAC,IAAG,OAAKD,OAAIC,MAAG,OAAO,aAAaL,EAAC;AAAA,EAAE,SAAOM,KAAE,EAAE;AAAQ,SAAOD;AAAC;AAAC,SAAS,GAAG,GAAE;AAAC,MAAE,OAAO,CAAC;AAAE,QAAM,IAAE;AAC1d,MAAI,IAAE,IAAG,IAAE,IAAGL,KAAE,GAAEC,KAAE;AAEpB,WAAQc,KAAE,GAAEA,KAAE,EAAE,QAAOA;AAEvB,SAAIf,KAAEA,MAAG,IAAE,EAAE,WAAWe,EAAC;AAAA,IACzBd,MAAG,GAAEA,MAAG,KAAG;AACX,WAAG,EAAED,MAAGC,KAAE,IAAE,EAAE,GAAEA,MAAG;AAAA,IAAC;AAEpB,SAAOA,KAAE,MAAI,KAAG,EAAED,MAAG,IAAEC,KAAE,EAAE;AAAA,EAC3B,IAAE,IAAI,QAAQ,IAAEA,MAAG,CAAC,IAAG,IAAE;AACzB;AAGK,IAAI,KAAG;AAAK,MAAM,GAAE;AAAA,EAAC,cAAa;AAAC,QAAG,OAAO,eAAe,MAAK,iBAAgB,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,kBAAiB,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,eAAc,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,6BAA4B,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,UAAS,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,CAAC,GAAG,OAAM,IAAI,UAAU,qBAAqB;AAAE,SAAK,gBAAc,GAAG,eAAc,KAAK,4BAA0B,GAAG,2BAA0B,KAAK,iBAAe,GAAG,2BAA0B,KAAK,cAAY,GAAG,aAAY,KAAK,SAAO,GAAG,QAAO,KAAG;AAAA,EAAI;AAAC;AAAC,MAAM,KAAG,oBAAI;AAAQ,MAAM,GAAE;AAAA,EAAC,OAAO,kBAAkB,GAAE;AAAC,WAAO,GAAG,IAAI,CAAC,KAAG,GAAG,IAAI,CAAC,KAAG,CAAA;AAAA,EAAE;AAAA,EAAC,YAAY,GAAE;AAAC,WAAO,eAAe,MAAK,YAAW,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,oDAAmD,CAAC,GAAE,OAAO,eAAe,MAAK,YAAW,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,YAAW,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,oBAAI,MAAG,CAAC,GAAE,KAAK,WAAS;AAAA,EAAC;AAAA,EAAC,QAAQ,GAAE,IAAE,IAAG;AAAC,QAAG,CAAC,KAAK,SAAS,IAAI,CAAC,EAAE,KAAG,KAAK,SAAS,IAAI,GAAE,CAAC,GAAE,GAAG,IAAI,CAAC,GAAE;AAAC,YAAMF,KAAE,GAAG,IAAI,CAAC,KAAG;AAAG,SAAG,IAAI,GAAE,CAAC,GAAGA,IAAE,IAAI,CAAC;AAAA,IAAC,MAAM,IAAG,IAAI,GAAE,CAAC,IAAI,CAAC;AAAA,EAAC;AAAA,EAAC,UAAU,GAAE;AAAC,QAAG,KAAK,SAAS,IAAI,CAAC,MAAI,KAAK,SAAS,OAAO,CAAC,GAAE,GAAG,IAAI,CAAC,IAAG;AAAC,YAAM,IAAE,GAAG,IAAI,CAAC,KAAG,CAAA;AAAG,SAAG,IAAI,GAAE,EAAE,OAAQ,CAAAD,OAAGA,OAAI,KAAM;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,SAAS,MAAK;AAAA,EAAE;AAAA,EAAC,eAAc;AAAC,WAAO,KAAK,SAAS,KAAI;AAAA,EAAE;AAAA,EAAC,mBAAmB,GAAE;AAAC,QAAG,KAAK,SAAS,IAAI,CAAC,EAAE,QAAO,KAAK,SAAS,IAAI,CAAC;AAAA,EAAC;AAAA,EAAC,SAAS,GAAE;AAAC,UAAM,IAAE,KAAK,UAAS,IAAE,MAAM,KAAK,EAAE,KAAI,CAAE,GAAE,IAAE,EAAE,OAAQ,CAAAA,OAAG,EAAE,SAASA,GAAE,MAAM,CAAC,EAAG,IAAK,CAAAA,QAAI,KAAGA,IAAE,IAAI,KAAG;AAAG,MAAE,UAAQ,KAAK,SAAS,GAAE,IAAI;AAAA,EAAC;AAAC;AAAC,MAAM,KAAG,EAAC,iBAAgB,GAAE,aAAY,GAAE,MAAK,GAAE,cAAa,GAAE,OAAMA,mCAAAA,OAAE,aAAYC,mCAAAA,aAAE,MAAK,GAAE,YAAW,GAAE,UAAS,GAAE,SAAQ,GAAE,eAAc,GAAE,YAAW,GAAE,cAAa,GAAE,eAAc,GAAE,SAAQ,GAAE,gBAAegB,8CAAAA,gBAAE,UAAS,IAAG,aAAY,IAAG,aAAY,IAAG,OAAM,GAAE,YAAW,GAAE,SAAQ,GAAE,KAAI,IAAG,MAAK,gBAAgB,MAAK,MAAK,IAAG,OAAM,IAAG,gBAAe,IAAG,qBAAoB,GAAE;AAe3mE,SAAS,GAAG,GAAE,GAAE,GAAE,GAAE;AAAqF,MAAG,cAAY,OAAO,IAAE,MAAI,KAAG,CAAC,IAAE,CAAC,EAAE,IAAI,CAAC,EAAE,OAAM,IAAI,UAAU,0EAA0E;AAAE,SAAM,QAAM,IAAE,IAAE,QAAM,IAAE,EAAE,KAAK,CAAC,IAAE,IAAE,EAAE,QAAM,EAAE,IAAI,CAAC;AAAC;AAAC,SAAS,GAAG,GAAE,GAAE,GAAE,GAAEf,IAAE;AAAsJ,MAAG,cAAY,OAAO,IAAE,MAAI,KAAG,OAAG,CAAC,EAAE,IAAI,CAAC,EAAE,OAAM,IAAI,UAAU,yEAAyE;AAAE,SAAsC,EAAE,IAAI,GAAE,CAAC,GAAE;AAAC;AAAC,cAAY,OAAO,mBAAiB;AAAgB,MAAM,WAAWD,mCAAAA,YAAC;AAAA,EAAC,cAAa;AAAC,UAAK,GAAG,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,WAAU,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,UAAS,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,eAAc,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,cAAa,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,aAAY,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAE,OAAO,eAAe,MAAK,aAAY,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC,GAAEe,mCAAAA,uBAAE,MAAK,SAAS,OAAG;AAAC,UAAI;AAAE,aAAO,UAAQ,IAAE,KAAK,YAAU,WAAS,IAAE,SAAO,EAAE,KAAK,MAAK,CAAC;AAAA,IAAC,CAAC,GAAGA,mCAAAA,uBAAE,MAAK,SAAS,OAAG;AAAC,UAAI;AAAE,aAAO,UAAQ,IAAE,KAAK,YAAU,WAAS,IAAE,SAAO,EAAE,KAAK,MAAK,CAAC;AAAA,IAAC,CAAC,GAAGA,mCAAAA,uBAAE,MAAK,QAAQ,OAAG;AAAC,UAAI;AAAE,aAAO,UAAQ,IAAE,KAAK,WAAS,WAAS,IAAE,SAAO,EAAE,KAAK,MAAK,CAAC;AAAA,IAAC,CAAC,GAAGA,mCAAAA,uBAAE,MAAK,aAAa,OAAG;AAAC,UAAI;AAAE,aAAO,UAAQ,IAAE,KAAK,gBAAc,WAAS,IAAE,SAAO,EAAE,KAAK,MAAK,CAAC;AAAA,IAAC,CAAC,GAAGA,mCAAAA,uBAAE,MAAK,YAAY,OAAG;AAAC,UAAI;AAAE,aAAO,UAAQ,IAAE,KAAK,eAAa,WAAS,IAAE,SAAO,EAAE,KAAK,MAAK,CAAC;AAAA,IAAC,CAAC,GAAGA,mCAAAA,uBAAE,MAAK,WAAW,OAAG;AAAC,UAAI;AAAE,aAAO,UAAQ,IAAE,KAAK,cAAY,WAAS,IAAE,SAAO,EAAE,KAAK,MAAK,CAAC;AAAA,IAAC,CAAC,GAAGA,mCAAAA,uBAAE,MAAK,WAAW,OAAG;AAAC,UAAI;AAAE,aAAO,UAAQ,IAAE,KAAK,cAAY,WAAS,IAAE,SAAO,EAAE,KAAK,MAAK,CAAC;AAAA,IAAC,CAAC;AAAA,EAAE;AAAC;AAAC,IAAI,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG;AAAG,MAAM,KAAG,GAAE,KAAG,GAAE,KAAG,GAAE,KAAG,GAAE,KAAG,GAAE,KAAG,EAAC,KAAI,YAAW,KAAI,sBAAqB,KAAI,cAAa,KAAI,MAAK,KAAI,WAAU,KAAI,YAAW,KAAI,iCAAgC,KAAI,cAAa,KAAI,iBAAgB,KAAI,mBAAkB,KAAI,gBAAe,KAAI,gBAAe,KAAI,WAAU,KAAI,mBAAkB,KAAI,qBAAoB,KAAI,SAAQ,KAAI,aAAY,KAAI,gBAAe,KAAI,aAAY,KAAI,UAAS,KAAI,sBAAqB,KAAI,sBAAqB,KAAI,eAAc,KAAI,gBAAe,KAAI,oBAAmB,KAAI,aAAY,KAAI,aAAY,KAAI,sBAAqB,KAAI,kBAAiB,KAAI,iCAAgC,KAAI,mBAAkB,KAAI,YAAW,KAAI,QAAO,KAAI,mBAAkB,KAAI,uBAAsB,KAAI,qBAAoB,KAAI,gBAAe,KAAI,0BAAyB,KAAI,mCAAkC,KAAI,sBAAqB,KAAI,gBAAe,KAAI,uBAAsB,KAAI,wBAAuB,KAAI,UAAS,KAAI,qBAAoB,KAAI,oBAAmB,KAAI,yBAAwB,KAAI,qBAAoB,KAAI,mCAAkC,KAAI,iCAAgC,KAAI,yBAAwB,KAAI,mBAAkB,KAAI,eAAc,KAAI,uBAAsB,KAAI,mBAAkB,KAAI,8BAA6B,KAAI,2BAA0B,KAAI,wBAAuB,KAAI,iBAAgB,KAAI,gBAAe,KAAI,kCAAiC;AAE9hH,SAAS,GAAG,GAAE;AAAC,QAAM,IAAE,CAAA;AAAG,aAAU,KAAK,EAAE,GAAE,eAAe,CAAC,MAAI,EAAE,EAAE,YAAW,CAAE,IAAE,EAAE,CAAC;AAAG,SAAO;AAAC;AAElG,MAAM,KAAkB,iBAAC,kBAAiB,mBAAkB,kCAAiC,iCAAgC,cAAa,kBAAiB,UAAS,WAAU,QAAO,OAAM,UAAS,sBAAqB,QAAO,cAAa,UAAS,UAAS,QAAO,WAAU,MAAK,WAAU,qBAAoB,WAAU,KAAK,EAAE,IAAK,OAAG,EAAE,YAAW,CAAE,EAAG,IAAK,OAAG,EAAE,KAAI,CAAE;AAAG,KAAG,oBAAI,WAAQ,KAAG,oBAAI,WAAQ,KAAG,oBAAI,WAAQ,KAAG,oBAAI,WAAQ,KAAG,oBAAI,WAAQ,KAAG,oBAAI,WAAQ,KAAG,oBAAI,WAAQ,KAAG,oBAAI,WAAQ,KAAG,oBAAI,WAAQ,KAAG,oBAAI,WAAQ,KAAG,oBAAI,WAAQ,KAAG,oBAAI,WAAQ,KAAG,oBAAI,WAAQ,KAAG,oBAAI,WAAQ,KAAG,oBAAI;AAAa,MAAC,KAAG,cAAc,GAAE;AAAA,EAAC,cAAa;AAAC,UAAK;AAAA,IAE/nB,GAAG,IAAI,MAAK,MAAM,GAAE,GAAG,IAAI,MAAK,MAAM,GAAE,GAAG,IAAI,MAAK,CAAA,CAAE,GAAE,GAAG,IAAI,MAAK,CAAA,CAAE,GAAE,GAAG,IAAI,MAAK,MAAM,GAAE,GAAG,IAAI,MAAK,KAAE,GAAE,GAAG,IAAI,MAAK,EAAE,GAAE,GAAG,IAAI,MAAK,KAAE,GAAE,GAAG,IAAI,MAAK,EAAE,GAAE,GAAG,IAAI,MAAK,CAAC;AAAA,IAEzK,GAAG,IAAI,MAAK,IAAI;AAAA,IAEhB,GAAG,IAAI,MAAK,KAAE,GAAE,GAAG,IAAI,MAAK,IAAI,GAAE,GAAG,IAAI,MAAK,CAAC,GAAE,GAAG,IAAI,MAAK,KAAE,GAAE,OAAO,eAAe,MAAK,sBAAqB,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,OAAM,CAAC;AAAA,IAEzKA,mCAAAA,uBAAE,MAAK,oBAAoB,OAAG;AAAC,UAAI;AAAE,aAAO,UAAQ,IAAE,KAAK,uBAAqB,WAAS,IAAE,SAAO,EAAE,KAAK,MAAK,CAAC;AAAA,IAAC,CAAC;AAAA,EAAE;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,GAAG,MAAK,IAAG,GAAG;AAAA,EAAC;AAAA,EAAC,IAAI,kBAAiB;AAAC,WAAO,GAAG,MAAK,IAAG,GAAG;AAAA,EAAC;AAAA,EAAC,IAAI,gBAAgB,GAAE;AAAC,YAAQ,KAAK,iDAAiD,GAAE,GAAG,MAAK,IAAG,CAAK;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,GAAG,MAAK,IAAG,GAAG;AAAA,EAAC;AAAA,EAAC,IAAI,eAAc;AAAC,QAAG,OAAK,GAAG,MAAK,IAAG,GAAG,KAAG,WAAS,GAAG,MAAK,IAAG,GAAG,EAAE,OAAM,EAAE,uJAAuJ,GAAG,MAAK,IAAG,GAAG,CAAC,KAAK;AAAE,WAAM,YAAU,OAAO,GAAG,MAAK,IAAG,GAAG,IAAE,KAAK,UAAU,GAAG,MAAK,IAAG,GAAG,CAAC,IAAE,GAAG,MAAK,IAAG,GAAG;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,MAAK,IAAG,GAAG;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,QAAG,OAAK,GAAG,MAAK,IAAG,GAAG,KAAG,eAAa,GAAG,MAAK,IAAG,GAAG,EAAE,OAAM,EAAE,0JAA0J,GAAG,MAAK,IAAG,GAAG,CAAC,KAAK;AAAE,UAAM,MAAM,+BAA+B;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,GAAG,MAAK,IAAG,GAAG;AAAA,EAAC;AAAA,EAAC,IAAI,QAAQ,GAAE;AAAC,OAAG,MAAK,IAAG,CAAK;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,GAAG,MAAK,IAAG,GAAG;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,GAAG,KAAK,MAAM,KAAG;AAAA,EAAS;AAAA,EAAC,IAAI,aAAa,GAAE;AAE/qC,QAAG,KAAK,cAAY,MAAI,KAAK,cAAY,MAAI,CAAC,GAAG,MAAK,IAAG,GAAG,EAAE,OAAM,EAAE,sFAAsF;AAAE,OAAG,MAAK,IAAG,CAAK;AAAA,EAAC;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,GAAG,MAAK,IAAG,GAAG;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,GAAG,MAAK,IAAG,GAAG;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtP,iBAAiB,GAAE;AAAC,SAAK;AAAA,EAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASnC,KAAK,GAAE,GAAE,IAAE,MAAGd,IAAEC,IAAE;AAEzB,SAAK,cAAY,MAAI,KAAK,MAAK,GAAG,GAAG,MAAK,IAAG,CAAK,GAAE,GAAG,MAAK,IAAG,CAAK,GAAE,GAAG,MAAK,IAAG,EAAM,GAAE,GAAG,MAAK,IAAG,IAAM,GAAE,KAAK,cAAc,IAAIH,mCAAAA,MAAE,kBAAkB,CAAC;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOlJ,KAAK,GAAE;AAAC,QAAI,GAAE;AAAE,QAAG,GAAG,MAAK,IAAG,GAAG,MAAI,GAAG,OAAM,EAAE,kFAAkF;AAEzI,QAAG,SAAK,GAAG,MAAK,IAAG,GAAG,EAAE;AAExB,QAAG,GAAG,MAAK,IAAG,GAAG,EAAE;AAEnB,OAAG,MAAK,IAAG,KAAM;AAAE,UAAMG,KAAE,GAAG,MAAK,IAAG,GAAG,GAAEC,KAAE,UAAQ,IAAE,GAAG,MAAK,IAAG,GAAG,MAAI,WAAS,IAAE,SAAO,EAAE,eAAcC,KAAE,EAAC,GAAG,GAAG,MAAK,IAAG,GAAG,GAAE,QAAO,UAAQ,IAAE,GAAG,MAAK,IAAG,GAAG,EAAE,WAAS,WAAS,IAAE,IAAE,MAAK,GAAEC,KAAE,MAAI,GAAG,MAAK,IAAG,GAAG,IAAE,IAAE,IAAE,GAAG,MAAK,IAAG,GAAG;AAAE,QAAIC,IAAEC,KAAE;AAAK,YAAO,GAAG,MAAK,IAAG,GAAG,GAAC;AAAA,MAAE,KAAI;AAAA,MAAG,KAAI;AAAA,MAAO,KAAI;AAAO,QAAAD,KAAE,QAAO,WAAS,GAAG,MAAK,IAAG,GAAG,MAAIC,KAAE;AAAQ;AAAA,MAAM,KAAI;AAAA,MAAc,KAAI;AAAO,QAAAD,KAAE;AAAc;AAAA,MAAM,KAAI;AAAW,cAAM,EAAE,qDAAqD;AAAA,IAAC;AAW/d,QAAG,GAAG,MAAK,IAAG,KAAM,GAAE,KAAK,cAAc,IAAI,gBAAgB,cAAc,WAAW,CAAC,GAAEJ,GAAE,WAAW,EAAE,GAAE;AAAC,YAAMF,KAAE,GAAGE,EAAC;AAAE,aAAO,MAAKF,KAAE,WAAY,MAAI;AAAC,WAAG,MAAK,IAAG,EAAM,GAAE,KAAK,cAAc,IAAID,mCAAAA,MAAE,kBAAkB,CAAC,GAAE,GAAG,MAAK,IAAG,GAAO,GAAE,GAAG,MAAK,IAAG,EAAC,gBAAeC,GAAE,KAAI,CAAK,GAAE,KAAK,cAAc,IAAI,gBAAgB,cAAc,UAAU,CAAC,GAAE,GAAG,MAAK,IAAG,EAAM,GAAE,KAAK,cAAc,IAAID,mCAAAA,MAAE,kBAAkB,CAAC,GAAE,GAAG,MAAK,IAAGC,GAAE,MAAK,CAAM,GAAE,GAAG,MAAK,IAAG,EAAM,GAAE,KAAK,cAAc,IAAID,mCAAAA,MAAE,kBAAkB,CAAC,GAAE,KAAK,cAAc,IAAI,gBAAgB,cAAc,MAAM,CAAC,GAAE,KAAK,cAAc,IAAI,gBAAgB,cAAc,SAAS,CAAC,GAAE,GAAG,MAAK,IAAG,IAAM;AAAA,MAAC,GAAG,CAAC,KAAG,GAAG,MAAK,IAAG,EAAM,GAAE,KAAK,cAAc,IAAIA,mCAAAA,MAAE,kBAAkB,CAAC,GAAE,KAAK,cAAc,IAAI,gBAAgB,cAAc,OAAO,CAAC,GAAE,KAAK,cAAc,IAAI,gBAAgB,cAAc,SAAS,CAAC,GAAE,GAAG,MAAK,IAAG,IAAM;AAAA,IAAG;AAAC,QAAIS,KAAE;AAAG,UAAMC,KAAE,IAAI,QAAS,CAACM,IAAEC,OAAI;AAAC,YAAMf,KAAEgB,oDAAAA,KAAG,QAAQ,EAAC,KAAIf,IAAE,QAAOC,IAAE,QAAOC,IAAE,MAAK,GAAE,UAASG,IAAE,cAAaD,IAAE,SAAQD,IAAE,SAAQU,IAAE,MAAKC,GAAC,CAAC,GAAEP,KAAE,CAAAT,OAAG;AAAC,YAAIe;AAAE,WAAG,MAAK,IAAG,GAAG,KAAG,GAAG,MAAK,IAAG,GAAG,MAAI,GAAG,MAAK,IAAG,EAAM,GAAE,KAAK,cAAc,IAAIhB,mCAAAA,MAAE,kBAAkB,CAAC,GAAE,GAAG,MAAK,IAAGC,GAAE,UAAc,GAAE,GAAG,MAAK,IAAG,GAAGA,GAAE,MAAM,CAAK,GAAE,UAAQe,KAAE,GAAG,MAAK,IAAG,GAAG,MAAI,WAASA,MAAGA,GAAE,mBAAmBN,EAAC,GAAED,KAAE;AAAA,MAAG;AAEhuC,MAAAP,GAAE,kBAAkBQ,EAAC,GAAE,GAAG,MAAK,IAAGR,EAAK;AAAA,IAAC,CAAC;AAAG,IAAAQ,GAAE,KAAM,CAAAT,OAAG;AAAC,SAAG,MAAK,IAAG,GAAG,MAAIQ,OAAI,GAAG,MAAK,IAAGR,GAAE,UAAc,GAAE,GAAG,MAAK,IAAG,GAAGA,GAAE,MAAM,CAAK,IAAG,KAAK,cAAc,IAAI,gBAAgB,cAAc,UAAU,CAAC,GAAE,GAAG,MAAK,IAAG,EAAM,GAAE,KAAK,cAAc,IAAID,mCAAAA,MAAE,kBAAkB,CAAC,GAAE,WAAS,GAAG,MAAK,IAAG,GAAG,IAAE,GAAG,MAAK,IAAG,IAAI,EAAE,CAACC,GAAE,IAAI,GAAE,EAAC,MAAK,GAAG,MAAK,IAAG,GAAG,EAAE,cAAc,EAAC,CAAC,CAAK,IAAE,GAAG,MAAK,IAAG,WAASA,GAAE,OAAK,OAAKA,GAAE,IAAQ,GAAEA,GAAE,cAAY,OAAK,QAAQ,MAAM,GAAG,GAAG,MAAK,IAAG,GAAG,CAAC,IAAI,GAAG,MAAK,IAAG,GAAG,CAAC,IAAIA,GAAE,UAAU,EAAE,GAAE,GAAG,MAAK,IAAG,EAAM,GAAE,KAAK,cAAc,IAAID,mCAAAA,MAAE,kBAAkB,CAAC,GAAE,KAAK,cAAc,IAAI,gBAAgB,cAAc,MAAM,CAAC;AAAA,IAAE,CAAC,EAAG,MAAO,CAAAC,OAAG;AAAC,cAAO,GAAG,MAAK,IAAG,EAAM,GAAE,KAAK,cAAc,IAAID,mCAAAA,MAAE,kBAAkB,CAAC,GAAEC,GAAE,OAAK;AAAA,QAE/sB,KAAK;AAAE,eAAK,cAAc,IAAI,gBAAgB,cAAc,OAAO,CAAC;AAAE;AAAA,QAEtE,KAAK;AAAE,eAAK,cAAc,IAAI,gBAAgB,cAAc,SAAS,CAAC;AAAE;AAAA,QAExE;AAAQ,cAAG,GAAG,MAAK,IAAG,GAAG,EAAE;AAAO,aAAG,MAAK,IAAGA,EAAK,GAAE,KAAK,cAAc,IAAI,gBAAgB,cAAc,OAAO,CAAC;AAAA,MAAC;AAAA,IAAC,CAAC,EAAG,QAAS,MAAI;AAAC,SAAG,MAAK,IAAG,IAAM,GAAE,GAAG,MAAK,IAAG,IAAQ,GAAE,KAAK,cAAc,IAAI,gBAAgB,cAAc,SAAS,CAAC;AAAA,IAAC,CAAC;AAAA,EAAE;AAAA;AAAA;AAAA;AAAA,EAGzO,QAAO;AAGd,OAAG,MAAK,IAAG,GAAG,KAAG,GAAG,MAAK,IAAG,GAAG,MAAI,GAAG,MAAK,IAAG,GAAG,KAAG,GAAG,MAAK,IAAG,GAAG,EAAE,MAAK,GAAG,GAAG,MAAK,IAAG,IAAM,GAAE,KAAK,cAAc,IAAID,mCAAAA,MAAE,OAAO,CAAC;AAAA,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3H,iBAAiB,GAAE,GAAE;AAE5B,QAAG,KAAK,cAAY,MAAI,KAAK,cAAY,MAAI,CAAC,GAAG,MAAK,IAAG,GAAG,EAAE,OAAM,EAAE,8FAA8F;AAAE,QAAG,GAAG,UAAW,CAAAC,OAAGA,GAAE,KAAI,MAAK,CAAC,KAAI;AAE1M;AAAO,UAAM,IAAE,EAAE,YAAW;AAAG,OAAG,MAAK,IAAG,GAAG,EAAE,CAAC,KAAG,GAAG,MAAK,IAAG,GAAG,EAAE,CAAC,IAAE,GAAG,MAAK,IAAG,GAAG,EAAE,CAAC,IAAE,MAAI,MAAI;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAI3F,kBAAkB,GAAE;AAAC,QAAI;AAAE,WAAO,UAAQ,IAAE,GAAG,MAAK,IAAG,GAAG,EAAE,EAAE,YAAW,CAAE,MAAI,WAAS,IAAE,IAAE;AAAA,EAAI;AAAA;AAAA;AAAA;AAAA,EAGhG,wBAAuB;AAAC,WAAO,OAAO,QAAQ,GAAG,GAAG,MAAK,IAAG,GAAG,CAAC,CAAC,EAAE,OAAQ,CAAC,GAAE,CAAC,GAAE,CAAC,OAAK,EAAE,KAAK,GAAG,EAAE,aAAa,KAAK,CAAC,EAAE,GAAE,IAAI,CAAA,CAAE,EAAE,KAAK,MAAM;AAAA,EAAC;AAAC,GAAE,KAAkB,uBAAO,OAAO,iBAAgB,EAAC,GAAG,IAAG,gBAAe,GAAE,CAAC;;;;;;;;;", "x_google_ignoreList": [0]}