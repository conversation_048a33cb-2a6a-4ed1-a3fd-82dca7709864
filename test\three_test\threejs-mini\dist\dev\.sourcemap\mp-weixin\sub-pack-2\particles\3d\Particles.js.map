{"version": 3, "file": "Particles.js", "sources": ["sub-pack-2/particles/glsl/particles.vert", "sub-pack-2/particles/glsl/particles.frag", "sub-pack-2/particles/glsl/particlesDistance.vert", "sub-pack-2/particles/glsl/particlesDistance.frag"], "sourcesContent": ["//#include <common>\r\n//#include <shadowmap_pars_vertex>\r\n//#include <logdepthbuf_pars_vertex>\r\n\r\nuniform sampler2D texturePosition;\r\nvarying float vLife;\r\n\r\nvoid main() {\r\n\r\n//    #include <beginnormal_vertex>\r\n//\t#include <defaultnormal_vertex>\r\n//\r\n//\t#include <logdepthbuf_vertex>\r\n\r\n    vec4 positionInfo = texture2D(texturePosition, position.xy);\r\n    vec4 worldPosition = modelMatrix * vec4(positionInfo.xyz, 1.0);\r\n    vec4 mvPosition = viewMatrix * worldPosition;\r\n\r\n//\t#include <shadowmap_vertex>\r\n\r\n\r\n    vLife = positionInfo.w;\r\n\r\n    gl_PointSize = 1300.0 / length(mvPosition.xyz) * smoothstep(0.0, 0.2, positionInfo.w);\r\n    gl_Position = projectionMatrix * mvPosition;\r\n\r\n}\r\n\r\n\r\n//#define STANDARD\r\n//\r\n//uniform sampler2D texturePosition;\r\n//varying float vLife;\r\n//\r\n//varying vec3 vViewPosition;\r\n//\r\n//#ifdef USE_TRANSMISSION\r\n//\r\n//varying vec3 vWorldPosition;\r\n//\r\n//#endif\r\n//\r\n//#include <common>\r\n//#include <batching_pars_vertex>\r\n//#include <uv_pars_vertex>\r\n//#include <displacementmap_pars_vertex>\r\n//#include <color_pars_vertex>\r\n//#include <fog_pars_vertex>\r\n//#include <normal_pars_vertex>\r\n//#include <morphtarget_pars_vertex>\r\n//#include <skinning_pars_vertex>\r\n//#include <shadowmap_pars_vertex>\r\n//#include <logdepthbuf_pars_vertex>\r\n//#include <clipping_planes_pars_vertex>\r\n//\r\n//void main() {\r\n//\r\n//    #include <uv_vertex>\r\n//\t#include <color_vertex>\r\n//\t#include <morphinstance_vertex>\r\n//\t#include <morphcolor_vertex>\r\n//\t#include <batching_vertex>\r\n//\r\n//    #include <beginnormal_vertex>\r\n//\t#include <morphnormal_vertex>\r\n//\t#include <skinbase_vertex>\r\n//\t#include <skinnormal_vertex>\r\n//\t#include <defaultnormal_vertex>\r\n//\t#include <normal_vertex>\r\n//\r\n//    #include <begin_vertex>\r\n//\t#include <morphtarget_vertex>\r\n//\t#include <skinning_vertex>\r\n//\t#include <displacementmap_vertex>\r\n////\t#include <project_vertex>\r\n//\t#include <logdepthbuf_vertex>\r\n//\t#include <clipping_planes_vertex>\r\n//\r\n//        vec4 positionInfo = texture2D(texturePosition, position.xy);\r\n//        vec4 worldPosition = modelMatrix * vec4(positionInfo.xyz, 1.0);\r\n//        vec4 mvPosition = viewMatrix * worldPosition;\r\n//        vLife = positionInfo.w;\r\n//\r\n//        gl_PointSize = 1300.0 / length(mvPosition.xyz) * smoothstep(0.0, 0.2, positionInfo.w);\r\n//        gl_Position = projectionMatrix * mvPosition;\r\n//\r\n//    vViewPosition = - mvPosition.xyz;\r\n//\r\n////    #include <worldpos_vertex>\r\n//\t#include <shadowmap_vertex>\r\n//\t#include <fog_vertex>\r\n//\r\n//    #ifdef USE_TRANSMISSION\r\n//\r\n//    vWorldPosition = worldPosition.xyz;\r\n//\r\n//    #endif\r\n//}\r\n", "//#include <common>\r\n//#include <packing>\r\n//#include <lights_pars_begin>\r\n//#include <logdepthbuf_pars_fragment>\r\n//#include <shadowmap_pars_fragment>\r\n//#include <shadowmask_pars_fragment>\r\n\r\nvarying float vLife;\r\nuniform vec3 color1;\r\nuniform vec3 color2;\r\n\r\nvoid main() {\r\n//    #include <logdepthbuf_fragment>\r\n\r\n    vec3 outgoingLight = mix(color2, color1, smoothstep(0.0, 0.7, vLife));\r\n\r\n//    outgoingLight *= vec3( 1.0 - getShadowMask() );\r\ngl_FragColor = vec4( outgoingLight, 1.0 );\r\n\r\n//    #include <tonemapping_fragment>\r\n//\t#include <colorspace_fragment>\r\n//\t#include <fog_fragment>\r\n}\r\n\r\n\r\n//varying float vLife;\r\n//uniform vec3 color1;\r\n//uniform vec3 color2;\r\n//\r\n//#define STANDARD\r\n//\r\n//#ifdef PHYSICAL\r\n//\t#define IOR\r\n//\t#define USE_SPECULAR\r\n//#endif\r\n//\r\n////uniform vec3 diffuse;\r\n//uniform vec3 emissive;\r\n//uniform float roughness;\r\n//uniform float metalness;\r\n//uniform float opacity;\r\n//\r\n//#ifdef IOR\r\n//\tuniform float ior;\r\n//#endif\r\n//\r\n//#ifdef USE_SPECULAR\r\n//\tuniform float specularIntensity;\r\n//uniform vec3 specularColor;\r\n//\r\n//#ifdef USE_SPECULAR_COLORMAP\r\n//\t\tuniform sampler2D specularColorMap;\r\n//#endif\r\n//\r\n//#ifdef USE_SPECULAR_INTENSITYMAP\r\n//\t\tuniform sampler2D specularIntensityMap;\r\n//#endif\r\n//#endif\r\n//\r\n//#ifdef USE_CLEARCOAT\r\n//\tuniform float clearcoat;\r\n//uniform float clearcoatRoughness;\r\n//#endif\r\n//\r\n//#ifdef USE_DISPERSION\r\n//\tuniform float dispersion;\r\n//#endif\r\n//\r\n//#ifdef USE_IRIDESCENCE\r\n//\tuniform float iridescence;\r\n//uniform float iridescenceIOR;\r\n//uniform float iridescenceThicknessMinimum;\r\n//uniform float iridescenceThicknessMaximum;\r\n//#endif\r\n//\r\n//#ifdef USE_SHEEN\r\n//\tuniform vec3 sheenColor;\r\n//uniform float sheenRoughness;\r\n//\r\n//#ifdef USE_SHEEN_COLORMAP\r\n//\t\tuniform sampler2D sheenColorMap;\r\n//#endif\r\n//\r\n//#ifdef USE_SHEEN_ROUGHNESSMAP\r\n//\t\tuniform sampler2D sheenRoughnessMap;\r\n//#endif\r\n//#endif\r\n//\r\n//#ifdef USE_ANISOTROPY\r\n//\tuniform vec2 anisotropyVector;\r\n//\r\n//#ifdef USE_ANISOTROPYMAP\r\n//\t\tuniform sampler2D anisotropyMap;\r\n//#endif\r\n//#endif\r\n//\r\n//varying vec3 vViewPosition;\r\n//\r\n//#include <common>\r\n//#include <packing>\r\n//#include <dithering_pars_fragment>\r\n//#include <color_pars_fragment>\r\n//#include <uv_pars_fragment>\r\n//#include <map_pars_fragment>\r\n//#include <alphamap_pars_fragment>\r\n//#include <alphatest_pars_fragment>\r\n//#include <alphahash_pars_fragment>\r\n//#include <aomap_pars_fragment>\r\n//#include <lightmap_pars_fragment>\r\n//#include <emissivemap_pars_fragment>\r\n//#include <iridescence_fragment>\r\n//#include <cube_uv_reflection_fragment>\r\n//#include <envmap_common_pars_fragment>\r\n//#include <envmap_physical_pars_fragment>\r\n//#include <fog_pars_fragment>\r\n//#include <lights_pars_begin>\r\n//#include <normal_pars_fragment>\r\n//#include <lights_physical_pars_fragment>\r\n//#include <transmission_pars_fragment>\r\n//#include <shadowmap_pars_fragment>\r\n//#include <bumpmap_pars_fragment>\r\n//#include <normalmap_pars_fragment>\r\n//#include <clearcoat_pars_fragment>\r\n//#include <iridescence_pars_fragment>\r\n//#include <roughnessmap_pars_fragment>\r\n//#include <metalnessmap_pars_fragment>\r\n//#include <logdepthbuf_pars_fragment>\r\n//#include <clipping_planes_pars_fragment>\r\n//\r\n//void main() {\r\n//\r\n//    vec3 diffuse = mix(color2, color1, smoothstep(0.0, 0.7, vLife));\r\n//    vec4 diffuseColor = vec4( diffuse, opacity );\r\n//    #include <clipping_planes_fragment>\r\n//\r\n//    ReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );\r\n//    vec3 totalEmissiveRadiance = emissive;\r\n//\r\n//    #include <logdepthbuf_fragment>\r\n//\t#include <map_fragment>\r\n//\t#include <color_fragment>\r\n//\t#include <alphamap_fragment>\r\n//\t#include <alphatest_fragment>\r\n//\t#include <alphahash_fragment>\r\n//\t#include <roughnessmap_fragment>\r\n//\t#include <metalnessmap_fragment>\r\n//\t#include <normal_fragment_begin>\r\n//\t#include <normal_fragment_maps>\r\n//\t#include <clearcoat_normal_fragment_begin>\r\n//\t#include <clearcoat_normal_fragment_maps>\r\n//\t#include <emissivemap_fragment>\r\n//\r\n//    // accumulation\r\n//    #include <lights_physical_fragment>\r\n//\t#include <lights_fragment_begin>\r\n//\t#include <lights_fragment_maps>\r\n//\t#include <lights_fragment_end>\r\n//\r\n//    // modulation\r\n//    #include <aomap_fragment>\r\n//\r\n//    vec3 totalDiffuse = reflectedLight.directDiffuse + reflectedLight.indirectDiffuse;\r\n//    vec3 totalSpecular = reflectedLight.directSpecular + reflectedLight.indirectSpecular;\r\n//\r\n//    #include <transmission_fragment>\r\n//\r\n//    vec3 outgoingLight = totalDiffuse + totalSpecular + totalEmissiveRadiance;\r\n//\r\n//\r\n//    gl_FragColor = vec4( diffuse, 1.0 );\r\n////    #include <opaque_fragment>\r\n//\t#include <tonemapping_fragment>\r\n//\t#include <colorspace_fragment>\r\n//\t#include <fog_fragment>\r\n//\t#include <premultiplied_alpha_fragment>\r\n//\t#include <dithering_fragment>\r\n//\r\n//}\r\n\r\n", "uniform sampler2D texturePosition;\r\n\r\nvarying vec4 vWorldPosition;\r\n\r\nvoid main() {\r\n\r\n    vec4 positionInfo = texture2D( texturePosition, position.xy );\r\n\r\n    vec4 worldPosition = modelMatrix * vec4( positionInfo.xyz, 1.0 );\r\n    vec4 mvPosition = viewMatrix * worldPosition;\r\n\r\n    gl_PointSize = 50.0 / length( mvPosition.xyz );\r\n\r\n    vWorldPosition = worldPosition;\r\n\r\n    gl_Position = projectionMatrix * mvPosition;\r\n\r\n}\r\n", "uniform vec3 lightPos;\r\nvarying vec4 vWorldPosition;\r\n\r\n#include <common>\r\n\r\nvec4 pack1K ( float depth ) {\r\n\r\n   depth /= 1000.0;\r\n   const vec4 bitSh = vec4( 256.0 * 256.0 * 256.0, 256.0 * 256.0, 256.0, 1.0 );\r\n   const vec4 bitMsk = vec4( 0.0, 1.0 / 256.0, 1.0 / 256.0, 1.0 / 256.0 );\r\n   vec4 res = fract( depth * bitSh );\r\n   res -= res.xxyz * bitMsk;\r\n   return res;\r\n\r\n}\r\n\r\nfloat unpack1K ( vec4 color ) {\r\n\r\n   const vec4 bitSh = vec4( 1.0 / ( 256.0 * 256.0 * 256.0 ), 1.0 / ( 256.0 * 256.0 ), 1.0 / 256.0, 1.0 );\r\n   return dot( color, bitSh ) * 1000.0;\r\n\r\n}\r\n\r\nvoid main () {\r\n\r\n   gl_FragColor = pack1K( length( vWorldPosition.xyz - lightPos.xyz ) );\r\n\r\n}\r\n"], "names": ["particles_default", "particlesDistance_default"], "mappings": ";;;;AAAA,IAAAA,sBAAA;ACAA,IAAA,oBAAA;ACAA,IAAAC,8BAAA;ACAA,IAAA,4BAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}