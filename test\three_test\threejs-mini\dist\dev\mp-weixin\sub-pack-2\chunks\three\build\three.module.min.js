"use strict";
const _mpChunkDeps_three_build_three_core_min = require("./three.core.min.js");
const _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter = require("../../../../common/chunks/@minisheep/three-platform-adapter/dist/Adapter.js");
/**
 * @license
 * Copyright 2010-2024 Three.js Authors
 * SPDX-License-Identifier: MIT
 */
function Pn() {
  let e = null, t = false, n = null, i = null;
  function r(t2, a) {
    n(t2, a), i = e.requestAnimationFrame(r);
  }
  return { start: function() {
    true !== t && null !== n && (i = e.requestAnimationFrame(r), t = true);
  }, stop: function() {
    e.cancelAnimationFrame(i), t = false;
  }, setAnimationLoop: function(e2) {
    n = e2;
  }, setContext: function(t2) {
    e = t2;
  } };
}
function Un(e) {
  const t = /* @__PURE__ */ new WeakMap();
  return { get: function(e2) {
    return e2.isInterleavedBufferAttribute && (e2 = e2.data), t.get(e2);
  }, remove: function(n) {
    n.isInterleavedBufferAttribute && (n = n.data);
    const i = t.get(n);
    i && (e.deleteBuffer(i.buffer), t.delete(n));
  }, update: function(n, i) {
    if (n.isInterleavedBufferAttribute && (n = n.data), n.isGLBufferAttribute) {
      const e2 = t.get(n);
      return void ((!e2 || e2.version < n.version) && t.set(n, { buffer: n.buffer, type: n.type, bytesPerElement: n.elementSize, version: n.version }));
    }
    const r = t.get(n);
    if (void 0 === r) t.set(n, function(t2, n2) {
      const i2 = t2.array, r2 = t2.usage, a = i2.byteLength, o = e.createBuffer();
      let s;
      if (e.bindBuffer(n2, o), e.bufferData(n2, i2, r2), t2.onUploadCallback(), i2 instanceof Float32Array) s = e.FLOAT;
      else if (i2 instanceof Uint16Array) s = t2.isFloat16BufferAttribute ? e.HALF_FLOAT : e.UNSIGNED_SHORT;
      else if (i2 instanceof Int16Array) s = e.SHORT;
      else if (i2 instanceof Uint32Array) s = e.UNSIGNED_INT;
      else if (i2 instanceof Int32Array) s = e.INT;
      else if (i2 instanceof Int8Array) s = e.BYTE;
      else if (i2 instanceof Uint8Array) s = e.UNSIGNED_BYTE;
      else {
        if (!(i2 instanceof Uint8ClampedArray)) throw new Error("THREE.WebGLAttributes: Unsupported buffer data format: " + i2);
        s = e.UNSIGNED_BYTE;
      }
      return { buffer: o, type: s, bytesPerElement: i2.BYTES_PER_ELEMENT, version: t2.version, size: a };
    }(n, i));
    else if (r.version < n.version) {
      if (r.size !== n.array.byteLength) throw new Error("THREE.WebGLAttributes: The size of the buffer attribute's array buffer does not match the original size. Resizing buffer attributes is not supported.");
      !function(t2, n2, i2) {
        const r2 = n2.array, a = n2.updateRanges;
        if (e.bindBuffer(i2, t2), 0 === a.length) e.bufferSubData(i2, 0, r2);
        else {
          a.sort((e2, t4) => e2.start - t4.start);
          let t3 = 0;
          for (let e2 = 1; e2 < a.length; e2++) {
            const n3 = a[t3], i3 = a[e2];
            i3.start <= n3.start + n3.count + 1 ? n3.count = Math.max(n3.count, i3.start + i3.count - n3.start) : (++t3, a[t3] = i3);
          }
          a.length = t3 + 1;
          for (let t4 = 0, n3 = a.length; t4 < n3; t4++) {
            const n4 = a[t4];
            e.bufferSubData(i2, n4.start * r2.BYTES_PER_ELEMENT, r2, n4.start, n4.count);
          }
          n2.clearUpdateRanges();
        }
        n2.onUploadCallback();
      }(r.buffer, n, i), r.version = n.version;
    }
  } };
}
const wn = { alphahash_fragment: "#ifdef USE_ALPHAHASH\n	if ( diffuseColor.a < getAlphaHashThreshold( vPosition ) ) discard;\n#endif", alphahash_pars_fragment: "#ifdef USE_ALPHAHASH\n	const float ALPHA_HASH_SCALE = 0.05;\n	float hash2D( vec2 value ) {\n		return fract( 1.0e4 * sin( 17.0 * value.x + 0.1 * value.y ) * ( 0.1 + abs( sin( 13.0 * value.y + value.x ) ) ) );\n	}\n	float hash3D( vec3 value ) {\n		return hash2D( vec2( hash2D( value.xy ), value.z ) );\n	}\n	float getAlphaHashThreshold( vec3 position ) {\n		float maxDeriv = max(\n			length( dFdx( position.xyz ) ),\n			length( dFdy( position.xyz ) )\n		);\n		float pixScale = 1.0 / ( ALPHA_HASH_SCALE * maxDeriv );\n		vec2 pixScales = vec2(\n			exp2( floor( log2( pixScale ) ) ),\n			exp2( ceil( log2( pixScale ) ) )\n		);\n		vec2 alpha = vec2(\n			hash3D( floor( pixScales.x * position.xyz ) ),\n			hash3D( floor( pixScales.y * position.xyz ) )\n		);\n		float lerpFactor = fract( log2( pixScale ) );\n		float x = ( 1.0 - lerpFactor ) * alpha.x + lerpFactor * alpha.y;\n		float a = min( lerpFactor, 1.0 - lerpFactor );\n		vec3 cases = vec3(\n			x * x / ( 2.0 * a * ( 1.0 - a ) ),\n			( x - 0.5 * a ) / ( 1.0 - a ),\n			1.0 - ( ( 1.0 - x ) * ( 1.0 - x ) / ( 2.0 * a * ( 1.0 - a ) ) )\n		);\n		float threshold = ( x < ( 1.0 - a ) )\n			? ( ( x < a ) ? cases.x : cases.y )\n			: cases.z;\n		return clamp( threshold , 1.0e-6, 1.0 );\n	}\n#endif", alphamap_fragment: "#ifdef USE_ALPHAMAP\n	diffuseColor.a *= texture2D( alphaMap, vAlphaMapUv ).g;\n#endif", alphamap_pars_fragment: "#ifdef USE_ALPHAMAP\n	uniform sampler2D alphaMap;\n#endif", alphatest_fragment: "#ifdef USE_ALPHATEST\n	#ifdef ALPHA_TO_COVERAGE\n	diffuseColor.a = smoothstep( alphaTest, alphaTest + fwidth( diffuseColor.a ), diffuseColor.a );\n	if ( diffuseColor.a == 0.0 ) discard;\n	#else\n	if ( diffuseColor.a < alphaTest ) discard;\n	#endif\n#endif", alphatest_pars_fragment: "#ifdef USE_ALPHATEST\n	uniform float alphaTest;\n#endif", aomap_fragment: "#ifdef USE_AOMAP\n	float ambientOcclusion = ( texture2D( aoMap, vAoMapUv ).r - 1.0 ) * aoMapIntensity + 1.0;\n	reflectedLight.indirectDiffuse *= ambientOcclusion;\n	#if defined( USE_CLEARCOAT ) \n		clearcoatSpecularIndirect *= ambientOcclusion;\n	#endif\n	#if defined( USE_SHEEN ) \n		sheenSpecularIndirect *= ambientOcclusion;\n	#endif\n	#if defined( USE_ENVMAP ) && defined( STANDARD )\n		float dotNV = saturate( dot( geometryNormal, geometryViewDir ) );\n		reflectedLight.indirectSpecular *= computeSpecularOcclusion( dotNV, ambientOcclusion, material.roughness );\n	#endif\n#endif", aomap_pars_fragment: "#ifdef USE_AOMAP\n	uniform sampler2D aoMap;\n	uniform float aoMapIntensity;\n#endif", batching_pars_vertex: "#ifdef USE_BATCHING\n	#if ! defined( GL_ANGLE_multi_draw )\n	#define gl_DrawID _gl_DrawID\n	uniform int _gl_DrawID;\n	#endif\n	uniform highp sampler2D batchingTexture;\n	uniform highp usampler2D batchingIdTexture;\n	mat4 getBatchingMatrix( const in float i ) {\n		int size = textureSize( batchingTexture, 0 ).x;\n		int j = int( i ) * 4;\n		int x = j % size;\n		int y = j / size;\n		vec4 v1 = texelFetch( batchingTexture, ivec2( x, y ), 0 );\n		vec4 v2 = texelFetch( batchingTexture, ivec2( x + 1, y ), 0 );\n		vec4 v3 = texelFetch( batchingTexture, ivec2( x + 2, y ), 0 );\n		vec4 v4 = texelFetch( batchingTexture, ivec2( x + 3, y ), 0 );\n		return mat4( v1, v2, v3, v4 );\n	}\n	float getIndirectIndex( const in int i ) {\n		int size = textureSize( batchingIdTexture, 0 ).x;\n		int x = i % size;\n		int y = i / size;\n		return float( texelFetch( batchingIdTexture, ivec2( x, y ), 0 ).r );\n	}\n#endif\n#ifdef USE_BATCHING_COLOR\n	uniform sampler2D batchingColorTexture;\n	vec3 getBatchingColor( const in float i ) {\n		int size = textureSize( batchingColorTexture, 0 ).x;\n		int j = int( i );\n		int x = j % size;\n		int y = j / size;\n		return texelFetch( batchingColorTexture, ivec2( x, y ), 0 ).rgb;\n	}\n#endif", batching_vertex: "#ifdef USE_BATCHING\n	mat4 batchingMatrix = getBatchingMatrix( getIndirectIndex( gl_DrawID ) );\n#endif", begin_vertex: "vec3 transformed = vec3( position );\n#ifdef USE_ALPHAHASH\n	vPosition = vec3( position );\n#endif", beginnormal_vertex: "vec3 objectNormal = vec3( normal );\n#ifdef USE_TANGENT\n	vec3 objectTangent = vec3( tangent.xyz );\n#endif", bsdfs: "float G_BlinnPhong_Implicit( ) {\n	return 0.25;\n}\nfloat D_BlinnPhong( const in float shininess, const in float dotNH ) {\n	return RECIPROCAL_PI * ( shininess * 0.5 + 1.0 ) * pow( dotNH, shininess );\n}\nvec3 BRDF_BlinnPhong( const in vec3 lightDir, const in vec3 viewDir, const in vec3 normal, const in vec3 specularColor, const in float shininess ) {\n	vec3 halfDir = normalize( lightDir + viewDir );\n	float dotNH = saturate( dot( normal, halfDir ) );\n	float dotVH = saturate( dot( viewDir, halfDir ) );\n	vec3 F = F_Schlick( specularColor, 1.0, dotVH );\n	float G = G_BlinnPhong_Implicit( );\n	float D = D_BlinnPhong( shininess, dotNH );\n	return F * ( G * D );\n} // validated", iridescence_fragment: "#ifdef USE_IRIDESCENCE\n	const mat3 XYZ_TO_REC709 = mat3(\n		 3.2404542, -0.9692660,  0.0556434,\n		-1.5371385,  1.8760108, -0.2040259,\n		-0.4985314,  0.0415560,  1.0572252\n	);\n	vec3 Fresnel0ToIor( vec3 fresnel0 ) {\n		vec3 sqrtF0 = sqrt( fresnel0 );\n		return ( vec3( 1.0 ) + sqrtF0 ) / ( vec3( 1.0 ) - sqrtF0 );\n	}\n	vec3 IorToFresnel0( vec3 transmittedIor, float incidentIor ) {\n		return pow2( ( transmittedIor - vec3( incidentIor ) ) / ( transmittedIor + vec3( incidentIor ) ) );\n	}\n	float IorToFresnel0( float transmittedIor, float incidentIor ) {\n		return pow2( ( transmittedIor - incidentIor ) / ( transmittedIor + incidentIor ));\n	}\n	vec3 evalSensitivity( float OPD, vec3 shift ) {\n		float phase = 2.0 * PI * OPD * 1.0e-9;\n		vec3 val = vec3( 5.4856e-13, 4.4201e-13, 5.2481e-13 );\n		vec3 pos = vec3( 1.6810e+06, 1.7953e+06, 2.2084e+06 );\n		vec3 var = vec3( 4.3278e+09, 9.3046e+09, 6.6121e+09 );\n		vec3 xyz = val * sqrt( 2.0 * PI * var ) * cos( pos * phase + shift ) * exp( - pow2( phase ) * var );\n		xyz.x += 9.7470e-14 * sqrt( 2.0 * PI * 4.5282e+09 ) * cos( 2.2399e+06 * phase + shift[ 0 ] ) * exp( - 4.5282e+09 * pow2( phase ) );\n		xyz /= 1.0685e-7;\n		vec3 rgb = XYZ_TO_REC709 * xyz;\n		return rgb;\n	}\n	vec3 evalIridescence( float outsideIOR, float eta2, float cosTheta1, float thinFilmThickness, vec3 baseF0 ) {\n		vec3 I;\n		float iridescenceIOR = mix( outsideIOR, eta2, smoothstep( 0.0, 0.03, thinFilmThickness ) );\n		float sinTheta2Sq = pow2( outsideIOR / iridescenceIOR ) * ( 1.0 - pow2( cosTheta1 ) );\n		float cosTheta2Sq = 1.0 - sinTheta2Sq;\n		if ( cosTheta2Sq < 0.0 ) {\n			return vec3( 1.0 );\n		}\n		float cosTheta2 = sqrt( cosTheta2Sq );\n		float R0 = IorToFresnel0( iridescenceIOR, outsideIOR );\n		float R12 = F_Schlick( R0, 1.0, cosTheta1 );\n		float T121 = 1.0 - R12;\n		float phi12 = 0.0;\n		if ( iridescenceIOR < outsideIOR ) phi12 = PI;\n		float phi21 = PI - phi12;\n		vec3 baseIOR = Fresnel0ToIor( clamp( baseF0, 0.0, 0.9999 ) );		vec3 R1 = IorToFresnel0( baseIOR, iridescenceIOR );\n		vec3 R23 = F_Schlick( R1, 1.0, cosTheta2 );\n		vec3 phi23 = vec3( 0.0 );\n		if ( baseIOR[ 0 ] < iridescenceIOR ) phi23[ 0 ] = PI;\n		if ( baseIOR[ 1 ] < iridescenceIOR ) phi23[ 1 ] = PI;\n		if ( baseIOR[ 2 ] < iridescenceIOR ) phi23[ 2 ] = PI;\n		float OPD = 2.0 * iridescenceIOR * thinFilmThickness * cosTheta2;\n		vec3 phi = vec3( phi21 ) + phi23;\n		vec3 R123 = clamp( R12 * R23, 1e-5, 0.9999 );\n		vec3 r123 = sqrt( R123 );\n		vec3 Rs = pow2( T121 ) * R23 / ( vec3( 1.0 ) - R123 );\n		vec3 C0 = R12 + Rs;\n		I = C0;\n		vec3 Cm = Rs - T121;\n		for ( int m = 1; m <= 2; ++ m ) {\n			Cm *= r123;\n			vec3 Sm = 2.0 * evalSensitivity( float( m ) * OPD, float( m ) * phi );\n			I += Cm * Sm;\n		}\n		return max( I, vec3( 0.0 ) );\n	}\n#endif", bumpmap_pars_fragment: "#ifdef USE_BUMPMAP\n	uniform sampler2D bumpMap;\n	uniform float bumpScale;\n	vec2 dHdxy_fwd() {\n		vec2 dSTdx = dFdx( vBumpMapUv );\n		vec2 dSTdy = dFdy( vBumpMapUv );\n		float Hll = bumpScale * texture2D( bumpMap, vBumpMapUv ).x;\n		float dBx = bumpScale * texture2D( bumpMap, vBumpMapUv + dSTdx ).x - Hll;\n		float dBy = bumpScale * texture2D( bumpMap, vBumpMapUv + dSTdy ).x - Hll;\n		return vec2( dBx, dBy );\n	}\n	vec3 perturbNormalArb( vec3 surf_pos, vec3 surf_norm, vec2 dHdxy, float faceDirection ) {\n		vec3 vSigmaX = normalize( dFdx( surf_pos.xyz ) );\n		vec3 vSigmaY = normalize( dFdy( surf_pos.xyz ) );\n		vec3 vN = surf_norm;\n		vec3 R1 = cross( vSigmaY, vN );\n		vec3 R2 = cross( vN, vSigmaX );\n		float fDet = dot( vSigmaX, R1 ) * faceDirection;\n		vec3 vGrad = sign( fDet ) * ( dHdxy.x * R1 + dHdxy.y * R2 );\n		return normalize( abs( fDet ) * surf_norm - vGrad );\n	}\n#endif", clipping_planes_fragment: "#if NUM_CLIPPING_PLANES > 0\n	vec4 plane;\n	#ifdef ALPHA_TO_COVERAGE\n		float distanceToPlane, distanceGradient;\n		float clipOpacity = 1.0;\n		#pragma unroll_loop_start\n		for ( int i = 0; i < UNION_CLIPPING_PLANES; i ++ ) {\n			plane = clippingPlanes[ i ];\n			distanceToPlane = - dot( vClipPosition, plane.xyz ) + plane.w;\n			distanceGradient = fwidth( distanceToPlane ) / 2.0;\n			clipOpacity *= smoothstep( - distanceGradient, distanceGradient, distanceToPlane );\n			if ( clipOpacity == 0.0 ) discard;\n		}\n		#pragma unroll_loop_end\n		#if UNION_CLIPPING_PLANES < NUM_CLIPPING_PLANES\n			float unionClipOpacity = 1.0;\n			#pragma unroll_loop_start\n			for ( int i = UNION_CLIPPING_PLANES; i < NUM_CLIPPING_PLANES; i ++ ) {\n				plane = clippingPlanes[ i ];\n				distanceToPlane = - dot( vClipPosition, plane.xyz ) + plane.w;\n				distanceGradient = fwidth( distanceToPlane ) / 2.0;\n				unionClipOpacity *= 1.0 - smoothstep( - distanceGradient, distanceGradient, distanceToPlane );\n			}\n			#pragma unroll_loop_end\n			clipOpacity *= 1.0 - unionClipOpacity;\n		#endif\n		diffuseColor.a *= clipOpacity;\n		if ( diffuseColor.a == 0.0 ) discard;\n	#else\n		#pragma unroll_loop_start\n		for ( int i = 0; i < UNION_CLIPPING_PLANES; i ++ ) {\n			plane = clippingPlanes[ i ];\n			if ( dot( vClipPosition, plane.xyz ) > plane.w ) discard;\n		}\n		#pragma unroll_loop_end\n		#if UNION_CLIPPING_PLANES < NUM_CLIPPING_PLANES\n			bool clipped = true;\n			#pragma unroll_loop_start\n			for ( int i = UNION_CLIPPING_PLANES; i < NUM_CLIPPING_PLANES; i ++ ) {\n				plane = clippingPlanes[ i ];\n				clipped = ( dot( vClipPosition, plane.xyz ) > plane.w ) && clipped;\n			}\n			#pragma unroll_loop_end\n			if ( clipped ) discard;\n		#endif\n	#endif\n#endif", clipping_planes_pars_fragment: "#if NUM_CLIPPING_PLANES > 0\n	varying vec3 vClipPosition;\n	uniform vec4 clippingPlanes[ NUM_CLIPPING_PLANES ];\n#endif", clipping_planes_pars_vertex: "#if NUM_CLIPPING_PLANES > 0\n	varying vec3 vClipPosition;\n#endif", clipping_planes_vertex: "#if NUM_CLIPPING_PLANES > 0\n	vClipPosition = - mvPosition.xyz;\n#endif", color_fragment: "#if defined( USE_COLOR_ALPHA )\n	diffuseColor *= vColor;\n#elif defined( USE_COLOR )\n	diffuseColor.rgb *= vColor;\n#endif", color_pars_fragment: "#if defined( USE_COLOR_ALPHA )\n	varying vec4 vColor;\n#elif defined( USE_COLOR )\n	varying vec3 vColor;\n#endif", color_pars_vertex: "#if defined( USE_COLOR_ALPHA )\n	varying vec4 vColor;\n#elif defined( USE_COLOR ) || defined( USE_INSTANCING_COLOR ) || defined( USE_BATCHING_COLOR )\n	varying vec3 vColor;\n#endif", color_vertex: "#if defined( USE_COLOR_ALPHA )\n	vColor = vec4( 1.0 );\n#elif defined( USE_COLOR ) || defined( USE_INSTANCING_COLOR ) || defined( USE_BATCHING_COLOR )\n	vColor = vec3( 1.0 );\n#endif\n#ifdef USE_COLOR\n	vColor *= color;\n#endif\n#ifdef USE_INSTANCING_COLOR\n	vColor.xyz *= instanceColor.xyz;\n#endif\n#ifdef USE_BATCHING_COLOR\n	vec3 batchingColor = getBatchingColor( getIndirectIndex( gl_DrawID ) );\n	vColor.xyz *= batchingColor.xyz;\n#endif", common: "#define PI 3.141592653589793\n#define PI2 6.283185307179586\n#define PI_HALF 1.5707963267948966\n#define RECIPROCAL_PI 0.3183098861837907\n#define RECIPROCAL_PI2 0.15915494309189535\n#define EPSILON 1e-6\n#ifndef saturate\n#define saturate( a ) clamp( a, 0.0, 1.0 )\n#endif\n#define whiteComplement( a ) ( 1.0 - saturate( a ) )\nfloat pow2( const in float x ) { return x*x; }\nvec3 pow2( const in vec3 x ) { return x*x; }\nfloat pow3( const in float x ) { return x*x*x; }\nfloat pow4( const in float x ) { float x2 = x*x; return x2*x2; }\nfloat max3( const in vec3 v ) { return max( max( v.x, v.y ), v.z ); }\nfloat average( const in vec3 v ) { return dot( v, vec3( 0.3333333 ) ); }\nhighp float rand( const in vec2 uv ) {\n	const highp float a = 12.9898, b = 78.233, c = 43758.5453;\n	highp float dt = dot( uv.xy, vec2( a,b ) ), sn = mod( dt, PI );\n	return fract( sin( sn ) * c );\n}\n#ifdef HIGH_PRECISION\n	float precisionSafeLength( vec3 v ) { return length( v ); }\n#else\n	float precisionSafeLength( vec3 v ) {\n		float maxComponent = max3( abs( v ) );\n		return length( v / maxComponent ) * maxComponent;\n	}\n#endif\nstruct IncidentLight {\n	vec3 color;\n	vec3 direction;\n	bool visible;\n};\nstruct ReflectedLight {\n	vec3 directDiffuse;\n	vec3 directSpecular;\n	vec3 indirectDiffuse;\n	vec3 indirectSpecular;\n};\n#ifdef USE_ALPHAHASH\n	varying vec3 vPosition;\n#endif\nvec3 transformDirection( in vec3 dir, in mat4 matrix ) {\n	return normalize( ( matrix * vec4( dir, 0.0 ) ).xyz );\n}\nvec3 inverseTransformDirection( in vec3 dir, in mat4 matrix ) {\n	return normalize( ( vec4( dir, 0.0 ) * matrix ).xyz );\n}\nmat3 transposeMat3( const in mat3 m ) {\n	mat3 tmp;\n	tmp[ 0 ] = vec3( m[ 0 ].x, m[ 1 ].x, m[ 2 ].x );\n	tmp[ 1 ] = vec3( m[ 0 ].y, m[ 1 ].y, m[ 2 ].y );\n	tmp[ 2 ] = vec3( m[ 0 ].z, m[ 1 ].z, m[ 2 ].z );\n	return tmp;\n}\nbool isPerspectiveMatrix( mat4 m ) {\n	return m[ 2 ][ 3 ] == - 1.0;\n}\nvec2 equirectUv( in vec3 dir ) {\n	float u = atan( dir.z, dir.x ) * RECIPROCAL_PI2 + 0.5;\n	float v = asin( clamp( dir.y, - 1.0, 1.0 ) ) * RECIPROCAL_PI + 0.5;\n	return vec2( u, v );\n}\nvec3 BRDF_Lambert( const in vec3 diffuseColor ) {\n	return RECIPROCAL_PI * diffuseColor;\n}\nvec3 F_Schlick( const in vec3 f0, const in float f90, const in float dotVH ) {\n	float fresnel = exp2( ( - 5.55473 * dotVH - 6.98316 ) * dotVH );\n	return f0 * ( 1.0 - fresnel ) + ( f90 * fresnel );\n}\nfloat F_Schlick( const in float f0, const in float f90, const in float dotVH ) {\n	float fresnel = exp2( ( - 5.55473 * dotVH - 6.98316 ) * dotVH );\n	return f0 * ( 1.0 - fresnel ) + ( f90 * fresnel );\n} // validated", cube_uv_reflection_fragment: "#ifdef ENVMAP_TYPE_CUBE_UV\n	#define cubeUV_minMipLevel 4.0\n	#define cubeUV_minTileSize 16.0\n	float getFace( vec3 direction ) {\n		vec3 absDirection = abs( direction );\n		float face = - 1.0;\n		if ( absDirection.x > absDirection.z ) {\n			if ( absDirection.x > absDirection.y )\n				face = direction.x > 0.0 ? 0.0 : 3.0;\n			else\n				face = direction.y > 0.0 ? 1.0 : 4.0;\n		} else {\n			if ( absDirection.z > absDirection.y )\n				face = direction.z > 0.0 ? 2.0 : 5.0;\n			else\n				face = direction.y > 0.0 ? 1.0 : 4.0;\n		}\n		return face;\n	}\n	vec2 getUV( vec3 direction, float face ) {\n		vec2 uv;\n		if ( face == 0.0 ) {\n			uv = vec2( direction.z, direction.y ) / abs( direction.x );\n		} else if ( face == 1.0 ) {\n			uv = vec2( - direction.x, - direction.z ) / abs( direction.y );\n		} else if ( face == 2.0 ) {\n			uv = vec2( - direction.x, direction.y ) / abs( direction.z );\n		} else if ( face == 3.0 ) {\n			uv = vec2( - direction.z, direction.y ) / abs( direction.x );\n		} else if ( face == 4.0 ) {\n			uv = vec2( - direction.x, direction.z ) / abs( direction.y );\n		} else {\n			uv = vec2( direction.x, direction.y ) / abs( direction.z );\n		}\n		return 0.5 * ( uv + 1.0 );\n	}\n	vec3 bilinearCubeUV( sampler2D envMap, vec3 direction, float mipInt ) {\n		float face = getFace( direction );\n		float filterInt = max( cubeUV_minMipLevel - mipInt, 0.0 );\n		mipInt = max( mipInt, cubeUV_minMipLevel );\n		float faceSize = exp2( mipInt );\n		highp vec2 uv = getUV( direction, face ) * ( faceSize - 2.0 ) + 1.0;\n		if ( face > 2.0 ) {\n			uv.y += faceSize;\n			face -= 3.0;\n		}\n		uv.x += face * faceSize;\n		uv.x += filterInt * 3.0 * cubeUV_minTileSize;\n		uv.y += 4.0 * ( exp2( CUBEUV_MAX_MIP ) - faceSize );\n		uv.x *= CUBEUV_TEXEL_WIDTH;\n		uv.y *= CUBEUV_TEXEL_HEIGHT;\n		#ifdef texture2DGradEXT\n			return texture2DGradEXT( envMap, uv, vec2( 0.0 ), vec2( 0.0 ) ).rgb;\n		#else\n			return texture2D( envMap, uv ).rgb;\n		#endif\n	}\n	#define cubeUV_r0 1.0\n	#define cubeUV_m0 - 2.0\n	#define cubeUV_r1 0.8\n	#define cubeUV_m1 - 1.0\n	#define cubeUV_r4 0.4\n	#define cubeUV_m4 2.0\n	#define cubeUV_r5 0.305\n	#define cubeUV_m5 3.0\n	#define cubeUV_r6 0.21\n	#define cubeUV_m6 4.0\n	float roughnessToMip( float roughness ) {\n		float mip = 0.0;\n		if ( roughness >= cubeUV_r1 ) {\n			mip = ( cubeUV_r0 - roughness ) * ( cubeUV_m1 - cubeUV_m0 ) / ( cubeUV_r0 - cubeUV_r1 ) + cubeUV_m0;\n		} else if ( roughness >= cubeUV_r4 ) {\n			mip = ( cubeUV_r1 - roughness ) * ( cubeUV_m4 - cubeUV_m1 ) / ( cubeUV_r1 - cubeUV_r4 ) + cubeUV_m1;\n		} else if ( roughness >= cubeUV_r5 ) {\n			mip = ( cubeUV_r4 - roughness ) * ( cubeUV_m5 - cubeUV_m4 ) / ( cubeUV_r4 - cubeUV_r5 ) + cubeUV_m4;\n		} else if ( roughness >= cubeUV_r6 ) {\n			mip = ( cubeUV_r5 - roughness ) * ( cubeUV_m6 - cubeUV_m5 ) / ( cubeUV_r5 - cubeUV_r6 ) + cubeUV_m5;\n		} else {\n			mip = - 2.0 * log2( 1.16 * roughness );		}\n		return mip;\n	}\n	vec4 textureCubeUV( sampler2D envMap, vec3 sampleDir, float roughness ) {\n		float mip = clamp( roughnessToMip( roughness ), cubeUV_m0, CUBEUV_MAX_MIP );\n		float mipF = fract( mip );\n		float mipInt = floor( mip );\n		vec3 color0 = bilinearCubeUV( envMap, sampleDir, mipInt );\n		if ( mipF == 0.0 ) {\n			return vec4( color0, 1.0 );\n		} else {\n			vec3 color1 = bilinearCubeUV( envMap, sampleDir, mipInt + 1.0 );\n			return vec4( mix( color0, color1, mipF ), 1.0 );\n		}\n	}\n#endif", defaultnormal_vertex: "vec3 transformedNormal = objectNormal;\n#ifdef USE_TANGENT\n	vec3 transformedTangent = objectTangent;\n#endif\n#ifdef USE_BATCHING\n	mat3 bm = mat3( batchingMatrix );\n	transformedNormal /= vec3( dot( bm[ 0 ], bm[ 0 ] ), dot( bm[ 1 ], bm[ 1 ] ), dot( bm[ 2 ], bm[ 2 ] ) );\n	transformedNormal = bm * transformedNormal;\n	#ifdef USE_TANGENT\n		transformedTangent = bm * transformedTangent;\n	#endif\n#endif\n#ifdef USE_INSTANCING\n	mat3 im = mat3( instanceMatrix );\n	transformedNormal /= vec3( dot( im[ 0 ], im[ 0 ] ), dot( im[ 1 ], im[ 1 ] ), dot( im[ 2 ], im[ 2 ] ) );\n	transformedNormal = im * transformedNormal;\n	#ifdef USE_TANGENT\n		transformedTangent = im * transformedTangent;\n	#endif\n#endif\ntransformedNormal = normalMatrix * transformedNormal;\n#ifdef FLIP_SIDED\n	transformedNormal = - transformedNormal;\n#endif\n#ifdef USE_TANGENT\n	transformedTangent = ( modelViewMatrix * vec4( transformedTangent, 0.0 ) ).xyz;\n	#ifdef FLIP_SIDED\n		transformedTangent = - transformedTangent;\n	#endif\n#endif", displacementmap_pars_vertex: "#ifdef USE_DISPLACEMENTMAP\n	uniform sampler2D displacementMap;\n	uniform float displacementScale;\n	uniform float displacementBias;\n#endif", displacementmap_vertex: "#ifdef USE_DISPLACEMENTMAP\n	transformed += normalize( objectNormal ) * ( texture2D( displacementMap, vDisplacementMapUv ).x * displacementScale + displacementBias );\n#endif", emissivemap_fragment: "#ifdef USE_EMISSIVEMAP\n	vec4 emissiveColor = texture2D( emissiveMap, vEmissiveMapUv );\n	#ifdef DECODE_VIDEO_TEXTURE_EMISSIVE\n		emissiveColor = sRGBTransferEOTF( emissiveColor );\n	#endif\n	totalEmissiveRadiance *= emissiveColor.rgb;\n#endif", emissivemap_pars_fragment: "#ifdef USE_EMISSIVEMAP\n	uniform sampler2D emissiveMap;\n#endif", colorspace_fragment: "gl_FragColor = linearToOutputTexel( gl_FragColor );", colorspace_pars_fragment: "vec4 LinearTransferOETF( in vec4 value ) {\n	return value;\n}\nvec4 sRGBTransferEOTF( in vec4 value ) {\n	return vec4( mix( pow( value.rgb * 0.9478672986 + vec3( 0.0521327014 ), vec3( 2.4 ) ), value.rgb * 0.0773993808, vec3( lessThanEqual( value.rgb, vec3( 0.04045 ) ) ) ), value.a );\n}\nvec4 sRGBTransferOETF( in vec4 value ) {\n	return vec4( mix( pow( value.rgb, vec3( 0.41666 ) ) * 1.055 - vec3( 0.055 ), value.rgb * 12.92, vec3( lessThanEqual( value.rgb, vec3( 0.0031308 ) ) ) ), value.a );\n}", envmap_fragment: "#ifdef USE_ENVMAP\n	#ifdef ENV_WORLDPOS\n		vec3 cameraToFrag;\n		if ( isOrthographic ) {\n			cameraToFrag = normalize( vec3( - viewMatrix[ 0 ][ 2 ], - viewMatrix[ 1 ][ 2 ], - viewMatrix[ 2 ][ 2 ] ) );\n		} else {\n			cameraToFrag = normalize( vWorldPosition - cameraPosition );\n		}\n		vec3 worldNormal = inverseTransformDirection( normal, viewMatrix );\n		#ifdef ENVMAP_MODE_REFLECTION\n			vec3 reflectVec = reflect( cameraToFrag, worldNormal );\n		#else\n			vec3 reflectVec = refract( cameraToFrag, worldNormal, refractionRatio );\n		#endif\n	#else\n		vec3 reflectVec = vReflect;\n	#endif\n	#ifdef ENVMAP_TYPE_CUBE\n		vec4 envColor = textureCube( envMap, envMapRotation * vec3( flipEnvMap * reflectVec.x, reflectVec.yz ) );\n	#else\n		vec4 envColor = vec4( 0.0 );\n	#endif\n	#ifdef ENVMAP_BLENDING_MULTIPLY\n		outgoingLight = mix( outgoingLight, outgoingLight * envColor.xyz, specularStrength * reflectivity );\n	#elif defined( ENVMAP_BLENDING_MIX )\n		outgoingLight = mix( outgoingLight, envColor.xyz, specularStrength * reflectivity );\n	#elif defined( ENVMAP_BLENDING_ADD )\n		outgoingLight += envColor.xyz * specularStrength * reflectivity;\n	#endif\n#endif", envmap_common_pars_fragment: "#ifdef USE_ENVMAP\n	uniform float envMapIntensity;\n	uniform float flipEnvMap;\n	uniform mat3 envMapRotation;\n	#ifdef ENVMAP_TYPE_CUBE\n		uniform samplerCube envMap;\n	#else\n		uniform sampler2D envMap;\n	#endif\n	\n#endif", envmap_pars_fragment: "#ifdef USE_ENVMAP\n	uniform float reflectivity;\n	#if defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( PHONG ) || defined( LAMBERT )\n		#define ENV_WORLDPOS\n	#endif\n	#ifdef ENV_WORLDPOS\n		varying vec3 vWorldPosition;\n		uniform float refractionRatio;\n	#else\n		varying vec3 vReflect;\n	#endif\n#endif", envmap_pars_vertex: "#ifdef USE_ENVMAP\n	#if defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( PHONG ) || defined( LAMBERT )\n		#define ENV_WORLDPOS\n	#endif\n	#ifdef ENV_WORLDPOS\n		\n		varying vec3 vWorldPosition;\n	#else\n		varying vec3 vReflect;\n		uniform float refractionRatio;\n	#endif\n#endif", envmap_physical_pars_fragment: "#ifdef USE_ENVMAP\n	vec3 getIBLIrradiance( const in vec3 normal ) {\n		#ifdef ENVMAP_TYPE_CUBE_UV\n			vec3 worldNormal = inverseTransformDirection( normal, viewMatrix );\n			vec4 envMapColor = textureCubeUV( envMap, envMapRotation * worldNormal, 1.0 );\n			return PI * envMapColor.rgb * envMapIntensity;\n		#else\n			return vec3( 0.0 );\n		#endif\n	}\n	vec3 getIBLRadiance( const in vec3 viewDir, const in vec3 normal, const in float roughness ) {\n		#ifdef ENVMAP_TYPE_CUBE_UV\n			vec3 reflectVec = reflect( - viewDir, normal );\n			reflectVec = normalize( mix( reflectVec, normal, roughness * roughness) );\n			reflectVec = inverseTransformDirection( reflectVec, viewMatrix );\n			vec4 envMapColor = textureCubeUV( envMap, envMapRotation * reflectVec, roughness );\n			return envMapColor.rgb * envMapIntensity;\n		#else\n			return vec3( 0.0 );\n		#endif\n	}\n	#ifdef USE_ANISOTROPY\n		vec3 getIBLAnisotropyRadiance( const in vec3 viewDir, const in vec3 normal, const in float roughness, const in vec3 bitangent, const in float anisotropy ) {\n			#ifdef ENVMAP_TYPE_CUBE_UV\n				vec3 bentNormal = cross( bitangent, viewDir );\n				bentNormal = normalize( cross( bentNormal, bitangent ) );\n				bentNormal = normalize( mix( bentNormal, normal, pow2( pow2( 1.0 - anisotropy * ( 1.0 - roughness ) ) ) ) );\n				return getIBLRadiance( viewDir, bentNormal, roughness );\n			#else\n				return vec3( 0.0 );\n			#endif\n		}\n	#endif\n#endif", envmap_vertex: "#ifdef USE_ENVMAP\n	#ifdef ENV_WORLDPOS\n		vWorldPosition = worldPosition.xyz;\n	#else\n		vec3 cameraToVertex;\n		if ( isOrthographic ) {\n			cameraToVertex = normalize( vec3( - viewMatrix[ 0 ][ 2 ], - viewMatrix[ 1 ][ 2 ], - viewMatrix[ 2 ][ 2 ] ) );\n		} else {\n			cameraToVertex = normalize( worldPosition.xyz - cameraPosition );\n		}\n		vec3 worldNormal = inverseTransformDirection( transformedNormal, viewMatrix );\n		#ifdef ENVMAP_MODE_REFLECTION\n			vReflect = reflect( cameraToVertex, worldNormal );\n		#else\n			vReflect = refract( cameraToVertex, worldNormal, refractionRatio );\n		#endif\n	#endif\n#endif", fog_vertex: "#ifdef USE_FOG\n	vFogDepth = - mvPosition.z;\n#endif", fog_pars_vertex: "#ifdef USE_FOG\n	varying float vFogDepth;\n#endif", fog_fragment: "#ifdef USE_FOG\n	#ifdef FOG_EXP2\n		float fogFactor = 1.0 - exp( - fogDensity * fogDensity * vFogDepth * vFogDepth );\n	#else\n		float fogFactor = smoothstep( fogNear, fogFar, vFogDepth );\n	#endif\n	gl_FragColor.rgb = mix( gl_FragColor.rgb, fogColor, fogFactor );\n#endif", fog_pars_fragment: "#ifdef USE_FOG\n	uniform vec3 fogColor;\n	varying float vFogDepth;\n	#ifdef FOG_EXP2\n		uniform float fogDensity;\n	#else\n		uniform float fogNear;\n		uniform float fogFar;\n	#endif\n#endif", gradientmap_pars_fragment: "#ifdef USE_GRADIENTMAP\n	uniform sampler2D gradientMap;\n#endif\nvec3 getGradientIrradiance( vec3 normal, vec3 lightDirection ) {\n	float dotNL = dot( normal, lightDirection );\n	vec2 coord = vec2( dotNL * 0.5 + 0.5, 0.0 );\n	#ifdef USE_GRADIENTMAP\n		return vec3( texture2D( gradientMap, coord ).r );\n	#else\n		vec2 fw = fwidth( coord ) * 0.5;\n		return mix( vec3( 0.7 ), vec3( 1.0 ), smoothstep( 0.7 - fw.x, 0.7 + fw.x, coord.x ) );\n	#endif\n}", lightmap_pars_fragment: "#ifdef USE_LIGHTMAP\n	uniform sampler2D lightMap;\n	uniform float lightMapIntensity;\n#endif", lights_lambert_fragment: "LambertMaterial material;\nmaterial.diffuseColor = diffuseColor.rgb;\nmaterial.specularStrength = specularStrength;", lights_lambert_pars_fragment: "varying vec3 vViewPosition;\nstruct LambertMaterial {\n	vec3 diffuseColor;\n	float specularStrength;\n};\nvoid RE_Direct_Lambert( const in IncidentLight directLight, const in vec3 geometryPosition, const in vec3 geometryNormal, const in vec3 geometryViewDir, const in vec3 geometryClearcoatNormal, const in LambertMaterial material, inout ReflectedLight reflectedLight ) {\n	float dotNL = saturate( dot( geometryNormal, directLight.direction ) );\n	vec3 irradiance = dotNL * directLight.color;\n	reflectedLight.directDiffuse += irradiance * BRDF_Lambert( material.diffuseColor );\n}\nvoid RE_IndirectDiffuse_Lambert( const in vec3 irradiance, const in vec3 geometryPosition, const in vec3 geometryNormal, const in vec3 geometryViewDir, const in vec3 geometryClearcoatNormal, const in LambertMaterial material, inout ReflectedLight reflectedLight ) {\n	reflectedLight.indirectDiffuse += irradiance * BRDF_Lambert( material.diffuseColor );\n}\n#define RE_Direct				RE_Direct_Lambert\n#define RE_IndirectDiffuse		RE_IndirectDiffuse_Lambert", lights_pars_begin: "uniform bool receiveShadow;\nuniform vec3 ambientLightColor;\n#if defined( USE_LIGHT_PROBES )\n	uniform vec3 lightProbe[ 9 ];\n#endif\nvec3 shGetIrradianceAt( in vec3 normal, in vec3 shCoefficients[ 9 ] ) {\n	float x = normal.x, y = normal.y, z = normal.z;\n	vec3 result = shCoefficients[ 0 ] * 0.886227;\n	result += shCoefficients[ 1 ] * 2.0 * 0.511664 * y;\n	result += shCoefficients[ 2 ] * 2.0 * 0.511664 * z;\n	result += shCoefficients[ 3 ] * 2.0 * 0.511664 * x;\n	result += shCoefficients[ 4 ] * 2.0 * 0.429043 * x * y;\n	result += shCoefficients[ 5 ] * 2.0 * 0.429043 * y * z;\n	result += shCoefficients[ 6 ] * ( 0.743125 * z * z - 0.247708 );\n	result += shCoefficients[ 7 ] * 2.0 * 0.429043 * x * z;\n	result += shCoefficients[ 8 ] * 0.429043 * ( x * x - y * y );\n	return result;\n}\nvec3 getLightProbeIrradiance( const in vec3 lightProbe[ 9 ], const in vec3 normal ) {\n	vec3 worldNormal = inverseTransformDirection( normal, viewMatrix );\n	vec3 irradiance = shGetIrradianceAt( worldNormal, lightProbe );\n	return irradiance;\n}\nvec3 getAmbientLightIrradiance( const in vec3 ambientLightColor ) {\n	vec3 irradiance = ambientLightColor;\n	return irradiance;\n}\nfloat getDistanceAttenuation( const in float lightDistance, const in float cutoffDistance, const in float decayExponent ) {\n	float distanceFalloff = 1.0 / max( pow( lightDistance, decayExponent ), 0.01 );\n	if ( cutoffDistance > 0.0 ) {\n		distanceFalloff *= pow2( saturate( 1.0 - pow4( lightDistance / cutoffDistance ) ) );\n	}\n	return distanceFalloff;\n}\nfloat getSpotAttenuation( const in float coneCosine, const in float penumbraCosine, const in float angleCosine ) {\n	return smoothstep( coneCosine, penumbraCosine, angleCosine );\n}\n#if NUM_DIR_LIGHTS > 0\n	struct DirectionalLight {\n		vec3 direction;\n		vec3 color;\n	};\n	uniform DirectionalLight directionalLights[ NUM_DIR_LIGHTS ];\n	void getDirectionalLightInfo( const in DirectionalLight directionalLight, out IncidentLight light ) {\n		light.color = directionalLight.color;\n		light.direction = directionalLight.direction;\n		light.visible = true;\n	}\n#endif\n#if NUM_POINT_LIGHTS > 0\n	struct PointLight {\n		vec3 position;\n		vec3 color;\n		float distance;\n		float decay;\n	};\n	uniform PointLight pointLights[ NUM_POINT_LIGHTS ];\n	void getPointLightInfo( const in PointLight pointLight, const in vec3 geometryPosition, out IncidentLight light ) {\n		vec3 lVector = pointLight.position - geometryPosition;\n		light.direction = normalize( lVector );\n		float lightDistance = length( lVector );\n		light.color = pointLight.color;\n		light.color *= getDistanceAttenuation( lightDistance, pointLight.distance, pointLight.decay );\n		light.visible = ( light.color != vec3( 0.0 ) );\n	}\n#endif\n#if NUM_SPOT_LIGHTS > 0\n	struct SpotLight {\n		vec3 position;\n		vec3 direction;\n		vec3 color;\n		float distance;\n		float decay;\n		float coneCos;\n		float penumbraCos;\n	};\n	uniform SpotLight spotLights[ NUM_SPOT_LIGHTS ];\n	void getSpotLightInfo( const in SpotLight spotLight, const in vec3 geometryPosition, out IncidentLight light ) {\n		vec3 lVector = spotLight.position - geometryPosition;\n		light.direction = normalize( lVector );\n		float angleCos = dot( light.direction, spotLight.direction );\n		float spotAttenuation = getSpotAttenuation( spotLight.coneCos, spotLight.penumbraCos, angleCos );\n		if ( spotAttenuation > 0.0 ) {\n			float lightDistance = length( lVector );\n			light.color = spotLight.color * spotAttenuation;\n			light.color *= getDistanceAttenuation( lightDistance, spotLight.distance, spotLight.decay );\n			light.visible = ( light.color != vec3( 0.0 ) );\n		} else {\n			light.color = vec3( 0.0 );\n			light.visible = false;\n		}\n	}\n#endif\n#if NUM_RECT_AREA_LIGHTS > 0\n	struct RectAreaLight {\n		vec3 color;\n		vec3 position;\n		vec3 halfWidth;\n		vec3 halfHeight;\n	};\n	uniform sampler2D ltc_1;	uniform sampler2D ltc_2;\n	uniform RectAreaLight rectAreaLights[ NUM_RECT_AREA_LIGHTS ];\n#endif\n#if NUM_HEMI_LIGHTS > 0\n	struct HemisphereLight {\n		vec3 direction;\n		vec3 skyColor;\n		vec3 groundColor;\n	};\n	uniform HemisphereLight hemisphereLights[ NUM_HEMI_LIGHTS ];\n	vec3 getHemisphereLightIrradiance( const in HemisphereLight hemiLight, const in vec3 normal ) {\n		float dotNL = dot( normal, hemiLight.direction );\n		float hemiDiffuseWeight = 0.5 * dotNL + 0.5;\n		vec3 irradiance = mix( hemiLight.groundColor, hemiLight.skyColor, hemiDiffuseWeight );\n		return irradiance;\n	}\n#endif", lights_toon_fragment: "ToonMaterial material;\nmaterial.diffuseColor = diffuseColor.rgb;", lights_toon_pars_fragment: "varying vec3 vViewPosition;\nstruct ToonMaterial {\n	vec3 diffuseColor;\n};\nvoid RE_Direct_Toon( const in IncidentLight directLight, const in vec3 geometryPosition, const in vec3 geometryNormal, const in vec3 geometryViewDir, const in vec3 geometryClearcoatNormal, const in ToonMaterial material, inout ReflectedLight reflectedLight ) {\n	vec3 irradiance = getGradientIrradiance( geometryNormal, directLight.direction ) * directLight.color;\n	reflectedLight.directDiffuse += irradiance * BRDF_Lambert( material.diffuseColor );\n}\nvoid RE_IndirectDiffuse_Toon( const in vec3 irradiance, const in vec3 geometryPosition, const in vec3 geometryNormal, const in vec3 geometryViewDir, const in vec3 geometryClearcoatNormal, const in ToonMaterial material, inout ReflectedLight reflectedLight ) {\n	reflectedLight.indirectDiffuse += irradiance * BRDF_Lambert( material.diffuseColor );\n}\n#define RE_Direct				RE_Direct_Toon\n#define RE_IndirectDiffuse		RE_IndirectDiffuse_Toon", lights_phong_fragment: "BlinnPhongMaterial material;\nmaterial.diffuseColor = diffuseColor.rgb;\nmaterial.specularColor = specular;\nmaterial.specularShininess = shininess;\nmaterial.specularStrength = specularStrength;", lights_phong_pars_fragment: "varying vec3 vViewPosition;\nstruct BlinnPhongMaterial {\n	vec3 diffuseColor;\n	vec3 specularColor;\n	float specularShininess;\n	float specularStrength;\n};\nvoid RE_Direct_BlinnPhong( const in IncidentLight directLight, const in vec3 geometryPosition, const in vec3 geometryNormal, const in vec3 geometryViewDir, const in vec3 geometryClearcoatNormal, const in BlinnPhongMaterial material, inout ReflectedLight reflectedLight ) {\n	float dotNL = saturate( dot( geometryNormal, directLight.direction ) );\n	vec3 irradiance = dotNL * directLight.color;\n	reflectedLight.directDiffuse += irradiance * BRDF_Lambert( material.diffuseColor );\n	reflectedLight.directSpecular += irradiance * BRDF_BlinnPhong( directLight.direction, geometryViewDir, geometryNormal, material.specularColor, material.specularShininess ) * material.specularStrength;\n}\nvoid RE_IndirectDiffuse_BlinnPhong( const in vec3 irradiance, const in vec3 geometryPosition, const in vec3 geometryNormal, const in vec3 geometryViewDir, const in vec3 geometryClearcoatNormal, const in BlinnPhongMaterial material, inout ReflectedLight reflectedLight ) {\n	reflectedLight.indirectDiffuse += irradiance * BRDF_Lambert( material.diffuseColor );\n}\n#define RE_Direct				RE_Direct_BlinnPhong\n#define RE_IndirectDiffuse		RE_IndirectDiffuse_BlinnPhong", lights_physical_fragment: "PhysicalMaterial material;\nmaterial.diffuseColor = diffuseColor.rgb * ( 1.0 - metalnessFactor );\nvec3 dxy = max( abs( dFdx( nonPerturbedNormal ) ), abs( dFdy( nonPerturbedNormal ) ) );\nfloat geometryRoughness = max( max( dxy.x, dxy.y ), dxy.z );\nmaterial.roughness = max( roughnessFactor, 0.0525 );material.roughness += geometryRoughness;\nmaterial.roughness = min( material.roughness, 1.0 );\n#ifdef IOR\n	material.ior = ior;\n	#ifdef USE_SPECULAR\n		float specularIntensityFactor = specularIntensity;\n		vec3 specularColorFactor = specularColor;\n		#ifdef USE_SPECULAR_COLORMAP\n			specularColorFactor *= texture2D( specularColorMap, vSpecularColorMapUv ).rgb;\n		#endif\n		#ifdef USE_SPECULAR_INTENSITYMAP\n			specularIntensityFactor *= texture2D( specularIntensityMap, vSpecularIntensityMapUv ).a;\n		#endif\n		material.specularF90 = mix( specularIntensityFactor, 1.0, metalnessFactor );\n	#else\n		float specularIntensityFactor = 1.0;\n		vec3 specularColorFactor = vec3( 1.0 );\n		material.specularF90 = 1.0;\n	#endif\n	material.specularColor = mix( min( pow2( ( material.ior - 1.0 ) / ( material.ior + 1.0 ) ) * specularColorFactor, vec3( 1.0 ) ) * specularIntensityFactor, diffuseColor.rgb, metalnessFactor );\n#else\n	material.specularColor = mix( vec3( 0.04 ), diffuseColor.rgb, metalnessFactor );\n	material.specularF90 = 1.0;\n#endif\n#ifdef USE_CLEARCOAT\n	material.clearcoat = clearcoat;\n	material.clearcoatRoughness = clearcoatRoughness;\n	material.clearcoatF0 = vec3( 0.04 );\n	material.clearcoatF90 = 1.0;\n	#ifdef USE_CLEARCOATMAP\n		material.clearcoat *= texture2D( clearcoatMap, vClearcoatMapUv ).x;\n	#endif\n	#ifdef USE_CLEARCOAT_ROUGHNESSMAP\n		material.clearcoatRoughness *= texture2D( clearcoatRoughnessMap, vClearcoatRoughnessMapUv ).y;\n	#endif\n	material.clearcoat = saturate( material.clearcoat );	material.clearcoatRoughness = max( material.clearcoatRoughness, 0.0525 );\n	material.clearcoatRoughness += geometryRoughness;\n	material.clearcoatRoughness = min( material.clearcoatRoughness, 1.0 );\n#endif\n#ifdef USE_DISPERSION\n	material.dispersion = dispersion;\n#endif\n#ifdef USE_IRIDESCENCE\n	material.iridescence = iridescence;\n	material.iridescenceIOR = iridescenceIOR;\n	#ifdef USE_IRIDESCENCEMAP\n		material.iridescence *= texture2D( iridescenceMap, vIridescenceMapUv ).r;\n	#endif\n	#ifdef USE_IRIDESCENCE_THICKNESSMAP\n		material.iridescenceThickness = (iridescenceThicknessMaximum - iridescenceThicknessMinimum) * texture2D( iridescenceThicknessMap, vIridescenceThicknessMapUv ).g + iridescenceThicknessMinimum;\n	#else\n		material.iridescenceThickness = iridescenceThicknessMaximum;\n	#endif\n#endif\n#ifdef USE_SHEEN\n	material.sheenColor = sheenColor;\n	#ifdef USE_SHEEN_COLORMAP\n		material.sheenColor *= texture2D( sheenColorMap, vSheenColorMapUv ).rgb;\n	#endif\n	material.sheenRoughness = clamp( sheenRoughness, 0.07, 1.0 );\n	#ifdef USE_SHEEN_ROUGHNESSMAP\n		material.sheenRoughness *= texture2D( sheenRoughnessMap, vSheenRoughnessMapUv ).a;\n	#endif\n#endif\n#ifdef USE_ANISOTROPY\n	#ifdef USE_ANISOTROPYMAP\n		mat2 anisotropyMat = mat2( anisotropyVector.x, anisotropyVector.y, - anisotropyVector.y, anisotropyVector.x );\n		vec3 anisotropyPolar = texture2D( anisotropyMap, vAnisotropyMapUv ).rgb;\n		vec2 anisotropyV = anisotropyMat * normalize( 2.0 * anisotropyPolar.rg - vec2( 1.0 ) ) * anisotropyPolar.b;\n	#else\n		vec2 anisotropyV = anisotropyVector;\n	#endif\n	material.anisotropy = length( anisotropyV );\n	if( material.anisotropy == 0.0 ) {\n		anisotropyV = vec2( 1.0, 0.0 );\n	} else {\n		anisotropyV /= material.anisotropy;\n		material.anisotropy = saturate( material.anisotropy );\n	}\n	material.alphaT = mix( pow2( material.roughness ), 1.0, pow2( material.anisotropy ) );\n	material.anisotropyT = tbn[ 0 ] * anisotropyV.x + tbn[ 1 ] * anisotropyV.y;\n	material.anisotropyB = tbn[ 1 ] * anisotropyV.x - tbn[ 0 ] * anisotropyV.y;\n#endif", lights_physical_pars_fragment: "struct PhysicalMaterial {\n	vec3 diffuseColor;\n	float roughness;\n	vec3 specularColor;\n	float specularF90;\n	float dispersion;\n	#ifdef USE_CLEARCOAT\n		float clearcoat;\n		float clearcoatRoughness;\n		vec3 clearcoatF0;\n		float clearcoatF90;\n	#endif\n	#ifdef USE_IRIDESCENCE\n		float iridescence;\n		float iridescenceIOR;\n		float iridescenceThickness;\n		vec3 iridescenceFresnel;\n		vec3 iridescenceF0;\n	#endif\n	#ifdef USE_SHEEN\n		vec3 sheenColor;\n		float sheenRoughness;\n	#endif\n	#ifdef IOR\n		float ior;\n	#endif\n	#ifdef USE_TRANSMISSION\n		float transmission;\n		float transmissionAlpha;\n		float thickness;\n		float attenuationDistance;\n		vec3 attenuationColor;\n	#endif\n	#ifdef USE_ANISOTROPY\n		float anisotropy;\n		float alphaT;\n		vec3 anisotropyT;\n		vec3 anisotropyB;\n	#endif\n};\nvec3 clearcoatSpecularDirect = vec3( 0.0 );\nvec3 clearcoatSpecularIndirect = vec3( 0.0 );\nvec3 sheenSpecularDirect = vec3( 0.0 );\nvec3 sheenSpecularIndirect = vec3(0.0 );\nvec3 Schlick_to_F0( const in vec3 f, const in float f90, const in float dotVH ) {\n    float x = clamp( 1.0 - dotVH, 0.0, 1.0 );\n    float x2 = x * x;\n    float x5 = clamp( x * x2 * x2, 0.0, 0.9999 );\n    return ( f - vec3( f90 ) * x5 ) / ( 1.0 - x5 );\n}\nfloat V_GGX_SmithCorrelated( const in float alpha, const in float dotNL, const in float dotNV ) {\n	float a2 = pow2( alpha );\n	float gv = dotNL * sqrt( a2 + ( 1.0 - a2 ) * pow2( dotNV ) );\n	float gl = dotNV * sqrt( a2 + ( 1.0 - a2 ) * pow2( dotNL ) );\n	return 0.5 / max( gv + gl, EPSILON );\n}\nfloat D_GGX( const in float alpha, const in float dotNH ) {\n	float a2 = pow2( alpha );\n	float denom = pow2( dotNH ) * ( a2 - 1.0 ) + 1.0;\n	return RECIPROCAL_PI * a2 / pow2( denom );\n}\n#ifdef USE_ANISOTROPY\n	float V_GGX_SmithCorrelated_Anisotropic( const in float alphaT, const in float alphaB, const in float dotTV, const in float dotBV, const in float dotTL, const in float dotBL, const in float dotNV, const in float dotNL ) {\n		float gv = dotNL * length( vec3( alphaT * dotTV, alphaB * dotBV, dotNV ) );\n		float gl = dotNV * length( vec3( alphaT * dotTL, alphaB * dotBL, dotNL ) );\n		float v = 0.5 / ( gv + gl );\n		return saturate(v);\n	}\n	float D_GGX_Anisotropic( const in float alphaT, const in float alphaB, const in float dotNH, const in float dotTH, const in float dotBH ) {\n		float a2 = alphaT * alphaB;\n		highp vec3 v = vec3( alphaB * dotTH, alphaT * dotBH, a2 * dotNH );\n		highp float v2 = dot( v, v );\n		float w2 = a2 / v2;\n		return RECIPROCAL_PI * a2 * pow2 ( w2 );\n	}\n#endif\n#ifdef USE_CLEARCOAT\n	vec3 BRDF_GGX_Clearcoat( const in vec3 lightDir, const in vec3 viewDir, const in vec3 normal, const in PhysicalMaterial material) {\n		vec3 f0 = material.clearcoatF0;\n		float f90 = material.clearcoatF90;\n		float roughness = material.clearcoatRoughness;\n		float alpha = pow2( roughness );\n		vec3 halfDir = normalize( lightDir + viewDir );\n		float dotNL = saturate( dot( normal, lightDir ) );\n		float dotNV = saturate( dot( normal, viewDir ) );\n		float dotNH = saturate( dot( normal, halfDir ) );\n		float dotVH = saturate( dot( viewDir, halfDir ) );\n		vec3 F = F_Schlick( f0, f90, dotVH );\n		float V = V_GGX_SmithCorrelated( alpha, dotNL, dotNV );\n		float D = D_GGX( alpha, dotNH );\n		return F * ( V * D );\n	}\n#endif\nvec3 BRDF_GGX( const in vec3 lightDir, const in vec3 viewDir, const in vec3 normal, const in PhysicalMaterial material ) {\n	vec3 f0 = material.specularColor;\n	float f90 = material.specularF90;\n	float roughness = material.roughness;\n	float alpha = pow2( roughness );\n	vec3 halfDir = normalize( lightDir + viewDir );\n	float dotNL = saturate( dot( normal, lightDir ) );\n	float dotNV = saturate( dot( normal, viewDir ) );\n	float dotNH = saturate( dot( normal, halfDir ) );\n	float dotVH = saturate( dot( viewDir, halfDir ) );\n	vec3 F = F_Schlick( f0, f90, dotVH );\n	#ifdef USE_IRIDESCENCE\n		F = mix( F, material.iridescenceFresnel, material.iridescence );\n	#endif\n	#ifdef USE_ANISOTROPY\n		float dotTL = dot( material.anisotropyT, lightDir );\n		float dotTV = dot( material.anisotropyT, viewDir );\n		float dotTH = dot( material.anisotropyT, halfDir );\n		float dotBL = dot( material.anisotropyB, lightDir );\n		float dotBV = dot( material.anisotropyB, viewDir );\n		float dotBH = dot( material.anisotropyB, halfDir );\n		float V = V_GGX_SmithCorrelated_Anisotropic( material.alphaT, alpha, dotTV, dotBV, dotTL, dotBL, dotNV, dotNL );\n		float D = D_GGX_Anisotropic( material.alphaT, alpha, dotNH, dotTH, dotBH );\n	#else\n		float V = V_GGX_SmithCorrelated( alpha, dotNL, dotNV );\n		float D = D_GGX( alpha, dotNH );\n	#endif\n	return F * ( V * D );\n}\nvec2 LTC_Uv( const in vec3 N, const in vec3 V, const in float roughness ) {\n	const float LUT_SIZE = 64.0;\n	const float LUT_SCALE = ( LUT_SIZE - 1.0 ) / LUT_SIZE;\n	const float LUT_BIAS = 0.5 / LUT_SIZE;\n	float dotNV = saturate( dot( N, V ) );\n	vec2 uv = vec2( roughness, sqrt( 1.0 - dotNV ) );\n	uv = uv * LUT_SCALE + LUT_BIAS;\n	return uv;\n}\nfloat LTC_ClippedSphereFormFactor( const in vec3 f ) {\n	float l = length( f );\n	return max( ( l * l + f.z ) / ( l + 1.0 ), 0.0 );\n}\nvec3 LTC_EdgeVectorFormFactor( const in vec3 v1, const in vec3 v2 ) {\n	float x = dot( v1, v2 );\n	float y = abs( x );\n	float a = 0.8543985 + ( 0.4965155 + 0.0145206 * y ) * y;\n	float b = 3.4175940 + ( 4.1616724 + y ) * y;\n	float v = a / b;\n	float theta_sintheta = ( x > 0.0 ) ? v : 0.5 * inversesqrt( max( 1.0 - x * x, 1e-7 ) ) - v;\n	return cross( v1, v2 ) * theta_sintheta;\n}\nvec3 LTC_Evaluate( const in vec3 N, const in vec3 V, const in vec3 P, const in mat3 mInv, const in vec3 rectCoords[ 4 ] ) {\n	vec3 v1 = rectCoords[ 1 ] - rectCoords[ 0 ];\n	vec3 v2 = rectCoords[ 3 ] - rectCoords[ 0 ];\n	vec3 lightNormal = cross( v1, v2 );\n	if( dot( lightNormal, P - rectCoords[ 0 ] ) < 0.0 ) return vec3( 0.0 );\n	vec3 T1, T2;\n	T1 = normalize( V - N * dot( V, N ) );\n	T2 = - cross( N, T1 );\n	mat3 mat = mInv * transposeMat3( mat3( T1, T2, N ) );\n	vec3 coords[ 4 ];\n	coords[ 0 ] = mat * ( rectCoords[ 0 ] - P );\n	coords[ 1 ] = mat * ( rectCoords[ 1 ] - P );\n	coords[ 2 ] = mat * ( rectCoords[ 2 ] - P );\n	coords[ 3 ] = mat * ( rectCoords[ 3 ] - P );\n	coords[ 0 ] = normalize( coords[ 0 ] );\n	coords[ 1 ] = normalize( coords[ 1 ] );\n	coords[ 2 ] = normalize( coords[ 2 ] );\n	coords[ 3 ] = normalize( coords[ 3 ] );\n	vec3 vectorFormFactor = vec3( 0.0 );\n	vectorFormFactor += LTC_EdgeVectorFormFactor( coords[ 0 ], coords[ 1 ] );\n	vectorFormFactor += LTC_EdgeVectorFormFactor( coords[ 1 ], coords[ 2 ] );\n	vectorFormFactor += LTC_EdgeVectorFormFactor( coords[ 2 ], coords[ 3 ] );\n	vectorFormFactor += LTC_EdgeVectorFormFactor( coords[ 3 ], coords[ 0 ] );\n	float result = LTC_ClippedSphereFormFactor( vectorFormFactor );\n	return vec3( result );\n}\n#if defined( USE_SHEEN )\nfloat D_Charlie( float roughness, float dotNH ) {\n	float alpha = pow2( roughness );\n	float invAlpha = 1.0 / alpha;\n	float cos2h = dotNH * dotNH;\n	float sin2h = max( 1.0 - cos2h, 0.0078125 );\n	return ( 2.0 + invAlpha ) * pow( sin2h, invAlpha * 0.5 ) / ( 2.0 * PI );\n}\nfloat V_Neubelt( float dotNV, float dotNL ) {\n	return saturate( 1.0 / ( 4.0 * ( dotNL + dotNV - dotNL * dotNV ) ) );\n}\nvec3 BRDF_Sheen( const in vec3 lightDir, const in vec3 viewDir, const in vec3 normal, vec3 sheenColor, const in float sheenRoughness ) {\n	vec3 halfDir = normalize( lightDir + viewDir );\n	float dotNL = saturate( dot( normal, lightDir ) );\n	float dotNV = saturate( dot( normal, viewDir ) );\n	float dotNH = saturate( dot( normal, halfDir ) );\n	float D = D_Charlie( sheenRoughness, dotNH );\n	float V = V_Neubelt( dotNV, dotNL );\n	return sheenColor * ( D * V );\n}\n#endif\nfloat IBLSheenBRDF( const in vec3 normal, const in vec3 viewDir, const in float roughness ) {\n	float dotNV = saturate( dot( normal, viewDir ) );\n	float r2 = roughness * roughness;\n	float a = roughness < 0.25 ? -339.2 * r2 + 161.4 * roughness - 25.9 : -8.48 * r2 + 14.3 * roughness - 9.95;\n	float b = roughness < 0.25 ? 44.0 * r2 - 23.7 * roughness + 3.26 : 1.97 * r2 - 3.27 * roughness + 0.72;\n	float DG = exp( a * dotNV + b ) + ( roughness < 0.25 ? 0.0 : 0.1 * ( roughness - 0.25 ) );\n	return saturate( DG * RECIPROCAL_PI );\n}\nvec2 DFGApprox( const in vec3 normal, const in vec3 viewDir, const in float roughness ) {\n	float dotNV = saturate( dot( normal, viewDir ) );\n	const vec4 c0 = vec4( - 1, - 0.0275, - 0.572, 0.022 );\n	const vec4 c1 = vec4( 1, 0.0425, 1.04, - 0.04 );\n	vec4 r = roughness * c0 + c1;\n	float a004 = min( r.x * r.x, exp2( - 9.28 * dotNV ) ) * r.x + r.y;\n	vec2 fab = vec2( - 1.04, 1.04 ) * a004 + r.zw;\n	return fab;\n}\nvec3 EnvironmentBRDF( const in vec3 normal, const in vec3 viewDir, const in vec3 specularColor, const in float specularF90, const in float roughness ) {\n	vec2 fab = DFGApprox( normal, viewDir, roughness );\n	return specularColor * fab.x + specularF90 * fab.y;\n}\n#ifdef USE_IRIDESCENCE\nvoid computeMultiscatteringIridescence( const in vec3 normal, const in vec3 viewDir, const in vec3 specularColor, const in float specularF90, const in float iridescence, const in vec3 iridescenceF0, const in float roughness, inout vec3 singleScatter, inout vec3 multiScatter ) {\n#else\nvoid computeMultiscattering( const in vec3 normal, const in vec3 viewDir, const in vec3 specularColor, const in float specularF90, const in float roughness, inout vec3 singleScatter, inout vec3 multiScatter ) {\n#endif\n	vec2 fab = DFGApprox( normal, viewDir, roughness );\n	#ifdef USE_IRIDESCENCE\n		vec3 Fr = mix( specularColor, iridescenceF0, iridescence );\n	#else\n		vec3 Fr = specularColor;\n	#endif\n	vec3 FssEss = Fr * fab.x + specularF90 * fab.y;\n	float Ess = fab.x + fab.y;\n	float Ems = 1.0 - Ess;\n	vec3 Favg = Fr + ( 1.0 - Fr ) * 0.047619;	vec3 Fms = FssEss * Favg / ( 1.0 - Ems * Favg );\n	singleScatter += FssEss;\n	multiScatter += Fms * Ems;\n}\n#if NUM_RECT_AREA_LIGHTS > 0\n	void RE_Direct_RectArea_Physical( const in RectAreaLight rectAreaLight, const in vec3 geometryPosition, const in vec3 geometryNormal, const in vec3 geometryViewDir, const in vec3 geometryClearcoatNormal, const in PhysicalMaterial material, inout ReflectedLight reflectedLight ) {\n		vec3 normal = geometryNormal;\n		vec3 viewDir = geometryViewDir;\n		vec3 position = geometryPosition;\n		vec3 lightPos = rectAreaLight.position;\n		vec3 halfWidth = rectAreaLight.halfWidth;\n		vec3 halfHeight = rectAreaLight.halfHeight;\n		vec3 lightColor = rectAreaLight.color;\n		float roughness = material.roughness;\n		vec3 rectCoords[ 4 ];\n		rectCoords[ 0 ] = lightPos + halfWidth - halfHeight;		rectCoords[ 1 ] = lightPos - halfWidth - halfHeight;\n		rectCoords[ 2 ] = lightPos - halfWidth + halfHeight;\n		rectCoords[ 3 ] = lightPos + halfWidth + halfHeight;\n		vec2 uv = LTC_Uv( normal, viewDir, roughness );\n		vec4 t1 = texture2D( ltc_1, uv );\n		vec4 t2 = texture2D( ltc_2, uv );\n		mat3 mInv = mat3(\n			vec3( t1.x, 0, t1.y ),\n			vec3(    0, 1,    0 ),\n			vec3( t1.z, 0, t1.w )\n		);\n		vec3 fresnel = ( material.specularColor * t2.x + ( vec3( 1.0 ) - material.specularColor ) * t2.y );\n		reflectedLight.directSpecular += lightColor * fresnel * LTC_Evaluate( normal, viewDir, position, mInv, rectCoords );\n		reflectedLight.directDiffuse += lightColor * material.diffuseColor * LTC_Evaluate( normal, viewDir, position, mat3( 1.0 ), rectCoords );\n	}\n#endif\nvoid RE_Direct_Physical( const in IncidentLight directLight, const in vec3 geometryPosition, const in vec3 geometryNormal, const in vec3 geometryViewDir, const in vec3 geometryClearcoatNormal, const in PhysicalMaterial material, inout ReflectedLight reflectedLight ) {\n	float dotNL = saturate( dot( geometryNormal, directLight.direction ) );\n	vec3 irradiance = dotNL * directLight.color;\n	#ifdef USE_CLEARCOAT\n		float dotNLcc = saturate( dot( geometryClearcoatNormal, directLight.direction ) );\n		vec3 ccIrradiance = dotNLcc * directLight.color;\n		clearcoatSpecularDirect += ccIrradiance * BRDF_GGX_Clearcoat( directLight.direction, geometryViewDir, geometryClearcoatNormal, material );\n	#endif\n	#ifdef USE_SHEEN\n		sheenSpecularDirect += irradiance * BRDF_Sheen( directLight.direction, geometryViewDir, geometryNormal, material.sheenColor, material.sheenRoughness );\n	#endif\n	reflectedLight.directSpecular += irradiance * BRDF_GGX( directLight.direction, geometryViewDir, geometryNormal, material );\n	reflectedLight.directDiffuse += irradiance * BRDF_Lambert( material.diffuseColor );\n}\nvoid RE_IndirectDiffuse_Physical( const in vec3 irradiance, const in vec3 geometryPosition, const in vec3 geometryNormal, const in vec3 geometryViewDir, const in vec3 geometryClearcoatNormal, const in PhysicalMaterial material, inout ReflectedLight reflectedLight ) {\n	reflectedLight.indirectDiffuse += irradiance * BRDF_Lambert( material.diffuseColor );\n}\nvoid RE_IndirectSpecular_Physical( const in vec3 radiance, const in vec3 irradiance, const in vec3 clearcoatRadiance, const in vec3 geometryPosition, const in vec3 geometryNormal, const in vec3 geometryViewDir, const in vec3 geometryClearcoatNormal, const in PhysicalMaterial material, inout ReflectedLight reflectedLight) {\n	#ifdef USE_CLEARCOAT\n		clearcoatSpecularIndirect += clearcoatRadiance * EnvironmentBRDF( geometryClearcoatNormal, geometryViewDir, material.clearcoatF0, material.clearcoatF90, material.clearcoatRoughness );\n	#endif\n	#ifdef USE_SHEEN\n		sheenSpecularIndirect += irradiance * material.sheenColor * IBLSheenBRDF( geometryNormal, geometryViewDir, material.sheenRoughness );\n	#endif\n	vec3 singleScattering = vec3( 0.0 );\n	vec3 multiScattering = vec3( 0.0 );\n	vec3 cosineWeightedIrradiance = irradiance * RECIPROCAL_PI;\n	#ifdef USE_IRIDESCENCE\n		computeMultiscatteringIridescence( geometryNormal, geometryViewDir, material.specularColor, material.specularF90, material.iridescence, material.iridescenceFresnel, material.roughness, singleScattering, multiScattering );\n	#else\n		computeMultiscattering( geometryNormal, geometryViewDir, material.specularColor, material.specularF90, material.roughness, singleScattering, multiScattering );\n	#endif\n	vec3 totalScattering = singleScattering + multiScattering;\n	vec3 diffuse = material.diffuseColor * ( 1.0 - max( max( totalScattering.r, totalScattering.g ), totalScattering.b ) );\n	reflectedLight.indirectSpecular += radiance * singleScattering;\n	reflectedLight.indirectSpecular += multiScattering * cosineWeightedIrradiance;\n	reflectedLight.indirectDiffuse += diffuse * cosineWeightedIrradiance;\n}\n#define RE_Direct				RE_Direct_Physical\n#define RE_Direct_RectArea		RE_Direct_RectArea_Physical\n#define RE_IndirectDiffuse		RE_IndirectDiffuse_Physical\n#define RE_IndirectSpecular		RE_IndirectSpecular_Physical\nfloat computeSpecularOcclusion( const in float dotNV, const in float ambientOcclusion, const in float roughness ) {\n	return saturate( pow( dotNV + ambientOcclusion, exp2( - 16.0 * roughness - 1.0 ) ) - 1.0 + ambientOcclusion );\n}", lights_fragment_begin: "\nvec3 geometryPosition = - vViewPosition;\nvec3 geometryNormal = normal;\nvec3 geometryViewDir = ( isOrthographic ) ? vec3( 0, 0, 1 ) : normalize( vViewPosition );\nvec3 geometryClearcoatNormal = vec3( 0.0 );\n#ifdef USE_CLEARCOAT\n	geometryClearcoatNormal = clearcoatNormal;\n#endif\n#ifdef USE_IRIDESCENCE\n	float dotNVi = saturate( dot( normal, geometryViewDir ) );\n	if ( material.iridescenceThickness == 0.0 ) {\n		material.iridescence = 0.0;\n	} else {\n		material.iridescence = saturate( material.iridescence );\n	}\n	if ( material.iridescence > 0.0 ) {\n		material.iridescenceFresnel = evalIridescence( 1.0, material.iridescenceIOR, dotNVi, material.iridescenceThickness, material.specularColor );\n		material.iridescenceF0 = Schlick_to_F0( material.iridescenceFresnel, 1.0, dotNVi );\n	}\n#endif\nIncidentLight directLight;\n#if ( NUM_POINT_LIGHTS > 0 ) && defined( RE_Direct )\n	PointLight pointLight;\n	#if defined( USE_SHADOWMAP ) && NUM_POINT_LIGHT_SHADOWS > 0\n	PointLightShadow pointLightShadow;\n	#endif\n	#pragma unroll_loop_start\n	for ( int i = 0; i < NUM_POINT_LIGHTS; i ++ ) {\n		pointLight = pointLights[ i ];\n		getPointLightInfo( pointLight, geometryPosition, directLight );\n		#if defined( USE_SHADOWMAP ) && ( UNROLLED_LOOP_INDEX < NUM_POINT_LIGHT_SHADOWS )\n		pointLightShadow = pointLightShadows[ i ];\n		directLight.color *= ( directLight.visible && receiveShadow ) ? getPointShadow( pointShadowMap[ i ], pointLightShadow.shadowMapSize, pointLightShadow.shadowIntensity, pointLightShadow.shadowBias, pointLightShadow.shadowRadius, vPointShadowCoord[ i ], pointLightShadow.shadowCameraNear, pointLightShadow.shadowCameraFar ) : 1.0;\n		#endif\n		RE_Direct( directLight, geometryPosition, geometryNormal, geometryViewDir, geometryClearcoatNormal, material, reflectedLight );\n	}\n	#pragma unroll_loop_end\n#endif\n#if ( NUM_SPOT_LIGHTS > 0 ) && defined( RE_Direct )\n	SpotLight spotLight;\n	vec4 spotColor;\n	vec3 spotLightCoord;\n	bool inSpotLightMap;\n	#if defined( USE_SHADOWMAP ) && NUM_SPOT_LIGHT_SHADOWS > 0\n	SpotLightShadow spotLightShadow;\n	#endif\n	#pragma unroll_loop_start\n	for ( int i = 0; i < NUM_SPOT_LIGHTS; i ++ ) {\n		spotLight = spotLights[ i ];\n		getSpotLightInfo( spotLight, geometryPosition, directLight );\n		#if ( UNROLLED_LOOP_INDEX < NUM_SPOT_LIGHT_SHADOWS_WITH_MAPS )\n		#define SPOT_LIGHT_MAP_INDEX UNROLLED_LOOP_INDEX\n		#elif ( UNROLLED_LOOP_INDEX < NUM_SPOT_LIGHT_SHADOWS )\n		#define SPOT_LIGHT_MAP_INDEX NUM_SPOT_LIGHT_MAPS\n		#else\n		#define SPOT_LIGHT_MAP_INDEX ( UNROLLED_LOOP_INDEX - NUM_SPOT_LIGHT_SHADOWS + NUM_SPOT_LIGHT_SHADOWS_WITH_MAPS )\n		#endif\n		#if ( SPOT_LIGHT_MAP_INDEX < NUM_SPOT_LIGHT_MAPS )\n			spotLightCoord = vSpotLightCoord[ i ].xyz / vSpotLightCoord[ i ].w;\n			inSpotLightMap = all( lessThan( abs( spotLightCoord * 2. - 1. ), vec3( 1.0 ) ) );\n			spotColor = texture2D( spotLightMap[ SPOT_LIGHT_MAP_INDEX ], spotLightCoord.xy );\n			directLight.color = inSpotLightMap ? directLight.color * spotColor.rgb : directLight.color;\n		#endif\n		#undef SPOT_LIGHT_MAP_INDEX\n		#if defined( USE_SHADOWMAP ) && ( UNROLLED_LOOP_INDEX < NUM_SPOT_LIGHT_SHADOWS )\n		spotLightShadow = spotLightShadows[ i ];\n		directLight.color *= ( directLight.visible && receiveShadow ) ? getShadow( spotShadowMap[ i ], spotLightShadow.shadowMapSize, spotLightShadow.shadowIntensity, spotLightShadow.shadowBias, spotLightShadow.shadowRadius, vSpotLightCoord[ i ] ) : 1.0;\n		#endif\n		RE_Direct( directLight, geometryPosition, geometryNormal, geometryViewDir, geometryClearcoatNormal, material, reflectedLight );\n	}\n	#pragma unroll_loop_end\n#endif\n#if ( NUM_DIR_LIGHTS > 0 ) && defined( RE_Direct )\n	DirectionalLight directionalLight;\n	#if defined( USE_SHADOWMAP ) && NUM_DIR_LIGHT_SHADOWS > 0\n	DirectionalLightShadow directionalLightShadow;\n	#endif\n	#pragma unroll_loop_start\n	for ( int i = 0; i < NUM_DIR_LIGHTS; i ++ ) {\n		directionalLight = directionalLights[ i ];\n		getDirectionalLightInfo( directionalLight, directLight );\n		#if defined( USE_SHADOWMAP ) && ( UNROLLED_LOOP_INDEX < NUM_DIR_LIGHT_SHADOWS )\n		directionalLightShadow = directionalLightShadows[ i ];\n		directLight.color *= ( directLight.visible && receiveShadow ) ? getShadow( directionalShadowMap[ i ], directionalLightShadow.shadowMapSize, directionalLightShadow.shadowIntensity, directionalLightShadow.shadowBias, directionalLightShadow.shadowRadius, vDirectionalShadowCoord[ i ] ) : 1.0;\n		#endif\n		RE_Direct( directLight, geometryPosition, geometryNormal, geometryViewDir, geometryClearcoatNormal, material, reflectedLight );\n	}\n	#pragma unroll_loop_end\n#endif\n#if ( NUM_RECT_AREA_LIGHTS > 0 ) && defined( RE_Direct_RectArea )\n	RectAreaLight rectAreaLight;\n	#pragma unroll_loop_start\n	for ( int i = 0; i < NUM_RECT_AREA_LIGHTS; i ++ ) {\n		rectAreaLight = rectAreaLights[ i ];\n		RE_Direct_RectArea( rectAreaLight, geometryPosition, geometryNormal, geometryViewDir, geometryClearcoatNormal, material, reflectedLight );\n	}\n	#pragma unroll_loop_end\n#endif\n#if defined( RE_IndirectDiffuse )\n	vec3 iblIrradiance = vec3( 0.0 );\n	vec3 irradiance = getAmbientLightIrradiance( ambientLightColor );\n	#if defined( USE_LIGHT_PROBES )\n		irradiance += getLightProbeIrradiance( lightProbe, geometryNormal );\n	#endif\n	#if ( NUM_HEMI_LIGHTS > 0 )\n		#pragma unroll_loop_start\n		for ( int i = 0; i < NUM_HEMI_LIGHTS; i ++ ) {\n			irradiance += getHemisphereLightIrradiance( hemisphereLights[ i ], geometryNormal );\n		}\n		#pragma unroll_loop_end\n	#endif\n#endif\n#if defined( RE_IndirectSpecular )\n	vec3 radiance = vec3( 0.0 );\n	vec3 clearcoatRadiance = vec3( 0.0 );\n#endif", lights_fragment_maps: "#if defined( RE_IndirectDiffuse )\n	#ifdef USE_LIGHTMAP\n		vec4 lightMapTexel = texture2D( lightMap, vLightMapUv );\n		vec3 lightMapIrradiance = lightMapTexel.rgb * lightMapIntensity;\n		irradiance += lightMapIrradiance;\n	#endif\n	#if defined( USE_ENVMAP ) && defined( STANDARD ) && defined( ENVMAP_TYPE_CUBE_UV )\n		iblIrradiance += getIBLIrradiance( geometryNormal );\n	#endif\n#endif\n#if defined( USE_ENVMAP ) && defined( RE_IndirectSpecular )\n	#ifdef USE_ANISOTROPY\n		radiance += getIBLAnisotropyRadiance( geometryViewDir, geometryNormal, material.roughness, material.anisotropyB, material.anisotropy );\n	#else\n		radiance += getIBLRadiance( geometryViewDir, geometryNormal, material.roughness );\n	#endif\n	#ifdef USE_CLEARCOAT\n		clearcoatRadiance += getIBLRadiance( geometryViewDir, geometryClearcoatNormal, material.clearcoatRoughness );\n	#endif\n#endif", lights_fragment_end: "#if defined( RE_IndirectDiffuse )\n	RE_IndirectDiffuse( irradiance, geometryPosition, geometryNormal, geometryViewDir, geometryClearcoatNormal, material, reflectedLight );\n#endif\n#if defined( RE_IndirectSpecular )\n	RE_IndirectSpecular( radiance, iblIrradiance, clearcoatRadiance, geometryPosition, geometryNormal, geometryViewDir, geometryClearcoatNormal, material, reflectedLight );\n#endif", logdepthbuf_fragment: "#if defined( USE_LOGDEPTHBUF )\n	gl_FragDepth = vIsPerspective == 0.0 ? gl_FragCoord.z : log2( vFragDepth ) * logDepthBufFC * 0.5;\n#endif", logdepthbuf_pars_fragment: "#if defined( USE_LOGDEPTHBUF )\n	uniform float logDepthBufFC;\n	varying float vFragDepth;\n	varying float vIsPerspective;\n#endif", logdepthbuf_pars_vertex: "#ifdef USE_LOGDEPTHBUF\n	varying float vFragDepth;\n	varying float vIsPerspective;\n#endif", logdepthbuf_vertex: "#ifdef USE_LOGDEPTHBUF\n	vFragDepth = 1.0 + gl_Position.w;\n	vIsPerspective = float( isPerspectiveMatrix( projectionMatrix ) );\n#endif", map_fragment: "#ifdef USE_MAP\n	vec4 sampledDiffuseColor = texture2D( map, vMapUv );\n	#ifdef DECODE_VIDEO_TEXTURE\n		sampledDiffuseColor = sRGBTransferEOTF( sampledDiffuseColor );\n	#endif\n	diffuseColor *= sampledDiffuseColor;\n#endif", map_pars_fragment: "#ifdef USE_MAP\n	uniform sampler2D map;\n#endif", map_particle_fragment: "#if defined( USE_MAP ) || defined( USE_ALPHAMAP )\n	#if defined( USE_POINTS_UV )\n		vec2 uv = vUv;\n	#else\n		vec2 uv = ( uvTransform * vec3( gl_PointCoord.x, 1.0 - gl_PointCoord.y, 1 ) ).xy;\n	#endif\n#endif\n#ifdef USE_MAP\n	diffuseColor *= texture2D( map, uv );\n#endif\n#ifdef USE_ALPHAMAP\n	diffuseColor.a *= texture2D( alphaMap, uv ).g;\n#endif", map_particle_pars_fragment: "#if defined( USE_POINTS_UV )\n	varying vec2 vUv;\n#else\n	#if defined( USE_MAP ) || defined( USE_ALPHAMAP )\n		uniform mat3 uvTransform;\n	#endif\n#endif\n#ifdef USE_MAP\n	uniform sampler2D map;\n#endif\n#ifdef USE_ALPHAMAP\n	uniform sampler2D alphaMap;\n#endif", metalnessmap_fragment: "float metalnessFactor = metalness;\n#ifdef USE_METALNESSMAP\n	vec4 texelMetalness = texture2D( metalnessMap, vMetalnessMapUv );\n	metalnessFactor *= texelMetalness.b;\n#endif", metalnessmap_pars_fragment: "#ifdef USE_METALNESSMAP\n	uniform sampler2D metalnessMap;\n#endif", morphinstance_vertex: "#ifdef USE_INSTANCING_MORPH\n	float morphTargetInfluences[ MORPHTARGETS_COUNT ];\n	float morphTargetBaseInfluence = texelFetch( morphTexture, ivec2( 0, gl_InstanceID ), 0 ).r;\n	for ( int i = 0; i < MORPHTARGETS_COUNT; i ++ ) {\n		morphTargetInfluences[i] =  texelFetch( morphTexture, ivec2( i + 1, gl_InstanceID ), 0 ).r;\n	}\n#endif", morphcolor_vertex: "#if defined( USE_MORPHCOLORS )\n	vColor *= morphTargetBaseInfluence;\n	for ( int i = 0; i < MORPHTARGETS_COUNT; i ++ ) {\n		#if defined( USE_COLOR_ALPHA )\n			if ( morphTargetInfluences[ i ] != 0.0 ) vColor += getMorph( gl_VertexID, i, 2 ) * morphTargetInfluences[ i ];\n		#elif defined( USE_COLOR )\n			if ( morphTargetInfluences[ i ] != 0.0 ) vColor += getMorph( gl_VertexID, i, 2 ).rgb * morphTargetInfluences[ i ];\n		#endif\n	}\n#endif", morphnormal_vertex: "#ifdef USE_MORPHNORMALS\n	objectNormal *= morphTargetBaseInfluence;\n	for ( int i = 0; i < MORPHTARGETS_COUNT; i ++ ) {\n		if ( morphTargetInfluences[ i ] != 0.0 ) objectNormal += getMorph( gl_VertexID, i, 1 ).xyz * morphTargetInfluences[ i ];\n	}\n#endif", morphtarget_pars_vertex: "#ifdef USE_MORPHTARGETS\n	#ifndef USE_INSTANCING_MORPH\n		uniform float morphTargetBaseInfluence;\n		uniform float morphTargetInfluences[ MORPHTARGETS_COUNT ];\n	#endif\n	uniform sampler2DArray morphTargetsTexture;\n	uniform ivec2 morphTargetsTextureSize;\n	vec4 getMorph( const in int vertexIndex, const in int morphTargetIndex, const in int offset ) {\n		int texelIndex = vertexIndex * MORPHTARGETS_TEXTURE_STRIDE + offset;\n		int y = texelIndex / morphTargetsTextureSize.x;\n		int x = texelIndex - y * morphTargetsTextureSize.x;\n		ivec3 morphUV = ivec3( x, y, morphTargetIndex );\n		return texelFetch( morphTargetsTexture, morphUV, 0 );\n	}\n#endif", morphtarget_vertex: "#ifdef USE_MORPHTARGETS\n	transformed *= morphTargetBaseInfluence;\n	for ( int i = 0; i < MORPHTARGETS_COUNT; i ++ ) {\n		if ( morphTargetInfluences[ i ] != 0.0 ) transformed += getMorph( gl_VertexID, i, 0 ).xyz * morphTargetInfluences[ i ];\n	}\n#endif", normal_fragment_begin: "float faceDirection = gl_FrontFacing ? 1.0 : - 1.0;\n#ifdef FLAT_SHADED\n	vec3 fdx = dFdx( vViewPosition );\n	vec3 fdy = dFdy( vViewPosition );\n	vec3 normal = normalize( cross( fdx, fdy ) );\n#else\n	vec3 normal = normalize( vNormal );\n	#ifdef DOUBLE_SIDED\n		normal *= faceDirection;\n	#endif\n#endif\n#if defined( USE_NORMALMAP_TANGENTSPACE ) || defined( USE_CLEARCOAT_NORMALMAP ) || defined( USE_ANISOTROPY )\n	#ifdef USE_TANGENT\n		mat3 tbn = mat3( normalize( vTangent ), normalize( vBitangent ), normal );\n	#else\n		mat3 tbn = getTangentFrame( - vViewPosition, normal,\n		#if defined( USE_NORMALMAP )\n			vNormalMapUv\n		#elif defined( USE_CLEARCOAT_NORMALMAP )\n			vClearcoatNormalMapUv\n		#else\n			vUv\n		#endif\n		);\n	#endif\n	#if defined( DOUBLE_SIDED ) && ! defined( FLAT_SHADED )\n		tbn[0] *= faceDirection;\n		tbn[1] *= faceDirection;\n	#endif\n#endif\n#ifdef USE_CLEARCOAT_NORMALMAP\n	#ifdef USE_TANGENT\n		mat3 tbn2 = mat3( normalize( vTangent ), normalize( vBitangent ), normal );\n	#else\n		mat3 tbn2 = getTangentFrame( - vViewPosition, normal, vClearcoatNormalMapUv );\n	#endif\n	#if defined( DOUBLE_SIDED ) && ! defined( FLAT_SHADED )\n		tbn2[0] *= faceDirection;\n		tbn2[1] *= faceDirection;\n	#endif\n#endif\nvec3 nonPerturbedNormal = normal;", normal_fragment_maps: "#ifdef USE_NORMALMAP_OBJECTSPACE\n	normal = texture2D( normalMap, vNormalMapUv ).xyz * 2.0 - 1.0;\n	#ifdef FLIP_SIDED\n		normal = - normal;\n	#endif\n	#ifdef DOUBLE_SIDED\n		normal = normal * faceDirection;\n	#endif\n	normal = normalize( normalMatrix * normal );\n#elif defined( USE_NORMALMAP_TANGENTSPACE )\n	vec3 mapN = texture2D( normalMap, vNormalMapUv ).xyz * 2.0 - 1.0;\n	mapN.xy *= normalScale;\n	normal = normalize( tbn * mapN );\n#elif defined( USE_BUMPMAP )\n	normal = perturbNormalArb( - vViewPosition, normal, dHdxy_fwd(), faceDirection );\n#endif", normal_pars_fragment: "#ifndef FLAT_SHADED\n	varying vec3 vNormal;\n	#ifdef USE_TANGENT\n		varying vec3 vTangent;\n		varying vec3 vBitangent;\n	#endif\n#endif", normal_pars_vertex: "#ifndef FLAT_SHADED\n	varying vec3 vNormal;\n	#ifdef USE_TANGENT\n		varying vec3 vTangent;\n		varying vec3 vBitangent;\n	#endif\n#endif", normal_vertex: "#ifndef FLAT_SHADED\n	vNormal = normalize( transformedNormal );\n	#ifdef USE_TANGENT\n		vTangent = normalize( transformedTangent );\n		vBitangent = normalize( cross( vNormal, vTangent ) * tangent.w );\n	#endif\n#endif", normalmap_pars_fragment: "#ifdef USE_NORMALMAP\n	uniform sampler2D normalMap;\n	uniform vec2 normalScale;\n#endif\n#ifdef USE_NORMALMAP_OBJECTSPACE\n	uniform mat3 normalMatrix;\n#endif\n#if ! defined ( USE_TANGENT ) && ( defined ( USE_NORMALMAP_TANGENTSPACE ) || defined ( USE_CLEARCOAT_NORMALMAP ) || defined( USE_ANISOTROPY ) )\n	mat3 getTangentFrame( vec3 eye_pos, vec3 surf_norm, vec2 uv ) {\n		vec3 q0 = dFdx( eye_pos.xyz );\n		vec3 q1 = dFdy( eye_pos.xyz );\n		vec2 st0 = dFdx( uv.st );\n		vec2 st1 = dFdy( uv.st );\n		vec3 N = surf_norm;\n		vec3 q1perp = cross( q1, N );\n		vec3 q0perp = cross( N, q0 );\n		vec3 T = q1perp * st0.x + q0perp * st1.x;\n		vec3 B = q1perp * st0.y + q0perp * st1.y;\n		float det = max( dot( T, T ), dot( B, B ) );\n		float scale = ( det == 0.0 ) ? 0.0 : inversesqrt( det );\n		return mat3( T * scale, B * scale, N );\n	}\n#endif", clearcoat_normal_fragment_begin: "#ifdef USE_CLEARCOAT\n	vec3 clearcoatNormal = nonPerturbedNormal;\n#endif", clearcoat_normal_fragment_maps: "#ifdef USE_CLEARCOAT_NORMALMAP\n	vec3 clearcoatMapN = texture2D( clearcoatNormalMap, vClearcoatNormalMapUv ).xyz * 2.0 - 1.0;\n	clearcoatMapN.xy *= clearcoatNormalScale;\n	clearcoatNormal = normalize( tbn2 * clearcoatMapN );\n#endif", clearcoat_pars_fragment: "#ifdef USE_CLEARCOATMAP\n	uniform sampler2D clearcoatMap;\n#endif\n#ifdef USE_CLEARCOAT_NORMALMAP\n	uniform sampler2D clearcoatNormalMap;\n	uniform vec2 clearcoatNormalScale;\n#endif\n#ifdef USE_CLEARCOAT_ROUGHNESSMAP\n	uniform sampler2D clearcoatRoughnessMap;\n#endif", iridescence_pars_fragment: "#ifdef USE_IRIDESCENCEMAP\n	uniform sampler2D iridescenceMap;\n#endif\n#ifdef USE_IRIDESCENCE_THICKNESSMAP\n	uniform sampler2D iridescenceThicknessMap;\n#endif", opaque_fragment: "#ifdef OPAQUE\ndiffuseColor.a = 1.0;\n#endif\n#ifdef USE_TRANSMISSION\ndiffuseColor.a *= material.transmissionAlpha;\n#endif\ngl_FragColor = vec4( outgoingLight, diffuseColor.a );", packing: "vec3 packNormalToRGB( const in vec3 normal ) {\n	return normalize( normal ) * 0.5 + 0.5;\n}\nvec3 unpackRGBToNormal( const in vec3 rgb ) {\n	return 2.0 * rgb.xyz - 1.0;\n}\nconst float PackUpscale = 256. / 255.;const float UnpackDownscale = 255. / 256.;const float ShiftRight8 = 1. / 256.;\nconst float Inv255 = 1. / 255.;\nconst vec4 PackFactors = vec4( 1.0, 256.0, 256.0 * 256.0, 256.0 * 256.0 * 256.0 );\nconst vec2 UnpackFactors2 = vec2( UnpackDownscale, 1.0 / PackFactors.g );\nconst vec3 UnpackFactors3 = vec3( UnpackDownscale / PackFactors.rg, 1.0 / PackFactors.b );\nconst vec4 UnpackFactors4 = vec4( UnpackDownscale / PackFactors.rgb, 1.0 / PackFactors.a );\nvec4 packDepthToRGBA( const in float v ) {\n	if( v <= 0.0 )\n		return vec4( 0., 0., 0., 0. );\n	if( v >= 1.0 )\n		return vec4( 1., 1., 1., 1. );\n	float vuf;\n	float af = modf( v * PackFactors.a, vuf );\n	float bf = modf( vuf * ShiftRight8, vuf );\n	float gf = modf( vuf * ShiftRight8, vuf );\n	return vec4( vuf * Inv255, gf * PackUpscale, bf * PackUpscale, af );\n}\nvec3 packDepthToRGB( const in float v ) {\n	if( v <= 0.0 )\n		return vec3( 0., 0., 0. );\n	if( v >= 1.0 )\n		return vec3( 1., 1., 1. );\n	float vuf;\n	float bf = modf( v * PackFactors.b, vuf );\n	float gf = modf( vuf * ShiftRight8, vuf );\n	return vec3( vuf * Inv255, gf * PackUpscale, bf );\n}\nvec2 packDepthToRG( const in float v ) {\n	if( v <= 0.0 )\n		return vec2( 0., 0. );\n	if( v >= 1.0 )\n		return vec2( 1., 1. );\n	float vuf;\n	float gf = modf( v * 256., vuf );\n	return vec2( vuf * Inv255, gf );\n}\nfloat unpackRGBAToDepth( const in vec4 v ) {\n	return dot( v, UnpackFactors4 );\n}\nfloat unpackRGBToDepth( const in vec3 v ) {\n	return dot( v, UnpackFactors3 );\n}\nfloat unpackRGToDepth( const in vec2 v ) {\n	return v.r * UnpackFactors2.r + v.g * UnpackFactors2.g;\n}\nvec4 pack2HalfToRGBA( const in vec2 v ) {\n	vec4 r = vec4( v.x, fract( v.x * 255.0 ), v.y, fract( v.y * 255.0 ) );\n	return vec4( r.x - r.y / 255.0, r.y, r.z - r.w / 255.0, r.w );\n}\nvec2 unpackRGBATo2Half( const in vec4 v ) {\n	return vec2( v.x + ( v.y / 255.0 ), v.z + ( v.w / 255.0 ) );\n}\nfloat viewZToOrthographicDepth( const in float viewZ, const in float near, const in float far ) {\n	return ( viewZ + near ) / ( near - far );\n}\nfloat orthographicDepthToViewZ( const in float depth, const in float near, const in float far ) {\n	return depth * ( near - far ) - near;\n}\nfloat viewZToPerspectiveDepth( const in float viewZ, const in float near, const in float far ) {\n	return ( ( near + viewZ ) * far ) / ( ( far - near ) * viewZ );\n}\nfloat perspectiveDepthToViewZ( const in float depth, const in float near, const in float far ) {\n	return ( near * far ) / ( ( far - near ) * depth - far );\n}", premultiplied_alpha_fragment: "#ifdef PREMULTIPLIED_ALPHA\n	gl_FragColor.rgb *= gl_FragColor.a;\n#endif", project_vertex: "vec4 mvPosition = vec4( transformed, 1.0 );\n#ifdef USE_BATCHING\n	mvPosition = batchingMatrix * mvPosition;\n#endif\n#ifdef USE_INSTANCING\n	mvPosition = instanceMatrix * mvPosition;\n#endif\nmvPosition = modelViewMatrix * mvPosition;\ngl_Position = projectionMatrix * mvPosition;", dithering_fragment: "#ifdef DITHERING\n	gl_FragColor.rgb = dithering( gl_FragColor.rgb );\n#endif", dithering_pars_fragment: "#ifdef DITHERING\n	vec3 dithering( vec3 color ) {\n		float grid_position = rand( gl_FragCoord.xy );\n		vec3 dither_shift_RGB = vec3( 0.25 / 255.0, -0.25 / 255.0, 0.25 / 255.0 );\n		dither_shift_RGB = mix( 2.0 * dither_shift_RGB, -2.0 * dither_shift_RGB, grid_position );\n		return color + dither_shift_RGB;\n	}\n#endif", roughnessmap_fragment: "float roughnessFactor = roughness;\n#ifdef USE_ROUGHNESSMAP\n	vec4 texelRoughness = texture2D( roughnessMap, vRoughnessMapUv );\n	roughnessFactor *= texelRoughness.g;\n#endif", roughnessmap_pars_fragment: "#ifdef USE_ROUGHNESSMAP\n	uniform sampler2D roughnessMap;\n#endif", shadowmap_pars_fragment: "#if NUM_SPOT_LIGHT_COORDS > 0\n	varying vec4 vSpotLightCoord[ NUM_SPOT_LIGHT_COORDS ];\n#endif\n#if NUM_SPOT_LIGHT_MAPS > 0\n	uniform sampler2D spotLightMap[ NUM_SPOT_LIGHT_MAPS ];\n#endif\n#ifdef USE_SHADOWMAP\n	#if NUM_DIR_LIGHT_SHADOWS > 0\n		uniform sampler2D directionalShadowMap[ NUM_DIR_LIGHT_SHADOWS ];\n		varying vec4 vDirectionalShadowCoord[ NUM_DIR_LIGHT_SHADOWS ];\n		struct DirectionalLightShadow {\n			float shadowIntensity;\n			float shadowBias;\n			float shadowNormalBias;\n			float shadowRadius;\n			vec2 shadowMapSize;\n		};\n		uniform DirectionalLightShadow directionalLightShadows[ NUM_DIR_LIGHT_SHADOWS ];\n	#endif\n	#if NUM_SPOT_LIGHT_SHADOWS > 0\n		uniform sampler2D spotShadowMap[ NUM_SPOT_LIGHT_SHADOWS ];\n		struct SpotLightShadow {\n			float shadowIntensity;\n			float shadowBias;\n			float shadowNormalBias;\n			float shadowRadius;\n			vec2 shadowMapSize;\n		};\n		uniform SpotLightShadow spotLightShadows[ NUM_SPOT_LIGHT_SHADOWS ];\n	#endif\n	#if NUM_POINT_LIGHT_SHADOWS > 0\n		uniform sampler2D pointShadowMap[ NUM_POINT_LIGHT_SHADOWS ];\n		varying vec4 vPointShadowCoord[ NUM_POINT_LIGHT_SHADOWS ];\n		struct PointLightShadow {\n			float shadowIntensity;\n			float shadowBias;\n			float shadowNormalBias;\n			float shadowRadius;\n			vec2 shadowMapSize;\n			float shadowCameraNear;\n			float shadowCameraFar;\n		};\n		uniform PointLightShadow pointLightShadows[ NUM_POINT_LIGHT_SHADOWS ];\n	#endif\n	float texture2DCompare( sampler2D depths, vec2 uv, float compare ) {\n		return step( compare, unpackRGBAToDepth( texture2D( depths, uv ) ) );\n	}\n	vec2 texture2DDistribution( sampler2D shadow, vec2 uv ) {\n		return unpackRGBATo2Half( texture2D( shadow, uv ) );\n	}\n	float VSMShadow (sampler2D shadow, vec2 uv, float compare ){\n		float occlusion = 1.0;\n		vec2 distribution = texture2DDistribution( shadow, uv );\n		float hard_shadow = step( compare , distribution.x );\n		if (hard_shadow != 1.0 ) {\n			float distance = compare - distribution.x ;\n			float variance = max( 0.00000, distribution.y * distribution.y );\n			float softness_probability = variance / (variance + distance * distance );			softness_probability = clamp( ( softness_probability - 0.3 ) / ( 0.95 - 0.3 ), 0.0, 1.0 );			occlusion = clamp( max( hard_shadow, softness_probability ), 0.0, 1.0 );\n		}\n		return occlusion;\n	}\n	float getShadow( sampler2D shadowMap, vec2 shadowMapSize, float shadowIntensity, float shadowBias, float shadowRadius, vec4 shadowCoord ) {\n		float shadow = 1.0;\n		shadowCoord.xyz /= shadowCoord.w;\n		shadowCoord.z += shadowBias;\n		bool inFrustum = shadowCoord.x >= 0.0 && shadowCoord.x <= 1.0 && shadowCoord.y >= 0.0 && shadowCoord.y <= 1.0;\n		bool frustumTest = inFrustum && shadowCoord.z <= 1.0;\n		if ( frustumTest ) {\n		#if defined( SHADOWMAP_TYPE_PCF )\n			vec2 texelSize = vec2( 1.0 ) / shadowMapSize;\n			float dx0 = - texelSize.x * shadowRadius;\n			float dy0 = - texelSize.y * shadowRadius;\n			float dx1 = + texelSize.x * shadowRadius;\n			float dy1 = + texelSize.y * shadowRadius;\n			float dx2 = dx0 / 2.0;\n			float dy2 = dy0 / 2.0;\n			float dx3 = dx1 / 2.0;\n			float dy3 = dy1 / 2.0;\n			shadow = (\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( dx0, dy0 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( 0.0, dy0 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( dx1, dy0 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( dx2, dy2 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( 0.0, dy2 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( dx3, dy2 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( dx0, 0.0 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( dx2, 0.0 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy, shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( dx3, 0.0 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( dx1, 0.0 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( dx2, dy3 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( 0.0, dy3 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( dx3, dy3 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( dx0, dy1 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( 0.0, dy1 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, shadowCoord.xy + vec2( dx1, dy1 ), shadowCoord.z )\n			) * ( 1.0 / 17.0 );\n		#elif defined( SHADOWMAP_TYPE_PCF_SOFT )\n			vec2 texelSize = vec2( 1.0 ) / shadowMapSize;\n			float dx = texelSize.x;\n			float dy = texelSize.y;\n			vec2 uv = shadowCoord.xy;\n			vec2 f = fract( uv * shadowMapSize + 0.5 );\n			uv -= f * texelSize;\n			shadow = (\n				texture2DCompare( shadowMap, uv, shadowCoord.z ) +\n				texture2DCompare( shadowMap, uv + vec2( dx, 0.0 ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, uv + vec2( 0.0, dy ), shadowCoord.z ) +\n				texture2DCompare( shadowMap, uv + texelSize, shadowCoord.z ) +\n				mix( texture2DCompare( shadowMap, uv + vec2( -dx, 0.0 ), shadowCoord.z ),\n					 texture2DCompare( shadowMap, uv + vec2( 2.0 * dx, 0.0 ), shadowCoord.z ),\n					 f.x ) +\n				mix( texture2DCompare( shadowMap, uv + vec2( -dx, dy ), shadowCoord.z ),\n					 texture2DCompare( shadowMap, uv + vec2( 2.0 * dx, dy ), shadowCoord.z ),\n					 f.x ) +\n				mix( texture2DCompare( shadowMap, uv + vec2( 0.0, -dy ), shadowCoord.z ),\n					 texture2DCompare( shadowMap, uv + vec2( 0.0, 2.0 * dy ), shadowCoord.z ),\n					 f.y ) +\n				mix( texture2DCompare( shadowMap, uv + vec2( dx, -dy ), shadowCoord.z ),\n					 texture2DCompare( shadowMap, uv + vec2( dx, 2.0 * dy ), shadowCoord.z ),\n					 f.y ) +\n				mix( mix( texture2DCompare( shadowMap, uv + vec2( -dx, -dy ), shadowCoord.z ),\n						  texture2DCompare( shadowMap, uv + vec2( 2.0 * dx, -dy ), shadowCoord.z ),\n						  f.x ),\n					 mix( texture2DCompare( shadowMap, uv + vec2( -dx, 2.0 * dy ), shadowCoord.z ),\n						  texture2DCompare( shadowMap, uv + vec2( 2.0 * dx, 2.0 * dy ), shadowCoord.z ),\n						  f.x ),\n					 f.y )\n			) * ( 1.0 / 9.0 );\n		#elif defined( SHADOWMAP_TYPE_VSM )\n			shadow = VSMShadow( shadowMap, shadowCoord.xy, shadowCoord.z );\n		#else\n			shadow = texture2DCompare( shadowMap, shadowCoord.xy, shadowCoord.z );\n		#endif\n		}\n		return mix( 1.0, shadow, shadowIntensity );\n	}\n	vec2 cubeToUV( vec3 v, float texelSizeY ) {\n		vec3 absV = abs( v );\n		float scaleToCube = 1.0 / max( absV.x, max( absV.y, absV.z ) );\n		absV *= scaleToCube;\n		v *= scaleToCube * ( 1.0 - 2.0 * texelSizeY );\n		vec2 planar = v.xy;\n		float almostATexel = 1.5 * texelSizeY;\n		float almostOne = 1.0 - almostATexel;\n		if ( absV.z >= almostOne ) {\n			if ( v.z > 0.0 )\n				planar.x = 4.0 - v.x;\n		} else if ( absV.x >= almostOne ) {\n			float signX = sign( v.x );\n			planar.x = v.z * signX + 2.0 * signX;\n		} else if ( absV.y >= almostOne ) {\n			float signY = sign( v.y );\n			planar.x = v.x + 2.0 * signY + 2.0;\n			planar.y = v.z * signY - 2.0;\n		}\n		return vec2( 0.125, 0.25 ) * planar + vec2( 0.375, 0.75 );\n	}\n	float getPointShadow( sampler2D shadowMap, vec2 shadowMapSize, float shadowIntensity, float shadowBias, float shadowRadius, vec4 shadowCoord, float shadowCameraNear, float shadowCameraFar ) {\n		float shadow = 1.0;\n		vec3 lightToPosition = shadowCoord.xyz;\n		\n		float lightToPositionLength = length( lightToPosition );\n		if ( lightToPositionLength - shadowCameraFar <= 0.0 && lightToPositionLength - shadowCameraNear >= 0.0 ) {\n			float dp = ( lightToPositionLength - shadowCameraNear ) / ( shadowCameraFar - shadowCameraNear );			dp += shadowBias;\n			vec3 bd3D = normalize( lightToPosition );\n			vec2 texelSize = vec2( 1.0 ) / ( shadowMapSize * vec2( 4.0, 2.0 ) );\n			#if defined( SHADOWMAP_TYPE_PCF ) || defined( SHADOWMAP_TYPE_PCF_SOFT ) || defined( SHADOWMAP_TYPE_VSM )\n				vec2 offset = vec2( - 1, 1 ) * shadowRadius * texelSize.y;\n				shadow = (\n					texture2DCompare( shadowMap, cubeToUV( bd3D + offset.xyy, texelSize.y ), dp ) +\n					texture2DCompare( shadowMap, cubeToUV( bd3D + offset.yyy, texelSize.y ), dp ) +\n					texture2DCompare( shadowMap, cubeToUV( bd3D + offset.xyx, texelSize.y ), dp ) +\n					texture2DCompare( shadowMap, cubeToUV( bd3D + offset.yyx, texelSize.y ), dp ) +\n					texture2DCompare( shadowMap, cubeToUV( bd3D, texelSize.y ), dp ) +\n					texture2DCompare( shadowMap, cubeToUV( bd3D + offset.xxy, texelSize.y ), dp ) +\n					texture2DCompare( shadowMap, cubeToUV( bd3D + offset.yxy, texelSize.y ), dp ) +\n					texture2DCompare( shadowMap, cubeToUV( bd3D + offset.xxx, texelSize.y ), dp ) +\n					texture2DCompare( shadowMap, cubeToUV( bd3D + offset.yxx, texelSize.y ), dp )\n				) * ( 1.0 / 9.0 );\n			#else\n				shadow = texture2DCompare( shadowMap, cubeToUV( bd3D, texelSize.y ), dp );\n			#endif\n		}\n		return mix( 1.0, shadow, shadowIntensity );\n	}\n#endif", shadowmap_pars_vertex: "#if NUM_SPOT_LIGHT_COORDS > 0\n	uniform mat4 spotLightMatrix[ NUM_SPOT_LIGHT_COORDS ];\n	varying vec4 vSpotLightCoord[ NUM_SPOT_LIGHT_COORDS ];\n#endif\n#ifdef USE_SHADOWMAP\n	#if NUM_DIR_LIGHT_SHADOWS > 0\n		uniform mat4 directionalShadowMatrix[ NUM_DIR_LIGHT_SHADOWS ];\n		varying vec4 vDirectionalShadowCoord[ NUM_DIR_LIGHT_SHADOWS ];\n		struct DirectionalLightShadow {\n			float shadowIntensity;\n			float shadowBias;\n			float shadowNormalBias;\n			float shadowRadius;\n			vec2 shadowMapSize;\n		};\n		uniform DirectionalLightShadow directionalLightShadows[ NUM_DIR_LIGHT_SHADOWS ];\n	#endif\n	#if NUM_SPOT_LIGHT_SHADOWS > 0\n		struct SpotLightShadow {\n			float shadowIntensity;\n			float shadowBias;\n			float shadowNormalBias;\n			float shadowRadius;\n			vec2 shadowMapSize;\n		};\n		uniform SpotLightShadow spotLightShadows[ NUM_SPOT_LIGHT_SHADOWS ];\n	#endif\n	#if NUM_POINT_LIGHT_SHADOWS > 0\n		uniform mat4 pointShadowMatrix[ NUM_POINT_LIGHT_SHADOWS ];\n		varying vec4 vPointShadowCoord[ NUM_POINT_LIGHT_SHADOWS ];\n		struct PointLightShadow {\n			float shadowIntensity;\n			float shadowBias;\n			float shadowNormalBias;\n			float shadowRadius;\n			vec2 shadowMapSize;\n			float shadowCameraNear;\n			float shadowCameraFar;\n		};\n		uniform PointLightShadow pointLightShadows[ NUM_POINT_LIGHT_SHADOWS ];\n	#endif\n#endif", shadowmap_vertex: "#if ( defined( USE_SHADOWMAP ) && ( NUM_DIR_LIGHT_SHADOWS > 0 || NUM_POINT_LIGHT_SHADOWS > 0 ) ) || ( NUM_SPOT_LIGHT_COORDS > 0 )\n	vec3 shadowWorldNormal = inverseTransformDirection( transformedNormal, viewMatrix );\n	vec4 shadowWorldPosition;\n#endif\n#if defined( USE_SHADOWMAP )\n	#if NUM_DIR_LIGHT_SHADOWS > 0\n		#pragma unroll_loop_start\n		for ( int i = 0; i < NUM_DIR_LIGHT_SHADOWS; i ++ ) {\n			shadowWorldPosition = worldPosition + vec4( shadowWorldNormal * directionalLightShadows[ i ].shadowNormalBias, 0 );\n			vDirectionalShadowCoord[ i ] = directionalShadowMatrix[ i ] * shadowWorldPosition;\n		}\n		#pragma unroll_loop_end\n	#endif\n	#if NUM_POINT_LIGHT_SHADOWS > 0\n		#pragma unroll_loop_start\n		for ( int i = 0; i < NUM_POINT_LIGHT_SHADOWS; i ++ ) {\n			shadowWorldPosition = worldPosition + vec4( shadowWorldNormal * pointLightShadows[ i ].shadowNormalBias, 0 );\n			vPointShadowCoord[ i ] = pointShadowMatrix[ i ] * shadowWorldPosition;\n		}\n		#pragma unroll_loop_end\n	#endif\n#endif\n#if NUM_SPOT_LIGHT_COORDS > 0\n	#pragma unroll_loop_start\n	for ( int i = 0; i < NUM_SPOT_LIGHT_COORDS; i ++ ) {\n		shadowWorldPosition = worldPosition;\n		#if ( defined( USE_SHADOWMAP ) && UNROLLED_LOOP_INDEX < NUM_SPOT_LIGHT_SHADOWS )\n			shadowWorldPosition.xyz += shadowWorldNormal * spotLightShadows[ i ].shadowNormalBias;\n		#endif\n		vSpotLightCoord[ i ] = spotLightMatrix[ i ] * shadowWorldPosition;\n	}\n	#pragma unroll_loop_end\n#endif", shadowmask_pars_fragment: "float getShadowMask() {\n	float shadow = 1.0;\n	#ifdef USE_SHADOWMAP\n	#if NUM_DIR_LIGHT_SHADOWS > 0\n	DirectionalLightShadow directionalLight;\n	#pragma unroll_loop_start\n	for ( int i = 0; i < NUM_DIR_LIGHT_SHADOWS; i ++ ) {\n		directionalLight = directionalLightShadows[ i ];\n		shadow *= receiveShadow ? getShadow( directionalShadowMap[ i ], directionalLight.shadowMapSize, directionalLight.shadowIntensity, directionalLight.shadowBias, directionalLight.shadowRadius, vDirectionalShadowCoord[ i ] ) : 1.0;\n	}\n	#pragma unroll_loop_end\n	#endif\n	#if NUM_SPOT_LIGHT_SHADOWS > 0\n	SpotLightShadow spotLight;\n	#pragma unroll_loop_start\n	for ( int i = 0; i < NUM_SPOT_LIGHT_SHADOWS; i ++ ) {\n		spotLight = spotLightShadows[ i ];\n		shadow *= receiveShadow ? getShadow( spotShadowMap[ i ], spotLight.shadowMapSize, spotLight.shadowIntensity, spotLight.shadowBias, spotLight.shadowRadius, vSpotLightCoord[ i ] ) : 1.0;\n	}\n	#pragma unroll_loop_end\n	#endif\n	#if NUM_POINT_LIGHT_SHADOWS > 0\n	PointLightShadow pointLight;\n	#pragma unroll_loop_start\n	for ( int i = 0; i < NUM_POINT_LIGHT_SHADOWS; i ++ ) {\n		pointLight = pointLightShadows[ i ];\n		shadow *= receiveShadow ? getPointShadow( pointShadowMap[ i ], pointLight.shadowMapSize, pointLight.shadowIntensity, pointLight.shadowBias, pointLight.shadowRadius, vPointShadowCoord[ i ], pointLight.shadowCameraNear, pointLight.shadowCameraFar ) : 1.0;\n	}\n	#pragma unroll_loop_end\n	#endif\n	#endif\n	return shadow;\n}", skinbase_vertex: "#ifdef USE_SKINNING\n	mat4 boneMatX = getBoneMatrix( skinIndex.x );\n	mat4 boneMatY = getBoneMatrix( skinIndex.y );\n	mat4 boneMatZ = getBoneMatrix( skinIndex.z );\n	mat4 boneMatW = getBoneMatrix( skinIndex.w );\n#endif", skinning_pars_vertex: "#ifdef USE_SKINNING\n	uniform mat4 bindMatrix;\n	uniform mat4 bindMatrixInverse;\n	uniform highp sampler2D boneTexture;\n	mat4 getBoneMatrix( const in float i ) {\n		int size = textureSize( boneTexture, 0 ).x;\n		int j = int( i ) * 4;\n		int x = j % size;\n		int y = j / size;\n		vec4 v1 = texelFetch( boneTexture, ivec2( x, y ), 0 );\n		vec4 v2 = texelFetch( boneTexture, ivec2( x + 1, y ), 0 );\n		vec4 v3 = texelFetch( boneTexture, ivec2( x + 2, y ), 0 );\n		vec4 v4 = texelFetch( boneTexture, ivec2( x + 3, y ), 0 );\n		return mat4( v1, v2, v3, v4 );\n	}\n#endif", skinning_vertex: "#ifdef USE_SKINNING\n	vec4 skinVertex = bindMatrix * vec4( transformed, 1.0 );\n	vec4 skinned = vec4( 0.0 );\n	skinned += boneMatX * skinVertex * skinWeight.x;\n	skinned += boneMatY * skinVertex * skinWeight.y;\n	skinned += boneMatZ * skinVertex * skinWeight.z;\n	skinned += boneMatW * skinVertex * skinWeight.w;\n	transformed = ( bindMatrixInverse * skinned ).xyz;\n#endif", skinnormal_vertex: "#ifdef USE_SKINNING\n	mat4 skinMatrix = mat4( 0.0 );\n	skinMatrix += skinWeight.x * boneMatX;\n	skinMatrix += skinWeight.y * boneMatY;\n	skinMatrix += skinWeight.z * boneMatZ;\n	skinMatrix += skinWeight.w * boneMatW;\n	skinMatrix = bindMatrixInverse * skinMatrix * bindMatrix;\n	objectNormal = vec4( skinMatrix * vec4( objectNormal, 0.0 ) ).xyz;\n	#ifdef USE_TANGENT\n		objectTangent = vec4( skinMatrix * vec4( objectTangent, 0.0 ) ).xyz;\n	#endif\n#endif", specularmap_fragment: "float specularStrength;\n#ifdef USE_SPECULARMAP\n	vec4 texelSpecular = texture2D( specularMap, vSpecularMapUv );\n	specularStrength = texelSpecular.r;\n#else\n	specularStrength = 1.0;\n#endif", specularmap_pars_fragment: "#ifdef USE_SPECULARMAP\n	uniform sampler2D specularMap;\n#endif", tonemapping_fragment: "#if defined( TONE_MAPPING )\n	gl_FragColor.rgb = toneMapping( gl_FragColor.rgb );\n#endif", tonemapping_pars_fragment: "#ifndef saturate\n#define saturate( a ) clamp( a, 0.0, 1.0 )\n#endif\nuniform float toneMappingExposure;\nvec3 LinearToneMapping( vec3 color ) {\n	return saturate( toneMappingExposure * color );\n}\nvec3 ReinhardToneMapping( vec3 color ) {\n	color *= toneMappingExposure;\n	return saturate( color / ( vec3( 1.0 ) + color ) );\n}\nvec3 CineonToneMapping( vec3 color ) {\n	color *= toneMappingExposure;\n	color = max( vec3( 0.0 ), color - 0.004 );\n	return pow( ( color * ( 6.2 * color + 0.5 ) ) / ( color * ( 6.2 * color + 1.7 ) + 0.06 ), vec3( 2.2 ) );\n}\nvec3 RRTAndODTFit( vec3 v ) {\n	vec3 a = v * ( v + 0.0245786 ) - 0.000090537;\n	vec3 b = v * ( 0.983729 * v + 0.4329510 ) + 0.238081;\n	return a / b;\n}\nvec3 ACESFilmicToneMapping( vec3 color ) {\n	const mat3 ACESInputMat = mat3(\n		vec3( 0.59719, 0.07600, 0.02840 ),		vec3( 0.35458, 0.90834, 0.13383 ),\n		vec3( 0.04823, 0.01566, 0.83777 )\n	);\n	const mat3 ACESOutputMat = mat3(\n		vec3(  1.60475, -0.10208, -0.00327 ),		vec3( -0.53108,  1.10813, -0.07276 ),\n		vec3( -0.07367, -0.00605,  1.07602 )\n	);\n	color *= toneMappingExposure / 0.6;\n	color = ACESInputMat * color;\n	color = RRTAndODTFit( color );\n	color = ACESOutputMat * color;\n	return saturate( color );\n}\nconst mat3 LINEAR_REC2020_TO_LINEAR_SRGB = mat3(\n	vec3( 1.6605, - 0.1246, - 0.0182 ),\n	vec3( - 0.5876, 1.1329, - 0.1006 ),\n	vec3( - 0.0728, - 0.0083, 1.1187 )\n);\nconst mat3 LINEAR_SRGB_TO_LINEAR_REC2020 = mat3(\n	vec3( 0.6274, 0.0691, 0.0164 ),\n	vec3( 0.3293, 0.9195, 0.0880 ),\n	vec3( 0.0433, 0.0113, 0.8956 )\n);\nvec3 agxDefaultContrastApprox( vec3 x ) {\n	vec3 x2 = x * x;\n	vec3 x4 = x2 * x2;\n	return + 15.5 * x4 * x2\n		- 40.14 * x4 * x\n		+ 31.96 * x4\n		- 6.868 * x2 * x\n		+ 0.4298 * x2\n		+ 0.1191 * x\n		- 0.00232;\n}\nvec3 AgXToneMapping( vec3 color ) {\n	const mat3 AgXInsetMatrix = mat3(\n		vec3( 0.856627153315983, 0.137318972929847, 0.11189821299995 ),\n		vec3( 0.0951212405381588, 0.761241990602591, 0.0767994186031903 ),\n		vec3( 0.0482516061458583, 0.101439036467562, 0.811302368396859 )\n	);\n	const mat3 AgXOutsetMatrix = mat3(\n		vec3( 1.1271005818144368, - 0.1413297634984383, - 0.14132976349843826 ),\n		vec3( - 0.11060664309660323, 1.157823702216272, - 0.11060664309660294 ),\n		vec3( - 0.016493938717834573, - 0.016493938717834257, 1.2519364065950405 )\n	);\n	const float AgxMinEv = - 12.47393;	const float AgxMaxEv = 4.026069;\n	color *= toneMappingExposure;\n	color = LINEAR_SRGB_TO_LINEAR_REC2020 * color;\n	color = AgXInsetMatrix * color;\n	color = max( color, 1e-10 );	color = log2( color );\n	color = ( color - AgxMinEv ) / ( AgxMaxEv - AgxMinEv );\n	color = clamp( color, 0.0, 1.0 );\n	color = agxDefaultContrastApprox( color );\n	color = AgXOutsetMatrix * color;\n	color = pow( max( vec3( 0.0 ), color ), vec3( 2.2 ) );\n	color = LINEAR_REC2020_TO_LINEAR_SRGB * color;\n	color = clamp( color, 0.0, 1.0 );\n	return color;\n}\nvec3 NeutralToneMapping( vec3 color ) {\n	const float StartCompression = 0.8 - 0.04;\n	const float Desaturation = 0.15;\n	color *= toneMappingExposure;\n	float x = min( color.r, min( color.g, color.b ) );\n	float offset = x < 0.08 ? x - 6.25 * x * x : 0.04;\n	color -= offset;\n	float peak = max( color.r, max( color.g, color.b ) );\n	if ( peak < StartCompression ) return color;\n	float d = 1. - StartCompression;\n	float newPeak = 1. - d * d / ( peak + d - StartCompression );\n	color *= newPeak / peak;\n	float g = 1. - 1. / ( Desaturation * ( peak - newPeak ) + 1. );\n	return mix( color, vec3( newPeak ), g );\n}\nvec3 CustomToneMapping( vec3 color ) { return color; }", transmission_fragment: "#ifdef USE_TRANSMISSION\n	material.transmission = transmission;\n	material.transmissionAlpha = 1.0;\n	material.thickness = thickness;\n	material.attenuationDistance = attenuationDistance;\n	material.attenuationColor = attenuationColor;\n	#ifdef USE_TRANSMISSIONMAP\n		material.transmission *= texture2D( transmissionMap, vTransmissionMapUv ).r;\n	#endif\n	#ifdef USE_THICKNESSMAP\n		material.thickness *= texture2D( thicknessMap, vThicknessMapUv ).g;\n	#endif\n	vec3 pos = vWorldPosition;\n	vec3 v = normalize( cameraPosition - pos );\n	vec3 n = inverseTransformDirection( normal, viewMatrix );\n	vec4 transmitted = getIBLVolumeRefraction(\n		n, v, material.roughness, material.diffuseColor, material.specularColor, material.specularF90,\n		pos, modelMatrix, viewMatrix, projectionMatrix, material.dispersion, material.ior, material.thickness,\n		material.attenuationColor, material.attenuationDistance );\n	material.transmissionAlpha = mix( material.transmissionAlpha, transmitted.a, material.transmission );\n	totalDiffuse = mix( totalDiffuse, transmitted.rgb, material.transmission );\n#endif", transmission_pars_fragment: "#ifdef USE_TRANSMISSION\n	uniform float transmission;\n	uniform float thickness;\n	uniform float attenuationDistance;\n	uniform vec3 attenuationColor;\n	#ifdef USE_TRANSMISSIONMAP\n		uniform sampler2D transmissionMap;\n	#endif\n	#ifdef USE_THICKNESSMAP\n		uniform sampler2D thicknessMap;\n	#endif\n	uniform vec2 transmissionSamplerSize;\n	uniform sampler2D transmissionSamplerMap;\n	uniform mat4 modelMatrix;\n	uniform mat4 projectionMatrix;\n	varying vec3 vWorldPosition;\n	float w0( float a ) {\n		return ( 1.0 / 6.0 ) * ( a * ( a * ( - a + 3.0 ) - 3.0 ) + 1.0 );\n	}\n	float w1( float a ) {\n		return ( 1.0 / 6.0 ) * ( a *  a * ( 3.0 * a - 6.0 ) + 4.0 );\n	}\n	float w2( float a ){\n		return ( 1.0 / 6.0 ) * ( a * ( a * ( - 3.0 * a + 3.0 ) + 3.0 ) + 1.0 );\n	}\n	float w3( float a ) {\n		return ( 1.0 / 6.0 ) * ( a * a * a );\n	}\n	float g0( float a ) {\n		return w0( a ) + w1( a );\n	}\n	float g1( float a ) {\n		return w2( a ) + w3( a );\n	}\n	float h0( float a ) {\n		return - 1.0 + w1( a ) / ( w0( a ) + w1( a ) );\n	}\n	float h1( float a ) {\n		return 1.0 + w3( a ) / ( w2( a ) + w3( a ) );\n	}\n	vec4 bicubic( sampler2D tex, vec2 uv, vec4 texelSize, float lod ) {\n		uv = uv * texelSize.zw + 0.5;\n		vec2 iuv = floor( uv );\n		vec2 fuv = fract( uv );\n		float g0x = g0( fuv.x );\n		float g1x = g1( fuv.x );\n		float h0x = h0( fuv.x );\n		float h1x = h1( fuv.x );\n		float h0y = h0( fuv.y );\n		float h1y = h1( fuv.y );\n		vec2 p0 = ( vec2( iuv.x + h0x, iuv.y + h0y ) - 0.5 ) * texelSize.xy;\n		vec2 p1 = ( vec2( iuv.x + h1x, iuv.y + h0y ) - 0.5 ) * texelSize.xy;\n		vec2 p2 = ( vec2( iuv.x + h0x, iuv.y + h1y ) - 0.5 ) * texelSize.xy;\n		vec2 p3 = ( vec2( iuv.x + h1x, iuv.y + h1y ) - 0.5 ) * texelSize.xy;\n		return g0( fuv.y ) * ( g0x * textureLod( tex, p0, lod ) + g1x * textureLod( tex, p1, lod ) ) +\n			g1( fuv.y ) * ( g0x * textureLod( tex, p2, lod ) + g1x * textureLod( tex, p3, lod ) );\n	}\n	vec4 textureBicubic( sampler2D sampler, vec2 uv, float lod ) {\n		vec2 fLodSize = vec2( textureSize( sampler, int( lod ) ) );\n		vec2 cLodSize = vec2( textureSize( sampler, int( lod + 1.0 ) ) );\n		vec2 fLodSizeInv = 1.0 / fLodSize;\n		vec2 cLodSizeInv = 1.0 / cLodSize;\n		vec4 fSample = bicubic( sampler, uv, vec4( fLodSizeInv, fLodSize ), floor( lod ) );\n		vec4 cSample = bicubic( sampler, uv, vec4( cLodSizeInv, cLodSize ), ceil( lod ) );\n		return mix( fSample, cSample, fract( lod ) );\n	}\n	vec3 getVolumeTransmissionRay( const in vec3 n, const in vec3 v, const in float thickness, const in float ior, const in mat4 modelMatrix ) {\n		vec3 refractionVector = refract( - v, normalize( n ), 1.0 / ior );\n		vec3 modelScale;\n		modelScale.x = length( vec3( modelMatrix[ 0 ].xyz ) );\n		modelScale.y = length( vec3( modelMatrix[ 1 ].xyz ) );\n		modelScale.z = length( vec3( modelMatrix[ 2 ].xyz ) );\n		return normalize( refractionVector ) * thickness * modelScale;\n	}\n	float applyIorToRoughness( const in float roughness, const in float ior ) {\n		return roughness * clamp( ior * 2.0 - 2.0, 0.0, 1.0 );\n	}\n	vec4 getTransmissionSample( const in vec2 fragCoord, const in float roughness, const in float ior ) {\n		float lod = log2( transmissionSamplerSize.x ) * applyIorToRoughness( roughness, ior );\n		return textureBicubic( transmissionSamplerMap, fragCoord.xy, lod );\n	}\n	vec3 volumeAttenuation( const in float transmissionDistance, const in vec3 attenuationColor, const in float attenuationDistance ) {\n		if ( isinf( attenuationDistance ) ) {\n			return vec3( 1.0 );\n		} else {\n			vec3 attenuationCoefficient = -log( attenuationColor ) / attenuationDistance;\n			vec3 transmittance = exp( - attenuationCoefficient * transmissionDistance );			return transmittance;\n		}\n	}\n	vec4 getIBLVolumeRefraction( const in vec3 n, const in vec3 v, const in float roughness, const in vec3 diffuseColor,\n		const in vec3 specularColor, const in float specularF90, const in vec3 position, const in mat4 modelMatrix,\n		const in mat4 viewMatrix, const in mat4 projMatrix, const in float dispersion, const in float ior, const in float thickness,\n		const in vec3 attenuationColor, const in float attenuationDistance ) {\n		vec4 transmittedLight;\n		vec3 transmittance;\n		#ifdef USE_DISPERSION\n			float halfSpread = ( ior - 1.0 ) * 0.025 * dispersion;\n			vec3 iors = vec3( ior - halfSpread, ior, ior + halfSpread );\n			for ( int i = 0; i < 3; i ++ ) {\n				vec3 transmissionRay = getVolumeTransmissionRay( n, v, thickness, iors[ i ], modelMatrix );\n				vec3 refractedRayExit = position + transmissionRay;\n				vec4 ndcPos = projMatrix * viewMatrix * vec4( refractedRayExit, 1.0 );\n				vec2 refractionCoords = ndcPos.xy / ndcPos.w;\n				refractionCoords += 1.0;\n				refractionCoords /= 2.0;\n				vec4 transmissionSample = getTransmissionSample( refractionCoords, roughness, iors[ i ] );\n				transmittedLight[ i ] = transmissionSample[ i ];\n				transmittedLight.a += transmissionSample.a;\n				transmittance[ i ] = diffuseColor[ i ] * volumeAttenuation( length( transmissionRay ), attenuationColor, attenuationDistance )[ i ];\n			}\n			transmittedLight.a /= 3.0;\n		#else\n			vec3 transmissionRay = getVolumeTransmissionRay( n, v, thickness, ior, modelMatrix );\n			vec3 refractedRayExit = position + transmissionRay;\n			vec4 ndcPos = projMatrix * viewMatrix * vec4( refractedRayExit, 1.0 );\n			vec2 refractionCoords = ndcPos.xy / ndcPos.w;\n			refractionCoords += 1.0;\n			refractionCoords /= 2.0;\n			transmittedLight = getTransmissionSample( refractionCoords, roughness, ior );\n			transmittance = diffuseColor * volumeAttenuation( length( transmissionRay ), attenuationColor, attenuationDistance );\n		#endif\n		vec3 attenuatedColor = transmittance * transmittedLight.rgb;\n		vec3 F = EnvironmentBRDF( n, v, specularColor, specularF90, roughness );\n		float transmittanceFactor = ( transmittance.r + transmittance.g + transmittance.b ) / 3.0;\n		return vec4( ( 1.0 - F ) * attenuatedColor, 1.0 - ( 1.0 - transmittedLight.a ) * transmittanceFactor );\n	}\n#endif", uv_pars_fragment: "#if defined( USE_UV ) || defined( USE_ANISOTROPY )\n	varying vec2 vUv;\n#endif\n#ifdef USE_MAP\n	varying vec2 vMapUv;\n#endif\n#ifdef USE_ALPHAMAP\n	varying vec2 vAlphaMapUv;\n#endif\n#ifdef USE_LIGHTMAP\n	varying vec2 vLightMapUv;\n#endif\n#ifdef USE_AOMAP\n	varying vec2 vAoMapUv;\n#endif\n#ifdef USE_BUMPMAP\n	varying vec2 vBumpMapUv;\n#endif\n#ifdef USE_NORMALMAP\n	varying vec2 vNormalMapUv;\n#endif\n#ifdef USE_EMISSIVEMAP\n	varying vec2 vEmissiveMapUv;\n#endif\n#ifdef USE_METALNESSMAP\n	varying vec2 vMetalnessMapUv;\n#endif\n#ifdef USE_ROUGHNESSMAP\n	varying vec2 vRoughnessMapUv;\n#endif\n#ifdef USE_ANISOTROPYMAP\n	varying vec2 vAnisotropyMapUv;\n#endif\n#ifdef USE_CLEARCOATMAP\n	varying vec2 vClearcoatMapUv;\n#endif\n#ifdef USE_CLEARCOAT_NORMALMAP\n	varying vec2 vClearcoatNormalMapUv;\n#endif\n#ifdef USE_CLEARCOAT_ROUGHNESSMAP\n	varying vec2 vClearcoatRoughnessMapUv;\n#endif\n#ifdef USE_IRIDESCENCEMAP\n	varying vec2 vIridescenceMapUv;\n#endif\n#ifdef USE_IRIDESCENCE_THICKNESSMAP\n	varying vec2 vIridescenceThicknessMapUv;\n#endif\n#ifdef USE_SHEEN_COLORMAP\n	varying vec2 vSheenColorMapUv;\n#endif\n#ifdef USE_SHEEN_ROUGHNESSMAP\n	varying vec2 vSheenRoughnessMapUv;\n#endif\n#ifdef USE_SPECULARMAP\n	varying vec2 vSpecularMapUv;\n#endif\n#ifdef USE_SPECULAR_COLORMAP\n	varying vec2 vSpecularColorMapUv;\n#endif\n#ifdef USE_SPECULAR_INTENSITYMAP\n	varying vec2 vSpecularIntensityMapUv;\n#endif\n#ifdef USE_TRANSMISSIONMAP\n	uniform mat3 transmissionMapTransform;\n	varying vec2 vTransmissionMapUv;\n#endif\n#ifdef USE_THICKNESSMAP\n	uniform mat3 thicknessMapTransform;\n	varying vec2 vThicknessMapUv;\n#endif", uv_pars_vertex: "#if defined( USE_UV ) || defined( USE_ANISOTROPY )\n	varying vec2 vUv;\n#endif\n#ifdef USE_MAP\n	uniform mat3 mapTransform;\n	varying vec2 vMapUv;\n#endif\n#ifdef USE_ALPHAMAP\n	uniform mat3 alphaMapTransform;\n	varying vec2 vAlphaMapUv;\n#endif\n#ifdef USE_LIGHTMAP\n	uniform mat3 lightMapTransform;\n	varying vec2 vLightMapUv;\n#endif\n#ifdef USE_AOMAP\n	uniform mat3 aoMapTransform;\n	varying vec2 vAoMapUv;\n#endif\n#ifdef USE_BUMPMAP\n	uniform mat3 bumpMapTransform;\n	varying vec2 vBumpMapUv;\n#endif\n#ifdef USE_NORMALMAP\n	uniform mat3 normalMapTransform;\n	varying vec2 vNormalMapUv;\n#endif\n#ifdef USE_DISPLACEMENTMAP\n	uniform mat3 displacementMapTransform;\n	varying vec2 vDisplacementMapUv;\n#endif\n#ifdef USE_EMISSIVEMAP\n	uniform mat3 emissiveMapTransform;\n	varying vec2 vEmissiveMapUv;\n#endif\n#ifdef USE_METALNESSMAP\n	uniform mat3 metalnessMapTransform;\n	varying vec2 vMetalnessMapUv;\n#endif\n#ifdef USE_ROUGHNESSMAP\n	uniform mat3 roughnessMapTransform;\n	varying vec2 vRoughnessMapUv;\n#endif\n#ifdef USE_ANISOTROPYMAP\n	uniform mat3 anisotropyMapTransform;\n	varying vec2 vAnisotropyMapUv;\n#endif\n#ifdef USE_CLEARCOATMAP\n	uniform mat3 clearcoatMapTransform;\n	varying vec2 vClearcoatMapUv;\n#endif\n#ifdef USE_CLEARCOAT_NORMALMAP\n	uniform mat3 clearcoatNormalMapTransform;\n	varying vec2 vClearcoatNormalMapUv;\n#endif\n#ifdef USE_CLEARCOAT_ROUGHNESSMAP\n	uniform mat3 clearcoatRoughnessMapTransform;\n	varying vec2 vClearcoatRoughnessMapUv;\n#endif\n#ifdef USE_SHEEN_COLORMAP\n	uniform mat3 sheenColorMapTransform;\n	varying vec2 vSheenColorMapUv;\n#endif\n#ifdef USE_SHEEN_ROUGHNESSMAP\n	uniform mat3 sheenRoughnessMapTransform;\n	varying vec2 vSheenRoughnessMapUv;\n#endif\n#ifdef USE_IRIDESCENCEMAP\n	uniform mat3 iridescenceMapTransform;\n	varying vec2 vIridescenceMapUv;\n#endif\n#ifdef USE_IRIDESCENCE_THICKNESSMAP\n	uniform mat3 iridescenceThicknessMapTransform;\n	varying vec2 vIridescenceThicknessMapUv;\n#endif\n#ifdef USE_SPECULARMAP\n	uniform mat3 specularMapTransform;\n	varying vec2 vSpecularMapUv;\n#endif\n#ifdef USE_SPECULAR_COLORMAP\n	uniform mat3 specularColorMapTransform;\n	varying vec2 vSpecularColorMapUv;\n#endif\n#ifdef USE_SPECULAR_INTENSITYMAP\n	uniform mat3 specularIntensityMapTransform;\n	varying vec2 vSpecularIntensityMapUv;\n#endif\n#ifdef USE_TRANSMISSIONMAP\n	uniform mat3 transmissionMapTransform;\n	varying vec2 vTransmissionMapUv;\n#endif\n#ifdef USE_THICKNESSMAP\n	uniform mat3 thicknessMapTransform;\n	varying vec2 vThicknessMapUv;\n#endif", uv_vertex: "#if defined( USE_UV ) || defined( USE_ANISOTROPY )\n	vUv = vec3( uv, 1 ).xy;\n#endif\n#ifdef USE_MAP\n	vMapUv = ( mapTransform * vec3( MAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_ALPHAMAP\n	vAlphaMapUv = ( alphaMapTransform * vec3( ALPHAMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_LIGHTMAP\n	vLightMapUv = ( lightMapTransform * vec3( LIGHTMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_AOMAP\n	vAoMapUv = ( aoMapTransform * vec3( AOMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_BUMPMAP\n	vBumpMapUv = ( bumpMapTransform * vec3( BUMPMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_NORMALMAP\n	vNormalMapUv = ( normalMapTransform * vec3( NORMALMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_DISPLACEMENTMAP\n	vDisplacementMapUv = ( displacementMapTransform * vec3( DISPLACEMENTMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_EMISSIVEMAP\n	vEmissiveMapUv = ( emissiveMapTransform * vec3( EMISSIVEMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_METALNESSMAP\n	vMetalnessMapUv = ( metalnessMapTransform * vec3( METALNESSMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_ROUGHNESSMAP\n	vRoughnessMapUv = ( roughnessMapTransform * vec3( ROUGHNESSMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_ANISOTROPYMAP\n	vAnisotropyMapUv = ( anisotropyMapTransform * vec3( ANISOTROPYMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_CLEARCOATMAP\n	vClearcoatMapUv = ( clearcoatMapTransform * vec3( CLEARCOATMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_CLEARCOAT_NORMALMAP\n	vClearcoatNormalMapUv = ( clearcoatNormalMapTransform * vec3( CLEARCOAT_NORMALMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_CLEARCOAT_ROUGHNESSMAP\n	vClearcoatRoughnessMapUv = ( clearcoatRoughnessMapTransform * vec3( CLEARCOAT_ROUGHNESSMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_IRIDESCENCEMAP\n	vIridescenceMapUv = ( iridescenceMapTransform * vec3( IRIDESCENCEMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_IRIDESCENCE_THICKNESSMAP\n	vIridescenceThicknessMapUv = ( iridescenceThicknessMapTransform * vec3( IRIDESCENCE_THICKNESSMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_SHEEN_COLORMAP\n	vSheenColorMapUv = ( sheenColorMapTransform * vec3( SHEEN_COLORMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_SHEEN_ROUGHNESSMAP\n	vSheenRoughnessMapUv = ( sheenRoughnessMapTransform * vec3( SHEEN_ROUGHNESSMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_SPECULARMAP\n	vSpecularMapUv = ( specularMapTransform * vec3( SPECULARMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_SPECULAR_COLORMAP\n	vSpecularColorMapUv = ( specularColorMapTransform * vec3( SPECULAR_COLORMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_SPECULAR_INTENSITYMAP\n	vSpecularIntensityMapUv = ( specularIntensityMapTransform * vec3( SPECULAR_INTENSITYMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_TRANSMISSIONMAP\n	vTransmissionMapUv = ( transmissionMapTransform * vec3( TRANSMISSIONMAP_UV, 1 ) ).xy;\n#endif\n#ifdef USE_THICKNESSMAP\n	vThicknessMapUv = ( thicknessMapTransform * vec3( THICKNESSMAP_UV, 1 ) ).xy;\n#endif", worldpos_vertex: "#if defined( USE_ENVMAP ) || defined( DISTANCE ) || defined ( USE_SHADOWMAP ) || defined ( USE_TRANSMISSION ) || NUM_SPOT_LIGHT_COORDS > 0\n	vec4 worldPosition = vec4( transformed, 1.0 );\n	#ifdef USE_BATCHING\n		worldPosition = batchingMatrix * worldPosition;\n	#endif\n	#ifdef USE_INSTANCING\n		worldPosition = instanceMatrix * worldPosition;\n	#endif\n	worldPosition = modelMatrix * worldPosition;\n#endif", background_vert: "varying vec2 vUv;\nuniform mat3 uvTransform;\nvoid main() {\n	vUv = ( uvTransform * vec3( uv, 1 ) ).xy;\n	gl_Position = vec4( position.xy, 1.0, 1.0 );\n}", background_frag: "uniform sampler2D t2D;\nuniform float backgroundIntensity;\nvarying vec2 vUv;\nvoid main() {\n	vec4 texColor = texture2D( t2D, vUv );\n	#ifdef DECODE_VIDEO_TEXTURE\n		texColor = vec4( mix( pow( texColor.rgb * 0.9478672986 + vec3( 0.0521327014 ), vec3( 2.4 ) ), texColor.rgb * 0.0773993808, vec3( lessThanEqual( texColor.rgb, vec3( 0.04045 ) ) ) ), texColor.w );\n	#endif\n	texColor.rgb *= backgroundIntensity;\n	gl_FragColor = texColor;\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n}", backgroundCube_vert: "varying vec3 vWorldDirection;\n#include <common>\nvoid main() {\n	vWorldDirection = transformDirection( position, modelMatrix );\n	#include <begin_vertex>\n	#include <project_vertex>\n	gl_Position.z = gl_Position.w;\n}", backgroundCube_frag: "#ifdef ENVMAP_TYPE_CUBE\n	uniform samplerCube envMap;\n#elif defined( ENVMAP_TYPE_CUBE_UV )\n	uniform sampler2D envMap;\n#endif\nuniform float flipEnvMap;\nuniform float backgroundBlurriness;\nuniform float backgroundIntensity;\nuniform mat3 backgroundRotation;\nvarying vec3 vWorldDirection;\n#include <cube_uv_reflection_fragment>\nvoid main() {\n	#ifdef ENVMAP_TYPE_CUBE\n		vec4 texColor = textureCube( envMap, backgroundRotation * vec3( flipEnvMap * vWorldDirection.x, vWorldDirection.yz ) );\n	#elif defined( ENVMAP_TYPE_CUBE_UV )\n		vec4 texColor = textureCubeUV( envMap, backgroundRotation * vWorldDirection, backgroundBlurriness );\n	#else\n		vec4 texColor = vec4( 0.0, 0.0, 0.0, 1.0 );\n	#endif\n	texColor.rgb *= backgroundIntensity;\n	gl_FragColor = texColor;\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n}", cube_vert: "varying vec3 vWorldDirection;\n#include <common>\nvoid main() {\n	vWorldDirection = transformDirection( position, modelMatrix );\n	#include <begin_vertex>\n	#include <project_vertex>\n	gl_Position.z = gl_Position.w;\n}", cube_frag: "uniform samplerCube tCube;\nuniform float tFlip;\nuniform float opacity;\nvarying vec3 vWorldDirection;\nvoid main() {\n	vec4 texColor = textureCube( tCube, vec3( tFlip * vWorldDirection.x, vWorldDirection.yz ) );\n	gl_FragColor = texColor;\n	gl_FragColor.a *= opacity;\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n}", depth_vert: "#include <common>\n#include <batching_pars_vertex>\n#include <uv_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvarying vec2 vHighPrecisionZW;\nvoid main() {\n	#include <uv_vertex>\n	#include <batching_vertex>\n	#include <skinbase_vertex>\n	#include <morphinstance_vertex>\n	#ifdef USE_DISPLACEMENTMAP\n		#include <beginnormal_vertex>\n		#include <morphnormal_vertex>\n		#include <skinnormal_vertex>\n	#endif\n	#include <begin_vertex>\n	#include <morphtarget_vertex>\n	#include <skinning_vertex>\n	#include <displacementmap_vertex>\n	#include <project_vertex>\n	#include <logdepthbuf_vertex>\n	#include <clipping_planes_vertex>\n	vHighPrecisionZW = gl_Position.zw;\n}", depth_frag: "#if DEPTH_PACKING == 3200\n	uniform float opacity;\n#endif\n#include <common>\n#include <packing>\n#include <uv_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <alphatest_pars_fragment>\n#include <alphahash_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvarying vec2 vHighPrecisionZW;\nvoid main() {\n	vec4 diffuseColor = vec4( 1.0 );\n	#include <clipping_planes_fragment>\n	#if DEPTH_PACKING == 3200\n		diffuseColor.a = opacity;\n	#endif\n	#include <map_fragment>\n	#include <alphamap_fragment>\n	#include <alphatest_fragment>\n	#include <alphahash_fragment>\n	#include <logdepthbuf_fragment>\n	float fragCoordZ = 0.5 * vHighPrecisionZW[0] / vHighPrecisionZW[1] + 0.5;\n	#if DEPTH_PACKING == 3200\n		gl_FragColor = vec4( vec3( 1.0 - fragCoordZ ), opacity );\n	#elif DEPTH_PACKING == 3201\n		gl_FragColor = packDepthToRGBA( fragCoordZ );\n	#elif DEPTH_PACKING == 3202\n		gl_FragColor = vec4( packDepthToRGB( fragCoordZ ), 1.0 );\n	#elif DEPTH_PACKING == 3203\n		gl_FragColor = vec4( packDepthToRG( fragCoordZ ), 0.0, 1.0 );\n	#endif\n}", distanceRGBA_vert: "#define DISTANCE\nvarying vec3 vWorldPosition;\n#include <common>\n#include <batching_pars_vertex>\n#include <uv_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n	#include <uv_vertex>\n	#include <batching_vertex>\n	#include <skinbase_vertex>\n	#include <morphinstance_vertex>\n	#ifdef USE_DISPLACEMENTMAP\n		#include <beginnormal_vertex>\n		#include <morphnormal_vertex>\n		#include <skinnormal_vertex>\n	#endif\n	#include <begin_vertex>\n	#include <morphtarget_vertex>\n	#include <skinning_vertex>\n	#include <displacementmap_vertex>\n	#include <project_vertex>\n	#include <worldpos_vertex>\n	#include <clipping_planes_vertex>\n	vWorldPosition = worldPosition.xyz;\n}", distanceRGBA_frag: "#define DISTANCE\nuniform vec3 referencePosition;\nuniform float nearDistance;\nuniform float farDistance;\nvarying vec3 vWorldPosition;\n#include <common>\n#include <packing>\n#include <uv_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <alphatest_pars_fragment>\n#include <alphahash_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main () {\n	vec4 diffuseColor = vec4( 1.0 );\n	#include <clipping_planes_fragment>\n	#include <map_fragment>\n	#include <alphamap_fragment>\n	#include <alphatest_fragment>\n	#include <alphahash_fragment>\n	float dist = length( vWorldPosition - referencePosition );\n	dist = ( dist - nearDistance ) / ( farDistance - nearDistance );\n	dist = saturate( dist );\n	gl_FragColor = packDepthToRGBA( dist );\n}", equirect_vert: "varying vec3 vWorldDirection;\n#include <common>\nvoid main() {\n	vWorldDirection = transformDirection( position, modelMatrix );\n	#include <begin_vertex>\n	#include <project_vertex>\n}", equirect_frag: "uniform sampler2D tEquirect;\nvarying vec3 vWorldDirection;\n#include <common>\nvoid main() {\n	vec3 direction = normalize( vWorldDirection );\n	vec2 sampleUV = equirectUv( direction );\n	gl_FragColor = texture2D( tEquirect, sampleUV );\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n}", linedashed_vert: "uniform float scale;\nattribute float lineDistance;\nvarying float vLineDistance;\n#include <common>\n#include <uv_pars_vertex>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n	vLineDistance = scale * lineDistance;\n	#include <uv_vertex>\n	#include <color_vertex>\n	#include <morphinstance_vertex>\n	#include <morphcolor_vertex>\n	#include <begin_vertex>\n	#include <morphtarget_vertex>\n	#include <project_vertex>\n	#include <logdepthbuf_vertex>\n	#include <clipping_planes_vertex>\n	#include <fog_vertex>\n}", linedashed_frag: "uniform vec3 diffuse;\nuniform float opacity;\nuniform float dashSize;\nuniform float totalSize;\nvarying float vLineDistance;\n#include <common>\n#include <color_pars_fragment>\n#include <uv_pars_fragment>\n#include <map_pars_fragment>\n#include <fog_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n	vec4 diffuseColor = vec4( diffuse, opacity );\n	#include <clipping_planes_fragment>\n	if ( mod( vLineDistance, totalSize ) > dashSize ) {\n		discard;\n	}\n	vec3 outgoingLight = vec3( 0.0 );\n	#include <logdepthbuf_fragment>\n	#include <map_fragment>\n	#include <color_fragment>\n	outgoingLight = diffuseColor.rgb;\n	#include <opaque_fragment>\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n	#include <fog_fragment>\n	#include <premultiplied_alpha_fragment>\n}", meshbasic_vert: "#include <common>\n#include <batching_pars_vertex>\n#include <uv_pars_vertex>\n#include <envmap_pars_vertex>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n	#include <uv_vertex>\n	#include <color_vertex>\n	#include <morphinstance_vertex>\n	#include <morphcolor_vertex>\n	#include <batching_vertex>\n	#if defined ( USE_ENVMAP ) || defined ( USE_SKINNING )\n		#include <beginnormal_vertex>\n		#include <morphnormal_vertex>\n		#include <skinbase_vertex>\n		#include <skinnormal_vertex>\n		#include <defaultnormal_vertex>\n	#endif\n	#include <begin_vertex>\n	#include <morphtarget_vertex>\n	#include <skinning_vertex>\n	#include <project_vertex>\n	#include <logdepthbuf_vertex>\n	#include <clipping_planes_vertex>\n	#include <worldpos_vertex>\n	#include <envmap_vertex>\n	#include <fog_vertex>\n}", meshbasic_frag: "uniform vec3 diffuse;\nuniform float opacity;\n#ifndef FLAT_SHADED\n	varying vec3 vNormal;\n#endif\n#include <common>\n#include <dithering_pars_fragment>\n#include <color_pars_fragment>\n#include <uv_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <alphatest_pars_fragment>\n#include <alphahash_pars_fragment>\n#include <aomap_pars_fragment>\n#include <lightmap_pars_fragment>\n#include <envmap_common_pars_fragment>\n#include <envmap_pars_fragment>\n#include <fog_pars_fragment>\n#include <specularmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n	vec4 diffuseColor = vec4( diffuse, opacity );\n	#include <clipping_planes_fragment>\n	#include <logdepthbuf_fragment>\n	#include <map_fragment>\n	#include <color_fragment>\n	#include <alphamap_fragment>\n	#include <alphatest_fragment>\n	#include <alphahash_fragment>\n	#include <specularmap_fragment>\n	ReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );\n	#ifdef USE_LIGHTMAP\n		vec4 lightMapTexel = texture2D( lightMap, vLightMapUv );\n		reflectedLight.indirectDiffuse += lightMapTexel.rgb * lightMapIntensity * RECIPROCAL_PI;\n	#else\n		reflectedLight.indirectDiffuse += vec3( 1.0 );\n	#endif\n	#include <aomap_fragment>\n	reflectedLight.indirectDiffuse *= diffuseColor.rgb;\n	vec3 outgoingLight = reflectedLight.indirectDiffuse;\n	#include <envmap_fragment>\n	#include <opaque_fragment>\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n	#include <fog_fragment>\n	#include <premultiplied_alpha_fragment>\n	#include <dithering_fragment>\n}", meshlambert_vert: "#define LAMBERT\nvarying vec3 vViewPosition;\n#include <common>\n#include <batching_pars_vertex>\n#include <uv_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <envmap_pars_vertex>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <normal_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <shadowmap_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n	#include <uv_vertex>\n	#include <color_vertex>\n	#include <morphinstance_vertex>\n	#include <morphcolor_vertex>\n	#include <batching_vertex>\n	#include <beginnormal_vertex>\n	#include <morphnormal_vertex>\n	#include <skinbase_vertex>\n	#include <skinnormal_vertex>\n	#include <defaultnormal_vertex>\n	#include <normal_vertex>\n	#include <begin_vertex>\n	#include <morphtarget_vertex>\n	#include <skinning_vertex>\n	#include <displacementmap_vertex>\n	#include <project_vertex>\n	#include <logdepthbuf_vertex>\n	#include <clipping_planes_vertex>\n	vViewPosition = - mvPosition.xyz;\n	#include <worldpos_vertex>\n	#include <envmap_vertex>\n	#include <shadowmap_vertex>\n	#include <fog_vertex>\n}", meshlambert_frag: "#define LAMBERT\nuniform vec3 diffuse;\nuniform vec3 emissive;\nuniform float opacity;\n#include <common>\n#include <packing>\n#include <dithering_pars_fragment>\n#include <color_pars_fragment>\n#include <uv_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <alphatest_pars_fragment>\n#include <alphahash_pars_fragment>\n#include <aomap_pars_fragment>\n#include <lightmap_pars_fragment>\n#include <emissivemap_pars_fragment>\n#include <envmap_common_pars_fragment>\n#include <envmap_pars_fragment>\n#include <fog_pars_fragment>\n#include <bsdfs>\n#include <lights_pars_begin>\n#include <normal_pars_fragment>\n#include <lights_lambert_pars_fragment>\n#include <shadowmap_pars_fragment>\n#include <bumpmap_pars_fragment>\n#include <normalmap_pars_fragment>\n#include <specularmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n	vec4 diffuseColor = vec4( diffuse, opacity );\n	#include <clipping_planes_fragment>\n	ReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );\n	vec3 totalEmissiveRadiance = emissive;\n	#include <logdepthbuf_fragment>\n	#include <map_fragment>\n	#include <color_fragment>\n	#include <alphamap_fragment>\n	#include <alphatest_fragment>\n	#include <alphahash_fragment>\n	#include <specularmap_fragment>\n	#include <normal_fragment_begin>\n	#include <normal_fragment_maps>\n	#include <emissivemap_fragment>\n	#include <lights_lambert_fragment>\n	#include <lights_fragment_begin>\n	#include <lights_fragment_maps>\n	#include <lights_fragment_end>\n	#include <aomap_fragment>\n	vec3 outgoingLight = reflectedLight.directDiffuse + reflectedLight.indirectDiffuse + totalEmissiveRadiance;\n	#include <envmap_fragment>\n	#include <opaque_fragment>\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n	#include <fog_fragment>\n	#include <premultiplied_alpha_fragment>\n	#include <dithering_fragment>\n}", meshmatcap_vert: "#define MATCAP\nvarying vec3 vViewPosition;\n#include <common>\n#include <batching_pars_vertex>\n#include <uv_pars_vertex>\n#include <color_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <fog_pars_vertex>\n#include <normal_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n	#include <uv_vertex>\n	#include <color_vertex>\n	#include <morphinstance_vertex>\n	#include <morphcolor_vertex>\n	#include <batching_vertex>\n	#include <beginnormal_vertex>\n	#include <morphnormal_vertex>\n	#include <skinbase_vertex>\n	#include <skinnormal_vertex>\n	#include <defaultnormal_vertex>\n	#include <normal_vertex>\n	#include <begin_vertex>\n	#include <morphtarget_vertex>\n	#include <skinning_vertex>\n	#include <displacementmap_vertex>\n	#include <project_vertex>\n	#include <logdepthbuf_vertex>\n	#include <clipping_planes_vertex>\n	#include <fog_vertex>\n	vViewPosition = - mvPosition.xyz;\n}", meshmatcap_frag: "#define MATCAP\nuniform vec3 diffuse;\nuniform float opacity;\nuniform sampler2D matcap;\nvarying vec3 vViewPosition;\n#include <common>\n#include <dithering_pars_fragment>\n#include <color_pars_fragment>\n#include <uv_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <alphatest_pars_fragment>\n#include <alphahash_pars_fragment>\n#include <fog_pars_fragment>\n#include <normal_pars_fragment>\n#include <bumpmap_pars_fragment>\n#include <normalmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n	vec4 diffuseColor = vec4( diffuse, opacity );\n	#include <clipping_planes_fragment>\n	#include <logdepthbuf_fragment>\n	#include <map_fragment>\n	#include <color_fragment>\n	#include <alphamap_fragment>\n	#include <alphatest_fragment>\n	#include <alphahash_fragment>\n	#include <normal_fragment_begin>\n	#include <normal_fragment_maps>\n	vec3 viewDir = normalize( vViewPosition );\n	vec3 x = normalize( vec3( viewDir.z, 0.0, - viewDir.x ) );\n	vec3 y = cross( viewDir, x );\n	vec2 uv = vec2( dot( x, normal ), dot( y, normal ) ) * 0.495 + 0.5;\n	#ifdef USE_MATCAP\n		vec4 matcapColor = texture2D( matcap, uv );\n	#else\n		vec4 matcapColor = vec4( vec3( mix( 0.2, 0.8, uv.y ) ), 1.0 );\n	#endif\n	vec3 outgoingLight = diffuseColor.rgb * matcapColor.rgb;\n	#include <opaque_fragment>\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n	#include <fog_fragment>\n	#include <premultiplied_alpha_fragment>\n	#include <dithering_fragment>\n}", meshnormal_vert: "#define NORMAL\n#if defined( FLAT_SHADED ) || defined( USE_BUMPMAP ) || defined( USE_NORMALMAP_TANGENTSPACE )\n	varying vec3 vViewPosition;\n#endif\n#include <common>\n#include <batching_pars_vertex>\n#include <uv_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <normal_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n	#include <uv_vertex>\n	#include <batching_vertex>\n	#include <beginnormal_vertex>\n	#include <morphinstance_vertex>\n	#include <morphnormal_vertex>\n	#include <skinbase_vertex>\n	#include <skinnormal_vertex>\n	#include <defaultnormal_vertex>\n	#include <normal_vertex>\n	#include <begin_vertex>\n	#include <morphtarget_vertex>\n	#include <skinning_vertex>\n	#include <displacementmap_vertex>\n	#include <project_vertex>\n	#include <logdepthbuf_vertex>\n	#include <clipping_planes_vertex>\n#if defined( FLAT_SHADED ) || defined( USE_BUMPMAP ) || defined( USE_NORMALMAP_TANGENTSPACE )\n	vViewPosition = - mvPosition.xyz;\n#endif\n}", meshnormal_frag: "#define NORMAL\nuniform float opacity;\n#if defined( FLAT_SHADED ) || defined( USE_BUMPMAP ) || defined( USE_NORMALMAP_TANGENTSPACE )\n	varying vec3 vViewPosition;\n#endif\n#include <packing>\n#include <uv_pars_fragment>\n#include <normal_pars_fragment>\n#include <bumpmap_pars_fragment>\n#include <normalmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n	vec4 diffuseColor = vec4( 0.0, 0.0, 0.0, opacity );\n	#include <clipping_planes_fragment>\n	#include <logdepthbuf_fragment>\n	#include <normal_fragment_begin>\n	#include <normal_fragment_maps>\n	gl_FragColor = vec4( packNormalToRGB( normal ), diffuseColor.a );\n	#ifdef OPAQUE\n		gl_FragColor.a = 1.0;\n	#endif\n}", meshphong_vert: "#define PHONG\nvarying vec3 vViewPosition;\n#include <common>\n#include <batching_pars_vertex>\n#include <uv_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <envmap_pars_vertex>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <normal_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <shadowmap_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n	#include <uv_vertex>\n	#include <color_vertex>\n	#include <morphcolor_vertex>\n	#include <batching_vertex>\n	#include <beginnormal_vertex>\n	#include <morphinstance_vertex>\n	#include <morphnormal_vertex>\n	#include <skinbase_vertex>\n	#include <skinnormal_vertex>\n	#include <defaultnormal_vertex>\n	#include <normal_vertex>\n	#include <begin_vertex>\n	#include <morphtarget_vertex>\n	#include <skinning_vertex>\n	#include <displacementmap_vertex>\n	#include <project_vertex>\n	#include <logdepthbuf_vertex>\n	#include <clipping_planes_vertex>\n	vViewPosition = - mvPosition.xyz;\n	#include <worldpos_vertex>\n	#include <envmap_vertex>\n	#include <shadowmap_vertex>\n	#include <fog_vertex>\n}", meshphong_frag: "#define PHONG\nuniform vec3 diffuse;\nuniform vec3 emissive;\nuniform vec3 specular;\nuniform float shininess;\nuniform float opacity;\n#include <common>\n#include <packing>\n#include <dithering_pars_fragment>\n#include <color_pars_fragment>\n#include <uv_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <alphatest_pars_fragment>\n#include <alphahash_pars_fragment>\n#include <aomap_pars_fragment>\n#include <lightmap_pars_fragment>\n#include <emissivemap_pars_fragment>\n#include <envmap_common_pars_fragment>\n#include <envmap_pars_fragment>\n#include <fog_pars_fragment>\n#include <bsdfs>\n#include <lights_pars_begin>\n#include <normal_pars_fragment>\n#include <lights_phong_pars_fragment>\n#include <shadowmap_pars_fragment>\n#include <bumpmap_pars_fragment>\n#include <normalmap_pars_fragment>\n#include <specularmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n	vec4 diffuseColor = vec4( diffuse, opacity );\n	#include <clipping_planes_fragment>\n	ReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );\n	vec3 totalEmissiveRadiance = emissive;\n	#include <logdepthbuf_fragment>\n	#include <map_fragment>\n	#include <color_fragment>\n	#include <alphamap_fragment>\n	#include <alphatest_fragment>\n	#include <alphahash_fragment>\n	#include <specularmap_fragment>\n	#include <normal_fragment_begin>\n	#include <normal_fragment_maps>\n	#include <emissivemap_fragment>\n	#include <lights_phong_fragment>\n	#include <lights_fragment_begin>\n	#include <lights_fragment_maps>\n	#include <lights_fragment_end>\n	#include <aomap_fragment>\n	vec3 outgoingLight = reflectedLight.directDiffuse + reflectedLight.indirectDiffuse + reflectedLight.directSpecular + reflectedLight.indirectSpecular + totalEmissiveRadiance;\n	#include <envmap_fragment>\n	#include <opaque_fragment>\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n	#include <fog_fragment>\n	#include <premultiplied_alpha_fragment>\n	#include <dithering_fragment>\n}", meshphysical_vert: "#define STANDARD\nvarying vec3 vViewPosition;\n#ifdef USE_TRANSMISSION\n	varying vec3 vWorldPosition;\n#endif\n#include <common>\n#include <batching_pars_vertex>\n#include <uv_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <normal_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <shadowmap_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n	#include <uv_vertex>\n	#include <color_vertex>\n	#include <morphinstance_vertex>\n	#include <morphcolor_vertex>\n	#include <batching_vertex>\n	#include <beginnormal_vertex>\n	#include <morphnormal_vertex>\n	#include <skinbase_vertex>\n	#include <skinnormal_vertex>\n	#include <defaultnormal_vertex>\n	#include <normal_vertex>\n	#include <begin_vertex>\n	#include <morphtarget_vertex>\n	#include <skinning_vertex>\n	#include <displacementmap_vertex>\n	#include <project_vertex>\n	#include <logdepthbuf_vertex>\n	#include <clipping_planes_vertex>\n	vViewPosition = - mvPosition.xyz;\n	#include <worldpos_vertex>\n	#include <shadowmap_vertex>\n	#include <fog_vertex>\n#ifdef USE_TRANSMISSION\n	vWorldPosition = worldPosition.xyz;\n#endif\n}", meshphysical_frag: "#define STANDARD\n#ifdef PHYSICAL\n	#define IOR\n	#define USE_SPECULAR\n#endif\nuniform vec3 diffuse;\nuniform vec3 emissive;\nuniform float roughness;\nuniform float metalness;\nuniform float opacity;\n#ifdef IOR\n	uniform float ior;\n#endif\n#ifdef USE_SPECULAR\n	uniform float specularIntensity;\n	uniform vec3 specularColor;\n	#ifdef USE_SPECULAR_COLORMAP\n		uniform sampler2D specularColorMap;\n	#endif\n	#ifdef USE_SPECULAR_INTENSITYMAP\n		uniform sampler2D specularIntensityMap;\n	#endif\n#endif\n#ifdef USE_CLEARCOAT\n	uniform float clearcoat;\n	uniform float clearcoatRoughness;\n#endif\n#ifdef USE_DISPERSION\n	uniform float dispersion;\n#endif\n#ifdef USE_IRIDESCENCE\n	uniform float iridescence;\n	uniform float iridescenceIOR;\n	uniform float iridescenceThicknessMinimum;\n	uniform float iridescenceThicknessMaximum;\n#endif\n#ifdef USE_SHEEN\n	uniform vec3 sheenColor;\n	uniform float sheenRoughness;\n	#ifdef USE_SHEEN_COLORMAP\n		uniform sampler2D sheenColorMap;\n	#endif\n	#ifdef USE_SHEEN_ROUGHNESSMAP\n		uniform sampler2D sheenRoughnessMap;\n	#endif\n#endif\n#ifdef USE_ANISOTROPY\n	uniform vec2 anisotropyVector;\n	#ifdef USE_ANISOTROPYMAP\n		uniform sampler2D anisotropyMap;\n	#endif\n#endif\nvarying vec3 vViewPosition;\n#include <common>\n#include <packing>\n#include <dithering_pars_fragment>\n#include <color_pars_fragment>\n#include <uv_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <alphatest_pars_fragment>\n#include <alphahash_pars_fragment>\n#include <aomap_pars_fragment>\n#include <lightmap_pars_fragment>\n#include <emissivemap_pars_fragment>\n#include <iridescence_fragment>\n#include <cube_uv_reflection_fragment>\n#include <envmap_common_pars_fragment>\n#include <envmap_physical_pars_fragment>\n#include <fog_pars_fragment>\n#include <lights_pars_begin>\n#include <normal_pars_fragment>\n#include <lights_physical_pars_fragment>\n#include <transmission_pars_fragment>\n#include <shadowmap_pars_fragment>\n#include <bumpmap_pars_fragment>\n#include <normalmap_pars_fragment>\n#include <clearcoat_pars_fragment>\n#include <iridescence_pars_fragment>\n#include <roughnessmap_pars_fragment>\n#include <metalnessmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n	vec4 diffuseColor = vec4( diffuse, opacity );\n	#include <clipping_planes_fragment>\n	ReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );\n	vec3 totalEmissiveRadiance = emissive;\n	#include <logdepthbuf_fragment>\n	#include <map_fragment>\n	#include <color_fragment>\n	#include <alphamap_fragment>\n	#include <alphatest_fragment>\n	#include <alphahash_fragment>\n	#include <roughnessmap_fragment>\n	#include <metalnessmap_fragment>\n	#include <normal_fragment_begin>\n	#include <normal_fragment_maps>\n	#include <clearcoat_normal_fragment_begin>\n	#include <clearcoat_normal_fragment_maps>\n	#include <emissivemap_fragment>\n	#include <lights_physical_fragment>\n	#include <lights_fragment_begin>\n	#include <lights_fragment_maps>\n	#include <lights_fragment_end>\n	#include <aomap_fragment>\n	vec3 totalDiffuse = reflectedLight.directDiffuse + reflectedLight.indirectDiffuse;\n	vec3 totalSpecular = reflectedLight.directSpecular + reflectedLight.indirectSpecular;\n	#include <transmission_fragment>\n	vec3 outgoingLight = totalDiffuse + totalSpecular + totalEmissiveRadiance;\n	#ifdef USE_SHEEN\n		float sheenEnergyComp = 1.0 - 0.157 * max3( material.sheenColor );\n		outgoingLight = outgoingLight * sheenEnergyComp + sheenSpecularDirect + sheenSpecularIndirect;\n	#endif\n	#ifdef USE_CLEARCOAT\n		float dotNVcc = saturate( dot( geometryClearcoatNormal, geometryViewDir ) );\n		vec3 Fcc = F_Schlick( material.clearcoatF0, material.clearcoatF90, dotNVcc );\n		outgoingLight = outgoingLight * ( 1.0 - material.clearcoat * Fcc ) + ( clearcoatSpecularDirect + clearcoatSpecularIndirect ) * material.clearcoat;\n	#endif\n	#include <opaque_fragment>\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n	#include <fog_fragment>\n	#include <premultiplied_alpha_fragment>\n	#include <dithering_fragment>\n}", meshtoon_vert: "#define TOON\nvarying vec3 vViewPosition;\n#include <common>\n#include <batching_pars_vertex>\n#include <uv_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <normal_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <shadowmap_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n	#include <uv_vertex>\n	#include <color_vertex>\n	#include <morphinstance_vertex>\n	#include <morphcolor_vertex>\n	#include <batching_vertex>\n	#include <beginnormal_vertex>\n	#include <morphnormal_vertex>\n	#include <skinbase_vertex>\n	#include <skinnormal_vertex>\n	#include <defaultnormal_vertex>\n	#include <normal_vertex>\n	#include <begin_vertex>\n	#include <morphtarget_vertex>\n	#include <skinning_vertex>\n	#include <displacementmap_vertex>\n	#include <project_vertex>\n	#include <logdepthbuf_vertex>\n	#include <clipping_planes_vertex>\n	vViewPosition = - mvPosition.xyz;\n	#include <worldpos_vertex>\n	#include <shadowmap_vertex>\n	#include <fog_vertex>\n}", meshtoon_frag: "#define TOON\nuniform vec3 diffuse;\nuniform vec3 emissive;\nuniform float opacity;\n#include <common>\n#include <packing>\n#include <dithering_pars_fragment>\n#include <color_pars_fragment>\n#include <uv_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <alphatest_pars_fragment>\n#include <alphahash_pars_fragment>\n#include <aomap_pars_fragment>\n#include <lightmap_pars_fragment>\n#include <emissivemap_pars_fragment>\n#include <gradientmap_pars_fragment>\n#include <fog_pars_fragment>\n#include <bsdfs>\n#include <lights_pars_begin>\n#include <normal_pars_fragment>\n#include <lights_toon_pars_fragment>\n#include <shadowmap_pars_fragment>\n#include <bumpmap_pars_fragment>\n#include <normalmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n	vec4 diffuseColor = vec4( diffuse, opacity );\n	#include <clipping_planes_fragment>\n	ReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );\n	vec3 totalEmissiveRadiance = emissive;\n	#include <logdepthbuf_fragment>\n	#include <map_fragment>\n	#include <color_fragment>\n	#include <alphamap_fragment>\n	#include <alphatest_fragment>\n	#include <alphahash_fragment>\n	#include <normal_fragment_begin>\n	#include <normal_fragment_maps>\n	#include <emissivemap_fragment>\n	#include <lights_toon_fragment>\n	#include <lights_fragment_begin>\n	#include <lights_fragment_maps>\n	#include <lights_fragment_end>\n	#include <aomap_fragment>\n	vec3 outgoingLight = reflectedLight.directDiffuse + reflectedLight.indirectDiffuse + totalEmissiveRadiance;\n	#include <opaque_fragment>\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n	#include <fog_fragment>\n	#include <premultiplied_alpha_fragment>\n	#include <dithering_fragment>\n}", points_vert: "uniform float size;\nuniform float scale;\n#include <common>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\n#ifdef USE_POINTS_UV\n	varying vec2 vUv;\n	uniform mat3 uvTransform;\n#endif\nvoid main() {\n	#ifdef USE_POINTS_UV\n		vUv = ( uvTransform * vec3( uv, 1 ) ).xy;\n	#endif\n	#include <color_vertex>\n	#include <morphinstance_vertex>\n	#include <morphcolor_vertex>\n	#include <begin_vertex>\n	#include <morphtarget_vertex>\n	#include <project_vertex>\n	gl_PointSize = size;\n	#ifdef USE_SIZEATTENUATION\n		bool isPerspective = isPerspectiveMatrix( projectionMatrix );\n		if ( isPerspective ) gl_PointSize *= ( scale / - mvPosition.z );\n	#endif\n	#include <logdepthbuf_vertex>\n	#include <clipping_planes_vertex>\n	#include <worldpos_vertex>\n	#include <fog_vertex>\n}", points_frag: "uniform vec3 diffuse;\nuniform float opacity;\n#include <common>\n#include <color_pars_fragment>\n#include <map_particle_pars_fragment>\n#include <alphatest_pars_fragment>\n#include <alphahash_pars_fragment>\n#include <fog_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n	vec4 diffuseColor = vec4( diffuse, opacity );\n	#include <clipping_planes_fragment>\n	vec3 outgoingLight = vec3( 0.0 );\n	#include <logdepthbuf_fragment>\n	#include <map_particle_fragment>\n	#include <color_fragment>\n	#include <alphatest_fragment>\n	#include <alphahash_fragment>\n	outgoingLight = diffuseColor.rgb;\n	#include <opaque_fragment>\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n	#include <fog_fragment>\n	#include <premultiplied_alpha_fragment>\n}", shadow_vert: "#include <common>\n#include <batching_pars_vertex>\n#include <fog_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <shadowmap_pars_vertex>\nvoid main() {\n	#include <batching_vertex>\n	#include <beginnormal_vertex>\n	#include <morphinstance_vertex>\n	#include <morphnormal_vertex>\n	#include <skinbase_vertex>\n	#include <skinnormal_vertex>\n	#include <defaultnormal_vertex>\n	#include <begin_vertex>\n	#include <morphtarget_vertex>\n	#include <skinning_vertex>\n	#include <project_vertex>\n	#include <logdepthbuf_vertex>\n	#include <worldpos_vertex>\n	#include <shadowmap_vertex>\n	#include <fog_vertex>\n}", shadow_frag: "uniform vec3 color;\nuniform float opacity;\n#include <common>\n#include <packing>\n#include <fog_pars_fragment>\n#include <bsdfs>\n#include <lights_pars_begin>\n#include <logdepthbuf_pars_fragment>\n#include <shadowmap_pars_fragment>\n#include <shadowmask_pars_fragment>\nvoid main() {\n	#include <logdepthbuf_fragment>\n	gl_FragColor = vec4( color, opacity * ( 1.0 - getShadowMask() ) );\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n	#include <fog_fragment>\n}", sprite_vert: "uniform float rotation;\nuniform vec2 center;\n#include <common>\n#include <uv_pars_vertex>\n#include <fog_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n	#include <uv_vertex>\n	vec4 mvPosition = modelViewMatrix[ 3 ];\n	vec2 scale = vec2( length( modelMatrix[ 0 ].xyz ), length( modelMatrix[ 1 ].xyz ) );\n	#ifndef USE_SIZEATTENUATION\n		bool isPerspective = isPerspectiveMatrix( projectionMatrix );\n		if ( isPerspective ) scale *= - mvPosition.z;\n	#endif\n	vec2 alignedPosition = ( position.xy - ( center - vec2( 0.5 ) ) ) * scale;\n	vec2 rotatedPosition;\n	rotatedPosition.x = cos( rotation ) * alignedPosition.x - sin( rotation ) * alignedPosition.y;\n	rotatedPosition.y = sin( rotation ) * alignedPosition.x + cos( rotation ) * alignedPosition.y;\n	mvPosition.xy += rotatedPosition;\n	gl_Position = projectionMatrix * mvPosition;\n	#include <logdepthbuf_vertex>\n	#include <clipping_planes_vertex>\n	#include <fog_vertex>\n}", sprite_frag: "uniform vec3 diffuse;\nuniform float opacity;\n#include <common>\n#include <uv_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <alphatest_pars_fragment>\n#include <alphahash_pars_fragment>\n#include <fog_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n	vec4 diffuseColor = vec4( diffuse, opacity );\n	#include <clipping_planes_fragment>\n	vec3 outgoingLight = vec3( 0.0 );\n	#include <logdepthbuf_fragment>\n	#include <map_fragment>\n	#include <alphamap_fragment>\n	#include <alphatest_fragment>\n	#include <alphahash_fragment>\n	outgoingLight = diffuseColor.rgb;\n	#include <opaque_fragment>\n	#include <tonemapping_fragment>\n	#include <colorspace_fragment>\n	#include <fog_fragment>\n}" }, Dn = { common: { diffuse: { value: new _mpChunkDeps_three_build_three_core_min.$r(16777215) }, opacity: { value: 1 }, map: { value: null }, mapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() }, alphaMap: { value: null }, alphaMapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() }, alphaTest: { value: 0 } }, specularmap: { specularMap: { value: null }, specularMapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() } }, envmap: { envMap: { value: null }, envMapRotation: { value: new _mpChunkDeps_three_build_three_core_min.Gs() }, flipEnvMap: { value: -1 }, reflectivity: { value: 1 }, ior: { value: 1.5 }, refractionRatio: { value: 0.98 } }, aomap: { aoMap: { value: null }, aoMapIntensity: { value: 1 }, aoMapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() } }, lightmap: { lightMap: { value: null }, lightMapIntensity: { value: 1 }, lightMapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() } }, bumpmap: { bumpMap: { value: null }, bumpMapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() }, bumpScale: { value: 1 } }, normalmap: { normalMap: { value: null }, normalMapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() }, normalScale: { value: new _mpChunkDeps_three_build_three_core_min.Zs(1, 1) } }, displacementmap: { displacementMap: { value: null }, displacementMapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() }, displacementScale: { value: 1 }, displacementBias: { value: 0 } }, emissivemap: { emissiveMap: { value: null }, emissiveMapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() } }, metalnessmap: { metalnessMap: { value: null }, metalnessMapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() } }, roughnessmap: { roughnessMap: { value: null }, roughnessMapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() } }, gradientmap: { gradientMap: { value: null } }, fog: { fogDensity: { value: 25e-5 }, fogNear: { value: 1 }, fogFar: { value: 2e3 }, fogColor: { value: new _mpChunkDeps_three_build_three_core_min.$r(16777215) } }, lights: { ambientLightColor: { value: [] }, lightProbe: { value: [] }, directionalLights: { value: [], properties: { direction: {}, color: {} } }, directionalLightShadows: { value: [], properties: { shadowIntensity: 1, shadowBias: {}, shadowNormalBias: {}, shadowRadius: {}, shadowMapSize: {} } }, directionalShadowMap: { value: [] }, directionalShadowMatrix: { value: [] }, spotLights: { value: [], properties: { color: {}, position: {}, direction: {}, distance: {}, coneCos: {}, penumbraCos: {}, decay: {} } }, spotLightShadows: { value: [], properties: { shadowIntensity: 1, shadowBias: {}, shadowNormalBias: {}, shadowRadius: {}, shadowMapSize: {} } }, spotLightMap: { value: [] }, spotShadowMap: { value: [] }, spotLightMatrix: { value: [] }, pointLights: { value: [], properties: { color: {}, position: {}, decay: {}, distance: {} } }, pointLightShadows: { value: [], properties: { shadowIntensity: 1, shadowBias: {}, shadowNormalBias: {}, shadowRadius: {}, shadowMapSize: {}, shadowCameraNear: {}, shadowCameraFar: {} } }, pointShadowMap: { value: [] }, pointShadowMatrix: { value: [] }, hemisphereLights: { value: [], properties: { direction: {}, skyColor: {}, groundColor: {} } }, rectAreaLights: { value: [], properties: { color: {}, position: {}, width: {}, height: {} } }, ltc_1: { value: null }, ltc_2: { value: null } }, points: { diffuse: { value: new _mpChunkDeps_three_build_three_core_min.$r(16777215) }, opacity: { value: 1 }, size: { value: 1 }, scale: { value: 1 }, map: { value: null }, alphaMap: { value: null }, alphaMapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() }, alphaTest: { value: 0 }, uvTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() } }, sprite: { diffuse: { value: new _mpChunkDeps_three_build_three_core_min.$r(16777215) }, opacity: { value: 1 }, center: { value: new _mpChunkDeps_three_build_three_core_min.Zs(0.5, 0.5) }, rotation: { value: 0 }, map: { value: null }, mapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() }, alphaMap: { value: null }, alphaMapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() }, alphaTest: { value: 0 } } }, yn = { basic: { uniforms: _mpChunkDeps_three_build_three_core_min.Dn([Dn.common, Dn.specularmap, Dn.envmap, Dn.aomap, Dn.lightmap, Dn.fog]), vertexShader: wn.meshbasic_vert, fragmentShader: wn.meshbasic_frag }, lambert: { uniforms: _mpChunkDeps_three_build_three_core_min.Dn([Dn.common, Dn.specularmap, Dn.envmap, Dn.aomap, Dn.lightmap, Dn.emissivemap, Dn.bumpmap, Dn.normalmap, Dn.displacementmap, Dn.fog, Dn.lights, { emissive: { value: new _mpChunkDeps_three_build_three_core_min.$r(0) } }]), vertexShader: wn.meshlambert_vert, fragmentShader: wn.meshlambert_frag }, phong: { uniforms: _mpChunkDeps_three_build_three_core_min.Dn([Dn.common, Dn.specularmap, Dn.envmap, Dn.aomap, Dn.lightmap, Dn.emissivemap, Dn.bumpmap, Dn.normalmap, Dn.displacementmap, Dn.fog, Dn.lights, { emissive: { value: new _mpChunkDeps_three_build_three_core_min.$r(0) }, specular: { value: new _mpChunkDeps_three_build_three_core_min.$r(1118481) }, shininess: { value: 30 } }]), vertexShader: wn.meshphong_vert, fragmentShader: wn.meshphong_frag }, standard: { uniforms: _mpChunkDeps_three_build_three_core_min.Dn([Dn.common, Dn.envmap, Dn.aomap, Dn.lightmap, Dn.emissivemap, Dn.bumpmap, Dn.normalmap, Dn.displacementmap, Dn.roughnessmap, Dn.metalnessmap, Dn.fog, Dn.lights, { emissive: { value: new _mpChunkDeps_three_build_three_core_min.$r(0) }, roughness: { value: 1 }, metalness: { value: 0 }, envMapIntensity: { value: 1 } }]), vertexShader: wn.meshphysical_vert, fragmentShader: wn.meshphysical_frag }, toon: { uniforms: _mpChunkDeps_three_build_three_core_min.Dn([Dn.common, Dn.aomap, Dn.lightmap, Dn.emissivemap, Dn.bumpmap, Dn.normalmap, Dn.displacementmap, Dn.gradientmap, Dn.fog, Dn.lights, { emissive: { value: new _mpChunkDeps_three_build_three_core_min.$r(0) } }]), vertexShader: wn.meshtoon_vert, fragmentShader: wn.meshtoon_frag }, matcap: { uniforms: _mpChunkDeps_three_build_three_core_min.Dn([Dn.common, Dn.bumpmap, Dn.normalmap, Dn.displacementmap, Dn.fog, { matcap: { value: null } }]), vertexShader: wn.meshmatcap_vert, fragmentShader: wn.meshmatcap_frag }, points: { uniforms: _mpChunkDeps_three_build_three_core_min.Dn([Dn.points, Dn.fog]), vertexShader: wn.points_vert, fragmentShader: wn.points_frag }, dashed: { uniforms: _mpChunkDeps_three_build_three_core_min.Dn([Dn.common, Dn.fog, { scale: { value: 1 }, dashSize: { value: 1 }, totalSize: { value: 2 } }]), vertexShader: wn.linedashed_vert, fragmentShader: wn.linedashed_frag }, depth: { uniforms: _mpChunkDeps_three_build_three_core_min.Dn([Dn.common, Dn.displacementmap]), vertexShader: wn.depth_vert, fragmentShader: wn.depth_frag }, normal: { uniforms: _mpChunkDeps_three_build_three_core_min.Dn([Dn.common, Dn.bumpmap, Dn.normalmap, Dn.displacementmap, { opacity: { value: 1 } }]), vertexShader: wn.meshnormal_vert, fragmentShader: wn.meshnormal_frag }, sprite: { uniforms: _mpChunkDeps_three_build_three_core_min.Dn([Dn.sprite, Dn.fog]), vertexShader: wn.sprite_vert, fragmentShader: wn.sprite_frag }, background: { uniforms: { uvTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() }, t2D: { value: null }, backgroundIntensity: { value: 1 } }, vertexShader: wn.background_vert, fragmentShader: wn.background_frag }, backgroundCube: { uniforms: { envMap: { value: null }, flipEnvMap: { value: -1 }, backgroundBlurriness: { value: 0 }, backgroundIntensity: { value: 1 }, backgroundRotation: { value: new _mpChunkDeps_three_build_three_core_min.Gs() } }, vertexShader: wn.backgroundCube_vert, fragmentShader: wn.backgroundCube_frag }, cube: { uniforms: { tCube: { value: null }, tFlip: { value: -1 }, opacity: { value: 1 } }, vertexShader: wn.cube_vert, fragmentShader: wn.cube_frag }, equirect: { uniforms: { tEquirect: { value: null } }, vertexShader: wn.equirect_vert, fragmentShader: wn.equirect_frag }, distanceRGBA: { uniforms: _mpChunkDeps_three_build_three_core_min.Dn([Dn.common, Dn.displacementmap, { referencePosition: { value: new _mpChunkDeps_three_build_three_core_min.Ii() }, nearDistance: { value: 1 }, farDistance: { value: 1e3 } }]), vertexShader: wn.distanceRGBA_vert, fragmentShader: wn.distanceRGBA_frag }, shadow: { uniforms: _mpChunkDeps_three_build_three_core_min.Dn([Dn.lights, Dn.fog, { color: { value: new _mpChunkDeps_three_build_three_core_min.$r(0) }, opacity: { value: 1 } }]), vertexShader: wn.shadow_vert, fragmentShader: wn.shadow_frag } };
yn.physical = { uniforms: _mpChunkDeps_three_build_three_core_min.Dn([yn.standard.uniforms, { clearcoat: { value: 0 }, clearcoatMap: { value: null }, clearcoatMapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() }, clearcoatNormalMap: { value: null }, clearcoatNormalMapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() }, clearcoatNormalScale: { value: new _mpChunkDeps_three_build_three_core_min.Zs(1, 1) }, clearcoatRoughness: { value: 0 }, clearcoatRoughnessMap: { value: null }, clearcoatRoughnessMapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() }, dispersion: { value: 0 }, iridescence: { value: 0 }, iridescenceMap: { value: null }, iridescenceMapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() }, iridescenceIOR: { value: 1.3 }, iridescenceThicknessMinimum: { value: 100 }, iridescenceThicknessMaximum: { value: 400 }, iridescenceThicknessMap: { value: null }, iridescenceThicknessMapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() }, sheen: { value: 0 }, sheenColor: { value: new _mpChunkDeps_three_build_three_core_min.$r(0) }, sheenColorMap: { value: null }, sheenColorMapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() }, sheenRoughness: { value: 1 }, sheenRoughnessMap: { value: null }, sheenRoughnessMapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() }, transmission: { value: 0 }, transmissionMap: { value: null }, transmissionMapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() }, transmissionSamplerSize: { value: new _mpChunkDeps_three_build_three_core_min.Zs() }, transmissionSamplerMap: { value: null }, thickness: { value: 0 }, thicknessMap: { value: null }, thicknessMapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() }, attenuationDistance: { value: 0 }, attenuationColor: { value: new _mpChunkDeps_three_build_three_core_min.$r(0) }, specularColor: { value: new _mpChunkDeps_three_build_three_core_min.$r(1, 1, 1) }, specularColorMap: { value: null }, specularColorMapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() }, specularIntensity: { value: 1 }, specularIntensityMap: { value: null }, specularIntensityMapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() }, anisotropyVector: { value: new _mpChunkDeps_three_build_three_core_min.Zs() }, anisotropyMap: { value: null }, anisotropyMapTransform: { value: new _mpChunkDeps_three_build_three_core_min.Gs() } }]), vertexShader: wn.meshphysical_vert, fragmentShader: wn.meshphysical_frag };
const In = { r: 0, b: 0, g: 0 }, Nn = new _mpChunkDeps_three_build_three_core_min.yr(), On = new _mpChunkDeps_three_build_three_core_min.nr();
function Fn(t, n, i, r, _, g, v) {
  const E = new _mpChunkDeps_three_build_three_core_min.$r(0);
  let S, T, M = true === g ? 0 : 1, x = null, R = 0, A = null;
  function b(e) {
    let t2 = true === e.isScene ? e.background : null;
    if (t2 && t2.isTexture) {
      t2 = (e.backgroundBlurriness > 0 ? i : n).get(t2);
    }
    return t2;
  }
  function C(e, n2) {
    e.getRGB(In, _mpChunkDeps_three_build_three_core_min.Hn(t)), r.buffers.color.setClear(In.r, In.g, In.b, n2, v);
  }
  return { getClearColor: function() {
    return E;
  }, setClearColor: function(e, t2 = 1) {
    E.set(e), M = t2, C(E, M);
  }, getClearAlpha: function() {
    return M;
  }, setClearAlpha: function(e) {
    M = e, C(E, M);
  }, render: function(e) {
    let n2 = false;
    const i2 = b(e);
    null === i2 ? C(E, M) : i2 && i2.isColor && (C(i2, 1), n2 = true);
    const a = t.xr.getEnvironmentBlendMode();
    "additive" === a ? r.buffers.color.setClear(0, 0, 0, 1, v) : "alpha-blend" === a && r.buffers.color.setClear(0, 0, 0, 0, v), (t.autoClear || n2) && (r.buffers.depth.setTest(true), r.buffers.depth.setMask(true), r.buffers.color.setMask(true), t.clear(t.autoClearColor, t.autoClearDepth, t.autoClearStencil));
  }, addToRenderList: function(e, n2) {
    const i2 = b(n2);
    i2 && (i2.isCubeTexture || i2.mapping === _mpChunkDeps_three_build_three_core_min.dt) ? (void 0 === T && (T = new _mpChunkDeps_three_build_three_core_min.Vn(new _mpChunkDeps_three_build_three_core_min.jn(1, 1, 1), new _mpChunkDeps_three_build_three_core_min.Jn({ name: "BackgroundCubeMaterial", uniforms: _mpChunkDeps_three_build_three_core_min.Un(yn.backgroundCube.uniforms), vertexShader: yn.backgroundCube.vertexShader, fragmentShader: yn.backgroundCube.fragmentShader, side: _mpChunkDeps_three_build_three_core_min.d, depthTest: false, depthWrite: false, fog: false })), T.geometry.deleteAttribute("normal"), T.geometry.deleteAttribute("uv"), T.onBeforeRender = function(e2, t2, n3) {
      this.matrixWorld.copyPosition(n3.matrixWorld);
    }, Object.defineProperty(T.material, "envMap", { get: function() {
      return this.uniforms.envMap.value;
    } }), _.update(T)), Nn.copy(n2.backgroundRotation), Nn.x *= -1, Nn.y *= -1, Nn.z *= -1, i2.isCubeTexture && false === i2.isRenderTargetTexture && (Nn.y *= -1, Nn.z *= -1), T.material.uniforms.envMap.value = i2, T.material.uniforms.flipEnvMap.value = i2.isCubeTexture && false === i2.isRenderTargetTexture ? -1 : 1, T.material.uniforms.backgroundBlurriness.value = n2.backgroundBlurriness, T.material.uniforms.backgroundIntensity.value = n2.backgroundIntensity, T.material.uniforms.backgroundRotation.value.setFromMatrix4(On.makeRotationFromEuler(Nn)), T.material.toneMapped = _mpChunkDeps_three_build_three_core_min.ui.getTransfer(i2.colorSpace) !== _mpChunkDeps_three_build_three_core_min.Ke, x === i2 && R === i2.version && A === t.toneMapping || (T.material.needsUpdate = true, x = i2, R = i2.version, A = t.toneMapping), T.layers.enableAll(), e.unshift(T, T.geometry, T.material, 0, 0, null)) : i2 && i2.isTexture && (void 0 === S && (S = new _mpChunkDeps_three_build_three_core_min.Vn(new _mpChunkDeps_three_build_three_core_min.ul(2, 2), new _mpChunkDeps_three_build_three_core_min.Jn({ name: "BackgroundMaterial", uniforms: _mpChunkDeps_three_build_three_core_min.Un(yn.background.uniforms), vertexShader: yn.background.vertexShader, fragmentShader: yn.background.fragmentShader, side: _mpChunkDeps_three_build_three_core_min.u, depthTest: false, depthWrite: false, fog: false })), S.geometry.deleteAttribute("normal"), Object.defineProperty(S.material, "map", { get: function() {
      return this.uniforms.t2D.value;
    } }), _.update(S)), S.material.uniforms.t2D.value = i2, S.material.uniforms.backgroundIntensity.value = n2.backgroundIntensity, S.material.toneMapped = _mpChunkDeps_three_build_three_core_min.ui.getTransfer(i2.colorSpace) !== _mpChunkDeps_three_build_three_core_min.Ke, true === i2.matrixAutoUpdate && i2.updateMatrix(), S.material.uniforms.uvTransform.value.copy(i2.matrix), x === i2 && R === i2.version && A === t.toneMapping || (S.material.needsUpdate = true, x = i2, R = i2.version, A = t.toneMapping), S.layers.enableAll(), e.unshift(S, S.geometry, S.material, 0, 0, null));
  }, dispose: function() {
    void 0 !== T && (T.geometry.dispose(), T.material.dispose()), void 0 !== S && (S.geometry.dispose(), S.material.dispose());
  } };
}
function Bn(e, t) {
  const n = e.getParameter(e.MAX_VERTEX_ATTRIBS), i = {}, r = c(null);
  let a = r, o = false;
  function s(t2) {
    return e.bindVertexArray(t2);
  }
  function l(t2) {
    return e.deleteVertexArray(t2);
  }
  function c(e2) {
    const t2 = [], i2 = [], r2 = [];
    for (let e3 = 0; e3 < n; e3++) t2[e3] = 0, i2[e3] = 0, r2[e3] = 0;
    return { geometry: null, program: null, wireframe: false, newAttributes: t2, enabledAttributes: i2, attributeDivisors: r2, object: e2, attributes: {}, index: null };
  }
  function d() {
    const e2 = a.newAttributes;
    for (let t2 = 0, n2 = e2.length; t2 < n2; t2++) e2[t2] = 0;
  }
  function u(e2) {
    f(e2, 0);
  }
  function f(t2, n2) {
    const i2 = a.newAttributes, r2 = a.enabledAttributes, o2 = a.attributeDivisors;
    i2[t2] = 1, 0 === r2[t2] && (e.enableVertexAttribArray(t2), r2[t2] = 1), o2[t2] !== n2 && (e.vertexAttribDivisor(t2, n2), o2[t2] = n2);
  }
  function p() {
    const t2 = a.newAttributes, n2 = a.enabledAttributes;
    for (let i2 = 0, r2 = n2.length; i2 < r2; i2++) n2[i2] !== t2[i2] && (e.disableVertexAttribArray(i2), n2[i2] = 0);
  }
  function m(t2, n2, i2, r2, a2, o2, s2) {
    true === s2 ? e.vertexAttribIPointer(t2, n2, i2, a2, o2) : e.vertexAttribPointer(t2, n2, i2, r2, a2, o2);
  }
  function h() {
    _(), o = true, a !== r && (a = r, s(a.object));
  }
  function _() {
    r.geometry = null, r.program = null, r.wireframe = false;
  }
  return { setup: function(n2, r2, l2, h2, _2) {
    let g = false;
    const E = function(t2, n3, r3) {
      const a2 = true === r3.wireframe;
      let o2 = i[t2.id];
      void 0 === o2 && (o2 = {}, i[t2.id] = o2);
      let s2 = o2[n3.id];
      void 0 === s2 && (s2 = {}, o2[n3.id] = s2);
      let l3 = s2[a2];
      void 0 === l3 && (l3 = c(e.createVertexArray()), s2[a2] = l3);
      return l3;
    }(h2, l2, r2);
    a !== E && (a = E, s(a.object)), g = function(e2, t2, n3, i2) {
      const r3 = a.attributes, o2 = t2.attributes;
      let s2 = 0;
      const l3 = n3.getAttributes();
      for (const t3 in l3) {
        if (l3[t3].location >= 0) {
          const n4 = r3[t3];
          let i3 = o2[t3];
          if (void 0 === i3 && ("instanceMatrix" === t3 && e2.instanceMatrix && (i3 = e2.instanceMatrix), "instanceColor" === t3 && e2.instanceColor && (i3 = e2.instanceColor)), void 0 === n4) return true;
          if (n4.attribute !== i3) return true;
          if (i3 && n4.data !== i3.data) return true;
          s2++;
        }
      }
      return a.attributesNum !== s2 || a.index !== i2;
    }(n2, h2, l2, _2), g && function(e2, t2, n3, i2) {
      const r3 = {}, o2 = t2.attributes;
      let s2 = 0;
      const l3 = n3.getAttributes();
      for (const t3 in l3) {
        if (l3[t3].location >= 0) {
          let n4 = o2[t3];
          void 0 === n4 && ("instanceMatrix" === t3 && e2.instanceMatrix && (n4 = e2.instanceMatrix), "instanceColor" === t3 && e2.instanceColor && (n4 = e2.instanceColor));
          const i3 = {};
          i3.attribute = n4, n4 && n4.data && (i3.data = n4.data), r3[t3] = i3, s2++;
        }
      }
      a.attributes = r3, a.attributesNum = s2, a.index = i2;
    }(n2, h2, l2, _2), null !== _2 && t.update(_2, e.ELEMENT_ARRAY_BUFFER), (g || o) && (o = false, function(n3, i2, r3, a2) {
      d();
      const o2 = a2.attributes, s2 = r3.getAttributes(), l3 = i2.defaultAttributeValues;
      for (const i3 in s2) {
        const r4 = s2[i3];
        if (r4.location >= 0) {
          let s3 = o2[i3];
          if (void 0 === s3 && ("instanceMatrix" === i3 && n3.instanceMatrix && (s3 = n3.instanceMatrix), "instanceColor" === i3 && n3.instanceColor && (s3 = n3.instanceColor)), void 0 !== s3) {
            const i4 = s3.normalized, o3 = s3.itemSize, l4 = t.get(s3);
            if (void 0 === l4) continue;
            const c2 = l4.buffer, d2 = l4.type, p2 = l4.bytesPerElement, h3 = d2 === e.INT || d2 === e.UNSIGNED_INT || s3.gpuType === _mpChunkDeps_three_build_three_core_min.Bt;
            if (s3.isInterleavedBufferAttribute) {
              const t2 = s3.data, l5 = t2.stride, _3 = s3.offset;
              if (t2.isInstancedInterleavedBuffer) {
                for (let e2 = 0; e2 < r4.locationSize; e2++) f(r4.location + e2, t2.meshPerAttribute);
                true !== n3.isInstancedMesh && void 0 === a2._maxInstanceCount && (a2._maxInstanceCount = t2.meshPerAttribute * t2.count);
              } else for (let e2 = 0; e2 < r4.locationSize; e2++) u(r4.location + e2);
              e.bindBuffer(e.ARRAY_BUFFER, c2);
              for (let e2 = 0; e2 < r4.locationSize; e2++) m(r4.location + e2, o3 / r4.locationSize, d2, i4, l5 * p2, (_3 + o3 / r4.locationSize * e2) * p2, h3);
            } else {
              if (s3.isInstancedBufferAttribute) {
                for (let e2 = 0; e2 < r4.locationSize; e2++) f(r4.location + e2, s3.meshPerAttribute);
                true !== n3.isInstancedMesh && void 0 === a2._maxInstanceCount && (a2._maxInstanceCount = s3.meshPerAttribute * s3.count);
              } else for (let e2 = 0; e2 < r4.locationSize; e2++) u(r4.location + e2);
              e.bindBuffer(e.ARRAY_BUFFER, c2);
              for (let e2 = 0; e2 < r4.locationSize; e2++) m(r4.location + e2, o3 / r4.locationSize, d2, i4, o3 * p2, o3 / r4.locationSize * e2 * p2, h3);
            }
          } else if (void 0 !== l3) {
            const t2 = l3[i3];
            if (void 0 !== t2) switch (t2.length) {
              case 2:
                e.vertexAttrib2fv(r4.location, t2);
                break;
              case 3:
                e.vertexAttrib3fv(r4.location, t2);
                break;
              case 4:
                e.vertexAttrib4fv(r4.location, t2);
                break;
              default:
                e.vertexAttrib1fv(r4.location, t2);
            }
          }
        }
      }
      p();
    }(n2, r2, l2, h2), null !== _2 && e.bindBuffer(e.ELEMENT_ARRAY_BUFFER, t.get(_2).buffer));
  }, reset: h, resetDefaultState: _, dispose: function() {
    h();
    for (const e2 in i) {
      const t2 = i[e2];
      for (const e3 in t2) {
        const n2 = t2[e3];
        for (const e4 in n2) l(n2[e4].object), delete n2[e4];
        delete t2[e3];
      }
      delete i[e2];
    }
  }, releaseStatesOfGeometry: function(e2) {
    if (void 0 === i[e2.id]) return;
    const t2 = i[e2.id];
    for (const e3 in t2) {
      const n2 = t2[e3];
      for (const e4 in n2) l(n2[e4].object), delete n2[e4];
      delete t2[e3];
    }
    delete i[e2.id];
  }, releaseStatesOfProgram: function(e2) {
    for (const t2 in i) {
      const n2 = i[t2];
      if (void 0 === n2[e2.id]) continue;
      const r2 = n2[e2.id];
      for (const e3 in r2) l(r2[e3].object), delete r2[e3];
      delete n2[e2.id];
    }
  }, initAttributes: d, enableAttribute: u, disableUnusedAttributes: p };
}
function Hn(e, t, n) {
  let i;
  function r(t2, r2, a) {
    0 !== a && (e.drawArraysInstanced(i, t2, r2, a), n.update(r2, i, a));
  }
  this.setMode = function(e2) {
    i = e2;
  }, this.render = function(t2, r2) {
    e.drawArrays(i, t2, r2), n.update(r2, i, 1);
  }, this.renderInstances = r, this.renderMultiDraw = function(e2, r2, a) {
    if (0 === a) return;
    t.get("WEBGL_multi_draw").multiDrawArraysWEBGL(i, e2, 0, r2, 0, a);
    let o = 0;
    for (let e3 = 0; e3 < a; e3++) o += r2[e3];
    n.update(o, i, 1);
  }, this.renderMultiDrawInstances = function(e2, a, o, s) {
    if (0 === o) return;
    const l = t.get("WEBGL_multi_draw");
    if (null === l) for (let t2 = 0; t2 < e2.length; t2++) r(e2[t2], a[t2], s[t2]);
    else {
      l.multiDrawArraysInstancedWEBGL(i, e2, 0, a, 0, s, 0, o);
      let t2 = 0;
      for (let e3 = 0; e3 < o; e3++) t2 += a[e3] * s[e3];
      n.update(t2, i, 1);
    }
  };
}
function Gn(e, t, n, i) {
  let r;
  function a(t2) {
    if ("highp" === t2) {
      if (e.getShaderPrecisionFormat(e.VERTEX_SHADER, e.HIGH_FLOAT).precision > 0 && e.getShaderPrecisionFormat(e.FRAGMENT_SHADER, e.HIGH_FLOAT).precision > 0) return "highp";
      t2 = "mediump";
    }
    return "mediump" === t2 && e.getShaderPrecisionFormat(e.VERTEX_SHADER, e.MEDIUM_FLOAT).precision > 0 && e.getShaderPrecisionFormat(e.FRAGMENT_SHADER, e.MEDIUM_FLOAT).precision > 0 ? "mediump" : "lowp";
  }
  let o = void 0 !== n.precision ? n.precision : "highp";
  const s = a(o);
  s !== o && (console.warn("THREE.WebGLRenderer:", o, "not supported, using", s, "instead."), o = s);
  const l = true === n.logarithmicDepthBuffer, c = true === n.reverseDepthBuffer && t.has("EXT_clip_control"), d = e.getParameter(e.MAX_TEXTURE_IMAGE_UNITS), u = e.getParameter(e.MAX_VERTEX_TEXTURE_IMAGE_UNITS);
  return { isWebGL2: true, getMaxAnisotropy: function() {
    if (void 0 !== r) return r;
    if (true === t.has("EXT_texture_filter_anisotropic")) {
      const n2 = t.get("EXT_texture_filter_anisotropic");
      r = e.getParameter(n2.MAX_TEXTURE_MAX_ANISOTROPY_EXT);
    } else r = 0;
    return r;
  }, getMaxPrecision: a, textureFormatReadable: function(t2) {
    return t2 === _mpChunkDeps_three_build_three_core_min.Wt || i.convert(t2) === e.getParameter(e.IMPLEMENTATION_COLOR_READ_FORMAT);
  }, textureTypeReadable: function(n2) {
    const r2 = n2 === _mpChunkDeps_three_build_three_core_min.Et && (t.has("EXT_color_buffer_half_float") || t.has("EXT_color_buffer_float"));
    return !(n2 !== _mpChunkDeps_three_build_three_core_min.Tt && i.convert(n2) !== e.getParameter(e.IMPLEMENTATION_COLOR_READ_TYPE) && n2 !== _mpChunkDeps_three_build_three_core_min.Rt && !r2);
  }, precision: o, logarithmicDepthBuffer: l, reverseDepthBuffer: c, maxTextures: d, maxVertexTextures: u, maxTextureSize: e.getParameter(e.MAX_TEXTURE_SIZE), maxCubemapSize: e.getParameter(e.MAX_CUBE_MAP_TEXTURE_SIZE), maxAttributes: e.getParameter(e.MAX_VERTEX_ATTRIBS), maxVertexUniforms: e.getParameter(e.MAX_VERTEX_UNIFORM_VECTORS), maxVaryings: e.getParameter(e.MAX_VARYING_VECTORS), maxFragmentUniforms: e.getParameter(e.MAX_FRAGMENT_UNIFORM_VECTORS), vertexTextures: u > 0, maxSamples: e.getParameter(e.MAX_SAMPLES) };
}
function Vn(e) {
  const n = this;
  let i = null, r = 0, a = false, o = false;
  const s = new _mpChunkDeps_three_build_three_core_min.ea(), l = new _mpChunkDeps_three_build_three_core_min.Gs(), c = { value: null, needsUpdate: false };
  function d(e2, t, i2, r2) {
    const a2 = null !== e2 ? e2.length : 0;
    let o2 = null;
    if (0 !== a2) {
      if (o2 = c.value, true !== r2 || null === o2) {
        const n2 = i2 + 4 * a2, r3 = t.matrixWorldInverse;
        l.getNormalMatrix(r3), (null === o2 || o2.length < n2) && (o2 = new Float32Array(n2));
        for (let t2 = 0, n3 = i2; t2 !== a2; ++t2, n3 += 4) s.copy(e2[t2]).applyMatrix4(r3, l), s.normal.toArray(o2, n3), o2[n3 + 3] = s.constant;
      }
      c.value = o2, c.needsUpdate = true;
    }
    return n.numPlanes = a2, n.numIntersection = 0, o2;
  }
  this.uniform = c, this.numPlanes = 0, this.numIntersection = 0, this.init = function(e2, t) {
    const n2 = 0 !== e2.length || t || 0 !== r || a;
    return a = t, r = e2.length, n2;
  }, this.beginShadows = function() {
    o = true, d(null);
  }, this.endShadows = function() {
    o = false;
  }, this.setGlobalState = function(e2, t) {
    i = d(e2, t, 0);
  }, this.setState = function(t, s2, l2) {
    const u = t.clippingPlanes, f = t.clipIntersection, p = t.clipShadows, m = e.get(t);
    if (!a || null === u || 0 === u.length || o && !p) o ? d(null) : function() {
      c.value !== i && (c.value = i, c.needsUpdate = r > 0);
      n.numPlanes = r, n.numIntersection = 0;
    }();
    else {
      const e2 = o ? 0 : r, t2 = 4 * e2;
      let n2 = m.clippingState || null;
      c.value = n2, n2 = d(u, s2, t2, l2);
      for (let e3 = 0; e3 !== t2; ++e3) n2[e3] = i[e3];
      m.clippingState = n2, this.numIntersection = f ? this.numPlanes : 0, this.numPlanes += e2;
    }
  };
}
function zn(e) {
  let t = /* @__PURE__ */ new WeakMap();
  function n(e2, t2) {
    return t2 === _mpChunkDeps_three_build_three_core_min.ct ? e2.mapping = _mpChunkDeps_three_build_three_core_min.ht : t2 === _mpChunkDeps_three_build_three_core_min.ut && (e2.mapping = _mpChunkDeps_three_build_three_core_min.lt), e2;
  }
  function i(e2) {
    const n2 = e2.target;
    n2.removeEventListener("dispose", i);
    const r = t.get(n2);
    void 0 !== r && (t.delete(n2), r.dispose());
  }
  return { get: function(r) {
    if (r && r.isTexture) {
      const a = r.mapping;
      if (a === _mpChunkDeps_three_build_three_core_min.ct || a === _mpChunkDeps_three_build_three_core_min.ut) {
        if (t.has(r)) {
          return n(t.get(r).texture, r.mapping);
        }
        {
          const a2 = r.image;
          if (a2 && a2.height > 0) {
            const o = new _mpChunkDeps_three_build_three_core_min.eo(a2.height);
            return o.fromEquirectangularTexture(e, r), t.set(r, o), r.addEventListener("dispose", i), n(o.texture, r.mapping);
          }
          return null;
        }
      }
    }
    return r;
  }, dispose: function() {
    t = /* @__PURE__ */ new WeakMap();
  } };
}
const kn = [0.125, 0.215, 0.35, 0.446, 0.526, 0.582], Wn = 20, Xn = new _mpChunkDeps_three_build_three_core_min.Tc(), Yn = new _mpChunkDeps_three_build_three_core_min.$r();
let jn = null, Kn = 0, qn = 0, Zn = false;
const $n = (1 + Math.sqrt(5)) / 2, Qn = 1 / $n, Jn = [new _mpChunkDeps_three_build_three_core_min.Ii(-$n, Qn, 0), new _mpChunkDeps_three_build_three_core_min.Ii($n, Qn, 0), new _mpChunkDeps_three_build_three_core_min.Ii(-Qn, 0, $n), new _mpChunkDeps_three_build_three_core_min.Ii(Qn, 0, $n), new _mpChunkDeps_three_build_three_core_min.Ii(0, $n, -Qn), new _mpChunkDeps_three_build_three_core_min.Ii(0, $n, Qn), new _mpChunkDeps_three_build_three_core_min.Ii(-1, 1, -1), new _mpChunkDeps_three_build_three_core_min.Ii(1, 1, -1), new _mpChunkDeps_three_build_three_core_min.Ii(-1, 1, 1), new _mpChunkDeps_three_build_three_core_min.Ii(1, 1, 1)];
class ei {
  constructor(e) {
    this._renderer = e, this._pingPongRenderTarget = null, this._lodMax = 0, this._cubeSize = 0, this._lodPlanes = [], this._sizeLods = [], this._sigmas = [], this._blurMaterial = null, this._cubemapMaterial = null, this._equirectMaterial = null, this._compileMaterial(this._blurMaterial);
  }
  fromScene(e, t = 0, n = 0.1, i = 100) {
    jn = this._renderer.getRenderTarget(), Kn = this._renderer.getActiveCubeFace(), qn = this._renderer.getActiveMipmapLevel(), Zn = this._renderer.xr.enabled, this._renderer.xr.enabled = false, this._setSize(256);
    const r = this._allocateTargets();
    return r.depthBuffer = true, this._sceneToCubeUV(e, n, i, r), t > 0 && this._blur(r, 0, 0, t), this._applyPMREM(r), this._cleanup(r), r;
  }
  fromEquirectangular(e, t = null) {
    return this._fromTexture(e, t);
  }
  fromCubemap(e, t = null) {
    return this._fromTexture(e, t);
  }
  compileCubemapShader() {
    null === this._cubemapMaterial && (this._cubemapMaterial = ri(), this._compileMaterial(this._cubemapMaterial));
  }
  compileEquirectangularShader() {
    null === this._equirectMaterial && (this._equirectMaterial = ii(), this._compileMaterial(this._equirectMaterial));
  }
  dispose() {
    this._dispose(), null !== this._cubemapMaterial && this._cubemapMaterial.dispose(), null !== this._equirectMaterial && this._equirectMaterial.dispose();
  }
  _setSize(e) {
    this._lodMax = Math.floor(Math.log2(e)), this._cubeSize = Math.pow(2, this._lodMax);
  }
  _dispose() {
    null !== this._blurMaterial && this._blurMaterial.dispose(), null !== this._pingPongRenderTarget && this._pingPongRenderTarget.dispose();
    for (let e = 0; e < this._lodPlanes.length; e++) this._lodPlanes[e].dispose();
  }
  _cleanup(e) {
    this._renderer.setRenderTarget(jn, Kn, qn), this._renderer.xr.enabled = Zn, e.scissorTest = false, ni(e, 0, 0, e.width, e.height);
  }
  _fromTexture(e, t) {
    e.mapping === _mpChunkDeps_three_build_three_core_min.ht || e.mapping === _mpChunkDeps_three_build_three_core_min.lt ? this._setSize(0 === e.image.length ? 16 : e.image[0].width || e.image[0].image.width) : this._setSize(e.image.width / 4), jn = this._renderer.getRenderTarget(), Kn = this._renderer.getActiveCubeFace(), qn = this._renderer.getActiveMipmapLevel(), Zn = this._renderer.xr.enabled, this._renderer.xr.enabled = false;
    const n = t || this._allocateTargets();
    return this._textureToCubeUV(e, n), this._applyPMREM(n), this._cleanup(n), n;
  }
  _allocateTargets() {
    const e = 3 * Math.max(this._cubeSize, 112), t = 4 * this._cubeSize, n = { magFilter: _mpChunkDeps_three_build_three_core_min.wt, minFilter: _mpChunkDeps_three_build_three_core_min.wt, generateMipmaps: false, type: _mpChunkDeps_three_build_three_core_min.Et, format: _mpChunkDeps_three_build_three_core_min.Wt, colorSpace: _mpChunkDeps_three_build_three_core_min.$e, depthBuffer: false }, i = ti(e, t, n);
    if (null === this._pingPongRenderTarget || this._pingPongRenderTarget.width !== e || this._pingPongRenderTarget.height !== t) {
      null !== this._pingPongRenderTarget && this._dispose(), this._pingPongRenderTarget = ti(e, t, n);
      const { _lodMax: i2 } = this;
      ({ sizeLods: this._sizeLods, lodPlanes: this._lodPlanes, sigmas: this._sigmas } = function(e2) {
        const t2 = [], n2 = [], i3 = [];
        let r = e2;
        const a = e2 - 4 + 1 + kn.length;
        for (let o = 0; o < a; o++) {
          const a2 = Math.pow(2, r);
          n2.push(a2);
          let s = 1 / a2;
          o > e2 - 4 ? s = kn[o - e2 + 4 - 1] : 0 === o && (s = 0), i3.push(s);
          const l = 1 / (a2 - 2), c = -l, d = 1 + l, u = [c, c, d, c, d, d, c, c, d, d, c, d], f = 6, p = 6, m = 3, h = 2, _ = 1, g = new Float32Array(m * p * f), v = new Float32Array(h * p * f), E = new Float32Array(_ * p * f);
          for (let e3 = 0; e3 < f; e3++) {
            const t3 = e3 % 3 * 2 / 3 - 1, n3 = e3 > 2 ? 0 : -1, i4 = [t3, n3, 0, t3 + 2 / 3, n3, 0, t3 + 2 / 3, n3 + 1, 0, t3, n3, 0, t3 + 2 / 3, n3 + 1, 0, t3, n3 + 1, 0];
            g.set(i4, m * p * e3), v.set(u, h * p * e3);
            const r2 = [e3, e3, e3, e3, e3, e3];
            E.set(r2, _ * p * e3);
          }
          const S = new _mpChunkDeps_three_build_three_core_min.zn();
          S.setAttribute("position", new _mpChunkDeps_three_build_three_core_min.cn(g, m)), S.setAttribute("uv", new _mpChunkDeps_three_build_three_core_min.cn(v, h)), S.setAttribute("faceIndex", new _mpChunkDeps_three_build_three_core_min.cn(E, _)), t2.push(S), r > 4 && r--;
        }
        return { lodPlanes: t2, sizeLods: n2, sigmas: i3 };
      }(i2)), this._blurMaterial = function(e2, t2, n2) {
        const i3 = new Float32Array(Wn), a = new _mpChunkDeps_three_build_three_core_min.Ii(0, 1, 0), o = new _mpChunkDeps_three_build_three_core_min.Jn({ name: "SphericalGaussianBlur", defines: { n: Wn, CUBEUV_TEXEL_WIDTH: 1 / t2, CUBEUV_TEXEL_HEIGHT: 1 / n2, CUBEUV_MAX_MIP: `${e2}.0` }, uniforms: { envMap: { value: null }, samples: { value: 1 }, weights: { value: i3 }, latitudinal: { value: false }, dTheta: { value: 0 }, mipInt: { value: 0 }, poleAxis: { value: a } }, vertexShader: ai(), fragmentShader: "\n\n			precision mediump float;\n			precision mediump int;\n\n			varying vec3 vOutputDirection;\n\n			uniform sampler2D envMap;\n			uniform int samples;\n			uniform float weights[ n ];\n			uniform bool latitudinal;\n			uniform float dTheta;\n			uniform float mipInt;\n			uniform vec3 poleAxis;\n\n			#define ENVMAP_TYPE_CUBE_UV\n			#include <cube_uv_reflection_fragment>\n\n			vec3 getSample( float theta, vec3 axis ) {\n\n				float cosTheta = cos( theta );\n				// Rodrigues' axis-angle rotation\n				vec3 sampleDirection = vOutputDirection * cosTheta\n					+ cross( axis, vOutputDirection ) * sin( theta )\n					+ axis * dot( axis, vOutputDirection ) * ( 1.0 - cosTheta );\n\n				return bilinearCubeUV( envMap, sampleDirection, mipInt );\n\n			}\n\n			void main() {\n\n				vec3 axis = latitudinal ? poleAxis : cross( poleAxis, vOutputDirection );\n\n				if ( all( equal( axis, vec3( 0.0 ) ) ) ) {\n\n					axis = vec3( vOutputDirection.z, 0.0, - vOutputDirection.x );\n\n				}\n\n				axis = normalize( axis );\n\n				gl_FragColor = vec4( 0.0, 0.0, 0.0, 1.0 );\n				gl_FragColor.rgb += weights[ 0 ] * getSample( 0.0, axis );\n\n				for ( int i = 1; i < n; i++ ) {\n\n					if ( i >= samples ) {\n\n						break;\n\n					}\n\n					float theta = dTheta * float( i );\n					gl_FragColor.rgb += weights[ i ] * getSample( -1.0 * theta, axis );\n					gl_FragColor.rgb += weights[ i ] * getSample( theta, axis );\n\n				}\n\n			}\n		", blending: _mpChunkDeps_three_build_three_core_min.m, depthTest: false, depthWrite: false });
        return o;
      }(i2, e, t);
    }
    return i;
  }
  _compileMaterial(e) {
    const t = new _mpChunkDeps_three_build_three_core_min.Vn(this._lodPlanes[0], e);
    this._renderer.compile(t, Xn);
  }
  _sceneToCubeUV(e, t, n, i) {
    const r = new _mpChunkDeps_three_build_three_core_min.$n(90, 1, t, n), a = [1, -1, 1, 1, 1, 1], l = [1, 1, 1, -1, -1, -1], c = this._renderer, u = c.autoClear, f = c.toneMapping;
    c.getClearColor(Yn), c.toneMapping = _mpChunkDeps_three_build_three_core_min.$, c.autoClear = false;
    const p = new _mpChunkDeps_three_build_three_core_min.en({ name: "PMREM.Background", side: _mpChunkDeps_three_build_three_core_min.d, depthWrite: false, depthTest: false }), m = new _mpChunkDeps_three_build_three_core_min.Vn(new _mpChunkDeps_three_build_three_core_min.jn(), p);
    let h = false;
    const _ = e.background;
    _ ? _.isColor && (p.color.copy(_), e.background = null, h = true) : (p.color.copy(Yn), h = true);
    for (let t2 = 0; t2 < 6; t2++) {
      const n2 = t2 % 3;
      0 === n2 ? (r.up.set(0, a[t2], 0), r.lookAt(l[t2], 0, 0)) : 1 === n2 ? (r.up.set(0, 0, a[t2]), r.lookAt(0, l[t2], 0)) : (r.up.set(0, a[t2], 0), r.lookAt(0, 0, l[t2]));
      const o = this._cubeSize;
      ni(i, n2 * o, t2 > 2 ? o : 0, o, o), c.setRenderTarget(i), h && c.render(m, r), c.render(e, r);
    }
    m.geometry.dispose(), m.material.dispose(), c.toneMapping = f, c.autoClear = u, e.background = _;
  }
  _textureToCubeUV(e, t) {
    const n = this._renderer, i = e.mapping === _mpChunkDeps_three_build_three_core_min.ht || e.mapping === _mpChunkDeps_three_build_three_core_min.lt;
    i ? (null === this._cubemapMaterial && (this._cubemapMaterial = ri()), this._cubemapMaterial.uniforms.flipEnvMap.value = false === e.isRenderTargetTexture ? -1 : 1) : null === this._equirectMaterial && (this._equirectMaterial = ii());
    const r = i ? this._cubemapMaterial : this._equirectMaterial, a = new _mpChunkDeps_three_build_three_core_min.Vn(this._lodPlanes[0], r);
    r.uniforms.envMap.value = e;
    const s = this._cubeSize;
    ni(t, 0, 0, 3 * s, 2 * s), n.setRenderTarget(t), n.render(a, Xn);
  }
  _applyPMREM(e) {
    const t = this._renderer, n = t.autoClear;
    t.autoClear = false;
    const i = this._lodPlanes.length;
    for (let t2 = 1; t2 < i; t2++) {
      const n2 = Math.sqrt(this._sigmas[t2] * this._sigmas[t2] - this._sigmas[t2 - 1] * this._sigmas[t2 - 1]), r = Jn[(i - t2 - 1) % Jn.length];
      this._blur(e, t2 - 1, t2, n2, r);
    }
    t.autoClear = n;
  }
  _blur(e, t, n, i, r) {
    const a = this._pingPongRenderTarget;
    this._halfBlur(e, a, t, n, i, "latitudinal", r), this._halfBlur(a, e, n, n, i, "longitudinal", r);
  }
  _halfBlur(e, t, n, i, r, a, s) {
    const l = this._renderer, c = this._blurMaterial;
    "latitudinal" !== a && "longitudinal" !== a && console.error("blur direction must be either latitudinal or longitudinal!");
    const d = new _mpChunkDeps_three_build_three_core_min.Vn(this._lodPlanes[i], c), u = c.uniforms, f = this._sizeLods[n] - 1, p = isFinite(r) ? Math.PI / (2 * f) : 2 * Math.PI / 39, m = r / p, h = isFinite(r) ? 1 + Math.floor(3 * m) : Wn;
    h > Wn && console.warn(`sigmaRadians, ${r}, is too large and will clip, as it requested ${h} samples when the maximum is set to 20`);
    const _ = [];
    let g = 0;
    for (let e2 = 0; e2 < Wn; ++e2) {
      const t2 = e2 / m, n2 = Math.exp(-t2 * t2 / 2);
      _.push(n2), 0 === e2 ? g += n2 : e2 < h && (g += 2 * n2);
    }
    for (let e2 = 0; e2 < _.length; e2++) _[e2] = _[e2] / g;
    u.envMap.value = e.texture, u.samples.value = h, u.weights.value = _, u.latitudinal.value = "latitudinal" === a, s && (u.poleAxis.value = s);
    const { _lodMax: v } = this;
    u.dTheta.value = p, u.mipInt.value = v - n;
    const E = this._sizeLods[i];
    ni(t, 3 * E * (i > v - 4 ? i - v + 4 : 0), 4 * (this._cubeSize - E), 3 * E, 2 * E), l.setRenderTarget(t), l.render(d, Xn);
  }
}
function ti(e, t, n) {
  const i = new _mpChunkDeps_three_build_three_core_min.Si(e, t, n);
  return i.texture.mapping = _mpChunkDeps_three_build_three_core_min.dt, i.texture.name = "PMREM.cubeUv", i.scissorTest = true, i;
}
function ni(e, t, n, i, r) {
  e.viewport.set(t, n, i, r), e.scissor.set(t, n, i, r);
}
function ii() {
  return new _mpChunkDeps_three_build_three_core_min.Jn({ name: "EquirectangularToCubeUV", uniforms: { envMap: { value: null } }, vertexShader: ai(), fragmentShader: "\n\n			precision mediump float;\n			precision mediump int;\n\n			varying vec3 vOutputDirection;\n\n			uniform sampler2D envMap;\n\n			#include <common>\n\n			void main() {\n\n				vec3 outputDirection = normalize( vOutputDirection );\n				vec2 uv = equirectUv( outputDirection );\n\n				gl_FragColor = vec4( texture2D ( envMap, uv ).rgb, 1.0 );\n\n			}\n		", blending: _mpChunkDeps_three_build_three_core_min.m, depthTest: false, depthWrite: false });
}
function ri() {
  return new _mpChunkDeps_three_build_three_core_min.Jn({ name: "CubemapToCubeUV", uniforms: { envMap: { value: null }, flipEnvMap: { value: -1 } }, vertexShader: ai(), fragmentShader: "\n\n			precision mediump float;\n			precision mediump int;\n\n			uniform float flipEnvMap;\n\n			varying vec3 vOutputDirection;\n\n			uniform samplerCube envMap;\n\n			void main() {\n\n				gl_FragColor = textureCube( envMap, vec3( flipEnvMap * vOutputDirection.x, vOutputDirection.yz ) );\n\n			}\n		", blending: _mpChunkDeps_three_build_three_core_min.m, depthTest: false, depthWrite: false });
}
function ai() {
  return "\n\n		precision mediump float;\n		precision mediump int;\n\n		attribute float faceIndex;\n\n		varying vec3 vOutputDirection;\n\n		// RH coordinate system; PMREM face-indexing convention\n		vec3 getDirection( vec2 uv, float face ) {\n\n			uv = 2.0 * uv - 1.0;\n\n			vec3 direction = vec3( uv, 1.0 );\n\n			if ( face == 0.0 ) {\n\n				direction = direction.zyx; // ( 1, v, u ) pos x\n\n			} else if ( face == 1.0 ) {\n\n				direction = direction.xzy;\n				direction.xz *= -1.0; // ( -u, 1, -v ) pos y\n\n			} else if ( face == 2.0 ) {\n\n				direction.x *= -1.0; // ( -u, v, 1 ) pos z\n\n			} else if ( face == 3.0 ) {\n\n				direction = direction.zyx;\n				direction.xz *= -1.0; // ( -1, v, -u ) neg x\n\n			} else if ( face == 4.0 ) {\n\n				direction = direction.xzy;\n				direction.xy *= -1.0; // ( -u, -1, v ) neg y\n\n			} else if ( face == 5.0 ) {\n\n				direction.z *= -1.0; // ( u, v, -1 ) neg z\n\n			}\n\n			return direction;\n\n		}\n\n		void main() {\n\n			vOutputDirection = getDirection( uv, faceIndex );\n			gl_Position = vec4( position, 1.0 );\n\n		}\n	";
}
function oi(e) {
  let t = /* @__PURE__ */ new WeakMap(), n = null;
  function i(e2) {
    const n2 = e2.target;
    n2.removeEventListener("dispose", i);
    const r = t.get(n2);
    void 0 !== r && (t.delete(n2), r.dispose());
  }
  return { get: function(r) {
    if (r && r.isTexture) {
      const a = r.mapping, o = a === _mpChunkDeps_three_build_three_core_min.ct || a === _mpChunkDeps_three_build_three_core_min.ut, s = a === _mpChunkDeps_three_build_three_core_min.ht || a === _mpChunkDeps_three_build_three_core_min.lt;
      if (o || s) {
        let a2 = t.get(r);
        const l = void 0 !== a2 ? a2.texture.pmremVersion : 0;
        if (r.isRenderTargetTexture && r.pmremVersion !== l) return null === n && (n = new ei(e)), a2 = o ? n.fromEquirectangular(r, a2) : n.fromCubemap(r, a2), a2.texture.pmremVersion = r.pmremVersion, t.set(r, a2), a2.texture;
        if (void 0 !== a2) return a2.texture;
        {
          const l2 = r.image;
          return o && l2 && l2.height > 0 || s && l2 && function(e2) {
            let t2 = 0;
            const n2 = 6;
            for (let i2 = 0; i2 < n2; i2++) void 0 !== e2[i2] && t2++;
            return t2 === n2;
          }(l2) ? (null === n && (n = new ei(e)), a2 = o ? n.fromEquirectangular(r) : n.fromCubemap(r), a2.texture.pmremVersion = r.pmremVersion, t.set(r, a2), r.addEventListener("dispose", i), a2.texture) : null;
        }
      }
    }
    return r;
  }, dispose: function() {
    t = /* @__PURE__ */ new WeakMap(), null !== n && (n.dispose(), n = null);
  } };
}
function si(e) {
  const t = {};
  function n(n2) {
    if (void 0 !== t[n2]) return t[n2];
    let i;
    switch (n2) {
      case "WEBGL_depth_texture":
        i = e.getExtension("WEBGL_depth_texture") || e.getExtension("MOZ_WEBGL_depth_texture") || e.getExtension("WEBKIT_WEBGL_depth_texture");
        break;
      case "EXT_texture_filter_anisotropic":
        i = e.getExtension("EXT_texture_filter_anisotropic") || e.getExtension("MOZ_EXT_texture_filter_anisotropic") || e.getExtension("WEBKIT_EXT_texture_filter_anisotropic");
        break;
      case "WEBGL_compressed_texture_s3tc":
        i = e.getExtension("WEBGL_compressed_texture_s3tc") || e.getExtension("MOZ_WEBGL_compressed_texture_s3tc") || e.getExtension("WEBKIT_WEBGL_compressed_texture_s3tc");
        break;
      case "WEBGL_compressed_texture_pvrtc":
        i = e.getExtension("WEBGL_compressed_texture_pvrtc") || e.getExtension("WEBKIT_WEBGL_compressed_texture_pvrtc");
        break;
      default:
        i = e.getExtension(n2);
    }
    return t[n2] = i, i;
  }
  return { has: function(e2) {
    return null !== n(e2);
  }, init: function() {
    n("EXT_color_buffer_float"), n("WEBGL_clip_cull_distance"), n("OES_texture_float_linear"), n("EXT_color_buffer_half_float"), n("WEBGL_multisampled_render_to_texture"), n("WEBGL_render_shared_exponent");
  }, get: function(e2) {
    const t2 = n(e2);
    return null === t2 && _mpChunkDeps_three_build_three_core_min.ri("THREE.WebGLRenderer: " + e2 + " extension not supported."), t2;
  } };
}
function li(e, t, n, i) {
  const r = {}, a = /* @__PURE__ */ new WeakMap();
  function o(e2) {
    const s2 = e2.target;
    null !== s2.index && t.remove(s2.index);
    for (const e3 in s2.attributes) t.remove(s2.attributes[e3]);
    s2.removeEventListener("dispose", o), delete r[s2.id];
    const l = a.get(s2);
    l && (t.remove(l), a.delete(s2)), i.releaseStatesOfGeometry(s2), true === s2.isInstancedBufferGeometry && delete s2._maxInstanceCount, n.memory.geometries--;
  }
  function s(e2) {
    const n2 = [], i2 = e2.index, r2 = e2.attributes.position;
    let o2 = 0;
    if (null !== i2) {
      const e3 = i2.array;
      o2 = i2.version;
      for (let t2 = 0, i3 = e3.length; t2 < i3; t2 += 3) {
        const i4 = e3[t2 + 0], r3 = e3[t2 + 1], a2 = e3[t2 + 2];
        n2.push(i4, r3, r3, a2, a2, i4);
      }
    } else {
      if (void 0 === r2) return;
      {
        const e3 = r2.array;
        o2 = r2.version;
        for (let t2 = 0, i3 = e3.length / 3 - 1; t2 < i3; t2 += 3) {
          const e4 = t2 + 0, i4 = t2 + 1, r3 = t2 + 2;
          n2.push(e4, i4, i4, r3, r3, e4);
        }
      }
    }
    const s2 = new (_mpChunkDeps_three_build_three_core_min.Qs(n2) ? _mpChunkDeps_three_build_three_core_min.gn : _mpChunkDeps_three_build_three_core_min.yn)(n2, 1);
    s2.version = o2;
    const l = a.get(e2);
    l && t.remove(l), a.set(e2, s2);
  }
  return { get: function(e2, t2) {
    return true === r[t2.id] || (t2.addEventListener("dispose", o), r[t2.id] = true, n.memory.geometries++), t2;
  }, update: function(n2) {
    const i2 = n2.attributes;
    for (const n3 in i2) t.update(i2[n3], e.ARRAY_BUFFER);
  }, getWireframeAttribute: function(e2) {
    const t2 = a.get(e2);
    if (t2) {
      const n2 = e2.index;
      null !== n2 && t2.version < n2.version && s(e2);
    } else s(e2);
    return a.get(e2);
  } };
}
function ci(e, t, n) {
  let i, r, a;
  function o(t2, o2, s) {
    0 !== s && (e.drawElementsInstanced(i, o2, r, t2 * a, s), n.update(o2, i, s));
  }
  this.setMode = function(e2) {
    i = e2;
  }, this.setIndex = function(e2) {
    r = e2.type, a = e2.bytesPerElement;
  }, this.render = function(t2, o2) {
    e.drawElements(i, o2, r, t2 * a), n.update(o2, i, 1);
  }, this.renderInstances = o, this.renderMultiDraw = function(e2, a2, o2) {
    if (0 === o2) return;
    t.get("WEBGL_multi_draw").multiDrawElementsWEBGL(i, a2, 0, r, e2, 0, o2);
    let s = 0;
    for (let e3 = 0; e3 < o2; e3++) s += a2[e3];
    n.update(s, i, 1);
  }, this.renderMultiDrawInstances = function(e2, s, l, c) {
    if (0 === l) return;
    const d = t.get("WEBGL_multi_draw");
    if (null === d) for (let t2 = 0; t2 < e2.length; t2++) o(e2[t2] / a, s[t2], c[t2]);
    else {
      d.multiDrawElementsInstancedWEBGL(i, s, 0, r, e2, 0, c, 0, l);
      let t2 = 0;
      for (let e3 = 0; e3 < l; e3++) t2 += s[e3] * c[e3];
      n.update(t2, i, 1);
    }
  };
}
function di(e) {
  const t = { frame: 0, calls: 0, triangles: 0, points: 0, lines: 0 };
  return { memory: { geometries: 0, textures: 0 }, render: t, programs: null, autoReset: true, reset: function() {
    t.calls = 0, t.triangles = 0, t.points = 0, t.lines = 0;
  }, update: function(n, i, r) {
    switch (t.calls++, i) {
      case e.TRIANGLES:
        t.triangles += r * (n / 3);
        break;
      case e.LINES:
        t.lines += r * (n / 2);
        break;
      case e.LINE_STRIP:
        t.lines += r * (n - 1);
        break;
      case e.LINE_LOOP:
        t.lines += r * n;
        break;
      case e.POINTS:
        t.points += r * n;
        break;
      default:
        console.error("THREE.WebGLInfo: Unknown draw mode:", i);
    }
  } };
}
function ui(e, t, i) {
  const r = /* @__PURE__ */ new WeakMap(), a = new _mpChunkDeps_three_build_three_core_min.wi();
  return { update: function(o, s, l) {
    const c = o.morphTargetInfluences, d = s.morphAttributes.position || s.morphAttributes.normal || s.morphAttributes.color, u = void 0 !== d ? d.length : 0;
    let f = r.get(s);
    if (void 0 === f || f.count !== u) {
      let b = function() {
        R.dispose(), r.delete(s), s.removeEventListener("dispose", b);
      };
      void 0 !== f && f.texture.dispose();
      const p = void 0 !== s.morphAttributes.position, m = void 0 !== s.morphAttributes.normal, h = void 0 !== s.morphAttributes.color, _ = s.morphAttributes.position || [], g = s.morphAttributes.normal || [], v = s.morphAttributes.color || [];
      let E = 0;
      true === p && (E = 1), true === m && (E = 2), true === h && (E = 3);
      let S = s.attributes.position.count * E, T = 1;
      S > t.maxTextureSize && (T = Math.ceil(S / t.maxTextureSize), S = t.maxTextureSize);
      const x = new Float32Array(S * T * 4 * u), R = new _mpChunkDeps_three_build_three_core_min._i(x, S, T, u);
      R.type = _mpChunkDeps_three_build_three_core_min.Rt, R.needsUpdate = true;
      const A = 4 * E;
      for (let C = 0; C < u; C++) {
        const L = _[C], P = g[C], U = v[C], w = S * T * 4 * C;
        for (let D = 0; D < L.count; D++) {
          const y = D * A;
          true === p && (a.fromBufferAttribute(L, D), x[w + y + 0] = a.x, x[w + y + 1] = a.y, x[w + y + 2] = a.z, x[w + y + 3] = 0), true === m && (a.fromBufferAttribute(P, D), x[w + y + 4] = a.x, x[w + y + 5] = a.y, x[w + y + 6] = a.z, x[w + y + 7] = 0), true === h && (a.fromBufferAttribute(U, D), x[w + y + 8] = a.x, x[w + y + 9] = a.y, x[w + y + 10] = a.z, x[w + y + 11] = 4 === U.itemSize ? a.w : 1);
        }
      }
      f = { count: u, texture: R, size: new _mpChunkDeps_three_build_three_core_min.Zs(S, T) }, r.set(s, f), s.addEventListener("dispose", b);
    }
    if (true === o.isInstancedMesh && null !== o.morphTexture) l.getUniforms().setValue(e, "morphTexture", o.morphTexture, i);
    else {
      let I = 0;
      for (let O = 0; O < c.length; O++) I += c[O];
      const N = s.morphTargetsRelative ? 1 : 1 - I;
      l.getUniforms().setValue(e, "morphTargetBaseInfluence", N), l.getUniforms().setValue(e, "morphTargetInfluences", c);
    }
    l.getUniforms().setValue(e, "morphTargetsTexture", f.texture, i), l.getUniforms().setValue(e, "morphTargetsTextureSize", f.size);
  } };
}
function fi(e, t, n, i) {
  let r = /* @__PURE__ */ new WeakMap();
  function a(e2) {
    const t2 = e2.target;
    t2.removeEventListener("dispose", a), n.remove(t2.instanceMatrix), null !== t2.instanceColor && n.remove(t2.instanceColor);
  }
  return { update: function(o) {
    const s = i.render.frame, l = o.geometry, c = t.get(o, l);
    if (r.get(c) !== s && (t.update(c), r.set(c, s)), o.isInstancedMesh && (false === o.hasEventListener("dispose", a) && o.addEventListener("dispose", a), r.get(o) !== s && (n.update(o.instanceMatrix, e.ARRAY_BUFFER), null !== o.instanceColor && n.update(o.instanceColor, e.ARRAY_BUFFER), r.set(o, s))), o.isSkinnedMesh) {
      const e2 = o.skeleton;
      r.get(e2) !== s && (e2.update(), r.set(e2, s));
    }
    return c;
  }, dispose: function() {
    r = /* @__PURE__ */ new WeakMap();
  } };
}
const pi = new _mpChunkDeps_three_build_three_core_min.vi(), mi = new _mpChunkDeps_three_build_three_core_min.$a(1, 1), hi = new _mpChunkDeps_three_build_three_core_min._i(), _i = new _mpChunkDeps_three_build_three_core_min.Ti(), gi = new _mpChunkDeps_three_build_three_core_min.to(), vi = [], Ei = [], Si = new Float32Array(16), Ti = new Float32Array(9), Mi = new Float32Array(4);
function xi(e, t, n) {
  const i = e[0];
  if (i <= 0 || i > 0) return e;
  const r = t * n;
  let a = vi[r];
  if (void 0 === a && (a = new Float32Array(r), vi[r] = a), 0 !== t) {
    i.toArray(a, 0);
    for (let i2 = 1, r2 = 0; i2 !== t; ++i2) r2 += n, e[i2].toArray(a, r2);
  }
  return a;
}
function Ri(e, t) {
  if (e.length !== t.length) return false;
  for (let n = 0, i = e.length; n < i; n++) if (e[n] !== t[n]) return false;
  return true;
}
function Ai(e, t) {
  for (let n = 0, i = t.length; n < i; n++) e[n] = t[n];
}
function bi(e, t) {
  let n = Ei[t];
  void 0 === n && (n = new Int32Array(t), Ei[t] = n);
  for (let i = 0; i !== t; ++i) n[i] = e.allocateTextureUnit();
  return n;
}
function Ci(e, t) {
  const n = this.cache;
  n[0] !== t && (e.uniform1f(this.addr, t), n[0] = t);
}
function Li(e, t) {
  const n = this.cache;
  if (void 0 !== t.x) n[0] === t.x && n[1] === t.y || (e.uniform2f(this.addr, t.x, t.y), n[0] = t.x, n[1] = t.y);
  else {
    if (Ri(n, t)) return;
    e.uniform2fv(this.addr, t), Ai(n, t);
  }
}
function Pi(e, t) {
  const n = this.cache;
  if (void 0 !== t.x) n[0] === t.x && n[1] === t.y && n[2] === t.z || (e.uniform3f(this.addr, t.x, t.y, t.z), n[0] = t.x, n[1] = t.y, n[2] = t.z);
  else if (void 0 !== t.r) n[0] === t.r && n[1] === t.g && n[2] === t.b || (e.uniform3f(this.addr, t.r, t.g, t.b), n[0] = t.r, n[1] = t.g, n[2] = t.b);
  else {
    if (Ri(n, t)) return;
    e.uniform3fv(this.addr, t), Ai(n, t);
  }
}
function Ui(e, t) {
  const n = this.cache;
  if (void 0 !== t.x) n[0] === t.x && n[1] === t.y && n[2] === t.z && n[3] === t.w || (e.uniform4f(this.addr, t.x, t.y, t.z, t.w), n[0] = t.x, n[1] = t.y, n[2] = t.z, n[3] = t.w);
  else {
    if (Ri(n, t)) return;
    e.uniform4fv(this.addr, t), Ai(n, t);
  }
}
function wi(e, t) {
  const n = this.cache, i = t.elements;
  if (void 0 === i) {
    if (Ri(n, t)) return;
    e.uniformMatrix2fv(this.addr, false, t), Ai(n, t);
  } else {
    if (Ri(n, i)) return;
    Mi.set(i), e.uniformMatrix2fv(this.addr, false, Mi), Ai(n, i);
  }
}
function Di(e, t) {
  const n = this.cache, i = t.elements;
  if (void 0 === i) {
    if (Ri(n, t)) return;
    e.uniformMatrix3fv(this.addr, false, t), Ai(n, t);
  } else {
    if (Ri(n, i)) return;
    Ti.set(i), e.uniformMatrix3fv(this.addr, false, Ti), Ai(n, i);
  }
}
function yi(e, t) {
  const n = this.cache, i = t.elements;
  if (void 0 === i) {
    if (Ri(n, t)) return;
    e.uniformMatrix4fv(this.addr, false, t), Ai(n, t);
  } else {
    if (Ri(n, i)) return;
    Si.set(i), e.uniformMatrix4fv(this.addr, false, Si), Ai(n, i);
  }
}
function Ii(e, t) {
  const n = this.cache;
  n[0] !== t && (e.uniform1i(this.addr, t), n[0] = t);
}
function Ni(e, t) {
  const n = this.cache;
  if (void 0 !== t.x) n[0] === t.x && n[1] === t.y || (e.uniform2i(this.addr, t.x, t.y), n[0] = t.x, n[1] = t.y);
  else {
    if (Ri(n, t)) return;
    e.uniform2iv(this.addr, t), Ai(n, t);
  }
}
function Oi(e, t) {
  const n = this.cache;
  if (void 0 !== t.x) n[0] === t.x && n[1] === t.y && n[2] === t.z || (e.uniform3i(this.addr, t.x, t.y, t.z), n[0] = t.x, n[1] = t.y, n[2] = t.z);
  else {
    if (Ri(n, t)) return;
    e.uniform3iv(this.addr, t), Ai(n, t);
  }
}
function Fi(e, t) {
  const n = this.cache;
  if (void 0 !== t.x) n[0] === t.x && n[1] === t.y && n[2] === t.z && n[3] === t.w || (e.uniform4i(this.addr, t.x, t.y, t.z, t.w), n[0] = t.x, n[1] = t.y, n[2] = t.z, n[3] = t.w);
  else {
    if (Ri(n, t)) return;
    e.uniform4iv(this.addr, t), Ai(n, t);
  }
}
function Bi(e, t) {
  const n = this.cache;
  n[0] !== t && (e.uniform1ui(this.addr, t), n[0] = t);
}
function Hi(e, t) {
  const n = this.cache;
  if (void 0 !== t.x) n[0] === t.x && n[1] === t.y || (e.uniform2ui(this.addr, t.x, t.y), n[0] = t.x, n[1] = t.y);
  else {
    if (Ri(n, t)) return;
    e.uniform2uiv(this.addr, t), Ai(n, t);
  }
}
function Gi(e, t) {
  const n = this.cache;
  if (void 0 !== t.x) n[0] === t.x && n[1] === t.y && n[2] === t.z || (e.uniform3ui(this.addr, t.x, t.y, t.z), n[0] = t.x, n[1] = t.y, n[2] = t.z);
  else {
    if (Ri(n, t)) return;
    e.uniform3uiv(this.addr, t), Ai(n, t);
  }
}
function Vi(e, t) {
  const n = this.cache;
  if (void 0 !== t.x) n[0] === t.x && n[1] === t.y && n[2] === t.z && n[3] === t.w || (e.uniform4ui(this.addr, t.x, t.y, t.z, t.w), n[0] = t.x, n[1] = t.y, n[2] = t.z, n[3] = t.w);
  else {
    if (Ri(n, t)) return;
    e.uniform4uiv(this.addr, t), Ai(n, t);
  }
}
function zi(e, t, n) {
  const i = this.cache, r = n.allocateTextureUnit();
  let a;
  i[0] !== r && (e.uniform1i(this.addr, r), i[0] = r), this.type === e.SAMPLER_2D_SHADOW ? (mi.compareFunction = _mpChunkDeps_three_build_three_core_min.bs, a = mi) : a = pi, n.setTexture2D(t || a, r);
}
function ki(e, t, n) {
  const i = this.cache, r = n.allocateTextureUnit();
  i[0] !== r && (e.uniform1i(this.addr, r), i[0] = r), n.setTexture3D(t || _i, r);
}
function Wi(e, t, n) {
  const i = this.cache, r = n.allocateTextureUnit();
  i[0] !== r && (e.uniform1i(this.addr, r), i[0] = r), n.setTextureCube(t || gi, r);
}
function Xi(e, t, n) {
  const i = this.cache, r = n.allocateTextureUnit();
  i[0] !== r && (e.uniform1i(this.addr, r), i[0] = r), n.setTexture2DArray(t || hi, r);
}
function Yi(e, t) {
  e.uniform1fv(this.addr, t);
}
function ji(e, t) {
  const n = xi(t, this.size, 2);
  e.uniform2fv(this.addr, n);
}
function Ki(e, t) {
  const n = xi(t, this.size, 3);
  e.uniform3fv(this.addr, n);
}
function qi(e, t) {
  const n = xi(t, this.size, 4);
  e.uniform4fv(this.addr, n);
}
function Zi(e, t) {
  const n = xi(t, this.size, 4);
  e.uniformMatrix2fv(this.addr, false, n);
}
function $i(e, t) {
  const n = xi(t, this.size, 9);
  e.uniformMatrix3fv(this.addr, false, n);
}
function Qi(e, t) {
  const n = xi(t, this.size, 16);
  e.uniformMatrix4fv(this.addr, false, n);
}
function Ji(e, t) {
  e.uniform1iv(this.addr, t);
}
function er(e, t) {
  e.uniform2iv(this.addr, t);
}
function tr(e, t) {
  e.uniform3iv(this.addr, t);
}
function nr(e, t) {
  e.uniform4iv(this.addr, t);
}
function ir(e, t) {
  e.uniform1uiv(this.addr, t);
}
function rr(e, t) {
  e.uniform2uiv(this.addr, t);
}
function ar(e, t) {
  e.uniform3uiv(this.addr, t);
}
function or(e, t) {
  e.uniform4uiv(this.addr, t);
}
function sr(e, t, n) {
  const i = this.cache, r = t.length, a = bi(n, r);
  Ri(i, a) || (e.uniform1iv(this.addr, a), Ai(i, a));
  for (let e2 = 0; e2 !== r; ++e2) n.setTexture2D(t[e2] || pi, a[e2]);
}
function lr(e, t, n) {
  const i = this.cache, r = t.length, a = bi(n, r);
  Ri(i, a) || (e.uniform1iv(this.addr, a), Ai(i, a));
  for (let e2 = 0; e2 !== r; ++e2) n.setTexture3D(t[e2] || _i, a[e2]);
}
function cr(e, t, n) {
  const i = this.cache, r = t.length, a = bi(n, r);
  Ri(i, a) || (e.uniform1iv(this.addr, a), Ai(i, a));
  for (let e2 = 0; e2 !== r; ++e2) n.setTextureCube(t[e2] || gi, a[e2]);
}
function dr(e, t, n) {
  const i = this.cache, r = t.length, a = bi(n, r);
  Ri(i, a) || (e.uniform1iv(this.addr, a), Ai(i, a));
  for (let e2 = 0; e2 !== r; ++e2) n.setTexture2DArray(t[e2] || hi, a[e2]);
}
class ur {
  constructor(e, t, n) {
    this.id = e, this.addr = n, this.cache = [], this.type = t.type, this.setValue = function(e2) {
      switch (e2) {
        case 5126:
          return Ci;
        case 35664:
          return Li;
        case 35665:
          return Pi;
        case 35666:
          return Ui;
        case 35674:
          return wi;
        case 35675:
          return Di;
        case 35676:
          return yi;
        case 5124:
        case 35670:
          return Ii;
        case 35667:
        case 35671:
          return Ni;
        case 35668:
        case 35672:
          return Oi;
        case 35669:
        case 35673:
          return Fi;
        case 5125:
          return Bi;
        case 36294:
          return Hi;
        case 36295:
          return Gi;
        case 36296:
          return Vi;
        case 35678:
        case 36198:
        case 36298:
        case 36306:
        case 35682:
          return zi;
        case 35679:
        case 36299:
        case 36307:
          return ki;
        case 35680:
        case 36300:
        case 36308:
        case 36293:
          return Wi;
        case 36289:
        case 36303:
        case 36311:
        case 36292:
          return Xi;
      }
    }(t.type);
  }
}
class fr {
  constructor(e, t, n) {
    this.id = e, this.addr = n, this.cache = [], this.type = t.type, this.size = t.size, this.setValue = function(e2) {
      switch (e2) {
        case 5126:
          return Yi;
        case 35664:
          return ji;
        case 35665:
          return Ki;
        case 35666:
          return qi;
        case 35674:
          return Zi;
        case 35675:
          return $i;
        case 35676:
          return Qi;
        case 5124:
        case 35670:
          return Ji;
        case 35667:
        case 35671:
          return er;
        case 35668:
        case 35672:
          return tr;
        case 35669:
        case 35673:
          return nr;
        case 5125:
          return ir;
        case 36294:
          return rr;
        case 36295:
          return ar;
        case 36296:
          return or;
        case 35678:
        case 36198:
        case 36298:
        case 36306:
        case 35682:
          return sr;
        case 35679:
        case 36299:
        case 36307:
          return lr;
        case 35680:
        case 36300:
        case 36308:
        case 36293:
          return cr;
        case 36289:
        case 36303:
        case 36311:
        case 36292:
          return dr;
      }
    }(t.type);
  }
}
class pr {
  constructor(e) {
    this.id = e, this.seq = [], this.map = {};
  }
  setValue(e, t, n) {
    const i = this.seq;
    for (let r = 0, a = i.length; r !== a; ++r) {
      const a2 = i[r];
      a2.setValue(e, t[a2.id], n);
    }
  }
}
const mr = /(\w+)(\])?(\[|\.)?/g;
function hr(e, t) {
  e.seq.push(t), e.map[t.id] = t;
}
function _r(e, t, n) {
  const i = e.name, r = i.length;
  for (mr.lastIndex = 0; ; ) {
    const a = mr.exec(i), o = mr.lastIndex;
    let s = a[1];
    const l = "]" === a[2], c = a[3];
    if (l && (s |= 0), void 0 === c || "[" === c && o + 2 === r) {
      hr(n, void 0 === c ? new ur(s, e, t) : new fr(s, e, t));
      break;
    }
    {
      let e2 = n.map[s];
      void 0 === e2 && (e2 = new pr(s), hr(n, e2)), n = e2;
    }
  }
}
class gr {
  constructor(e, t) {
    this.seq = [], this.map = {};
    const n = e.getProgramParameter(t, e.ACTIVE_UNIFORMS);
    for (let i = 0; i < n; ++i) {
      const n2 = e.getActiveUniform(t, i);
      _r(n2, e.getUniformLocation(t, n2.name), this);
    }
  }
  setValue(e, t, n, i) {
    const r = this.map[t];
    void 0 !== r && r.setValue(e, n, i);
  }
  setOptional(e, t, n) {
    const i = t[n];
    void 0 !== i && this.setValue(e, n, i);
  }
  static upload(e, t, n, i) {
    for (let r = 0, a = t.length; r !== a; ++r) {
      const a2 = t[r], o = n[a2.id];
      false !== o.needsUpdate && a2.setValue(e, o.value, i);
    }
  }
  static seqWithValue(e, t) {
    const n = [];
    for (let i = 0, r = e.length; i !== r; ++i) {
      const r2 = e[i];
      r2.id in t && n.push(r2);
    }
    return n;
  }
}
function vr(e, t, n) {
  const i = e.createShader(t);
  return e.shaderSource(i, n), e.compileShader(i), i;
}
let Er = 0;
const Sr = new _mpChunkDeps_three_build_three_core_min.Gs();
function Tr(e, t, n) {
  const i = e.getShaderParameter(t, e.COMPILE_STATUS), r = e.getShaderInfoLog(t).trim();
  if (i && "" === r) return "";
  const a = /ERROR: 0:(\d+)/.exec(r);
  if (a) {
    const i2 = parseInt(a[1]);
    return n.toUpperCase() + "\n\n" + r + "\n\n" + function(e2, t2) {
      const n2 = e2.split("\n"), i3 = [], r2 = Math.max(t2 - 6, 0), a2 = Math.min(t2 + 6, n2.length);
      for (let e3 = r2; e3 < a2; e3++) {
        const r3 = e3 + 1;
        i3.push(`${r3 === t2 ? ">" : " "} ${r3}: ${n2[e3]}`);
      }
      return i3.join("\n");
    }(e.getShaderSource(t), i2);
  }
  return r;
}
function Mr(e, t) {
  const n = function(e2) {
    _mpChunkDeps_three_build_three_core_min.ui._getMatrix(Sr, _mpChunkDeps_three_build_three_core_min.ui.workingColorSpace, e2);
    const t2 = `mat3( ${Sr.elements.map((e3) => e3.toFixed(4))} )`;
    switch (_mpChunkDeps_three_build_three_core_min.ui.getTransfer(e2)) {
      case _mpChunkDeps_three_build_three_core_min.Qe:
        return [t2, "LinearTransferOETF"];
      case _mpChunkDeps_three_build_three_core_min.Ke:
        return [t2, "sRGBTransferOETF"];
      default:
        return console.warn("THREE.WebGLProgram: Unsupported color space: ", e2), [t2, "LinearTransferOETF"];
    }
  }(t);
  return [`vec4 ${e}( vec4 value ) {`, `	return ${n[1]}( vec4( value.rgb * ${n[0]}, value.a ) );`, "}"].join("\n");
}
function xr(e, t) {
  let n;
  switch (t) {
    case _mpChunkDeps_three_build_three_core_min.Q:
      n = "Linear";
      break;
    case _mpChunkDeps_three_build_three_core_min.K:
      n = "Reinhard";
      break;
    case _mpChunkDeps_three_build_three_core_min.tt:
      n = "Cineon";
      break;
    case _mpChunkDeps_three_build_three_core_min.et:
      n = "ACESFilmic";
      break;
    case _mpChunkDeps_three_build_three_core_min.it:
      n = "AgX";
      break;
    case _mpChunkDeps_three_build_three_core_min.rt:
      n = "Neutral";
      break;
    case _mpChunkDeps_three_build_three_core_min.st:
      n = "Custom";
      break;
    default:
      console.warn("THREE.WebGLProgram: Unsupported toneMapping:", t), n = "Linear";
  }
  return "vec3 " + e + "( vec3 color ) { return " + n + "ToneMapping( color ); }";
}
const Rr = new _mpChunkDeps_three_build_three_core_min.Ii();
function Ar() {
  _mpChunkDeps_three_build_three_core_min.ui.getLuminanceCoefficients(Rr);
  return ["float luminance( const in vec3 rgb ) {", `	const vec3 weights = vec3( ${Rr.x.toFixed(4)}, ${Rr.y.toFixed(4)}, ${Rr.z.toFixed(4)} );`, "	return dot( weights, rgb );", "}"].join("\n");
}
function br(e) {
  return "" !== e;
}
function Cr(e, t) {
  const n = t.numSpotLightShadows + t.numSpotLightMaps - t.numSpotLightShadowsWithMaps;
  return e.replace(/NUM_DIR_LIGHTS/g, t.numDirLights).replace(/NUM_SPOT_LIGHTS/g, t.numSpotLights).replace(/NUM_SPOT_LIGHT_MAPS/g, t.numSpotLightMaps).replace(/NUM_SPOT_LIGHT_COORDS/g, n).replace(/NUM_RECT_AREA_LIGHTS/g, t.numRectAreaLights).replace(/NUM_POINT_LIGHTS/g, t.numPointLights).replace(/NUM_HEMI_LIGHTS/g, t.numHemiLights).replace(/NUM_DIR_LIGHT_SHADOWS/g, t.numDirLightShadows).replace(/NUM_SPOT_LIGHT_SHADOWS_WITH_MAPS/g, t.numSpotLightShadowsWithMaps).replace(/NUM_SPOT_LIGHT_SHADOWS/g, t.numSpotLightShadows).replace(/NUM_POINT_LIGHT_SHADOWS/g, t.numPointLightShadows);
}
function Lr(e, t) {
  return e.replace(/NUM_CLIPPING_PLANES/g, t.numClippingPlanes).replace(/UNION_CLIPPING_PLANES/g, t.numClippingPlanes - t.numClipIntersection);
}
const Pr = /^[ \t]*#include +<([\w\d./]+)>/gm;
function Ur(e) {
  return e.replace(Pr, Dr);
}
const wr = /* @__PURE__ */ new Map();
function Dr(e, t) {
  let n = wn[t];
  if (void 0 === n) {
    const e2 = wr.get(t);
    if (void 0 === e2) throw new Error("Can not resolve #include <" + t + ">");
    n = wn[e2], console.warn('THREE.WebGLRenderer: Shader chunk "%s" has been deprecated. Use "%s" instead.', t, e2);
  }
  return Ur(n);
}
const yr = /#pragma unroll_loop_start\s+for\s*\(\s*int\s+i\s*=\s*(\d+)\s*;\s*i\s*<\s*(\d+)\s*;\s*i\s*\+\+\s*\)\s*{([\s\S]+?)}\s+#pragma unroll_loop_end/g;
function Ir(e) {
  return e.replace(yr, Nr);
}
function Nr(e, t, n, i) {
  let r = "";
  for (let e2 = parseInt(t); e2 < parseInt(n); e2++) r += i.replace(/\[\s*i\s*\]/g, "[ " + e2 + " ]").replace(/UNROLLED_LOOP_INDEX/g, e2);
  return r;
}
function Or(e) {
  let t = `precision ${e.precision} float;
	precision ${e.precision} int;
	precision ${e.precision} sampler2D;
	precision ${e.precision} samplerCube;
	precision ${e.precision} sampler3D;
	precision ${e.precision} sampler2DArray;
	precision ${e.precision} sampler2DShadow;
	precision ${e.precision} samplerCubeShadow;
	precision ${e.precision} sampler2DArrayShadow;
	precision ${e.precision} isampler2D;
	precision ${e.precision} isampler3D;
	precision ${e.precision} isamplerCube;
	precision ${e.precision} isampler2DArray;
	precision ${e.precision} usampler2D;
	precision ${e.precision} usampler3D;
	precision ${e.precision} usamplerCube;
	precision ${e.precision} usampler2DArray;
	`;
  return "highp" === e.precision ? t += "\n#define HIGH_PRECISION" : "mediump" === e.precision ? t += "\n#define MEDIUM_PRECISION" : "lowp" === e.precision && (t += "\n#define LOW_PRECISION"), t;
}
function Fr(e, t, n, i) {
  const r = e.getContext(), o = n.defines;
  let s = n.vertexShader, l = n.fragmentShader;
  const c = function(e2) {
    let t2 = "SHADOWMAP_TYPE_BASIC";
    return e2.shadowMapType === _mpChunkDeps_three_build_three_core_min.h ? t2 = "SHADOWMAP_TYPE_PCF" : e2.shadowMapType === _mpChunkDeps_three_build_three_core_min.l ? t2 = "SHADOWMAP_TYPE_PCF_SOFT" : e2.shadowMapType === _mpChunkDeps_three_build_three_core_min.c && (t2 = "SHADOWMAP_TYPE_VSM"), t2;
  }(n), d = function(e2) {
    let t2 = "ENVMAP_TYPE_CUBE";
    if (e2.envMap) switch (e2.envMapMode) {
      case _mpChunkDeps_three_build_three_core_min.ht:
      case _mpChunkDeps_three_build_three_core_min.lt:
        t2 = "ENVMAP_TYPE_CUBE";
        break;
      case _mpChunkDeps_three_build_three_core_min.dt:
        t2 = "ENVMAP_TYPE_CUBE_UV";
    }
    return t2;
  }(n), u = function(e2) {
    let t2 = "ENVMAP_MODE_REFLECTION";
    e2.envMap && e2.envMapMode === _mpChunkDeps_three_build_three_core_min.lt && (t2 = "ENVMAP_MODE_REFRACTION");
    return t2;
  }(n), f = function(e2) {
    let t2 = "ENVMAP_BLENDING_NONE";
    if (e2.envMap) switch (e2.combine) {
      case _mpChunkDeps_three_build_three_core_min.Y:
        t2 = "ENVMAP_BLENDING_MULTIPLY";
        break;
      case _mpChunkDeps_three_build_three_core_min.Z:
        t2 = "ENVMAP_BLENDING_MIX";
        break;
      case _mpChunkDeps_three_build_three_core_min.G:
        t2 = "ENVMAP_BLENDING_ADD";
    }
    return t2;
  }(n), p = function(e2) {
    const t2 = e2.envMapCubeUVHeight;
    if (null === t2) return null;
    const n2 = Math.log2(t2) - 2, i2 = 1 / t2;
    return { texelWidth: 1 / (3 * Math.max(Math.pow(2, n2), 112)), texelHeight: i2, maxMip: n2 };
  }(n), m = function(e2) {
    return [e2.extensionClipCullDistance ? "#extension GL_ANGLE_clip_cull_distance : require" : "", e2.extensionMultiDraw ? "#extension GL_ANGLE_multi_draw : require" : ""].filter(br).join("\n");
  }(n), h = function(e2) {
    const t2 = [];
    for (const n2 in e2) {
      const i2 = e2[n2];
      false !== i2 && t2.push("#define " + n2 + " " + i2);
    }
    return t2.join("\n");
  }(o), _ = r.createProgram();
  let g, v, E = n.glslVersion ? "#version " + n.glslVersion + "\n" : "";
  n.isRawShaderMaterial ? (g = ["#define SHADER_TYPE " + n.shaderType, "#define SHADER_NAME " + n.shaderName, h].filter(br).join("\n"), g.length > 0 && (g += "\n"), v = ["#define SHADER_TYPE " + n.shaderType, "#define SHADER_NAME " + n.shaderName, h].filter(br).join("\n"), v.length > 0 && (v += "\n")) : (g = [Or(n), "#define SHADER_TYPE " + n.shaderType, "#define SHADER_NAME " + n.shaderName, h, n.extensionClipCullDistance ? "#define USE_CLIP_DISTANCE" : "", n.batching ? "#define USE_BATCHING" : "", n.batchingColor ? "#define USE_BATCHING_COLOR" : "", n.instancing ? "#define USE_INSTANCING" : "", n.instancingColor ? "#define USE_INSTANCING_COLOR" : "", n.instancingMorph ? "#define USE_INSTANCING_MORPH" : "", n.useFog && n.fog ? "#define USE_FOG" : "", n.useFog && n.fogExp2 ? "#define FOG_EXP2" : "", n.map ? "#define USE_MAP" : "", n.envMap ? "#define USE_ENVMAP" : "", n.envMap ? "#define " + u : "", n.lightMap ? "#define USE_LIGHTMAP" : "", n.aoMap ? "#define USE_AOMAP" : "", n.bumpMap ? "#define USE_BUMPMAP" : "", n.normalMap ? "#define USE_NORMALMAP" : "", n.normalMapObjectSpace ? "#define USE_NORMALMAP_OBJECTSPACE" : "", n.normalMapTangentSpace ? "#define USE_NORMALMAP_TANGENTSPACE" : "", n.displacementMap ? "#define USE_DISPLACEMENTMAP" : "", n.emissiveMap ? "#define USE_EMISSIVEMAP" : "", n.anisotropy ? "#define USE_ANISOTROPY" : "", n.anisotropyMap ? "#define USE_ANISOTROPYMAP" : "", n.clearcoatMap ? "#define USE_CLEARCOATMAP" : "", n.clearcoatRoughnessMap ? "#define USE_CLEARCOAT_ROUGHNESSMAP" : "", n.clearcoatNormalMap ? "#define USE_CLEARCOAT_NORMALMAP" : "", n.iridescenceMap ? "#define USE_IRIDESCENCEMAP" : "", n.iridescenceThicknessMap ? "#define USE_IRIDESCENCE_THICKNESSMAP" : "", n.specularMap ? "#define USE_SPECULARMAP" : "", n.specularColorMap ? "#define USE_SPECULAR_COLORMAP" : "", n.specularIntensityMap ? "#define USE_SPECULAR_INTENSITYMAP" : "", n.roughnessMap ? "#define USE_ROUGHNESSMAP" : "", n.metalnessMap ? "#define USE_METALNESSMAP" : "", n.alphaMap ? "#define USE_ALPHAMAP" : "", n.alphaHash ? "#define USE_ALPHAHASH" : "", n.transmission ? "#define USE_TRANSMISSION" : "", n.transmissionMap ? "#define USE_TRANSMISSIONMAP" : "", n.thicknessMap ? "#define USE_THICKNESSMAP" : "", n.sheenColorMap ? "#define USE_SHEEN_COLORMAP" : "", n.sheenRoughnessMap ? "#define USE_SHEEN_ROUGHNESSMAP" : "", n.mapUv ? "#define MAP_UV " + n.mapUv : "", n.alphaMapUv ? "#define ALPHAMAP_UV " + n.alphaMapUv : "", n.lightMapUv ? "#define LIGHTMAP_UV " + n.lightMapUv : "", n.aoMapUv ? "#define AOMAP_UV " + n.aoMapUv : "", n.emissiveMapUv ? "#define EMISSIVEMAP_UV " + n.emissiveMapUv : "", n.bumpMapUv ? "#define BUMPMAP_UV " + n.bumpMapUv : "", n.normalMapUv ? "#define NORMALMAP_UV " + n.normalMapUv : "", n.displacementMapUv ? "#define DISPLACEMENTMAP_UV " + n.displacementMapUv : "", n.metalnessMapUv ? "#define METALNESSMAP_UV " + n.metalnessMapUv : "", n.roughnessMapUv ? "#define ROUGHNESSMAP_UV " + n.roughnessMapUv : "", n.anisotropyMapUv ? "#define ANISOTROPYMAP_UV " + n.anisotropyMapUv : "", n.clearcoatMapUv ? "#define CLEARCOATMAP_UV " + n.clearcoatMapUv : "", n.clearcoatNormalMapUv ? "#define CLEARCOAT_NORMALMAP_UV " + n.clearcoatNormalMapUv : "", n.clearcoatRoughnessMapUv ? "#define CLEARCOAT_ROUGHNESSMAP_UV " + n.clearcoatRoughnessMapUv : "", n.iridescenceMapUv ? "#define IRIDESCENCEMAP_UV " + n.iridescenceMapUv : "", n.iridescenceThicknessMapUv ? "#define IRIDESCENCE_THICKNESSMAP_UV " + n.iridescenceThicknessMapUv : "", n.sheenColorMapUv ? "#define SHEEN_COLORMAP_UV " + n.sheenColorMapUv : "", n.sheenRoughnessMapUv ? "#define SHEEN_ROUGHNESSMAP_UV " + n.sheenRoughnessMapUv : "", n.specularMapUv ? "#define SPECULARMAP_UV " + n.specularMapUv : "", n.specularColorMapUv ? "#define SPECULAR_COLORMAP_UV " + n.specularColorMapUv : "", n.specularIntensityMapUv ? "#define SPECULAR_INTENSITYMAP_UV " + n.specularIntensityMapUv : "", n.transmissionMapUv ? "#define TRANSMISSIONMAP_UV " + n.transmissionMapUv : "", n.thicknessMapUv ? "#define THICKNESSMAP_UV " + n.thicknessMapUv : "", n.vertexTangents && false === n.flatShading ? "#define USE_TANGENT" : "", n.vertexColors ? "#define USE_COLOR" : "", n.vertexAlphas ? "#define USE_COLOR_ALPHA" : "", n.vertexUv1s ? "#define USE_UV1" : "", n.vertexUv2s ? "#define USE_UV2" : "", n.vertexUv3s ? "#define USE_UV3" : "", n.pointsUvs ? "#define USE_POINTS_UV" : "", n.flatShading ? "#define FLAT_SHADED" : "", n.skinning ? "#define USE_SKINNING" : "", n.morphTargets ? "#define USE_MORPHTARGETS" : "", n.morphNormals && false === n.flatShading ? "#define USE_MORPHNORMALS" : "", n.morphColors ? "#define USE_MORPHCOLORS" : "", n.morphTargetsCount > 0 ? "#define MORPHTARGETS_TEXTURE_STRIDE " + n.morphTextureStride : "", n.morphTargetsCount > 0 ? "#define MORPHTARGETS_COUNT " + n.morphTargetsCount : "", n.doubleSided ? "#define DOUBLE_SIDED" : "", n.flipSided ? "#define FLIP_SIDED" : "", n.shadowMapEnabled ? "#define USE_SHADOWMAP" : "", n.shadowMapEnabled ? "#define " + c : "", n.sizeAttenuation ? "#define USE_SIZEATTENUATION" : "", n.numLightProbes > 0 ? "#define USE_LIGHT_PROBES" : "", n.logarithmicDepthBuffer ? "#define USE_LOGDEPTHBUF" : "", n.reverseDepthBuffer ? "#define USE_REVERSEDEPTHBUF" : "", "uniform mat4 modelMatrix;", "uniform mat4 modelViewMatrix;", "uniform mat4 projectionMatrix;", "uniform mat4 viewMatrix;", "uniform mat3 normalMatrix;", "uniform vec3 cameraPosition;", "uniform bool isOrthographic;", "#ifdef USE_INSTANCING", "	attribute mat4 instanceMatrix;", "#endif", "#ifdef USE_INSTANCING_COLOR", "	attribute vec3 instanceColor;", "#endif", "#ifdef USE_INSTANCING_MORPH", "	uniform sampler2D morphTexture;", "#endif", "attribute vec3 position;", "attribute vec3 normal;", "attribute vec2 uv;", "#ifdef USE_UV1", "	attribute vec2 uv1;", "#endif", "#ifdef USE_UV2", "	attribute vec2 uv2;", "#endif", "#ifdef USE_UV3", "	attribute vec2 uv3;", "#endif", "#ifdef USE_TANGENT", "	attribute vec4 tangent;", "#endif", "#if defined( USE_COLOR_ALPHA )", "	attribute vec4 color;", "#elif defined( USE_COLOR )", "	attribute vec3 color;", "#endif", "#ifdef USE_SKINNING", "	attribute vec4 skinIndex;", "	attribute vec4 skinWeight;", "#endif", "\n"].filter(br).join("\n"), v = [Or(n), "#define SHADER_TYPE " + n.shaderType, "#define SHADER_NAME " + n.shaderName, h, n.useFog && n.fog ? "#define USE_FOG" : "", n.useFog && n.fogExp2 ? "#define FOG_EXP2" : "", n.alphaToCoverage ? "#define ALPHA_TO_COVERAGE" : "", n.map ? "#define USE_MAP" : "", n.matcap ? "#define USE_MATCAP" : "", n.envMap ? "#define USE_ENVMAP" : "", n.envMap ? "#define " + d : "", n.envMap ? "#define " + u : "", n.envMap ? "#define " + f : "", p ? "#define CUBEUV_TEXEL_WIDTH " + p.texelWidth : "", p ? "#define CUBEUV_TEXEL_HEIGHT " + p.texelHeight : "", p ? "#define CUBEUV_MAX_MIP " + p.maxMip + ".0" : "", n.lightMap ? "#define USE_LIGHTMAP" : "", n.aoMap ? "#define USE_AOMAP" : "", n.bumpMap ? "#define USE_BUMPMAP" : "", n.normalMap ? "#define USE_NORMALMAP" : "", n.normalMapObjectSpace ? "#define USE_NORMALMAP_OBJECTSPACE" : "", n.normalMapTangentSpace ? "#define USE_NORMALMAP_TANGENTSPACE" : "", n.emissiveMap ? "#define USE_EMISSIVEMAP" : "", n.anisotropy ? "#define USE_ANISOTROPY" : "", n.anisotropyMap ? "#define USE_ANISOTROPYMAP" : "", n.clearcoat ? "#define USE_CLEARCOAT" : "", n.clearcoatMap ? "#define USE_CLEARCOATMAP" : "", n.clearcoatRoughnessMap ? "#define USE_CLEARCOAT_ROUGHNESSMAP" : "", n.clearcoatNormalMap ? "#define USE_CLEARCOAT_NORMALMAP" : "", n.dispersion ? "#define USE_DISPERSION" : "", n.iridescence ? "#define USE_IRIDESCENCE" : "", n.iridescenceMap ? "#define USE_IRIDESCENCEMAP" : "", n.iridescenceThicknessMap ? "#define USE_IRIDESCENCE_THICKNESSMAP" : "", n.specularMap ? "#define USE_SPECULARMAP" : "", n.specularColorMap ? "#define USE_SPECULAR_COLORMAP" : "", n.specularIntensityMap ? "#define USE_SPECULAR_INTENSITYMAP" : "", n.roughnessMap ? "#define USE_ROUGHNESSMAP" : "", n.metalnessMap ? "#define USE_METALNESSMAP" : "", n.alphaMap ? "#define USE_ALPHAMAP" : "", n.alphaTest ? "#define USE_ALPHATEST" : "", n.alphaHash ? "#define USE_ALPHAHASH" : "", n.sheen ? "#define USE_SHEEN" : "", n.sheenColorMap ? "#define USE_SHEEN_COLORMAP" : "", n.sheenRoughnessMap ? "#define USE_SHEEN_ROUGHNESSMAP" : "", n.transmission ? "#define USE_TRANSMISSION" : "", n.transmissionMap ? "#define USE_TRANSMISSIONMAP" : "", n.thicknessMap ? "#define USE_THICKNESSMAP" : "", n.vertexTangents && false === n.flatShading ? "#define USE_TANGENT" : "", n.vertexColors || n.instancingColor || n.batchingColor ? "#define USE_COLOR" : "", n.vertexAlphas ? "#define USE_COLOR_ALPHA" : "", n.vertexUv1s ? "#define USE_UV1" : "", n.vertexUv2s ? "#define USE_UV2" : "", n.vertexUv3s ? "#define USE_UV3" : "", n.pointsUvs ? "#define USE_POINTS_UV" : "", n.gradientMap ? "#define USE_GRADIENTMAP" : "", n.flatShading ? "#define FLAT_SHADED" : "", n.doubleSided ? "#define DOUBLE_SIDED" : "", n.flipSided ? "#define FLIP_SIDED" : "", n.shadowMapEnabled ? "#define USE_SHADOWMAP" : "", n.shadowMapEnabled ? "#define " + c : "", n.premultipliedAlpha ? "#define PREMULTIPLIED_ALPHA" : "", n.numLightProbes > 0 ? "#define USE_LIGHT_PROBES" : "", n.decodeVideoTexture ? "#define DECODE_VIDEO_TEXTURE" : "", n.decodeVideoTextureEmissive ? "#define DECODE_VIDEO_TEXTURE_EMISSIVE" : "", n.logarithmicDepthBuffer ? "#define USE_LOGDEPTHBUF" : "", n.reverseDepthBuffer ? "#define USE_REVERSEDEPTHBUF" : "", "uniform mat4 viewMatrix;", "uniform vec3 cameraPosition;", "uniform bool isOrthographic;", n.toneMapping !== _mpChunkDeps_three_build_three_core_min.$ ? "#define TONE_MAPPING" : "", n.toneMapping !== _mpChunkDeps_three_build_three_core_min.$ ? wn.tonemapping_pars_fragment : "", n.toneMapping !== _mpChunkDeps_three_build_three_core_min.$ ? xr("toneMapping", n.toneMapping) : "", n.dithering ? "#define DITHERING" : "", n.opaque ? "#define OPAQUE" : "", wn.colorspace_pars_fragment, Mr("linearToOutputTexel", n.outputColorSpace), Ar(), n.useDepthPacking ? "#define DEPTH_PACKING " + n.depthPacking : "", "\n"].filter(br).join("\n")), s = Ur(s), s = Cr(s, n), s = Lr(s, n), l = Ur(l), l = Cr(l, n), l = Lr(l, n), s = Ir(s), l = Ir(l), true !== n.isRawShaderMaterial && (E = "#version 300 es\n", g = [m, "#define attribute in", "#define varying out", "#define texture2D texture"].join("\n") + "\n" + g, v = ["#define varying in", n.glslVersion === _mpChunkDeps_three_build_three_core_min.Ps ? "" : "layout(location = 0) out highp vec4 pc_fragColor;", n.glslVersion === _mpChunkDeps_three_build_three_core_min.Ps ? "" : "#define gl_FragColor pc_fragColor", "#define gl_FragDepthEXT gl_FragDepth", "#define texture2D texture", "#define textureCube texture", "#define texture2DProj textureProj", "#define texture2DLodEXT textureLod", "#define texture2DProjLodEXT textureProjLod", "#define textureCubeLodEXT textureLod", "#define texture2DGradEXT textureGrad", "#define texture2DProjGradEXT textureProjGrad", "#define textureCubeGradEXT textureGrad"].join("\n") + "\n" + v);
  const S = E + g + s, T = E + v + l, M = vr(r, r.VERTEX_SHADER, S), x = vr(r, r.FRAGMENT_SHADER, T);
  function R(t2) {
    if (e.debug.checkShaderErrors) {
      const n2 = r.getProgramInfoLog(_).trim(), i2 = r.getShaderInfoLog(M).trim(), a = r.getShaderInfoLog(x).trim();
      let o2 = true, s2 = true;
      if (false === r.getProgramParameter(_, r.LINK_STATUS)) if (o2 = false, "function" == typeof e.debug.onShaderError) e.debug.onShaderError(r, _, M, x);
      else {
        const e2 = Tr(r, M, "vertex"), i3 = Tr(r, x, "fragment");
        console.error("THREE.WebGLProgram: Shader Error " + r.getError() + " - VALIDATE_STATUS " + r.getProgramParameter(_, r.VALIDATE_STATUS) + "\n\nMaterial Name: " + t2.name + "\nMaterial Type: " + t2.type + "\n\nProgram Info Log: " + n2 + "\n" + e2 + "\n" + i3);
      }
      else "" !== n2 ? console.warn("THREE.WebGLProgram: Program Info Log:", n2) : "" !== i2 && "" !== a || (s2 = false);
      s2 && (t2.diagnostics = { runnable: o2, programLog: n2, vertexShader: { log: i2, prefix: g }, fragmentShader: { log: a, prefix: v } });
    }
    r.deleteShader(M), r.deleteShader(x), A = new gr(r, _), b = function(e2, t3) {
      const n2 = {}, i2 = e2.getProgramParameter(t3, e2.ACTIVE_ATTRIBUTES);
      for (let r2 = 0; r2 < i2; r2++) {
        const i3 = e2.getActiveAttrib(t3, r2), a = i3.name;
        let o2 = 1;
        i3.type === e2.FLOAT_MAT2 && (o2 = 2), i3.type === e2.FLOAT_MAT3 && (o2 = 3), i3.type === e2.FLOAT_MAT4 && (o2 = 4), n2[a] = { type: i3.type, location: e2.getAttribLocation(t3, a), locationSize: o2 };
      }
      return n2;
    }(r, _);
  }
  let A, b;
  r.attachShader(_, M), r.attachShader(_, x), void 0 !== n.index0AttributeName ? r.bindAttribLocation(_, 0, n.index0AttributeName) : true === n.morphTargets && r.bindAttribLocation(_, 0, "position"), r.linkProgram(_), this.getUniforms = function() {
    return void 0 === A && R(this), A;
  }, this.getAttributes = function() {
    return void 0 === b && R(this), b;
  };
  let P = false === n.rendererExtensionParallelShaderCompile;
  return this.isReady = function() {
    return false === P && (P = r.getProgramParameter(_, 37297)), P;
  }, this.destroy = function() {
    i.releaseStatesOfProgram(this), r.deleteProgram(_), this.program = void 0;
  }, this.type = n.shaderType, this.name = n.shaderName, this.id = Er++, this.cacheKey = t, this.usedTimes = 1, this.program = _, this.vertexShader = M, this.fragmentShader = x, this;
}
let Br = 0;
class Hr {
  constructor() {
    this.shaderCache = /* @__PURE__ */ new Map(), this.materialCache = /* @__PURE__ */ new Map();
  }
  update(e) {
    const t = e.vertexShader, n = e.fragmentShader, i = this._getShaderStage(t), r = this._getShaderStage(n), a = this._getShaderCacheForMaterial(e);
    return false === a.has(i) && (a.add(i), i.usedTimes++), false === a.has(r) && (a.add(r), r.usedTimes++), this;
  }
  remove(e) {
    const t = this.materialCache.get(e);
    for (const e2 of t) e2.usedTimes--, 0 === e2.usedTimes && this.shaderCache.delete(e2.code);
    return this.materialCache.delete(e), this;
  }
  getVertexShaderID(e) {
    return this._getShaderStage(e.vertexShader).id;
  }
  getFragmentShaderID(e) {
    return this._getShaderStage(e.fragmentShader).id;
  }
  dispose() {
    this.shaderCache.clear(), this.materialCache.clear();
  }
  _getShaderCacheForMaterial(e) {
    const t = this.materialCache;
    let n = t.get(e);
    return void 0 === n && (n = /* @__PURE__ */ new Set(), t.set(e, n)), n;
  }
  _getShaderStage(e) {
    const t = this.shaderCache;
    let n = t.get(e);
    return void 0 === n && (n = new Gr(e), t.set(e, n)), n;
  }
}
class Gr {
  constructor(e) {
    this.id = Br++, this.code = e, this.usedTimes = 0;
  }
}
function Vr(e, t, n, i, r, o, s) {
  const l = new _mpChunkDeps_three_build_three_core_min.fr(), c = new Hr(), p = /* @__PURE__ */ new Set(), m = [], h = r.logarithmicDepthBuffer, _ = r.vertexTextures;
  let g = r.precision;
  const v = { MeshDepthMaterial: "depth", MeshDistanceMaterial: "distanceRGBA", MeshNormalMaterial: "normal", MeshBasicMaterial: "basic", MeshLambertMaterial: "lambert", MeshPhongMaterial: "phong", MeshToonMaterial: "toon", MeshStandardMaterial: "physical", MeshPhysicalMaterial: "physical", MeshMatcapMaterial: "matcap", LineBasicMaterial: "basic", LineDashedMaterial: "dashed", PointsMaterial: "points", ShadowMaterial: "shadow", SpriteMaterial: "sprite" };
  function E(e2) {
    return p.add(e2), 0 === e2 ? "uv" : `uv${e2}`;
  }
  return { getParameters: function(o2, l2, m2, S, T) {
    const M = S.fog, x = T.geometry, R = o2.isMeshStandardMaterial ? S.environment : null, A = (o2.isMeshStandardMaterial ? n : t).get(o2.envMap || R), b = A && A.mapping === _mpChunkDeps_three_build_three_core_min.dt ? A.image.height : null, C = v[o2.type];
    null !== o2.precision && (g = r.getMaxPrecision(o2.precision), g !== o2.precision && console.warn("THREE.WebGLProgram.getParameters:", o2.precision, "not supported, using", g, "instead."));
    const L = x.morphAttributes.position || x.morphAttributes.normal || x.morphAttributes.color, P = void 0 !== L ? L.length : 0;
    let w, D, y, I, N = 0;
    if (void 0 !== x.morphAttributes.position && (N = 1), void 0 !== x.morphAttributes.normal && (N = 2), void 0 !== x.morphAttributes.color && (N = 3), C) {
      const e2 = yn[C];
      w = e2.vertexShader, D = e2.fragmentShader;
    } else w = o2.vertexShader, D = o2.fragmentShader, c.update(o2), y = c.getVertexShaderID(o2), I = c.getFragmentShaderID(o2);
    const O = e.getRenderTarget(), F = e.state.buffers.depth.getReversed(), H = true === T.isInstancedMesh, G = true === T.isBatchedMesh, V = !!o2.map, z = !!o2.matcap, k = !!A, W = !!o2.aoMap, X = !!o2.lightMap, Y = !!o2.bumpMap, j = !!o2.normalMap, K = !!o2.displacementMap, q = !!o2.emissiveMap, Z = !!o2.metalnessMap, $ = !!o2.roughnessMap, Q = o2.anisotropy > 0, J = o2.clearcoat > 0, ee = o2.dispersion > 0, te = o2.iridescence > 0, ne = o2.sheen > 0, ie = o2.transmission > 0, re = Q && !!o2.anisotropyMap, ae = J && !!o2.clearcoatMap, oe = J && !!o2.clearcoatNormalMap, se = J && !!o2.clearcoatRoughnessMap, le = te && !!o2.iridescenceMap, ce = te && !!o2.iridescenceThicknessMap, de = ne && !!o2.sheenColorMap, he = ne && !!o2.sheenRoughnessMap, _e = !!o2.specularMap, ge = !!o2.specularColorMap, ve = !!o2.specularIntensityMap, Ee = ie && !!o2.transmissionMap, Se = ie && !!o2.thicknessMap, Te = !!o2.gradientMap, Me = !!o2.alphaMap, xe = o2.alphaTest > 0, Re = !!o2.alphaHash, Ae = !!o2.extensions;
    let be = _mpChunkDeps_three_build_three_core_min.$;
    o2.toneMapped && (null !== O && true !== O.isXRRenderTarget || (be = e.toneMapping));
    const Ce = { shaderID: C, shaderType: o2.type, shaderName: o2.name, vertexShader: w, fragmentShader: D, defines: o2.defines, customVertexShaderID: y, customFragmentShaderID: I, isRawShaderMaterial: true === o2.isRawShaderMaterial, glslVersion: o2.glslVersion, precision: g, batching: G, batchingColor: G && null !== T._colorsTexture, instancing: H, instancingColor: H && null !== T.instanceColor, instancingMorph: H && null !== T.morphTexture, supportsVertexTextures: _, outputColorSpace: null === O ? e.outputColorSpace : true === O.isXRRenderTarget ? O.texture.colorSpace : _mpChunkDeps_three_build_three_core_min.$e, alphaToCoverage: !!o2.alphaToCoverage, map: V, matcap: z, envMap: k, envMapMode: k && A.mapping, envMapCubeUVHeight: b, aoMap: W, lightMap: X, bumpMap: Y, normalMap: j, displacementMap: _ && K, emissiveMap: q, normalMapObjectSpace: j && o2.normalMapType === _mpChunkDeps_three_build_three_core_min.Ye, normalMapTangentSpace: j && o2.normalMapType === _mpChunkDeps_three_build_three_core_min.Xe, metalnessMap: Z, roughnessMap: $, anisotropy: Q, anisotropyMap: re, clearcoat: J, clearcoatMap: ae, clearcoatNormalMap: oe, clearcoatRoughnessMap: se, dispersion: ee, iridescence: te, iridescenceMap: le, iridescenceThicknessMap: ce, sheen: ne, sheenColorMap: de, sheenRoughnessMap: he, specularMap: _e, specularColorMap: ge, specularIntensityMap: ve, transmission: ie, transmissionMap: Ee, thicknessMap: Se, gradientMap: Te, opaque: false === o2.transparent && o2.blending === _mpChunkDeps_three_build_three_core_min.y && false === o2.alphaToCoverage, alphaMap: Me, alphaTest: xe, alphaHash: Re, combine: o2.combine, mapUv: V && E(o2.map.channel), aoMapUv: W && E(o2.aoMap.channel), lightMapUv: X && E(o2.lightMap.channel), bumpMapUv: Y && E(o2.bumpMap.channel), normalMapUv: j && E(o2.normalMap.channel), displacementMapUv: K && E(o2.displacementMap.channel), emissiveMapUv: q && E(o2.emissiveMap.channel), metalnessMapUv: Z && E(o2.metalnessMap.channel), roughnessMapUv: $ && E(o2.roughnessMap.channel), anisotropyMapUv: re && E(o2.anisotropyMap.channel), clearcoatMapUv: ae && E(o2.clearcoatMap.channel), clearcoatNormalMapUv: oe && E(o2.clearcoatNormalMap.channel), clearcoatRoughnessMapUv: se && E(o2.clearcoatRoughnessMap.channel), iridescenceMapUv: le && E(o2.iridescenceMap.channel), iridescenceThicknessMapUv: ce && E(o2.iridescenceThicknessMap.channel), sheenColorMapUv: de && E(o2.sheenColorMap.channel), sheenRoughnessMapUv: he && E(o2.sheenRoughnessMap.channel), specularMapUv: _e && E(o2.specularMap.channel), specularColorMapUv: ge && E(o2.specularColorMap.channel), specularIntensityMapUv: ve && E(o2.specularIntensityMap.channel), transmissionMapUv: Ee && E(o2.transmissionMap.channel), thicknessMapUv: Se && E(o2.thicknessMap.channel), alphaMapUv: Me && E(o2.alphaMap.channel), vertexTangents: !!x.attributes.tangent && (j || Q), vertexColors: o2.vertexColors, vertexAlphas: true === o2.vertexColors && !!x.attributes.color && 4 === x.attributes.color.itemSize, pointsUvs: true === T.isPoints && !!x.attributes.uv && (V || Me), fog: !!M, useFog: true === o2.fog, fogExp2: !!M && M.isFogExp2, flatShading: true === o2.flatShading, sizeAttenuation: true === o2.sizeAttenuation, logarithmicDepthBuffer: h, reverseDepthBuffer: F, skinning: true === T.isSkinnedMesh, morphTargets: void 0 !== x.morphAttributes.position, morphNormals: void 0 !== x.morphAttributes.normal, morphColors: void 0 !== x.morphAttributes.color, morphTargetsCount: P, morphTextureStride: N, numDirLights: l2.directional.length, numPointLights: l2.point.length, numSpotLights: l2.spot.length, numSpotLightMaps: l2.spotLightMap.length, numRectAreaLights: l2.rectArea.length, numHemiLights: l2.hemi.length, numDirLightShadows: l2.directionalShadowMap.length, numPointLightShadows: l2.pointShadowMap.length, numSpotLightShadows: l2.spotShadowMap.length, numSpotLightShadowsWithMaps: l2.numSpotLightShadowsWithMaps, numLightProbes: l2.numLightProbes, numClippingPlanes: s.numPlanes, numClipIntersection: s.numIntersection, dithering: o2.dithering, shadowMapEnabled: e.shadowMap.enabled && m2.length > 0, shadowMapType: e.shadowMap.type, toneMapping: be, decodeVideoTexture: V && true === o2.map.isVideoTexture && _mpChunkDeps_three_build_three_core_min.ui.getTransfer(o2.map.colorSpace) === _mpChunkDeps_three_build_three_core_min.Ke, decodeVideoTextureEmissive: q && true === o2.emissiveMap.isVideoTexture && _mpChunkDeps_three_build_three_core_min.ui.getTransfer(o2.emissiveMap.colorSpace) === _mpChunkDeps_three_build_three_core_min.Ke, premultipliedAlpha: o2.premultipliedAlpha, doubleSided: o2.side === _mpChunkDeps_three_build_three_core_min.p, flipSided: o2.side === _mpChunkDeps_three_build_three_core_min.d, useDepthPacking: o2.depthPacking >= 0, depthPacking: o2.depthPacking || 0, index0AttributeName: o2.index0AttributeName, extensionClipCullDistance: Ae && true === o2.extensions.clipCullDistance && i.has("WEBGL_clip_cull_distance"), extensionMultiDraw: (Ae && true === o2.extensions.multiDraw || G) && i.has("WEBGL_multi_draw"), rendererExtensionParallelShaderCompile: i.has("KHR_parallel_shader_compile"), customProgramCacheKey: o2.customProgramCacheKey() };
    return Ce.vertexUv1s = p.has(1), Ce.vertexUv2s = p.has(2), Ce.vertexUv3s = p.has(3), p.clear(), Ce;
  }, getProgramCacheKey: function(t2) {
    const n2 = [];
    if (t2.shaderID ? n2.push(t2.shaderID) : (n2.push(t2.customVertexShaderID), n2.push(t2.customFragmentShaderID)), void 0 !== t2.defines) for (const e2 in t2.defines) n2.push(e2), n2.push(t2.defines[e2]);
    return false === t2.isRawShaderMaterial && (!function(e2, t3) {
      e2.push(t3.precision), e2.push(t3.outputColorSpace), e2.push(t3.envMapMode), e2.push(t3.envMapCubeUVHeight), e2.push(t3.mapUv), e2.push(t3.alphaMapUv), e2.push(t3.lightMapUv), e2.push(t3.aoMapUv), e2.push(t3.bumpMapUv), e2.push(t3.normalMapUv), e2.push(t3.displacementMapUv), e2.push(t3.emissiveMapUv), e2.push(t3.metalnessMapUv), e2.push(t3.roughnessMapUv), e2.push(t3.anisotropyMapUv), e2.push(t3.clearcoatMapUv), e2.push(t3.clearcoatNormalMapUv), e2.push(t3.clearcoatRoughnessMapUv), e2.push(t3.iridescenceMapUv), e2.push(t3.iridescenceThicknessMapUv), e2.push(t3.sheenColorMapUv), e2.push(t3.sheenRoughnessMapUv), e2.push(t3.specularMapUv), e2.push(t3.specularColorMapUv), e2.push(t3.specularIntensityMapUv), e2.push(t3.transmissionMapUv), e2.push(t3.thicknessMapUv), e2.push(t3.combine), e2.push(t3.fogExp2), e2.push(t3.sizeAttenuation), e2.push(t3.morphTargetsCount), e2.push(t3.morphAttributeCount), e2.push(t3.numDirLights), e2.push(t3.numPointLights), e2.push(t3.numSpotLights), e2.push(t3.numSpotLightMaps), e2.push(t3.numHemiLights), e2.push(t3.numRectAreaLights), e2.push(t3.numDirLightShadows), e2.push(t3.numPointLightShadows), e2.push(t3.numSpotLightShadows), e2.push(t3.numSpotLightShadowsWithMaps), e2.push(t3.numLightProbes), e2.push(t3.shadowMapType), e2.push(t3.toneMapping), e2.push(t3.numClippingPlanes), e2.push(t3.numClipIntersection), e2.push(t3.depthPacking);
    }(n2, t2), function(e2, t3) {
      l.disableAll(), t3.supportsVertexTextures && l.enable(0);
      t3.instancing && l.enable(1);
      t3.instancingColor && l.enable(2);
      t3.instancingMorph && l.enable(3);
      t3.matcap && l.enable(4);
      t3.envMap && l.enable(5);
      t3.normalMapObjectSpace && l.enable(6);
      t3.normalMapTangentSpace && l.enable(7);
      t3.clearcoat && l.enable(8);
      t3.iridescence && l.enable(9);
      t3.alphaTest && l.enable(10);
      t3.vertexColors && l.enable(11);
      t3.vertexAlphas && l.enable(12);
      t3.vertexUv1s && l.enable(13);
      t3.vertexUv2s && l.enable(14);
      t3.vertexUv3s && l.enable(15);
      t3.vertexTangents && l.enable(16);
      t3.anisotropy && l.enable(17);
      t3.alphaHash && l.enable(18);
      t3.batching && l.enable(19);
      t3.dispersion && l.enable(20);
      t3.batchingColor && l.enable(21);
      e2.push(l.mask), l.disableAll(), t3.fog && l.enable(0);
      t3.useFog && l.enable(1);
      t3.flatShading && l.enable(2);
      t3.logarithmicDepthBuffer && l.enable(3);
      t3.reverseDepthBuffer && l.enable(4);
      t3.skinning && l.enable(5);
      t3.morphTargets && l.enable(6);
      t3.morphNormals && l.enable(7);
      t3.morphColors && l.enable(8);
      t3.premultipliedAlpha && l.enable(9);
      t3.shadowMapEnabled && l.enable(10);
      t3.doubleSided && l.enable(11);
      t3.flipSided && l.enable(12);
      t3.useDepthPacking && l.enable(13);
      t3.dithering && l.enable(14);
      t3.transmission && l.enable(15);
      t3.sheen && l.enable(16);
      t3.opaque && l.enable(17);
      t3.pointsUvs && l.enable(18);
      t3.decodeVideoTexture && l.enable(19);
      t3.decodeVideoTextureEmissive && l.enable(20);
      t3.alphaToCoverage && l.enable(21);
      e2.push(l.mask);
    }(n2, t2), n2.push(e.outputColorSpace)), n2.push(t2.customProgramCacheKey), n2.join();
  }, getUniforms: function(e2) {
    const t2 = v[e2.type];
    let n2;
    if (t2) {
      const e3 = yn[t2];
      n2 = _mpChunkDeps_three_build_three_core_min.qn.clone(e3.uniforms);
    } else n2 = e2.uniforms;
    return n2;
  }, acquireProgram: function(t2, n2) {
    let i2;
    for (let e2 = 0, t3 = m.length; e2 < t3; e2++) {
      const t4 = m[e2];
      if (t4.cacheKey === n2) {
        i2 = t4, ++i2.usedTimes;
        break;
      }
    }
    return void 0 === i2 && (i2 = new Fr(e, n2, t2, o), m.push(i2)), i2;
  }, releaseProgram: function(e2) {
    if (0 == --e2.usedTimes) {
      const t2 = m.indexOf(e2);
      m[t2] = m[m.length - 1], m.pop(), e2.destroy();
    }
  }, releaseShaderCache: function(e2) {
    c.remove(e2);
  }, programs: m, dispose: function() {
    c.dispose();
  } };
}
function zr() {
  let e = /* @__PURE__ */ new WeakMap();
  return { has: function(t) {
    return e.has(t);
  }, get: function(t) {
    let n = e.get(t);
    return void 0 === n && (n = {}, e.set(t, n)), n;
  }, remove: function(t) {
    e.delete(t);
  }, update: function(t, n, i) {
    e.get(t)[n] = i;
  }, dispose: function() {
    e = /* @__PURE__ */ new WeakMap();
  } };
}
function kr(e, t) {
  return e.groupOrder !== t.groupOrder ? e.groupOrder - t.groupOrder : e.renderOrder !== t.renderOrder ? e.renderOrder - t.renderOrder : e.material.id !== t.material.id ? e.material.id - t.material.id : e.z !== t.z ? e.z - t.z : e.id - t.id;
}
function Wr(e, t) {
  return e.groupOrder !== t.groupOrder ? e.groupOrder - t.groupOrder : e.renderOrder !== t.renderOrder ? e.renderOrder - t.renderOrder : e.z !== t.z ? t.z - e.z : e.id - t.id;
}
function Xr() {
  const e = [];
  let t = 0;
  const n = [], i = [], r = [];
  function a(n2, i2, r2, a2, o, s) {
    let l = e[t];
    return void 0 === l ? (l = { id: n2.id, object: n2, geometry: i2, material: r2, groupOrder: a2, renderOrder: n2.renderOrder, z: o, group: s }, e[t] = l) : (l.id = n2.id, l.object = n2, l.geometry = i2, l.material = r2, l.groupOrder = a2, l.renderOrder = n2.renderOrder, l.z = o, l.group = s), t++, l;
  }
  return { opaque: n, transmissive: i, transparent: r, init: function() {
    t = 0, n.length = 0, i.length = 0, r.length = 0;
  }, push: function(e2, t2, o, s, l, c) {
    const d = a(e2, t2, o, s, l, c);
    o.transmission > 0 ? i.push(d) : true === o.transparent ? r.push(d) : n.push(d);
  }, unshift: function(e2, t2, o, s, l, c) {
    const d = a(e2, t2, o, s, l, c);
    o.transmission > 0 ? i.unshift(d) : true === o.transparent ? r.unshift(d) : n.unshift(d);
  }, finish: function() {
    for (let n2 = t, i2 = e.length; n2 < i2; n2++) {
      const t2 = e[n2];
      if (null === t2.id) break;
      t2.id = null, t2.object = null, t2.geometry = null, t2.material = null, t2.group = null;
    }
  }, sort: function(e2, t2) {
    n.length > 1 && n.sort(e2 || kr), i.length > 1 && i.sort(t2 || Wr), r.length > 1 && r.sort(t2 || Wr);
  } };
}
function Yr() {
  let e = /* @__PURE__ */ new WeakMap();
  return { get: function(t, n) {
    const i = e.get(t);
    let r;
    return void 0 === i ? (r = new Xr(), e.set(t, [r])) : n >= i.length ? (r = new Xr(), i.push(r)) : r = i[n], r;
  }, dispose: function() {
    e = /* @__PURE__ */ new WeakMap();
  } };
}
function jr() {
  const t = {};
  return { get: function(n) {
    if (void 0 !== t[n.id]) return t[n.id];
    let i;
    switch (n.type) {
      case "DirectionalLight":
        i = { direction: new _mpChunkDeps_three_build_three_core_min.Ii(), color: new _mpChunkDeps_three_build_three_core_min.$r() };
        break;
      case "SpotLight":
        i = { position: new _mpChunkDeps_three_build_three_core_min.Ii(), direction: new _mpChunkDeps_three_build_three_core_min.Ii(), color: new _mpChunkDeps_three_build_three_core_min.$r(), distance: 0, coneCos: 0, penumbraCos: 0, decay: 0 };
        break;
      case "PointLight":
        i = { position: new _mpChunkDeps_three_build_three_core_min.Ii(), color: new _mpChunkDeps_three_build_three_core_min.$r(), distance: 0, decay: 0 };
        break;
      case "HemisphereLight":
        i = { direction: new _mpChunkDeps_three_build_three_core_min.Ii(), skyColor: new _mpChunkDeps_three_build_three_core_min.$r(), groundColor: new _mpChunkDeps_three_build_three_core_min.$r() };
        break;
      case "RectAreaLight":
        i = { color: new _mpChunkDeps_three_build_three_core_min.$r(), position: new _mpChunkDeps_three_build_three_core_min.Ii(), halfWidth: new _mpChunkDeps_three_build_three_core_min.Ii(), halfHeight: new _mpChunkDeps_three_build_three_core_min.Ii() };
    }
    return t[n.id] = i, i;
  } };
}
let Kr = 0;
function qr(e, t) {
  return (t.castShadow ? 2 : 0) - (e.castShadow ? 2 : 0) + (t.map ? 1 : 0) - (e.map ? 1 : 0);
}
function Zr(e) {
  const t = new jr(), i = /* @__PURE__ */ function() {
    const e2 = {};
    return { get: function(t2) {
      if (void 0 !== e2[t2.id]) return e2[t2.id];
      let i2;
      switch (t2.type) {
        case "DirectionalLight":
        case "SpotLight":
          i2 = { shadowIntensity: 1, shadowBias: 0, shadowNormalBias: 0, shadowRadius: 1, shadowMapSize: new _mpChunkDeps_three_build_three_core_min.Zs() };
          break;
        case "PointLight":
          i2 = { shadowIntensity: 1, shadowBias: 0, shadowNormalBias: 0, shadowRadius: 1, shadowMapSize: new _mpChunkDeps_three_build_three_core_min.Zs(), shadowCameraNear: 1, shadowCameraFar: 1e3 };
      }
      return e2[t2.id] = i2, i2;
    } };
  }(), a = { version: 0, hash: { directionalLength: -1, pointLength: -1, spotLength: -1, rectAreaLength: -1, hemiLength: -1, numDirectionalShadows: -1, numPointShadows: -1, numSpotShadows: -1, numSpotMaps: -1, numLightProbes: -1 }, ambient: [0, 0, 0], probe: [], directional: [], directionalShadow: [], directionalShadowMap: [], directionalShadowMatrix: [], spot: [], spotLightMap: [], spotShadow: [], spotShadowMap: [], spotLightMatrix: [], rectArea: [], rectAreaLTC1: null, rectAreaLTC2: null, point: [], pointShadow: [], pointShadowMap: [], pointShadowMatrix: [], hemi: [], numSpotLightShadowsWithMaps: 0, numLightProbes: 0 };
  for (let e2 = 0; e2 < 9; e2++) a.probe.push(new _mpChunkDeps_three_build_three_core_min.Ii());
  const o = new _mpChunkDeps_three_build_three_core_min.Ii(), s = new _mpChunkDeps_three_build_three_core_min.nr(), l = new _mpChunkDeps_three_build_three_core_min.nr();
  return { setup: function(n) {
    let r = 0, o2 = 0, s2 = 0;
    for (let e2 = 0; e2 < 9; e2++) a.probe[e2].set(0, 0, 0);
    let l2 = 0, c = 0, d = 0, u = 0, f = 0, p = 0, m = 0, h = 0, _ = 0, g = 0, v = 0;
    n.sort(qr);
    for (let e2 = 0, E2 = n.length; e2 < E2; e2++) {
      const E3 = n[e2], S = E3.color, T = E3.intensity, M = E3.distance, x = E3.shadow && E3.shadow.map ? E3.shadow.map.texture : null;
      if (E3.isAmbientLight) r += S.r * T, o2 += S.g * T, s2 += S.b * T;
      else if (E3.isLightProbe) {
        for (let e3 = 0; e3 < 9; e3++) a.probe[e3].addScaledVector(E3.sh.coefficients[e3], T);
        v++;
      } else if (E3.isDirectionalLight) {
        const e3 = t.get(E3);
        if (e3.color.copy(E3.color).multiplyScalar(E3.intensity), E3.castShadow) {
          const e4 = E3.shadow, t2 = i.get(E3);
          t2.shadowIntensity = e4.intensity, t2.shadowBias = e4.bias, t2.shadowNormalBias = e4.normalBias, t2.shadowRadius = e4.radius, t2.shadowMapSize = e4.mapSize, a.directionalShadow[l2] = t2, a.directionalShadowMap[l2] = x, a.directionalShadowMatrix[l2] = E3.shadow.matrix, p++;
        }
        a.directional[l2] = e3, l2++;
      } else if (E3.isSpotLight) {
        const e3 = t.get(E3);
        e3.position.setFromMatrixPosition(E3.matrixWorld), e3.color.copy(S).multiplyScalar(T), e3.distance = M, e3.coneCos = Math.cos(E3.angle), e3.penumbraCos = Math.cos(E3.angle * (1 - E3.penumbra)), e3.decay = E3.decay, a.spot[d] = e3;
        const n2 = E3.shadow;
        if (E3.map && (a.spotLightMap[_] = E3.map, _++, n2.updateMatrices(E3), E3.castShadow && g++), a.spotLightMatrix[d] = n2.matrix, E3.castShadow) {
          const e4 = i.get(E3);
          e4.shadowIntensity = n2.intensity, e4.shadowBias = n2.bias, e4.shadowNormalBias = n2.normalBias, e4.shadowRadius = n2.radius, e4.shadowMapSize = n2.mapSize, a.spotShadow[d] = e4, a.spotShadowMap[d] = x, h++;
        }
        d++;
      } else if (E3.isRectAreaLight) {
        const e3 = t.get(E3);
        e3.color.copy(S).multiplyScalar(T), e3.halfWidth.set(0.5 * E3.width, 0, 0), e3.halfHeight.set(0, 0.5 * E3.height, 0), a.rectArea[u] = e3, u++;
      } else if (E3.isPointLight) {
        const e3 = t.get(E3);
        if (e3.color.copy(E3.color).multiplyScalar(E3.intensity), e3.distance = E3.distance, e3.decay = E3.decay, E3.castShadow) {
          const e4 = E3.shadow, t2 = i.get(E3);
          t2.shadowIntensity = e4.intensity, t2.shadowBias = e4.bias, t2.shadowNormalBias = e4.normalBias, t2.shadowRadius = e4.radius, t2.shadowMapSize = e4.mapSize, t2.shadowCameraNear = e4.camera.near, t2.shadowCameraFar = e4.camera.far, a.pointShadow[c] = t2, a.pointShadowMap[c] = x, a.pointShadowMatrix[c] = E3.shadow.matrix, m++;
        }
        a.point[c] = e3, c++;
      } else if (E3.isHemisphereLight) {
        const e3 = t.get(E3);
        e3.skyColor.copy(E3.color).multiplyScalar(T), e3.groundColor.copy(E3.groundColor).multiplyScalar(T), a.hemi[f] = e3, f++;
      }
    }
    u > 0 && (true === e.has("OES_texture_float_linear") ? (a.rectAreaLTC1 = Dn.LTC_FLOAT_1, a.rectAreaLTC2 = Dn.LTC_FLOAT_2) : (a.rectAreaLTC1 = Dn.LTC_HALF_1, a.rectAreaLTC2 = Dn.LTC_HALF_2)), a.ambient[0] = r, a.ambient[1] = o2, a.ambient[2] = s2;
    const E = a.hash;
    E.directionalLength === l2 && E.pointLength === c && E.spotLength === d && E.rectAreaLength === u && E.hemiLength === f && E.numDirectionalShadows === p && E.numPointShadows === m && E.numSpotShadows === h && E.numSpotMaps === _ && E.numLightProbes === v || (a.directional.length = l2, a.spot.length = d, a.rectArea.length = u, a.point.length = c, a.hemi.length = f, a.directionalShadow.length = p, a.directionalShadowMap.length = p, a.pointShadow.length = m, a.pointShadowMap.length = m, a.spotShadow.length = h, a.spotShadowMap.length = h, a.directionalShadowMatrix.length = p, a.pointShadowMatrix.length = m, a.spotLightMatrix.length = h + _ - g, a.spotLightMap.length = _, a.numSpotLightShadowsWithMaps = g, a.numLightProbes = v, E.directionalLength = l2, E.pointLength = c, E.spotLength = d, E.rectAreaLength = u, E.hemiLength = f, E.numDirectionalShadows = p, E.numPointShadows = m, E.numSpotShadows = h, E.numSpotMaps = _, E.numLightProbes = v, a.version = Kr++);
  }, setupView: function(e2, t2) {
    let n = 0, i2 = 0, r = 0, c = 0, d = 0;
    const u = t2.matrixWorldInverse;
    for (let t3 = 0, f = e2.length; t3 < f; t3++) {
      const f2 = e2[t3];
      if (f2.isDirectionalLight) {
        const e3 = a.directional[n];
        e3.direction.setFromMatrixPosition(f2.matrixWorld), o.setFromMatrixPosition(f2.target.matrixWorld), e3.direction.sub(o), e3.direction.transformDirection(u), n++;
      } else if (f2.isSpotLight) {
        const e3 = a.spot[r];
        e3.position.setFromMatrixPosition(f2.matrixWorld), e3.position.applyMatrix4(u), e3.direction.setFromMatrixPosition(f2.matrixWorld), o.setFromMatrixPosition(f2.target.matrixWorld), e3.direction.sub(o), e3.direction.transformDirection(u), r++;
      } else if (f2.isRectAreaLight) {
        const e3 = a.rectArea[c];
        e3.position.setFromMatrixPosition(f2.matrixWorld), e3.position.applyMatrix4(u), l.identity(), s.copy(f2.matrixWorld), s.premultiply(u), l.extractRotation(s), e3.halfWidth.set(0.5 * f2.width, 0, 0), e3.halfHeight.set(0, 0.5 * f2.height, 0), e3.halfWidth.applyMatrix4(l), e3.halfHeight.applyMatrix4(l), c++;
      } else if (f2.isPointLight) {
        const e3 = a.point[i2];
        e3.position.setFromMatrixPosition(f2.matrixWorld), e3.position.applyMatrix4(u), i2++;
      } else if (f2.isHemisphereLight) {
        const e3 = a.hemi[d];
        e3.direction.setFromMatrixPosition(f2.matrixWorld), e3.direction.transformDirection(u), d++;
      }
    }
  }, state: a };
}
function $r(e) {
  const t = new Zr(e), n = [], i = [];
  const r = { lightsArray: n, shadowsArray: i, camera: null, lights: t, transmissionRenderTarget: {} };
  return { init: function(e2) {
    r.camera = e2, n.length = 0, i.length = 0;
  }, state: r, setupLights: function() {
    t.setup(n);
  }, setupLightsView: function(e2) {
    t.setupView(n, e2);
  }, pushLight: function(e2) {
    n.push(e2);
  }, pushShadow: function(e2) {
    i.push(e2);
  } };
}
function Qr(e) {
  let t = /* @__PURE__ */ new WeakMap();
  return { get: function(n, i = 0) {
    const r = t.get(n);
    let a;
    return void 0 === r ? (a = new $r(e), t.set(n, [a])) : i >= r.length ? (a = new $r(e), r.push(a)) : a = r[i], a;
  }, dispose: function() {
    t = /* @__PURE__ */ new WeakMap();
  } };
}
function Jr(e, t, i) {
  let r = new _mpChunkDeps_three_build_three_core_min.ra();
  const a = new _mpChunkDeps_three_build_three_core_min.Zs(), s = new _mpChunkDeps_three_build_three_core_min.Zs(), c = new _mpChunkDeps_three_build_three_core_min.wi(), u = new _mpChunkDeps_three_build_three_core_min.Bl({ depthPacking: _mpChunkDeps_three_build_three_core_min.He }), f = new _mpChunkDeps_three_build_three_core_min.kl(), p = {}, h = i.maxTextureSize, _ = { [_mpChunkDeps_three_build_three_core_min.u]: _mpChunkDeps_three_build_three_core_min.d, [_mpChunkDeps_three_build_three_core_min.d]: _mpChunkDeps_three_build_three_core_min.u, [_mpChunkDeps_three_build_three_core_min.p]: _mpChunkDeps_three_build_three_core_min.p }, g = new _mpChunkDeps_three_build_three_core_min.Jn({ defines: { VSM_SAMPLES: 8 }, uniforms: { shadow_pass: { value: null }, resolution: { value: new _mpChunkDeps_three_build_three_core_min.Zs() }, radius: { value: 4 } }, vertexShader: "void main() {\n	gl_Position = vec4( position, 1.0 );\n}", fragmentShader: "uniform sampler2D shadow_pass;\nuniform vec2 resolution;\nuniform float radius;\n#include <packing>\nvoid main() {\n	const float samples = float( VSM_SAMPLES );\n	float mean = 0.0;\n	float squared_mean = 0.0;\n	float uvStride = samples <= 1.0 ? 0.0 : 2.0 / ( samples - 1.0 );\n	float uvStart = samples <= 1.0 ? 0.0 : - 1.0;\n	for ( float i = 0.0; i < samples; i ++ ) {\n		float uvOffset = uvStart + i * uvStride;\n		#ifdef HORIZONTAL_PASS\n			vec2 distribution = unpackRGBATo2Half( texture2D( shadow_pass, ( gl_FragCoord.xy + vec2( uvOffset, 0.0 ) * radius ) / resolution ) );\n			mean += distribution.x;\n			squared_mean += distribution.y * distribution.y + distribution.x * distribution.x;\n		#else\n			float depth = unpackRGBAToDepth( texture2D( shadow_pass, ( gl_FragCoord.xy + vec2( 0.0, uvOffset ) * radius ) / resolution ) );\n			mean += depth;\n			squared_mean += depth * depth;\n		#endif\n	}\n	mean = mean / samples;\n	squared_mean = squared_mean / samples;\n	float std_dev = sqrt( squared_mean - mean * mean );\n	gl_FragColor = pack2HalfToRGBA( vec2( mean, std_dev ) );\n}" }), v = g.clone();
  v.defines.HORIZONTAL_PASS = 1;
  const E = new _mpChunkDeps_three_build_three_core_min.zn();
  E.setAttribute("position", new _mpChunkDeps_three_build_three_core_min.cn(new Float32Array([-1, -1, 0.5, 3, -1, 0.5, -1, 3, 0.5]), 3));
  const S = new _mpChunkDeps_three_build_three_core_min.Vn(E, g), T = this;
  this.enabled = false, this.autoUpdate = true, this.needsUpdate = false, this.type = _mpChunkDeps_three_build_three_core_min.h;
  let M = this.type;
  function x(n, i2) {
    const r2 = t.update(S);
    g.defines.VSM_SAMPLES !== n.blurSamples && (g.defines.VSM_SAMPLES = n.blurSamples, v.defines.VSM_SAMPLES = n.blurSamples, g.needsUpdate = true, v.needsUpdate = true), null === n.mapPass && (n.mapPass = new _mpChunkDeps_three_build_three_core_min.Si(a.x, a.y)), g.uniforms.shadow_pass.value = n.map.texture, g.uniforms.resolution.value = n.mapSize, g.uniforms.radius.value = n.radius, e.setRenderTarget(n.mapPass), e.clear(), e.renderBufferDirect(i2, null, r2, g, S, null), v.uniforms.shadow_pass.value = n.mapPass.texture, v.uniforms.resolution.value = n.mapSize, v.uniforms.radius.value = n.radius, e.setRenderTarget(n.map), e.clear(), e.renderBufferDirect(i2, null, r2, v, S, null);
  }
  function R(t2, n, i2, r2) {
    let a2 = null;
    const o = true === i2.isPointLight ? t2.customDistanceMaterial : t2.customDepthMaterial;
    if (void 0 !== o) a2 = o;
    else if (a2 = true === i2.isPointLight ? f : u, e.localClippingEnabled && true === n.clipShadows && Array.isArray(n.clippingPlanes) && 0 !== n.clippingPlanes.length || n.displacementMap && 0 !== n.displacementScale || n.alphaMap && n.alphaTest > 0 || n.map && n.alphaTest > 0) {
      const e2 = a2.uuid, t3 = n.uuid;
      let i3 = p[e2];
      void 0 === i3 && (i3 = {}, p[e2] = i3);
      let r3 = i3[t3];
      void 0 === r3 && (r3 = a2.clone(), i3[t3] = r3, n.addEventListener("dispose", b)), a2 = r3;
    }
    if (a2.visible = n.visible, a2.wireframe = n.wireframe, a2.side = r2 === _mpChunkDeps_three_build_three_core_min.c ? null !== n.shadowSide ? n.shadowSide : n.side : null !== n.shadowSide ? n.shadowSide : _[n.side], a2.alphaMap = n.alphaMap, a2.alphaTest = n.alphaTest, a2.map = n.map, a2.clipShadows = n.clipShadows, a2.clippingPlanes = n.clippingPlanes, a2.clipIntersection = n.clipIntersection, a2.displacementMap = n.displacementMap, a2.displacementScale = n.displacementScale, a2.displacementBias = n.displacementBias, a2.wireframeLinewidth = n.wireframeLinewidth, a2.linewidth = n.linewidth, true === i2.isPointLight && true === a2.isMeshDistanceMaterial) {
      e.properties.get(a2).light = i2;
    }
    return a2;
  }
  function A(n, i2, a2, o, s2) {
    if (false === n.visible) return;
    if (n.layers.test(i2.layers) && (n.isMesh || n.isLine || n.isPoints) && (n.castShadow || n.receiveShadow && s2 === _mpChunkDeps_three_build_three_core_min.c) && (!n.frustumCulled || r.intersectsObject(n))) {
      n.modelViewMatrix.multiplyMatrices(a2.matrixWorldInverse, n.matrixWorld);
      const r2 = t.update(n), l2 = n.material;
      if (Array.isArray(l2)) {
        const t2 = r2.groups;
        for (let c2 = 0, d = t2.length; c2 < d; c2++) {
          const d2 = t2[c2], u2 = l2[d2.materialIndex];
          if (u2 && u2.visible) {
            const t3 = R(n, u2, o, s2);
            n.onBeforeShadow(e, n, i2, a2, r2, t3, d2), e.renderBufferDirect(a2, null, r2, t3, n, d2), n.onAfterShadow(e, n, i2, a2, r2, t3, d2);
          }
        }
      } else if (l2.visible) {
        const t2 = R(n, l2, o, s2);
        n.onBeforeShadow(e, n, i2, a2, r2, t2, null), e.renderBufferDirect(a2, null, r2, t2, n, null), n.onAfterShadow(e, n, i2, a2, r2, t2, null);
      }
    }
    const l = n.children;
    for (let e2 = 0, t2 = l.length; e2 < t2; e2++) A(l[e2], i2, a2, o, s2);
  }
  function b(e2) {
    e2.target.removeEventListener("dispose", b);
    for (const t2 in p) {
      const n = p[t2], i2 = e2.target.uuid;
      if (i2 in n) {
        n[i2].dispose(), delete n[i2];
      }
    }
  }
  this.render = function(t2, n, i2) {
    if (false === T.enabled) return;
    if (false === T.autoUpdate && false === T.needsUpdate) return;
    if (0 === t2.length) return;
    const o = e.getRenderTarget(), l = e.getActiveCubeFace(), d = e.getActiveMipmapLevel(), u2 = e.state;
    u2.setBlending(_mpChunkDeps_three_build_three_core_min.m), u2.buffers.color.setClear(1, 1, 1, 1), u2.buffers.depth.setTest(true), u2.setScissorTest(false);
    const f2 = M !== _mpChunkDeps_three_build_three_core_min.c && this.type === _mpChunkDeps_three_build_three_core_min.c, p2 = M === _mpChunkDeps_three_build_three_core_min.c && this.type !== _mpChunkDeps_three_build_three_core_min.c;
    for (let o2 = 0, l2 = t2.length; o2 < l2; o2++) {
      const l3 = t2[o2], d2 = l3.shadow;
      if (void 0 === d2) {
        console.warn("THREE.WebGLShadowMap:", l3, "has no shadow.");
        continue;
      }
      if (false === d2.autoUpdate && false === d2.needsUpdate) continue;
      a.copy(d2.mapSize);
      const m = d2.getFrameExtents();
      if (a.multiply(m), s.copy(d2.mapSize), (a.x > h || a.y > h) && (a.x > h && (s.x = Math.floor(h / m.x), a.x = s.x * m.x, d2.mapSize.x = s.x), a.y > h && (s.y = Math.floor(h / m.y), a.y = s.y * m.y, d2.mapSize.y = s.y)), null === d2.map || true === f2 || true === p2) {
        const e2 = this.type !== _mpChunkDeps_three_build_three_core_min.c ? { minFilter: _mpChunkDeps_three_build_three_core_min.ft, magFilter: _mpChunkDeps_three_build_three_core_min.ft } : {};
        null !== d2.map && d2.map.dispose(), d2.map = new _mpChunkDeps_three_build_three_core_min.Si(a.x, a.y, e2), d2.map.texture.name = l3.name + ".shadowMap", d2.camera.updateProjectionMatrix();
      }
      e.setRenderTarget(d2.map), e.clear();
      const _2 = d2.getViewportCount();
      for (let e2 = 0; e2 < _2; e2++) {
        const t3 = d2.getViewport(e2);
        c.set(s.x * t3.x, s.y * t3.y, s.x * t3.z, s.y * t3.w), u2.viewport(c), d2.updateMatrices(l3, e2), r = d2.getFrustum(), A(n, i2, d2.camera, l3, this.type);
      }
      true !== d2.isPointLightShadow && this.type === _mpChunkDeps_three_build_three_core_min.c && x(d2, i2), d2.needsUpdate = false;
    }
    M = this.type, T.needsUpdate = false, e.setRenderTarget(o, l, d);
  };
}
const ea = { [_mpChunkDeps_three_build_three_core_min.W]: _mpChunkDeps_three_build_three_core_min.j, [_mpChunkDeps_three_build_three_core_min.U]: _mpChunkDeps_three_build_three_core_min.J, [_mpChunkDeps_three_build_three_core_min.H]: _mpChunkDeps_three_build_three_core_min.X, [_mpChunkDeps_three_build_three_core_min.D]: _mpChunkDeps_three_build_three_core_min.q, [_mpChunkDeps_three_build_three_core_min.j]: _mpChunkDeps_three_build_three_core_min.W, [_mpChunkDeps_three_build_three_core_min.J]: _mpChunkDeps_three_build_three_core_min.U, [_mpChunkDeps_three_build_three_core_min.X]: _mpChunkDeps_three_build_three_core_min.H, [_mpChunkDeps_three_build_three_core_min.q]: _mpChunkDeps_three_build_three_core_min.D };
function ta(t, n) {
  const i = new function() {
    let e = false;
    const n2 = new _mpChunkDeps_three_build_three_core_min.wi();
    let i2 = null;
    const r2 = new _mpChunkDeps_three_build_three_core_min.wi(0, 0, 0, 0);
    return { setMask: function(n3) {
      i2 === n3 || e || (t.colorMask(n3, n3, n3, n3), i2 = n3);
    }, setLocked: function(t2) {
      e = t2;
    }, setClear: function(e2, i3, a2, o2, s2) {
      true === s2 && (e2 *= o2, i3 *= o2, a2 *= o2), n2.set(e2, i3, a2, o2), false === r2.equals(n2) && (t.clearColor(e2, i3, a2, o2), r2.copy(n2));
    }, reset: function() {
      e = false, i2 = null, r2.set(-1, 0, 0, 0);
    } };
  }(), r = new function() {
    let e = false, i2 = false, r2 = null, a2 = null, o2 = null;
    return { setReversed: function(e2) {
      if (i2 !== e2) {
        const e3 = n.get("EXT_clip_control");
        i2 ? e3.clipControlEXT(e3.LOWER_LEFT_EXT, e3.ZERO_TO_ONE_EXT) : e3.clipControlEXT(e3.LOWER_LEFT_EXT, e3.NEGATIVE_ONE_TO_ONE_EXT);
        const t2 = o2;
        o2 = null, this.setClear(t2);
      }
      i2 = e2;
    }, getReversed: function() {
      return i2;
    }, setTest: function(e2) {
      e2 ? W(t.DEPTH_TEST) : X(t.DEPTH_TEST);
    }, setMask: function(n2) {
      r2 === n2 || e || (t.depthMask(n2), r2 = n2);
    }, setFunc: function(e2) {
      if (i2 && (e2 = ea[e2]), a2 !== e2) {
        switch (e2) {
          case _mpChunkDeps_three_build_three_core_min.W:
            t.depthFunc(t.NEVER);
            break;
          case _mpChunkDeps_three_build_three_core_min.j:
            t.depthFunc(t.ALWAYS);
            break;
          case _mpChunkDeps_three_build_three_core_min.U:
            t.depthFunc(t.LESS);
            break;
          case _mpChunkDeps_three_build_three_core_min.D:
            t.depthFunc(t.LEQUAL);
            break;
          case _mpChunkDeps_three_build_three_core_min.H:
            t.depthFunc(t.EQUAL);
            break;
          case _mpChunkDeps_three_build_three_core_min.q:
            t.depthFunc(t.GEQUAL);
            break;
          case _mpChunkDeps_three_build_three_core_min.J:
            t.depthFunc(t.GREATER);
            break;
          case _mpChunkDeps_three_build_three_core_min.X:
            t.depthFunc(t.NOTEQUAL);
            break;
          default:
            t.depthFunc(t.LEQUAL);
        }
        a2 = e2;
      }
    }, setLocked: function(t2) {
      e = t2;
    }, setClear: function(e2) {
      o2 !== e2 && (i2 && (e2 = 1 - e2), t.clearDepth(e2), o2 = e2);
    }, reset: function() {
      e = false, r2 = null, a2 = null, o2 = null, i2 = false;
    } };
  }(), a = new function() {
    let e = false, n2 = null, i2 = null, r2 = null, a2 = null, o2 = null, s2 = null, l2 = null, c2 = null;
    return { setTest: function(n3) {
      e || (n3 ? W(t.STENCIL_TEST) : X(t.STENCIL_TEST));
    }, setMask: function(i3) {
      n2 === i3 || e || (t.stencilMask(i3), n2 = i3);
    }, setFunc: function(e2, n3, o3) {
      i2 === e2 && r2 === n3 && a2 === o3 || (t.stencilFunc(e2, n3, o3), i2 = e2, r2 = n3, a2 = o3);
    }, setOp: function(e2, n3, i3) {
      o2 === e2 && s2 === n3 && l2 === i3 || (t.stencilOp(e2, n3, i3), o2 = e2, s2 = n3, l2 = i3);
    }, setLocked: function(t2) {
      e = t2;
    }, setClear: function(e2) {
      c2 !== e2 && (t.clearStencil(e2), c2 = e2);
    }, reset: function() {
      e = false, n2 = null, i2 = null, r2 = null, a2 = null, o2 = null, s2 = null, l2 = null, c2 = null;
    } };
  }(), o = /* @__PURE__ */ new WeakMap(), s = /* @__PURE__ */ new WeakMap();
  let l = {}, c = {}, u = /* @__PURE__ */ new WeakMap(), f = [], p = null, m = false, h = null, _ = null, g = null, v = null, E = null, S = null, T = null, M = new _mpChunkDeps_three_build_three_core_min.$r(0, 0, 0), x = 0, R = false, A = null, b = null, C = null, L = null, P = null;
  const U = t.getParameter(t.MAX_COMBINED_TEXTURE_IMAGE_UNITS);
  let w = false, D = 0;
  const y = t.getParameter(t.VERSION);
  -1 !== y.indexOf("WebGL") ? (D = parseFloat(/^WebGL (\d)/.exec(y)[1]), w = D >= 1) : -1 !== y.indexOf("OpenGL ES") && (D = parseFloat(/^OpenGL ES (\d)/.exec(y)[1]), w = D >= 2);
  let I = null, O = {};
  const F = t.getParameter(t.SCISSOR_BOX), B = t.getParameter(t.VIEWPORT), H = new _mpChunkDeps_three_build_three_core_min.wi().fromArray(F), G = new _mpChunkDeps_three_build_three_core_min.wi().fromArray(B);
  function V(e, n2, i2, r2) {
    const a2 = new Uint8Array(4), o2 = t.createTexture();
    t.bindTexture(e, o2), t.texParameteri(e, t.TEXTURE_MIN_FILTER, t.NEAREST), t.texParameteri(e, t.TEXTURE_MAG_FILTER, t.NEAREST);
    for (let o3 = 0; o3 < i2; o3++) e === t.TEXTURE_3D || e === t.TEXTURE_2D_ARRAY ? t.texImage3D(n2, 0, t.RGBA, 1, 1, r2, 0, t.RGBA, t.UNSIGNED_BYTE, a2) : t.texImage2D(n2 + o3, 0, t.RGBA, 1, 1, 0, t.RGBA, t.UNSIGNED_BYTE, a2);
    return o2;
  }
  const z = {};
  function W(e) {
    true !== l[e] && (t.enable(e), l[e] = true);
  }
  function X(e) {
    false !== l[e] && (t.disable(e), l[e] = false);
  }
  z[t.TEXTURE_2D] = V(t.TEXTURE_2D, t.TEXTURE_2D, 1), z[t.TEXTURE_CUBE_MAP] = V(t.TEXTURE_CUBE_MAP, t.TEXTURE_CUBE_MAP_POSITIVE_X, 6), z[t.TEXTURE_2D_ARRAY] = V(t.TEXTURE_2D_ARRAY, t.TEXTURE_2D_ARRAY, 1, 1), z[t.TEXTURE_3D] = V(t.TEXTURE_3D, t.TEXTURE_3D, 1, 1), i.setClear(0, 0, 0, 1), r.setClear(1), a.setClear(0), W(t.DEPTH_TEST), r.setFunc(_mpChunkDeps_three_build_three_core_min.D), q(false), Z(_mpChunkDeps_three_build_three_core_min.r), W(t.CULL_FACE), K(_mpChunkDeps_three_build_three_core_min.m);
  const Y = { [_mpChunkDeps_three_build_three_core_min.v]: t.FUNC_ADD, [_mpChunkDeps_three_build_three_core_min.w]: t.FUNC_SUBTRACT, [_mpChunkDeps_three_build_three_core_min.M]: t.FUNC_REVERSE_SUBTRACT };
  Y[_mpChunkDeps_three_build_three_core_min.S] = t.MIN, Y[_mpChunkDeps_three_build_three_core_min._] = t.MAX;
  const j = { [_mpChunkDeps_three_build_three_core_min.A]: t.ZERO, [_mpChunkDeps_three_build_three_core_min.T]: t.ONE, [_mpChunkDeps_three_build_three_core_min.z]: t.SRC_COLOR, [_mpChunkDeps_three_build_three_core_min.I]: t.SRC_ALPHA, [_mpChunkDeps_three_build_three_core_min.O]: t.SRC_ALPHA_SATURATE, [_mpChunkDeps_three_build_three_core_min.E]: t.DST_COLOR, [_mpChunkDeps_three_build_three_core_min.k]: t.DST_ALPHA, [_mpChunkDeps_three_build_three_core_min.C]: t.ONE_MINUS_SRC_COLOR, [_mpChunkDeps_three_build_three_core_min.B]: t.ONE_MINUS_SRC_ALPHA, [_mpChunkDeps_three_build_three_core_min.P]: t.ONE_MINUS_DST_COLOR, [_mpChunkDeps_three_build_three_core_min.R]: t.ONE_MINUS_DST_ALPHA, [_mpChunkDeps_three_build_three_core_min.F]: t.CONSTANT_COLOR, [_mpChunkDeps_three_build_three_core_min.N]: t.ONE_MINUS_CONSTANT_COLOR, [_mpChunkDeps_three_build_three_core_min.L]: t.CONSTANT_ALPHA, [_mpChunkDeps_three_build_three_core_min.V]: t.ONE_MINUS_CONSTANT_ALPHA };
  function K(e, n2, i2, r2, a2, o2, s2, l2, c2, d) {
    if (e !== _mpChunkDeps_three_build_three_core_min.m) {
      if (false === m && (W(t.BLEND), m = true), e === _mpChunkDeps_three_build_three_core_min.b) a2 = a2 || n2, o2 = o2 || i2, s2 = s2 || r2, n2 === _ && a2 === E || (t.blendEquationSeparate(Y[n2], Y[a2]), _ = n2, E = a2), i2 === g && r2 === v && o2 === S && s2 === T || (t.blendFuncSeparate(j[i2], j[r2], j[o2], j[s2]), g = i2, v = r2, S = o2, T = s2), false !== l2.equals(M) && c2 === x || (t.blendColor(l2.r, l2.g, l2.b, c2), M.copy(l2), x = c2), h = e, R = false;
      else if (e !== h || d !== R) {
        if (_ === _mpChunkDeps_three_build_three_core_min.v && E === _mpChunkDeps_three_build_three_core_min.v || (t.blendEquation(t.FUNC_ADD), _ = _mpChunkDeps_three_build_three_core_min.v, E = _mpChunkDeps_three_build_three_core_min.v), d) switch (e) {
          case _mpChunkDeps_three_build_three_core_min.y:
            t.blendFuncSeparate(t.ONE, t.ONE_MINUS_SRC_ALPHA, t.ONE, t.ONE_MINUS_SRC_ALPHA);
            break;
          case _mpChunkDeps_three_build_three_core_min.f:
            t.blendFunc(t.ONE, t.ONE);
            break;
          case _mpChunkDeps_three_build_three_core_min.g:
            t.blendFuncSeparate(t.ZERO, t.ONE_MINUS_SRC_COLOR, t.ZERO, t.ONE);
            break;
          case _mpChunkDeps_three_build_three_core_min.x:
            t.blendFuncSeparate(t.ZERO, t.SRC_COLOR, t.ZERO, t.SRC_ALPHA);
            break;
          default:
            console.error("THREE.WebGLState: Invalid blending: ", e);
        }
        else switch (e) {
          case _mpChunkDeps_three_build_three_core_min.y:
            t.blendFuncSeparate(t.SRC_ALPHA, t.ONE_MINUS_SRC_ALPHA, t.ONE, t.ONE_MINUS_SRC_ALPHA);
            break;
          case _mpChunkDeps_three_build_three_core_min.f:
            t.blendFunc(t.SRC_ALPHA, t.ONE);
            break;
          case _mpChunkDeps_three_build_three_core_min.g:
            t.blendFuncSeparate(t.ZERO, t.ONE_MINUS_SRC_COLOR, t.ZERO, t.ONE);
            break;
          case _mpChunkDeps_three_build_three_core_min.x:
            t.blendFunc(t.ZERO, t.SRC_COLOR);
            break;
          default:
            console.error("THREE.WebGLState: Invalid blending: ", e);
        }
        g = null, v = null, S = null, T = null, M.set(0, 0, 0), x = 0, h = e, R = d;
      }
    } else true === m && (X(t.BLEND), m = false);
  }
  function q(e) {
    A !== e && (e ? t.frontFace(t.CW) : t.frontFace(t.CCW), A = e);
  }
  function Z(e) {
    e !== _mpChunkDeps_three_build_three_core_min.i ? (W(t.CULL_FACE), e !== b && (e === _mpChunkDeps_three_build_three_core_min.r ? t.cullFace(t.BACK) : e === _mpChunkDeps_three_build_three_core_min.n ? t.cullFace(t.FRONT) : t.cullFace(t.FRONT_AND_BACK))) : X(t.CULL_FACE), b = e;
  }
  function $(e, n2, i2) {
    e ? (W(t.POLYGON_OFFSET_FILL), L === n2 && P === i2 || (t.polygonOffset(n2, i2), L = n2, P = i2)) : X(t.POLYGON_OFFSET_FILL);
  }
  return { buffers: { color: i, depth: r, stencil: a }, enable: W, disable: X, bindFramebuffer: function(e, n2) {
    return c[e] !== n2 && (t.bindFramebuffer(e, n2), c[e] = n2, e === t.DRAW_FRAMEBUFFER && (c[t.FRAMEBUFFER] = n2), e === t.FRAMEBUFFER && (c[t.DRAW_FRAMEBUFFER] = n2), true);
  }, drawBuffers: function(e, n2) {
    let i2 = f, r2 = false;
    if (e) {
      i2 = u.get(n2), void 0 === i2 && (i2 = [], u.set(n2, i2));
      const a2 = e.textures;
      if (i2.length !== a2.length || i2[0] !== t.COLOR_ATTACHMENT0) {
        for (let e2 = 0, n3 = a2.length; e2 < n3; e2++) i2[e2] = t.COLOR_ATTACHMENT0 + e2;
        i2.length = a2.length, r2 = true;
      }
    } else i2[0] !== t.BACK && (i2[0] = t.BACK, r2 = true);
    r2 && t.drawBuffers(i2);
  }, useProgram: function(e) {
    return p !== e && (t.useProgram(e), p = e, true);
  }, setBlending: K, setMaterial: function(e, n2) {
    e.side === _mpChunkDeps_three_build_three_core_min.p ? X(t.CULL_FACE) : W(t.CULL_FACE);
    let o2 = e.side === _mpChunkDeps_three_build_three_core_min.d;
    n2 && (o2 = !o2), q(o2), e.blending === _mpChunkDeps_three_build_three_core_min.y && false === e.transparent ? K(_mpChunkDeps_three_build_three_core_min.m) : K(e.blending, e.blendEquation, e.blendSrc, e.blendDst, e.blendEquationAlpha, e.blendSrcAlpha, e.blendDstAlpha, e.blendColor, e.blendAlpha, e.premultipliedAlpha), r.setFunc(e.depthFunc), r.setTest(e.depthTest), r.setMask(e.depthWrite), i.setMask(e.colorWrite);
    const s2 = e.stencilWrite;
    a.setTest(s2), s2 && (a.setMask(e.stencilWriteMask), a.setFunc(e.stencilFunc, e.stencilRef, e.stencilFuncMask), a.setOp(e.stencilFail, e.stencilZFail, e.stencilZPass)), $(e.polygonOffset, e.polygonOffsetFactor, e.polygonOffsetUnits), true === e.alphaToCoverage ? W(t.SAMPLE_ALPHA_TO_COVERAGE) : X(t.SAMPLE_ALPHA_TO_COVERAGE);
  }, setFlipSided: q, setCullFace: Z, setLineWidth: function(e) {
    e !== C && (w && t.lineWidth(e), C = e);
  }, setPolygonOffset: $, setScissorTest: function(e) {
    e ? W(t.SCISSOR_TEST) : X(t.SCISSOR_TEST);
  }, activeTexture: function(e) {
    void 0 === e && (e = t.TEXTURE0 + U - 1), I !== e && (t.activeTexture(e), I = e);
  }, bindTexture: function(e, n2, i2) {
    void 0 === i2 && (i2 = null === I ? t.TEXTURE0 + U - 1 : I);
    let r2 = O[i2];
    void 0 === r2 && (r2 = { type: void 0, texture: void 0 }, O[i2] = r2), r2.type === e && r2.texture === n2 || (I !== i2 && (t.activeTexture(i2), I = i2), t.bindTexture(e, n2 || z[e]), r2.type = e, r2.texture = n2);
  }, unbindTexture: function() {
    const e = O[I];
    void 0 !== e && void 0 !== e.type && (t.bindTexture(e.type, null), e.type = void 0, e.texture = void 0);
  }, compressedTexImage2D: function() {
    try {
      t.compressedTexImage2D.apply(t, arguments);
    } catch (e) {
      console.error("THREE.WebGLState:", e);
    }
  }, compressedTexImage3D: function() {
    try {
      t.compressedTexImage3D.apply(t, arguments);
    } catch (e) {
      console.error("THREE.WebGLState:", e);
    }
  }, texImage2D: function() {
    try {
      t.texImage2D.apply(t, arguments);
    } catch (e) {
      console.error("THREE.WebGLState:", e);
    }
  }, texImage3D: function() {
    try {
      t.texImage3D.apply(t, arguments);
    } catch (e) {
      console.error("THREE.WebGLState:", e);
    }
  }, updateUBOMapping: function(e, n2) {
    let i2 = s.get(n2);
    void 0 === i2 && (i2 = /* @__PURE__ */ new WeakMap(), s.set(n2, i2));
    let r2 = i2.get(e);
    void 0 === r2 && (r2 = t.getUniformBlockIndex(n2, e.name), i2.set(e, r2));
  }, uniformBlockBinding: function(e, n2) {
    const i2 = s.get(n2).get(e);
    o.get(n2) !== i2 && (t.uniformBlockBinding(n2, i2, e.__bindingPointIndex), o.set(n2, i2));
  }, texStorage2D: function() {
    try {
      t.texStorage2D.apply(t, arguments);
    } catch (e) {
      console.error("THREE.WebGLState:", e);
    }
  }, texStorage3D: function() {
    try {
      t.texStorage3D.apply(t, arguments);
    } catch (e) {
      console.error("THREE.WebGLState:", e);
    }
  }, texSubImage2D: function() {
    try {
      t.texSubImage2D.apply(t, arguments);
    } catch (e) {
      console.error("THREE.WebGLState:", e);
    }
  }, texSubImage3D: function() {
    try {
      t.texSubImage3D.apply(t, arguments);
    } catch (e) {
      console.error("THREE.WebGLState:", e);
    }
  }, compressedTexSubImage2D: function() {
    try {
      t.compressedTexSubImage2D.apply(t, arguments);
    } catch (e) {
      console.error("THREE.WebGLState:", e);
    }
  }, compressedTexSubImage3D: function() {
    try {
      t.compressedTexSubImage3D.apply(t, arguments);
    } catch (e) {
      console.error("THREE.WebGLState:", e);
    }
  }, scissor: function(e) {
    false === H.equals(e) && (t.scissor(e.x, e.y, e.z, e.w), H.copy(e));
  }, viewport: function(e) {
    false === G.equals(e) && (t.viewport(e.x, e.y, e.z, e.w), G.copy(e));
  }, reset: function() {
    t.disable(t.BLEND), t.disable(t.CULL_FACE), t.disable(t.DEPTH_TEST), t.disable(t.POLYGON_OFFSET_FILL), t.disable(t.SCISSOR_TEST), t.disable(t.STENCIL_TEST), t.disable(t.SAMPLE_ALPHA_TO_COVERAGE), t.blendEquation(t.FUNC_ADD), t.blendFunc(t.ONE, t.ZERO), t.blendFuncSeparate(t.ONE, t.ZERO, t.ONE, t.ZERO), t.blendColor(0, 0, 0, 0), t.colorMask(true, true, true, true), t.clearColor(0, 0, 0, 0), t.depthMask(true), t.depthFunc(t.LESS), r.setReversed(false), t.clearDepth(1), t.stencilMask(4294967295), t.stencilFunc(t.ALWAYS, 0, 4294967295), t.stencilOp(t.KEEP, t.KEEP, t.KEEP), t.clearStencil(0), t.cullFace(t.BACK), t.frontFace(t.CCW), t.polygonOffset(0, 0), t.activeTexture(t.TEXTURE0), t.bindFramebuffer(t.FRAMEBUFFER, null), t.bindFramebuffer(t.DRAW_FRAMEBUFFER, null), t.bindFramebuffer(t.READ_FRAMEBUFFER, null), t.useProgram(null), t.lineWidth(1), t.scissor(0, 0, t.canvas.width, t.canvas.height), t.viewport(0, 0, t.canvas.width, t.canvas.height), l = {}, I = null, O = {}, c = {}, u = /* @__PURE__ */ new WeakMap(), f = [], p = null, m = false, h = null, _ = null, g = null, v = null, E = null, S = null, T = null, M = new _mpChunkDeps_three_build_three_core_min.$r(0, 0, 0), x = 0, R = false, A = null, b = null, C = null, L = null, P = null, H.set(0, 0, t.canvas.width, t.canvas.height), G.set(0, 0, t.canvas.width, t.canvas.height), i.reset(), r.reset(), a.reset();
  } };
}
function na(e, t, i, r, a, o, s) {
  const l = t.has("WEBGL_multisampled_render_to_texture") ? t.get("WEBGL_multisampled_render_to_texture") : null, c = "undefined" != typeof THREEGlobals["navigator"] && /OculusBrowser/g.test(THREEGlobals["navigator"].userAgent), d = new _mpChunkDeps_three_build_three_core_min.Zs(), p = /* @__PURE__ */ new WeakMap();
  let m;
  const h = /* @__PURE__ */ new WeakMap();
  let _ = false;
  try {
    _ = "undefined" != typeof THREEGlobals["OffscreenCanvas"] && null !== new THREEGlobals["OffscreenCanvas"](1, 1).getContext("2d");
  } catch (e2) {
  }
  function g(e2, t2) {
    return _ ? new THREEGlobals["OffscreenCanvas"](e2, t2) : _mpChunkDeps_three_build_three_core_min.ei("canvas");
  }
  function v(e2, t2, n) {
    let i2 = 1;
    const r2 = Z(e2);
    if ((r2.width > n || r2.height > n) && (i2 = n / Math.max(r2.width, r2.height)), i2 < 1) {
      if ("undefined" != typeof THREEGlobals["HTMLImageElement"] && e2 instanceof THREEGlobals["HTMLImageElement"] || "undefined" != typeof THREEGlobals["HTMLCanvasElement"] && e2 instanceof THREEGlobals["HTMLCanvasElement"] || "undefined" != typeof THREEGlobals["ImageBitmap"] && e2 instanceof THREEGlobals["ImageBitmap"] || "undefined" != typeof THREEGlobals["VideoFrame"] && e2 instanceof THREEGlobals["VideoFrame"]) {
        const n2 = Math.floor(i2 * r2.width), a2 = Math.floor(i2 * r2.height);
        void 0 === m && (m = g(n2, a2));
        const o2 = t2 ? g(n2, a2) : m;
        o2.width = n2, o2.height = a2;
        return o2.getContext("2d").drawImage(e2, 0, 0, n2, a2), console.warn("THREE.WebGLRenderer: Texture has been resized from (" + r2.width + "x" + r2.height + ") to (" + n2 + "x" + a2 + ")."), o2;
      }
      return "data" in e2 && console.warn("THREE.WebGLRenderer: Image in DataTexture is too big (" + r2.width + "x" + r2.height + ")."), e2;
    }
    return e2;
  }
  function S(e2) {
    return e2.generateMipmaps;
  }
  function x(t2) {
    e.generateMipmap(t2);
  }
  function R(t2) {
    return t2.isWebGLCubeRenderTarget ? e.TEXTURE_CUBE_MAP : t2.isWebGL3DRenderTarget ? e.TEXTURE_3D : t2.isWebGLArrayRenderTarget || t2.isCompressedArrayTexture ? e.TEXTURE_2D_ARRAY : e.TEXTURE_2D;
  }
  function A(n, i2, r2, a2, o2 = false) {
    if (null !== n) {
      if (void 0 !== e[n]) return e[n];
      console.warn("THREE.WebGLRenderer: Attempt to use non-existing WebGL internal format '" + n + "'");
    }
    let s2 = i2;
    if (i2 === e.RED && (r2 === e.FLOAT && (s2 = e.R32F), r2 === e.HALF_FLOAT && (s2 = e.R16F), r2 === e.UNSIGNED_BYTE && (s2 = e.R8)), i2 === e.RED_INTEGER && (r2 === e.UNSIGNED_BYTE && (s2 = e.R8UI), r2 === e.UNSIGNED_SHORT && (s2 = e.R16UI), r2 === e.UNSIGNED_INT && (s2 = e.R32UI), r2 === e.BYTE && (s2 = e.R8I), r2 === e.SHORT && (s2 = e.R16I), r2 === e.INT && (s2 = e.R32I)), i2 === e.RG && (r2 === e.FLOAT && (s2 = e.RG32F), r2 === e.HALF_FLOAT && (s2 = e.RG16F), r2 === e.UNSIGNED_BYTE && (s2 = e.RG8)), i2 === e.RG_INTEGER && (r2 === e.UNSIGNED_BYTE && (s2 = e.RG8UI), r2 === e.UNSIGNED_SHORT && (s2 = e.RG16UI), r2 === e.UNSIGNED_INT && (s2 = e.RG32UI), r2 === e.BYTE && (s2 = e.RG8I), r2 === e.SHORT && (s2 = e.RG16I), r2 === e.INT && (s2 = e.RG32I)), i2 === e.RGB_INTEGER && (r2 === e.UNSIGNED_BYTE && (s2 = e.RGB8UI), r2 === e.UNSIGNED_SHORT && (s2 = e.RGB16UI), r2 === e.UNSIGNED_INT && (s2 = e.RGB32UI), r2 === e.BYTE && (s2 = e.RGB8I), r2 === e.SHORT && (s2 = e.RGB16I), r2 === e.INT && (s2 = e.RGB32I)), i2 === e.RGBA_INTEGER && (r2 === e.UNSIGNED_BYTE && (s2 = e.RGBA8UI), r2 === e.UNSIGNED_SHORT && (s2 = e.RGBA16UI), r2 === e.UNSIGNED_INT && (s2 = e.RGBA32UI), r2 === e.BYTE && (s2 = e.RGBA8I), r2 === e.SHORT && (s2 = e.RGBA16I), r2 === e.INT && (s2 = e.RGBA32I)), i2 === e.RGB && r2 === e.UNSIGNED_INT_5_9_9_9_REV && (s2 = e.RGB9_E5), i2 === e.RGBA) {
      const t2 = o2 ? _mpChunkDeps_three_build_three_core_min.Qe : _mpChunkDeps_three_build_three_core_min.ui.getTransfer(a2);
      r2 === e.FLOAT && (s2 = e.RGBA32F), r2 === e.HALF_FLOAT && (s2 = e.RGBA16F), r2 === e.UNSIGNED_BYTE && (s2 = t2 === _mpChunkDeps_three_build_three_core_min.Ke ? e.SRGB8_ALPHA8 : e.RGBA8), r2 === e.UNSIGNED_SHORT_4_4_4_4 && (s2 = e.RGBA4), r2 === e.UNSIGNED_SHORT_5_5_5_1 && (s2 = e.RGB5_A1);
    }
    return s2 !== e.R16F && s2 !== e.R32F && s2 !== e.RG16F && s2 !== e.RG32F && s2 !== e.RGBA16F && s2 !== e.RGBA32F || t.get("EXT_color_buffer_float"), s2;
  }
  function b(t2, n) {
    let i2;
    return t2 ? null === n || n === _mpChunkDeps_three_build_three_core_min.kt || n === _mpChunkDeps_three_build_three_core_min.Ft ? i2 = e.DEPTH24_STENCIL8 : n === _mpChunkDeps_three_build_three_core_min.Rt ? i2 = e.DEPTH32F_STENCIL8 : n === _mpChunkDeps_three_build_three_core_min.It && (i2 = e.DEPTH24_STENCIL8, console.warn("DepthTexture: 16 bit depth attachment is not supported with stencil. Using 24-bit attachment.")) : null === n || n === _mpChunkDeps_three_build_three_core_min.kt || n === _mpChunkDeps_three_build_three_core_min.Ft ? i2 = e.DEPTH_COMPONENT24 : n === _mpChunkDeps_three_build_three_core_min.Rt ? i2 = e.DEPTH_COMPONENT32F : n === _mpChunkDeps_three_build_three_core_min.It && (i2 = e.DEPTH_COMPONENT16), i2;
  }
  function C(e2, t2) {
    return true === S(e2) || e2.isFramebufferTexture && e2.minFilter !== _mpChunkDeps_three_build_three_core_min.ft && e2.minFilter !== _mpChunkDeps_three_build_three_core_min.wt ? Math.log2(Math.max(t2.width, t2.height)) + 1 : void 0 !== e2.mipmaps && e2.mipmaps.length > 0 ? e2.mipmaps.length : e2.isCompressedTexture && Array.isArray(e2.image) ? t2.mipmaps.length : 1;
  }
  function L(e2) {
    const t2 = e2.target;
    t2.removeEventListener("dispose", L), function(e3) {
      const t3 = r.get(e3);
      if (void 0 === t3.__webglInit) return;
      const n = e3.source, i2 = h.get(n);
      if (i2) {
        const r2 = i2[t3.__cacheKey];
        r2.usedTimes--, 0 === r2.usedTimes && U(e3), 0 === Object.keys(i2).length && h.delete(n);
      }
      r.remove(e3);
    }(t2), t2.isVideoTexture && p.delete(t2);
  }
  function P(t2) {
    const n = t2.target;
    n.removeEventListener("dispose", P), function(t3) {
      const n2 = r.get(t3);
      t3.depthTexture && (t3.depthTexture.dispose(), r.remove(t3.depthTexture));
      if (t3.isWebGLCubeRenderTarget) for (let t4 = 0; t4 < 6; t4++) {
        if (Array.isArray(n2.__webglFramebuffer[t4])) for (let i3 = 0; i3 < n2.__webglFramebuffer[t4].length; i3++) e.deleteFramebuffer(n2.__webglFramebuffer[t4][i3]);
        else e.deleteFramebuffer(n2.__webglFramebuffer[t4]);
        n2.__webglDepthbuffer && e.deleteRenderbuffer(n2.__webglDepthbuffer[t4]);
      }
      else {
        if (Array.isArray(n2.__webglFramebuffer)) for (let t4 = 0; t4 < n2.__webglFramebuffer.length; t4++) e.deleteFramebuffer(n2.__webglFramebuffer[t4]);
        else e.deleteFramebuffer(n2.__webglFramebuffer);
        if (n2.__webglDepthbuffer && e.deleteRenderbuffer(n2.__webglDepthbuffer), n2.__webglMultisampledFramebuffer && e.deleteFramebuffer(n2.__webglMultisampledFramebuffer), n2.__webglColorRenderbuffer) for (let t4 = 0; t4 < n2.__webglColorRenderbuffer.length; t4++) n2.__webglColorRenderbuffer[t4] && e.deleteRenderbuffer(n2.__webglColorRenderbuffer[t4]);
        n2.__webglDepthRenderbuffer && e.deleteRenderbuffer(n2.__webglDepthRenderbuffer);
      }
      const i2 = t3.textures;
      for (let t4 = 0, n3 = i2.length; t4 < n3; t4++) {
        const n4 = r.get(i2[t4]);
        n4.__webglTexture && (e.deleteTexture(n4.__webglTexture), s.memory.textures--), r.remove(i2[t4]);
      }
      r.remove(t3);
    }(n);
  }
  function U(t2) {
    const n = r.get(t2);
    e.deleteTexture(n.__webglTexture);
    const i2 = t2.source;
    delete h.get(i2)[n.__cacheKey], s.memory.textures--;
  }
  let w = 0;
  function D(t2, n) {
    const a2 = r.get(t2);
    if (t2.isVideoTexture && function(e2) {
      const t3 = s.render.frame;
      p.get(e2) !== t3 && (p.set(e2, t3), e2.update());
    }(t2), false === t2.isRenderTargetTexture && t2.version > 0 && a2.__version !== t2.version) {
      const e2 = t2.image;
      if (null === e2) console.warn("THREE.WebGLRenderer: Texture marked for update but no image data found.");
      else {
        if (false !== e2.complete) return void G(a2, t2, n);
        console.warn("THREE.WebGLRenderer: Texture marked for update but image is incomplete");
      }
    }
    i.bindTexture(e.TEXTURE_2D, a2.__webglTexture, e.TEXTURE0 + n);
  }
  const y = { [_mpChunkDeps_three_build_three_core_min.pt]: e.REPEAT, [_mpChunkDeps_three_build_three_core_min.mt]: e.CLAMP_TO_EDGE, [_mpChunkDeps_three_build_three_core_min.yt]: e.MIRRORED_REPEAT }, I = { [_mpChunkDeps_three_build_three_core_min.ft]: e.NEAREST, [_mpChunkDeps_three_build_three_core_min.gt]: e.NEAREST_MIPMAP_NEAREST, [_mpChunkDeps_three_build_three_core_min.bt]: e.NEAREST_MIPMAP_LINEAR, [_mpChunkDeps_three_build_three_core_min.wt]: e.LINEAR, [_mpChunkDeps_three_build_three_core_min.Mt]: e.LINEAR_MIPMAP_NEAREST, [_mpChunkDeps_three_build_three_core_min._t]: e.LINEAR_MIPMAP_LINEAR }, N = { [_mpChunkDeps_three_build_three_core_min.fs]: e.NEVER, [_mpChunkDeps_three_build_three_core_min.Ss]: e.ALWAYS, [_mpChunkDeps_three_build_three_core_min.gs]: e.LESS, [_mpChunkDeps_three_build_three_core_min.bs]: e.LEQUAL, [_mpChunkDeps_three_build_three_core_min.xs]: e.EQUAL, [_mpChunkDeps_three_build_three_core_min.Ms]: e.GEQUAL, [_mpChunkDeps_three_build_three_core_min.vs]: e.GREATER, [_mpChunkDeps_three_build_three_core_min.ws]: e.NOTEQUAL };
  function O(n, i2) {
    if (i2.type !== _mpChunkDeps_three_build_three_core_min.Rt || false !== t.has("OES_texture_float_linear") || i2.magFilter !== _mpChunkDeps_three_build_three_core_min.wt && i2.magFilter !== _mpChunkDeps_three_build_three_core_min.Mt && i2.magFilter !== _mpChunkDeps_three_build_three_core_min.bt && i2.magFilter !== _mpChunkDeps_three_build_three_core_min._t && i2.minFilter !== _mpChunkDeps_three_build_three_core_min.wt && i2.minFilter !== _mpChunkDeps_three_build_three_core_min.Mt && i2.minFilter !== _mpChunkDeps_three_build_three_core_min.bt && i2.minFilter !== _mpChunkDeps_three_build_three_core_min._t || console.warn("THREE.WebGLRenderer: Unable to use linear filtering with floating point textures. OES_texture_float_linear not supported on this device."), e.texParameteri(n, e.TEXTURE_WRAP_S, y[i2.wrapS]), e.texParameteri(n, e.TEXTURE_WRAP_T, y[i2.wrapT]), n !== e.TEXTURE_3D && n !== e.TEXTURE_2D_ARRAY || e.texParameteri(n, e.TEXTURE_WRAP_R, y[i2.wrapR]), e.texParameteri(n, e.TEXTURE_MAG_FILTER, I[i2.magFilter]), e.texParameteri(n, e.TEXTURE_MIN_FILTER, I[i2.minFilter]), i2.compareFunction && (e.texParameteri(n, e.TEXTURE_COMPARE_MODE, e.COMPARE_REF_TO_TEXTURE), e.texParameteri(n, e.TEXTURE_COMPARE_FUNC, N[i2.compareFunction])), true === t.has("EXT_texture_filter_anisotropic")) {
      if (i2.magFilter === _mpChunkDeps_three_build_three_core_min.ft) return;
      if (i2.minFilter !== _mpChunkDeps_three_build_three_core_min.bt && i2.minFilter !== _mpChunkDeps_three_build_three_core_min._t) return;
      if (i2.type === _mpChunkDeps_three_build_three_core_min.Rt && false === t.has("OES_texture_float_linear")) return;
      if (i2.anisotropy > 1 || r.get(i2).__currentAnisotropy) {
        const o2 = t.get("EXT_texture_filter_anisotropic");
        e.texParameterf(n, o2.TEXTURE_MAX_ANISOTROPY_EXT, Math.min(i2.anisotropy, a.getMaxAnisotropy())), r.get(i2).__currentAnisotropy = i2.anisotropy;
      }
    }
  }
  function H(t2, n) {
    let i2 = false;
    void 0 === t2.__webglInit && (t2.__webglInit = true, n.addEventListener("dispose", L));
    const r2 = n.source;
    let a2 = h.get(r2);
    void 0 === a2 && (a2 = {}, h.set(r2, a2));
    const o2 = function(e2) {
      const t3 = [];
      return t3.push(e2.wrapS), t3.push(e2.wrapT), t3.push(e2.wrapR || 0), t3.push(e2.magFilter), t3.push(e2.minFilter), t3.push(e2.anisotropy), t3.push(e2.internalFormat), t3.push(e2.format), t3.push(e2.type), t3.push(e2.generateMipmaps), t3.push(e2.premultiplyAlpha), t3.push(e2.flipY), t3.push(e2.unpackAlignment), t3.push(e2.colorSpace), t3.join();
    }(n);
    if (o2 !== t2.__cacheKey) {
      void 0 === a2[o2] && (a2[o2] = { texture: e.createTexture(), usedTimes: 0 }, s.memory.textures++, i2 = true), a2[o2].usedTimes++;
      const r3 = a2[t2.__cacheKey];
      void 0 !== r3 && (a2[t2.__cacheKey].usedTimes--, 0 === r3.usedTimes && U(n)), t2.__cacheKey = o2, t2.__webglTexture = a2[o2].texture;
    }
    return i2;
  }
  function G(t2, n, s2) {
    let l2 = e.TEXTURE_2D;
    (n.isDataArrayTexture || n.isCompressedArrayTexture) && (l2 = e.TEXTURE_2D_ARRAY), n.isData3DTexture && (l2 = e.TEXTURE_3D);
    const c2 = H(t2, n), d2 = n.source;
    i.bindTexture(l2, t2.__webglTexture, e.TEXTURE0 + s2);
    const f = r.get(d2);
    if (d2.version !== f.__version || true === c2) {
      i.activeTexture(e.TEXTURE0 + s2);
      const t3 = _mpChunkDeps_three_build_three_core_min.ui.getPrimaries(_mpChunkDeps_three_build_three_core_min.ui.workingColorSpace), r2 = n.colorSpace === _mpChunkDeps_three_build_three_core_min.Ze ? null : _mpChunkDeps_three_build_three_core_min.ui.getPrimaries(n.colorSpace), p2 = n.colorSpace === _mpChunkDeps_three_build_three_core_min.Ze || t3 === r2 ? e.NONE : e.BROWSER_DEFAULT_WEBGL;
      e.pixelStorei(e.UNPACK_FLIP_Y_WEBGL, n.flipY), e.pixelStorei(e.UNPACK_PREMULTIPLY_ALPHA_WEBGL, n.premultiplyAlpha), e.pixelStorei(e.UNPACK_ALIGNMENT, n.unpackAlignment), e.pixelStorei(e.UNPACK_COLORSPACE_CONVERSION_WEBGL, p2);
      let m2 = v(n.image, false, a.maxTextureSize);
      m2 = q(n, m2);
      const h2 = o.convert(n.format, n.colorSpace), _2 = o.convert(n.type);
      let g2, T = A(n.internalFormat, h2, _2, n.colorSpace, n.isVideoTexture);
      O(l2, n);
      const M = n.mipmaps, R2 = true !== n.isVideoTexture, L2 = void 0 === f.__version || true === c2, P2 = d2.dataReady, U2 = C(n, m2);
      if (n.isDepthTexture) T = b(n.format === _mpChunkDeps_three_build_three_core_min.Ht, n.type), L2 && (R2 ? i.texStorage2D(e.TEXTURE_2D, 1, T, m2.width, m2.height) : i.texImage2D(e.TEXTURE_2D, 0, T, m2.width, m2.height, 0, h2, _2, null));
      else if (n.isDataTexture) if (M.length > 0) {
        R2 && L2 && i.texStorage2D(e.TEXTURE_2D, U2, T, M[0].width, M[0].height);
        for (let t4 = 0, n2 = M.length; t4 < n2; t4++) g2 = M[t4], R2 ? P2 && i.texSubImage2D(e.TEXTURE_2D, t4, 0, 0, g2.width, g2.height, h2, _2, g2.data) : i.texImage2D(e.TEXTURE_2D, t4, T, g2.width, g2.height, 0, h2, _2, g2.data);
        n.generateMipmaps = false;
      } else R2 ? (L2 && i.texStorage2D(e.TEXTURE_2D, U2, T, m2.width, m2.height), P2 && i.texSubImage2D(e.TEXTURE_2D, 0, 0, 0, m2.width, m2.height, h2, _2, m2.data)) : i.texImage2D(e.TEXTURE_2D, 0, T, m2.width, m2.height, 0, h2, _2, m2.data);
      else if (n.isCompressedTexture) if (n.isCompressedArrayTexture) {
        R2 && L2 && i.texStorage3D(e.TEXTURE_2D_ARRAY, U2, T, M[0].width, M[0].height, m2.depth);
        for (let t4 = 0, r3 = M.length; t4 < r3; t4++) if (g2 = M[t4], n.format !== _mpChunkDeps_three_build_three_core_min.Wt) if (null !== h2) if (R2) {
          if (P2) if (n.layerUpdates.size > 0) {
            const r4 = _mpChunkDeps_three_build_three_core_min.vd(g2.width, g2.height, n.format, n.type);
            for (const a2 of n.layerUpdates) {
              const n2 = g2.data.subarray(a2 * r4 / g2.data.BYTES_PER_ELEMENT, (a2 + 1) * r4 / g2.data.BYTES_PER_ELEMENT);
              i.compressedTexSubImage3D(e.TEXTURE_2D_ARRAY, t4, 0, 0, a2, g2.width, g2.height, 1, h2, n2);
            }
            n.clearLayerUpdates();
          } else i.compressedTexSubImage3D(e.TEXTURE_2D_ARRAY, t4, 0, 0, 0, g2.width, g2.height, m2.depth, h2, g2.data);
        } else i.compressedTexImage3D(e.TEXTURE_2D_ARRAY, t4, T, g2.width, g2.height, m2.depth, 0, g2.data, 0, 0);
        else console.warn("THREE.WebGLRenderer: Attempt to load unsupported compressed texture format in .uploadTexture()");
        else R2 ? P2 && i.texSubImage3D(e.TEXTURE_2D_ARRAY, t4, 0, 0, 0, g2.width, g2.height, m2.depth, h2, _2, g2.data) : i.texImage3D(e.TEXTURE_2D_ARRAY, t4, T, g2.width, g2.height, m2.depth, 0, h2, _2, g2.data);
      } else {
        R2 && L2 && i.texStorage2D(e.TEXTURE_2D, U2, T, M[0].width, M[0].height);
        for (let t4 = 0, r3 = M.length; t4 < r3; t4++) g2 = M[t4], n.format !== _mpChunkDeps_three_build_three_core_min.Wt ? null !== h2 ? R2 ? P2 && i.compressedTexSubImage2D(e.TEXTURE_2D, t4, 0, 0, g2.width, g2.height, h2, g2.data) : i.compressedTexImage2D(e.TEXTURE_2D, t4, T, g2.width, g2.height, 0, g2.data) : console.warn("THREE.WebGLRenderer: Attempt to load unsupported compressed texture format in .uploadTexture()") : R2 ? P2 && i.texSubImage2D(e.TEXTURE_2D, t4, 0, 0, g2.width, g2.height, h2, _2, g2.data) : i.texImage2D(e.TEXTURE_2D, t4, T, g2.width, g2.height, 0, h2, _2, g2.data);
      }
      else if (n.isDataArrayTexture) if (R2) {
        if (L2 && i.texStorage3D(e.TEXTURE_2D_ARRAY, U2, T, m2.width, m2.height, m2.depth), P2) if (n.layerUpdates.size > 0) {
          const t4 = _mpChunkDeps_three_build_three_core_min.vd(m2.width, m2.height, n.format, n.type);
          for (const r3 of n.layerUpdates) {
            const n2 = m2.data.subarray(r3 * t4 / m2.data.BYTES_PER_ELEMENT, (r3 + 1) * t4 / m2.data.BYTES_PER_ELEMENT);
            i.texSubImage3D(e.TEXTURE_2D_ARRAY, 0, 0, 0, r3, m2.width, m2.height, 1, h2, _2, n2);
          }
          n.clearLayerUpdates();
        } else i.texSubImage3D(e.TEXTURE_2D_ARRAY, 0, 0, 0, 0, m2.width, m2.height, m2.depth, h2, _2, m2.data);
      } else i.texImage3D(e.TEXTURE_2D_ARRAY, 0, T, m2.width, m2.height, m2.depth, 0, h2, _2, m2.data);
      else if (n.isData3DTexture) R2 ? (L2 && i.texStorage3D(e.TEXTURE_3D, U2, T, m2.width, m2.height, m2.depth), P2 && i.texSubImage3D(e.TEXTURE_3D, 0, 0, 0, 0, m2.width, m2.height, m2.depth, h2, _2, m2.data)) : i.texImage3D(e.TEXTURE_3D, 0, T, m2.width, m2.height, m2.depth, 0, h2, _2, m2.data);
      else if (n.isFramebufferTexture) {
        if (L2) if (R2) i.texStorage2D(e.TEXTURE_2D, U2, T, m2.width, m2.height);
        else {
          let t4 = m2.width, n2 = m2.height;
          for (let r3 = 0; r3 < U2; r3++) i.texImage2D(e.TEXTURE_2D, r3, T, t4, n2, 0, h2, _2, null), t4 >>= 1, n2 >>= 1;
        }
      } else if (M.length > 0) {
        if (R2 && L2) {
          const t4 = Z(M[0]);
          i.texStorage2D(e.TEXTURE_2D, U2, T, t4.width, t4.height);
        }
        for (let t4 = 0, n2 = M.length; t4 < n2; t4++) g2 = M[t4], R2 ? P2 && i.texSubImage2D(e.TEXTURE_2D, t4, 0, 0, h2, _2, g2) : i.texImage2D(e.TEXTURE_2D, t4, T, h2, _2, g2);
        n.generateMipmaps = false;
      } else if (R2) {
        if (L2) {
          const t4 = Z(m2);
          i.texStorage2D(e.TEXTURE_2D, U2, T, t4.width, t4.height);
        }
        P2 && i.texSubImage2D(e.TEXTURE_2D, 0, 0, 0, h2, _2, m2);
      } else i.texImage2D(e.TEXTURE_2D, 0, T, h2, _2, m2);
      S(n) && x(l2), f.__version = d2.version, n.onUpdate && n.onUpdate(n);
    }
    t2.__version = n.version;
  }
  function V(t2, n, a2, s2, c2, d2) {
    const u = o.convert(a2.format, a2.colorSpace), f = o.convert(a2.type), p2 = A(a2.internalFormat, u, f, a2.colorSpace), m2 = r.get(n), h2 = r.get(a2);
    if (h2.__renderTarget = n, !m2.__hasExternalTextures) {
      const t3 = Math.max(1, n.width >> d2), r2 = Math.max(1, n.height >> d2);
      c2 === e.TEXTURE_3D || c2 === e.TEXTURE_2D_ARRAY ? i.texImage3D(c2, d2, p2, t3, r2, n.depth, 0, u, f, null) : i.texImage2D(c2, d2, p2, t3, r2, 0, u, f, null);
    }
    i.bindFramebuffer(e.FRAMEBUFFER, t2), K(n) ? l.framebufferTexture2DMultisampleEXT(e.FRAMEBUFFER, s2, c2, h2.__webglTexture, 0, j(n)) : (c2 === e.TEXTURE_2D || c2 >= e.TEXTURE_CUBE_MAP_POSITIVE_X && c2 <= e.TEXTURE_CUBE_MAP_NEGATIVE_Z) && e.framebufferTexture2D(e.FRAMEBUFFER, s2, c2, h2.__webglTexture, d2), i.bindFramebuffer(e.FRAMEBUFFER, null);
  }
  function z(t2, n, i2) {
    if (e.bindRenderbuffer(e.RENDERBUFFER, t2), n.depthBuffer) {
      const r2 = n.depthTexture, a2 = r2 && r2.isDepthTexture ? r2.type : null, o2 = b(n.stencilBuffer, a2), s2 = n.stencilBuffer ? e.DEPTH_STENCIL_ATTACHMENT : e.DEPTH_ATTACHMENT, c2 = j(n);
      K(n) ? l.renderbufferStorageMultisampleEXT(e.RENDERBUFFER, c2, o2, n.width, n.height) : i2 ? e.renderbufferStorageMultisample(e.RENDERBUFFER, c2, o2, n.width, n.height) : e.renderbufferStorage(e.RENDERBUFFER, o2, n.width, n.height), e.framebufferRenderbuffer(e.FRAMEBUFFER, s2, e.RENDERBUFFER, t2);
    } else {
      const t3 = n.textures;
      for (let r2 = 0; r2 < t3.length; r2++) {
        const a2 = t3[r2], s2 = o.convert(a2.format, a2.colorSpace), c2 = o.convert(a2.type), d2 = A(a2.internalFormat, s2, c2, a2.colorSpace), u = j(n);
        i2 && false === K(n) ? e.renderbufferStorageMultisample(e.RENDERBUFFER, u, d2, n.width, n.height) : K(n) ? l.renderbufferStorageMultisampleEXT(e.RENDERBUFFER, u, d2, n.width, n.height) : e.renderbufferStorage(e.RENDERBUFFER, d2, n.width, n.height);
      }
    }
    e.bindRenderbuffer(e.RENDERBUFFER, null);
  }
  function k(t2) {
    const n = r.get(t2), a2 = true === t2.isWebGLCubeRenderTarget;
    if (n.__boundDepthTexture !== t2.depthTexture) {
      const e2 = t2.depthTexture;
      if (n.__depthDisposeCallback && n.__depthDisposeCallback(), e2) {
        const t3 = () => {
          delete n.__boundDepthTexture, delete n.__depthDisposeCallback, e2.removeEventListener("dispose", t3);
        };
        e2.addEventListener("dispose", t3), n.__depthDisposeCallback = t3;
      }
      n.__boundDepthTexture = e2;
    }
    if (t2.depthTexture && !n.__autoAllocateDepthBuffer) {
      if (a2) throw new Error("target.depthTexture not supported in Cube render targets");
      !function(t3, n2) {
        if (n2 && n2.isWebGLCubeRenderTarget) throw new Error("Depth Texture with cube render targets is not supported");
        if (i.bindFramebuffer(e.FRAMEBUFFER, t3), !n2.depthTexture || !n2.depthTexture.isDepthTexture) throw new Error("renderTarget.depthTexture must be an instance of THREE.DepthTexture");
        const a3 = r.get(n2.depthTexture);
        a3.__renderTarget = n2, a3.__webglTexture && n2.depthTexture.image.width === n2.width && n2.depthTexture.image.height === n2.height || (n2.depthTexture.image.width = n2.width, n2.depthTexture.image.height = n2.height, n2.depthTexture.needsUpdate = true), D(n2.depthTexture, 0);
        const o2 = a3.__webglTexture, s2 = j(n2);
        if (n2.depthTexture.format === _mpChunkDeps_three_build_three_core_min.Dt) K(n2) ? l.framebufferTexture2DMultisampleEXT(e.FRAMEBUFFER, e.DEPTH_ATTACHMENT, e.TEXTURE_2D, o2, 0, s2) : e.framebufferTexture2D(e.FRAMEBUFFER, e.DEPTH_ATTACHMENT, e.TEXTURE_2D, o2, 0);
        else {
          if (n2.depthTexture.format !== _mpChunkDeps_three_build_three_core_min.Ht) throw new Error("Unknown depthTexture format");
          K(n2) ? l.framebufferTexture2DMultisampleEXT(e.FRAMEBUFFER, e.DEPTH_STENCIL_ATTACHMENT, e.TEXTURE_2D, o2, 0, s2) : e.framebufferTexture2D(e.FRAMEBUFFER, e.DEPTH_STENCIL_ATTACHMENT, e.TEXTURE_2D, o2, 0);
        }
      }(n.__webglFramebuffer, t2);
    } else if (a2) {
      n.__webglDepthbuffer = [];
      for (let r2 = 0; r2 < 6; r2++) if (i.bindFramebuffer(e.FRAMEBUFFER, n.__webglFramebuffer[r2]), void 0 === n.__webglDepthbuffer[r2]) n.__webglDepthbuffer[r2] = e.createRenderbuffer(), z(n.__webglDepthbuffer[r2], t2, false);
      else {
        const i2 = t2.stencilBuffer ? e.DEPTH_STENCIL_ATTACHMENT : e.DEPTH_ATTACHMENT, a3 = n.__webglDepthbuffer[r2];
        e.bindRenderbuffer(e.RENDERBUFFER, a3), e.framebufferRenderbuffer(e.FRAMEBUFFER, i2, e.RENDERBUFFER, a3);
      }
    } else if (i.bindFramebuffer(e.FRAMEBUFFER, n.__webglFramebuffer), void 0 === n.__webglDepthbuffer) n.__webglDepthbuffer = e.createRenderbuffer(), z(n.__webglDepthbuffer, t2, false);
    else {
      const i2 = t2.stencilBuffer ? e.DEPTH_STENCIL_ATTACHMENT : e.DEPTH_ATTACHMENT, r2 = n.__webglDepthbuffer;
      e.bindRenderbuffer(e.RENDERBUFFER, r2), e.framebufferRenderbuffer(e.FRAMEBUFFER, i2, e.RENDERBUFFER, r2);
    }
    i.bindFramebuffer(e.FRAMEBUFFER, null);
  }
  const W = [], Y = [];
  function j(e2) {
    return Math.min(a.maxSamples, e2.samples);
  }
  function K(e2) {
    const n = r.get(e2);
    return e2.samples > 0 && true === t.has("WEBGL_multisampled_render_to_texture") && false !== n.__useRenderToTexture;
  }
  function q(e2, t2) {
    const n = e2.colorSpace, i2 = e2.format, r2 = e2.type;
    return true === e2.isCompressedTexture || true === e2.isVideoTexture || n !== _mpChunkDeps_three_build_three_core_min.$e && n !== _mpChunkDeps_three_build_three_core_min.Ze && (_mpChunkDeps_three_build_three_core_min.ui.getTransfer(n) === _mpChunkDeps_three_build_three_core_min.Ke ? i2 === _mpChunkDeps_three_build_three_core_min.Wt && r2 === _mpChunkDeps_three_build_three_core_min.Tt || console.warn("THREE.WebGLTextures: sRGB encoded textures have to use RGBAFormat and UnsignedByteType.") : console.error("THREE.WebGLTextures: Unsupported texture color space:", n)), t2;
  }
  function Z(e2) {
    return "undefined" != typeof THREEGlobals["HTMLImageElement"] && e2 instanceof THREEGlobals["HTMLImageElement"] ? (d.width = e2.naturalWidth || e2.width, d.height = e2.naturalHeight || e2.height) : "undefined" != typeof THREEGlobals["VideoFrame"] && e2 instanceof THREEGlobals["VideoFrame"] ? (d.width = e2.displayWidth, d.height = e2.displayHeight) : (d.width = e2.width, d.height = e2.height), d;
  }
  this.allocateTextureUnit = function() {
    const e2 = w;
    return e2 >= a.maxTextures && console.warn("THREE.WebGLTextures: Trying to use " + e2 + " texture units while this GPU supports only " + a.maxTextures), w += 1, e2;
  }, this.resetTextureUnits = function() {
    w = 0;
  }, this.setTexture2D = D, this.setTexture2DArray = function(t2, n) {
    const a2 = r.get(t2);
    t2.version > 0 && a2.__version !== t2.version ? G(a2, t2, n) : i.bindTexture(e.TEXTURE_2D_ARRAY, a2.__webglTexture, e.TEXTURE0 + n);
  }, this.setTexture3D = function(t2, n) {
    const a2 = r.get(t2);
    t2.version > 0 && a2.__version !== t2.version ? G(a2, t2, n) : i.bindTexture(e.TEXTURE_3D, a2.__webglTexture, e.TEXTURE0 + n);
  }, this.setTextureCube = function(t2, n) {
    const s2 = r.get(t2);
    t2.version > 0 && s2.__version !== t2.version ? function(t3, n2, s3) {
      if (6 !== n2.image.length) return;
      const l2 = H(t3, n2), c2 = n2.source;
      i.bindTexture(e.TEXTURE_CUBE_MAP, t3.__webglTexture, e.TEXTURE0 + s3);
      const d2 = r.get(c2);
      if (c2.version !== d2.__version || true === l2) {
        i.activeTexture(e.TEXTURE0 + s3);
        const t4 = _mpChunkDeps_three_build_three_core_min.ui.getPrimaries(_mpChunkDeps_three_build_three_core_min.ui.workingColorSpace), r2 = n2.colorSpace === _mpChunkDeps_three_build_three_core_min.Ze ? null : _mpChunkDeps_three_build_three_core_min.ui.getPrimaries(n2.colorSpace), f = n2.colorSpace === _mpChunkDeps_three_build_three_core_min.Ze || t4 === r2 ? e.NONE : e.BROWSER_DEFAULT_WEBGL;
        e.pixelStorei(e.UNPACK_FLIP_Y_WEBGL, n2.flipY), e.pixelStorei(e.UNPACK_PREMULTIPLY_ALPHA_WEBGL, n2.premultiplyAlpha), e.pixelStorei(e.UNPACK_ALIGNMENT, n2.unpackAlignment), e.pixelStorei(e.UNPACK_COLORSPACE_CONVERSION_WEBGL, f);
        const p2 = n2.isCompressedTexture || n2.image[0].isCompressedTexture, m2 = n2.image[0] && n2.image[0].isDataTexture, h2 = [];
        for (let e2 = 0; e2 < 6; e2++) h2[e2] = p2 || m2 ? m2 ? n2.image[e2].image : n2.image[e2] : v(n2.image[e2], true, a.maxCubemapSize), h2[e2] = q(n2, h2[e2]);
        const _2 = h2[0], g2 = o.convert(n2.format, n2.colorSpace), T = o.convert(n2.type), M = A(n2.internalFormat, g2, T, n2.colorSpace), R2 = true !== n2.isVideoTexture, b2 = void 0 === d2.__version || true === l2, L2 = c2.dataReady;
        let P2, U2 = C(n2, _2);
        if (O(e.TEXTURE_CUBE_MAP, n2), p2) {
          R2 && b2 && i.texStorage2D(e.TEXTURE_CUBE_MAP, U2, M, _2.width, _2.height);
          for (let t5 = 0; t5 < 6; t5++) {
            P2 = h2[t5].mipmaps;
            for (let r3 = 0; r3 < P2.length; r3++) {
              const a2 = P2[r3];
              n2.format !== _mpChunkDeps_three_build_three_core_min.Wt ? null !== g2 ? R2 ? L2 && i.compressedTexSubImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X + t5, r3, 0, 0, a2.width, a2.height, g2, a2.data) : i.compressedTexImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X + t5, r3, M, a2.width, a2.height, 0, a2.data) : console.warn("THREE.WebGLRenderer: Attempt to load unsupported compressed texture format in .setTextureCube()") : R2 ? L2 && i.texSubImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X + t5, r3, 0, 0, a2.width, a2.height, g2, T, a2.data) : i.texImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X + t5, r3, M, a2.width, a2.height, 0, g2, T, a2.data);
            }
          }
        } else {
          if (P2 = n2.mipmaps, R2 && b2) {
            P2.length > 0 && U2++;
            const t5 = Z(h2[0]);
            i.texStorage2D(e.TEXTURE_CUBE_MAP, U2, M, t5.width, t5.height);
          }
          for (let t5 = 0; t5 < 6; t5++) if (m2) {
            R2 ? L2 && i.texSubImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X + t5, 0, 0, 0, h2[t5].width, h2[t5].height, g2, T, h2[t5].data) : i.texImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X + t5, 0, M, h2[t5].width, h2[t5].height, 0, g2, T, h2[t5].data);
            for (let n3 = 0; n3 < P2.length; n3++) {
              const r3 = P2[n3].image[t5].image;
              R2 ? L2 && i.texSubImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X + t5, n3 + 1, 0, 0, r3.width, r3.height, g2, T, r3.data) : i.texImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X + t5, n3 + 1, M, r3.width, r3.height, 0, g2, T, r3.data);
            }
          } else {
            R2 ? L2 && i.texSubImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X + t5, 0, 0, 0, g2, T, h2[t5]) : i.texImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X + t5, 0, M, g2, T, h2[t5]);
            for (let n3 = 0; n3 < P2.length; n3++) {
              const r3 = P2[n3];
              R2 ? L2 && i.texSubImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X + t5, n3 + 1, 0, 0, g2, T, r3.image[t5]) : i.texImage2D(e.TEXTURE_CUBE_MAP_POSITIVE_X + t5, n3 + 1, M, g2, T, r3.image[t5]);
            }
          }
        }
        S(n2) && x(e.TEXTURE_CUBE_MAP), d2.__version = c2.version, n2.onUpdate && n2.onUpdate(n2);
      }
      t3.__version = n2.version;
    }(s2, t2, n) : i.bindTexture(e.TEXTURE_CUBE_MAP, s2.__webglTexture, e.TEXTURE0 + n);
  }, this.rebindTextures = function(t2, n, i2) {
    const a2 = r.get(t2);
    void 0 !== n && V(a2.__webglFramebuffer, t2, t2.texture, e.COLOR_ATTACHMENT0, e.TEXTURE_2D, 0), void 0 !== i2 && k(t2);
  }, this.setupRenderTarget = function(t2) {
    const n = t2.texture, a2 = r.get(t2), l2 = r.get(n);
    t2.addEventListener("dispose", P);
    const c2 = t2.textures, d2 = true === t2.isWebGLCubeRenderTarget, u = c2.length > 1;
    if (u || (void 0 === l2.__webglTexture && (l2.__webglTexture = e.createTexture()), l2.__version = n.version, s.memory.textures++), d2) {
      a2.__webglFramebuffer = [];
      for (let t3 = 0; t3 < 6; t3++) if (n.mipmaps && n.mipmaps.length > 0) {
        a2.__webglFramebuffer[t3] = [];
        for (let i2 = 0; i2 < n.mipmaps.length; i2++) a2.__webglFramebuffer[t3][i2] = e.createFramebuffer();
      } else a2.__webglFramebuffer[t3] = e.createFramebuffer();
    } else {
      if (n.mipmaps && n.mipmaps.length > 0) {
        a2.__webglFramebuffer = [];
        for (let t3 = 0; t3 < n.mipmaps.length; t3++) a2.__webglFramebuffer[t3] = e.createFramebuffer();
      } else a2.__webglFramebuffer = e.createFramebuffer();
      if (u) for (let t3 = 0, n2 = c2.length; t3 < n2; t3++) {
        const n3 = r.get(c2[t3]);
        void 0 === n3.__webglTexture && (n3.__webglTexture = e.createTexture(), s.memory.textures++);
      }
      if (t2.samples > 0 && false === K(t2)) {
        a2.__webglMultisampledFramebuffer = e.createFramebuffer(), a2.__webglColorRenderbuffer = [], i.bindFramebuffer(e.FRAMEBUFFER, a2.__webglMultisampledFramebuffer);
        for (let n2 = 0; n2 < c2.length; n2++) {
          const i2 = c2[n2];
          a2.__webglColorRenderbuffer[n2] = e.createRenderbuffer(), e.bindRenderbuffer(e.RENDERBUFFER, a2.__webglColorRenderbuffer[n2]);
          const r2 = o.convert(i2.format, i2.colorSpace), s2 = o.convert(i2.type), l3 = A(i2.internalFormat, r2, s2, i2.colorSpace, true === t2.isXRRenderTarget), d3 = j(t2);
          e.renderbufferStorageMultisample(e.RENDERBUFFER, d3, l3, t2.width, t2.height), e.framebufferRenderbuffer(e.FRAMEBUFFER, e.COLOR_ATTACHMENT0 + n2, e.RENDERBUFFER, a2.__webglColorRenderbuffer[n2]);
        }
        e.bindRenderbuffer(e.RENDERBUFFER, null), t2.depthBuffer && (a2.__webglDepthRenderbuffer = e.createRenderbuffer(), z(a2.__webglDepthRenderbuffer, t2, true)), i.bindFramebuffer(e.FRAMEBUFFER, null);
      }
    }
    if (d2) {
      i.bindTexture(e.TEXTURE_CUBE_MAP, l2.__webglTexture), O(e.TEXTURE_CUBE_MAP, n);
      for (let i2 = 0; i2 < 6; i2++) if (n.mipmaps && n.mipmaps.length > 0) for (let r2 = 0; r2 < n.mipmaps.length; r2++) V(a2.__webglFramebuffer[i2][r2], t2, n, e.COLOR_ATTACHMENT0, e.TEXTURE_CUBE_MAP_POSITIVE_X + i2, r2);
      else V(a2.__webglFramebuffer[i2], t2, n, e.COLOR_ATTACHMENT0, e.TEXTURE_CUBE_MAP_POSITIVE_X + i2, 0);
      S(n) && x(e.TEXTURE_CUBE_MAP), i.unbindTexture();
    } else if (u) {
      for (let n2 = 0, o2 = c2.length; n2 < o2; n2++) {
        const o3 = c2[n2], s2 = r.get(o3);
        i.bindTexture(e.TEXTURE_2D, s2.__webglTexture), O(e.TEXTURE_2D, o3), V(a2.__webglFramebuffer, t2, o3, e.COLOR_ATTACHMENT0 + n2, e.TEXTURE_2D, 0), S(o3) && x(e.TEXTURE_2D);
      }
      i.unbindTexture();
    } else {
      let r2 = e.TEXTURE_2D;
      if ((t2.isWebGL3DRenderTarget || t2.isWebGLArrayRenderTarget) && (r2 = t2.isWebGL3DRenderTarget ? e.TEXTURE_3D : e.TEXTURE_2D_ARRAY), i.bindTexture(r2, l2.__webglTexture), O(r2, n), n.mipmaps && n.mipmaps.length > 0) for (let i2 = 0; i2 < n.mipmaps.length; i2++) V(a2.__webglFramebuffer[i2], t2, n, e.COLOR_ATTACHMENT0, r2, i2);
      else V(a2.__webglFramebuffer, t2, n, e.COLOR_ATTACHMENT0, r2, 0);
      S(n) && x(r2), i.unbindTexture();
    }
    t2.depthBuffer && k(t2);
  }, this.updateRenderTargetMipmap = function(e2) {
    const t2 = e2.textures;
    for (let n = 0, a2 = t2.length; n < a2; n++) {
      const a3 = t2[n];
      if (S(a3)) {
        const t3 = R(e2), n2 = r.get(a3).__webglTexture;
        i.bindTexture(t3, n2), x(t3), i.unbindTexture();
      }
    }
  }, this.updateMultisampleRenderTarget = function(t2) {
    if (t2.samples > 0) {
      if (false === K(t2)) {
        const n = t2.textures, a2 = t2.width, o2 = t2.height;
        let s2 = e.COLOR_BUFFER_BIT;
        const l2 = t2.stencilBuffer ? e.DEPTH_STENCIL_ATTACHMENT : e.DEPTH_ATTACHMENT, d2 = r.get(t2), u = n.length > 1;
        if (u) for (let t3 = 0; t3 < n.length; t3++) i.bindFramebuffer(e.FRAMEBUFFER, d2.__webglMultisampledFramebuffer), e.framebufferRenderbuffer(e.FRAMEBUFFER, e.COLOR_ATTACHMENT0 + t3, e.RENDERBUFFER, null), i.bindFramebuffer(e.FRAMEBUFFER, d2.__webglFramebuffer), e.framebufferTexture2D(e.DRAW_FRAMEBUFFER, e.COLOR_ATTACHMENT0 + t3, e.TEXTURE_2D, null, 0);
        i.bindFramebuffer(e.READ_FRAMEBUFFER, d2.__webglMultisampledFramebuffer), i.bindFramebuffer(e.DRAW_FRAMEBUFFER, d2.__webglFramebuffer);
        for (let i2 = 0; i2 < n.length; i2++) {
          if (t2.resolveDepthBuffer && (t2.depthBuffer && (s2 |= e.DEPTH_BUFFER_BIT), t2.stencilBuffer && t2.resolveStencilBuffer && (s2 |= e.STENCIL_BUFFER_BIT)), u) {
            e.framebufferRenderbuffer(e.READ_FRAMEBUFFER, e.COLOR_ATTACHMENT0, e.RENDERBUFFER, d2.__webglColorRenderbuffer[i2]);
            const t3 = r.get(n[i2]).__webglTexture;
            e.framebufferTexture2D(e.DRAW_FRAMEBUFFER, e.COLOR_ATTACHMENT0, e.TEXTURE_2D, t3, 0);
          }
          e.blitFramebuffer(0, 0, a2, o2, 0, 0, a2, o2, s2, e.NEAREST), true === c && (W.length = 0, Y.length = 0, W.push(e.COLOR_ATTACHMENT0 + i2), t2.depthBuffer && false === t2.resolveDepthBuffer && (W.push(l2), Y.push(l2), e.invalidateFramebuffer(e.DRAW_FRAMEBUFFER, Y)), e.invalidateFramebuffer(e.READ_FRAMEBUFFER, W));
        }
        if (i.bindFramebuffer(e.READ_FRAMEBUFFER, null), i.bindFramebuffer(e.DRAW_FRAMEBUFFER, null), u) for (let t3 = 0; t3 < n.length; t3++) {
          i.bindFramebuffer(e.FRAMEBUFFER, d2.__webglMultisampledFramebuffer), e.framebufferRenderbuffer(e.FRAMEBUFFER, e.COLOR_ATTACHMENT0 + t3, e.RENDERBUFFER, d2.__webglColorRenderbuffer[t3]);
          const a3 = r.get(n[t3]).__webglTexture;
          i.bindFramebuffer(e.FRAMEBUFFER, d2.__webglFramebuffer), e.framebufferTexture2D(e.DRAW_FRAMEBUFFER, e.COLOR_ATTACHMENT0 + t3, e.TEXTURE_2D, a3, 0);
        }
        i.bindFramebuffer(e.DRAW_FRAMEBUFFER, d2.__webglMultisampledFramebuffer);
      } else if (t2.depthBuffer && false === t2.resolveDepthBuffer && c) {
        const n = t2.stencilBuffer ? e.DEPTH_STENCIL_ATTACHMENT : e.DEPTH_ATTACHMENT;
        e.invalidateFramebuffer(e.DRAW_FRAMEBUFFER, [n]);
      }
    }
  }, this.setupDepthRenderbuffer = k, this.setupFrameBufferTexture = V, this.useMultisampledRTT = K;
}
function ia(e, t) {
  return { convert: function(n, i = _mpChunkDeps_three_build_three_core_min.Ze) {
    let r;
    const a = _mpChunkDeps_three_build_three_core_min.ui.getTransfer(i);
    if (n === _mpChunkDeps_three_build_three_core_min.Tt) return e.UNSIGNED_BYTE;
    if (n === _mpChunkDeps_three_build_three_core_min.Pt) return e.UNSIGNED_SHORT_4_4_4_4;
    if (n === _mpChunkDeps_three_build_three_core_min.Ot) return e.UNSIGNED_SHORT_5_5_5_1;
    if (n === _mpChunkDeps_three_build_three_core_min.Nt) return e.UNSIGNED_INT_5_9_9_9_REV;
    if (n === _mpChunkDeps_three_build_three_core_min.zt) return e.BYTE;
    if (n === _mpChunkDeps_three_build_three_core_min.Ct) return e.SHORT;
    if (n === _mpChunkDeps_three_build_three_core_min.It) return e.UNSIGNED_SHORT;
    if (n === _mpChunkDeps_three_build_three_core_min.Bt) return e.INT;
    if (n === _mpChunkDeps_three_build_three_core_min.kt) return e.UNSIGNED_INT;
    if (n === _mpChunkDeps_three_build_three_core_min.Rt) return e.FLOAT;
    if (n === _mpChunkDeps_three_build_three_core_min.Et) return e.HALF_FLOAT;
    if (n === _mpChunkDeps_three_build_three_core_min.Lt) return e.ALPHA;
    if (n === _mpChunkDeps_three_build_three_core_min.Vt) return e.RGB;
    if (n === _mpChunkDeps_three_build_three_core_min.Wt) return e.RGBA;
    if (n === _mpChunkDeps_three_build_three_core_min.jt) return e.LUMINANCE;
    if (n === _mpChunkDeps_three_build_three_core_min.Ut) return e.LUMINANCE_ALPHA;
    if (n === _mpChunkDeps_three_build_three_core_min.Dt) return e.DEPTH_COMPONENT;
    if (n === _mpChunkDeps_three_build_three_core_min.Ht) return e.DEPTH_STENCIL;
    if (n === _mpChunkDeps_three_build_three_core_min.qt) return e.RED;
    if (n === _mpChunkDeps_three_build_three_core_min.Jt) return e.RED_INTEGER;
    if (n === _mpChunkDeps_three_build_three_core_min.Xt) return e.RG;
    if (n === _mpChunkDeps_three_build_three_core_min.Yt) return e.RG_INTEGER;
    if (n === _mpChunkDeps_three_build_three_core_min.Gt) return e.RGBA_INTEGER;
    if (n === _mpChunkDeps_three_build_three_core_min.$t || n === _mpChunkDeps_three_build_three_core_min.Qt || n === _mpChunkDeps_three_build_three_core_min.Kt || n === _mpChunkDeps_three_build_three_core_min.te) if (a === _mpChunkDeps_three_build_three_core_min.Ke) {
      if (r = t.get("WEBGL_compressed_texture_s3tc_srgb"), null === r) return null;
      if (n === _mpChunkDeps_three_build_three_core_min.$t) return r.COMPRESSED_SRGB_S3TC_DXT1_EXT;
      if (n === _mpChunkDeps_three_build_three_core_min.Qt) return r.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT;
      if (n === _mpChunkDeps_three_build_three_core_min.Kt) return r.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT;
      if (n === _mpChunkDeps_three_build_three_core_min.te) return r.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT;
    } else {
      if (r = t.get("WEBGL_compressed_texture_s3tc"), null === r) return null;
      if (n === _mpChunkDeps_three_build_three_core_min.$t) return r.COMPRESSED_RGB_S3TC_DXT1_EXT;
      if (n === _mpChunkDeps_three_build_three_core_min.Qt) return r.COMPRESSED_RGBA_S3TC_DXT1_EXT;
      if (n === _mpChunkDeps_three_build_three_core_min.Kt) return r.COMPRESSED_RGBA_S3TC_DXT3_EXT;
      if (n === _mpChunkDeps_three_build_three_core_min.te) return r.COMPRESSED_RGBA_S3TC_DXT5_EXT;
    }
    if (n === _mpChunkDeps_three_build_three_core_min.ee || n === _mpChunkDeps_three_build_three_core_min.se || n === _mpChunkDeps_three_build_three_core_min.ie || n === _mpChunkDeps_three_build_three_core_min.re) {
      if (r = t.get("WEBGL_compressed_texture_pvrtc"), null === r) return null;
      if (n === _mpChunkDeps_three_build_three_core_min.ee) return r.COMPRESSED_RGB_PVRTC_4BPPV1_IMG;
      if (n === _mpChunkDeps_three_build_three_core_min.se) return r.COMPRESSED_RGB_PVRTC_2BPPV1_IMG;
      if (n === _mpChunkDeps_three_build_three_core_min.ie) return r.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG;
      if (n === _mpChunkDeps_three_build_three_core_min.re) return r.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG;
    }
    if (n === _mpChunkDeps_three_build_three_core_min.ne || n === _mpChunkDeps_three_build_three_core_min.oe || n === _mpChunkDeps_three_build_three_core_min.ae) {
      if (r = t.get("WEBGL_compressed_texture_etc"), null === r) return null;
      if (n === _mpChunkDeps_three_build_three_core_min.ne || n === _mpChunkDeps_three_build_three_core_min.oe) return a === _mpChunkDeps_three_build_three_core_min.Ke ? r.COMPRESSED_SRGB8_ETC2 : r.COMPRESSED_RGB8_ETC2;
      if (n === _mpChunkDeps_three_build_three_core_min.ae) return a === _mpChunkDeps_three_build_three_core_min.Ke ? r.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC : r.COMPRESSED_RGBA8_ETC2_EAC;
    }
    if (n === _mpChunkDeps_three_build_three_core_min.he || n === _mpChunkDeps_three_build_three_core_min.le || n === _mpChunkDeps_three_build_three_core_min.ce || n === _mpChunkDeps_three_build_three_core_min.ue || n === _mpChunkDeps_three_build_three_core_min.de || n === _mpChunkDeps_three_build_three_core_min.pe || n === _mpChunkDeps_three_build_three_core_min.me || n === _mpChunkDeps_three_build_three_core_min.ye || n === _mpChunkDeps_three_build_three_core_min.fe || n === _mpChunkDeps_three_build_three_core_min.ge || n === _mpChunkDeps_three_build_three_core_min.xe || n === _mpChunkDeps_three_build_three_core_min.be || n === _mpChunkDeps_three_build_three_core_min.ve || n === _mpChunkDeps_three_build_three_core_min.we) {
      if (r = t.get("WEBGL_compressed_texture_astc"), null === r) return null;
      if (n === _mpChunkDeps_three_build_three_core_min.he) return a === _mpChunkDeps_three_build_three_core_min.Ke ? r.COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR : r.COMPRESSED_RGBA_ASTC_4x4_KHR;
      if (n === _mpChunkDeps_three_build_three_core_min.le) return a === _mpChunkDeps_three_build_three_core_min.Ke ? r.COMPRESSED_SRGB8_ALPHA8_ASTC_5x4_KHR : r.COMPRESSED_RGBA_ASTC_5x4_KHR;
      if (n === _mpChunkDeps_three_build_three_core_min.ce) return a === _mpChunkDeps_three_build_three_core_min.Ke ? r.COMPRESSED_SRGB8_ALPHA8_ASTC_5x5_KHR : r.COMPRESSED_RGBA_ASTC_5x5_KHR;
      if (n === _mpChunkDeps_three_build_three_core_min.ue) return a === _mpChunkDeps_three_build_three_core_min.Ke ? r.COMPRESSED_SRGB8_ALPHA8_ASTC_6x5_KHR : r.COMPRESSED_RGBA_ASTC_6x5_KHR;
      if (n === _mpChunkDeps_three_build_three_core_min.de) return a === _mpChunkDeps_three_build_three_core_min.Ke ? r.COMPRESSED_SRGB8_ALPHA8_ASTC_6x6_KHR : r.COMPRESSED_RGBA_ASTC_6x6_KHR;
      if (n === _mpChunkDeps_three_build_three_core_min.pe) return a === _mpChunkDeps_three_build_three_core_min.Ke ? r.COMPRESSED_SRGB8_ALPHA8_ASTC_8x5_KHR : r.COMPRESSED_RGBA_ASTC_8x5_KHR;
      if (n === _mpChunkDeps_three_build_three_core_min.me) return a === _mpChunkDeps_three_build_three_core_min.Ke ? r.COMPRESSED_SRGB8_ALPHA8_ASTC_8x6_KHR : r.COMPRESSED_RGBA_ASTC_8x6_KHR;
      if (n === _mpChunkDeps_three_build_three_core_min.ye) return a === _mpChunkDeps_three_build_three_core_min.Ke ? r.COMPRESSED_SRGB8_ALPHA8_ASTC_8x8_KHR : r.COMPRESSED_RGBA_ASTC_8x8_KHR;
      if (n === _mpChunkDeps_three_build_three_core_min.fe) return a === _mpChunkDeps_three_build_three_core_min.Ke ? r.COMPRESSED_SRGB8_ALPHA8_ASTC_10x5_KHR : r.COMPRESSED_RGBA_ASTC_10x5_KHR;
      if (n === _mpChunkDeps_three_build_three_core_min.ge) return a === _mpChunkDeps_three_build_three_core_min.Ke ? r.COMPRESSED_SRGB8_ALPHA8_ASTC_10x6_KHR : r.COMPRESSED_RGBA_ASTC_10x6_KHR;
      if (n === _mpChunkDeps_three_build_three_core_min.xe) return a === _mpChunkDeps_three_build_three_core_min.Ke ? r.COMPRESSED_SRGB8_ALPHA8_ASTC_10x8_KHR : r.COMPRESSED_RGBA_ASTC_10x8_KHR;
      if (n === _mpChunkDeps_three_build_three_core_min.be) return a === _mpChunkDeps_three_build_three_core_min.Ke ? r.COMPRESSED_SRGB8_ALPHA8_ASTC_10x10_KHR : r.COMPRESSED_RGBA_ASTC_10x10_KHR;
      if (n === _mpChunkDeps_three_build_three_core_min.ve) return a === _mpChunkDeps_three_build_three_core_min.Ke ? r.COMPRESSED_SRGB8_ALPHA8_ASTC_12x10_KHR : r.COMPRESSED_RGBA_ASTC_12x10_KHR;
      if (n === _mpChunkDeps_three_build_three_core_min.we) return a === _mpChunkDeps_three_build_three_core_min.Ke ? r.COMPRESSED_SRGB8_ALPHA8_ASTC_12x12_KHR : r.COMPRESSED_RGBA_ASTC_12x12_KHR;
    }
    if (n === _mpChunkDeps_three_build_three_core_min.Me || n === _mpChunkDeps_three_build_three_core_min.Se || n === _mpChunkDeps_three_build_three_core_min._e) {
      if (r = t.get("EXT_texture_compression_bptc"), null === r) return null;
      if (n === _mpChunkDeps_three_build_three_core_min.Me) return a === _mpChunkDeps_three_build_three_core_min.Ke ? r.COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT : r.COMPRESSED_RGBA_BPTC_UNORM_EXT;
      if (n === _mpChunkDeps_three_build_three_core_min.Se) return r.COMPRESSED_RGB_BPTC_SIGNED_FLOAT_EXT;
      if (n === _mpChunkDeps_three_build_three_core_min._e) return r.COMPRESSED_RGB_BPTC_UNSIGNED_FLOAT_EXT;
    }
    if (n === _mpChunkDeps_three_build_three_core_min.Ae || n === _mpChunkDeps_three_build_three_core_min.Te || n === _mpChunkDeps_three_build_three_core_min.ze || n === _mpChunkDeps_three_build_three_core_min.Ce) {
      if (r = t.get("EXT_texture_compression_rgtc"), null === r) return null;
      if (n === _mpChunkDeps_three_build_three_core_min.Me) return r.COMPRESSED_RED_RGTC1_EXT;
      if (n === _mpChunkDeps_three_build_three_core_min.Te) return r.COMPRESSED_SIGNED_RED_RGTC1_EXT;
      if (n === _mpChunkDeps_three_build_three_core_min.ze) return r.COMPRESSED_RED_GREEN_RGTC2_EXT;
      if (n === _mpChunkDeps_three_build_three_core_min.Ce) return r.COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT;
    }
    return n === _mpChunkDeps_three_build_three_core_min.Ft ? e.UNSIGNED_INT_24_8 : void 0 !== e[n] ? e[n] : null;
  } };
}
const ra = { type: "move" };
class aa {
  constructor() {
    this._targetRay = null, this._grip = null, this._hand = null;
  }
  getHandSpace() {
    return null === this._hand && (this._hand = new _mpChunkDeps_three_build_three_core_min.Ha(), this._hand.matrixAutoUpdate = false, this._hand.visible = false, this._hand.joints = {}, this._hand.inputState = { pinching: false }), this._hand;
  }
  getTargetRaySpace() {
    return null === this._targetRay && (this._targetRay = new _mpChunkDeps_three_build_three_core_min.Ha(), this._targetRay.matrixAutoUpdate = false, this._targetRay.visible = false, this._targetRay.hasLinearVelocity = false, this._targetRay.linearVelocity = new _mpChunkDeps_three_build_three_core_min.Ii(), this._targetRay.hasAngularVelocity = false, this._targetRay.angularVelocity = new _mpChunkDeps_three_build_three_core_min.Ii()), this._targetRay;
  }
  getGripSpace() {
    return null === this._grip && (this._grip = new _mpChunkDeps_three_build_three_core_min.Ha(), this._grip.matrixAutoUpdate = false, this._grip.visible = false, this._grip.hasLinearVelocity = false, this._grip.linearVelocity = new _mpChunkDeps_three_build_three_core_min.Ii(), this._grip.hasAngularVelocity = false, this._grip.angularVelocity = new _mpChunkDeps_three_build_three_core_min.Ii()), this._grip;
  }
  dispatchEvent(e) {
    return null !== this._targetRay && this._targetRay.dispatchEvent(e), null !== this._grip && this._grip.dispatchEvent(e), null !== this._hand && this._hand.dispatchEvent(e), this;
  }
  connect(e) {
    if (e && e.hand) {
      const t = this._hand;
      if (t) for (const n of e.hand.values()) this._getHandJoint(t, n);
    }
    return this.dispatchEvent({ type: "connected", data: e }), this;
  }
  disconnect(e) {
    return this.dispatchEvent({ type: "disconnected", data: e }), null !== this._targetRay && (this._targetRay.visible = false), null !== this._grip && (this._grip.visible = false), null !== this._hand && (this._hand.visible = false), this;
  }
  update(e, t, n) {
    let i = null, r = null, a = null;
    const o = this._targetRay, s = this._grip, l = this._hand;
    if (e && "visible-blurred" !== t.session.visibilityState) {
      if (l && e.hand) {
        a = true;
        for (const i3 of e.hand.values()) {
          const e2 = t.getJointPose(i3, n), r3 = this._getHandJoint(l, i3);
          null !== e2 && (r3.matrix.fromArray(e2.transform.matrix), r3.matrix.decompose(r3.position, r3.rotation, r3.scale), r3.matrixWorldNeedsUpdate = true, r3.jointRadius = e2.radius), r3.visible = null !== e2;
        }
        const i2 = l.joints["index-finger-tip"], r2 = l.joints["thumb-tip"], o2 = i2.position.distanceTo(r2.position), s2 = 0.02, c = 5e-3;
        l.inputState.pinching && o2 > s2 + c ? (l.inputState.pinching = false, this.dispatchEvent({ type: "pinchend", handedness: e.handedness, target: this })) : !l.inputState.pinching && o2 <= s2 - c && (l.inputState.pinching = true, this.dispatchEvent({ type: "pinchstart", handedness: e.handedness, target: this }));
      } else null !== s && e.gripSpace && (r = t.getPose(e.gripSpace, n), null !== r && (s.matrix.fromArray(r.transform.matrix), s.matrix.decompose(s.position, s.rotation, s.scale), s.matrixWorldNeedsUpdate = true, r.linearVelocity ? (s.hasLinearVelocity = true, s.linearVelocity.copy(r.linearVelocity)) : s.hasLinearVelocity = false, r.angularVelocity ? (s.hasAngularVelocity = true, s.angularVelocity.copy(r.angularVelocity)) : s.hasAngularVelocity = false));
      null !== o && (i = t.getPose(e.targetRaySpace, n), null === i && null !== r && (i = r), null !== i && (o.matrix.fromArray(i.transform.matrix), o.matrix.decompose(o.position, o.rotation, o.scale), o.matrixWorldNeedsUpdate = true, i.linearVelocity ? (o.hasLinearVelocity = true, o.linearVelocity.copy(i.linearVelocity)) : o.hasLinearVelocity = false, i.angularVelocity ? (o.hasAngularVelocity = true, o.angularVelocity.copy(i.angularVelocity)) : o.hasAngularVelocity = false, this.dispatchEvent(ra)));
    }
    return null !== o && (o.visible = null !== i), null !== s && (s.visible = null !== r), null !== l && (l.visible = null !== a), this;
  }
  _getHandJoint(e, t) {
    if (void 0 === e.joints[t.jointName]) {
      const n = new _mpChunkDeps_three_build_three_core_min.Ha();
      n.matrixAutoUpdate = false, n.visible = false, e.joints[t.jointName] = n, e.add(n);
    }
    return e.joints[t.jointName];
  }
}
class oa {
  constructor() {
    this.texture = null, this.mesh = null, this.depthNear = 0, this.depthFar = 0;
  }
  init(e, t, n) {
    if (null === this.texture) {
      const i = new _mpChunkDeps_three_build_three_core_min.vi();
      e.properties.get(i).__webglTexture = t.texture, t.depthNear === n.depthNear && t.depthFar === n.depthFar || (this.depthNear = t.depthNear, this.depthFar = t.depthFar), this.texture = i;
    }
  }
  getMesh(e) {
    if (null !== this.texture && null === this.mesh) {
      const t = e.cameras[0].viewport, n = new _mpChunkDeps_three_build_three_core_min.Jn({ vertexShader: "\nvoid main() {\n\n	gl_Position = vec4( position, 1.0 );\n\n}", fragmentShader: "\nuniform sampler2DArray depthColor;\nuniform float depthWidth;\nuniform float depthHeight;\n\nvoid main() {\n\n	vec2 coord = vec2( gl_FragCoord.x / depthWidth, gl_FragCoord.y / depthHeight );\n\n	if ( coord.x >= 1.0 ) {\n\n		gl_FragDepth = texture( depthColor, vec3( coord.x - 1.0, coord.y, 1 ) ).r;\n\n	} else {\n\n		gl_FragDepth = texture( depthColor, vec3( coord.x, coord.y, 0 ) ).r;\n\n	}\n\n}", uniforms: { depthColor: { value: this.texture }, depthWidth: { value: t.z }, depthHeight: { value: t.w } } });
      this.mesh = new _mpChunkDeps_three_build_three_core_min.Vn(new _mpChunkDeps_three_build_three_core_min.ul(20, 20), n);
    }
    return this.mesh;
  }
  reset() {
    this.texture = null, this.mesh = null;
  }
  getDepthTexture() {
    return this.texture;
  }
}
class sa extends _mpChunkDeps_three_build_three_core_min.Ns {
  constructor(e, t) {
    super();
    const i = this;
    let a = null, o = 1, s = null, l = "local-floor", c = 1, d = null, u = null, f = null, p = null, m = null, h = null;
    const _ = new oa(), g = t.getContextAttributes();
    let v = null, S = null;
    const M = [], x = [], R = new _mpChunkDeps_three_build_three_core_min.Zs();
    let A = null;
    const b = new _mpChunkDeps_three_build_three_core_min.$n();
    b.viewport = new _mpChunkDeps_three_build_three_core_min.wi();
    const C = new _mpChunkDeps_three_build_three_core_min.$n();
    C.viewport = new _mpChunkDeps_three_build_three_core_min.wi();
    const L = [b, C], U = new _mpChunkDeps_three_build_three_core_min.Zc();
    let w = null, D = null;
    function y(e2) {
      const t2 = x.indexOf(e2.inputSource);
      if (-1 === t2) return;
      const n = M[t2];
      void 0 !== n && (n.update(e2.inputSource, e2.frame, d || s), n.dispatchEvent({ type: e2.type, data: e2.inputSource }));
    }
    function N() {
      a.removeEventListener("select", y), a.removeEventListener("selectstart", y), a.removeEventListener("selectend", y), a.removeEventListener("squeeze", y), a.removeEventListener("squeezestart", y), a.removeEventListener("squeezeend", y), a.removeEventListener("end", N), a.removeEventListener("inputsourceschange", O);
      for (let e2 = 0; e2 < M.length; e2++) {
        const t2 = x[e2];
        null !== t2 && (x[e2] = null, M[e2].disconnect(t2));
      }
      w = null, D = null, _.reset(), e.setRenderTarget(v), m = null, p = null, f = null, a = null, S = null, V.stop(), i.isPresenting = false, e.setPixelRatio(A), e.setSize(R.width, R.height, false), i.dispatchEvent({ type: "sessionend" });
    }
    function O(e2) {
      for (let t2 = 0; t2 < e2.removed.length; t2++) {
        const n = e2.removed[t2], i2 = x.indexOf(n);
        i2 >= 0 && (x[i2] = null, M[i2].disconnect(n));
      }
      for (let t2 = 0; t2 < e2.added.length; t2++) {
        const n = e2.added[t2];
        let i2 = x.indexOf(n);
        if (-1 === i2) {
          for (let e3 = 0; e3 < M.length; e3++) {
            if (e3 >= x.length) {
              x.push(n), i2 = e3;
              break;
            }
            if (null === x[e3]) {
              x[e3] = n, i2 = e3;
              break;
            }
          }
          if (-1 === i2) break;
        }
        const r = M[i2];
        r && r.connect(n);
      }
    }
    this.cameraAutoUpdate = true, this.enabled = false, this.isPresenting = false, this.getController = function(e2) {
      let t2 = M[e2];
      return void 0 === t2 && (t2 = new aa(), M[e2] = t2), t2.getTargetRaySpace();
    }, this.getControllerGrip = function(e2) {
      let t2 = M[e2];
      return void 0 === t2 && (t2 = new aa(), M[e2] = t2), t2.getGripSpace();
    }, this.getHand = function(e2) {
      let t2 = M[e2];
      return void 0 === t2 && (t2 = new aa(), M[e2] = t2), t2.getHandSpace();
    }, this.setFramebufferScaleFactor = function(e2) {
      o = e2, true === i.isPresenting && console.warn("THREE.WebXRManager: Cannot change framebuffer scale while presenting.");
    }, this.setReferenceSpaceType = function(e2) {
      l = e2, true === i.isPresenting && console.warn("THREE.WebXRManager: Cannot change reference space type while presenting.");
    }, this.getReferenceSpace = function() {
      return d || s;
    }, this.setReferenceSpace = function(e2) {
      d = e2;
    }, this.getBaseLayer = function() {
      return null !== p ? p : m;
    }, this.getBinding = function() {
      return f;
    }, this.getFrame = function() {
      return h;
    }, this.getSession = function() {
      return a;
    }, this.setSession = async function(n) {
      if (a = n, null !== a) {
        v = e.getRenderTarget(), a.addEventListener("select", y), a.addEventListener("selectstart", y), a.addEventListener("selectend", y), a.addEventListener("squeeze", y), a.addEventListener("squeezestart", y), a.addEventListener("squeezeend", y), a.addEventListener("end", N), a.addEventListener("inputsourceschange", O), true !== g.xrCompatible && await t.makeXRCompatible(), A = e.getPixelRatio(), e.getSize(R);
        if (void 0 !== a.enabledFeatures && a.enabledFeatures.includes("layers")) {
          let n2 = null, i2 = null, r = null;
          g.depth && (r = g.stencil ? t.DEPTH24_STENCIL8 : t.DEPTH_COMPONENT24, n2 = g.stencil ? _mpChunkDeps_three_build_three_core_min.Ht : _mpChunkDeps_three_build_three_core_min.Dt, i2 = g.stencil ? _mpChunkDeps_three_build_three_core_min.Ft : _mpChunkDeps_three_build_three_core_min.kt);
          const s2 = { colorFormat: t.RGBA8, depthFormat: r, scaleFactor: o };
          f = new THREEGlobals["XRWebGLBinding"](a, t), p = f.createProjectionLayer(s2), a.updateRenderState({ layers: [p] }), e.setPixelRatio(1), e.setSize(p.textureWidth, p.textureHeight, false), S = new _mpChunkDeps_three_build_three_core_min.Si(p.textureWidth, p.textureHeight, { format: _mpChunkDeps_three_build_three_core_min.Wt, type: _mpChunkDeps_three_build_three_core_min.Tt, depthTexture: new _mpChunkDeps_three_build_three_core_min.$a(p.textureWidth, p.textureHeight, i2, void 0, void 0, void 0, void 0, void 0, void 0, n2), stencilBuffer: g.stencil, colorSpace: e.outputColorSpace, samples: g.antialias ? 4 : 0, resolveDepthBuffer: false === p.ignoreDepthValues });
        } else {
          const n2 = { antialias: g.antialias, alpha: true, depth: g.depth, stencil: g.stencil, framebufferScaleFactor: o };
          m = new THREEGlobals["XRWebGLLayer"](a, t, n2), a.updateRenderState({ baseLayer: m }), e.setPixelRatio(1), e.setSize(m.framebufferWidth, m.framebufferHeight, false), S = new _mpChunkDeps_three_build_three_core_min.Si(m.framebufferWidth, m.framebufferHeight, { format: _mpChunkDeps_three_build_three_core_min.Wt, type: _mpChunkDeps_three_build_three_core_min.Tt, colorSpace: e.outputColorSpace, stencilBuffer: g.stencil });
        }
        S.isXRRenderTarget = true, this.setFoveation(c), d = null, s = await a.requestReferenceSpace(l), V.setContext(a), V.start(), i.isPresenting = true, i.dispatchEvent({ type: "sessionstart" });
      }
    }, this.getEnvironmentBlendMode = function() {
      if (null !== a) return a.environmentBlendMode;
    }, this.getDepthTexture = function() {
      return _.getDepthTexture();
    };
    const F = new _mpChunkDeps_three_build_three_core_min.Ii(), B = new _mpChunkDeps_three_build_three_core_min.Ii();
    function H(e2, t2) {
      null === t2 ? e2.matrixWorld.copy(e2.matrix) : e2.matrixWorld.multiplyMatrices(t2.matrixWorld, e2.matrix), e2.matrixWorldInverse.copy(e2.matrixWorld).invert();
    }
    this.updateCamera = function(e2) {
      if (null === a) return;
      let t2 = e2.near, n = e2.far;
      null !== _.texture && (_.depthNear > 0 && (t2 = _.depthNear), _.depthFar > 0 && (n = _.depthFar)), U.near = C.near = b.near = t2, U.far = C.far = b.far = n, w === U.near && D === U.far || (a.updateRenderState({ depthNear: U.near, depthFar: U.far }), w = U.near, D = U.far), b.layers.mask = 2 | e2.layers.mask, C.layers.mask = 4 | e2.layers.mask, U.layers.mask = b.layers.mask | C.layers.mask;
      const i2 = e2.parent, r = U.cameras;
      H(U, i2);
      for (let e3 = 0; e3 < r.length; e3++) H(r[e3], i2);
      2 === r.length ? function(e3, t3, n2) {
        F.setFromMatrixPosition(t3.matrixWorld), B.setFromMatrixPosition(n2.matrixWorld);
        const i3 = F.distanceTo(B), r2 = t3.projectionMatrix.elements, a2 = n2.projectionMatrix.elements, o2 = r2[14] / (r2[10] - 1), s2 = r2[14] / (r2[10] + 1), l2 = (r2[9] + 1) / r2[5], c2 = (r2[9] - 1) / r2[5], d2 = (r2[8] - 1) / r2[0], u2 = (a2[8] + 1) / a2[0], f2 = o2 * d2, p2 = o2 * u2, m2 = i3 / (-d2 + u2), h2 = m2 * -d2;
        if (t3.matrixWorld.decompose(e3.position, e3.quaternion, e3.scale), e3.translateX(h2), e3.translateZ(m2), e3.matrixWorld.compose(e3.position, e3.quaternion, e3.scale), e3.matrixWorldInverse.copy(e3.matrixWorld).invert(), -1 === r2[10]) e3.projectionMatrix.copy(t3.projectionMatrix), e3.projectionMatrixInverse.copy(t3.projectionMatrixInverse);
        else {
          const t4 = o2 + m2, n3 = s2 + m2, r3 = f2 - h2, a3 = p2 + (i3 - h2), d3 = l2 * s2 / n3 * t4, u3 = c2 * s2 / n3 * t4;
          e3.projectionMatrix.makePerspective(r3, a3, d3, u3, t4, n3), e3.projectionMatrixInverse.copy(e3.projectionMatrix).invert();
        }
      }(U, b, C) : U.projectionMatrix.copy(b.projectionMatrix), function(e3, t3, n2) {
        null === n2 ? e3.matrix.copy(t3.matrixWorld) : (e3.matrix.copy(n2.matrixWorld), e3.matrix.invert(), e3.matrix.multiply(t3.matrixWorld));
        e3.matrix.decompose(e3.position, e3.quaternion, e3.scale), e3.updateMatrixWorld(true), e3.projectionMatrix.copy(t3.projectionMatrix), e3.projectionMatrixInverse.copy(t3.projectionMatrixInverse), e3.isPerspectiveCamera && (e3.fov = 2 * _mpChunkDeps_three_build_three_core_min.js * Math.atan(1 / e3.projectionMatrix.elements[5]), e3.zoom = 1);
      }(e2, U, i2);
    }, this.getCamera = function() {
      return U;
    }, this.getFoveation = function() {
      if (null !== p || null !== m) return c;
    }, this.setFoveation = function(e2) {
      c = e2, null !== p && (p.fixedFoveation = e2), null !== m && void 0 !== m.fixedFoveation && (m.fixedFoveation = e2);
    }, this.hasDepthSensing = function() {
      return null !== _.texture;
    }, this.getDepthSensingMesh = function() {
      return _.getMesh(U);
    };
    let G = null;
    const V = new Pn();
    V.setAnimationLoop(function(t2, n) {
      if (u = n.getViewerPose(d || s), h = n, null !== u) {
        const t3 = u.views;
        null !== m && (e.setRenderTargetFramebuffer(S, m.framebuffer), e.setRenderTarget(S));
        let n2 = false;
        t3.length !== U.cameras.length && (U.cameras.length = 0, n2 = true);
        for (let i3 = 0; i3 < t3.length; i3++) {
          const r = t3[i3];
          let a2 = null;
          if (null !== m) a2 = m.getViewport(r);
          else {
            const t4 = f.getViewSubImage(p, r);
            a2 = t4.viewport, 0 === i3 && (e.setRenderTargetTextures(S, t4.colorTexture, p.ignoreDepthValues ? void 0 : t4.depthStencilTexture), e.setRenderTarget(S));
          }
          let o2 = L[i3];
          void 0 === o2 && (o2 = new _mpChunkDeps_three_build_three_core_min.$n(), o2.layers.enable(i3), o2.viewport = new _mpChunkDeps_three_build_three_core_min.wi(), L[i3] = o2), o2.matrix.fromArray(r.transform.matrix), o2.matrix.decompose(o2.position, o2.quaternion, o2.scale), o2.projectionMatrix.fromArray(r.projectionMatrix), o2.projectionMatrixInverse.copy(o2.projectionMatrix).invert(), o2.viewport.set(a2.x, a2.y, a2.width, a2.height), 0 === i3 && (U.matrix.copy(o2.matrix), U.matrix.decompose(U.position, U.quaternion, U.scale)), true === n2 && U.cameras.push(o2);
        }
        const i2 = a.enabledFeatures;
        if (i2 && i2.includes("depth-sensing")) {
          const n3 = f.getDepthInformation(t3[0]);
          n3 && n3.isValid && n3.texture && _.init(e, n3, a.renderState);
        }
      }
      for (let e2 = 0; e2 < M.length; e2++) {
        const t3 = x[e2], i2 = M[e2];
        null !== t3 && void 0 !== i2 && i2.update(t3, n, d || s);
      }
      G && G(t2, n), n.detectedPlanes && i.dispatchEvent({ type: "planesdetected", data: n }), h = null;
    }), this.setAnimationLoop = function(e2) {
      G = e2;
    }, this.dispose = function() {
    };
  }
}
const la = new _mpChunkDeps_three_build_three_core_min.yr(), ca = new _mpChunkDeps_three_build_three_core_min.nr();
function da(e, t) {
  function n(e2, t2) {
    true === e2.matrixAutoUpdate && e2.updateMatrix(), t2.value.copy(e2.matrix);
  }
  function i(e2, i2) {
    e2.opacity.value = i2.opacity, i2.color && e2.diffuse.value.copy(i2.color), i2.emissive && e2.emissive.value.copy(i2.emissive).multiplyScalar(i2.emissiveIntensity), i2.map && (e2.map.value = i2.map, n(i2.map, e2.mapTransform)), i2.alphaMap && (e2.alphaMap.value = i2.alphaMap, n(i2.alphaMap, e2.alphaMapTransform)), i2.bumpMap && (e2.bumpMap.value = i2.bumpMap, n(i2.bumpMap, e2.bumpMapTransform), e2.bumpScale.value = i2.bumpScale, i2.side === _mpChunkDeps_three_build_three_core_min.d && (e2.bumpScale.value *= -1)), i2.normalMap && (e2.normalMap.value = i2.normalMap, n(i2.normalMap, e2.normalMapTransform), e2.normalScale.value.copy(i2.normalScale), i2.side === _mpChunkDeps_three_build_three_core_min.d && e2.normalScale.value.negate()), i2.displacementMap && (e2.displacementMap.value = i2.displacementMap, n(i2.displacementMap, e2.displacementMapTransform), e2.displacementScale.value = i2.displacementScale, e2.displacementBias.value = i2.displacementBias), i2.emissiveMap && (e2.emissiveMap.value = i2.emissiveMap, n(i2.emissiveMap, e2.emissiveMapTransform)), i2.specularMap && (e2.specularMap.value = i2.specularMap, n(i2.specularMap, e2.specularMapTransform)), i2.alphaTest > 0 && (e2.alphaTest.value = i2.alphaTest);
    const r = t.get(i2), a = r.envMap, o = r.envMapRotation;
    a && (e2.envMap.value = a, la.copy(o), la.x *= -1, la.y *= -1, la.z *= -1, a.isCubeTexture && false === a.isRenderTargetTexture && (la.y *= -1, la.z *= -1), e2.envMapRotation.value.setFromMatrix4(ca.makeRotationFromEuler(la)), e2.flipEnvMap.value = a.isCubeTexture && false === a.isRenderTargetTexture ? -1 : 1, e2.reflectivity.value = i2.reflectivity, e2.ior.value = i2.ior, e2.refractionRatio.value = i2.refractionRatio), i2.lightMap && (e2.lightMap.value = i2.lightMap, e2.lightMapIntensity.value = i2.lightMapIntensity, n(i2.lightMap, e2.lightMapTransform)), i2.aoMap && (e2.aoMap.value = i2.aoMap, e2.aoMapIntensity.value = i2.aoMapIntensity, n(i2.aoMap, e2.aoMapTransform));
  }
  return { refreshFogUniforms: function(t2, n2) {
    n2.color.getRGB(t2.fogColor.value, _mpChunkDeps_three_build_three_core_min.Hn(e)), n2.isFog ? (t2.fogNear.value = n2.near, t2.fogFar.value = n2.far) : n2.isFogExp2 && (t2.fogDensity.value = n2.density);
  }, refreshMaterialUniforms: function(e2, r, a, o, s) {
    r.isMeshBasicMaterial || r.isMeshLambertMaterial ? i(e2, r) : r.isMeshToonMaterial ? (i(e2, r), function(e3, t2) {
      t2.gradientMap && (e3.gradientMap.value = t2.gradientMap);
    }(e2, r)) : r.isMeshPhongMaterial ? (i(e2, r), function(e3, t2) {
      e3.specular.value.copy(t2.specular), e3.shininess.value = Math.max(t2.shininess, 1e-4);
    }(e2, r)) : r.isMeshStandardMaterial ? (i(e2, r), function(e3, t2) {
      e3.metalness.value = t2.metalness, t2.metalnessMap && (e3.metalnessMap.value = t2.metalnessMap, n(t2.metalnessMap, e3.metalnessMapTransform));
      e3.roughness.value = t2.roughness, t2.roughnessMap && (e3.roughnessMap.value = t2.roughnessMap, n(t2.roughnessMap, e3.roughnessMapTransform));
      t2.envMap && (e3.envMapIntensity.value = t2.envMapIntensity);
    }(e2, r), r.isMeshPhysicalMaterial && function(e3, t2, i2) {
      e3.ior.value = t2.ior, t2.sheen > 0 && (e3.sheenColor.value.copy(t2.sheenColor).multiplyScalar(t2.sheen), e3.sheenRoughness.value = t2.sheenRoughness, t2.sheenColorMap && (e3.sheenColorMap.value = t2.sheenColorMap, n(t2.sheenColorMap, e3.sheenColorMapTransform)), t2.sheenRoughnessMap && (e3.sheenRoughnessMap.value = t2.sheenRoughnessMap, n(t2.sheenRoughnessMap, e3.sheenRoughnessMapTransform)));
      t2.clearcoat > 0 && (e3.clearcoat.value = t2.clearcoat, e3.clearcoatRoughness.value = t2.clearcoatRoughness, t2.clearcoatMap && (e3.clearcoatMap.value = t2.clearcoatMap, n(t2.clearcoatMap, e3.clearcoatMapTransform)), t2.clearcoatRoughnessMap && (e3.clearcoatRoughnessMap.value = t2.clearcoatRoughnessMap, n(t2.clearcoatRoughnessMap, e3.clearcoatRoughnessMapTransform)), t2.clearcoatNormalMap && (e3.clearcoatNormalMap.value = t2.clearcoatNormalMap, n(t2.clearcoatNormalMap, e3.clearcoatNormalMapTransform), e3.clearcoatNormalScale.value.copy(t2.clearcoatNormalScale), t2.side === _mpChunkDeps_three_build_three_core_min.d && e3.clearcoatNormalScale.value.negate()));
      t2.dispersion > 0 && (e3.dispersion.value = t2.dispersion);
      t2.iridescence > 0 && (e3.iridescence.value = t2.iridescence, e3.iridescenceIOR.value = t2.iridescenceIOR, e3.iridescenceThicknessMinimum.value = t2.iridescenceThicknessRange[0], e3.iridescenceThicknessMaximum.value = t2.iridescenceThicknessRange[1], t2.iridescenceMap && (e3.iridescenceMap.value = t2.iridescenceMap, n(t2.iridescenceMap, e3.iridescenceMapTransform)), t2.iridescenceThicknessMap && (e3.iridescenceThicknessMap.value = t2.iridescenceThicknessMap, n(t2.iridescenceThicknessMap, e3.iridescenceThicknessMapTransform)));
      t2.transmission > 0 && (e3.transmission.value = t2.transmission, e3.transmissionSamplerMap.value = i2.texture, e3.transmissionSamplerSize.value.set(i2.width, i2.height), t2.transmissionMap && (e3.transmissionMap.value = t2.transmissionMap, n(t2.transmissionMap, e3.transmissionMapTransform)), e3.thickness.value = t2.thickness, t2.thicknessMap && (e3.thicknessMap.value = t2.thicknessMap, n(t2.thicknessMap, e3.thicknessMapTransform)), e3.attenuationDistance.value = t2.attenuationDistance, e3.attenuationColor.value.copy(t2.attenuationColor));
      t2.anisotropy > 0 && (e3.anisotropyVector.value.set(t2.anisotropy * Math.cos(t2.anisotropyRotation), t2.anisotropy * Math.sin(t2.anisotropyRotation)), t2.anisotropyMap && (e3.anisotropyMap.value = t2.anisotropyMap, n(t2.anisotropyMap, e3.anisotropyMapTransform)));
      e3.specularIntensity.value = t2.specularIntensity, e3.specularColor.value.copy(t2.specularColor), t2.specularColorMap && (e3.specularColorMap.value = t2.specularColorMap, n(t2.specularColorMap, e3.specularColorMapTransform));
      t2.specularIntensityMap && (e3.specularIntensityMap.value = t2.specularIntensityMap, n(t2.specularIntensityMap, e3.specularIntensityMapTransform));
    }(e2, r, s)) : r.isMeshMatcapMaterial ? (i(e2, r), function(e3, t2) {
      t2.matcap && (e3.matcap.value = t2.matcap);
    }(e2, r)) : r.isMeshDepthMaterial ? i(e2, r) : r.isMeshDistanceMaterial ? (i(e2, r), function(e3, n2) {
      const i2 = t.get(n2).light;
      e3.referencePosition.value.setFromMatrixPosition(i2.matrixWorld), e3.nearDistance.value = i2.shadow.camera.near, e3.farDistance.value = i2.shadow.camera.far;
    }(e2, r)) : r.isMeshNormalMaterial ? i(e2, r) : r.isLineBasicMaterial ? (function(e3, t2) {
      e3.diffuse.value.copy(t2.color), e3.opacity.value = t2.opacity, t2.map && (e3.map.value = t2.map, n(t2.map, e3.mapTransform));
    }(e2, r), r.isLineDashedMaterial && function(e3, t2) {
      e3.dashSize.value = t2.dashSize, e3.totalSize.value = t2.dashSize + t2.gapSize, e3.scale.value = t2.scale;
    }(e2, r)) : r.isPointsMaterial ? function(e3, t2, i2, r2) {
      e3.diffuse.value.copy(t2.color), e3.opacity.value = t2.opacity, e3.size.value = t2.size * i2, e3.scale.value = 0.5 * r2, t2.map && (e3.map.value = t2.map, n(t2.map, e3.uvTransform));
      t2.alphaMap && (e3.alphaMap.value = t2.alphaMap, n(t2.alphaMap, e3.alphaMapTransform));
      t2.alphaTest > 0 && (e3.alphaTest.value = t2.alphaTest);
    }(e2, r, a, o) : r.isSpriteMaterial ? function(e3, t2) {
      e3.diffuse.value.copy(t2.color), e3.opacity.value = t2.opacity, e3.rotation.value = t2.rotation, t2.map && (e3.map.value = t2.map, n(t2.map, e3.mapTransform));
      t2.alphaMap && (e3.alphaMap.value = t2.alphaMap, n(t2.alphaMap, e3.alphaMapTransform));
      t2.alphaTest > 0 && (e3.alphaTest.value = t2.alphaTest);
    }(e2, r) : r.isShadowMaterial ? (e2.color.value.copy(r.color), e2.opacity.value = r.opacity) : r.isShaderMaterial && (r.uniformsNeedUpdate = false);
  } };
}
function ua(e, t, n, i) {
  let r = {}, a = {}, o = [];
  const s = e.getParameter(e.MAX_UNIFORM_BUFFER_BINDINGS);
  function l(e2, t2, n2, i2) {
    const r2 = e2.value, a2 = t2 + "_" + n2;
    if (void 0 === i2[a2]) return i2[a2] = "number" == typeof r2 || "boolean" == typeof r2 ? r2 : r2.clone(), true;
    {
      const e3 = i2[a2];
      if ("number" == typeof r2 || "boolean" == typeof r2) {
        if (e3 !== r2) return i2[a2] = r2, true;
      } else if (false === e3.equals(r2)) return e3.copy(r2), true;
    }
    return false;
  }
  function c(e2) {
    const t2 = { boundary: 0, storage: 0 };
    return "number" == typeof e2 || "boolean" == typeof e2 ? (t2.boundary = 4, t2.storage = 4) : e2.isVector2 ? (t2.boundary = 8, t2.storage = 8) : e2.isVector3 || e2.isColor ? (t2.boundary = 16, t2.storage = 12) : e2.isVector4 ? (t2.boundary = 16, t2.storage = 16) : e2.isMatrix3 ? (t2.boundary = 48, t2.storage = 48) : e2.isMatrix4 ? (t2.boundary = 64, t2.storage = 64) : e2.isTexture ? console.warn("THREE.WebGLRenderer: Texture samplers can not be part of an uniforms group.") : console.warn("THREE.WebGLRenderer: Unsupported uniform value type.", e2), t2;
  }
  function d(t2) {
    const n2 = t2.target;
    n2.removeEventListener("dispose", d);
    const i2 = o.indexOf(n2.__bindingPointIndex);
    o.splice(i2, 1), e.deleteBuffer(r[n2.id]), delete r[n2.id], delete a[n2.id];
  }
  return { bind: function(e2, t2) {
    const n2 = t2.program;
    i.uniformBlockBinding(e2, n2);
  }, update: function(n2, u) {
    let f = r[n2.id];
    void 0 === f && (!function(e2) {
      const t2 = e2.uniforms;
      let n3 = 0;
      const i2 = 16;
      for (let e3 = 0, r3 = t2.length; e3 < r3; e3++) {
        const r4 = Array.isArray(t2[e3]) ? t2[e3] : [t2[e3]];
        for (let e4 = 0, t3 = r4.length; e4 < t3; e4++) {
          const t4 = r4[e4], a2 = Array.isArray(t4.value) ? t4.value : [t4.value];
          for (let e5 = 0, r5 = a2.length; e5 < r5; e5++) {
            const r6 = c(a2[e5]), o2 = n3 % i2, s2 = o2 % r6.boundary, l2 = o2 + s2;
            n3 += s2, 0 !== l2 && i2 - l2 < r6.storage && (n3 += i2 - l2), t4.__data = new Float32Array(r6.storage / Float32Array.BYTES_PER_ELEMENT), t4.__offset = n3, n3 += r6.storage;
          }
        }
      }
      const r2 = n3 % i2;
      r2 > 0 && (n3 += i2 - r2);
      e2.__size = n3, e2.__cache = {};
    }(n2), f = function(t2) {
      const n3 = function() {
        for (let e2 = 0; e2 < s; e2++) if (-1 === o.indexOf(e2)) return o.push(e2), e2;
        return console.error("THREE.WebGLRenderer: Maximum number of simultaneously usable uniforms groups reached."), 0;
      }();
      t2.__bindingPointIndex = n3;
      const i2 = e.createBuffer(), r2 = t2.__size, a2 = t2.usage;
      return e.bindBuffer(e.UNIFORM_BUFFER, i2), e.bufferData(e.UNIFORM_BUFFER, r2, a2), e.bindBuffer(e.UNIFORM_BUFFER, null), e.bindBufferBase(e.UNIFORM_BUFFER, n3, i2), i2;
    }(n2), r[n2.id] = f, n2.addEventListener("dispose", d));
    const p = u.program;
    i.updateUBOMapping(n2, p);
    const m = t.render.frame;
    a[n2.id] !== m && (!function(t2) {
      const n3 = r[t2.id], i2 = t2.uniforms, a2 = t2.__cache;
      e.bindBuffer(e.UNIFORM_BUFFER, n3);
      for (let t3 = 0, n4 = i2.length; t3 < n4; t3++) {
        const n5 = Array.isArray(i2[t3]) ? i2[t3] : [i2[t3]];
        for (let i3 = 0, r2 = n5.length; i3 < r2; i3++) {
          const r3 = n5[i3];
          if (true === l(r3, t3, i3, a2)) {
            const t4 = r3.__offset, n6 = Array.isArray(r3.value) ? r3.value : [r3.value];
            let i4 = 0;
            for (let a3 = 0; a3 < n6.length; a3++) {
              const o2 = n6[a3], s2 = c(o2);
              "number" == typeof o2 || "boolean" == typeof o2 ? (r3.__data[0] = o2, e.bufferSubData(e.UNIFORM_BUFFER, t4 + i4, r3.__data)) : o2.isMatrix3 ? (r3.__data[0] = o2.elements[0], r3.__data[1] = o2.elements[1], r3.__data[2] = o2.elements[2], r3.__data[3] = 0, r3.__data[4] = o2.elements[3], r3.__data[5] = o2.elements[4], r3.__data[6] = o2.elements[5], r3.__data[7] = 0, r3.__data[8] = o2.elements[6], r3.__data[9] = o2.elements[7], r3.__data[10] = o2.elements[8], r3.__data[11] = 0) : (o2.toArray(r3.__data, i4), i4 += s2.storage / Float32Array.BYTES_PER_ELEMENT);
            }
            e.bufferSubData(e.UNIFORM_BUFFER, t4, r3.__data);
          }
        }
      }
      e.bindBuffer(e.UNIFORM_BUFFER, null);
    }(n2), a[n2.id] = m);
  }, dispose: function() {
    for (const t2 in r) e.deleteBuffer(r[t2]);
    o = [], r = {}, a = {};
  } };
}
class fa {
  constructor(t = {}) {
    const { canvas: n = _mpChunkDeps_three_build_three_core_min.si(), context: i = null, depth: a = true, stencil: o = false, alpha: s = false, antialias: l = false, premultipliedAlpha: c = true, preserveDrawingBuffer: f = false, powerPreference: p = "default", failIfMajorPerformanceCaveat: h = false, reverseDepthBuffer: _ = false } = t;
    let v;
    if (this.isWebGLRenderer = true, null !== i) {
      if ("undefined" != typeof THREEGlobals["WebGLRenderingContext"] && i instanceof THREEGlobals["WebGLRenderingContext"]) throw new Error("THREE.WebGLRenderer: WebGL 1 is not supported since r163.");
      v = i.getContextAttributes().alpha;
    } else v = s;
    const E = new Uint32Array(4), M = new Int32Array(4);
    let x = null, R = null;
    const A = [], b = [];
    this.domElement = n, this.debug = { checkShaderErrors: true, onShaderError: null }, this.autoClear = true, this.autoClearColor = true, this.autoClearDepth = true, this.autoClearStencil = true, this.sortObjects = true, this.clippingPlanes = [], this.localClippingEnabled = false, this._outputColorSpace = _mpChunkDeps_three_build_three_core_min.Ge, this.toneMapping = _mpChunkDeps_three_build_three_core_min.$, this.toneMappingExposure = 1;
    const C = this;
    let L = false, P = 0, w = 0, D = null, y = -1, N = null;
    const O = new _mpChunkDeps_three_build_three_core_min.wi(), F = new _mpChunkDeps_three_build_three_core_min.wi();
    let G = null;
    const V = new _mpChunkDeps_three_build_three_core_min.$r(0);
    let z = 0, W = n.width, X = n.height, Y = 1, j = null, K = null;
    const q = new _mpChunkDeps_three_build_three_core_min.wi(0, 0, W, X), Z = new _mpChunkDeps_three_build_three_core_min.wi(0, 0, W, X);
    let $ = false;
    const Q = new _mpChunkDeps_three_build_three_core_min.ra();
    let J = false, ee = false;
    this.transmissionResolutionScale = 1;
    const te = new _mpChunkDeps_three_build_three_core_min.nr(), ne = new _mpChunkDeps_three_build_three_core_min.nr(), ie = new _mpChunkDeps_three_build_three_core_min.Ii(), re = new _mpChunkDeps_three_build_three_core_min.wi(), ae = { background: null, fog: null, environment: null, overrideMaterial: null, isScene: true };
    let oe = false;
    function se() {
      return null === D ? Y : 1;
    }
    let le, ce, de, ue, fe, pe, he, _e, ve, Ee, Se, Te, Me, xe, Re, Ae, be, Ce, Le, Pe, Ue, we, De, ye, Ie = i;
    function Ne(e, t2) {
      return n.getContext(e, t2);
    }
    try {
      const e = { alpha: true, depth: a, stencil: o, antialias: l, premultipliedAlpha: c, preserveDrawingBuffer: f, powerPreference: p, failIfMajorPerformanceCaveat: h };
      if ("setAttribute" in n && n.setAttribute("data-engine", `three.js r${_mpChunkDeps_three_build_three_core_min.t}`), n.addEventListener("webglcontextlost", Be, false), n.addEventListener("webglcontextrestored", He, false), n.addEventListener("webglcontextcreationerror", Ge, false), null === Ie) {
        const t2 = "webgl2";
        if (Ie = Ne(t2, e), null === Ie) throw Ne(t2) ? new Error("Error creating WebGL context with your selected attributes.") : new Error("Error creating WebGL context.");
      }
    } catch (e) {
      throw console.error("THREE.WebGLRenderer: " + e.message), e;
    }
    function Oe() {
      le = new si(Ie), le.init(), we = new ia(Ie, le), ce = new Gn(Ie, le, t, we), de = new ta(Ie, le), ce.reverseDepthBuffer && _ && de.buffers.depth.setReversed(true), ue = new di(Ie), fe = new zr(), pe = new na(Ie, le, de, fe, ce, we, ue), he = new zn(C), _e = new oi(C), ve = new Un(Ie), De = new Bn(Ie, ve), Ee = new li(Ie, ve, ue, De), Se = new fi(Ie, Ee, ve, ue), Le = new ui(Ie, ce, pe), Ae = new Vn(fe), Te = new Vr(C, he, _e, le, ce, De, Ae), Me = new da(C, fe), xe = new Yr(), Re = new Qr(le), Ce = new Fn(C, he, _e, de, Se, v, c), be = new Jr(C, Se, ce), ye = new ua(Ie, ue, ce, de), Pe = new Hn(Ie, le, ue), Ue = new ci(Ie, le, ue), ue.programs = Te.programs, C.capabilities = ce, C.extensions = le, C.properties = fe, C.renderLists = xe, C.shadowMap = be, C.state = de, C.info = ue;
    }
    Oe();
    const Fe = new sa(C, Ie);
    function Be(e) {
      e.preventDefault(), console.log("THREE.WebGLRenderer: Context Lost."), L = true;
    }
    function He() {
      console.log("THREE.WebGLRenderer: Context Restored."), L = false;
      const e = ue.autoReset, t2 = be.enabled, n2 = be.autoUpdate, i2 = be.needsUpdate, r = be.type;
      Oe(), ue.autoReset = e, be.enabled = t2, be.autoUpdate = n2, be.needsUpdate = i2, be.type = r;
    }
    function Ge(e) {
      console.error("THREE.WebGLRenderer: A WebGL context could not be created. Reason: ", e.statusMessage);
    }
    function Ve(e) {
      const t2 = e.target;
      t2.removeEventListener("dispose", Ve), function(e2) {
        (function(e3) {
          const t3 = fe.get(e3).programs;
          void 0 !== t3 && (t3.forEach(function(e4) {
            Te.releaseProgram(e4);
          }), e3.isShaderMaterial && Te.releaseShaderCache(e3));
        })(e2), fe.remove(e2);
      }(t2);
    }
    function ze(e, t2, n2) {
      true === e.transparent && e.side === _mpChunkDeps_three_build_three_core_min.p && false === e.forceSinglePass ? (e.side = _mpChunkDeps_three_build_three_core_min.d, e.needsUpdate = true, Qe(e, t2, n2), e.side = _mpChunkDeps_three_build_three_core_min.u, e.needsUpdate = true, Qe(e, t2, n2), e.side = _mpChunkDeps_three_build_three_core_min.p) : Qe(e, t2, n2);
    }
    this.xr = Fe, this.getContext = function() {
      return Ie;
    }, this.getContextAttributes = function() {
      return Ie.getContextAttributes();
    }, this.forceContextLoss = function() {
      const e = le.get("WEBGL_lose_context");
      e && e.loseContext();
    }, this.forceContextRestore = function() {
      const e = le.get("WEBGL_lose_context");
      e && e.restoreContext();
    }, this.getPixelRatio = function() {
      return Y;
    }, this.setPixelRatio = function(e) {
      void 0 !== e && (Y = e, this.setSize(W, X, false));
    }, this.getSize = function(e) {
      return e.set(W, X);
    }, this.setSize = function(e, t2, i2 = true) {
      Fe.isPresenting ? console.warn("THREE.WebGLRenderer: Can't change size while VR device is presenting.") : (W = e, X = t2, n.width = Math.floor(e * Y), n.height = Math.floor(t2 * Y), true === i2 && (n.style.width = e + "px", n.style.height = t2 + "px"), this.setViewport(0, 0, e, t2));
    }, this.getDrawingBufferSize = function(e) {
      return e.set(W * Y, X * Y).floor();
    }, this.setDrawingBufferSize = function(e, t2, i2) {
      W = e, X = t2, Y = i2, n.width = Math.floor(e * i2), n.height = Math.floor(t2 * i2), this.setViewport(0, 0, e, t2);
    }, this.getCurrentViewport = function(e) {
      return e.copy(O);
    }, this.getViewport = function(e) {
      return e.copy(q);
    }, this.setViewport = function(e, t2, n2, i2) {
      e.isVector4 ? q.set(e.x, e.y, e.z, e.w) : q.set(e, t2, n2, i2), de.viewport(O.copy(q).multiplyScalar(Y).round());
    }, this.getScissor = function(e) {
      return e.copy(Z);
    }, this.setScissor = function(e, t2, n2, i2) {
      e.isVector4 ? Z.set(e.x, e.y, e.z, e.w) : Z.set(e, t2, n2, i2), de.scissor(F.copy(Z).multiplyScalar(Y).round());
    }, this.getScissorTest = function() {
      return $;
    }, this.setScissorTest = function(e) {
      de.setScissorTest($ = e);
    }, this.setOpaqueSort = function(e) {
      j = e;
    }, this.setTransparentSort = function(e) {
      K = e;
    }, this.getClearColor = function(e) {
      return e.copy(Ce.getClearColor());
    }, this.setClearColor = function() {
      Ce.setClearColor.apply(Ce, arguments);
    }, this.getClearAlpha = function() {
      return Ce.getClearAlpha();
    }, this.setClearAlpha = function() {
      Ce.setClearAlpha.apply(Ce, arguments);
    }, this.clear = function(e = true, t2 = true, n2 = true) {
      let i2 = 0;
      if (e) {
        let e2 = false;
        if (null !== D) {
          const t3 = D.texture.format;
          e2 = t3 === _mpChunkDeps_three_build_three_core_min.Gt || t3 === _mpChunkDeps_three_build_three_core_min.Yt || t3 === _mpChunkDeps_three_build_three_core_min.Jt;
        }
        if (e2) {
          const e3 = D.texture.type, t3 = e3 === _mpChunkDeps_three_build_three_core_min.Tt || e3 === _mpChunkDeps_three_build_three_core_min.kt || e3 === _mpChunkDeps_three_build_three_core_min.It || e3 === _mpChunkDeps_three_build_three_core_min.Ft || e3 === _mpChunkDeps_three_build_three_core_min.Pt || e3 === _mpChunkDeps_three_build_three_core_min.Ot, n3 = Ce.getClearColor(), i3 = Ce.getClearAlpha(), r = n3.r, a2 = n3.g, o2 = n3.b;
          t3 ? (E[0] = r, E[1] = a2, E[2] = o2, E[3] = i3, Ie.clearBufferuiv(Ie.COLOR, 0, E)) : (M[0] = r, M[1] = a2, M[2] = o2, M[3] = i3, Ie.clearBufferiv(Ie.COLOR, 0, M));
        } else i2 |= Ie.COLOR_BUFFER_BIT;
      }
      t2 && (i2 |= Ie.DEPTH_BUFFER_BIT), n2 && (i2 |= Ie.STENCIL_BUFFER_BIT, this.state.buffers.stencil.setMask(4294967295)), Ie.clear(i2);
    }, this.clearColor = function() {
      this.clear(true, false, false);
    }, this.clearDepth = function() {
      this.clear(false, true, false);
    }, this.clearStencil = function() {
      this.clear(false, false, true);
    }, this.dispose = function() {
      n.removeEventListener("webglcontextlost", Be, false), n.removeEventListener("webglcontextrestored", He, false), n.removeEventListener("webglcontextcreationerror", Ge, false), Ce.dispose(), xe.dispose(), Re.dispose(), fe.dispose(), he.dispose(), _e.dispose(), Se.dispose(), De.dispose(), ye.dispose(), Te.dispose(), Fe.dispose(), Fe.removeEventListener("sessionstart", We), Fe.removeEventListener("sessionend", Xe), Ye.stop();
    }, this.renderBufferDirect = function(e, t2, n2, i2, r, a2) {
      null === t2 && (t2 = ae);
      const o2 = r.isMesh && r.matrixWorld.determinant() < 0, s2 = function(e2, t3, n3, i3, r2) {
        true !== t3.isScene && (t3 = ae);
        pe.resetTextureUnits();
        const a3 = t3.fog, o3 = i3.isMeshStandardMaterial ? t3.environment : null, s3 = null === D ? C.outputColorSpace : true === D.isXRRenderTarget ? D.texture.colorSpace : _mpChunkDeps_three_build_three_core_min.$e, l3 = (i3.isMeshStandardMaterial ? _e : he).get(i3.envMap || o3), c3 = true === i3.vertexColors && !!n3.attributes.color && 4 === n3.attributes.color.itemSize, d2 = !!n3.attributes.tangent && (!!i3.normalMap || i3.anisotropy > 0), u2 = !!n3.morphAttributes.position, f3 = !!n3.morphAttributes.normal, p3 = !!n3.morphAttributes.color;
        let m2 = _mpChunkDeps_three_build_three_core_min.$;
        i3.toneMapped && (null !== D && true !== D.isXRRenderTarget || (m2 = C.toneMapping));
        const h3 = n3.morphAttributes.position || n3.morphAttributes.normal || n3.morphAttributes.color, _3 = void 0 !== h3 ? h3.length : 0, g = fe.get(i3), v2 = R.state.lights;
        if (true === J && (true === ee || e2 !== N)) {
          const t4 = e2 === N && i3.id === y;
          Ae.setState(i3, e2, t4);
        }
        let E2 = false;
        i3.version === g.__version ? g.needsLights && g.lightsStateVersion !== v2.state.version || g.outputColorSpace !== s3 || r2.isBatchedMesh && false === g.batching ? E2 = true : r2.isBatchedMesh || true !== g.batching ? r2.isBatchedMesh && true === g.batchingColor && null === r2.colorTexture || r2.isBatchedMesh && false === g.batchingColor && null !== r2.colorTexture || r2.isInstancedMesh && false === g.instancing ? E2 = true : r2.isInstancedMesh || true !== g.instancing ? r2.isSkinnedMesh && false === g.skinning ? E2 = true : r2.isSkinnedMesh || true !== g.skinning ? r2.isInstancedMesh && true === g.instancingColor && null === r2.instanceColor || r2.isInstancedMesh && false === g.instancingColor && null !== r2.instanceColor || r2.isInstancedMesh && true === g.instancingMorph && null === r2.morphTexture || r2.isInstancedMesh && false === g.instancingMorph && null !== r2.morphTexture || g.envMap !== l3 || true === i3.fog && g.fog !== a3 ? E2 = true : void 0 === g.numClippingPlanes || g.numClippingPlanes === Ae.numPlanes && g.numIntersection === Ae.numIntersection ? (g.vertexAlphas !== c3 || g.vertexTangents !== d2 || g.morphTargets !== u2 || g.morphNormals !== f3 || g.morphColors !== p3 || g.toneMapping !== m2 || g.morphTargetsCount !== _3) && (E2 = true) : E2 = true : E2 = true : E2 = true : E2 = true : (E2 = true, g.__version = i3.version);
        let S = g.currentProgram;
        true === E2 && (S = Qe(i3, t3, r2));
        let T = false, M2 = false, x2 = false;
        const A2 = S.getUniforms(), b2 = g.uniforms;
        de.useProgram(S.program) && (T = true, M2 = true, x2 = true);
        i3.id !== y && (y = i3.id, M2 = true);
        if (T || N !== e2) {
          de.buffers.depth.getReversed() ? (te.copy(e2.projectionMatrix), _mpChunkDeps_three_build_three_core_min.oi(te), _mpChunkDeps_three_build_three_core_min.ai(te), A2.setValue(Ie, "projectionMatrix", te)) : A2.setValue(Ie, "projectionMatrix", e2.projectionMatrix), A2.setValue(Ie, "viewMatrix", e2.matrixWorldInverse);
          const t4 = A2.map.cameraPosition;
          void 0 !== t4 && t4.setValue(Ie, ie.setFromMatrixPosition(e2.matrixWorld)), ce.logarithmicDepthBuffer && A2.setValue(Ie, "logDepthBufFC", 2 / (Math.log(e2.far + 1) / Math.LN2)), (i3.isMeshPhongMaterial || i3.isMeshToonMaterial || i3.isMeshLambertMaterial || i3.isMeshBasicMaterial || i3.isMeshStandardMaterial || i3.isShaderMaterial) && A2.setValue(Ie, "isOrthographic", true === e2.isOrthographicCamera), N !== e2 && (N = e2, M2 = true, x2 = true);
        }
        if (r2.isSkinnedMesh) {
          A2.setOptional(Ie, r2, "bindMatrix"), A2.setOptional(Ie, r2, "bindMatrixInverse");
          const e3 = r2.skeleton;
          e3 && (null === e3.boneTexture && e3.computeBoneTexture(), A2.setValue(Ie, "boneTexture", e3.boneTexture, pe));
        }
        r2.isBatchedMesh && (A2.setOptional(Ie, r2, "batchingTexture"), A2.setValue(Ie, "batchingTexture", r2._matricesTexture, pe), A2.setOptional(Ie, r2, "batchingIdTexture"), A2.setValue(Ie, "batchingIdTexture", r2._indirectTexture, pe), A2.setOptional(Ie, r2, "batchingColorTexture"), null !== r2._colorsTexture && A2.setValue(Ie, "batchingColorTexture", r2._colorsTexture, pe));
        const L2 = n3.morphAttributes;
        void 0 === L2.position && void 0 === L2.normal && void 0 === L2.color || Le.update(r2, n3, S);
        (M2 || g.receiveShadow !== r2.receiveShadow) && (g.receiveShadow = r2.receiveShadow, A2.setValue(Ie, "receiveShadow", r2.receiveShadow));
        i3.isMeshGouraudMaterial && null !== i3.envMap && (b2.envMap.value = l3, b2.flipEnvMap.value = l3.isCubeTexture && false === l3.isRenderTargetTexture ? -1 : 1);
        i3.isMeshStandardMaterial && null === i3.envMap && null !== t3.environment && (b2.envMapIntensity.value = t3.environmentIntensity);
        M2 && (A2.setValue(Ie, "toneMappingExposure", C.toneMappingExposure), g.needsLights && (w2 = x2, (P2 = b2).ambientLightColor.needsUpdate = w2, P2.lightProbe.needsUpdate = w2, P2.directionalLights.needsUpdate = w2, P2.directionalLightShadows.needsUpdate = w2, P2.pointLights.needsUpdate = w2, P2.pointLightShadows.needsUpdate = w2, P2.spotLights.needsUpdate = w2, P2.spotLightShadows.needsUpdate = w2, P2.rectAreaLights.needsUpdate = w2, P2.hemisphereLights.needsUpdate = w2), a3 && true === i3.fog && Me.refreshFogUniforms(b2, a3), Me.refreshMaterialUniforms(b2, i3, Y, X, R.state.transmissionRenderTarget[e2.id]), gr.upload(Ie, Je(g), b2, pe));
        var P2, w2;
        i3.isShaderMaterial && true === i3.uniformsNeedUpdate && (gr.upload(Ie, Je(g), b2, pe), i3.uniformsNeedUpdate = false);
        i3.isSpriteMaterial && A2.setValue(Ie, "center", r2.center);
        if (A2.setValue(Ie, "modelViewMatrix", r2.modelViewMatrix), A2.setValue(Ie, "normalMatrix", r2.normalMatrix), A2.setValue(Ie, "modelMatrix", r2.matrixWorld), i3.isShaderMaterial || i3.isRawShaderMaterial) {
          const e3 = i3.uniformsGroups;
          for (let t4 = 0, n4 = e3.length; t4 < n4; t4++) {
            const n5 = e3[t4];
            ye.update(n5, S), ye.bind(n5, S);
          }
        }
        return S;
      }(e, t2, n2, i2, r);
      de.setMaterial(i2, o2);
      let l2 = n2.index, c2 = 1;
      if (true === i2.wireframe) {
        if (l2 = Ee.getWireframeAttribute(n2), void 0 === l2) return;
        c2 = 2;
      }
      const d = n2.drawRange, u = n2.attributes.position;
      let f2 = d.start * c2, p2 = (d.start + d.count) * c2;
      null !== a2 && (f2 = Math.max(f2, a2.start * c2), p2 = Math.min(p2, (a2.start + a2.count) * c2)), null !== l2 ? (f2 = Math.max(f2, 0), p2 = Math.min(p2, l2.count)) : null != u && (f2 = Math.max(f2, 0), p2 = Math.min(p2, u.count));
      const m = p2 - f2;
      if (m < 0 || m === 1 / 0) return;
      let h2;
      De.setup(r, i2, s2, n2, l2);
      let _2 = Pe;
      if (null !== l2 && (h2 = ve.get(l2), _2 = Ue, _2.setIndex(h2)), r.isMesh) true === i2.wireframe ? (de.setLineWidth(i2.wireframeLinewidth * se()), _2.setMode(Ie.LINES)) : _2.setMode(Ie.TRIANGLES);
      else if (r.isLine) {
        let e2 = i2.linewidth;
        void 0 === e2 && (e2 = 1), de.setLineWidth(e2 * se()), r.isLineSegments ? _2.setMode(Ie.LINES) : r.isLineLoop ? _2.setMode(Ie.LINE_LOOP) : _2.setMode(Ie.LINE_STRIP);
      } else r.isPoints ? _2.setMode(Ie.POINTS) : r.isSprite && _2.setMode(Ie.TRIANGLES);
      if (r.isBatchedMesh) if (null !== r._multiDrawInstances) _2.renderMultiDrawInstances(r._multiDrawStarts, r._multiDrawCounts, r._multiDrawCount, r._multiDrawInstances);
      else if (le.get("WEBGL_multi_draw")) _2.renderMultiDraw(r._multiDrawStarts, r._multiDrawCounts, r._multiDrawCount);
      else {
        const e2 = r._multiDrawStarts, t3 = r._multiDrawCounts, n3 = r._multiDrawCount, a3 = l2 ? ve.get(l2).bytesPerElement : 1, o3 = fe.get(i2).currentProgram.getUniforms();
        for (let i3 = 0; i3 < n3; i3++) o3.setValue(Ie, "_gl_DrawID", i3), _2.render(e2[i3] / a3, t3[i3]);
      }
      else if (r.isInstancedMesh) _2.renderInstances(f2, m, r.count);
      else if (n2.isInstancedBufferGeometry) {
        const e2 = void 0 !== n2._maxInstanceCount ? n2._maxInstanceCount : 1 / 0, t3 = Math.min(n2.instanceCount, e2);
        _2.renderInstances(f2, m, t3);
      } else _2.render(f2, m);
    }, this.compile = function(e, t2, n2 = null) {
      null === n2 && (n2 = e), R = Re.get(n2), R.init(t2), b.push(R), n2.traverseVisible(function(e2) {
        e2.isLight && e2.layers.test(t2.layers) && (R.pushLight(e2), e2.castShadow && R.pushShadow(e2));
      }), e !== n2 && e.traverseVisible(function(e2) {
        e2.isLight && e2.layers.test(t2.layers) && (R.pushLight(e2), e2.castShadow && R.pushShadow(e2));
      }), R.setupLights();
      const i2 = /* @__PURE__ */ new Set();
      return e.traverse(function(e2) {
        if (!(e2.isMesh || e2.isPoints || e2.isLine || e2.isSprite)) return;
        const t3 = e2.material;
        if (t3) if (Array.isArray(t3)) for (let r = 0; r < t3.length; r++) {
          const a2 = t3[r];
          ze(a2, n2, e2), i2.add(a2);
        }
        else ze(t3, n2, e2), i2.add(t3);
      }), b.pop(), R = null, i2;
    }, this.compileAsync = function(e, t2, n2 = null) {
      const i2 = this.compile(e, t2, n2);
      return new Promise((t3) => {
        function n3() {
          i2.forEach(function(e2) {
            fe.get(e2).currentProgram.isReady() && i2.delete(e2);
          }), 0 !== i2.size ? setTimeout(n3, 10) : t3(e);
        }
        null !== le.get("KHR_parallel_shader_compile") ? n3() : setTimeout(n3, 10);
      });
    };
    let ke = null;
    function We() {
      Ye.stop();
    }
    function Xe() {
      Ye.start();
    }
    const Ye = new Pn();
    function je(e, t2, n2, i2) {
      if (false === e.visible) return;
      if (e.layers.test(t2.layers)) {
        if (e.isGroup) n2 = e.renderOrder;
        else if (e.isLOD) true === e.autoUpdate && e.update(t2);
        else if (e.isLight) R.pushLight(e), e.castShadow && R.pushShadow(e);
        else if (e.isSprite) {
          if (!e.frustumCulled || Q.intersectsSprite(e)) {
            i2 && re.setFromMatrixPosition(e.matrixWorld).applyMatrix4(ne);
            const t3 = Se.update(e), r2 = e.material;
            r2.visible && x.push(e, t3, r2, n2, re.z, null);
          }
        } else if ((e.isMesh || e.isLine || e.isPoints) && (!e.frustumCulled || Q.intersectsObject(e))) {
          const t3 = Se.update(e), r2 = e.material;
          if (i2 && (void 0 !== e.boundingSphere ? (null === e.boundingSphere && e.computeBoundingSphere(), re.copy(e.boundingSphere.center)) : (null === t3.boundingSphere && t3.computeBoundingSphere(), re.copy(t3.boundingSphere.center)), re.applyMatrix4(e.matrixWorld).applyMatrix4(ne)), Array.isArray(r2)) {
            const i3 = t3.groups;
            for (let a2 = 0, o2 = i3.length; a2 < o2; a2++) {
              const o3 = i3[a2], s2 = r2[o3.materialIndex];
              s2 && s2.visible && x.push(e, t3, s2, n2, re.z, o3);
            }
          } else r2.visible && x.push(e, t3, r2, n2, re.z, null);
        }
      }
      const r = e.children;
      for (let e2 = 0, a2 = r.length; e2 < a2; e2++) je(r[e2], t2, n2, i2);
    }
    function Ke(e, t2, n2, i2) {
      const r = e.opaque, a2 = e.transmissive, o2 = e.transparent;
      R.setupLightsView(n2), true === J && Ae.setGlobalState(C.clippingPlanes, n2), i2 && de.viewport(O.copy(i2)), r.length > 0 && Ze(r, t2, n2), a2.length > 0 && Ze(a2, t2, n2), o2.length > 0 && Ze(o2, t2, n2), de.buffers.depth.setTest(true), de.buffers.depth.setMask(true), de.buffers.color.setMask(true), de.setPolygonOffset(false);
    }
    function qe(e, t2, n2, i2) {
      if (null !== (true === n2.isScene ? n2.overrideMaterial : null)) return;
      void 0 === R.state.transmissionRenderTarget[i2.id] && (R.state.transmissionRenderTarget[i2.id] = new _mpChunkDeps_three_build_three_core_min.Si(1, 1, { generateMipmaps: true, type: le.has("EXT_color_buffer_half_float") || le.has("EXT_color_buffer_float") ? _mpChunkDeps_three_build_three_core_min.Et : _mpChunkDeps_three_build_three_core_min.Tt, minFilter: _mpChunkDeps_three_build_three_core_min._t, samples: 4, stencilBuffer: o, resolveDepthBuffer: false, resolveStencilBuffer: false, colorSpace: _mpChunkDeps_three_build_three_core_min.ui.workingColorSpace }));
      const r = R.state.transmissionRenderTarget[i2.id], a2 = i2.viewport || O;
      r.setSize(a2.z * C.transmissionResolutionScale, a2.w * C.transmissionResolutionScale);
      const s2 = C.getRenderTarget();
      C.setRenderTarget(r), C.getClearColor(V), z = C.getClearAlpha(), z < 1 && C.setClearColor(16777215, 0.5), C.clear(), oe && Ce.render(n2);
      const l2 = C.toneMapping;
      C.toneMapping = _mpChunkDeps_three_build_three_core_min.$;
      const c2 = i2.viewport;
      if (void 0 !== i2.viewport && (i2.viewport = void 0), R.setupLightsView(i2), true === J && Ae.setGlobalState(C.clippingPlanes, i2), Ze(e, n2, i2), pe.updateMultisampleRenderTarget(r), pe.updateRenderTargetMipmap(r), false === le.has("WEBGL_multisampled_render_to_texture")) {
        let e2 = false;
        for (let r2 = 0, a3 = t2.length; r2 < a3; r2++) {
          const a4 = t2[r2], o2 = a4.object, s3 = a4.geometry, l3 = a4.material, c3 = a4.group;
          if (l3.side === _mpChunkDeps_three_build_three_core_min.p && o2.layers.test(i2.layers)) {
            const t3 = l3.side;
            l3.side = _mpChunkDeps_three_build_three_core_min.d, l3.needsUpdate = true, $e(o2, n2, i2, s3, l3, c3), l3.side = t3, l3.needsUpdate = true, e2 = true;
          }
        }
        true === e2 && (pe.updateMultisampleRenderTarget(r), pe.updateRenderTargetMipmap(r));
      }
      C.setRenderTarget(s2), C.setClearColor(V, z), void 0 !== c2 && (i2.viewport = c2), C.toneMapping = l2;
    }
    function Ze(e, t2, n2) {
      const i2 = true === t2.isScene ? t2.overrideMaterial : null;
      for (let r = 0, a2 = e.length; r < a2; r++) {
        const a3 = e[r], o2 = a3.object, s2 = a3.geometry, l2 = null === i2 ? a3.material : i2, c2 = a3.group;
        o2.layers.test(n2.layers) && $e(o2, t2, n2, s2, l2, c2);
      }
    }
    function $e(e, t2, n2, i2, r, a2) {
      e.onBeforeRender(C, t2, n2, i2, r, a2), e.modelViewMatrix.multiplyMatrices(n2.matrixWorldInverse, e.matrixWorld), e.normalMatrix.getNormalMatrix(e.modelViewMatrix), r.onBeforeRender(C, t2, n2, i2, e, a2), true === r.transparent && r.side === _mpChunkDeps_three_build_three_core_min.p && false === r.forceSinglePass ? (r.side = _mpChunkDeps_three_build_three_core_min.d, r.needsUpdate = true, C.renderBufferDirect(n2, t2, i2, r, e, a2), r.side = _mpChunkDeps_three_build_three_core_min.u, r.needsUpdate = true, C.renderBufferDirect(n2, t2, i2, r, e, a2), r.side = _mpChunkDeps_three_build_three_core_min.p) : C.renderBufferDirect(n2, t2, i2, r, e, a2), e.onAfterRender(C, t2, n2, i2, r, a2);
    }
    function Qe(e, t2, n2) {
      true !== t2.isScene && (t2 = ae);
      const i2 = fe.get(e), r = R.state.lights, a2 = R.state.shadowsArray, o2 = r.state.version, s2 = Te.getParameters(e, r.state, a2, t2, n2), l2 = Te.getProgramCacheKey(s2);
      let c2 = i2.programs;
      i2.environment = e.isMeshStandardMaterial ? t2.environment : null, i2.fog = t2.fog, i2.envMap = (e.isMeshStandardMaterial ? _e : he).get(e.envMap || i2.environment), i2.envMapRotation = null !== i2.environment && null === e.envMap ? t2.environmentRotation : e.envMapRotation, void 0 === c2 && (e.addEventListener("dispose", Ve), c2 = /* @__PURE__ */ new Map(), i2.programs = c2);
      let d = c2.get(l2);
      if (void 0 !== d) {
        if (i2.currentProgram === d && i2.lightsStateVersion === o2) return et(e, s2), d;
      } else s2.uniforms = Te.getUniforms(e), e.onBeforeCompile(s2, C), d = Te.acquireProgram(s2, l2), c2.set(l2, d), i2.uniforms = s2.uniforms;
      const u = i2.uniforms;
      return (e.isShaderMaterial || e.isRawShaderMaterial) && true !== e.clipping || (u.clippingPlanes = Ae.uniform), et(e, s2), i2.needsLights = function(e2) {
        return e2.isMeshLambertMaterial || e2.isMeshToonMaterial || e2.isMeshPhongMaterial || e2.isMeshStandardMaterial || e2.isShadowMaterial || e2.isShaderMaterial && true === e2.lights;
      }(e), i2.lightsStateVersion = o2, i2.needsLights && (u.ambientLightColor.value = r.state.ambient, u.lightProbe.value = r.state.probe, u.directionalLights.value = r.state.directional, u.directionalLightShadows.value = r.state.directionalShadow, u.spotLights.value = r.state.spot, u.spotLightShadows.value = r.state.spotShadow, u.rectAreaLights.value = r.state.rectArea, u.ltc_1.value = r.state.rectAreaLTC1, u.ltc_2.value = r.state.rectAreaLTC2, u.pointLights.value = r.state.point, u.pointLightShadows.value = r.state.pointShadow, u.hemisphereLights.value = r.state.hemi, u.directionalShadowMap.value = r.state.directionalShadowMap, u.directionalShadowMatrix.value = r.state.directionalShadowMatrix, u.spotShadowMap.value = r.state.spotShadowMap, u.spotLightMatrix.value = r.state.spotLightMatrix, u.spotLightMap.value = r.state.spotLightMap, u.pointShadowMap.value = r.state.pointShadowMap, u.pointShadowMatrix.value = r.state.pointShadowMatrix), i2.currentProgram = d, i2.uniformsList = null, d;
    }
    function Je(e) {
      if (null === e.uniformsList) {
        const t2 = e.currentProgram.getUniforms();
        e.uniformsList = gr.seqWithValue(t2.seq, e.uniforms);
      }
      return e.uniformsList;
    }
    function et(e, t2) {
      const n2 = fe.get(e);
      n2.outputColorSpace = t2.outputColorSpace, n2.batching = t2.batching, n2.batchingColor = t2.batchingColor, n2.instancing = t2.instancing, n2.instancingColor = t2.instancingColor, n2.instancingMorph = t2.instancingMorph, n2.skinning = t2.skinning, n2.morphTargets = t2.morphTargets, n2.morphNormals = t2.morphNormals, n2.morphColors = t2.morphColors, n2.morphTargetsCount = t2.morphTargetsCount, n2.numClippingPlanes = t2.numClippingPlanes, n2.numIntersection = t2.numClipIntersection, n2.vertexAlphas = t2.vertexAlphas, n2.vertexTangents = t2.vertexTangents, n2.toneMapping = t2.toneMapping;
    }
    Ye.setAnimationLoop(function(e) {
      ke && ke(e);
    }), "undefined" != typeof THREEGlobals["self"] && Ye.setContext(THREEGlobals["self"]), this.setAnimationLoop = function(e) {
      ke = e, Fe.setAnimationLoop(e), null === e ? Ye.stop() : Ye.start();
    }, Fe.addEventListener("sessionstart", We), Fe.addEventListener("sessionend", Xe), this.render = function(e, t2) {
      if (void 0 !== t2 && true !== t2.isCamera) return void console.error("THREE.WebGLRenderer.render: camera is not an instance of THREE.Camera.");
      if (true === L) return;
      if (true === e.matrixWorldAutoUpdate && e.updateMatrixWorld(), null === t2.parent && true === t2.matrixWorldAutoUpdate && t2.updateMatrixWorld(), true === Fe.enabled && true === Fe.isPresenting && (true === Fe.cameraAutoUpdate && Fe.updateCamera(t2), t2 = Fe.getCamera()), true === e.isScene && e.onBeforeRender(C, e, t2, D), R = Re.get(e, b.length), R.init(t2), b.push(R), ne.multiplyMatrices(t2.projectionMatrix, t2.matrixWorldInverse), Q.setFromProjectionMatrix(ne), ee = this.localClippingEnabled, J = Ae.init(this.clippingPlanes, ee), x = xe.get(e, A.length), x.init(), A.push(x), true === Fe.enabled && true === Fe.isPresenting) {
        const e2 = C.xr.getDepthSensingMesh();
        null !== e2 && je(e2, t2, -1 / 0, C.sortObjects);
      }
      je(e, t2, 0, C.sortObjects), x.finish(), true === C.sortObjects && x.sort(j, K), oe = false === Fe.enabled || false === Fe.isPresenting || false === Fe.hasDepthSensing(), oe && Ce.addToRenderList(x, e), this.info.render.frame++, true === J && Ae.beginShadows();
      const n2 = R.state.shadowsArray;
      be.render(n2, e, t2), true === J && Ae.endShadows(), true === this.info.autoReset && this.info.reset();
      const i2 = x.opaque, r = x.transmissive;
      if (R.setupLights(), t2.isArrayCamera) {
        const n3 = t2.cameras;
        if (r.length > 0) for (let t3 = 0, a2 = n3.length; t3 < a2; t3++) {
          qe(i2, r, e, n3[t3]);
        }
        oe && Ce.render(e);
        for (let t3 = 0, i3 = n3.length; t3 < i3; t3++) {
          const i4 = n3[t3];
          Ke(x, e, i4, i4.viewport);
        }
      } else r.length > 0 && qe(i2, r, e, t2), oe && Ce.render(e), Ke(x, e, t2);
      null !== D && 0 === w && (pe.updateMultisampleRenderTarget(D), pe.updateRenderTargetMipmap(D)), true === e.isScene && e.onAfterRender(C, e, t2), De.resetDefaultState(), y = -1, N = null, b.pop(), b.length > 0 ? (R = b[b.length - 1], true === J && Ae.setGlobalState(C.clippingPlanes, R.state.camera)) : R = null, A.pop(), x = A.length > 0 ? A[A.length - 1] : null;
    }, this.getActiveCubeFace = function() {
      return P;
    }, this.getActiveMipmapLevel = function() {
      return w;
    }, this.getRenderTarget = function() {
      return D;
    }, this.setRenderTargetTextures = function(e, t2, n2) {
      fe.get(e.texture).__webglTexture = t2, fe.get(e.depthTexture).__webglTexture = n2;
      const i2 = fe.get(e);
      i2.__hasExternalTextures = true, i2.__autoAllocateDepthBuffer = void 0 === n2, i2.__autoAllocateDepthBuffer || true === le.has("WEBGL_multisampled_render_to_texture") && (console.warn("THREE.WebGLRenderer: Render-to-texture extension was disabled because an external texture was provided"), i2.__useRenderToTexture = false);
    }, this.setRenderTargetFramebuffer = function(e, t2) {
      const n2 = fe.get(e);
      n2.__webglFramebuffer = t2, n2.__useDefaultFramebuffer = void 0 === t2;
    };
    const tt = Ie.createFramebuffer();
    this.setRenderTarget = function(e, t2 = 0, n2 = 0) {
      D = e, P = t2, w = n2;
      let i2 = true, r = null, a2 = false, o2 = false;
      if (e) {
        const s2 = fe.get(e);
        if (void 0 !== s2.__useDefaultFramebuffer) de.bindFramebuffer(Ie.FRAMEBUFFER, null), i2 = false;
        else if (void 0 === s2.__webglFramebuffer) pe.setupRenderTarget(e);
        else if (s2.__hasExternalTextures) pe.rebindTextures(e, fe.get(e.texture).__webglTexture, fe.get(e.depthTexture).__webglTexture);
        else if (e.depthBuffer) {
          const t3 = e.depthTexture;
          if (s2.__boundDepthTexture !== t3) {
            if (null !== t3 && fe.has(t3) && (e.width !== t3.image.width || e.height !== t3.image.height)) throw new Error("WebGLRenderTarget: Attached DepthTexture is initialized to the incorrect size.");
            pe.setupDepthRenderbuffer(e);
          }
        }
        const l2 = e.texture;
        (l2.isData3DTexture || l2.isDataArrayTexture || l2.isCompressedArrayTexture) && (o2 = true);
        const c2 = fe.get(e).__webglFramebuffer;
        e.isWebGLCubeRenderTarget ? (r = Array.isArray(c2[t2]) ? c2[t2][n2] : c2[t2], a2 = true) : r = e.samples > 0 && false === pe.useMultisampledRTT(e) ? fe.get(e).__webglMultisampledFramebuffer : Array.isArray(c2) ? c2[n2] : c2, O.copy(e.viewport), F.copy(e.scissor), G = e.scissorTest;
      } else O.copy(q).multiplyScalar(Y).floor(), F.copy(Z).multiplyScalar(Y).floor(), G = $;
      0 !== n2 && (r = tt);
      if (de.bindFramebuffer(Ie.FRAMEBUFFER, r) && i2 && de.drawBuffers(e, r), de.viewport(O), de.scissor(F), de.setScissorTest(G), a2) {
        const i3 = fe.get(e.texture);
        Ie.framebufferTexture2D(Ie.FRAMEBUFFER, Ie.COLOR_ATTACHMENT0, Ie.TEXTURE_CUBE_MAP_POSITIVE_X + t2, i3.__webglTexture, n2);
      } else if (o2) {
        const i3 = fe.get(e.texture), r2 = t2;
        Ie.framebufferTextureLayer(Ie.FRAMEBUFFER, Ie.COLOR_ATTACHMENT0, i3.__webglTexture, n2, r2);
      } else if (null !== e && 0 !== n2) {
        const t3 = fe.get(e.texture);
        Ie.framebufferTexture2D(Ie.FRAMEBUFFER, Ie.COLOR_ATTACHMENT0, Ie.TEXTURE_2D, t3.__webglTexture, n2);
      }
      y = -1;
    }, this.readRenderTargetPixels = function(e, t2, n2, i2, r, a2, o2) {
      if (!e || !e.isWebGLRenderTarget) return void console.error("THREE.WebGLRenderer.readRenderTargetPixels: renderTarget is not THREE.WebGLRenderTarget.");
      let s2 = fe.get(e).__webglFramebuffer;
      if (e.isWebGLCubeRenderTarget && void 0 !== o2 && (s2 = s2[o2]), s2) {
        de.bindFramebuffer(Ie.FRAMEBUFFER, s2);
        try {
          const o3 = e.texture, s3 = o3.format, l2 = o3.type;
          if (!ce.textureFormatReadable(s3)) return void console.error("THREE.WebGLRenderer.readRenderTargetPixels: renderTarget is not in RGBA or implementation defined format.");
          if (!ce.textureTypeReadable(l2)) return void console.error("THREE.WebGLRenderer.readRenderTargetPixels: renderTarget is not in UnsignedByteType or implementation defined type.");
          t2 >= 0 && t2 <= e.width - i2 && n2 >= 0 && n2 <= e.height - r && Ie.readPixels(t2, n2, i2, r, we.convert(s3), we.convert(l2), a2);
        } finally {
          const e2 = null !== D ? fe.get(D).__webglFramebuffer : null;
          de.bindFramebuffer(Ie.FRAMEBUFFER, e2);
        }
      }
    }, this.readRenderTargetPixelsAsync = async function(e, t2, n2, i2, r, a2, o2) {
      if (!e || !e.isWebGLRenderTarget) throw new Error("THREE.WebGLRenderer.readRenderTargetPixels: renderTarget is not THREE.WebGLRenderTarget.");
      let s2 = fe.get(e).__webglFramebuffer;
      if (e.isWebGLCubeRenderTarget && void 0 !== o2 && (s2 = s2[o2]), s2) {
        const o3 = e.texture, l2 = o3.format, c2 = o3.type;
        if (!ce.textureFormatReadable(l2)) throw new Error("THREE.WebGLRenderer.readRenderTargetPixelsAsync: renderTarget is not in RGBA or implementation defined format.");
        if (!ce.textureTypeReadable(c2)) throw new Error("THREE.WebGLRenderer.readRenderTargetPixelsAsync: renderTarget is not in UnsignedByteType or implementation defined type.");
        if (t2 >= 0 && t2 <= e.width - i2 && n2 >= 0 && n2 <= e.height - r) {
          de.bindFramebuffer(Ie.FRAMEBUFFER, s2);
          const e2 = Ie.createBuffer();
          Ie.bindBuffer(Ie.PIXEL_PACK_BUFFER, e2), Ie.bufferData(Ie.PIXEL_PACK_BUFFER, a2.byteLength, Ie.STREAM_READ), Ie.readPixels(t2, n2, i2, r, we.convert(l2), we.convert(c2), 0);
          const o4 = null !== D ? fe.get(D).__webglFramebuffer : null;
          de.bindFramebuffer(Ie.FRAMEBUFFER, o4);
          const d = Ie.fenceSync(Ie.SYNC_GPU_COMMANDS_COMPLETE, 0);
          return Ie.flush(), await _mpChunkDeps_three_build_three_core_min.ni(Ie, d, 4), Ie.bindBuffer(Ie.PIXEL_PACK_BUFFER, e2), Ie.getBufferSubData(Ie.PIXEL_PACK_BUFFER, 0, a2), Ie.deleteBuffer(e2), Ie.deleteSync(d), a2;
        }
        throw new Error("THREE.WebGLRenderer.readRenderTargetPixelsAsync: requested read bounds are out of range.");
      }
    }, this.copyFramebufferToTexture = function(e, t2 = null, n2 = 0) {
      true !== e.isTexture && (_mpChunkDeps_three_build_three_core_min.ri("WebGLRenderer: copyFramebufferToTexture function signature has changed."), t2 = arguments[0] || null, e = arguments[1]);
      const i2 = Math.pow(2, -n2), r = Math.floor(e.image.width * i2), a2 = Math.floor(e.image.height * i2), o2 = null !== t2 ? t2.x : 0, s2 = null !== t2 ? t2.y : 0;
      pe.setTexture2D(e, 0), Ie.copyTexSubImage2D(Ie.TEXTURE_2D, n2, 0, 0, o2, s2, r, a2), de.unbindTexture();
    };
    const nt = Ie.createFramebuffer(), it = Ie.createFramebuffer();
    this.copyTextureToTexture = function(e, t2, n2 = null, i2 = null, r = 0, a2 = null) {
      let o2, s2, l2, c2, d, u, f2, p2, m;
      true !== e.isTexture && (_mpChunkDeps_three_build_three_core_min.ri("WebGLRenderer: copyTextureToTexture function signature has changed."), i2 = arguments[0] || null, e = arguments[1], t2 = arguments[2], a2 = arguments[3] || 0, n2 = null), null === a2 && (0 !== r ? (_mpChunkDeps_three_build_three_core_min.ri("WebGLRenderer: copyTextureToTexture function signature has changed to support src and dst mipmap levels."), a2 = r, r = 0) : a2 = 0);
      const h2 = e.isCompressedTexture ? e.mipmaps[a2] : e.image;
      if (null !== n2) o2 = n2.max.x - n2.min.x, s2 = n2.max.y - n2.min.y, l2 = n2.isBox3 ? n2.max.z - n2.min.z : 1, c2 = n2.min.x, d = n2.min.y, u = n2.isBox3 ? n2.min.z : 0;
      else {
        const t3 = Math.pow(2, -r);
        o2 = Math.floor(h2.width * t3), s2 = Math.floor(h2.height * t3), l2 = e.isDataArrayTexture ? h2.depth : e.isData3DTexture ? Math.floor(h2.depth * t3) : 1, c2 = 0, d = 0, u = 0;
      }
      null !== i2 ? (f2 = i2.x, p2 = i2.y, m = i2.z) : (f2 = 0, p2 = 0, m = 0);
      const _2 = we.convert(t2.format), g = we.convert(t2.type);
      let v2;
      t2.isData3DTexture ? (pe.setTexture3D(t2, 0), v2 = Ie.TEXTURE_3D) : t2.isDataArrayTexture || t2.isCompressedArrayTexture ? (pe.setTexture2DArray(t2, 0), v2 = Ie.TEXTURE_2D_ARRAY) : (pe.setTexture2D(t2, 0), v2 = Ie.TEXTURE_2D), Ie.pixelStorei(Ie.UNPACK_FLIP_Y_WEBGL, t2.flipY), Ie.pixelStorei(Ie.UNPACK_PREMULTIPLY_ALPHA_WEBGL, t2.premultiplyAlpha), Ie.pixelStorei(Ie.UNPACK_ALIGNMENT, t2.unpackAlignment);
      const E2 = Ie.getParameter(Ie.UNPACK_ROW_LENGTH), S = Ie.getParameter(Ie.UNPACK_IMAGE_HEIGHT), T = Ie.getParameter(Ie.UNPACK_SKIP_PIXELS), M2 = Ie.getParameter(Ie.UNPACK_SKIP_ROWS), x2 = Ie.getParameter(Ie.UNPACK_SKIP_IMAGES);
      Ie.pixelStorei(Ie.UNPACK_ROW_LENGTH, h2.width), Ie.pixelStorei(Ie.UNPACK_IMAGE_HEIGHT, h2.height), Ie.pixelStorei(Ie.UNPACK_SKIP_PIXELS, c2), Ie.pixelStorei(Ie.UNPACK_SKIP_ROWS, d), Ie.pixelStorei(Ie.UNPACK_SKIP_IMAGES, u);
      const R2 = e.isDataArrayTexture || e.isData3DTexture, A2 = t2.isDataArrayTexture || t2.isData3DTexture;
      if (e.isDepthTexture) {
        const n3 = fe.get(e), i3 = fe.get(t2), h3 = fe.get(n3.__renderTarget), _3 = fe.get(i3.__renderTarget);
        de.bindFramebuffer(Ie.READ_FRAMEBUFFER, h3.__webglFramebuffer), de.bindFramebuffer(Ie.DRAW_FRAMEBUFFER, _3.__webglFramebuffer);
        for (let n4 = 0; n4 < l2; n4++) R2 && (Ie.framebufferTextureLayer(Ie.READ_FRAMEBUFFER, Ie.COLOR_ATTACHMENT0, fe.get(e).__webglTexture, r, u + n4), Ie.framebufferTextureLayer(Ie.DRAW_FRAMEBUFFER, Ie.COLOR_ATTACHMENT0, fe.get(t2).__webglTexture, a2, m + n4)), Ie.blitFramebuffer(c2, d, o2, s2, f2, p2, o2, s2, Ie.DEPTH_BUFFER_BIT, Ie.NEAREST);
        de.bindFramebuffer(Ie.READ_FRAMEBUFFER, null), de.bindFramebuffer(Ie.DRAW_FRAMEBUFFER, null);
      } else if (0 !== r || e.isRenderTargetTexture || fe.has(e)) {
        const n3 = fe.get(e), i3 = fe.get(t2);
        de.bindFramebuffer(Ie.READ_FRAMEBUFFER, nt), de.bindFramebuffer(Ie.DRAW_FRAMEBUFFER, it);
        for (let e2 = 0; e2 < l2; e2++) R2 ? Ie.framebufferTextureLayer(Ie.READ_FRAMEBUFFER, Ie.COLOR_ATTACHMENT0, n3.__webglTexture, r, u + e2) : Ie.framebufferTexture2D(Ie.READ_FRAMEBUFFER, Ie.COLOR_ATTACHMENT0, Ie.TEXTURE_2D, n3.__webglTexture, r), A2 ? Ie.framebufferTextureLayer(Ie.DRAW_FRAMEBUFFER, Ie.COLOR_ATTACHMENT0, i3.__webglTexture, a2, m + e2) : Ie.framebufferTexture2D(Ie.DRAW_FRAMEBUFFER, Ie.COLOR_ATTACHMENT0, Ie.TEXTURE_2D, i3.__webglTexture, a2), 0 !== r ? Ie.blitFramebuffer(c2, d, o2, s2, f2, p2, o2, s2, Ie.COLOR_BUFFER_BIT, Ie.NEAREST) : A2 ? Ie.copyTexSubImage3D(v2, a2, f2, p2, m + e2, c2, d, o2, s2) : Ie.copyTexSubImage2D(v2, a2, f2, p2, c2, d, o2, s2);
        de.bindFramebuffer(Ie.READ_FRAMEBUFFER, null), de.bindFramebuffer(Ie.DRAW_FRAMEBUFFER, null);
      } else A2 ? e.isDataTexture || e.isData3DTexture ? Ie.texSubImage3D(v2, a2, f2, p2, m, o2, s2, l2, _2, g, h2.data) : t2.isCompressedArrayTexture ? Ie.compressedTexSubImage3D(v2, a2, f2, p2, m, o2, s2, l2, _2, h2.data) : Ie.texSubImage3D(v2, a2, f2, p2, m, o2, s2, l2, _2, g, h2) : e.isDataTexture ? Ie.texSubImage2D(Ie.TEXTURE_2D, a2, f2, p2, o2, s2, _2, g, h2.data) : e.isCompressedTexture ? Ie.compressedTexSubImage2D(Ie.TEXTURE_2D, a2, f2, p2, h2.width, h2.height, _2, h2.data) : Ie.texSubImage2D(Ie.TEXTURE_2D, a2, f2, p2, o2, s2, _2, g, h2);
      Ie.pixelStorei(Ie.UNPACK_ROW_LENGTH, E2), Ie.pixelStorei(Ie.UNPACK_IMAGE_HEIGHT, S), Ie.pixelStorei(Ie.UNPACK_SKIP_PIXELS, T), Ie.pixelStorei(Ie.UNPACK_SKIP_ROWS, M2), Ie.pixelStorei(Ie.UNPACK_SKIP_IMAGES, x2), 0 === a2 && t2.generateMipmaps && Ie.generateMipmap(v2), de.unbindTexture();
    }, this.copyTextureToTexture3D = function(e, t2, n2 = null, i2 = null, r = 0) {
      return true !== e.isTexture && (_mpChunkDeps_three_build_three_core_min.ri("WebGLRenderer: copyTextureToTexture3D function signature has changed."), n2 = arguments[0] || null, i2 = arguments[1] || null, e = arguments[2], t2 = arguments[3], r = arguments[4] || 0), _mpChunkDeps_three_build_three_core_min.ri('WebGLRenderer: copyTextureToTexture3D function has been deprecated. Use "copyTextureToTexture" instead.'), this.copyTextureToTexture(e, t2, n2, i2, r);
    }, this.initRenderTarget = function(e) {
      void 0 === fe.get(e).__webglFramebuffer && pe.setupRenderTarget(e);
    }, this.initTexture = function(e) {
      e.isCubeTexture ? pe.setTextureCube(e, 0) : e.isData3DTexture ? pe.setTexture3D(e, 0) : e.isDataArrayTexture || e.isCompressedArrayTexture ? pe.setTexture2DArray(e, 0) : pe.setTexture2D(e, 0), de.unbindTexture();
    }, this.resetState = function() {
      P = 0, w = 0, D = null, de.reset(), De.reset();
    }, "undefined" != typeof __THREE_DEVTOOLS__ && __THREE_DEVTOOLS__.dispatchEvent(new THREEGlobals["CustomEvent"]("observe", { detail: this }));
  }
  get coordinateSystem() {
    return _mpChunkDeps_three_build_three_core_min.Os;
  }
  get outputColorSpace() {
    return this._outputColorSpace;
  }
  set outputColorSpace(e) {
    this._outputColorSpace = e;
    const t = this.getContext();
    t.drawingBufferColorspace = _mpChunkDeps_three_build_three_core_min.ui._getDrawingBufferColorSpace(e), t.unpackColorSpace = _mpChunkDeps_three_build_three_core_min.ui._getUnpackColorSpace();
  }
}
class WebGLRenderer extends fa {
  constructor(parameters) {
    if (!(parameters || {}).canvas) {
      _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.b.set("defaultCanvasContextType", "webgl2", true);
    }
    super(parameters);
    const originSetRenderTarget = this.setRenderTarget;
    this.setRenderTarget = function(...args) {
      originSetRenderTarget.apply(this, args);
      if (args[0]) {
        this.clear(false, true, false);
      }
    };
  }
}
exports.WebGLRenderer = WebGLRenderer;
exports.ei = ei;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/_mpChunkDeps/three/build/three.module.min.js.map
