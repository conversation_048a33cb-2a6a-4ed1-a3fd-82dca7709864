"use strict";
require("../../chunks/three/build/three.module.min.js");
const _mpChunkDeps_three_build_three_core_min = require("../../chunks/three/build/three.core.min.js");
class Floor extends _mpChunkDeps_three_build_three_core_min.Vn {
  constructor() {
    const geometry = new _mpChunkDeps_three_build_three_core_min.ul(4e3, 4e3, 10, 10);
    const planeMaterial = new _mpChunkDeps_three_build_three_core_min._l({
      roughness: 0.7,
      metalness: 1,
      color: 3355443,
      emissive: 0
    });
    super(geometry, planeMaterial);
    this.rotation.x = -1.57;
    this.receiveShadow = true;
    this.position.y = -100;
  }
}
exports.Floor = Floor;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/sub-pack-2/particles/3d/Floor.js.map
