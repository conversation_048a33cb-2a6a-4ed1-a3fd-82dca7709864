"use strict";
const _mpChunkDeps__minisheep_threePlatformAdapter_dist_threeOverride_jsm_utils_LocalAsset = require("../utils/LocalAsset.js");
let t, r, o;
const s = { env: { emscripten_notify_memory_growth: function(e) {
  o = new Uint8Array(r.exports.memory.buffer);
} } };
class Q {
  init() {
    return t || (t = THREEGlobals["WebAssembly"].instantiate(_mpChunkDeps__minisheep_threePlatformAdapter_dist_threeOverride_jsm_utils_LocalAsset.LocalAsset.resolve("wasm", "zstd_decoder.wasm"), s).then(this._init), t);
  }
  _init(e) {
    r = e.instance, s.env.emscripten_notify_memory_growth(0);
  }
  decode(e, t2 = 0) {
    if (!r) throw new Error("ZSTDDecoder: Await .init() before decoding.");
    const s2 = e.byteLength, n = r.exports.malloc(s2);
    o.set(e, n), t2 = t2 || Number(r.exports.ZSTD_findDecompressedSize(n, s2));
    const i = r.exports.malloc(t2), c = r.exports.ZSTD_decompress(i, t2, n, s2), m = o.slice(i, i + c);
    return r.exports.free(n), r.exports.free(i), m;
  }
}
exports.Q = Q;
//# sourceMappingURL=../../../../../../../../.sourcemap/mp-weixin/_mpChunkDeps/@minisheep/three-platform-adapter/dist/three-override/jsm/libs/zstddec.module.js.map
