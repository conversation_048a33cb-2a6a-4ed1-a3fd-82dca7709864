/**
 * @license
 * Copyright 2010-2024 Three.js Authors
 * SPDX-License-Identifier: MIT
 */
import{Color as e,Vector2 as t,Vector3 as r,Vector4 as s,Matrix3 as i,Matrix4 as n,<PERSON><PERSON><PERSON>patcher as o,MathUtils as a,WebGLCoordinateSystem as u,WebGPUCoordinateSystem as l,ColorManagement as d,SRGBTransfer as c,NoToneMapping as h,StaticDrawUsage as p,InterleavedBuffer as g,DynamicDrawUsage as m,InterleavedBufferAttribute as f,NoColorSpace as y,UnsignedIntType as b,IntType as x,BackSide as T,CubeReflectionMapping as _,CubeRefractionMapping as v,TangentSpaceNormalMap as N,ObjectSpaceNormalMap as S,InstancedInterleavedBuffer as A,InstancedBufferAttribute as R,DataArrayTexture as C,FloatType as E,FramebufferTexture as w,LinearMipmapLinearFilter as M,DepthTexture as B,Material as F,NormalBlending as U,PointsMaterial as P,LineBasicMaterial as I,LineDashedMaterial as D,NoBlending as L,MeshNormalMaterial as V,WebGLCubeRenderTarget as O,BoxGeometry as G,Mesh as k,Scene as z,LinearFilter as $,CubeCamera as H,CubeTexture as W,EquirectangularReflectionMapping as j,EquirectangularRefractionMapping as q,AddOperation as K,MixOperation as X,MultiplyOperation as Y,MeshBasicMaterial as Q,MeshLambertMaterial as Z,MeshPhongMaterial as J,Texture as ee,MeshStandardMaterial as te,MeshPhysicalMaterial as re,MeshToonMaterial as se,MeshMatcapMaterial as ie,SpriteMaterial as ne,ShadowMaterial as oe,Uint32BufferAttribute as ae,Uint16BufferAttribute as ue,DoubleSide as le,DepthStencilFormat as de,DepthFormat as ce,UnsignedInt248Type as he,UnsignedByteType as pe,RenderTarget as ge,Plane as me,Object3D as fe,HalfFloatType as ye,LinearMipMapLinearFilter as be,OrthographicCamera as xe,BufferGeometry as Te,Float32BufferAttribute as _e,BufferAttribute as ve,UVMapping as Ne,Euler as Se,LinearSRGBColorSpace as Ae,LessCompare as Re,VSMShadowMap as Ce,RGFormat as Ee,BasicShadowMap as we,SphereGeometry as Me,CubeUVReflectionMapping as Be,PerspectiveCamera as Fe,RGBAFormat as Ue,LinearMipmapNearestFilter as Pe,NearestMipmapLinearFilter as Ie,Float16BufferAttribute as De,REVISION as Le,SRGBColorSpace as Ve,PCFShadowMap as Oe,FrontSide as Ge,Frustum as ke,DataTexture as ze,RedIntegerFormat as $e,RedFormat as He,RGIntegerFormat as We,RGBIntegerFormat as je,RGBFormat as qe,RGBAIntegerFormat as Ke,UnsignedShortType as Xe,ByteType as Ye,ShortType as Qe,createCanvasElement as Ze,AddEquation as Je,SubtractEquation as et,ReverseSubtractEquation as tt,ZeroFactor as rt,OneFactor as st,SrcColorFactor as it,SrcAlphaFactor as nt,SrcAlphaSaturateFactor as ot,DstColorFactor as at,DstAlphaFactor as ut,OneMinusSrcColorFactor as lt,OneMinusSrcAlphaFactor as dt,OneMinusDstColorFactor as ct,OneMinusDstAlphaFactor as ht,CullFaceNone as pt,CullFaceBack as gt,CullFaceFront as mt,CustomBlending as ft,MultiplyBlending as yt,SubtractiveBlending as bt,AdditiveBlending as xt,NotEqualDepth as Tt,GreaterDepth as _t,GreaterEqualDepth as vt,EqualDepth as Nt,LessEqualDepth as St,LessDepth as At,AlwaysDepth as Rt,NeverDepth as Ct,UnsignedShort4444Type as Et,UnsignedShort5551Type as wt,UnsignedInt5999Type as Mt,AlphaFormat as Bt,LuminanceFormat as Ft,LuminanceAlphaFormat as Ut,RGB_S3TC_DXT1_Format as Pt,RGBA_S3TC_DXT1_Format as It,RGBA_S3TC_DXT3_Format as Dt,RGBA_S3TC_DXT5_Format as Lt,RGB_PVRTC_4BPPV1_Format as Vt,RGB_PVRTC_2BPPV1_Format as Ot,RGBA_PVRTC_4BPPV1_Format as Gt,RGBA_PVRTC_2BPPV1_Format as kt,RGB_ETC1_Format as zt,RGB_ETC2_Format as $t,RGBA_ETC2_EAC_Format as Ht,RGBA_ASTC_4x4_Format as Wt,RGBA_ASTC_5x4_Format as jt,RGBA_ASTC_5x5_Format as qt,RGBA_ASTC_6x5_Format as Kt,RGBA_ASTC_6x6_Format as Xt,RGBA_ASTC_8x5_Format as Yt,RGBA_ASTC_8x6_Format as Qt,RGBA_ASTC_8x8_Format as Zt,RGBA_ASTC_10x5_Format as Jt,RGBA_ASTC_10x6_Format as er,RGBA_ASTC_10x8_Format as tr,RGBA_ASTC_10x10_Format as rr,RGBA_ASTC_12x10_Format as sr,RGBA_ASTC_12x12_Format as ir,RGBA_BPTC_Format as nr,RED_RGTC1_Format as or,SIGNED_RED_RGTC1_Format as ar,RED_GREEN_RGTC2_Format as ur,SIGNED_RED_GREEN_RGTC2_Format as lr,RepeatWrapping as dr,ClampToEdgeWrapping as cr,MirroredRepeatWrapping as hr,NearestFilter as pr,NearestMipmapNearestFilter as gr,NeverCompare as mr,AlwaysCompare as fr,LessEqualCompare as yr,EqualCompare as br,GreaterEqualCompare as xr,GreaterCompare as Tr,NotEqualCompare as _r,warnOnce as vr,NotEqualStencilFunc as Nr,GreaterStencilFunc as Sr,GreaterEqualStencilFunc as Ar,EqualStencilFunc as Rr,LessEqualStencilFunc as Cr,LessStencilFunc as Er,AlwaysStencilFunc as wr,NeverStencilFunc as Mr,DecrementWrapStencilOp as Br,IncrementWrapStencilOp as Fr,DecrementStencilOp as Ur,IncrementStencilOp as Pr,InvertStencilOp as Ir,ReplaceStencilOp as Dr,ZeroStencilOp as Lr,KeepStencilOp as Vr,MaxEquation as Or,MinEquation as Gr,SpotLight as kr,PointLight as zr,DirectionalLight as $r,RectAreaLight as Hr,AmbientLight as Wr,HemisphereLight as jr,LightProbe as qr,LinearToneMapping as Kr,ReinhardToneMapping as Xr,CineonToneMapping as Yr,ACESFilmicToneMapping as Qr,AgXToneMapping as Zr,NeutralToneMapping as Jr,Group as es,Loader as ts,FileLoader as rs,MaterialLoader as ss,ObjectLoader as is}from"./three.core.min.js";export{AdditiveAnimationBlendMode,AnimationAction,AnimationClip,AnimationLoader,AnimationMixer,AnimationObjectGroup,AnimationUtils,ArcCurve,ArrayCamera,ArrowHelper,AttachedBindMode,Audio,AudioAnalyser,AudioContext,AudioListener,AudioLoader,AxesHelper,BasicDepthPacking,BatchedMesh,Bone,BooleanKeyframeTrack,Box2,Box3,Box3Helper,BoxHelper,BufferGeometryLoader,Cache,Camera,CameraHelper,CanvasTexture,CapsuleGeometry,CatmullRomCurve3,CircleGeometry,Clock,ColorKeyframeTrack,CompressedArrayTexture,CompressedCubeTexture,CompressedTexture,CompressedTextureLoader,ConeGeometry,ConstantAlphaFactor,ConstantColorFactor,Controls,CubeTextureLoader,CubicBezierCurve,CubicBezierCurve3,CubicInterpolant,CullFaceFrontBack,Curve,CurvePath,CustomToneMapping,CylinderGeometry,Cylindrical,Data3DTexture,DataTextureLoader,DataUtils,DefaultLoadingManager,DetachedBindMode,DirectionalLightHelper,DiscreteInterpolant,DodecahedronGeometry,DynamicCopyUsage,DynamicReadUsage,EdgesGeometry,EllipseCurve,ExtrudeGeometry,Fog,FogExp2,GLBufferAttribute,GLSL1,GLSL3,GridHelper,HemisphereLightHelper,IcosahedronGeometry,ImageBitmapLoader,ImageLoader,ImageUtils,InstancedBufferGeometry,InstancedMesh,Int16BufferAttribute,Int32BufferAttribute,Int8BufferAttribute,Interpolant,InterpolateDiscrete,InterpolateLinear,InterpolateSmooth,KeyframeTrack,LOD,LatheGeometry,Layers,Light,Line,Line3,LineCurve,LineCurve3,LineLoop,LineSegments,LinearInterpolant,LinearMipMapNearestFilter,LinearTransfer,LoaderUtils,LoadingManager,LoopOnce,LoopPingPong,LoopRepeat,MOUSE,Matrix2,MeshDepthMaterial,MeshDistanceMaterial,NearestMipMapLinearFilter,NearestMipMapNearestFilter,NormalAnimationBlendMode,NumberKeyframeTrack,OctahedronGeometry,OneMinusConstantAlphaFactor,OneMinusConstantColorFactor,PCFSoftShadowMap,Path,PlaneGeometry,PlaneHelper,PointLightHelper,Points,PolarGridHelper,PolyhedronGeometry,PositionalAudio,PropertyBinding,PropertyMixer,QuadraticBezierCurve,QuadraticBezierCurve3,Quaternion,QuaternionKeyframeTrack,QuaternionLinearInterpolant,RGBADepthPacking,RGBDepthPacking,RGB_BPTC_SIGNED_Format,RGB_BPTC_UNSIGNED_Format,RGDepthPacking,RawShaderMaterial,Ray,Raycaster,RenderTarget3D,RenderTargetArray,RingGeometry,ShaderMaterial,Shape,ShapeGeometry,ShapePath,ShapeUtils,Skeleton,SkeletonHelper,SkinnedMesh,Source,Sphere,Spherical,SphericalHarmonics3,SplineCurve,SpotLightHelper,Sprite,StaticCopyUsage,StaticReadUsage,StereoCamera,StreamCopyUsage,StreamDrawUsage,StreamReadUsage,StringKeyframeTrack,TOUCH,TetrahedronGeometry,TextureLoader,TextureUtils,TorusGeometry,TorusKnotGeometry,Triangle,TriangleFanDrawMode,TriangleStripDrawMode,TrianglesDrawMode,TubeGeometry,Uint8BufferAttribute,Uint8ClampedBufferAttribute,Uniform,UniformsGroup,VectorKeyframeTrack,VideoTexture,WebGL3DRenderTarget,WebGLArrayRenderTarget,WebGLRenderTarget,WireframeGeometry,WrapAroundEnding,ZeroCurvatureEnding,ZeroSlopeEnding}from"./three.core.min.js";const ns=["alphaMap","alphaTest","anisotropy","anisotropyMap","anisotropyRotation","aoMap","attenuationColor","attenuationDistance","bumpMap","clearcoat","clearcoatMap","clearcoatNormalMap","clearcoatNormalScale","clearcoatRoughness","color","dispersion","displacementMap","emissive","emissiveMap","envMap","gradientMap","ior","iridescence","iridescenceIOR","iridescenceMap","iridescenceThicknessMap","lightMap","map","matcap","metalness","metalnessMap","normalMap","normalScale","opacity","roughness","roughnessMap","sheen","sheenColor","sheenColorMap","sheenRoughnessMap","shininess","specular","specularColor","specularColorMap","specularIntensity","specularIntensityMap","specularMap","thickness","transmission","transmissionMap"];class os{constructor(e){this.renderObjects=new WeakMap,this.hasNode=this.containsNode(e),this.hasAnimation=!0===e.object.isSkinnedMesh,this.refreshUniforms=ns,this.renderId=0}firstInitialization(e){return!1===this.renderObjects.has(e)&&(this.getRenderObjectData(e),!0)}getRenderObjectData(e){let t=this.renderObjects.get(e);if(void 0===t){const{geometry:r,material:s,object:i}=e;if(t={material:this.getMaterialData(s),geometry:{attributes:this.getAttributesData(r.attributes),indexVersion:r.index?r.index.version:null,drawRange:{start:r.drawRange.start,count:r.drawRange.count}},worldMatrix:i.matrixWorld.clone()},i.center&&(t.center=i.center.clone()),i.morphTargetInfluences&&(t.morphTargetInfluences=i.morphTargetInfluences.slice()),null!==e.bundle&&(t.version=e.bundle.version),t.material.transmission>0){const{width:r,height:s}=e.context;t.bufferWidth=r,t.bufferHeight=s}this.renderObjects.set(e,t)}return t}getAttributesData(e){const t={};for(const r in e){const s=e[r];t[r]={version:s.version}}return t}containsNode(e){const t=e.material;for(const e in t)if(t[e]&&t[e].isNode)return!0;return null!==e.renderer.nodes.modelViewMatrix||null!==e.renderer.nodes.modelNormalViewMatrix}getMaterialData(e){const t={};for(const r of this.refreshUniforms){const s=e[r];null!=s&&("object"==typeof s&&void 0!==s.clone?!0===s.isTexture?t[r]={id:s.id,version:s.version}:t[r]=s.clone():t[r]=s)}return t}equals(e){const{object:t,material:r,geometry:s}=e,i=this.getRenderObjectData(e);if(!0!==i.worldMatrix.equals(t.matrixWorld))return i.worldMatrix.copy(t.matrixWorld),!1;const n=i.material;for(const e in n){const t=n[e],s=r[e];if(void 0!==t.equals){if(!1===t.equals(s))return t.copy(s),!1}else if(!0===s.isTexture){if(t.id!==s.id||t.version!==s.version)return t.id=s.id,t.version=s.version,!1}else if(t!==s)return n[e]=s,!1}if(n.transmission>0){const{width:t,height:r}=e.context;if(i.bufferWidth!==t||i.bufferHeight!==r)return i.bufferWidth=t,i.bufferHeight=r,!1}const o=i.geometry,a=s.attributes,u=o.attributes,l=Object.keys(u),d=Object.keys(a);if(l.length!==d.length)return i.geometry.attributes=this.getAttributesData(a),!1;for(const e of l){const t=u[e],r=a[e];if(void 0===r)return delete u[e],!1;if(t.version!==r.version)return t.version=r.version,!1}const c=s.index,h=o.indexVersion,p=c?c.version:null;if(h!==p)return o.indexVersion=p,!1;if(o.drawRange.start!==s.drawRange.start||o.drawRange.count!==s.drawRange.count)return o.drawRange.start=s.drawRange.start,o.drawRange.count=s.drawRange.count,!1;if(i.morphTargetInfluences){let e=!1;for(let r=0;r<i.morphTargetInfluences.length;r++)i.morphTargetInfluences[r]!==t.morphTargetInfluences[r]&&(e=!0);if(e)return!0}return i.center&&!1===i.center.equals(t.center)?(i.center.copy(t.center),!0):(null!==e.bundle&&(i.version=e.bundle.version),!0)}needsRefresh(e,t){if(this.hasNode||this.hasAnimation||this.firstInitialization(e))return!0;const{renderId:r}=t;if(this.renderId!==r)return this.renderId=r,!0;const s=!0===e.object.static,i=null!==e.bundle&&!0===e.bundle.static&&this.getRenderObjectData(e).version===e.bundle.version;if(s||i)return!1;return!0!==this.equals(e)}}function as(e,t=0){let r=3735928559^t,s=1103547991^t;if(e instanceof Array)for(let t,i=0;i<e.length;i++)t=e[i],r=Math.imul(r^t,2654435761),s=Math.imul(s^t,1597334677);else for(let t,i=0;i<e.length;i++)t=e.charCodeAt(i),r=Math.imul(r^t,2654435761),s=Math.imul(s^t,1597334677);return r=Math.imul(r^r>>>16,2246822507),r^=Math.imul(s^s>>>13,3266489909),s=Math.imul(s^s>>>16,2246822507),s^=Math.imul(r^r>>>13,3266489909),4294967296*(2097151&s)+(r>>>0)}const us=e=>as(e),ls=e=>as(e),ds=(...e)=>as(e);function cs(e,t=!1){const r=[];!0===e.isNode&&(r.push(e.id),e=e.getSelf());for(const{property:s,childNode:i}of hs(e))r.push(r,as(s.slice(0,-4)),i.getCacheKey(t));return as(r)}function*hs(e,t=!1){for(const r in e){if(!0===r.startsWith("_"))continue;const s=e[r];if(!0===Array.isArray(s))for(let e=0;e<s.length;e++){const i=s[e];i&&(!0===i.isNode||t&&"function"==typeof i.toJSON)&&(yield{property:r,index:e,childNode:i})}else if(s&&!0===s.isNode)yield{property:r,childNode:s};else if("object"==typeof s)for(const e in s){const i=s[e];i&&(!0===i.isNode||t&&"function"==typeof i.toJSON)&&(yield{property:r,index:e,childNode:i})}}}const ps=new Map([[1,"float"],[2,"vec2"],[3,"vec3"],[4,"vec4"],[9,"mat3"],[16,"mat4"]]),gs=new WeakMap;function ms(e){return ps.get(e)}function fs(e){if(/[iu]?vec\d/.test(e))return e.startsWith("ivec")?Int32Array:e.startsWith("uvec")?Uint32Array:Float32Array;if(/mat\d/.test(e))return Float32Array;if(/float/.test(e))return Float32Array;if(/uint/.test(e))return Uint32Array;if(/int/.test(e))return Int32Array;throw new Error(`THREE.NodeUtils: Unsupported type: ${e}`)}function ys(e){return/float|int|uint/.test(e)?1:/vec2/.test(e)?2:/vec3/.test(e)?3:/vec4/.test(e)?4:/mat3/.test(e)?9:/mat4/.test(e)?16:void console.error("THREE.TSL: Unsupported type:",e)}function bs(e){if(null==e)return null;const t=typeof e;return!0===e.isNode?"node":"number"===t?"float":"boolean"===t?"bool":"string"===t?"string":"function"===t?"shader":!0===e.isVector2?"vec2":!0===e.isVector3?"vec3":!0===e.isVector4?"vec4":!0===e.isMatrix3?"mat3":!0===e.isMatrix4?"mat4":!0===e.isColor?"color":e instanceof ArrayBuffer?"ArrayBuffer":null}function xs(o,...a){const u=o?o.slice(-4):void 0;return 1===a.length&&("vec2"===u?a=[a[0],a[0]]:"vec3"===u?a=[a[0],a[0],a[0]]:"vec4"===u&&(a=[a[0],a[0],a[0],a[0]])),"color"===o?new e(...a):"vec2"===u?new t(...a):"vec3"===u?new r(...a):"vec4"===u?new s(...a):"mat3"===u?new i(...a):"mat4"===u?new n(...a):"bool"===o?a[0]||!1:"float"===o||"int"===o||"uint"===o?a[0]||0:"string"===o?a[0]||"":"ArrayBuffer"===o?vs(a[0]):null}function Ts(e){let t=gs.get(e);return void 0===t&&(t={},gs.set(e,t)),t}function _s(e){let t="";const r=new Uint8Array(e);for(let e=0;e<r.length;e++)t+=String.fromCharCode(r[e]);return btoa(t)}function vs(e){return Uint8Array.from(atob(e),(e=>e.charCodeAt(0))).buffer}var Ns=Object.freeze({__proto__:null,arrayBufferToBase64:_s,base64ToArrayBuffer:vs,getCacheKey:cs,getDataFromObject:Ts,getLengthFromType:ys,getNodeChildren:hs,getTypeFromLength:ms,getTypedArrayFromType:fs,getValueFromType:xs,getValueType:bs,hash:ds,hashArray:ls,hashString:us});const Ss={VERTEX:"vertex",FRAGMENT:"fragment"},As={NONE:"none",FRAME:"frame",RENDER:"render",OBJECT:"object"},Rs={BOOLEAN:"bool",INTEGER:"int",FLOAT:"float",VECTOR2:"vec2",VECTOR3:"vec3",VECTOR4:"vec4",MATRIX2:"mat2",MATRIX3:"mat3",MATRIX4:"mat4"},Cs={READ_ONLY:"readOnly",WRITE_ONLY:"writeOnly",READ_WRITE:"readWrite"},Es=["fragment","vertex"],ws=["setup","analyze","generate"],Ms=[...Es,"compute"],Bs=["x","y","z","w"];let Fs=0;class Us extends o{static get type(){return"Node"}constructor(e=null){super(),this.nodeType=e,this.updateType=As.NONE,this.updateBeforeType=As.NONE,this.updateAfterType=As.NONE,this.uuid=a.generateUUID(),this.version=0,this.global=!1,this.isNode=!0,this._cacheKey=null,this._cacheKeyVersion=0,Object.defineProperty(this,"id",{value:Fs++})}set needsUpdate(e){!0===e&&this.version++}get type(){return this.constructor.type}onUpdate(e,t){return this.updateType=t,this.update=e.bind(this.getSelf()),this}onFrameUpdate(e){return this.onUpdate(e,As.FRAME)}onRenderUpdate(e){return this.onUpdate(e,As.RENDER)}onObjectUpdate(e){return this.onUpdate(e,As.OBJECT)}onReference(e){return this.updateReference=e.bind(this.getSelf()),this}getSelf(){return this.self||this}updateReference(){return this}isGlobal(){return this.global}*getChildren(){for(const{childNode:e}of hs(this))yield e}dispose(){this.dispatchEvent({type:"dispose"})}traverse(e){e(this);for(const t of this.getChildren())t.traverse(e)}getCacheKey(e=!1){return!0!==(e=e||this.version!==this._cacheKeyVersion)&&null!==this._cacheKey||(this._cacheKey=ds(cs(this,e),this.customCacheKey()),this._cacheKeyVersion=this.version),this._cacheKey}customCacheKey(){return 0}getScope(){return this}getHash(){return this.uuid}getUpdateType(){return this.updateType}getUpdateBeforeType(){return this.updateBeforeType}getUpdateAfterType(){return this.updateAfterType}getElementType(e){const t=this.getNodeType(e);return e.getElementType(t)}getNodeType(e){const t=e.getNodeProperties(this);return t.outputNode?t.outputNode.getNodeType(e):this.nodeType}getShared(e){const t=this.getHash(e);return e.getNodeFromHash(t)||this}setup(e){const t=e.getNodeProperties(this);let r=0;for(const e of this.getChildren())t["node"+r++]=e;return t.outputNode||null}analyze(e){if(1===e.increaseUsage(this)){const t=e.getNodeProperties(this);for(const r of Object.values(t))r&&!0===r.isNode&&r.build(e)}}generate(e,t){const{outputNode:r}=e.getNodeProperties(this);if(r&&!0===r.isNode)return r.build(e,t)}updateBefore(){console.warn("Abstract function.")}updateAfter(){console.warn("Abstract function.")}update(){console.warn("Abstract function.")}build(e,t=null){const r=this.getShared(e);if(this!==r)return r.build(e,t);e.addNode(this),e.addChain(this);let s=null;const i=e.getBuildStage();if("setup"===i){this.updateReference(e);const t=e.getNodeProperties(this);if(!0!==t.initialized){t.initialized=!0;const r=this.setup(e),s=r&&!0===r.isNode;for(const r of Object.values(t))r&&!0===r.isNode&&r.build(e);s&&r.build(e),t.outputNode=r}}else if("analyze"===i)this.analyze(e);else if("generate"===i){if(1===this.generate.length){const r=this.getNodeType(e),i=e.getDataFromNode(this);s=i.snippet,void 0===s?(s=this.generate(e)||"",i.snippet=s):void 0!==i.flowCodes&&void 0!==e.context.nodeBlock&&e.addFlowCodeHierarchy(this,e.context.nodeBlock),s=e.format(s,r,t)}else s=this.generate(e,t)||""}return e.removeChain(this),e.addSequentialNode(this),s}getSerializeChildren(){return hs(this)}serialize(e){const t=this.getSerializeChildren(),r={};for(const{property:s,index:i,childNode:n}of t)void 0!==i?(void 0===r[s]&&(r[s]=Number.isInteger(i)?[]:{}),r[s][i]=n.toJSON(e.meta).uuid):r[s]=n.toJSON(e.meta).uuid;Object.keys(r).length>0&&(e.inputNodes=r)}deserialize(e){if(void 0!==e.inputNodes){const t=e.meta.nodes;for(const r in e.inputNodes)if(Array.isArray(e.inputNodes[r])){const s=[];for(const i of e.inputNodes[r])s.push(t[i]);this[r]=s}else if("object"==typeof e.inputNodes[r]){const s={};for(const i in e.inputNodes[r]){const n=e.inputNodes[r][i];s[i]=t[n]}this[r]=s}else{const s=e.inputNodes[r];this[r]=t[s]}}}toJSON(e){const{uuid:t,type:r}=this,s=void 0===e||"string"==typeof e;s&&(e={textures:{},images:{},nodes:{}});let i=e.nodes[t];function n(e){const t=[];for(const r in e){const s=e[r];delete s.metadata,t.push(s)}return t}if(void 0===i&&(i={uuid:t,type:r,meta:e,metadata:{version:4.6,type:"Node",generator:"Node.toJSON"}},!0!==s&&(e.nodes[i.uuid]=i),this.serialize(i),delete i.meta),s){const t=n(e.textures),r=n(e.images),s=n(e.nodes);t.length>0&&(i.textures=t),r.length>0&&(i.images=r),s.length>0&&(i.nodes=s)}return i}}class Ps extends Us{static get type(){return"ArrayElementNode"}constructor(e,t){super(),this.node=e,this.indexNode=t,this.isArrayElementNode=!0}getNodeType(e){return this.node.getElementType(e)}generate(e){return`${this.node.build(e)}[ ${this.indexNode.build(e,"uint")} ]`}}class Is extends Us{static get type(){return"ConvertNode"}constructor(e,t){super(),this.node=e,this.convertTo=t}getNodeType(e){const t=this.node.getNodeType(e);let r=null;for(const s of this.convertTo.split("|"))null!==r&&e.getTypeLength(t)!==e.getTypeLength(s)||(r=s);return r}serialize(e){super.serialize(e),e.convertTo=this.convertTo}deserialize(e){super.deserialize(e),this.convertTo=e.convertTo}generate(e,t){const r=this.node,s=this.getNodeType(e),i=r.build(e,s);return e.format(i,s,t)}}class Ds extends Us{static get type(){return"TempNode"}constructor(e=null){super(e),this.isTempNode=!0}hasDependencies(e){return e.getDataFromNode(this).usageCount>1}build(e,t){if("generate"===e.getBuildStage()){const r=e.getVectorType(this.getNodeType(e,t)),s=e.getDataFromNode(this);if(void 0!==s.propertyName)return e.format(s.propertyName,r,t);if("void"!==r&&"void"!==t&&this.hasDependencies(e)){const i=super.build(e,r),n=e.getVarFromNode(this,null,r),o=e.getPropertyName(n);return e.addLineFlowCode(`${o} = ${i}`,this),s.snippet=i,s.propertyName=o,e.format(s.propertyName,r,t)}}return super.build(e,t)}}class Ls extends Ds{static get type(){return"JoinNode"}constructor(e=[],t=null){super(t),this.nodes=e}getNodeType(e){return null!==this.nodeType?e.getVectorType(this.nodeType):e.getTypeFromLength(this.nodes.reduce(((t,r)=>t+e.getTypeLength(r.getNodeType(e))),0))}generate(e,t){const r=this.getNodeType(e),s=this.nodes,i=e.getComponentType(r),n=[];for(const t of s){let r=t.build(e);const s=e.getComponentType(t.getNodeType(e));s!==i&&(r=e.format(r,s,i)),n.push(r)}const o=`${e.getType(r)}( ${n.join(", ")} )`;return e.format(o,r,t)}}const Vs=Bs.join("");class Os extends Us{static get type(){return"SplitNode"}constructor(e,t="x"){super(),this.node=e,this.components=t,this.isSplitNode=!0}getVectorLength(){let e=this.components.length;for(const t of this.components)e=Math.max(Bs.indexOf(t)+1,e);return e}getComponentType(e){return e.getComponentType(this.node.getNodeType(e))}getNodeType(e){return e.getTypeFromLength(this.components.length,this.getComponentType(e))}generate(e,t){const r=this.node,s=e.getTypeLength(r.getNodeType(e));let i=null;if(s>1){let n=null;this.getVectorLength()>=s&&(n=e.getTypeFromLength(this.getVectorLength(),this.getComponentType(e)));const o=r.build(e,n);i=this.components.length===s&&this.components===Vs.slice(0,this.components.length)?e.format(o,n,t):e.format(`${o}.${this.components}`,this.getNodeType(e),t)}else i=r.build(e,t);return i}serialize(e){super.serialize(e),e.components=this.components}deserialize(e){super.deserialize(e),this.components=e.components}}class Gs extends Ds{static get type(){return"SetNode"}constructor(e,t,r){super(),this.sourceNode=e,this.components=t,this.targetNode=r}getNodeType(e){return this.sourceNode.getNodeType(e)}generate(e){const{sourceNode:t,components:r,targetNode:s}=this,i=this.getNodeType(e),n=e.getComponentType(s.getNodeType(e)),o=e.getTypeFromLength(r.length,n),a=s.build(e,o),u=t.build(e,i),l=e.getTypeLength(i),d=[];for(let e=0;e<l;e++){const t=Bs[e];t===r[0]?(d.push(a),e+=r.length-1):d.push(u+"."+t)}return`${e.getType(i)}( ${d.join(", ")} )`}}class ks extends Ds{static get type(){return"FlipNode"}constructor(e,t){super(),this.sourceNode=e,this.components=t}getNodeType(e){return this.sourceNode.getNodeType(e)}generate(e){const{components:t,sourceNode:r}=this,s=this.getNodeType(e),i=r.build(e),n=e.getVarFromNode(this),o=e.getPropertyName(n);e.addLineFlowCode(o+" = "+i,this);const a=e.getTypeLength(s),u=[];let l=0;for(let e=0;e<a;e++){const r=Bs[e];r===t[l]?(u.push("1.0 - "+o+"."+r),l++):u.push(o+"."+r)}return`${e.getType(s)}( ${u.join(", ")} )`}}class zs extends Us{static get type(){return"InputNode"}constructor(e,t=null){super(t),this.isInputNode=!0,this.value=e,this.precision=null}getNodeType(){return null===this.nodeType?bs(this.value):this.nodeType}getInputType(e){return this.getNodeType(e)}setPrecision(e){return this.precision=e,this}serialize(e){super.serialize(e),e.value=this.value,this.value&&this.value.toArray&&(e.value=this.value.toArray()),e.valueType=bs(this.value),e.nodeType=this.nodeType,"ArrayBuffer"===e.valueType&&(e.value=_s(e.value)),e.precision=this.precision}deserialize(e){super.deserialize(e),this.nodeType=e.nodeType,this.value=Array.isArray(e.value)?xs(e.valueType,...e.value):e.value,this.precision=e.precision||null,this.value&&this.value.fromArray&&(this.value=this.value.fromArray(e.value))}generate(){console.warn("Abstract function.")}}class $s extends zs{static get type(){return"ConstNode"}constructor(e,t=null){super(e,t),this.isConstNode=!0}generateConst(e){return e.generateConst(this.getNodeType(e),this.value)}generate(e,t){const r=this.getNodeType(e);return e.format(this.generateConst(e),r,t)}}let Hs=null;const Ws=new Map;function js(e,t){if(Ws.has(e))console.warn(`Redefinition of method chaining ${e}`);else{if("function"!=typeof t)throw new Error(`Node element ${e} is not a function`);Ws.set(e,t)}}const qs=e=>e.replace(/r|s/g,"x").replace(/g|t/g,"y").replace(/b|p/g,"z").replace(/a|q/g,"w"),Ks=e=>qs(e).split("").sort().join(""),Xs={setup(e,t){const r=t.shift();return e(Ti(r),...t)},get(e,t,r){if("string"==typeof t&&void 0===e[t]){if(!0!==e.isStackNode&&"assign"===t)return(...e)=>(Hs.assign(r,...e),r);if(Ws.has(t)){const s=Ws.get(t);return e.isStackNode?(...e)=>r.add(s(...e)):(...e)=>s(r,...e)}if("self"===t)return e;if(t.endsWith("Assign")&&Ws.has(t.slice(0,t.length-6))){const s=Ws.get(t.slice(0,t.length-6));return e.isStackNode?(...e)=>r.assign(e[0],s(...e)):(...e)=>r.assign(s(r,...e))}if(!0===/^[xyzwrgbastpq]{1,4}$/.test(t))return t=qs(t),xi(new Os(r,t));if(!0===/^set[XYZWRGBASTPQ]{1,4}$/.test(t))return t=Ks(t.slice(3).toLowerCase()),r=>xi(new Gs(e,t,r));if(!0===/^flip[XYZWRGBASTPQ]{1,4}$/.test(t))return t=Ks(t.slice(4).toLowerCase()),()=>xi(new ks(xi(e),t));if("width"===t||"height"===t||"depth"===t)return"width"===t?t="x":"height"===t?t="y":"depth"===t&&(t="z"),xi(new Os(e,t));if(!0===/^\d+$/.test(t))return xi(new Ps(r,new $s(Number(t),"uint")))}return Reflect.get(e,t,r)},set:(e,t,r,s)=>"string"!=typeof t||void 0!==e[t]||!0!==/^[xyzwrgbastpq]{1,4}$/.test(t)&&"width"!==t&&"height"!==t&&"depth"!==t&&!0!==/^\d+$/.test(t)?Reflect.set(e,t,r,s):(s[t].assign(r),!0)},Ys=new WeakMap,Qs=new WeakMap,Zs=function(e,t=null){for(const r in e)e[r]=xi(e[r],t);return e},Js=function(e,t=null){const r=e.length;for(let s=0;s<r;s++)e[s]=xi(e[s],t);return e},ei=function(e,t=null,r=null,s=null){const i=e=>xi(null!==s?Object.assign(e,s):e);return null===t?(...t)=>i(new e(..._i(t))):null!==r?(r=xi(r),(...s)=>i(new e(t,..._i(s),r))):(...r)=>i(new e(t,..._i(r)))},ti=function(e,...t){return xi(new e(..._i(t)))};class ri extends Us{constructor(e,t){super(),this.shaderNode=e,this.inputNodes=t}getNodeType(e){return this.shaderNode.nodeType||this.getOutputNode(e).getNodeType(e)}call(e){const{shaderNode:t,inputNodes:r}=this,s=e.getNodeProperties(t);if(s.onceOutput)return s.onceOutput;let i=null;if(t.layout){let s=Qs.get(e.constructor);void 0===s&&(s=new WeakMap,Qs.set(e.constructor,s));let n=s.get(t);void 0===n&&(n=xi(e.buildFunctionNode(t)),s.set(t,n)),null!==e.currentFunctionNode&&e.currentFunctionNode.includes.push(n),i=xi(n.call(r))}else{const s=t.jsFunc,n=null!==r?s(r,e):s(e);i=xi(n)}return t.once&&(s.onceOutput=i),i}getOutputNode(e){const t=e.getNodeProperties(this);return null===t.outputNode&&(t.outputNode=this.setupOutput(e)),t.outputNode}setup(e){return this.getOutputNode(e)}setupOutput(e){return e.addStack(),e.stack.outputNode=this.call(e),e.removeStack()}generate(e,t){return this.getOutputNode(e).build(e,t)}}class si extends Us{constructor(e,t){super(t),this.jsFunc=e,this.layout=null,this.global=!0,this.once=!1}setLayout(e){return this.layout=e,this}call(e=null){return Ti(e),xi(new ri(this,e))}setup(){return this.call()}}const ii=[!1,!0],ni=[0,1,2,3],oi=[-1,-2],ai=[.5,1.5,1/3,1e-6,1e6,Math.PI,2*Math.PI,1/Math.PI,2/Math.PI,1/(2*Math.PI),Math.PI/2],ui=new Map;for(const e of ii)ui.set(e,new $s(e));const li=new Map;for(const e of ni)li.set(e,new $s(e,"uint"));const di=new Map([...li].map((e=>new $s(e.value,"int"))));for(const e of oi)di.set(e,new $s(e,"int"));const ci=new Map([...di].map((e=>new $s(e.value))));for(const e of ai)ci.set(e,new $s(e));for(const e of ai)ci.set(-e,new $s(-e));const hi={bool:ui,uint:li,ints:di,float:ci},pi=new Map([...ui,...ci]),gi=(e,t)=>pi.has(e)?pi.get(e):!0===e.isNode?e:new $s(e,t),mi=function(e,t=null){return(...r)=>{if((0===r.length||!["bool","float","int","uint"].includes(e)&&r.every((e=>"object"!=typeof e)))&&(r=[xs(e,...r)]),1===r.length&&null!==t&&t.has(r[0]))return xi(t.get(r[0]));if(1===r.length){const t=gi(r[0],e);return(e=>{try{return e.getNodeType()}catch(e){return}})(t)===e?xi(t):xi(new Is(t,e))}const s=r.map((e=>gi(e)));return xi(new Ls(s,e))}},fi=e=>"object"==typeof e&&null!==e?e.value:e,yi=e=>null!=e?e.nodeType||e.convertTo||("string"==typeof e?e:null):null;function bi(e,t){return new Proxy(new si(e,t),Xs)}const xi=(e,t=null)=>function(e,t=null){const r=bs(e);if("node"===r){let t=Ys.get(e);return void 0===t&&(t=new Proxy(e,Xs),Ys.set(e,t),Ys.set(t,t)),t}return null===t&&("float"===r||"boolean"===r)||r&&"shader"!==r&&"string"!==r?xi(gi(e,t)):"shader"===r?Si(e):e}(e,t),Ti=(e,t=null)=>new Zs(e,t),_i=(e,t=null)=>new Js(e,t),vi=(...e)=>new ei(...e),Ni=(...e)=>new ti(...e),Si=(e,t)=>{const r=new bi(e,t),s=(...e)=>{let t;return Ti(e),t=e[0]&&e[0].isNode?[...e]:e[0],r.call(t)};return s.shaderNode=r,s.setLayout=e=>(r.setLayout(e),s),s.once=()=>(r.once=!0,s),s};js("toGlobal",(e=>(e.global=!0,e)));const Ai=e=>{Hs=e},Ri=()=>Hs,Ci=(...e)=>Hs.If(...e);function Ei(e){return Hs&&Hs.add(e),e}js("append",Ei);const wi=new mi("color"),Mi=new mi("float",hi.float),Bi=new mi("int",hi.ints),Fi=new mi("uint",hi.uint),Ui=new mi("bool",hi.bool),Pi=new mi("vec2"),Ii=new mi("ivec2"),Di=new mi("uvec2"),Li=new mi("bvec2"),Vi=new mi("vec3"),Oi=new mi("ivec3"),Gi=new mi("uvec3"),ki=new mi("bvec3"),zi=new mi("vec4"),$i=new mi("ivec4"),Hi=new mi("uvec4"),Wi=new mi("bvec4"),ji=new mi("mat2"),qi=new mi("mat3"),Ki=new mi("mat4");js("toColor",wi),js("toFloat",Mi),js("toInt",Bi),js("toUint",Fi),js("toBool",Ui),js("toVec2",Pi),js("toIVec2",Ii),js("toUVec2",Di),js("toBVec2",Li),js("toVec3",Vi),js("toIVec3",Oi),js("toUVec3",Gi),js("toBVec3",ki),js("toVec4",zi),js("toIVec4",$i),js("toUVec4",Hi),js("toBVec4",Wi),js("toMat2",ji),js("toMat3",qi),js("toMat4",Ki);const Xi=vi(Ps),Yi=(e,t)=>xi(new Is(xi(e),t));js("element",Xi),js("convert",Yi);class Qi extends Us{static get type(){return"UniformGroupNode"}constructor(e,t=!1,r=1){super("string"),this.name=e,this.shared=t,this.order=r,this.isUniformGroup=!0}serialize(e){super.serialize(e),e.name=this.name,e.version=this.version,e.shared=this.shared}deserialize(e){super.deserialize(e),this.name=e.name,this.version=e.version,this.shared=e.shared}}const Zi=e=>new Qi(e),Ji=(e,t=0)=>new Qi(e,!0,t),en=Ji("frame"),tn=Ji("render"),rn=Zi("object");class sn extends zs{static get type(){return"UniformNode"}constructor(e,t=null){super(e,t),this.isUniformNode=!0,this.name="",this.groupNode=rn}label(e){return this.name=e,this}setGroup(e){return this.groupNode=e,this}getGroup(){return this.groupNode}getUniformHash(e){return this.getHash(e)}onUpdate(e,t){const r=this.getSelf();return e=e.bind(r),super.onUpdate((t=>{const s=e(t,r);void 0!==s&&(this.value=s)}),t)}generate(e,t){const r=this.getNodeType(e),s=this.getUniformHash(e);let i=e.getNodeFromHash(s);void 0===i&&(e.setHashNode(this,s),i=this);const n=i.getInputType(e),o=e.getUniformFromNode(i,n,e.shaderStage,this.name||e.context.label),a=e.getPropertyName(o);return void 0!==e.context.label&&delete e.context.label,e.format(a,r,t)}}const nn=(e,t)=>{const r=yi(t||e),s=e&&!0===e.isNode?e.node&&e.node.value||e.value:e;return xi(new sn(s,r))};class on extends Us{static get type(){return"PropertyNode"}constructor(e,t=null,r=!1){super(e),this.name=t,this.varying=r,this.isPropertyNode=!0}getHash(e){return this.name||super.getHash(e)}isGlobal(){return!0}generate(e){let t;return!0===this.varying?(t=e.getVaryingFromNode(this,this.name),t.needsInterpolation=!0):t=e.getVarFromNode(this,this.name),e.getPropertyName(t)}}const an=(e,t)=>xi(new on(e,t)),un=(e,t)=>xi(new on(e,t,!0)),ln=Ni(on,"vec4","DiffuseColor"),dn=Ni(on,"vec3","EmissiveColor"),cn=Ni(on,"float","Roughness"),hn=Ni(on,"float","Metalness"),pn=Ni(on,"float","Clearcoat"),gn=Ni(on,"float","ClearcoatRoughness"),mn=Ni(on,"vec3","Sheen"),fn=Ni(on,"float","SheenRoughness"),yn=Ni(on,"float","Iridescence"),bn=Ni(on,"float","IridescenceIOR"),xn=Ni(on,"float","IridescenceThickness"),Tn=Ni(on,"float","AlphaT"),_n=Ni(on,"float","Anisotropy"),vn=Ni(on,"vec3","AnisotropyT"),Nn=Ni(on,"vec3","AnisotropyB"),Sn=Ni(on,"color","SpecularColor"),An=Ni(on,"float","SpecularF90"),Rn=Ni(on,"float","Shininess"),Cn=Ni(on,"vec4","Output"),En=Ni(on,"float","dashSize"),wn=Ni(on,"float","gapSize"),Mn=Ni(on,"float","pointWidth"),Bn=Ni(on,"float","IOR"),Fn=Ni(on,"float","Transmission"),Un=Ni(on,"float","Thickness"),Pn=Ni(on,"float","AttenuationDistance"),In=Ni(on,"color","AttenuationColor"),Dn=Ni(on,"float","Dispersion");class Ln extends Ds{static get type(){return"AssignNode"}constructor(e,t){super(),this.targetNode=e,this.sourceNode=t}hasDependencies(){return!1}getNodeType(e,t){return"void"!==t?this.targetNode.getNodeType(e):"void"}needsSplitAssign(e){const{targetNode:t}=this;if(!1===e.isAvailable("swizzleAssign")&&t.isSplitNode&&t.components.length>1){const r=e.getTypeLength(t.node.getNodeType(e));return Bs.join("").slice(0,r)!==t.components}return!1}generate(e,t){const{targetNode:r,sourceNode:s}=this,i=this.needsSplitAssign(e),n=r.getNodeType(e),o=r.context({assign:!0}).build(e),a=s.build(e,n),u=s.getNodeType(e),l=e.getDataFromNode(this);let d;if(!0===l.initialized)"void"!==t&&(d=o);else if(i){const s=e.getVarFromNode(this,null,n),i=e.getPropertyName(s);e.addLineFlowCode(`${i} = ${a}`,this);const u=r.node.context({assign:!0}).build(e);for(let t=0;t<r.components.length;t++){const s=r.components[t];e.addLineFlowCode(`${u}.${s} = ${i}[ ${t} ]`,this)}"void"!==t&&(d=o)}else d=`${o} = ${a}`,"void"!==t&&"void"!==u||(e.addLineFlowCode(d,this),"void"!==t&&(d=o));return l.initialized=!0,e.format(d,n,t)}}const Vn=vi(Ln);js("assign",Vn);class On extends Ds{static get type(){return"FunctionCallNode"}constructor(e=null,t={}){super(),this.functionNode=e,this.parameters=t}setParameters(e){return this.parameters=e,this}getParameters(){return this.parameters}getNodeType(e){return this.functionNode.getNodeType(e)}generate(e){const t=[],r=this.functionNode,s=r.getInputs(e),i=this.parameters,n=(t,r)=>{const s=r.type;let i;return i="pointer"===s?"&"+t.build(e):t.build(e,s),i};if(Array.isArray(i))for(let e=0;e<i.length;e++)t.push(n(i[e],s[e]));else for(const e of s){const r=i[e.name];if(void 0===r)throw new Error(`FunctionCallNode: Input '${e.name}' not found in FunctionNode.`);t.push(n(r,e))}return`${r.build(e,"property")}( ${t.join(", ")} )`}}const Gn=(e,...t)=>(t=t.length>1||t[0]&&!0===t[0].isNode?_i(t):Ti(t[0]),xi(new On(xi(e),t)));js("call",Gn);class kn extends Ds{static get type(){return"OperatorNode"}constructor(e,t,r,...s){if(super(),s.length>0){let i=new kn(e,t,r);for(let t=0;t<s.length-1;t++)i=new kn(e,i,s[t]);t=i,r=s[s.length-1]}this.op=e,this.aNode=t,this.bNode=r}getNodeType(e,t){const r=this.op,s=this.aNode,i=this.bNode,n=s.getNodeType(e),o=void 0!==i?i.getNodeType(e):null;if("void"===n||"void"===o)return"void";if("%"===r)return n;if("~"===r||"&"===r||"|"===r||"^"===r||">>"===r||"<<"===r)return e.getIntegerType(n);if("!"===r||"=="===r||"&&"===r||"||"===r||"^^"===r)return"bool";if("<"===r||">"===r||"<="===r||">="===r){const r=t?e.getTypeLength(t):Math.max(e.getTypeLength(n),e.getTypeLength(o));return r>1?`bvec${r}`:"bool"}return"float"===n&&e.isMatrix(o)?o:e.isMatrix(n)&&e.isVector(o)?e.getVectorFromMatrix(n):e.isVector(n)&&e.isMatrix(o)?e.getVectorFromMatrix(o):e.getTypeLength(o)>e.getTypeLength(n)?o:n}generate(e,t){const r=this.op,s=this.aNode,i=this.bNode,n=this.getNodeType(e,t);let o=null,a=null;"void"!==n?(o=s.getNodeType(e),a=void 0!==i?i.getNodeType(e):null,"<"===r||">"===r||"<="===r||">="===r||"=="===r?e.isVector(o)?a=o:o!==a&&(o=a="float"):">>"===r||"<<"===r?(o=n,a=e.changeComponentType(a,"uint")):e.isMatrix(o)&&e.isVector(a)?a=e.getVectorFromMatrix(o):o=e.isVector(o)&&e.isMatrix(a)?e.getVectorFromMatrix(a):a=n):o=a=n;const u=s.build(e,o),l=void 0!==i?i.build(e,a):null,d=e.getTypeLength(t),c=e.getFunctionOperator(r);return"void"!==t?"<"===r&&d>1?e.useComparisonMethod?e.format(`${e.getMethod("lessThan",t)}( ${u}, ${l} )`,n,t):e.format(`( ${u} < ${l} )`,n,t):"<="===r&&d>1?e.useComparisonMethod?e.format(`${e.getMethod("lessThanEqual",t)}( ${u}, ${l} )`,n,t):e.format(`( ${u} <= ${l} )`,n,t):">"===r&&d>1?e.useComparisonMethod?e.format(`${e.getMethod("greaterThan",t)}( ${u}, ${l} )`,n,t):e.format(`( ${u} > ${l} )`,n,t):">="===r&&d>1?e.useComparisonMethod?e.format(`${e.getMethod("greaterThanEqual",t)}( ${u}, ${l} )`,n,t):e.format(`( ${u} >= ${l} )`,n,t):"!"===r||"~"===r?e.format(`(${r}${u})`,o,t):c?e.format(`${c}( ${u}, ${l} )`,n,t):e.format(`( ${u} ${r} ${l} )`,n,t):"void"!==o?c?e.format(`${c}( ${u}, ${l} )`,n,t):e.format(`${u} ${r} ${l}`,n,t):void 0}serialize(e){super.serialize(e),e.op=this.op}deserialize(e){super.deserialize(e),this.op=e.op}}const zn=vi(kn,"+"),$n=vi(kn,"-"),Hn=vi(kn,"*"),Wn=vi(kn,"/"),jn=vi(kn,"%"),qn=vi(kn,"=="),Kn=vi(kn,"!="),Xn=vi(kn,"<"),Yn=vi(kn,">"),Qn=vi(kn,"<="),Zn=vi(kn,">="),Jn=vi(kn,"&&"),eo=vi(kn,"||"),to=vi(kn,"!"),ro=vi(kn,"^^"),so=vi(kn,"&"),io=vi(kn,"~"),no=vi(kn,"|"),oo=vi(kn,"^"),ao=vi(kn,"<<"),uo=vi(kn,">>");js("add",zn),js("sub",$n),js("mul",Hn),js("div",Wn),js("modInt",jn),js("equal",qn),js("notEqual",Kn),js("lessThan",Xn),js("greaterThan",Yn),js("lessThanEqual",Qn),js("greaterThanEqual",Zn),js("and",Jn),js("or",eo),js("not",to),js("xor",ro),js("bitAnd",so),js("bitNot",io),js("bitOr",no),js("bitXor",oo),js("shiftLeft",ao),js("shiftRight",uo);const lo=(...e)=>(console.warn("TSL.OperatorNode: .remainder() has been renamed to .modInt()."),jn(...e));js("remainder",lo);class co extends Ds{static get type(){return"MathNode"}constructor(e,t,r=null,s=null){super(),this.method=e,this.aNode=t,this.bNode=r,this.cNode=s}getInputType(e){const t=this.aNode.getNodeType(e),r=this.bNode?this.bNode.getNodeType(e):null,s=this.cNode?this.cNode.getNodeType(e):null,i=e.isMatrix(t)?0:e.getTypeLength(t),n=e.isMatrix(r)?0:e.getTypeLength(r),o=e.isMatrix(s)?0:e.getTypeLength(s);return i>n&&i>o?t:n>o?r:o>i?s:t}getNodeType(e){const t=this.method;return t===co.LENGTH||t===co.DISTANCE||t===co.DOT?"float":t===co.CROSS?"vec3":t===co.ALL?"bool":t===co.EQUALS?e.changeComponentType(this.aNode.getNodeType(e),"bool"):t===co.MOD?this.aNode.getNodeType(e):this.getInputType(e)}generate(e,t){let r=this.method;const s=this.getNodeType(e),i=this.getInputType(e),n=this.aNode,o=this.bNode,a=this.cNode,d=e.renderer.coordinateSystem;if(r===co.TRANSFORM_DIRECTION){let r=n,s=o;e.isMatrix(r.getNodeType(e))?s=zi(Vi(s),0):r=zi(Vi(r),0);const i=Hn(r,s).xyz;return Eo(i).build(e,t)}if(r===co.NEGATE)return e.format("( - "+n.build(e,i)+" )",s,t);if(r===co.ONE_MINUS)return $n(1,n).build(e,t);if(r===co.RECIPROCAL)return Wn(1,n).build(e,t);if(r===co.DIFFERENCE)return Do($n(n,o)).build(e,t);{const c=[];return r===co.CROSS||r===co.MOD?c.push(n.build(e,s),o.build(e,s)):d===u&&r===co.STEP?c.push(n.build(e,1===e.getTypeLength(n.getNodeType(e))?"float":i),o.build(e,i)):d===u&&(r===co.MIN||r===co.MAX)||r===co.MOD?c.push(n.build(e,i),o.build(e,1===e.getTypeLength(o.getNodeType(e))?"float":i)):r===co.REFRACT?c.push(n.build(e,i),o.build(e,i),a.build(e,"float")):r===co.MIX?c.push(n.build(e,i),o.build(e,i),a.build(e,1===e.getTypeLength(a.getNodeType(e))?"float":i)):(d===l&&r===co.ATAN&&null!==o&&(r="atan2"),c.push(n.build(e,i)),null!==o&&c.push(o.build(e,i)),null!==a&&c.push(a.build(e,i))),e.format(`${e.getMethod(r,s)}( ${c.join(", ")} )`,s,t)}}serialize(e){super.serialize(e),e.method=this.method}deserialize(e){super.deserialize(e),this.method=e.method}}co.ALL="all",co.ANY="any",co.RADIANS="radians",co.DEGREES="degrees",co.EXP="exp",co.EXP2="exp2",co.LOG="log",co.LOG2="log2",co.SQRT="sqrt",co.INVERSE_SQRT="inversesqrt",co.FLOOR="floor",co.CEIL="ceil",co.NORMALIZE="normalize",co.FRACT="fract",co.SIN="sin",co.COS="cos",co.TAN="tan",co.ASIN="asin",co.ACOS="acos",co.ATAN="atan",co.ABS="abs",co.SIGN="sign",co.LENGTH="length",co.NEGATE="negate",co.ONE_MINUS="oneMinus",co.DFDX="dFdx",co.DFDY="dFdy",co.ROUND="round",co.RECIPROCAL="reciprocal",co.TRUNC="trunc",co.FWIDTH="fwidth",co.TRANSPOSE="transpose",co.BITCAST="bitcast",co.EQUALS="equals",co.MIN="min",co.MAX="max",co.MOD="mod",co.STEP="step",co.REFLECT="reflect",co.DISTANCE="distance",co.DIFFERENCE="difference",co.DOT="dot",co.CROSS="cross",co.POW="pow",co.TRANSFORM_DIRECTION="transformDirection",co.MIX="mix",co.CLAMP="clamp",co.REFRACT="refract",co.SMOOTHSTEP="smoothstep",co.FACEFORWARD="faceforward";const ho=Mi(1e-6),po=Mi(1e6),go=Mi(Math.PI),mo=Mi(2*Math.PI),fo=vi(co,co.ALL),yo=vi(co,co.ANY),bo=vi(co,co.RADIANS),xo=vi(co,co.DEGREES),To=vi(co,co.EXP),_o=vi(co,co.EXP2),vo=vi(co,co.LOG),No=vi(co,co.LOG2),So=vi(co,co.SQRT),Ao=vi(co,co.INVERSE_SQRT),Ro=vi(co,co.FLOOR),Co=vi(co,co.CEIL),Eo=vi(co,co.NORMALIZE),wo=vi(co,co.FRACT),Mo=vi(co,co.SIN),Bo=vi(co,co.COS),Fo=vi(co,co.TAN),Uo=vi(co,co.ASIN),Po=vi(co,co.ACOS),Io=vi(co,co.ATAN),Do=vi(co,co.ABS),Lo=vi(co,co.SIGN),Vo=vi(co,co.LENGTH),Oo=vi(co,co.NEGATE),Go=vi(co,co.ONE_MINUS),ko=vi(co,co.DFDX),zo=vi(co,co.DFDY),$o=vi(co,co.ROUND),Ho=vi(co,co.RECIPROCAL),Wo=vi(co,co.TRUNC),jo=vi(co,co.FWIDTH),qo=vi(co,co.TRANSPOSE),Ko=vi(co,co.BITCAST),Xo=vi(co,co.EQUALS),Yo=vi(co,co.MIN),Qo=vi(co,co.MAX),Zo=vi(co,co.MOD),Jo=vi(co,co.STEP),ea=vi(co,co.REFLECT),ta=vi(co,co.DISTANCE),ra=vi(co,co.DIFFERENCE),sa=vi(co,co.DOT),ia=vi(co,co.CROSS),na=vi(co,co.POW),oa=vi(co,co.POW,2),aa=vi(co,co.POW,3),ua=vi(co,co.POW,4),la=vi(co,co.TRANSFORM_DIRECTION),da=e=>Hn(Lo(e),na(Do(e),1/3)),ca=e=>sa(e,e),ha=vi(co,co.MIX),pa=(e,t=0,r=1)=>xi(new co(co.CLAMP,xi(e),xi(t),xi(r))),ga=e=>pa(e),ma=vi(co,co.REFRACT),fa=vi(co,co.SMOOTHSTEP),ya=vi(co,co.FACEFORWARD),ba=Si((([e])=>{const t=sa(e.xy,Pi(12.9898,78.233)),r=Zo(t,go);return wo(Mo(r).mul(43758.5453))})),xa=(e,t,r)=>ha(t,r,e),Ta=(e,t,r)=>fa(t,r,e),_a=(e,t)=>(console.warn('THREE.TSL: "atan2" is overloaded. Use "atan" instead.'),Io(e,t)),va=ya,Na=Ao;js("all",fo),js("any",yo),js("equals",Xo),js("radians",bo),js("degrees",xo),js("exp",To),js("exp2",_o),js("log",vo),js("log2",No),js("sqrt",So),js("inverseSqrt",Ao),js("floor",Ro),js("ceil",Co),js("normalize",Eo),js("fract",wo),js("sin",Mo),js("cos",Bo),js("tan",Fo),js("asin",Uo),js("acos",Po),js("atan",Io),js("abs",Do),js("sign",Lo),js("length",Vo),js("lengthSq",ca),js("negate",Oo),js("oneMinus",Go),js("dFdx",ko),js("dFdy",zo),js("round",$o),js("reciprocal",Ho),js("trunc",Wo),js("fwidth",jo),js("atan2",_a),js("min",Yo),js("max",Qo),js("mod",Zo),js("step",Jo),js("reflect",ea),js("distance",ta),js("dot",sa),js("cross",ia),js("pow",na),js("pow2",oa),js("pow3",aa),js("pow4",ua),js("transformDirection",la),js("mix",xa),js("clamp",pa),js("refract",ma),js("smoothstep",Ta),js("faceForward",ya),js("difference",ra),js("saturate",ga),js("cbrt",da),js("transpose",qo),js("rand",ba);class Sa extends Us{static get type(){return"ConditionalNode"}constructor(e,t,r=null){super(),this.condNode=e,this.ifNode=t,this.elseNode=r}getNodeType(e){const{ifNode:t,elseNode:r}=e.getNodeProperties(this);if(void 0===t)return this.setup(e),this.getNodeType(e);const s=t.getNodeType(e);if(null!==r){const t=r.getNodeType(e);if(e.getTypeLength(t)>e.getTypeLength(s))return t}return s}setup(e){const t=this.condNode.cache(),r=this.ifNode.cache(),s=this.elseNode?this.elseNode.cache():null,i=e.context.nodeBlock;e.getDataFromNode(r).parentNodeBlock=i,null!==s&&(e.getDataFromNode(s).parentNodeBlock=i);const n=e.getNodeProperties(this);n.condNode=t,n.ifNode=r.context({nodeBlock:r}),n.elseNode=s?s.context({nodeBlock:s}):null}generate(e,t){const r=this.getNodeType(e),s=e.getDataFromNode(this);if(void 0!==s.nodeProperty)return s.nodeProperty;const{condNode:i,ifNode:n,elseNode:o}=e.getNodeProperties(this),a="void"!==t,u=a?an(r).build(e):"";s.nodeProperty=u;const l=i.build(e,"bool");e.addFlowCode(`\n${e.tab}if ( ${l} ) {\n\n`).addFlowTab();let d=n.build(e,r);if(d&&(d=a?u+" = "+d+";":"return "+d+";"),e.removeFlowTab().addFlowCode(e.tab+"\t"+d+"\n\n"+e.tab+"}"),null!==o){e.addFlowCode(" else {\n\n").addFlowTab();let t=o.build(e,r);t&&(t=a?u+" = "+t+";":"return "+t+";"),e.removeFlowTab().addFlowCode(e.tab+"\t"+t+"\n\n"+e.tab+"}\n\n")}else e.addFlowCode("\n\n");return e.format(u,r,t)}}const Aa=vi(Sa);js("select",Aa);const Ra=(...e)=>(console.warn("TSL.ConditionalNode: cond() has been renamed to select()."),Aa(...e));js("cond",Ra);class Ca extends Us{static get type(){return"ContextNode"}constructor(e,t={}){super(),this.isContextNode=!0,this.node=e,this.value=t}getScope(){return this.node.getScope()}getNodeType(e){return this.node.getNodeType(e)}analyze(e){this.node.build(e)}setup(e){const t=e.getContext();e.setContext({...e.context,...this.value});const r=this.node.build(e);return e.setContext(t),r}generate(e,t){const r=e.getContext();e.setContext({...e.context,...this.value});const s=this.node.build(e,t);return e.setContext(r),s}}const Ea=vi(Ca),wa=(e,t)=>Ea(e,{label:t});js("context",Ea),js("label",wa);class Ma extends Us{static get type(){return"VarNode"}constructor(e,t=null){super(),this.node=e,this.name=t,this.global=!0,this.isVarNode=!0}getHash(e){return this.name||super.getHash(e)}getNodeType(e){return this.node.getNodeType(e)}generate(e){const{node:t,name:r}=this,s=e.getVarFromNode(this,r,e.getVectorType(this.getNodeType(e))),i=e.getPropertyName(s),n=t.build(e,s.type);return e.addLineFlowCode(`${i} = ${n}`,this),i}}const Ba=vi(Ma);js("toVar",((...e)=>Ba(...e).append()));const Fa=e=>(console.warn('TSL: "temp" is deprecated. Use ".toVar()" instead.'),Ba(e));js("temp",Fa);class Ua extends Us{static get type(){return"VaryingNode"}constructor(e,t=null){super(),this.node=e,this.name=t,this.isVaryingNode=!0}isGlobal(){return!0}getHash(e){return this.name||super.getHash(e)}getNodeType(e){return this.node.getNodeType(e)}setupVarying(e){const t=e.getNodeProperties(this);let r=t.varying;if(void 0===r){const s=this.name,i=this.getNodeType(e);t.varying=r=e.getVaryingFromNode(this,s,i),t.node=this.node}return r.needsInterpolation||(r.needsInterpolation="fragment"===e.shaderStage),r}setup(e){this.setupVarying(e)}analyze(e){return this.setupVarying(e),this.node.analyze(e)}generate(e){const t=e.getNodeProperties(this),r=this.setupVarying(e),s="fragment"===e.shaderStage&&!0===t.reassignPosition&&e.context.needsPositionReassign;if(void 0===t.propertyName||s){const i=this.getNodeType(e),n=e.getPropertyName(r,Ss.VERTEX);e.flowNodeFromShaderStage(Ss.VERTEX,this.node,i,n),t.propertyName=n,s?t.reassignPosition=!1:void 0===t.reassignPosition&&e.context.isPositionNodeInput&&(t.reassignPosition=!0)}return e.getPropertyName(r)}}const Pa=vi(Ua),Ia=e=>Pa(e);js("varying",Pa),js("vertexStage",Ia);const Da=Si((([e])=>{const t=e.mul(.9478672986).add(.0521327014).pow(2.4),r=e.mul(.0773993808),s=e.lessThanEqual(.04045);return ha(t,r,s)})).setLayout({name:"sRGBTransferEOTF",type:"vec3",inputs:[{name:"color",type:"vec3"}]}),La=Si((([e])=>{const t=e.pow(.41666).mul(1.055).sub(.055),r=e.mul(12.92),s=e.lessThanEqual(.0031308);return ha(t,r,s)})).setLayout({name:"sRGBTransferOETF",type:"vec3",inputs:[{name:"color",type:"vec3"}]}),Va="WorkingColorSpace",Oa="OutputColorSpace";class Ga extends Ds{static get type(){return"ColorSpaceNode"}constructor(e,t,r){super("vec4"),this.colorNode=e,this.source=t,this.target=r}resolveColorSpace(e,t){return t===Va?d.workingColorSpace:t===Oa?e.context.outputColorSpace||e.renderer.outputColorSpace:t}setup(e){const{colorNode:t}=this,r=this.resolveColorSpace(e,this.source),s=this.resolveColorSpace(e,this.target);let n=t;return!1!==d.enabled&&r!==s&&r&&s?(d.getTransfer(r)===c&&(n=zi(Da(n.rgb),n.a)),d.getPrimaries(r)!==d.getPrimaries(s)&&(n=zi(qi(d._getMatrix(new i,r,s)).mul(n.rgb),n.a)),d.getTransfer(s)===c&&(n=zi(La(n.rgb),n.a)),n):n}}const ka=e=>xi(new Ga(xi(e),Va,Oa)),za=e=>xi(new Ga(xi(e),Oa,Va)),$a=(e,t)=>xi(new Ga(xi(e),Va,t)),Ha=(e,t)=>xi(new Ga(xi(e),t,Va));js("toOutputColorSpace",ka),js("toWorkingColorSpace",za),js("workingToColorSpace",$a),js("colorSpaceToWorking",Ha);let Wa=class extends Ps{static get type(){return"ReferenceElementNode"}constructor(e,t){super(e,t),this.referenceNode=e,this.isReferenceElementNode=!0}getNodeType(){return this.referenceNode.uniformType}generate(e){const t=super.generate(e),r=this.referenceNode.getNodeType(),s=this.getNodeType();return e.format(t,r,s)}};class ja extends Us{static get type(){return"ReferenceBaseNode"}constructor(e,t,r=null,s=null){super(),this.property=e,this.uniformType=t,this.object=r,this.count=s,this.properties=e.split("."),this.reference=r,this.node=null,this.group=null,this.updateType=As.OBJECT}setGroup(e){return this.group=e,this}element(e){return xi(new Wa(this,xi(e)))}setNodeType(e){const t=nn(null,e).getSelf();null!==this.group&&t.setGroup(this.group),this.node=t}getNodeType(e){return null===this.node&&(this.updateReference(e),this.updateValue()),this.node.getNodeType(e)}getValueFromReference(e=this.reference){const{properties:t}=this;let r=e[t[0]];for(let e=1;e<t.length;e++)r=r[t[e]];return r}updateReference(e){return this.reference=null!==this.object?this.object:e.object,this.reference}setup(){return this.updateValue(),this.node}update(){this.updateValue()}updateValue(){null===this.node&&this.setNodeType(this.uniformType);const e=this.getValueFromReference();Array.isArray(e)?this.node.array=e:this.node.value=e}}class qa extends ja{static get type(){return"RendererReferenceNode"}constructor(e,t,r=null){super(e,t,r),this.renderer=r,this.setGroup(tn)}updateReference(e){return this.reference=null!==this.renderer?this.renderer:e.renderer,this.reference}}const Ka=(e,t,r=null)=>xi(new qa(e,t,r));class Xa extends Ds{static get type(){return"ToneMappingNode"}constructor(e,t=Qa,r=null){super("vec3"),this.toneMapping=e,this.exposureNode=t,this.colorNode=r}customCacheKey(){return ds(this.toneMapping)}setup(e){const t=this.colorNode||e.context.color,r=this.toneMapping;if(r===h)return t;let s=null;const i=e.renderer.library.getToneMappingFunction(r);return null!==i?s=zi(i(t.rgb,this.exposureNode),t.a):(console.error("ToneMappingNode: Unsupported Tone Mapping configuration.",r),s=t),s}}const Ya=(e,t,r)=>xi(new Xa(e,xi(t),xi(r))),Qa=Ka("toneMappingExposure","float");js("toneMapping",((e,t,r)=>Ya(t,r,e)));class Za extends zs{static get type(){return"BufferAttributeNode"}constructor(e,t=null,r=0,s=0){super(e,t),this.isBufferNode=!0,this.bufferType=t,this.bufferStride=r,this.bufferOffset=s,this.usage=p,this.instanced=!1,this.attribute=null,this.global=!0,e&&!0===e.isBufferAttribute&&(this.attribute=e,this.usage=e.usage,this.instanced=e.isInstancedBufferAttribute)}getHash(e){if(0===this.bufferStride&&0===this.bufferOffset){let t=e.globalCache.getData(this.value);return void 0===t&&(t={node:this},e.globalCache.setData(this.value,t)),t.node.uuid}return this.uuid}getNodeType(e){return null===this.bufferType&&(this.bufferType=e.getTypeFromAttribute(this.attribute)),this.bufferType}setup(e){if(null!==this.attribute)return;const t=this.getNodeType(e),r=this.value,s=e.getTypeLength(t),i=this.bufferStride||s,n=this.bufferOffset,o=!0===r.isInterleavedBuffer?r:new g(r,i),a=new f(o,s,n);o.setUsage(this.usage),this.attribute=a,this.attribute.isInstancedBufferAttribute=this.instanced}generate(e){const t=this.getNodeType(e),r=e.getBufferAttributeFromNode(this,t),s=e.getPropertyName(r);let i=null;if("vertex"===e.shaderStage||"compute"===e.shaderStage)this.name=s,i=s;else{i=Pa(this).build(e,t)}return i}getInputType(){return"bufferAttribute"}setUsage(e){return this.usage=e,this.attribute&&!0===this.attribute.isBufferAttribute&&(this.attribute.usage=e),this}setInstanced(e){return this.instanced=e,this}}const Ja=(e,t=null,r=0,s=0)=>xi(new Za(e,t,r,s)),eu=(e,t=null,r=0,s=0)=>Ja(e,t,r,s).setUsage(m),tu=(e,t=null,r=0,s=0)=>Ja(e,t,r,s).setInstanced(!0),ru=(e,t=null,r=0,s=0)=>eu(e,t,r,s).setInstanced(!0);js("toAttribute",(e=>Ja(e.value)));class su extends Us{static get type(){return"ComputeNode"}constructor(e,t,r=[64]){super("void"),this.isComputeNode=!0,this.computeNode=e,this.count=t,this.workgroupSize=r,this.dispatchCount=0,this.version=1,this.name="",this.updateBeforeType=As.OBJECT,this.onInitFunction=null,this.updateDispatchCount()}dispose(){this.dispatchEvent({type:"dispose"})}label(e){return this.name=e,this}updateDispatchCount(){const{count:e,workgroupSize:t}=this;let r=t[0];for(let e=1;e<t.length;e++)r*=t[e];this.dispatchCount=Math.ceil(e/r)}onInit(e){return this.onInitFunction=e,this}updateBefore({renderer:e}){e.compute(this)}generate(e){const{shaderStage:t}=e;if("compute"===t){const t=this.computeNode.build(e,"void");""!==t&&e.addLineFlowCode(t,this)}}}const iu=(e,t,r)=>xi(new su(xi(e),t,r));js("compute",iu);class nu extends Us{static get type(){return"CacheNode"}constructor(e,t=!0){super(),this.node=e,this.parent=t,this.isCacheNode=!0}getNodeType(e){const t=e.getCache(),r=e.getCacheFromNode(this,this.parent);e.setCache(r);const s=this.node.getNodeType(e);return e.setCache(t),s}build(e,...t){const r=e.getCache(),s=e.getCacheFromNode(this,this.parent);e.setCache(s);const i=this.node.build(e,...t);return e.setCache(r),i}}const ou=(e,t)=>xi(new nu(xi(e),t));js("cache",ou);class au extends Us{static get type(){return"BypassNode"}constructor(e,t){super(),this.isBypassNode=!0,this.outputNode=e,this.callNode=t}getNodeType(e){return this.outputNode.getNodeType(e)}generate(e){const t=this.callNode.build(e,"void");return""!==t&&e.addLineFlowCode(t,this),this.outputNode.build(e)}}const uu=vi(au);js("bypass",uu);class lu extends Us{static get type(){return"RemapNode"}constructor(e,t,r,s=Mi(0),i=Mi(1)){super(),this.node=e,this.inLowNode=t,this.inHighNode=r,this.outLowNode=s,this.outHighNode=i,this.doClamp=!0}setup(){const{node:e,inLowNode:t,inHighNode:r,outLowNode:s,outHighNode:i,doClamp:n}=this;let o=e.sub(t).div(r.sub(t));return!0===n&&(o=o.clamp()),o.mul(i.sub(s)).add(s)}}const du=vi(lu,null,null,{doClamp:!1}),cu=vi(lu);js("remap",du),js("remapClamp",cu);class hu extends Us{static get type(){return"ExpressionNode"}constructor(e="",t="void"){super(t),this.snippet=e}generate(e,t){const r=this.getNodeType(e),s=this.snippet;if("void"!==r)return e.format(`( ${s} )`,r,t);e.addLineFlowCode(s,this)}}const pu=vi(hu),gu=e=>(e?Aa(e,pu("discard")):pu("discard")).append();js("discard",gu);class mu extends Ds{static get type(){return"RenderOutputNode"}constructor(e,t,r){super("vec4"),this.colorNode=e,this.toneMapping=t,this.outputColorSpace=r,this.isRenderOutputNode=!0}setup({context:e}){let t=this.colorNode||e.color;const r=(null!==this.toneMapping?this.toneMapping:e.toneMapping)||h,s=(null!==this.outputColorSpace?this.outputColorSpace:e.outputColorSpace)||y;return r!==h&&(t=t.toneMapping(r)),s!==y&&s!==d.workingColorSpace&&(t=t.workingToColorSpace(s)),t}}const fu=(e,t=null,r=null)=>xi(new mu(xi(e),t,r));js("renderOutput",fu);class yu extends Us{static get type(){return"AttributeNode"}constructor(e,t=null){super(t),this.global=!0,this._attributeName=e}getHash(e){return this.getAttributeName(e)}getNodeType(e){let t=this.nodeType;if(null===t){const r=this.getAttributeName(e);if(e.hasGeometryAttribute(r)){const s=e.geometry.getAttribute(r);t=e.getTypeFromAttribute(s)}else t="float"}return t}setAttributeName(e){return this._attributeName=e,this}getAttributeName(){return this._attributeName}generate(e){const t=this.getAttributeName(e),r=this.getNodeType(e);if(!0===e.hasGeometryAttribute(t)){const s=e.geometry.getAttribute(t),i=e.getTypeFromAttribute(s),n=e.getAttribute(t,i);if("vertex"===e.shaderStage)return e.format(n.name,i,r);return Pa(this).build(e,r)}return console.warn(`AttributeNode: Vertex attribute "${t}" not found on geometry.`),e.generateConst(r)}serialize(e){super.serialize(e),e.global=this.global,e._attributeName=this._attributeName}deserialize(e){super.deserialize(e),this.global=e.global,this._attributeName=e._attributeName}}const bu=(e,t)=>xi(new yu(e,t)),xu=(e=0)=>bu("uv"+(e>0?e:""),"vec2");class Tu extends Us{static get type(){return"TextureSizeNode"}constructor(e,t=null){super("uvec2"),this.isTextureSizeNode=!0,this.textureNode=e,this.levelNode=t}generate(e,t){const r=this.textureNode.build(e,"property"),s=null===this.levelNode?"0":this.levelNode.build(e,"int");return e.format(`${e.getMethod("textureDimensions")}( ${r}, ${s} )`,this.getNodeType(e),t)}}const _u=vi(Tu);class vu extends sn{static get type(){return"MaxMipLevelNode"}constructor(e){super(0),this._textureNode=e,this.updateType=As.FRAME}get textureNode(){return this._textureNode}get texture(){return this._textureNode.value}update(){const e=this.texture,t=e.images,r=t&&t.length>0?t[0]&&t[0].image||t[0]:e.image;if(r&&void 0!==r.width){const{width:e,height:t}=r;this.value=Math.log2(Math.max(e,t))}}}const Nu=vi(vu);class Su extends sn{static get type(){return"TextureNode"}constructor(e,t=null,r=null,s=null){super(e),this.isTextureNode=!0,this.uvNode=t,this.levelNode=r,this.biasNode=s,this.compareNode=null,this.depthNode=null,this.gradNode=null,this.sampler=!0,this.updateMatrix=!1,this.updateType=As.NONE,this.referenceNode=null,this._value=e,this._matrixUniform=null,this.setUpdateMatrix(null===t)}set value(e){this.referenceNode?this.referenceNode.value=e:this._value=e}get value(){return this.referenceNode?this.referenceNode.value:this._value}getUniformHash(){return this.value.uuid}getNodeType(){return!0===this.value.isDepthTexture?"float":this.value.type===b?"uvec4":this.value.type===x?"ivec4":"vec4"}getInputType(){return"texture"}getDefaultUV(){return xu(this.value.channel)}updateReference(){return this.value}getTransformedUV(e){return null===this._matrixUniform&&(this._matrixUniform=nn(this.value.matrix)),this._matrixUniform.mul(Vi(e,1)).xy}setUpdateMatrix(e){return this.updateMatrix=e,this.updateType=e?As.RENDER:As.NONE,this}setupUV(e,t){const r=this.value;return e.isFlipY()&&(r.image instanceof ImageBitmap&&!0===r.flipY||!0===r.isRenderTargetTexture||!0===r.isFramebufferTexture||!0===r.isDepthTexture)&&(t=this.sampler?t.flipY():t.setY(Bi(_u(this,this.levelNode).y).sub(t.y).sub(1))),t}setup(e){const t=e.getNodeProperties(this);t.referenceNode=this.referenceNode;const r=this.value;if(!r||!0!==r.isTexture)throw new Error("THREE.TSL: `texture( value )` function expects a valid instance of THREE.Texture().");let s=this.uvNode;null!==s&&!0!==e.context.forceUVContext||!e.context.getUV||(s=e.context.getUV(this)),s||(s=this.getDefaultUV()),!0===this.updateMatrix&&(s=this.getTransformedUV(s)),s=this.setupUV(e,s);let i=this.levelNode;null===i&&e.context.getTextureLevel&&(i=e.context.getTextureLevel(this)),t.uvNode=s,t.levelNode=i,t.biasNode=this.biasNode,t.compareNode=this.compareNode,t.gradNode=this.gradNode,t.depthNode=this.depthNode}generateUV(e,t){return t.build(e,!0===this.sampler?"vec2":"ivec2")}generateSnippet(e,t,r,s,i,n,o,a){const u=this.value;let l;return l=s?e.generateTextureLevel(u,t,r,s,n):i?e.generateTextureBias(u,t,r,i,n):a?e.generateTextureGrad(u,t,r,a,n):o?e.generateTextureCompare(u,t,r,o,n):!1===this.sampler?e.generateTextureLoad(u,t,r,n):e.generateTexture(u,t,r,n),l}generate(e,t){const r=this.value,s=e.getNodeProperties(this),i=super.generate(e,"property");if("sampler"===t)return i+"_sampler";if(e.isReference(t))return i;{const n=e.getDataFromNode(this);let o=n.propertyName;if(void 0===o){const{uvNode:t,levelNode:r,biasNode:a,compareNode:u,depthNode:l,gradNode:d}=s,c=this.generateUV(e,t),h=r?r.build(e,"float"):null,p=a?a.build(e,"float"):null,g=l?l.build(e,"int"):null,m=u?u.build(e,"float"):null,f=d?[d[0].build(e,"vec2"),d[1].build(e,"vec2")]:null,y=e.getVarFromNode(this);o=e.getPropertyName(y);const b=this.generateSnippet(e,i,c,h,p,g,m,f);e.addLineFlowCode(`${o} = ${b}`,this),n.snippet=b,n.propertyName=o}let a=o;const u=this.getNodeType(e);return e.needsToWorkingColorSpace(r)&&(a=Ha(pu(a,u),r.colorSpace).setup(e).build(e,u)),e.format(a,u,t)}}setSampler(e){return this.sampler=e,this}getSampler(){return this.sampler}uv(e){return console.warn("THREE.TextureNode: .uv() has been renamed. Use .sample() instead."),this.sample(e)}sample(e){const t=this.clone();return t.uvNode=xi(e),t.referenceNode=this.getSelf(),xi(t)}blur(e){const t=this.clone();return t.biasNode=xi(e).mul(Nu(t)),t.referenceNode=this.getSelf(),xi(t)}level(e){const t=this.clone();return t.levelNode=xi(e),t.referenceNode=this.getSelf(),xi(t)}size(e){return _u(this,e)}bias(e){const t=this.clone();return t.biasNode=xi(e),t.referenceNode=this.getSelf(),xi(t)}compare(e){const t=this.clone();return t.compareNode=xi(e),t.referenceNode=this.getSelf(),xi(t)}grad(e,t){const r=this.clone();return r.gradNode=[xi(e),xi(t)],r.referenceNode=this.getSelf(),xi(r)}depth(e){const t=this.clone();return t.depthNode=xi(e),t.referenceNode=this.getSelf(),xi(t)}serialize(e){super.serialize(e),e.value=this.value.toJSON(e.meta).uuid,e.sampler=this.sampler,e.updateMatrix=this.updateMatrix,e.updateType=this.updateType}deserialize(e){super.deserialize(e),this.value=e.meta.textures[e.value],this.sampler=e.sampler,this.updateMatrix=e.updateMatrix,this.updateType=e.updateType}update(){const e=this.value,t=this._matrixUniform;null!==t&&(t.value=e.matrix),!0===e.matrixAutoUpdate&&e.updateMatrix()}clone(){const e=new this.constructor(this.value,this.uvNode,this.levelNode,this.biasNode);return e.sampler=this.sampler,e}}const Au=vi(Su),Ru=(...e)=>Au(...e).setSampler(!1),Cu=nn("float").label("cameraNear").setGroup(tn).onRenderUpdate((({camera:e})=>e.near)),Eu=nn("float").label("cameraFar").setGroup(tn).onRenderUpdate((({camera:e})=>e.far)),wu=nn("mat4").label("cameraProjectionMatrix").setGroup(tn).onRenderUpdate((({camera:e})=>e.projectionMatrix)),Mu=nn("mat4").label("cameraProjectionMatrixInverse").setGroup(tn).onRenderUpdate((({camera:e})=>e.projectionMatrixInverse)),Bu=nn("mat4").label("cameraViewMatrix").setGroup(tn).onRenderUpdate((({camera:e})=>e.matrixWorldInverse)),Fu=nn("mat4").label("cameraWorldMatrix").setGroup(tn).onRenderUpdate((({camera:e})=>e.matrixWorld)),Uu=nn("mat3").label("cameraNormalMatrix").setGroup(tn).onRenderUpdate((({camera:e})=>e.normalMatrix)),Pu=nn(new r).label("cameraPosition").setGroup(tn).onRenderUpdate((({camera:e},t)=>t.value.setFromMatrixPosition(e.matrixWorld)));class Iu extends Us{static get type(){return"Object3DNode"}constructor(e,t=null){super(),this.scope=e,this.object3d=t,this.updateType=As.OBJECT,this._uniformNode=new sn(null)}getNodeType(){const e=this.scope;return e===Iu.WORLD_MATRIX?"mat4":e===Iu.POSITION||e===Iu.VIEW_POSITION||e===Iu.DIRECTION||e===Iu.SCALE?"vec3":void 0}update(e){const t=this.object3d,s=this._uniformNode,i=this.scope;if(i===Iu.WORLD_MATRIX)s.value=t.matrixWorld;else if(i===Iu.POSITION)s.value=s.value||new r,s.value.setFromMatrixPosition(t.matrixWorld);else if(i===Iu.SCALE)s.value=s.value||new r,s.value.setFromMatrixScale(t.matrixWorld);else if(i===Iu.DIRECTION)s.value=s.value||new r,t.getWorldDirection(s.value);else if(i===Iu.VIEW_POSITION){const i=e.camera;s.value=s.value||new r,s.value.setFromMatrixPosition(t.matrixWorld),s.value.applyMatrix4(i.matrixWorldInverse)}}generate(e){const t=this.scope;return t===Iu.WORLD_MATRIX?this._uniformNode.nodeType="mat4":t!==Iu.POSITION&&t!==Iu.VIEW_POSITION&&t!==Iu.DIRECTION&&t!==Iu.SCALE||(this._uniformNode.nodeType="vec3"),this._uniformNode.build(e)}serialize(e){super.serialize(e),e.scope=this.scope}deserialize(e){super.deserialize(e),this.scope=e.scope}}Iu.WORLD_MATRIX="worldMatrix",Iu.POSITION="position",Iu.SCALE="scale",Iu.VIEW_POSITION="viewPosition",Iu.DIRECTION="direction";const Du=vi(Iu,Iu.DIRECTION),Lu=vi(Iu,Iu.WORLD_MATRIX),Vu=vi(Iu,Iu.POSITION),Ou=vi(Iu,Iu.SCALE),Gu=vi(Iu,Iu.VIEW_POSITION);class ku extends Iu{static get type(){return"ModelNode"}constructor(e){super(e)}update(e){this.object3d=e.object,super.update(e)}}const zu=Ni(ku,ku.DIRECTION),$u=Ni(ku,ku.WORLD_MATRIX),Hu=Ni(ku,ku.POSITION),Wu=Ni(ku,ku.SCALE),ju=Ni(ku,ku.VIEW_POSITION),qu=nn(new i).onObjectUpdate((({object:e},t)=>t.value.getNormalMatrix(e.matrixWorld))),Ku=nn(new n).onObjectUpdate((({object:e},t)=>t.value.copy(e.matrixWorld).invert())),Xu=Si((e=>e.renderer.nodes.modelViewMatrix||Yu)).once()().toVar("modelViewMatrix"),Yu=Bu.mul($u),Qu=Si((e=>(e.context.isHighPrecisionModelViewMatrix=!0,nn("mat4").onObjectUpdate((({object:e,camera:t})=>e.modelViewMatrix.multiplyMatrices(t.matrixWorldInverse,e.matrixWorld)))))).once()().toVar("highpModelViewMatrix"),Zu=Si((e=>{const t=e.context.isHighPrecisionModelViewMatrix;return nn("mat3").onObjectUpdate((({object:e,camera:r})=>(!0!==t&&e.modelViewMatrix.multiplyMatrices(r.matrixWorldInverse,e.matrixWorld),e.normalMatrix.getNormalMatrix(e.modelViewMatrix))))})).once()().toVar("highpModelNormalViewMatrix"),Ju=bu("position","vec3"),el=Ju.varying("positionLocal"),tl=Ju.varying("positionPrevious"),rl=$u.mul(el).xyz.varying("v_positionWorld").context({needsPositionReassign:!0}),sl=el.transformDirection($u).varying("v_positionWorldDirection").normalize().toVar("positionWorldDirection").context({needsPositionReassign:!0}),il=Si((e=>e.context.setupPositionView()),"vec3").once()().varying("v_positionView").context({needsPositionReassign:!0}),nl=il.negate().varying("v_positionViewDirection").normalize().toVar("positionViewDirection");class ol extends Us{static get type(){return"FrontFacingNode"}constructor(){super("bool"),this.isFrontFacingNode=!0}generate(e){const{renderer:t,material:r}=e;return t.coordinateSystem===u&&r.side===T?"false":e.getFrontFacing()}}const al=Ni(ol),ul=Mi(al).mul(2).sub(1),ll=bu("normal","vec3"),dl=Si((e=>!1===e.geometry.hasAttribute("normal")?(console.warn('TSL.NormalNode: Vertex attribute "normal" not found on geometry.'),Vi(0,1,0)):ll),"vec3").once()().toVar("normalLocal"),cl=il.dFdx().cross(il.dFdy()).normalize().toVar("normalFlat"),hl=Si((e=>{let t;return t=!0===e.material.flatShading?cl:Pa(bl(dl),"v_normalView").normalize(),t}),"vec3").once()().toVar("normalView"),pl=Pa(hl.transformDirection(Bu),"v_normalWorld").normalize().toVar("normalWorld"),gl=Si((e=>e.context.setupNormal()),"vec3").once()().mul(ul).toVar("transformedNormalView"),ml=gl.transformDirection(Bu).toVar("transformedNormalWorld"),fl=Si((e=>e.context.setupClearcoatNormal()),"vec3").once()().mul(ul).toVar("transformedClearcoatNormalView"),yl=Si((([e,t=$u])=>{const r=qi(t),s=e.div(Vi(r[0].dot(r[0]),r[1].dot(r[1]),r[2].dot(r[2])));return r.mul(s).xyz})),bl=Si((([e],t)=>{const r=t.renderer.nodes.modelNormalViewMatrix;if(null!==r)return r.transformDirection(e);const s=qu.mul(e);return Bu.transformDirection(s)})),xl=nn(0).onReference((({material:e})=>e)).onRenderUpdate((({material:e})=>e.refractionRatio)),Tl=nl.negate().reflect(gl),_l=nl.negate().refract(gl,xl),vl=Tl.transformDirection(Bu).toVar("reflectVector"),Nl=_l.transformDirection(Bu).toVar("reflectVector");class Sl extends Su{static get type(){return"CubeTextureNode"}constructor(e,t=null,r=null,s=null){super(e,t,r,s),this.isCubeTextureNode=!0}getInputType(){return"cubeTexture"}getDefaultUV(){const e=this.value;return e.mapping===_?vl:e.mapping===v?Nl:(console.error('THREE.CubeTextureNode: Mapping "%s" not supported.',e.mapping),Vi(0,0,0))}setUpdateMatrix(){}setupUV(e,t){const r=this.value;return e.renderer.coordinateSystem!==l&&r.isRenderTargetTexture?t:Vi(t.x.negate(),t.yz)}generateUV(e,t){return t.build(e,"vec3")}}const Al=vi(Sl);class Rl extends sn{static get type(){return"BufferNode"}constructor(e,t,r=0){super(e,t),this.isBufferNode=!0,this.bufferType=t,this.bufferCount=r}getElementType(e){return this.getNodeType(e)}getInputType(){return"buffer"}}const Cl=(e,t,r)=>xi(new Rl(e,t,r));class El extends Ps{static get type(){return"UniformArrayElementNode"}constructor(e,t){super(e,t),this.isArrayBufferElementNode=!0}generate(e){const t=super.generate(e),r=this.getNodeType(),s=this.node.getPaddedType();return e.format(t,s,r)}}class wl extends Rl{static get type(){return"UniformArrayNode"}constructor(e,t=null){super(null),this.array=e,this.elementType=null===t?bs(e[0]):t,this.paddedType=this.getPaddedType(),this.updateType=As.RENDER,this.isArrayBufferNode=!0}getNodeType(){return this.paddedType}getElementType(){return this.elementType}getPaddedType(){const e=this.elementType;let t="vec4";return"mat2"===e?t="mat2":!0===/mat/.test(e)?t="mat4":"i"===e.charAt(0)?t="ivec4":"u"===e.charAt(0)&&(t="uvec4"),t}update(){const{array:e,value:t}=this,r=this.elementType;if("float"===r||"int"===r||"uint"===r)for(let r=0;r<e.length;r++){t[4*r]=e[r]}else if("color"===r)for(let r=0;r<e.length;r++){const s=4*r,i=e[r];t[s]=i.r,t[s+1]=i.g,t[s+2]=i.b||0}else if("mat2"===r)for(let r=0;r<e.length;r++){const s=4*r,i=e[r];t[s]=i.elements[0],t[s+1]=i.elements[1],t[s+2]=i.elements[2],t[s+3]=i.elements[3]}else if("mat3"===r)for(let r=0;r<e.length;r++){const s=16*r,i=e[r];t[s]=i.elements[0],t[s+1]=i.elements[1],t[s+2]=i.elements[2],t[s+4]=i.elements[3],t[s+5]=i.elements[4],t[s+6]=i.elements[5],t[s+8]=i.elements[6],t[s+9]=i.elements[7],t[s+10]=i.elements[8],t[s+15]=1}else if("mat4"===r)for(let r=0;r<e.length;r++){const s=16*r,i=e[r];for(let e=0;e<i.elements.length;e++)t[s+e]=i.elements[e]}else for(let r=0;r<e.length;r++){const s=4*r,i=e[r];t[s]=i.x,t[s+1]=i.y,t[s+2]=i.z||0,t[s+3]=i.w||0}}setup(e){const t=this.array.length,r=this.elementType;let s=Float32Array;const i=this.paddedType,n=e.getTypeLength(i);return"i"===r.charAt(0)&&(s=Int32Array),"u"===r.charAt(0)&&(s=Uint32Array),this.value=new s(t*n),this.bufferCount=t,this.bufferType=i,super.setup(e)}element(e){return xi(new El(this,xi(e)))}}const Ml=(e,t)=>xi(new wl(e,t));class Bl extends Ps{static get type(){return"ReferenceElementNode"}constructor(e,t){super(e,t),this.referenceNode=e,this.isReferenceElementNode=!0}getNodeType(){return this.referenceNode.uniformType}generate(e){const t=super.generate(e),r=this.referenceNode.getNodeType(),s=this.getNodeType();return e.format(t,r,s)}}class Fl extends Us{static get type(){return"ReferenceNode"}constructor(e,t,r=null,s=null){super(),this.property=e,this.uniformType=t,this.object=r,this.count=s,this.properties=e.split("."),this.reference=r,this.node=null,this.group=null,this.name=null,this.updateType=As.OBJECT}element(e){return xi(new Bl(this,xi(e)))}setGroup(e){return this.group=e,this}label(e){return this.name=e,this}setNodeType(e){let t=null;t=null!==this.count?Cl(null,e,this.count):Array.isArray(this.getValueFromReference())?Ml(null,e):"texture"===e?Au(null):"cubeTexture"===e?Al(null):nn(null,e),null!==this.group&&t.setGroup(this.group),null!==this.name&&t.label(this.name),this.node=t.getSelf()}getNodeType(e){return null===this.node&&(this.updateReference(e),this.updateValue()),this.node.getNodeType(e)}getValueFromReference(e=this.reference){const{properties:t}=this;let r=e[t[0]];for(let e=1;e<t.length;e++)r=r[t[e]];return r}updateReference(e){return this.reference=null!==this.object?this.object:e.object,this.reference}setup(){return this.updateValue(),this.node}update(){this.updateValue()}updateValue(){null===this.node&&this.setNodeType(this.uniformType);const e=this.getValueFromReference();Array.isArray(e)?this.node.array=e:this.node.value=e}}const Ul=(e,t,r)=>xi(new Fl(e,t,r)),Pl=(e,t,r,s)=>xi(new Fl(e,t,s,r));class Il extends Fl{static get type(){return"MaterialReferenceNode"}constructor(e,t,r=null){super(e,t,r),this.material=r,this.isMaterialReferenceNode=!0}updateReference(e){return this.reference=null!==this.material?this.material:e.material,this.reference}}const Dl=(e,t,r=null)=>xi(new Il(e,t,r)),Ll=Si((e=>(!1===e.geometry.hasAttribute("tangent")&&e.geometry.computeTangents(),bu("tangent","vec4"))))(),Vl=Ll.xyz.toVar("tangentLocal"),Ol=Xu.mul(zi(Vl,0)).xyz.varying("v_tangentView").normalize().toVar("tangentView"),Gl=Ol.transformDirection(Bu).varying("v_tangentWorld").normalize().toVar("tangentWorld"),kl=Ol.toVar("transformedTangentView"),zl=kl.transformDirection(Bu).normalize().toVar("transformedTangentWorld"),$l=e=>e.mul(Ll.w).xyz,Hl=Pa($l(ll.cross(Ll)),"v_bitangentGeometry").normalize().toVar("bitangentGeometry"),Wl=Pa($l(dl.cross(Vl)),"v_bitangentLocal").normalize().toVar("bitangentLocal"),jl=Pa($l(hl.cross(Ol)),"v_bitangentView").normalize().toVar("bitangentView"),ql=Pa($l(pl.cross(Gl)),"v_bitangentWorld").normalize().toVar("bitangentWorld"),Kl=$l(gl.cross(kl)).normalize().toVar("transformedBitangentView"),Xl=Kl.transformDirection(Bu).normalize().toVar("transformedBitangentWorld"),Yl=qi(Ol,jl,hl),Ql=nl.mul(Yl),Zl=(()=>{let e=Nn.cross(nl);return e=e.cross(Nn).normalize(),e=ha(e,gl,_n.mul(cn.oneMinus()).oneMinus().pow2().pow2()).normalize(),e})(),Jl=Si((e=>{const{eye_pos:t,surf_norm:r,mapN:s,uv:i}=e,n=t.dFdx(),o=t.dFdy(),a=i.dFdx(),u=i.dFdy(),l=r,d=o.cross(l),c=l.cross(n),h=d.mul(a.x).add(c.mul(u.x)),p=d.mul(a.y).add(c.mul(u.y)),g=h.dot(h).max(p.dot(p)),m=ul.mul(g.inverseSqrt());return zn(h.mul(s.x,m),p.mul(s.y,m),l.mul(s.z)).normalize()}));class ed extends Ds{static get type(){return"NormalMapNode"}constructor(e,t=null){super("vec3"),this.node=e,this.scaleNode=t,this.normalMapType=N}setup(e){const{normalMapType:t,scaleNode:r}=this;let s=this.node.mul(2).sub(1);null!==r&&(s=Vi(s.xy.mul(r),s.z));let i=null;if(t===S)i=bl(s);else if(t===N){i=!0===e.hasGeometryAttribute("tangent")?Yl.mul(s).normalize():Jl({eye_pos:il,surf_norm:hl,mapN:s,uv:xu()})}return i}}const td=vi(ed),rd=Si((({textureNode:e,bumpScale:t})=>{const r=t=>e.cache().context({getUV:e=>t(e.uvNode||xu()),forceUVContext:!0}),s=Mi(r((e=>e)));return Pi(Mi(r((e=>e.add(e.dFdx())))).sub(s),Mi(r((e=>e.add(e.dFdy())))).sub(s)).mul(t)})),sd=Si((e=>{const{surf_pos:t,surf_norm:r,dHdxy:s}=e,i=t.dFdx().normalize(),n=r,o=t.dFdy().normalize().cross(n),a=n.cross(i),u=i.dot(o).mul(ul),l=u.sign().mul(s.x.mul(o).add(s.y.mul(a)));return u.abs().mul(r).sub(l).normalize()}));class id extends Ds{static get type(){return"BumpMapNode"}constructor(e,t=null){super("vec3"),this.textureNode=e,this.scaleNode=t}setup(){const e=null!==this.scaleNode?this.scaleNode:1,t=rd({textureNode:this.textureNode,bumpScale:e});return sd({surf_pos:il,surf_norm:hl,dHdxy:t})}}const nd=vi(id),od=new Map;class ad extends Us{static get type(){return"MaterialNode"}constructor(e){super(),this.scope=e}getCache(e,t){let r=od.get(e);return void 0===r&&(r=Dl(e,t),od.set(e,r)),r}getFloat(e){return this.getCache(e,"float")}getColor(e){return this.getCache(e,"color")}getTexture(e){return this.getCache("map"===e?"map":e+"Map","texture")}setup(e){const t=e.context.material,r=this.scope;let s=null;if(r===ad.COLOR){const e=void 0!==t.color?this.getColor(r):Vi();s=t.map&&!0===t.map.isTexture?e.mul(this.getTexture("map")):e}else if(r===ad.OPACITY){const e=this.getFloat(r);s=t.alphaMap&&!0===t.alphaMap.isTexture?e.mul(this.getTexture("alpha")):e}else if(r===ad.SPECULAR_STRENGTH)s=t.specularMap&&!0===t.specularMap.isTexture?this.getTexture("specular").r:Mi(1);else if(r===ad.SPECULAR_INTENSITY){const e=this.getFloat(r);s=t.specularIntensityMap&&!0===t.specularIntensityMap.isTexture?e.mul(this.getTexture(r).a):e}else if(r===ad.SPECULAR_COLOR){const e=this.getColor(r);s=t.specularColorMap&&!0===t.specularColorMap.isTexture?e.mul(this.getTexture(r).rgb):e}else if(r===ad.ROUGHNESS){const e=this.getFloat(r);s=t.roughnessMap&&!0===t.roughnessMap.isTexture?e.mul(this.getTexture(r).g):e}else if(r===ad.METALNESS){const e=this.getFloat(r);s=t.metalnessMap&&!0===t.metalnessMap.isTexture?e.mul(this.getTexture(r).b):e}else if(r===ad.EMISSIVE){const e=this.getFloat("emissiveIntensity"),i=this.getColor(r).mul(e);s=t.emissiveMap&&!0===t.emissiveMap.isTexture?i.mul(this.getTexture(r)):i}else if(r===ad.NORMAL)t.normalMap?(s=td(this.getTexture("normal"),this.getCache("normalScale","vec2")),s.normalMapType=t.normalMapType):s=t.bumpMap?nd(this.getTexture("bump").r,this.getFloat("bumpScale")):hl;else if(r===ad.CLEARCOAT){const e=this.getFloat(r);s=t.clearcoatMap&&!0===t.clearcoatMap.isTexture?e.mul(this.getTexture(r).r):e}else if(r===ad.CLEARCOAT_ROUGHNESS){const e=this.getFloat(r);s=t.clearcoatRoughnessMap&&!0===t.clearcoatRoughnessMap.isTexture?e.mul(this.getTexture(r).r):e}else if(r===ad.CLEARCOAT_NORMAL)s=t.clearcoatNormalMap?td(this.getTexture(r),this.getCache(r+"Scale","vec2")):hl;else if(r===ad.SHEEN){const e=this.getColor("sheenColor").mul(this.getFloat("sheen"));s=t.sheenColorMap&&!0===t.sheenColorMap.isTexture?e.mul(this.getTexture("sheenColor").rgb):e}else if(r===ad.SHEEN_ROUGHNESS){const e=this.getFloat(r);s=t.sheenRoughnessMap&&!0===t.sheenRoughnessMap.isTexture?e.mul(this.getTexture(r).a):e,s=s.clamp(.07,1)}else if(r===ad.ANISOTROPY)if(t.anisotropyMap&&!0===t.anisotropyMap.isTexture){const e=this.getTexture(r);s=ji(Wd.x,Wd.y,Wd.y.negate(),Wd.x).mul(e.rg.mul(2).sub(Pi(1)).normalize().mul(e.b))}else s=Wd;else if(r===ad.IRIDESCENCE_THICKNESS){const e=Ul("1","float",t.iridescenceThicknessRange);if(t.iridescenceThicknessMap){const i=Ul("0","float",t.iridescenceThicknessRange);s=e.sub(i).mul(this.getTexture(r).g).add(i)}else s=e}else if(r===ad.TRANSMISSION){const e=this.getFloat(r);s=t.transmissionMap?e.mul(this.getTexture(r).r):e}else if(r===ad.THICKNESS){const e=this.getFloat(r);s=t.thicknessMap?e.mul(this.getTexture(r).g):e}else if(r===ad.IOR)s=this.getFloat(r);else if(r===ad.LIGHT_MAP)s=this.getTexture(r).rgb.mul(this.getFloat("lightMapIntensity"));else if(r===ad.AO)s=this.getTexture(r).r.sub(1).mul(this.getFloat("aoMapIntensity")).add(1);else{const t=this.getNodeType(e);s=this.getCache(r,t)}return s}}ad.ALPHA_TEST="alphaTest",ad.COLOR="color",ad.OPACITY="opacity",ad.SHININESS="shininess",ad.SPECULAR="specular",ad.SPECULAR_STRENGTH="specularStrength",ad.SPECULAR_INTENSITY="specularIntensity",ad.SPECULAR_COLOR="specularColor",ad.REFLECTIVITY="reflectivity",ad.ROUGHNESS="roughness",ad.METALNESS="metalness",ad.NORMAL="normal",ad.CLEARCOAT="clearcoat",ad.CLEARCOAT_ROUGHNESS="clearcoatRoughness",ad.CLEARCOAT_NORMAL="clearcoatNormal",ad.EMISSIVE="emissive",ad.ROTATION="rotation",ad.SHEEN="sheen",ad.SHEEN_ROUGHNESS="sheenRoughness",ad.ANISOTROPY="anisotropy",ad.IRIDESCENCE="iridescence",ad.IRIDESCENCE_IOR="iridescenceIOR",ad.IRIDESCENCE_THICKNESS="iridescenceThickness",ad.IOR="ior",ad.TRANSMISSION="transmission",ad.THICKNESS="thickness",ad.ATTENUATION_DISTANCE="attenuationDistance",ad.ATTENUATION_COLOR="attenuationColor",ad.LINE_SCALE="scale",ad.LINE_DASH_SIZE="dashSize",ad.LINE_GAP_SIZE="gapSize",ad.LINE_WIDTH="linewidth",ad.LINE_DASH_OFFSET="dashOffset",ad.POINT_WIDTH="pointWidth",ad.DISPERSION="dispersion",ad.LIGHT_MAP="light",ad.AO="ao";const ud=Ni(ad,ad.ALPHA_TEST),ld=Ni(ad,ad.COLOR),dd=Ni(ad,ad.SHININESS),cd=Ni(ad,ad.EMISSIVE),hd=Ni(ad,ad.OPACITY),pd=Ni(ad,ad.SPECULAR),gd=Ni(ad,ad.SPECULAR_INTENSITY),md=Ni(ad,ad.SPECULAR_COLOR),fd=Ni(ad,ad.SPECULAR_STRENGTH),yd=Ni(ad,ad.REFLECTIVITY),bd=Ni(ad,ad.ROUGHNESS),xd=Ni(ad,ad.METALNESS),Td=Ni(ad,ad.NORMAL).context({getUV:null}),_d=Ni(ad,ad.CLEARCOAT),vd=Ni(ad,ad.CLEARCOAT_ROUGHNESS),Nd=Ni(ad,ad.CLEARCOAT_NORMAL).context({getUV:null}),Sd=Ni(ad,ad.ROTATION),Ad=Ni(ad,ad.SHEEN),Rd=Ni(ad,ad.SHEEN_ROUGHNESS),Cd=Ni(ad,ad.ANISOTROPY),Ed=Ni(ad,ad.IRIDESCENCE),wd=Ni(ad,ad.IRIDESCENCE_IOR),Md=Ni(ad,ad.IRIDESCENCE_THICKNESS),Bd=Ni(ad,ad.TRANSMISSION),Fd=Ni(ad,ad.THICKNESS),Ud=Ni(ad,ad.IOR),Pd=Ni(ad,ad.ATTENUATION_DISTANCE),Id=Ni(ad,ad.ATTENUATION_COLOR),Dd=Ni(ad,ad.LINE_SCALE),Ld=Ni(ad,ad.LINE_DASH_SIZE),Vd=Ni(ad,ad.LINE_GAP_SIZE),Od=Ni(ad,ad.LINE_WIDTH),Gd=Ni(ad,ad.LINE_DASH_OFFSET),kd=Ni(ad,ad.POINT_WIDTH),zd=Ni(ad,ad.DISPERSION),$d=Ni(ad,ad.LIGHT_MAP),Hd=Ni(ad,ad.AO),Wd=nn(new t).onReference((function(e){return e.material})).onRenderUpdate((function({material:e}){this.value.set(e.anisotropy*Math.cos(e.anisotropyRotation),e.anisotropy*Math.sin(e.anisotropyRotation))})),jd=Si((e=>e.context.setupModelViewProjection()),"vec4").once()().varying("v_modelViewProjection");class qd extends Us{static get type(){return"IndexNode"}constructor(e){super("uint"),this.scope=e,this.isIndexNode=!0}generate(e){const t=this.getNodeType(e),r=this.scope;let s,i;if(r===qd.VERTEX)s=e.getVertexIndex();else if(r===qd.INSTANCE)s=e.getInstanceIndex();else if(r===qd.DRAW)s=e.getDrawIndex();else if(r===qd.INVOCATION_LOCAL)s=e.getInvocationLocalIndex();else if(r===qd.INVOCATION_SUBGROUP)s=e.getInvocationSubgroupIndex();else{if(r!==qd.SUBGROUP)throw new Error("THREE.IndexNode: Unknown scope: "+r);s=e.getSubgroupIndex()}if("vertex"===e.shaderStage||"compute"===e.shaderStage)i=s;else{i=Pa(this).build(e,t)}return i}}qd.VERTEX="vertex",qd.INSTANCE="instance",qd.SUBGROUP="subgroup",qd.INVOCATION_LOCAL="invocationLocal",qd.INVOCATION_SUBGROUP="invocationSubgroup",qd.DRAW="draw";const Kd=Ni(qd,qd.VERTEX),Xd=Ni(qd,qd.INSTANCE),Yd=Ni(qd,qd.SUBGROUP),Qd=Ni(qd,qd.INVOCATION_SUBGROUP),Zd=Ni(qd,qd.INVOCATION_LOCAL),Jd=Ni(qd,qd.DRAW);class ec extends Us{static get type(){return"InstanceNode"}constructor(e,t,r){super("void"),this.count=e,this.instanceMatrix=t,this.instanceColor=r,this.instanceMatrixNode=null,this.instanceColorNode=null,this.updateType=As.FRAME,this.buffer=null,this.bufferColor=null}setup(e){const{count:t,instanceMatrix:r,instanceColor:s}=this;let{instanceMatrixNode:i,instanceColorNode:n}=this;if(null===i){if(t<=1e3)i=Cl(r.array,"mat4",Math.max(t,1)).element(Xd);else{const e=new A(r.array,16,1);this.buffer=e;const t=r.usage===m?ru:tu,s=[t(e,"vec4",16,0),t(e,"vec4",16,4),t(e,"vec4",16,8),t(e,"vec4",16,12)];i=Ki(...s)}this.instanceMatrixNode=i}if(s&&null===n){const e=new R(s.array,3),t=s.usage===m?ru:tu;this.bufferColor=e,n=Vi(t(e,"vec3",3,0)),this.instanceColorNode=n}const o=i.mul(el).xyz;if(el.assign(o),e.hasGeometryAttribute("normal")){const e=yl(dl,i);dl.assign(e)}null!==this.instanceColorNode&&un("vec3","vInstanceColor").assign(this.instanceColorNode)}update(){this.instanceMatrix.usage!==m&&null!==this.buffer&&this.instanceMatrix.version!==this.buffer.version&&(this.buffer.version=this.instanceMatrix.version),this.instanceColor&&this.instanceColor.usage!==m&&null!==this.bufferColor&&this.instanceColor.version!==this.bufferColor.version&&(this.bufferColor.version=this.instanceColor.version)}}const tc=vi(ec);class rc extends ec{static get type(){return"InstancedMeshNode"}constructor(e){const{count:t,instanceMatrix:r,instanceColor:s}=e;super(t,r,s),this.instancedMesh=e}}const sc=vi(rc);class ic extends Us{static get type(){return"BatchNode"}constructor(e){super("void"),this.batchMesh=e,this.batchingIdNode=null}setup(e){null===this.batchingIdNode&&(null===e.getDrawIndex()?this.batchingIdNode=Xd:this.batchingIdNode=Jd);const t=Si((([e])=>{const t=_u(Ru(this.batchMesh._indirectTexture),0),r=Bi(e).modInt(Bi(t)),s=Bi(e).div(Bi(t));return Ru(this.batchMesh._indirectTexture,Ii(r,s)).x})).setLayout({name:"getIndirectIndex",type:"uint",inputs:[{name:"id",type:"int"}]}),r=t(Bi(this.batchingIdNode)),s=this.batchMesh._matricesTexture,i=_u(Ru(s),0),n=Mi(r).mul(4).toInt().toVar(),o=n.modInt(i),a=n.div(Bi(i)),u=Ki(Ru(s,Ii(o,a)),Ru(s,Ii(o.add(1),a)),Ru(s,Ii(o.add(2),a)),Ru(s,Ii(o.add(3),a))),l=this.batchMesh._colorsTexture;if(null!==l){const e=Si((([e])=>{const t=_u(Ru(l),0).x,r=e,s=r.modInt(t),i=r.div(t);return Ru(l,Ii(s,i)).rgb})).setLayout({name:"getBatchingColor",type:"vec3",inputs:[{name:"id",type:"int"}]}),t=e(r);un("vec3","vBatchColor").assign(t)}const d=qi(u);el.assign(u.mul(el));const c=dl.div(Vi(d[0].dot(d[0]),d[1].dot(d[1]),d[2].dot(d[2]))),h=d.mul(c).xyz;dl.assign(h),e.hasGeometryAttribute("tangent")&&Vl.mulAssign(d)}}const nc=vi(ic),oc=new WeakMap;class ac extends Us{static get type(){return"SkinningNode"}constructor(e,t=!1){let r,s,i;super("void"),this.skinnedMesh=e,this.useReference=t,this.updateType=As.OBJECT,this.skinIndexNode=bu("skinIndex","uvec4"),this.skinWeightNode=bu("skinWeight","vec4"),t?(r=Ul("bindMatrix","mat4"),s=Ul("bindMatrixInverse","mat4"),i=Pl("skeleton.boneMatrices","mat4",e.skeleton.bones.length)):(r=nn(e.bindMatrix,"mat4"),s=nn(e.bindMatrixInverse,"mat4"),i=Cl(e.skeleton.boneMatrices,"mat4",e.skeleton.bones.length)),this.bindMatrixNode=r,this.bindMatrixInverseNode=s,this.boneMatricesNode=i,this.previousBoneMatricesNode=null}getSkinnedPosition(e=this.boneMatricesNode,t=el){const{skinIndexNode:r,skinWeightNode:s,bindMatrixNode:i,bindMatrixInverseNode:n}=this,o=e.element(r.x),a=e.element(r.y),u=e.element(r.z),l=e.element(r.w),d=i.mul(t),c=zn(o.mul(s.x).mul(d),a.mul(s.y).mul(d),u.mul(s.z).mul(d),l.mul(s.w).mul(d));return n.mul(c).xyz}getSkinnedNormal(e=this.boneMatricesNode,t=dl){const{skinIndexNode:r,skinWeightNode:s,bindMatrixNode:i,bindMatrixInverseNode:n}=this,o=e.element(r.x),a=e.element(r.y),u=e.element(r.z),l=e.element(r.w);let d=zn(s.x.mul(o),s.y.mul(a),s.z.mul(u),s.w.mul(l));return d=n.mul(d).mul(i),d.transformDirection(t).xyz}getPreviousSkinnedPosition(e){const t=e.object;return null===this.previousBoneMatricesNode&&(t.skeleton.previousBoneMatrices=new Float32Array(t.skeleton.boneMatrices),this.previousBoneMatricesNode=Pl("skeleton.previousBoneMatrices","mat4",t.skeleton.bones.length)),this.getSkinnedPosition(this.previousBoneMatricesNode,tl)}needsPreviousBoneMatrices(e){const t=e.renderer.getMRT();return t&&t.has("velocity")||!0===Ts(e.object).useVelocity}setup(e){this.needsPreviousBoneMatrices(e)&&tl.assign(this.getPreviousSkinnedPosition(e));const t=this.getSkinnedPosition();if(el.assign(t),e.hasGeometryAttribute("normal")){const t=this.getSkinnedNormal();dl.assign(t),e.hasGeometryAttribute("tangent")&&Vl.assign(t)}}generate(e,t){if("void"!==t)return el.build(e,t)}update(e){const t=(this.useReference?e.object:this.skinnedMesh).skeleton;oc.get(t)!==e.frameId&&(oc.set(t,e.frameId),null!==this.previousBoneMatricesNode&&t.previousBoneMatrices.set(t.boneMatrices),t.update())}}const uc=e=>xi(new ac(e,!0));class lc extends Us{static get type(){return"LoopNode"}constructor(e=[]){super(),this.params=e}getVarName(e){return String.fromCharCode("i".charCodeAt(0)+e)}getProperties(e){const t=e.getNodeProperties(this);if(void 0!==t.stackNode)return t;const r={};for(let e=0,t=this.params.length-1;e<t;e++){const t=this.params[e],s=!0!==t.isNode&&t.name||this.getVarName(e),i=!0!==t.isNode&&t.type||"int";r[s]=pu(s,i)}const s=e.addStack();return t.returnsNode=this.params[this.params.length-1](r,s,e),t.stackNode=s,e.removeStack(),t}getNodeType(e){const{returnsNode:t}=this.getProperties(e);return t?t.getNodeType(e):"void"}setup(e){this.getProperties(e)}generate(e){const t=this.getProperties(e),r=this.params,s=t.stackNode;for(let t=0,s=r.length-1;t<s;t++){const s=r[t];let i=null,n=null,o=null,a=null,u=null,l=null;s.isNode?(a="int",o=this.getVarName(t),i="0",n=s.build(e,a),u="<"):(a=s.type||"int",o=s.name||this.getVarName(t),i=s.start,n=s.end,u=s.condition,l=s.update,"number"==typeof i?i=e.generateConst(a,i):i&&i.isNode&&(i=i.build(e,a)),"number"==typeof n?n=e.generateConst(a,n):n&&n.isNode&&(n=n.build(e,a)),void 0!==i&&void 0===n?(i+=" - 1",n="0",u=">="):void 0!==n&&void 0===i&&(i="0",u="<"),void 0===u&&(u=Number(i)>Number(n)?">=":"<"));const d={start:i,end:n,condition:u},c=d.start,h=d.end;let p="",g="",m="";l||(l="int"===a||"uint"===a?u.includes("<")?"++":"--":u.includes("<")?"+= 1.":"-= 1."),p+=e.getVar(a,o)+" = "+c,g+=o+" "+u+" "+h,m+=o+" "+l;const f=`for ( ${p}; ${g}; ${m} )`;e.addFlowCode((0===t?"\n":"")+e.tab+f+" {\n\n").addFlowTab()}const i=s.build(e,"void"),n=t.returnsNode?t.returnsNode.build(e):"";e.removeFlowTab().addFlowCode("\n"+e.tab+i);for(let t=0,r=this.params.length-1;t<r;t++)e.addFlowCode((0===t?"":e.tab)+"}\n\n").removeFlowTab();return e.addFlowTab(),n}}const dc=(...e)=>xi(new lc(_i(e,"int"))).append(),cc=()=>pu("break").append(),hc=new WeakMap,pc=new s,gc=Si((({bufferMap:e,influence:t,stride:r,width:s,depth:i,offset:n})=>{const o=Bi(Kd).mul(r).add(n),a=o.div(s),u=o.sub(a.mul(s));return Ru(e,Ii(u,a)).depth(i).mul(t)}));class mc extends Us{static get type(){return"MorphNode"}constructor(e){super("void"),this.mesh=e,this.morphBaseInfluence=nn(1),this.updateType=As.OBJECT}setup(e){const{geometry:r}=e,s=void 0!==r.morphAttributes.position,i=r.hasAttribute("normal")&&void 0!==r.morphAttributes.normal,n=r.morphAttributes.position||r.morphAttributes.normal||r.morphAttributes.color,o=void 0!==n?n.length:0,{texture:a,stride:u,size:l}=function(e){const r=void 0!==e.morphAttributes.position,s=void 0!==e.morphAttributes.normal,i=void 0!==e.morphAttributes.color,n=e.morphAttributes.position||e.morphAttributes.normal||e.morphAttributes.color,o=void 0!==n?n.length:0;let a=hc.get(e);if(void 0===a||a.count!==o){void 0!==a&&a.texture.dispose();const u=e.morphAttributes.position||[],l=e.morphAttributes.normal||[],d=e.morphAttributes.color||[];let c=0;!0===r&&(c=1),!0===s&&(c=2),!0===i&&(c=3);let h=e.attributes.position.count*c,p=1;const g=4096;h>g&&(p=Math.ceil(h/g),h=g);const m=new Float32Array(h*p*4*o),f=new C(m,h,p,o);f.type=E,f.needsUpdate=!0;const y=4*c;for(let x=0;x<o;x++){const T=u[x],_=l[x],v=d[x],N=h*p*4*x;for(let S=0;S<T.count;S++){const A=S*y;!0===r&&(pc.fromBufferAttribute(T,S),m[N+A+0]=pc.x,m[N+A+1]=pc.y,m[N+A+2]=pc.z,m[N+A+3]=0),!0===s&&(pc.fromBufferAttribute(_,S),m[N+A+4]=pc.x,m[N+A+5]=pc.y,m[N+A+6]=pc.z,m[N+A+7]=0),!0===i&&(pc.fromBufferAttribute(v,S),m[N+A+8]=pc.x,m[N+A+9]=pc.y,m[N+A+10]=pc.z,m[N+A+11]=4===v.itemSize?pc.w:1)}}function b(){f.dispose(),hc.delete(e),e.removeEventListener("dispose",b)}a={count:o,texture:f,stride:c,size:new t(h,p)},hc.set(e,a),e.addEventListener("dispose",b)}return a}(r);!0===s&&el.mulAssign(this.morphBaseInfluence),!0===i&&dl.mulAssign(this.morphBaseInfluence);const d=Bi(l.width);dc(o,(({i:e})=>{const t=Mi(0).toVar();this.mesh.count>1&&null!==this.mesh.morphTexture&&void 0!==this.mesh.morphTexture?t.assign(Ru(this.mesh.morphTexture,Ii(Bi(e).add(1),Bi(Xd))).r):t.assign(Ul("morphTargetInfluences","float").element(e).toVar()),!0===s&&el.addAssign(gc({bufferMap:a,influence:t,stride:u,width:d,depth:e,offset:Bi(0)})),!0===i&&dl.addAssign(gc({bufferMap:a,influence:t,stride:u,width:d,depth:e,offset:Bi(1)}))}))}update(){const e=this.morphBaseInfluence;this.mesh.geometry.morphTargetsRelative?e.value=1:e.value=1-this.mesh.morphTargetInfluences.reduce(((e,t)=>e+t),0)}}const fc=vi(mc);class yc extends Us{static get type(){return"LightingNode"}constructor(){super("vec3"),this.isLightingNode=!0}}class bc extends yc{static get type(){return"AONode"}constructor(e=null){super(),this.aoNode=e}setup(e){e.context.ambientOcclusion.mulAssign(this.aoNode)}}class xc extends Ca{static get type(){return"LightingContextNode"}constructor(e,t=null,r=null,s=null){super(e),this.lightingModel=t,this.backdropNode=r,this.backdropAlphaNode=s,this._value=null}getContext(){const{backdropNode:e,backdropAlphaNode:t}=this,r={directDiffuse:Vi().toVar("directDiffuse"),directSpecular:Vi().toVar("directSpecular"),indirectDiffuse:Vi().toVar("indirectDiffuse"),indirectSpecular:Vi().toVar("indirectSpecular")};return{radiance:Vi().toVar("radiance"),irradiance:Vi().toVar("irradiance"),iblIrradiance:Vi().toVar("iblIrradiance"),ambientOcclusion:Mi(1).toVar("ambientOcclusion"),reflectedLight:r,backdrop:e,backdropAlpha:t}}setup(e){return this.value=this._value||(this._value=this.getContext()),this.value.lightingModel=this.lightingModel||e.context.lightingModel,super.setup(e)}}const Tc=vi(xc);class _c extends yc{static get type(){return"IrradianceNode"}constructor(e){super(),this.node=e}setup(e){e.context.irradiance.addAssign(this.node)}}let vc,Nc;class Sc extends Us{static get type(){return"ScreenNode"}constructor(e){super(),this.scope=e,this.isViewportNode=!0}getNodeType(){return this.scope===Sc.VIEWPORT?"vec4":"vec2"}getUpdateType(){let e=As.NONE;return this.scope!==Sc.SIZE&&this.scope!==Sc.VIEWPORT||(e=As.RENDER),this.updateType=e,e}update({renderer:e}){const t=e.getRenderTarget();this.scope===Sc.VIEWPORT?null!==t?Nc.copy(t.viewport):(e.getViewport(Nc),Nc.multiplyScalar(e.getPixelRatio())):null!==t?(vc.width=t.width,vc.height=t.height):e.getDrawingBufferSize(vc)}setup(){const e=this.scope;let r=null;return r=e===Sc.SIZE?nn(vc||(vc=new t)):e===Sc.VIEWPORT?nn(Nc||(Nc=new s)):Pi(Cc.div(Rc)),r}generate(e){if(this.scope===Sc.COORDINATE){let t=e.getFragCoord();if(e.isFlipY()){const r=e.getNodeProperties(Rc).outputNode.build(e);t=`${e.getType("vec2")}( ${t}.x, ${r}.y - ${t}.y )`}return t}return super.generate(e)}}Sc.COORDINATE="coordinate",Sc.VIEWPORT="viewport",Sc.SIZE="size",Sc.UV="uv";const Ac=Ni(Sc,Sc.UV),Rc=Ni(Sc,Sc.SIZE),Cc=Ni(Sc,Sc.COORDINATE),Ec=Ni(Sc,Sc.VIEWPORT),wc=Ec.zw,Mc=Cc.sub(Ec.xy),Bc=Mc.div(wc),Fc=Si((()=>(console.warn('TSL.ViewportNode: "viewportResolution" is deprecated. Use "screenSize" instead.'),Rc)),"vec2").once()(),Uc=Si((()=>(console.warn('TSL.ViewportNode: "viewportTopLeft" is deprecated. Use "screenUV" instead.'),Ac)),"vec2").once()(),Pc=Si((()=>(console.warn('TSL.ViewportNode: "viewportBottomLeft" is deprecated. Use "screenUV.flipY()" instead.'),Ac.flipY())),"vec2").once()(),Ic=new t;class Dc extends Su{static get type(){return"ViewportTextureNode"}constructor(e=Ac,t=null,r=null){null===r&&((r=new w).minFilter=M),super(r,e,t),this.generateMipmaps=!1,this.isOutputTextureNode=!0,this.updateBeforeType=As.FRAME}updateBefore(e){const t=e.renderer;t.getDrawingBufferSize(Ic);const r=this.value;r.image.width===Ic.width&&r.image.height===Ic.height||(r.image.width=Ic.width,r.image.height=Ic.height,r.needsUpdate=!0);const s=r.generateMipmaps;r.generateMipmaps=this.generateMipmaps,t.copyFramebufferToTexture(r),r.generateMipmaps=s}clone(){const e=new this.constructor(this.uvNode,this.levelNode,this.value);return e.generateMipmaps=this.generateMipmaps,e}}const Lc=vi(Dc),Vc=vi(Dc,null,null,{generateMipmaps:!0});let Oc=null;class Gc extends Dc{static get type(){return"ViewportDepthTextureNode"}constructor(e=Ac,t=null){null===Oc&&(Oc=new B),super(e,t,Oc)}}const kc=vi(Gc);class zc extends Us{static get type(){return"ViewportDepthNode"}constructor(e,t=null){super("float"),this.scope=e,this.valueNode=t,this.isViewportDepthNode=!0}generate(e){const{scope:t}=this;return t===zc.DEPTH_BASE?e.getFragDepth():super.generate(e)}setup({camera:e}){const{scope:t}=this,r=this.valueNode;let s=null;if(t===zc.DEPTH_BASE)null!==r&&(s=qc().assign(r));else if(t===zc.DEPTH)s=e.isPerspectiveCamera?Hc(il.z,Cu,Eu):$c(il.z,Cu,Eu);else if(t===zc.LINEAR_DEPTH)if(null!==r)if(e.isPerspectiveCamera){const e=Wc(r,Cu,Eu);s=$c(e,Cu,Eu)}else s=r;else s=$c(il.z,Cu,Eu);return s}}zc.DEPTH_BASE="depthBase",zc.DEPTH="depth",zc.LINEAR_DEPTH="linearDepth";const $c=(e,t,r)=>e.add(t).div(t.sub(r)),Hc=(e,t,r)=>t.add(e).mul(r).div(r.sub(t).mul(e)),Wc=(e,t,r)=>t.mul(r).div(r.sub(t).mul(e).sub(r)),jc=(e,t,r)=>{t=t.max(1e-6).toVar();const s=No(e.negate().div(t)),i=No(r.div(t));return s.div(i)},qc=vi(zc,zc.DEPTH_BASE),Kc=Ni(zc,zc.DEPTH),Xc=vi(zc,zc.LINEAR_DEPTH),Yc=Xc(kc());Kc.assign=e=>qc(e);const Qc=vi(class extends Us{constructor(e){super("float"),this.name=e,this.isBuiltinNode=!0}generate(){return this.name}});class Zc extends Us{static get type(){return"ClippingNode"}constructor(e=Zc.DEFAULT){super(),this.scope=e}setup(e){super.setup(e);const t=e.clippingContext,{intersectionPlanes:r,unionPlanes:s}=t;return this.hardwareClipping=e.material.hardwareClipping,this.scope===Zc.ALPHA_TO_COVERAGE?this.setupAlphaToCoverage(r,s):this.scope===Zc.HARDWARE?this.setupHardwareClipping(s,e):this.setupDefault(r,s)}setupAlphaToCoverage(e,t){return Si((()=>{const r=Mi().toVar("distanceToPlane"),s=Mi().toVar("distanceToGradient"),i=Mi(1).toVar("clipOpacity"),n=t.length;if(!1===this.hardwareClipping&&n>0){const e=Ml(t);dc(n,(({i:t})=>{const n=e.element(t);r.assign(il.dot(n.xyz).negate().add(n.w)),s.assign(r.fwidth().div(2)),i.mulAssign(fa(s.negate(),s,r))}))}const o=e.length;if(o>0){const t=Ml(e),n=Mi(1).toVar("intersectionClipOpacity");dc(o,(({i:e})=>{const i=t.element(e);r.assign(il.dot(i.xyz).negate().add(i.w)),s.assign(r.fwidth().div(2)),n.mulAssign(fa(s.negate(),s,r).oneMinus())})),i.mulAssign(n.oneMinus())}ln.a.mulAssign(i),ln.a.equal(0).discard()}))()}setupDefault(e,t){return Si((()=>{const r=t.length;if(!1===this.hardwareClipping&&r>0){const e=Ml(t);dc(r,(({i:t})=>{const r=e.element(t);il.dot(r.xyz).greaterThan(r.w).discard()}))}const s=e.length;if(s>0){const t=Ml(e),r=Ui(!0).toVar("clipped");dc(s,(({i:e})=>{const s=t.element(e);r.assign(il.dot(s.xyz).greaterThan(s.w).and(r))})),r.discard()}}))()}setupHardwareClipping(e,t){const r=e.length;return t.enableHardwareClipping(r),Si((()=>{const s=Ml(e),i=Qc(t.getClipDistance());dc(r,(({i:e})=>{const t=s.element(e),r=il.dot(t.xyz).sub(t.w).negate();i.element(e).assign(r)}))}))()}}Zc.ALPHA_TO_COVERAGE="alphaToCoverage",Zc.DEFAULT="default",Zc.HARDWARE="hardware";const Jc=Si((([e])=>wo(Hn(1e4,Mo(Hn(17,e.x).add(Hn(.1,e.y)))).mul(zn(.1,Do(Mo(Hn(13,e.y).add(e.x)))))))),eh=Si((([e])=>Jc(Pi(Jc(e.xy),e.z)))),th=Si((([e])=>{const t=Qo(Vo(ko(e.xyz)),Vo(zo(e.xyz))),r=Mi(1).div(Mi(.05).mul(t)).toVar("pixScale"),s=Pi(_o(Ro(No(r))),_o(Co(No(r)))),i=Pi(eh(Ro(s.x.mul(e.xyz))),eh(Ro(s.y.mul(e.xyz)))),n=wo(No(r)),o=zn(Hn(n.oneMinus(),i.x),Hn(n,i.y)),a=Yo(n,n.oneMinus()),u=Vi(o.mul(o).div(Hn(2,a).mul($n(1,a))),o.sub(Hn(.5,a)).div($n(1,a)),$n(1,$n(1,o).mul($n(1,o)).div(Hn(2,a).mul($n(1,a))))),l=o.lessThan(a.oneMinus()).select(o.lessThan(a).select(u.x,u.y),u.z);return pa(l,1e-6,1)})).setLayout({name:"getAlphaHashThreshold",type:"float",inputs:[{name:"position",type:"vec3"}]});class rh extends F{static get type(){return"NodeMaterial"}get type(){return this.constructor.type}set type(e){}constructor(){super(),this.isNodeMaterial=!0,this.fog=!0,this.lights=!1,this.hardwareClipping=!1,this.lightsNode=null,this.envNode=null,this.aoNode=null,this.colorNode=null,this.normalNode=null,this.opacityNode=null,this.backdropNode=null,this.backdropAlphaNode=null,this.alphaTestNode=null,this.positionNode=null,this.geometryNode=null,this.depthNode=null,this.shadowPositionNode=null,this.receivedShadowNode=null,this.castShadowNode=null,this.outputNode=null,this.mrtNode=null,this.fragmentNode=null,this.vertexNode=null}customProgramCacheKey(){return this.type+cs(this)}build(e){this.setup(e)}setupObserver(e){return new os(e)}setup(e){e.context.setupNormal=()=>this.setupNormal(e),e.context.setupPositionView=()=>this.setupPositionView(e),e.context.setupModelViewProjection=()=>this.setupModelViewProjection(e);const t=e.renderer,r=t.getRenderTarget();e.addStack();const s=this.vertexNode||this.setupVertex(e);let i;e.stack.outputNode=s,this.setupHardwareClipping(e),null!==this.geometryNode&&(e.stack.outputNode=e.stack.outputNode.bypass(this.geometryNode)),e.addFlow("vertex",e.removeStack()),e.addStack();const n=this.setupClipping(e);if(!0!==this.depthWrite&&!0!==this.depthTest||(null!==r?!0===r.depthBuffer&&this.setupDepth(e):!0===t.depth&&this.setupDepth(e)),null===this.fragmentNode){this.setupDiffuseColor(e),this.setupVariants(e);const s=this.setupLighting(e);null!==n&&e.stack.add(n);const o=zi(s,ln.a).max(0);if(i=this.setupOutput(e,o),Cn.assign(i),null!==this.outputNode&&(i=this.outputNode),null!==r){const e=t.getMRT(),r=this.mrtNode;null!==e?(i=e,null!==r&&(i=e.merge(r))):null!==r&&(i=r)}}else{let t=this.fragmentNode;!0!==t.isOutputStructNode&&(t=zi(t)),i=this.setupOutput(e,t)}e.stack.outputNode=i,e.addFlow("fragment",e.removeStack()),e.monitor=this.setupObserver(e)}setupClipping(e){if(null===e.clippingContext)return null;const{unionPlanes:t,intersectionPlanes:r}=e.clippingContext;let s=null;if(t.length>0||r.length>0){const t=e.renderer.samples;this.alphaToCoverage&&t>1?s=xi(new Zc(Zc.ALPHA_TO_COVERAGE)):e.stack.add(xi(new Zc))}return s}setupHardwareClipping(e){if(this.hardwareClipping=!1,null===e.clippingContext)return;const t=e.clippingContext.unionPlanes.length;t>0&&t<=8&&e.isAvailable("clipDistance")&&(e.stack.add(xi(new Zc(Zc.HARDWARE))),this.hardwareClipping=!0)}setupDepth(e){const{renderer:t,camera:r}=e;let s=this.depthNode;if(null===s){const e=t.getMRT();e&&e.has("depth")?s=e.get("depth"):!0===t.logarithmicDepthBuffer&&(s=r.isPerspectiveCamera?jc(il.z,Cu,Eu):$c(il.z,Cu,Eu))}null!==s&&Kc.assign(s).append()}setupPositionView(){return Xu.mul(el).xyz}setupModelViewProjection(){return wu.mul(il)}setupVertex(e){return e.addStack(),this.setupPosition(e),e.context.vertex=e.removeStack(),jd}setupPosition(e){const{object:t,geometry:r}=e;if((r.morphAttributes.position||r.morphAttributes.normal||r.morphAttributes.color)&&fc(t).append(),!0===t.isSkinnedMesh&&uc(t).append(),this.displacementMap){const e=Dl("displacementMap","texture"),t=Dl("displacementScale","float"),r=Dl("displacementBias","float");el.addAssign(dl.normalize().mul(e.x.mul(t).add(r)))}return t.isBatchedMesh&&nc(t).append(),t.isInstancedMesh&&t.instanceMatrix&&!0===t.instanceMatrix.isInstancedBufferAttribute&&sc(t).append(),null!==this.positionNode&&el.assign(this.positionNode.context({isPositionNodeInput:!0})),el}setupDiffuseColor({object:e,geometry:t}){let r=this.colorNode?zi(this.colorNode):ld;if(!0===this.vertexColors&&t.hasAttribute("color")&&(r=zi(r.xyz.mul(bu("color","vec3")),r.a)),e.instanceColor){r=un("vec3","vInstanceColor").mul(r)}if(e.isBatchedMesh&&e._colorsTexture){r=un("vec3","vBatchColor").mul(r)}ln.assign(r);const s=this.opacityNode?Mi(this.opacityNode):hd;if(ln.a.assign(ln.a.mul(s)),null!==this.alphaTestNode||this.alphaTest>0){const e=null!==this.alphaTestNode?Mi(this.alphaTestNode):ud;ln.a.lessThanEqual(e).discard()}!0===this.alphaHash&&ln.a.lessThan(th(el)).discard(),!1===this.transparent&&this.blending===U&&!1===this.alphaToCoverage&&ln.a.assign(1)}setupVariants(){}setupOutgoingLight(){return!0===this.lights?Vi(0):ln.rgb}setupNormal(){return this.normalNode?Vi(this.normalNode):Td}setupEnvironment(){let e=null;return this.envNode?e=this.envNode:this.envMap&&(e=this.envMap.isCubeTexture?Dl("envMap","cubeTexture"):Dl("envMap","texture")),e}setupLightMap(e){let t=null;return e.material.lightMap&&(t=new _c($d)),t}setupLights(e){const t=[],r=this.setupEnvironment(e);r&&r.isLightingNode&&t.push(r);const s=this.setupLightMap(e);if(s&&s.isLightingNode&&t.push(s),null!==this.aoNode||e.material.aoMap){const e=null!==this.aoNode?this.aoNode:Hd;t.push(new bc(e))}let i=this.lightsNode||e.lightsNode;return t.length>0&&(i=e.renderer.lighting.createNode([...i.getLights(),...t])),i}setupLightingModel(){}setupLighting(e){const{material:t}=e,{backdropNode:r,backdropAlphaNode:s,emissiveNode:i}=this,n=!0===this.lights||null!==this.lightsNode?this.setupLights(e):null;let o=this.setupOutgoingLight(e);if(n&&n.getScope().hasLights){const t=this.setupLightingModel(e);o=Tc(n,t,r,s)}else null!==r&&(o=Vi(null!==s?ha(o,r,s):r));return(i&&!0===i.isNode||t.emissive&&!0===t.emissive.isColor)&&(dn.assign(Vi(i||cd)),o=o.add(dn)),o}setupOutput(e,t){if(!0===this.fog){const r=e.fogNode;r&&(Cn.assign(t),t=zi(r))}return t}setDefaultValues(e){for(const t in e){const r=e[t];void 0===this[t]&&(this[t]=r,r&&r.clone&&(this[t]=r.clone()))}const t=Object.getOwnPropertyDescriptors(e.constructor.prototype);for(const e in t)void 0===Object.getOwnPropertyDescriptor(this.constructor.prototype,e)&&void 0!==t[e].get&&Object.defineProperty(this.constructor.prototype,e,t[e])}toJSON(e){const t=void 0===e||"string"==typeof e;t&&(e={textures:{},images:{},nodes:{}});const r=F.prototype.toJSON.call(this,e),s=hs(this);r.inputNodes={};for(const{property:t,childNode:i}of s)r.inputNodes[t]=i.toJSON(e).uuid;function i(e){const t=[];for(const r in e){const s=e[r];delete s.metadata,t.push(s)}return t}if(t){const t=i(e.textures),s=i(e.images),n=i(e.nodes);t.length>0&&(r.textures=t),s.length>0&&(r.images=s),n.length>0&&(r.nodes=n)}return r}copy(e){return this.lightsNode=e.lightsNode,this.envNode=e.envNode,this.colorNode=e.colorNode,this.normalNode=e.normalNode,this.opacityNode=e.opacityNode,this.backdropNode=e.backdropNode,this.backdropAlphaNode=e.backdropAlphaNode,this.alphaTestNode=e.alphaTestNode,this.positionNode=e.positionNode,this.geometryNode=e.geometryNode,this.depthNode=e.depthNode,this.shadowPositionNode=e.shadowPositionNode,this.receivedShadowNode=e.receivedShadowNode,this.castShadowNode=e.castShadowNode,this.outputNode=e.outputNode,this.mrtNode=e.mrtNode,this.fragmentNode=e.fragmentNode,this.vertexNode=e.vertexNode,super.copy(e)}}const sh=new P;class ih extends rh{static get type(){return"InstancedPointsNodeMaterial"}constructor(e={}){super(),this.isInstancedPointsNodeMaterial=!0,this.useColor=e.vertexColors,this.pointWidth=1,this.pointColorNode=null,this.pointWidthNode=null,this._useAlphaToCoverage=!0,this.setDefaultValues(sh),this.setValues(e)}setup(e){const{renderer:t}=e,r=this._useAlphaToCoverage,s=this.useColor;this.vertexNode=Si((()=>{const e=bu("instancePosition").xyz,t=zi(Xu.mul(zi(e,1))),r=Ec.z.div(Ec.w),s=wu.mul(t),i=Ju.xy.toVar();return i.mulAssign(this.pointWidthNode?this.pointWidthNode:kd),i.assign(i.div(Ec.z)),i.y.assign(i.y.mul(r)),i.assign(i.mul(s.w)),s.addAssign(zi(i,0,0)),s}))(),this.fragmentNode=Si((()=>{const e=Mi(1).toVar(),i=ca(xu().mul(2).sub(1));if(r&&t.samples>1){const t=Mi(i.fwidth()).toVar();e.assign(fa(t.oneMinus(),t.add(1),i).oneMinus())}else i.greaterThan(1).discard();let n;if(this.pointColorNode)n=this.pointColorNode;else if(s){n=bu("instanceColor").mul(ld)}else n=ld;return e.mulAssign(hd),zi(n,e)}))(),super.setup(e)}get alphaToCoverage(){return this._useAlphaToCoverage}set alphaToCoverage(e){this._useAlphaToCoverage!==e&&(this._useAlphaToCoverage=e,this.needsUpdate=!0)}}const nh=new I;class oh extends rh{static get type(){return"LineBasicNodeMaterial"}constructor(e){super(),this.isLineBasicNodeMaterial=!0,this.setDefaultValues(nh),this.setValues(e)}}const ah=new D;class uh extends rh{static get type(){return"LineDashedNodeMaterial"}constructor(e){super(),this.isLineDashedNodeMaterial=!0,this.setDefaultValues(ah),this.dashOffset=0,this.offsetNode=null,this.dashScaleNode=null,this.dashSizeNode=null,this.gapSizeNode=null,this.setValues(e)}setupVariants(){const e=this.offsetNode?Mi(this.offsetNode):Gd,t=this.dashScaleNode?Mi(this.dashScaleNode):Dd,r=this.dashSizeNode?Mi(this.dashSizeNode):Ld,s=this.gapSizeNode?Mi(this.gapSizeNode):Vd;En.assign(r),wn.assign(s);const i=Pa(bu("lineDistance").mul(t));(e?i.add(e):i).mod(En.add(wn)).greaterThan(En).discard()}}let lh=null;class dh extends Dc{static get type(){return"ViewportSharedTextureNode"}constructor(e=Ac,t=null){null===lh&&(lh=new w),super(e,t,lh)}updateReference(){return this}}const ch=vi(dh),hh=new D;class ph extends rh{static get type(){return"Line2NodeMaterial"}constructor(e={}){super(),this.isLine2NodeMaterial=!0,this.setDefaultValues(hh),this.useColor=e.vertexColors,this.dashOffset=0,this.lineWidth=1,this.lineColorNode=null,this.offsetNode=null,this.dashScaleNode=null,this.dashSizeNode=null,this.gapSizeNode=null,this.blending=L,this._useDash=e.dashed,this._useAlphaToCoverage=!0,this._useWorldUnits=!1,this.setValues(e)}setup(e){const{renderer:t}=e,r=this._useAlphaToCoverage,s=this.useColor,i=this._useDash,n=this._useWorldUnits,o=Si((({start:e,end:t})=>{const r=wu.element(2).element(2),s=wu.element(3).element(2).mul(-.5).div(r).sub(e.z).div(t.z.sub(e.z));return zi(ha(e.xyz,t.xyz,s),t.w)})).setLayout({name:"trimSegment",type:"vec4",inputs:[{name:"start",type:"vec4"},{name:"end",type:"vec4"}]});this.vertexNode=Si((()=>{const e=bu("instanceStart"),t=bu("instanceEnd"),r=zi(Xu.mul(zi(e,1))).toVar("start"),s=zi(Xu.mul(zi(t,1))).toVar("end");if(i){const e=this.dashScaleNode?Mi(this.dashScaleNode):Dd,t=this.offsetNode?Mi(this.offsetNode):Gd,r=bu("instanceDistanceStart"),s=bu("instanceDistanceEnd");let i=Ju.y.lessThan(.5).select(e.mul(r),e.mul(s));i=i.add(t),un("float","lineDistance").assign(i)}n&&(un("vec3","worldStart").assign(r.xyz),un("vec3","worldEnd").assign(s.xyz));const a=Ec.z.div(Ec.w),u=wu.element(2).element(3).equal(-1);Ci(u,(()=>{Ci(r.z.lessThan(0).and(s.z.greaterThan(0)),(()=>{s.assign(o({start:r,end:s}))})).ElseIf(s.z.lessThan(0).and(r.z.greaterThanEqual(0)),(()=>{r.assign(o({start:s,end:r}))}))}));const l=wu.mul(r),d=wu.mul(s),c=l.xyz.div(l.w),h=d.xyz.div(d.w),p=h.xy.sub(c.xy).toVar();p.x.assign(p.x.mul(a)),p.assign(p.normalize());const g=zi().toVar();if(n){const e=s.xyz.sub(r.xyz).normalize(),t=ha(r.xyz,s.xyz,.5).normalize(),n=e.cross(t).normalize(),o=e.cross(n),a=un("vec4","worldPos");a.assign(Ju.y.lessThan(.5).select(r,s));const u=Od.mul(.5);a.addAssign(zi(Ju.x.lessThan(0).select(n.mul(u),n.mul(u).negate()),0)),i||(a.addAssign(zi(Ju.y.lessThan(.5).select(e.mul(u).negate(),e.mul(u)),0)),a.addAssign(zi(o.mul(u),0)),Ci(Ju.y.greaterThan(1).or(Ju.y.lessThan(0)),(()=>{a.subAssign(zi(o.mul(2).mul(u),0))}))),g.assign(wu.mul(a));const l=Vi().toVar();l.assign(Ju.y.lessThan(.5).select(c,h)),g.z.assign(l.z.mul(g.w))}else{const e=Pi(p.y,p.x.negate()).toVar("offset");p.x.assign(p.x.div(a)),e.x.assign(e.x.div(a)),e.assign(Ju.x.lessThan(0).select(e.negate(),e)),Ci(Ju.y.lessThan(0),(()=>{e.assign(e.sub(p))})).ElseIf(Ju.y.greaterThan(1),(()=>{e.assign(e.add(p))})),e.assign(e.mul(Od)),e.assign(e.div(Ec.w)),g.assign(Ju.y.lessThan(.5).select(l,d)),e.assign(e.mul(g.w)),g.assign(g.add(zi(e,0,0)))}return g}))();const a=Si((({p1:e,p2:t,p3:r,p4:s})=>{const i=e.sub(r),n=s.sub(r),o=t.sub(e),a=i.dot(n),u=n.dot(o),l=i.dot(o),d=n.dot(n),c=o.dot(o).mul(d).sub(u.mul(u)),h=a.mul(u).sub(l.mul(d)).div(c).clamp(),p=a.add(u.mul(h)).div(d).clamp();return Pi(h,p)}));if(this.colorNode=Si((()=>{const e=xu();if(i){const t=this.dashSizeNode?Mi(this.dashSizeNode):Ld,r=this.gapSizeNode?Mi(this.gapSizeNode):Vd;En.assign(t),wn.assign(r);const s=un("float","lineDistance");e.y.lessThan(-1).or(e.y.greaterThan(1)).discard(),s.mod(En.add(wn)).greaterThan(En).discard()}const o=Mi(1).toVar("alpha");if(n){const e=un("vec3","worldStart"),s=un("vec3","worldEnd"),n=un("vec4","worldPos").xyz.normalize().mul(1e5),u=s.sub(e),l=a({p1:e,p2:s,p3:Vi(0,0,0),p4:n}),d=e.add(u.mul(l.x)),c=n.mul(l.y),h=d.sub(c).length().div(Od);if(!i)if(r&&t.samples>1){const e=h.fwidth();o.assign(fa(e.negate().add(.5),e.add(.5),h).oneMinus())}else h.greaterThan(.5).discard()}else if(r&&t.samples>1){const t=e.x,r=e.y.greaterThan(0).select(e.y.sub(1),e.y.add(1)),s=t.mul(t).add(r.mul(r)),i=Mi(s.fwidth()).toVar("dlen");Ci(e.y.abs().greaterThan(1),(()=>{o.assign(fa(i.oneMinus(),i.add(1),s).oneMinus())}))}else Ci(e.y.abs().greaterThan(1),(()=>{const t=e.x,r=e.y.greaterThan(0).select(e.y.sub(1),e.y.add(1));t.mul(t).add(r.mul(r)).greaterThan(1).discard()}));let u;if(this.lineColorNode)u=this.lineColorNode;else if(s){const e=bu("instanceColorStart"),t=bu("instanceColorEnd");u=Ju.y.lessThan(.5).select(e,t).mul(ld)}else u=ld;return zi(u,o)}))(),this.transparent){const e=this.opacityNode?Mi(this.opacityNode):hd;this.outputNode=zi(this.colorNode.rgb.mul(e).add(ch().rgb.mul(e.oneMinus())),this.colorNode.a)}super.setup(e)}get worldUnits(){return this._useWorldUnits}set worldUnits(e){this._useWorldUnits!==e&&(this._useWorldUnits=e,this.needsUpdate=!0)}get dashed(){return this._useDash}set dashed(e){this._useDash!==e&&(this._useDash=e,this.needsUpdate=!0)}get alphaToCoverage(){return this._useAlphaToCoverage}set alphaToCoverage(e){this._useAlphaToCoverage!==e&&(this._useAlphaToCoverage=e,this.needsUpdate=!0)}}const gh=e=>xi(e).mul(.5).add(.5),mh=new V;class fh extends rh{static get type(){return"MeshNormalNodeMaterial"}constructor(e){super(),this.isMeshNormalNodeMaterial=!0,this.setDefaultValues(mh),this.setValues(e)}setupDiffuseColor(){const e=this.opacityNode?Mi(this.opacityNode):hd;ln.assign(zi(gh(gl),e))}}class yh extends Ds{static get type(){return"EquirectUVNode"}constructor(e=sl){super("vec2"),this.dirNode=e}setup(){const e=this.dirNode,t=e.z.atan(e.x).mul(1/(2*Math.PI)).add(.5),r=e.y.clamp(-1,1).asin().mul(1/Math.PI).add(.5);return Pi(t,r)}}const bh=vi(yh);class xh extends O{constructor(e=1,t={}){super(e,t),this.isCubeRenderTarget=!0}fromEquirectangularTexture(e,t){const r=t.minFilter,s=t.generateMipmaps;t.generateMipmaps=!0,this.texture.type=t.type,this.texture.colorSpace=t.colorSpace,this.texture.generateMipmaps=t.generateMipmaps,this.texture.minFilter=t.minFilter,this.texture.magFilter=t.magFilter;const i=new G(5,5,5),n=bh(sl),o=new rh;o.colorNode=Au(t,n,0),o.side=T,o.blending=L;const a=new k(i,o),u=new z;u.add(a),t.minFilter===M&&(t.minFilter=$);const l=new H(1,10,this),d=e.getMRT();return e.setMRT(null),l.update(e,u),e.setMRT(d),t.minFilter=r,t.currentGenerateMipmaps=s,a.geometry.dispose(),a.material.dispose(),this}}const Th=new WeakMap;class _h extends Ds{static get type(){return"CubeMapNode"}constructor(e){super("vec3"),this.envNode=e,this._cubeTexture=null,this._cubeTextureNode=Al();const t=new W;t.isRenderTargetTexture=!0,this._defaultTexture=t,this.updateBeforeType=As.RENDER}updateBefore(e){const{renderer:t,material:r}=e,s=this.envNode;if(s.isTextureNode||s.isMaterialReferenceNode){const e=s.isTextureNode?s.value:r[s.property];if(e&&e.isTexture){const r=e.mapping;if(r===j||r===q){if(Th.has(e)){const t=Th.get(e);Nh(t,e.mapping),this._cubeTexture=t}else{const r=e.image;if(function(e){return null!=e&&e.height>0}(r)){const s=new xh(r.height);s.fromEquirectangularTexture(t,e),Nh(s.texture,e.mapping),this._cubeTexture=s.texture,Th.set(e,s.texture),e.addEventListener("dispose",vh)}else this._cubeTexture=this._defaultTexture}this._cubeTextureNode.value=this._cubeTexture}else this._cubeTextureNode=this.envNode}}}setup(e){return this.updateBefore(e),this._cubeTextureNode}}function vh(e){const t=e.target;t.removeEventListener("dispose",vh);const r=Th.get(t);void 0!==r&&(Th.delete(t),r.dispose())}function Nh(e,t){t===j?e.mapping=_:t===q&&(e.mapping=v)}const Sh=vi(_h);class Ah extends yc{static get type(){return"BasicEnvironmentNode"}constructor(e=null){super(),this.envNode=e}setup(e){e.context.environment=Sh(this.envNode)}}class Rh extends yc{static get type(){return"BasicLightMapNode"}constructor(e=null){super(),this.lightMapNode=e}setup(e){const t=Mi(1/Math.PI);e.context.irradianceLightMap=this.lightMapNode.mul(t)}}class Ch{start(){}finish(){}direct(){}directRectArea(){}indirect(){}ambientOcclusion(){}}class Eh extends Ch{constructor(){super()}indirect(e,t,r){const s=e.ambientOcclusion,i=e.reflectedLight,n=r.context.irradianceLightMap;i.indirectDiffuse.assign(zi(0)),n?i.indirectDiffuse.addAssign(n):i.indirectDiffuse.addAssign(zi(1,1,1,0)),i.indirectDiffuse.mulAssign(s),i.indirectDiffuse.mulAssign(ln.rgb)}finish(e,t,r){const s=r.material,i=e.outgoingLight,n=r.context.environment;if(n)switch(s.combine){case Y:i.rgb.assign(ha(i.rgb,i.rgb.mul(n.rgb),fd.mul(yd)));break;case X:i.rgb.assign(ha(i.rgb,n.rgb,fd.mul(yd)));break;case K:i.rgb.addAssign(n.rgb.mul(fd.mul(yd)));break;default:console.warn("THREE.BasicLightingModel: Unsupported .combine value:",s.combine)}}}const wh=new Q;class Mh extends rh{static get type(){return"MeshBasicNodeMaterial"}constructor(e){super(),this.isMeshBasicNodeMaterial=!0,this.lights=!0,this.setDefaultValues(wh),this.setValues(e)}setupNormal(){return hl}setupEnvironment(e){const t=super.setupEnvironment(e);return t?new Ah(t):null}setupLightMap(e){let t=null;return e.material.lightMap&&(t=new Rh($d)),t}setupOutgoingLight(){return ln.rgb}setupLightingModel(){return new Eh}}const Bh=Si((({f0:e,f90:t,dotVH:r})=>{const s=r.mul(-5.55473).sub(6.98316).mul(r).exp2();return e.mul(s.oneMinus()).add(t.mul(s))})),Fh=Si((e=>e.diffuseColor.mul(1/Math.PI))),Uh=Si((({dotNH:e})=>Rn.mul(Mi(.5)).add(1).mul(Mi(1/Math.PI)).mul(e.pow(Rn)))),Ph=Si((({lightDirection:e})=>{const t=e.add(nl).normalize(),r=gl.dot(t).clamp(),s=nl.dot(t).clamp(),i=Bh({f0:Sn,f90:1,dotVH:s}),n=Mi(.25),o=Uh({dotNH:r});return i.mul(n).mul(o)}));class Ih extends Eh{constructor(e=!0){super(),this.specular=e}direct({lightDirection:e,lightColor:t,reflectedLight:r}){const s=gl.dot(e).clamp().mul(t);r.directDiffuse.addAssign(s.mul(Fh({diffuseColor:ln.rgb}))),!0===this.specular&&r.directSpecular.addAssign(s.mul(Ph({lightDirection:e})).mul(fd))}indirect({ambientOcclusion:e,irradiance:t,reflectedLight:r}){r.indirectDiffuse.addAssign(t.mul(Fh({diffuseColor:ln}))),r.indirectDiffuse.mulAssign(e)}}const Dh=new Z;class Lh extends rh{static get type(){return"MeshLambertNodeMaterial"}constructor(e){super(),this.isMeshLambertNodeMaterial=!0,this.lights=!0,this.setDefaultValues(Dh),this.setValues(e)}setupEnvironment(e){const t=super.setupEnvironment(e);return t?new Ah(t):null}setupLightingModel(){return new Ih(!1)}}const Vh=new J;class Oh extends rh{static get type(){return"MeshPhongNodeMaterial"}constructor(e){super(),this.isMeshPhongNodeMaterial=!0,this.lights=!0,this.shininessNode=null,this.specularNode=null,this.setDefaultValues(Vh),this.setValues(e)}setupEnvironment(e){const t=super.setupEnvironment(e);return t?new Ah(t):null}setupLightingModel(){return new Ih}setupVariants(){const e=(this.shininessNode?Mi(this.shininessNode):dd).max(1e-4);Rn.assign(e);const t=this.specularNode||pd;Sn.assign(t)}copy(e){return this.shininessNode=e.shininessNode,this.specularNode=e.specularNode,super.copy(e)}}const Gh=Si((e=>{if(!1===e.geometry.hasAttribute("normal"))return Mi(0);const t=hl.dFdx().abs().max(hl.dFdy().abs());return t.x.max(t.y).max(t.z)})),kh=Si((e=>{const{roughness:t}=e,r=Gh();let s=t.max(.0525);return s=s.add(r),s=s.min(1),s})),zh=Si((({alpha:e,dotNL:t,dotNV:r})=>{const s=e.pow2(),i=t.mul(s.add(s.oneMinus().mul(r.pow2())).sqrt()),n=r.mul(s.add(s.oneMinus().mul(t.pow2())).sqrt());return Wn(.5,i.add(n).max(ho))})).setLayout({name:"V_GGX_SmithCorrelated",type:"float",inputs:[{name:"alpha",type:"float"},{name:"dotNL",type:"float"},{name:"dotNV",type:"float"}]}),$h=Si((({alphaT:e,alphaB:t,dotTV:r,dotBV:s,dotTL:i,dotBL:n,dotNV:o,dotNL:a})=>{const u=a.mul(Vi(e.mul(r),t.mul(s),o).length()),l=o.mul(Vi(e.mul(i),t.mul(n),a).length());return Wn(.5,u.add(l)).saturate()})).setLayout({name:"V_GGX_SmithCorrelated_Anisotropic",type:"float",inputs:[{name:"alphaT",type:"float",qualifier:"in"},{name:"alphaB",type:"float",qualifier:"in"},{name:"dotTV",type:"float",qualifier:"in"},{name:"dotBV",type:"float",qualifier:"in"},{name:"dotTL",type:"float",qualifier:"in"},{name:"dotBL",type:"float",qualifier:"in"},{name:"dotNV",type:"float",qualifier:"in"},{name:"dotNL",type:"float",qualifier:"in"}]}),Hh=Si((({alpha:e,dotNH:t})=>{const r=e.pow2(),s=t.pow2().mul(r.oneMinus()).oneMinus();return r.div(s.pow2()).mul(1/Math.PI)})).setLayout({name:"D_GGX",type:"float",inputs:[{name:"alpha",type:"float"},{name:"dotNH",type:"float"}]}),Wh=Mi(1/Math.PI),jh=Si((({alphaT:e,alphaB:t,dotNH:r,dotTH:s,dotBH:i})=>{const n=e.mul(t),o=Vi(t.mul(s),e.mul(i),n.mul(r)),a=o.dot(o),u=n.div(a);return Wh.mul(n.mul(u.pow2()))})).setLayout({name:"D_GGX_Anisotropic",type:"float",inputs:[{name:"alphaT",type:"float",qualifier:"in"},{name:"alphaB",type:"float",qualifier:"in"},{name:"dotNH",type:"float",qualifier:"in"},{name:"dotTH",type:"float",qualifier:"in"},{name:"dotBH",type:"float",qualifier:"in"}]}),qh=Si((e=>{const{lightDirection:t,f0:r,f90:s,roughness:i,f:n,USE_IRIDESCENCE:o,USE_ANISOTROPY:a}=e,u=e.normalView||gl,l=i.pow2(),d=t.add(nl).normalize(),c=u.dot(t).clamp(),h=u.dot(nl).clamp(),p=u.dot(d).clamp(),g=nl.dot(d).clamp();let m,f,y=Bh({f0:r,f90:s,dotVH:g});if(fi(o)&&(y=yn.mix(y,n)),fi(a)){const e=vn.dot(t),r=vn.dot(nl),s=vn.dot(d),i=Nn.dot(t),n=Nn.dot(nl),o=Nn.dot(d);m=$h({alphaT:Tn,alphaB:l,dotTV:r,dotBV:n,dotTL:e,dotBL:i,dotNV:h,dotNL:c}),f=jh({alphaT:Tn,alphaB:l,dotNH:p,dotTH:s,dotBH:o})}else m=zh({alpha:l,dotNL:c,dotNV:h}),f=Hh({alpha:l,dotNH:p});return y.mul(m).mul(f)})),Kh=Si((({roughness:e,dotNV:t})=>{const r=zi(-1,-.0275,-.572,.022),s=zi(1,.0425,1.04,-.04),i=e.mul(r).add(s),n=i.x.mul(i.x).min(t.mul(-9.28).exp2()).mul(i.x).add(i.y);return Pi(-1.04,1.04).mul(n).add(i.zw)})).setLayout({name:"DFGApprox",type:"vec2",inputs:[{name:"roughness",type:"float"},{name:"dotNV",type:"vec3"}]}),Xh=Si((e=>{const{dotNV:t,specularColor:r,specularF90:s,roughness:i}=e,n=Kh({dotNV:t,roughness:i});return r.mul(n.x).add(s.mul(n.y))})),Yh=Si((({f:e,f90:t,dotVH:r})=>{const s=r.oneMinus().saturate(),i=s.mul(s),n=s.mul(i,i).clamp(0,.9999);return e.sub(Vi(t).mul(n)).div(n.oneMinus())})).setLayout({name:"Schlick_to_F0",type:"vec3",inputs:[{name:"f",type:"vec3"},{name:"f90",type:"float"},{name:"dotVH",type:"float"}]}),Qh=Si((({roughness:e,dotNH:t})=>{const r=e.pow2(),s=Mi(1).div(r),i=t.pow2().oneMinus().max(.0078125);return Mi(2).add(s).mul(i.pow(s.mul(.5))).div(2*Math.PI)})).setLayout({name:"D_Charlie",type:"float",inputs:[{name:"roughness",type:"float"},{name:"dotNH",type:"float"}]}),Zh=Si((({dotNV:e,dotNL:t})=>Mi(1).div(Mi(4).mul(t.add(e).sub(t.mul(e)))))).setLayout({name:"V_Neubelt",type:"float",inputs:[{name:"dotNV",type:"float"},{name:"dotNL",type:"float"}]}),Jh=Si((({lightDirection:e})=>{const t=e.add(nl).normalize(),r=gl.dot(e).clamp(),s=gl.dot(nl).clamp(),i=gl.dot(t).clamp(),n=Qh({roughness:fn,dotNH:i}),o=Zh({dotNV:s,dotNL:r});return mn.mul(n).mul(o)})),ep=Si((({N:e,V:t,roughness:r})=>{const s=e.dot(t).saturate(),i=Pi(r,s.oneMinus().sqrt());return i.assign(i.mul(.984375).add(.0078125)),i})).setLayout({name:"LTC_Uv",type:"vec2",inputs:[{name:"N",type:"vec3"},{name:"V",type:"vec3"},{name:"roughness",type:"float"}]}),tp=Si((({f:e})=>{const t=e.length();return Qo(t.mul(t).add(e.z).div(t.add(1)),0)})).setLayout({name:"LTC_ClippedSphereFormFactor",type:"float",inputs:[{name:"f",type:"vec3"}]}),rp=Si((({v1:e,v2:t})=>{const r=e.dot(t),s=r.abs().toVar(),i=s.mul(.0145206).add(.4965155).mul(s).add(.8543985).toVar(),n=s.add(4.1616724).mul(s).add(3.417594).toVar(),o=i.div(n),a=r.greaterThan(0).select(o,Qo(r.mul(r).oneMinus(),1e-7).inverseSqrt().mul(.5).sub(o));return e.cross(t).mul(a)})).setLayout({name:"LTC_EdgeVectorFormFactor",type:"vec3",inputs:[{name:"v1",type:"vec3"},{name:"v2",type:"vec3"}]}),sp=Si((({N:e,V:t,P:r,mInv:s,p0:i,p1:n,p2:o,p3:a})=>{const u=n.sub(i).toVar(),l=a.sub(i).toVar(),d=u.cross(l),c=Vi().toVar();return Ci(d.dot(r.sub(i)).greaterThanEqual(0),(()=>{const u=t.sub(e.mul(t.dot(e))).normalize(),l=e.cross(u).negate(),d=s.mul(qi(u,l,e).transpose()).toVar(),h=d.mul(i.sub(r)).normalize().toVar(),p=d.mul(n.sub(r)).normalize().toVar(),g=d.mul(o.sub(r)).normalize().toVar(),m=d.mul(a.sub(r)).normalize().toVar(),f=Vi(0).toVar();f.addAssign(rp({v1:h,v2:p})),f.addAssign(rp({v1:p,v2:g})),f.addAssign(rp({v1:g,v2:m})),f.addAssign(rp({v1:m,v2:h})),c.assign(Vi(tp({f:f})))})),c})).setLayout({name:"LTC_Evaluate",type:"vec3",inputs:[{name:"N",type:"vec3"},{name:"V",type:"vec3"},{name:"P",type:"vec3"},{name:"mInv",type:"mat3"},{name:"p0",type:"vec3"},{name:"p1",type:"vec3"},{name:"p2",type:"vec3"},{name:"p3",type:"vec3"}]}),ip=1/6,np=e=>Hn(ip,Hn(e,Hn(e,e.negate().add(3)).sub(3)).add(1)),op=e=>Hn(ip,Hn(e,Hn(e,Hn(3,e).sub(6))).add(4)),ap=e=>Hn(ip,Hn(e,Hn(e,Hn(-3,e).add(3)).add(3)).add(1)),up=e=>Hn(ip,na(e,3)),lp=e=>np(e).add(op(e)),dp=e=>ap(e).add(up(e)),cp=e=>zn(-1,op(e).div(np(e).add(op(e)))),hp=e=>zn(1,up(e).div(ap(e).add(up(e)))),pp=(e,t,r)=>{const s=e.uvNode,i=Hn(s,t.zw).add(.5),n=Ro(i),o=wo(i),a=lp(o.x),u=dp(o.x),l=cp(o.x),d=hp(o.x),c=cp(o.y),h=hp(o.y),p=Pi(n.x.add(l),n.y.add(c)).sub(.5).mul(t.xy),g=Pi(n.x.add(d),n.y.add(c)).sub(.5).mul(t.xy),m=Pi(n.x.add(l),n.y.add(h)).sub(.5).mul(t.xy),f=Pi(n.x.add(d),n.y.add(h)).sub(.5).mul(t.xy),y=lp(o.y).mul(zn(a.mul(e.sample(p).level(r)),u.mul(e.sample(g).level(r)))),b=dp(o.y).mul(zn(a.mul(e.sample(m).level(r)),u.mul(e.sample(f).level(r))));return y.add(b)},gp=Si((([e,t=Mi(3)])=>{const r=Pi(e.size(Bi(t))),s=Pi(e.size(Bi(t.add(1)))),i=Wn(1,r),n=Wn(1,s),o=pp(e,zi(i,r),Ro(t)),a=pp(e,zi(n,s),Co(t));return wo(t).mix(o,a)})),mp=Si((([e,t,r,s,i])=>{const n=Vi(ma(t.negate(),Eo(e),Wn(1,s))),o=Vi(Vo(i[0].xyz),Vo(i[1].xyz),Vo(i[2].xyz));return Eo(n).mul(r.mul(o))})).setLayout({name:"getVolumeTransmissionRay",type:"vec3",inputs:[{name:"n",type:"vec3"},{name:"v",type:"vec3"},{name:"thickness",type:"float"},{name:"ior",type:"float"},{name:"modelMatrix",type:"mat4"}]}),fp=Si((([e,t])=>e.mul(pa(t.mul(2).sub(2),0,1)))).setLayout({name:"applyIorToRoughness",type:"float",inputs:[{name:"roughness",type:"float"},{name:"ior",type:"float"}]}),yp=Vc(),bp=Vc(),xp=Si((([e,t,r],{material:s})=>{const i=(s.side===T?yp:bp).sample(e),n=No(Rc.x).mul(fp(t,r));return gp(i,n)})),Tp=Si((([e,t,r])=>(Ci(r.notEqual(0),(()=>{const s=vo(t).negate().div(r);return To(s.negate().mul(e))})),Vi(1)))).setLayout({name:"volumeAttenuation",type:"vec3",inputs:[{name:"transmissionDistance",type:"float"},{name:"attenuationColor",type:"vec3"},{name:"attenuationDistance",type:"float"}]}),_p=Si((([e,t,r,s,i,n,o,a,u,l,d,c,h,p,g])=>{let m,f;if(g){m=zi().toVar(),f=Vi().toVar();const i=d.sub(1).mul(g.mul(.025)),n=Vi(d.sub(i),d,d.add(i));dc({start:0,end:3},(({i:i})=>{const d=n.element(i),g=mp(e,t,c,d,a),y=o.add(g),b=l.mul(u.mul(zi(y,1))),x=Pi(b.xy.div(b.w)).toVar();x.addAssign(1),x.divAssign(2),x.assign(Pi(x.x,x.y.oneMinus()));const T=xp(x,r,d);m.element(i).assign(T.element(i)),m.a.addAssign(T.a),f.element(i).assign(s.element(i).mul(Tp(Vo(g),h,p).element(i)))})),m.a.divAssign(3)}else{const i=mp(e,t,c,d,a),n=o.add(i),g=l.mul(u.mul(zi(n,1))),y=Pi(g.xy.div(g.w)).toVar();y.addAssign(1),y.divAssign(2),y.assign(Pi(y.x,y.y.oneMinus())),m=xp(y,r,d),f=s.mul(Tp(Vo(i),h,p))}const y=f.rgb.mul(m.rgb),b=e.dot(t).clamp(),x=Vi(Xh({dotNV:b,specularColor:i,specularF90:n,roughness:r})),T=f.r.add(f.g,f.b).div(3);return zi(x.oneMinus().mul(y),m.a.oneMinus().mul(T).oneMinus())})),vp=qi(3.2404542,-.969266,.0556434,-1.5371385,1.8760108,-.2040259,-.4985314,.041556,1.0572252),Np=(e,t)=>e.sub(t).div(e.add(t)).pow2(),Sp=Si((({outsideIOR:e,eta2:t,cosTheta1:r,thinFilmThickness:s,baseF0:i})=>{const n=ha(e,t,fa(0,.03,s)),o=e.div(n).pow2().mul(r.pow2().oneMinus()).oneMinus();Ci(o.lessThan(0),(()=>Vi(1)));const a=o.sqrt(),u=Np(n,e),l=Bh({f0:u,f90:1,dotVH:r}),d=l.oneMinus(),c=n.lessThan(e).select(Math.PI,0),h=Mi(Math.PI).sub(c),p=(e=>{const t=e.sqrt();return Vi(1).add(t).div(Vi(1).sub(t))})(i.clamp(0,.9999)),g=Np(p,n.toVec3()),m=Bh({f0:g,f90:1,dotVH:a}),f=Vi(p.x.lessThan(n).select(Math.PI,0),p.y.lessThan(n).select(Math.PI,0),p.z.lessThan(n).select(Math.PI,0)),y=n.mul(s,a,2),b=Vi(h).add(f),x=l.mul(m).clamp(1e-5,.9999),T=x.sqrt(),_=d.pow2().mul(m).div(Vi(1).sub(x)),v=l.add(_).toVar(),N=_.sub(d).toVar();return dc({start:1,end:2,condition:"<=",name:"m"},(({m:e})=>{N.mulAssign(T);const t=((e,t)=>{const r=e.mul(2*Math.PI*1e-9),s=Vi(54856e-17,44201e-17,52481e-17),i=Vi(1681e3,1795300,2208400),n=Vi(43278e5,93046e5,66121e5),o=Mi(9747e-17*Math.sqrt(2*Math.PI*45282e5)).mul(r.mul(2239900).add(t.x).cos()).mul(r.pow2().mul(-45282e5).exp());let a=s.mul(n.mul(2*Math.PI).sqrt()).mul(i.mul(r).add(t).cos()).mul(r.pow2().negate().mul(n).exp());return a=Vi(a.x.add(o),a.y,a.z).div(1.0685e-7),vp.mul(a)})(Mi(e).mul(y),Mi(e).mul(b)).mul(2);v.addAssign(N.mul(t))})),v.max(Vi(0))})).setLayout({name:"evalIridescence",type:"vec3",inputs:[{name:"outsideIOR",type:"float"},{name:"eta2",type:"float"},{name:"cosTheta1",type:"float"},{name:"thinFilmThickness",type:"float"},{name:"baseF0",type:"vec3"}]}),Ap=Si((({normal:e,viewDir:t,roughness:r})=>{const s=e.dot(t).saturate(),i=r.pow2(),n=Aa(r.lessThan(.25),Mi(-339.2).mul(i).add(Mi(161.4).mul(r)).sub(25.9),Mi(-8.48).mul(i).add(Mi(14.3).mul(r)).sub(9.95)),o=Aa(r.lessThan(.25),Mi(44).mul(i).sub(Mi(23.7).mul(r)).add(3.26),Mi(1.97).mul(i).sub(Mi(3.27).mul(r)).add(.72));return Aa(r.lessThan(.25),0,Mi(.1).mul(r).sub(.025)).add(n.mul(s).add(o).exp()).mul(1/Math.PI).saturate()})),Rp=Vi(.04),Cp=Mi(1);class Ep extends Ch{constructor(e=!1,t=!1,r=!1,s=!1,i=!1,n=!1){super(),this.clearcoat=e,this.sheen=t,this.iridescence=r,this.anisotropy=s,this.transmission=i,this.dispersion=n,this.clearcoatRadiance=null,this.clearcoatSpecularDirect=null,this.clearcoatSpecularIndirect=null,this.sheenSpecularDirect=null,this.sheenSpecularIndirect=null,this.iridescenceFresnel=null,this.iridescenceF0=null}start(e){if(!0===this.clearcoat&&(this.clearcoatRadiance=Vi().toVar("clearcoatRadiance"),this.clearcoatSpecularDirect=Vi().toVar("clearcoatSpecularDirect"),this.clearcoatSpecularIndirect=Vi().toVar("clearcoatSpecularIndirect")),!0===this.sheen&&(this.sheenSpecularDirect=Vi().toVar("sheenSpecularDirect"),this.sheenSpecularIndirect=Vi().toVar("sheenSpecularIndirect")),!0===this.iridescence){const e=gl.dot(nl).clamp();this.iridescenceFresnel=Sp({outsideIOR:Mi(1),eta2:bn,cosTheta1:e,thinFilmThickness:xn,baseF0:Sn}),this.iridescenceF0=Yh({f:this.iridescenceFresnel,f90:1,dotVH:e})}if(!0===this.transmission){const t=rl,r=Pu.sub(rl).normalize(),s=ml;e.backdrop=_p(s,r,cn,ln,Sn,An,t,$u,Bu,wu,Bn,Un,In,Pn,this.dispersion?Dn:null),e.backdropAlpha=Fn,ln.a.mulAssign(ha(1,e.backdrop.a,Fn))}}computeMultiscattering(e,t,r){const s=gl.dot(nl).clamp(),i=Kh({roughness:cn,dotNV:s}),n=(this.iridescenceF0?yn.mix(Sn,this.iridescenceF0):Sn).mul(i.x).add(r.mul(i.y)),o=i.x.add(i.y).oneMinus(),a=Sn.add(Sn.oneMinus().mul(.047619)),u=n.mul(a).div(o.mul(a).oneMinus());e.addAssign(n),t.addAssign(u.mul(o))}direct({lightDirection:e,lightColor:t,reflectedLight:r}){const s=gl.dot(e).clamp().mul(t);if(!0===this.sheen&&this.sheenSpecularDirect.addAssign(s.mul(Jh({lightDirection:e}))),!0===this.clearcoat){const r=fl.dot(e).clamp().mul(t);this.clearcoatSpecularDirect.addAssign(r.mul(qh({lightDirection:e,f0:Rp,f90:Cp,roughness:gn,normalView:fl})))}r.directDiffuse.addAssign(s.mul(Fh({diffuseColor:ln.rgb}))),r.directSpecular.addAssign(s.mul(qh({lightDirection:e,f0:Sn,f90:1,roughness:cn,iridescence:this.iridescence,f:this.iridescenceFresnel,USE_IRIDESCENCE:this.iridescence,USE_ANISOTROPY:this.anisotropy})))}directRectArea({lightColor:e,lightPosition:t,halfWidth:r,halfHeight:s,reflectedLight:i,ltc_1:n,ltc_2:o}){const a=t.add(r).sub(s),u=t.sub(r).sub(s),l=t.sub(r).add(s),d=t.add(r).add(s),c=gl,h=nl,p=il.toVar(),g=ep({N:c,V:h,roughness:cn}),m=n.sample(g).toVar(),f=o.sample(g).toVar(),y=qi(Vi(m.x,0,m.y),Vi(0,1,0),Vi(m.z,0,m.w)).toVar(),b=Sn.mul(f.x).add(Sn.oneMinus().mul(f.y)).toVar();i.directSpecular.addAssign(e.mul(b).mul(sp({N:c,V:h,P:p,mInv:y,p0:a,p1:u,p2:l,p3:d}))),i.directDiffuse.addAssign(e.mul(ln).mul(sp({N:c,V:h,P:p,mInv:qi(1,0,0,0,1,0,0,0,1),p0:a,p1:u,p2:l,p3:d})))}indirect(e,t,r){this.indirectDiffuse(e,t,r),this.indirectSpecular(e,t,r),this.ambientOcclusion(e,t,r)}indirectDiffuse({irradiance:e,reflectedLight:t}){t.indirectDiffuse.addAssign(e.mul(Fh({diffuseColor:ln})))}indirectSpecular({radiance:e,iblIrradiance:t,reflectedLight:r}){if(!0===this.sheen&&this.sheenSpecularIndirect.addAssign(t.mul(mn,Ap({normal:gl,viewDir:nl,roughness:fn}))),!0===this.clearcoat){const e=fl.dot(nl).clamp(),t=Xh({dotNV:e,specularColor:Rp,specularF90:Cp,roughness:gn});this.clearcoatSpecularIndirect.addAssign(this.clearcoatRadiance.mul(t))}const s=Vi().toVar("singleScattering"),i=Vi().toVar("multiScattering"),n=t.mul(1/Math.PI);this.computeMultiscattering(s,i,An);const o=s.add(i),a=ln.mul(o.r.max(o.g).max(o.b).oneMinus());r.indirectSpecular.addAssign(e.mul(s)),r.indirectSpecular.addAssign(i.mul(n)),r.indirectDiffuse.addAssign(a.mul(n))}ambientOcclusion({ambientOcclusion:e,reflectedLight:t}){const r=gl.dot(nl).clamp().add(e),s=cn.mul(-16).oneMinus().negate().exp2(),i=e.sub(r.pow(s).oneMinus()).clamp();!0===this.clearcoat&&this.clearcoatSpecularIndirect.mulAssign(e),!0===this.sheen&&this.sheenSpecularIndirect.mulAssign(e),t.indirectDiffuse.mulAssign(e),t.indirectSpecular.mulAssign(i)}finish(e){const{outgoingLight:t}=e;if(!0===this.clearcoat){const e=fl.dot(nl).clamp(),r=Bh({dotVH:e,f0:Rp,f90:Cp}),s=t.mul(pn.mul(r).oneMinus()).add(this.clearcoatSpecularDirect.add(this.clearcoatSpecularIndirect).mul(pn));t.assign(s)}if(!0===this.sheen){const e=mn.r.max(mn.g).max(mn.b).mul(.157).oneMinus(),r=t.mul(e).add(this.sheenSpecularDirect,this.sheenSpecularIndirect);t.assign(r)}}}const wp=Mi(1),Mp=Mi(-2),Bp=Mi(.8),Fp=Mi(-1),Up=Mi(.4),Pp=Mi(2),Ip=Mi(.305),Dp=Mi(3),Lp=Mi(.21),Vp=Mi(4),Op=Mi(4),Gp=Mi(16),kp=Si((([e])=>{const t=Vi(Do(e)).toVar(),r=Mi(-1).toVar();return Ci(t.x.greaterThan(t.z),(()=>{Ci(t.x.greaterThan(t.y),(()=>{r.assign(Aa(e.x.greaterThan(0),0,3))})).Else((()=>{r.assign(Aa(e.y.greaterThan(0),1,4))}))})).Else((()=>{Ci(t.z.greaterThan(t.y),(()=>{r.assign(Aa(e.z.greaterThan(0),2,5))})).Else((()=>{r.assign(Aa(e.y.greaterThan(0),1,4))}))})),r})).setLayout({name:"getFace",type:"float",inputs:[{name:"direction",type:"vec3"}]}),zp=Si((([e,t])=>{const r=Pi().toVar();return Ci(t.equal(0),(()=>{r.assign(Pi(e.z,e.y).div(Do(e.x)))})).ElseIf(t.equal(1),(()=>{r.assign(Pi(e.x.negate(),e.z.negate()).div(Do(e.y)))})).ElseIf(t.equal(2),(()=>{r.assign(Pi(e.x.negate(),e.y).div(Do(e.z)))})).ElseIf(t.equal(3),(()=>{r.assign(Pi(e.z.negate(),e.y).div(Do(e.x)))})).ElseIf(t.equal(4),(()=>{r.assign(Pi(e.x.negate(),e.z).div(Do(e.y)))})).Else((()=>{r.assign(Pi(e.x,e.y).div(Do(e.z)))})),Hn(.5,r.add(1))})).setLayout({name:"getUV",type:"vec2",inputs:[{name:"direction",type:"vec3"},{name:"face",type:"float"}]}),$p=Si((([e])=>{const t=Mi(0).toVar();return Ci(e.greaterThanEqual(Bp),(()=>{t.assign(wp.sub(e).mul(Fp.sub(Mp)).div(wp.sub(Bp)).add(Mp))})).ElseIf(e.greaterThanEqual(Up),(()=>{t.assign(Bp.sub(e).mul(Pp.sub(Fp)).div(Bp.sub(Up)).add(Fp))})).ElseIf(e.greaterThanEqual(Ip),(()=>{t.assign(Up.sub(e).mul(Dp.sub(Pp)).div(Up.sub(Ip)).add(Pp))})).ElseIf(e.greaterThanEqual(Lp),(()=>{t.assign(Ip.sub(e).mul(Vp.sub(Dp)).div(Ip.sub(Lp)).add(Dp))})).Else((()=>{t.assign(Mi(-2).mul(No(Hn(1.16,e))))})),t})).setLayout({name:"roughnessToMip",type:"float",inputs:[{name:"roughness",type:"float"}]}),Hp=Si((([e,t])=>{const r=e.toVar();r.assign(Hn(2,r).sub(1));const s=Vi(r,1).toVar();return Ci(t.equal(0),(()=>{s.assign(s.zyx)})).ElseIf(t.equal(1),(()=>{s.assign(s.xzy),s.xz.mulAssign(-1)})).ElseIf(t.equal(2),(()=>{s.x.mulAssign(-1)})).ElseIf(t.equal(3),(()=>{s.assign(s.zyx),s.xz.mulAssign(-1)})).ElseIf(t.equal(4),(()=>{s.assign(s.xzy),s.xy.mulAssign(-1)})).ElseIf(t.equal(5),(()=>{s.z.mulAssign(-1)})),s})).setLayout({name:"getDirection",type:"vec3",inputs:[{name:"uv",type:"vec2"},{name:"face",type:"float"}]}),Wp=Si((([e,t,r,s,i,n])=>{const o=Mi(r),a=Vi(t),u=pa($p(o),Mp,n),l=wo(u),d=Ro(u),c=Vi(jp(e,a,d,s,i,n)).toVar();return Ci(l.notEqual(0),(()=>{const t=Vi(jp(e,a,d.add(1),s,i,n)).toVar();c.assign(ha(c,t,l))})),c})),jp=Si((([e,t,r,s,i,n])=>{const o=Mi(r).toVar(),a=Vi(t),u=Mi(kp(a)).toVar(),l=Mi(Qo(Op.sub(o),0)).toVar();o.assign(Qo(o,Op));const d=Mi(_o(o)).toVar(),c=Pi(zp(a,u).mul(d.sub(2)).add(1)).toVar();return Ci(u.greaterThan(2),(()=>{c.y.addAssign(d),u.subAssign(3)})),c.x.addAssign(u.mul(d)),c.x.addAssign(l.mul(Hn(3,Gp))),c.y.addAssign(Hn(4,_o(n).sub(d))),c.x.mulAssign(s),c.y.mulAssign(i),e.sample(c).grad(Pi(),Pi())})),qp=Si((({envMap:e,mipInt:t,outputDirection:r,theta:s,axis:i,CUBEUV_TEXEL_WIDTH:n,CUBEUV_TEXEL_HEIGHT:o,CUBEUV_MAX_MIP:a})=>{const u=Bo(s),l=r.mul(u).add(i.cross(r).mul(Mo(s))).add(i.mul(i.dot(r).mul(u.oneMinus())));return jp(e,l,t,n,o,a)})),Kp=Si((({n:e,latitudinal:t,poleAxis:r,outputDirection:s,weights:i,samples:n,dTheta:o,mipInt:a,envMap:u,CUBEUV_TEXEL_WIDTH:l,CUBEUV_TEXEL_HEIGHT:d,CUBEUV_MAX_MIP:c})=>{const h=Vi(Aa(t,r,ia(r,s))).toVar();Ci(fo(h.equals(Vi(0))),(()=>{h.assign(Vi(s.z,0,s.x.negate()))})),h.assign(Eo(h));const p=Vi().toVar();return p.addAssign(i.element(Bi(0)).mul(qp({theta:0,axis:h,outputDirection:s,mipInt:a,envMap:u,CUBEUV_TEXEL_WIDTH:l,CUBEUV_TEXEL_HEIGHT:d,CUBEUV_MAX_MIP:c}))),dc({start:Bi(1),end:e},(({i:e})=>{Ci(e.greaterThanEqual(n),(()=>{cc()}));const t=Mi(o.mul(Mi(e))).toVar();p.addAssign(i.element(e).mul(qp({theta:t.mul(-1),axis:h,outputDirection:s,mipInt:a,envMap:u,CUBEUV_TEXEL_WIDTH:l,CUBEUV_TEXEL_HEIGHT:d,CUBEUV_MAX_MIP:c}))),p.addAssign(i.element(e).mul(qp({theta:t,axis:h,outputDirection:s,mipInt:a,envMap:u,CUBEUV_TEXEL_WIDTH:l,CUBEUV_TEXEL_HEIGHT:d,CUBEUV_MAX_MIP:c})))})),zi(p,1)}));let Xp=null;const Yp=new WeakMap;function Qp(e){let t=Yp.get(e);if((void 0!==t?t.pmremVersion:-1)!==e.pmremVersion){const r=e.image;if(e.isCubeTexture){if(!function(e){if(null==e)return!1;let t=0;const r=6;for(let s=0;s<r;s++)void 0!==e[s]&&t++;return t===r}(r))return null;t=Xp.fromCubemap(e,t)}else{if(!function(e){return null!=e&&e.height>0}(r))return null;t=Xp.fromEquirectangular(e,t)}t.pmremVersion=e.pmremVersion,Yp.set(e,t)}return t.texture}class Zp extends Ds{static get type(){return"PMREMNode"}constructor(e,t=null,r=null){super("vec3"),this._value=e,this._pmrem=null,this.uvNode=t,this.levelNode=r,this._generator=null;const s=new ee;s.isRenderTargetTexture=!0,this._texture=Au(s),this._width=nn(0),this._height=nn(0),this._maxMip=nn(0),this.updateBeforeType=As.RENDER}set value(e){this._value=e,this._pmrem=null}get value(){return this._value}updateFromTexture(e){const t=function(e){const t=Math.log2(e)-2,r=1/e;return{texelWidth:1/(3*Math.max(Math.pow(2,t),112)),texelHeight:r,maxMip:t}}(e.image.height);this._texture.value=e,this._width.value=t.texelWidth,this._height.value=t.texelHeight,this._maxMip.value=t.maxMip}updateBefore(){let e=this._pmrem;const t=e?e.pmremVersion:-1,r=this._value;t!==r.pmremVersion&&(e=!0===r.isPMREMTexture?r:Qp(r),null!==e&&(this._pmrem=e,this.updateFromTexture(e)))}setup(e){null===Xp&&(Xp=e.createPMREMGenerator()),this.updateBefore(e);let t=this.uvNode;null===t&&e.context.getUV&&(t=e.context.getUV(this));const r=this.value;e.renderer.coordinateSystem===u&&!0!==r.isPMREMTexture&&!0===r.isRenderTargetTexture&&(t=Vi(t.x.negate(),t.yz)),t=Vi(t.x,t.y.negate(),t.z);let s=this.levelNode;return null===s&&e.context.getTextureLevel&&(s=e.context.getTextureLevel(this)),Wp(this._texture,t,s,this._width,this._height,this._maxMip)}}const Jp=vi(Zp),eg=new WeakMap;class tg extends yc{static get type(){return"EnvironmentNode"}constructor(e=null){super(),this.envNode=e}setup(e){const{material:t}=e;let r=this.envNode;if(r.isTextureNode||r.isMaterialReferenceNode){const e=r.isTextureNode?r.value:t[r.property];let s=eg.get(e);void 0===s&&(s=Jp(e),eg.set(e,s)),r=s}const s=t.envMap?Ul("envMapIntensity","float",e.material):Ul("environmentIntensity","float",e.scene),i=!0===t.useAnisotropy||t.anisotropy>0?Zl:gl,n=r.context(rg(cn,i)).mul(s),o=r.context(sg(ml)).mul(Math.PI).mul(s),a=ou(n),u=ou(o);e.context.radiance.addAssign(a),e.context.iblIrradiance.addAssign(u);const l=e.context.lightingModel.clearcoatRadiance;if(l){const e=r.context(rg(gn,fl)).mul(s),t=ou(e);l.addAssign(t)}}}const rg=(e,t)=>{let r=null;return{getUV:()=>(null===r&&(r=nl.negate().reflect(t),r=e.mul(e).mix(r,t).normalize(),r=r.transformDirection(Bu)),r),getTextureLevel:()=>e}},sg=e=>({getUV:()=>e,getTextureLevel:()=>Mi(1)}),ig=new te;class ng extends rh{static get type(){return"MeshStandardNodeMaterial"}constructor(e){super(),this.isMeshStandardNodeMaterial=!0,this.lights=!0,this.emissiveNode=null,this.metalnessNode=null,this.roughnessNode=null,this.setDefaultValues(ig),this.setValues(e)}setupEnvironment(e){let t=super.setupEnvironment(e);return null===t&&e.environmentNode&&(t=e.environmentNode),t?new tg(t):null}setupLightingModel(){return new Ep}setupSpecular(){const e=ha(Vi(.04),ln.rgb,hn);Sn.assign(e),An.assign(1)}setupVariants(){const e=this.metalnessNode?Mi(this.metalnessNode):xd;hn.assign(e);let t=this.roughnessNode?Mi(this.roughnessNode):bd;t=kh({roughness:t}),cn.assign(t),this.setupSpecular(),ln.assign(zi(ln.rgb.mul(e.oneMinus()),ln.a))}copy(e){return this.emissiveNode=e.emissiveNode,this.metalnessNode=e.metalnessNode,this.roughnessNode=e.roughnessNode,super.copy(e)}}const og=new re;class ag extends ng{static get type(){return"MeshPhysicalNodeMaterial"}constructor(e){super(),this.isMeshPhysicalNodeMaterial=!0,this.clearcoatNode=null,this.clearcoatRoughnessNode=null,this.clearcoatNormalNode=null,this.sheenNode=null,this.sheenRoughnessNode=null,this.iridescenceNode=null,this.iridescenceIORNode=null,this.iridescenceThicknessNode=null,this.specularIntensityNode=null,this.specularColorNode=null,this.iorNode=null,this.transmissionNode=null,this.thicknessNode=null,this.attenuationDistanceNode=null,this.attenuationColorNode=null,this.dispersionNode=null,this.anisotropyNode=null,this.setDefaultValues(og),this.setValues(e)}get useClearcoat(){return this.clearcoat>0||null!==this.clearcoatNode}get useIridescence(){return this.iridescence>0||null!==this.iridescenceNode}get useSheen(){return this.sheen>0||null!==this.sheenNode}get useAnisotropy(){return this.anisotropy>0||null!==this.anisotropyNode}get useTransmission(){return this.transmission>0||null!==this.transmissionNode}get useDispersion(){return this.dispersion>0||null!==this.dispersionNode}setupSpecular(){const e=this.iorNode?Mi(this.iorNode):Ud;Bn.assign(e),Sn.assign(ha(Yo(oa(Bn.sub(1).div(Bn.add(1))).mul(md),Vi(1)).mul(gd),ln.rgb,hn)),An.assign(ha(gd,1,hn))}setupLightingModel(){return new Ep(this.useClearcoat,this.useSheen,this.useIridescence,this.useAnisotropy,this.useTransmission,this.useDispersion)}setupVariants(e){if(super.setupVariants(e),this.useClearcoat){const e=this.clearcoatNode?Mi(this.clearcoatNode):_d,t=this.clearcoatRoughnessNode?Mi(this.clearcoatRoughnessNode):vd;pn.assign(e),gn.assign(kh({roughness:t}))}if(this.useSheen){const e=this.sheenNode?Vi(this.sheenNode):Ad,t=this.sheenRoughnessNode?Mi(this.sheenRoughnessNode):Rd;mn.assign(e),fn.assign(t)}if(this.useIridescence){const e=this.iridescenceNode?Mi(this.iridescenceNode):Ed,t=this.iridescenceIORNode?Mi(this.iridescenceIORNode):wd,r=this.iridescenceThicknessNode?Mi(this.iridescenceThicknessNode):Md;yn.assign(e),bn.assign(t),xn.assign(r)}if(this.useAnisotropy){const e=(this.anisotropyNode?Pi(this.anisotropyNode):Cd).toVar();_n.assign(e.length()),Ci(_n.equal(0),(()=>{e.assign(Pi(1,0))})).Else((()=>{e.divAssign(Pi(_n)),_n.assign(_n.saturate())})),Tn.assign(_n.pow2().mix(cn.pow2(),1)),vn.assign(Yl[0].mul(e.x).add(Yl[1].mul(e.y))),Nn.assign(Yl[1].mul(e.x).sub(Yl[0].mul(e.y)))}if(this.useTransmission){const e=this.transmissionNode?Mi(this.transmissionNode):Bd,t=this.thicknessNode?Mi(this.thicknessNode):Fd,r=this.attenuationDistanceNode?Mi(this.attenuationDistanceNode):Pd,s=this.attenuationColorNode?Vi(this.attenuationColorNode):Id;if(Fn.assign(e),Un.assign(t),Pn.assign(r),In.assign(s),this.useDispersion){const e=this.dispersionNode?Mi(this.dispersionNode):zd;Dn.assign(e)}}}setupClearcoatNormal(){return this.clearcoatNormalNode?Vi(this.clearcoatNormalNode):Nd}setup(e){e.context.setupClearcoatNormal=()=>this.setupClearcoatNormal(e),super.setup(e)}copy(e){return this.clearcoatNode=e.clearcoatNode,this.clearcoatRoughnessNode=e.clearcoatRoughnessNode,this.clearcoatNormalNode=e.clearcoatNormalNode,this.sheenNode=e.sheenNode,this.sheenRoughnessNode=e.sheenRoughnessNode,this.iridescenceNode=e.iridescenceNode,this.iridescenceIORNode=e.iridescenceIORNode,this.iridescenceThicknessNode=e.iridescenceThicknessNode,this.specularIntensityNode=e.specularIntensityNode,this.specularColorNode=e.specularColorNode,this.transmissionNode=e.transmissionNode,this.thicknessNode=e.thicknessNode,this.attenuationDistanceNode=e.attenuationDistanceNode,this.attenuationColorNode=e.attenuationColorNode,this.dispersionNode=e.dispersionNode,this.anisotropyNode=e.anisotropyNode,super.copy(e)}}class ug extends Ep{constructor(e=!1,t=!1,r=!1,s=!1,i=!1,n=!1,o=!1){super(e,t,r,s,i,n),this.useSSS=o}direct({lightDirection:e,lightColor:t,reflectedLight:r},s,i){if(!0===this.useSSS){const s=i.material,{thicknessColorNode:n,thicknessDistortionNode:o,thicknessAmbientNode:a,thicknessAttenuationNode:u,thicknessPowerNode:l,thicknessScaleNode:d}=s,c=e.add(gl.mul(o)).normalize(),h=Mi(nl.dot(c.negate()).saturate().pow(l).mul(d)),p=Vi(h.add(a).mul(n));r.directDiffuse.addAssign(p.mul(u.mul(t)))}super.direct({lightDirection:e,lightColor:t,reflectedLight:r},s,i)}}class lg extends ag{static get type(){return"MeshSSSNodeMaterial"}constructor(e){super(e),this.thicknessColorNode=null,this.thicknessDistortionNode=Mi(.1),this.thicknessAmbientNode=Mi(0),this.thicknessAttenuationNode=Mi(.1),this.thicknessPowerNode=Mi(2),this.thicknessScaleNode=Mi(10)}get useSSS(){return null!==this.thicknessColorNode}setupLightingModel(){return new ug(this.useClearcoat,this.useSheen,this.useIridescence,this.useAnisotropy,this.useTransmission,this.useDispersion,this.useSSS)}copy(e){return this.thicknessColorNode=e.thicknessColorNode,this.thicknessDistortionNode=e.thicknessDistortionNode,this.thicknessAmbientNode=e.thicknessAmbientNode,this.thicknessAttenuationNode=e.thicknessAttenuationNode,this.thicknessPowerNode=e.thicknessPowerNode,this.thicknessScaleNode=e.thicknessScaleNode,super.copy(e)}}const dg=Si((({normal:e,lightDirection:t,builder:r})=>{const s=e.dot(t),i=Pi(s.mul(.5).add(.5),0);if(r.material.gradientMap){const e=Dl("gradientMap","texture").context({getUV:()=>i});return Vi(e.r)}{const e=i.fwidth().mul(.5);return ha(Vi(.7),Vi(1),fa(Mi(.7).sub(e.x),Mi(.7).add(e.x),i.x))}}));class cg extends Ch{direct({lightDirection:e,lightColor:t,reflectedLight:r},s,i){const n=dg({normal:ll,lightDirection:e,builder:i}).mul(t);r.directDiffuse.addAssign(n.mul(Fh({diffuseColor:ln.rgb})))}indirect({ambientOcclusion:e,irradiance:t,reflectedLight:r}){r.indirectDiffuse.addAssign(t.mul(Fh({diffuseColor:ln}))),r.indirectDiffuse.mulAssign(e)}}const hg=new se;class pg extends rh{static get type(){return"MeshToonNodeMaterial"}constructor(e){super(),this.isMeshToonNodeMaterial=!0,this.lights=!0,this.setDefaultValues(hg),this.setValues(e)}setupLightingModel(){return new cg}}class gg extends Ds{static get type(){return"MatcapUVNode"}constructor(){super("vec2")}setup(){const e=Vi(nl.z,0,nl.x.negate()).normalize(),t=nl.cross(e);return Pi(e.dot(gl),t.dot(gl)).mul(.495).add(.5)}}const mg=Ni(gg),fg=new ie;class yg extends rh{static get type(){return"MeshMatcapNodeMaterial"}constructor(e){super(),this.isMeshMatcapNodeMaterial=!0,this.setDefaultValues(fg),this.setValues(e)}setupVariants(e){const t=mg;let r;r=e.material.matcap?Dl("matcap","texture").context({getUV:()=>t}):Vi(ha(.2,.8,t.y)),ln.rgb.mulAssign(r.rgb)}}const bg=new P;class xg extends rh{static get type(){return"PointsNodeMaterial"}constructor(e){super(),this.isPointsNodeMaterial=!0,this.setDefaultValues(bg),this.setValues(e)}}class Tg extends Ds{static get type(){return"RotateNode"}constructor(e,t){super(),this.positionNode=e,this.rotationNode=t}getNodeType(e){return this.positionNode.getNodeType(e)}setup(e){const{rotationNode:t,positionNode:r}=this;if("vec2"===this.getNodeType(e)){const e=t.cos(),s=t.sin();return ji(e,s,s.negate(),e).mul(r)}{const e=t,s=Ki(zi(1,0,0,0),zi(0,Bo(e.x),Mo(e.x).negate(),0),zi(0,Mo(e.x),Bo(e.x),0),zi(0,0,0,1)),i=Ki(zi(Bo(e.y),0,Mo(e.y),0),zi(0,1,0,0),zi(Mo(e.y).negate(),0,Bo(e.y),0),zi(0,0,0,1)),n=Ki(zi(Bo(e.z),Mo(e.z).negate(),0,0),zi(Mo(e.z),Bo(e.z),0,0),zi(0,0,1,0),zi(0,0,0,1));return s.mul(i).mul(n).mul(zi(r,1)).xyz}}}const _g=vi(Tg),vg=new ne;class Ng extends rh{static get type(){return"SpriteNodeMaterial"}constructor(e){super(),this.isSpriteNodeMaterial=!0,this._useSizeAttenuation=!0,this.positionNode=null,this.rotationNode=null,this.scaleNode=null,this.setDefaultValues(vg),this.setValues(e)}setupPositionView(e){const{object:t,camera:r}=e,s=this.sizeAttenuation,{positionNode:i,rotationNode:n,scaleNode:o}=this,a=Xu.mul(Vi(i||0));let u=Pi($u[0].xyz.length(),$u[1].xyz.length());if(null!==o&&(u=u.mul(o)),!1===s)if(r.isPerspectiveCamera)u=u.mul(a.z.negate());else{const e=Mi(2).div(wu.element(1).element(1));u=u.mul(e.mul(2))}let l=Ju.xy;if(t.center&&!0===t.center.isVector2){const e=((e,t,r)=>xi(new ja(e,t,r)))("center","vec2",t);l=l.sub(e.sub(.5))}l=l.mul(u);const d=Mi(n||Sd),c=_g(l,d);return zi(a.xy.add(c),a.zw)}copy(e){return this.positionNode=e.positionNode,this.rotationNode=e.rotationNode,this.scaleNode=e.scaleNode,super.copy(e)}get sizeAttenuation(){return this._useSizeAttenuation}set sizeAttenuation(e){this._useSizeAttenuation!==e&&(this._useSizeAttenuation=e,this.needsUpdate=!0)}}class Sg extends Ch{constructor(){super(),this.shadowNode=Mi(1).toVar("shadowMask")}direct({shadowMask:e}){this.shadowNode.mulAssign(e)}finish(e){ln.a.mulAssign(this.shadowNode.oneMinus()),e.outgoingLight.rgb.assign(ln.rgb)}}const Ag=new oe;class Rg extends rh{static get type(){return"ShadowNodeMaterial"}constructor(e){super(),this.isShadowNodeMaterial=!0,this.lights=!0,this.setDefaultValues(Ag),this.setValues(e)}setupLightingModel(){return new Sg}}const Cg=Si((({texture:e,uv:t})=>{const r=1e-4,s=Vi().toVar();return Ci(t.x.lessThan(r),(()=>{s.assign(Vi(1,0,0))})).ElseIf(t.y.lessThan(r),(()=>{s.assign(Vi(0,1,0))})).ElseIf(t.z.lessThan(r),(()=>{s.assign(Vi(0,0,1))})).ElseIf(t.x.greaterThan(.9999),(()=>{s.assign(Vi(-1,0,0))})).ElseIf(t.y.greaterThan(.9999),(()=>{s.assign(Vi(0,-1,0))})).ElseIf(t.z.greaterThan(.9999),(()=>{s.assign(Vi(0,0,-1))})).Else((()=>{const r=.01,i=e.sample(t.add(Vi(-.01,0,0))).r.sub(e.sample(t.add(Vi(r,0,0))).r),n=e.sample(t.add(Vi(0,-.01,0))).r.sub(e.sample(t.add(Vi(0,r,0))).r),o=e.sample(t.add(Vi(0,0,-.01))).r.sub(e.sample(t.add(Vi(0,0,r))).r);s.assign(Vi(i,n,o))})),s.normalize()}));class Eg extends Su{static get type(){return"Texture3DNode"}constructor(e,t=null,r=null){super(e,t,r),this.isTexture3DNode=!0}getInputType(){return"texture3D"}getDefaultUV(){return Vi(.5,.5,.5)}setUpdateMatrix(){}setupUV(e,t){const r=this.value;return!e.isFlipY()||!0!==r.isRenderTargetTexture&&!0!==r.isFramebufferTexture||(t=this.sampler?t.flipY():t.setY(Bi(_u(this,this.levelNode).y).sub(t.y).sub(1))),t}generateUV(e,t){return t.build(e,"vec3")}normal(e){return Cg({texture:this,uv:e})}}const wg=vi(Eg);class Mg extends rh{static get type(){return"VolumeNodeMaterial"}constructor(t){super(),this.isVolumeNodeMaterial=!0,this.base=new e(16777215),this.map=null,this.steps=100,this.testNode=null,this.setValues(t)}setup(e){const t=wg(this.map,null,0),r=Si((({orig:e,dir:t})=>{const r=Vi(-.5),s=Vi(.5),i=t.reciprocal(),n=r.sub(e).mul(i),o=s.sub(e).mul(i),a=Yo(n,o),u=Qo(n,o),l=Qo(a.x,Qo(a.y,a.z)),d=Yo(u.x,Yo(u.y,u.z));return Pi(l,d)}));this.fragmentNode=Si((()=>{const e=Pa(Vi(Ku.mul(zi(Pu,1)))),s=Pa(Ju.sub(e)).normalize(),i=Pi(r({orig:e,dir:s})).toVar();i.x.greaterThan(i.y).discard(),i.assign(Pi(Qo(i.x,0),i.y));const n=Vi(e.add(i.x.mul(s))).toVar(),o=Vi(s.abs().reciprocal()).toVar(),a=Mi(Yo(o.x,Yo(o.y,o.z))).toVar("delta");a.divAssign(Dl("steps","float"));const u=zi(Dl("base","color"),0).toVar();return dc({type:"float",start:i.x,end:i.y,update:"+= delta"},(()=>{const e=an("float","d").assign(t.sample(n.add(.5)).r);null!==this.testNode?this.testNode({map:t,mapValue:e,probe:n,finalColor:u}).append():(u.a.assign(1),cc()),n.addAssign(s.mul(a))})),u.a.equal(0).discard(),zi(u)}))(),super.setup(e)}}class Bg{constructor(e,t){this.nodes=e,this.info=t,this._context=self,this._animationLoop=null,this._requestId=null}start(){const e=(t,r)=>{this._requestId=this._context.requestAnimationFrame(e),!0===this.info.autoReset&&this.info.reset(),this.nodes.nodeFrame.update(),this.info.frame=this.nodes.nodeFrame.frameId,null!==this._animationLoop&&this._animationLoop(t,r)};e()}stop(){this._context.cancelAnimationFrame(this._requestId),this._requestId=null}setAnimationLoop(e){this._animationLoop=e}setContext(e){this._context=e}dispose(){this.stop()}}class Fg{constructor(){this.weakMap=new WeakMap}get(e){let t=this.weakMap;for(let r=0;r<e.length;r++)if(t=t.get(e[r]),void 0===t)return;return t.get(e[e.length-1])}set(e,t){let r=this.weakMap;for(let t=0;t<e.length;t++){const s=e[t];!1===r.has(s)&&r.set(s,new WeakMap),r=r.get(s)}return r.set(e[e.length-1],t),this}delete(e){let t=this.weakMap;for(let r=0;r<e.length;r++)if(t=t.get(e[r]),void 0===t)return!1;return t.delete(e[e.length-1])}}let Ug=0;class Pg{constructor(e,t,r,s,i,n,o,a,u,l){this.id=Ug++,this._nodes=e,this._geometries=t,this.renderer=r,this.object=s,this.material=i,this.scene=n,this.camera=o,this.lightsNode=a,this.context=u,this.geometry=s.geometry,this.version=i.version,this.drawRange=null,this.attributes=null,this.pipeline=null,this.vertexBuffers=null,this.drawParams=null,this.bundle=null,this.clippingContext=l,this.clippingContextCacheKey=null!==l?l.cacheKey:"",this.initialNodesCacheKey=this.getDynamicCacheKey(),this.initialCacheKey=this.getCacheKey(),this._nodeBuilderState=null,this._bindings=null,this._monitor=null,this.onDispose=null,this.isRenderObject=!0,this.onMaterialDispose=()=>{this.dispose()},this.material.addEventListener("dispose",this.onMaterialDispose)}updateClipping(e){this.clippingContext=e}get clippingNeedsUpdate(){return null!==this.clippingContext&&this.clippingContext.cacheKey!==this.clippingContextCacheKey&&(this.clippingContextCacheKey=this.clippingContext.cacheKey,!0)}get hardwareClippingPlanes(){return!0===this.material.hardwareClipping?this.clippingContext.unionClippingCount:0}getNodeBuilderState(){return this._nodeBuilderState||(this._nodeBuilderState=this._nodes.getForRender(this))}getMonitor(){return this._monitor||(this._monitor=this.getNodeBuilderState().monitor)}getBindings(){return this._bindings||(this._bindings=this.getNodeBuilderState().createBindings())}getIndex(){return this._geometries.getIndex(this)}getIndirect(){return this._geometries.getIndirect(this)}getChainArray(){return[this.object,this.material,this.context,this.lightsNode]}setGeometry(e){this.geometry=e,this.attributes=null}getAttributes(){if(null!==this.attributes)return this.attributes;const e=this.getNodeBuilderState().nodeAttributes,t=this.geometry,r=[],s=new Set;for(const i of e){const e=i.node&&i.node.attribute?i.node.attribute:t.getAttribute(i.name);if(void 0===e)continue;r.push(e);const n=e.isInterleavedBufferAttribute?e.data:e;s.add(n)}return this.attributes=r,this.vertexBuffers=Array.from(s.values()),r}getVertexBuffers(){return null===this.vertexBuffers&&this.getAttributes(),this.vertexBuffers}getDrawParameters(){const{object:e,material:t,geometry:r,group:s,drawRange:i}=this,n=this.drawParams||(this.drawParams={vertexCount:0,firstVertex:0,instanceCount:0,firstInstance:0}),o=this.getIndex(),a=null!==o,u=r.isInstancedBufferGeometry?r.instanceCount:e.count>1?e.count:1;if(0===u)return null;if(n.instanceCount=u,!0===e.isBatchedMesh)return n;let l=1;!0!==t.wireframe||e.isPoints||e.isLineSegments||e.isLine||e.isLineLoop||(l=2);let d=i.start*l,c=(i.start+i.count)*l;null!==s&&(d=Math.max(d,s.start*l),c=Math.min(c,(s.start+s.count)*l));const h=r.attributes.position;let p=1/0;a?p=o.count:null!=h&&(p=h.count),d=Math.max(d,0),c=Math.min(c,p);const g=c-d;return g<0||g===1/0?null:(n.vertexCount=g,n.firstVertex=d,n)}getGeometryCacheKey(){const{geometry:e}=this;let t="";for(const r of Object.keys(e.attributes).sort()){const s=e.attributes[r];t+=r+",",s.data&&(t+=s.data.stride+","),s.offset&&(t+=s.offset+","),s.itemSize&&(t+=s.itemSize+","),s.normalized&&(t+="n,")}return e.index&&(t+="index,"),t}getMaterialCacheKey(){const{object:e,material:t}=this;let r=t.customProgramCacheKey();for(const e of function(e){const t=Object.keys(e);let r=Object.getPrototypeOf(e);for(;r;){const e=Object.getOwnPropertyDescriptors(r);for(const r in e)if(void 0!==e[r]){const s=e[r];s&&"function"==typeof s.get&&t.push(r)}r=Object.getPrototypeOf(r)}return t}(t)){if(/^(is[A-Z]|_)|^(visible|version|uuid|name|opacity|userData)$/.test(e))continue;const s=t[e];let i;if(null!==s){const e=typeof s;"number"===e?i=0!==s?"1":"0":"object"===e?(i="{",s.isTexture&&(i+=s.mapping),i+="}"):i=String(s)}else i=String(s);r+=i+","}return r+=this.clippingContextCacheKey+",",e.geometry&&(r+=this.getGeometryCacheKey()),e.skeleton&&(r+=e.skeleton.bones.length+","),e.morphTargetInfluences&&(r+=e.morphTargetInfluences.length+","),e.isBatchedMesh&&(r+=e._matricesTexture.uuid+",",null!==e._colorsTexture&&(r+=e._colorsTexture.uuid+",")),e.count>1&&(r+=e.uuid+","),r+=e.receiveShadow+",",us(r)}get needsGeometryUpdate(){return this.geometry.id!==this.object.geometry.id}get needsUpdate(){return this.initialNodesCacheKey!==this.getDynamicCacheKey()||this.clippingNeedsUpdate}getDynamicCacheKey(){let e=this._nodes.getCacheKey(this.scene,this.lightsNode);return this.object.receiveShadow&&(e+=1),e}getCacheKey(){return this.getMaterialCacheKey()+this.getDynamicCacheKey()}dispose(){this.material.removeEventListener("dispose",this.onMaterialDispose),this.onDispose()}}const Ig=[];class Dg{constructor(e,t,r,s,i,n){this.renderer=e,this.nodes=t,this.geometries=r,this.pipelines=s,this.bindings=i,this.info=n,this.chainMaps={}}get(e,t,r,s,i,n,o,a){const u=this.getChainMap(a);Ig[0]=e,Ig[1]=t,Ig[2]=n,Ig[3]=i;let l=u.get(Ig);return void 0===l?(l=this.createRenderObject(this.nodes,this.geometries,this.renderer,e,t,r,s,i,n,o,a),u.set(Ig,l)):(l.updateClipping(o),l.needsGeometryUpdate&&l.setGeometry(e.geometry),(l.version!==t.version||l.needsUpdate)&&(l.initialCacheKey!==l.getCacheKey()?(l.dispose(),l=this.get(e,t,r,s,i,n,o,a)):l.version=t.version)),l}getChainMap(e="default"){return this.chainMaps[e]||(this.chainMaps[e]=new Fg)}dispose(){this.chainMaps={}}createRenderObject(e,t,r,s,i,n,o,a,u,l,d){const c=this.getChainMap(d),h=new Pg(e,t,r,s,i,n,o,a,u,l);return h.onDispose=()=>{this.pipelines.delete(h),this.bindings.delete(h),this.nodes.delete(h),c.delete(h.getChainArray())},h}}class Lg{constructor(){this.data=new WeakMap}get(e){let t=this.data.get(e);return void 0===t&&(t={},this.data.set(e,t)),t}delete(e){let t=null;return this.data.has(e)&&(t=this.data.get(e),this.data.delete(e)),t}has(e){return this.data.has(e)}dispose(){this.data=new WeakMap}}const Vg=1,Og=2,Gg=3,kg=4,zg=16;class $g extends Lg{constructor(e){super(),this.backend=e}delete(e){const t=super.delete(e);return void 0!==t&&this.backend.destroyAttribute(e),t}update(e,t){const r=this.get(e);if(void 0===r.version)t===Vg?this.backend.createAttribute(e):t===Og?this.backend.createIndexAttribute(e):t===Gg?this.backend.createStorageAttribute(e):t===kg&&this.backend.createIndirectStorageAttribute(e),r.version=this._getBufferAttribute(e).version;else{const t=this._getBufferAttribute(e);(r.version<t.version||t.usage===m)&&(this.backend.updateAttribute(e),r.version=t.version)}}_getBufferAttribute(e){return e.isInterleavedBufferAttribute&&(e=e.data),e}}function Hg(e){return null!==e.index?e.index.version:e.attributes.position.version}function Wg(e){const t=[],r=e.index,s=e.attributes.position;if(null!==r){const e=r.array;for(let r=0,s=e.length;r<s;r+=3){const s=e[r+0],i=e[r+1],n=e[r+2];t.push(s,i,i,n,n,s)}}else{for(let e=0,r=s.array.length/3-1;e<r;e+=3){const r=e+0,s=e+1,i=e+2;t.push(r,s,s,i,i,r)}}const i=new(function(e){for(let t=e.length-1;t>=0;--t)if(e[t]>=65535)return!0;return!1}(t)?ae:ue)(t,1);return i.version=Hg(e),i}class jg extends Lg{constructor(e,t){super(),this.attributes=e,this.info=t,this.wireframes=new WeakMap,this.attributeCall=new WeakMap}has(e){const t=e.geometry;return super.has(t)&&!0===this.get(t).initialized}updateForRender(e){!1===this.has(e)&&this.initGeometry(e),this.updateAttributes(e)}initGeometry(e){const t=e.geometry;this.get(t).initialized=!0,this.info.memory.geometries++;const r=()=>{this.info.memory.geometries--;const s=t.index,i=e.getAttributes();null!==s&&this.attributes.delete(s);for(const e of i)this.attributes.delete(e);const n=this.wireframes.get(t);void 0!==n&&this.attributes.delete(n),t.removeEventListener("dispose",r)};t.addEventListener("dispose",r)}updateAttributes(e){const t=e.getAttributes();for(const e of t)e.isStorageBufferAttribute||e.isStorageInstancedBufferAttribute?this.updateAttribute(e,Gg):this.updateAttribute(e,Vg);const r=this.getIndex(e);null!==r&&this.updateAttribute(r,Og);const s=e.geometry.indirect;null!==s&&this.updateAttribute(s,kg)}updateAttribute(e,t){const r=this.info.render.calls;e.isInterleavedBufferAttribute?void 0===this.attributeCall.get(e)?(this.attributes.update(e,t),this.attributeCall.set(e,r)):this.attributeCall.get(e.data)!==r&&(this.attributes.update(e,t),this.attributeCall.set(e.data,r),this.attributeCall.set(e,r)):this.attributeCall.get(e)!==r&&(this.attributes.update(e,t),this.attributeCall.set(e,r))}getIndirect(e){return e.geometry.indirect}getIndex(e){const{geometry:t,material:r}=e;let s=t.index;if(!0===r.wireframe){const e=this.wireframes;let r=e.get(t);void 0===r?(r=Wg(t),e.set(t,r)):r.version!==Hg(t)&&(this.attributes.delete(r),r=Wg(t),e.set(t,r)),s=r}return s}}class qg{constructor(){this.autoReset=!0,this.frame=0,this.calls=0,this.render={calls:0,frameCalls:0,drawCalls:0,triangles:0,points:0,lines:0,timestamp:0,previousFrameCalls:0,timestampCalls:0},this.compute={calls:0,frameCalls:0,timestamp:0,previousFrameCalls:0,timestampCalls:0},this.memory={geometries:0,textures:0}}update(e,t,r){this.render.drawCalls++,e.isMesh||e.isSprite?this.render.triangles+=r*(t/3):e.isPoints?this.render.points+=r*t:e.isLineSegments?this.render.lines+=r*(t/2):e.isLine?this.render.lines+=r*(t-1):console.error("THREE.WebGPUInfo: Unknown object type.")}updateTimestamp(e,t){0===this[e].timestampCalls&&(this[e].timestamp=0),this[e].timestamp+=t,this[e].timestampCalls++,this[e].timestampCalls>=this[e].previousFrameCalls&&(this[e].timestampCalls=0)}reset(){const e=this.render.frameCalls;this.render.previousFrameCalls=e;const t=this.compute.frameCalls;this.compute.previousFrameCalls=t,this.render.drawCalls=0,this.render.frameCalls=0,this.compute.frameCalls=0,this.render.triangles=0,this.render.points=0,this.render.lines=0}dispose(){this.reset(),this.calls=0,this.render.calls=0,this.compute.calls=0,this.render.timestamp=0,this.compute.timestamp=0,this.memory.geometries=0,this.memory.textures=0}}class Kg{constructor(e){this.cacheKey=e,this.usedTimes=0}}class Xg extends Kg{constructor(e,t,r){super(e),this.vertexProgram=t,this.fragmentProgram=r}}class Yg extends Kg{constructor(e,t){super(e),this.computeProgram=t,this.isComputePipeline=!0}}let Qg=0;class Zg{constructor(e,t,r,s=null,i=null){this.id=Qg++,this.code=e,this.stage=t,this.name=r,this.transforms=s,this.attributes=i,this.usedTimes=0}}class Jg extends Lg{constructor(e,t){super(),this.backend=e,this.nodes=t,this.bindings=null,this.caches=new Map,this.programs={vertex:new Map,fragment:new Map,compute:new Map}}getForCompute(e,t){const{backend:r}=this,s=this.get(e);if(this._needsComputeUpdate(e)){const i=s.pipeline;i&&(i.usedTimes--,i.computeProgram.usedTimes--);const n=this.nodes.getForCompute(e);let o=this.programs.compute.get(n.computeShader);void 0===o&&(i&&0===i.computeProgram.usedTimes&&this._releaseProgram(i.computeProgram),o=new Zg(n.computeShader,"compute",e.name,n.transforms,n.nodeAttributes),this.programs.compute.set(n.computeShader,o),r.createProgram(o));const a=this._getComputeCacheKey(e,o);let u=this.caches.get(a);void 0===u&&(i&&0===i.usedTimes&&this._releasePipeline(i),u=this._getComputePipeline(e,o,a,t)),u.usedTimes++,o.usedTimes++,s.version=e.version,s.pipeline=u}return s.pipeline}getForRender(e,t=null){const{backend:r}=this,s=this.get(e);if(this._needsRenderUpdate(e)){const i=s.pipeline;i&&(i.usedTimes--,i.vertexProgram.usedTimes--,i.fragmentProgram.usedTimes--);const n=e.getNodeBuilderState(),o=e.material?e.material.name:"";let a=this.programs.vertex.get(n.vertexShader);void 0===a&&(i&&0===i.vertexProgram.usedTimes&&this._releaseProgram(i.vertexProgram),a=new Zg(n.vertexShader,"vertex",o),this.programs.vertex.set(n.vertexShader,a),r.createProgram(a));let u=this.programs.fragment.get(n.fragmentShader);void 0===u&&(i&&0===i.fragmentProgram.usedTimes&&this._releaseProgram(i.fragmentProgram),u=new Zg(n.fragmentShader,"fragment",o),this.programs.fragment.set(n.fragmentShader,u),r.createProgram(u));const l=this._getRenderCacheKey(e,a,u);let d=this.caches.get(l);void 0===d?(i&&0===i.usedTimes&&this._releasePipeline(i),d=this._getRenderPipeline(e,a,u,l,t)):e.pipeline=d,d.usedTimes++,a.usedTimes++,u.usedTimes++,s.pipeline=d}return s.pipeline}delete(e){const t=this.get(e).pipeline;return t&&(t.usedTimes--,0===t.usedTimes&&this._releasePipeline(t),t.isComputePipeline?(t.computeProgram.usedTimes--,0===t.computeProgram.usedTimes&&this._releaseProgram(t.computeProgram)):(t.fragmentProgram.usedTimes--,t.vertexProgram.usedTimes--,0===t.vertexProgram.usedTimes&&this._releaseProgram(t.vertexProgram),0===t.fragmentProgram.usedTimes&&this._releaseProgram(t.fragmentProgram))),super.delete(e)}dispose(){super.dispose(),this.caches=new Map,this.programs={vertex:new Map,fragment:new Map,compute:new Map}}updateForRender(e){this.getForRender(e)}_getComputePipeline(e,t,r,s){r=r||this._getComputeCacheKey(e,t);let i=this.caches.get(r);return void 0===i&&(i=new Yg(r,t),this.caches.set(r,i),this.backend.createComputePipeline(i,s)),i}_getRenderPipeline(e,t,r,s,i){s=s||this._getRenderCacheKey(e,t,r);let n=this.caches.get(s);return void 0===n&&(n=new Xg(s,t,r),this.caches.set(s,n),e.pipeline=n,this.backend.createRenderPipeline(e,i)),n}_getComputeCacheKey(e,t){return e.id+","+t.id}_getRenderCacheKey(e,t,r){return t.id+","+r.id+","+this.backend.getRenderCacheKey(e)}_releasePipeline(e){this.caches.delete(e.cacheKey)}_releaseProgram(e){const t=e.code,r=e.stage;this.programs[r].delete(t)}_needsComputeUpdate(e){const t=this.get(e);return void 0===t.pipeline||t.version!==e.version}_needsRenderUpdate(e){return void 0===this.get(e).pipeline||this.backend.needsRenderUpdate(e)}}class em extends Lg{constructor(e,t,r,s,i,n){super(),this.backend=e,this.textures=r,this.pipelines=i,this.attributes=s,this.nodes=t,this.info=n,this.pipelines.bindings=this}getForRender(e){const t=e.getBindings();for(const e of t){const r=this.get(e);void 0===r.bindGroup&&(this._init(e),this.backend.createBindings(e,t,0),r.bindGroup=e)}return t}getForCompute(e){const t=this.nodes.getForCompute(e).bindings;for(const e of t){const r=this.get(e);void 0===r.bindGroup&&(this._init(e),this.backend.createBindings(e,t,0),r.bindGroup=e)}return t}updateForCompute(e){this._updateBindings(this.getForCompute(e))}updateForRender(e){this._updateBindings(this.getForRender(e))}_updateBindings(e){for(const t of e)this._update(t,e)}_init(e){for(const t of e.bindings)if(t.isSampledTexture)this.textures.updateTexture(t.texture);else if(t.isStorageBuffer){const e=t.attribute,r=e.isIndirectStorageBufferAttribute?kg:Gg;this.attributes.update(e,r)}}_update(e,t){const{backend:r}=this;let s=!1,i=!0,n=0,o=0;for(const t of e.bindings){if(t.isNodeUniformsGroup){if(!1===this.nodes.updateGroup(t))continue}if(t.isUniformBuffer){t.update()&&r.updateBinding(t)}else if(t.isSampler)t.update();else if(t.isSampledTexture){const e=this.textures.get(t.texture);t.needsBindingsUpdate(e.generation)&&(s=!0);const a=t.update(),u=t.texture;a&&this.textures.updateTexture(u);const l=r.get(u);if(void 0!==l.externalTexture||e.isDefaultTexture?i=!1:(n=10*n+u.id,o+=u.version),!0===r.isWebGPUBackend&&void 0===l.texture&&void 0===l.externalTexture&&(console.error("Bindings._update: binding should be available:",t,a,u,t.textureNode.value,s),this.textures.updateTexture(u),s=!0),!0===u.isStorageTexture){const e=this.get(u);!0===t.store?e.needsMipmap=!0:this.textures.needsMipmaps(u)&&!0===e.needsMipmap&&(this.backend.generateMipmaps(u),e.needsMipmap=!1)}}}!0===s&&this.backend.updateBindings(e,t,i?n:0,o)}}function tm(e,t){return e.groupOrder!==t.groupOrder?e.groupOrder-t.groupOrder:e.renderOrder!==t.renderOrder?e.renderOrder-t.renderOrder:e.material.id!==t.material.id?e.material.id-t.material.id:e.z!==t.z?e.z-t.z:e.id-t.id}function rm(e,t){return e.groupOrder!==t.groupOrder?e.groupOrder-t.groupOrder:e.renderOrder!==t.renderOrder?e.renderOrder-t.renderOrder:e.z!==t.z?t.z-e.z:e.id-t.id}function sm(e){return(e.transmission>0||e.transmissionNode)&&e.side===le&&!1===e.forceSinglePass}class im{constructor(e,t,r){this.renderItems=[],this.renderItemsIndex=0,this.opaque=[],this.transparentDoublePass=[],this.transparent=[],this.bundles=[],this.lightsNode=e.getNode(t,r),this.lightsArray=[],this.scene=t,this.camera=r,this.occlusionQueryCount=0}begin(){return this.renderItemsIndex=0,this.opaque.length=0,this.transparentDoublePass.length=0,this.transparent.length=0,this.bundles.length=0,this.lightsArray.length=0,this.occlusionQueryCount=0,this}getNextRenderItem(e,t,r,s,i,n,o){let a=this.renderItems[this.renderItemsIndex];return void 0===a?(a={id:e.id,object:e,geometry:t,material:r,groupOrder:s,renderOrder:e.renderOrder,z:i,group:n,clippingContext:o},this.renderItems[this.renderItemsIndex]=a):(a.id=e.id,a.object=e,a.geometry=t,a.material=r,a.groupOrder=s,a.renderOrder=e.renderOrder,a.z=i,a.group=n,a.clippingContext=o),this.renderItemsIndex++,a}push(e,t,r,s,i,n,o){const a=this.getNextRenderItem(e,t,r,s,i,n,o);!0===e.occlusionTest&&this.occlusionQueryCount++,!0===r.transparent||r.transmission>0?(sm(r)&&this.transparentDoublePass.push(a),this.transparent.push(a)):this.opaque.push(a)}unshift(e,t,r,s,i,n,o){const a=this.getNextRenderItem(e,t,r,s,i,n,o);!0===r.transparent||r.transmission>0?(sm(r)&&this.transparentDoublePass.unshift(a),this.transparent.unshift(a)):this.opaque.unshift(a)}pushBundle(e){this.bundles.push(e)}pushLight(e){this.lightsArray.push(e)}sort(e,t){this.opaque.length>1&&this.opaque.sort(e||tm),this.transparentDoublePass.length>1&&this.transparentDoublePass.sort(t||rm),this.transparent.length>1&&this.transparent.sort(t||rm)}finish(){this.lightsNode.setLights(this.lightsArray);for(let e=this.renderItemsIndex,t=this.renderItems.length;e<t;e++){const t=this.renderItems[e];if(null===t.id)break;t.id=null,t.object=null,t.geometry=null,t.material=null,t.groupOrder=null,t.renderOrder=null,t.z=null,t.group=null,t.clippingContext=null}}}class nm{constructor(e){this.lighting=e,this.lists=new Fg}get(e,t){const r=this.lists,s=[e,t];let i=r.get(s);return void 0===i&&(i=new im(this.lighting,e,t),r.set(s,i)),i}dispose(){this.lists=new Fg}}let om=0;class am{constructor(){this.id=om++,this.color=!0,this.clearColor=!0,this.clearColorValue={r:0,g:0,b:0,a:1},this.depth=!0,this.clearDepth=!0,this.clearDepthValue=1,this.stencil=!1,this.clearStencil=!0,this.clearStencilValue=1,this.viewport=!1,this.viewportValue=new s,this.scissor=!1,this.scissorValue=new s,this.textures=null,this.depthTexture=null,this.activeCubeFace=0,this.sampleCount=1,this.width=0,this.height=0,this.isRenderContext=!0}getCacheKey(){return um(this)}}function um(e){const{textures:t,activeCubeFace:r}=e,s=[r];for(const e of t)s.push(e.id);return ls(s)}class lm{constructor(){this.chainMaps={}}get(e=null,t=null,r=null){const s=[];let i;if(null!==e&&s.push(e),null!==t&&s.push(t),0===s.length&&s.push({id:"default"}),null===r)i="default";else{const e=r.texture.format;i=`${r.textures.length}:${e}:${r.samples}:${r.depthBuffer}:${r.stencilBuffer}`}const n=this.getChainMap(i);let o=n.get(s);return void 0===o&&(o=new am,n.set(s,o)),null!==r&&(o.sampleCount=0===r.samples?1:r.samples),o}getChainMap(e){return this.chainMaps[e]||(this.chainMaps[e]=new Fg)}dispose(){this.chainMaps={}}}const dm=new r;class cm extends Lg{constructor(e,t,r){super(),this.renderer=e,this.backend=t,this.info=r}updateRenderTarget(e,t=0){const r=this.get(e),s=0===e.samples?1:e.samples,i=r.depthTextureMips||(r.depthTextureMips={}),n=e.textures,o=this.getSize(n[0]),a=o.width>>t,u=o.height>>t;let l=e.depthTexture||i[t];const d=!0===e.depthBuffer||!0===e.stencilBuffer;let c=!1;void 0===l&&d&&(l=new B,l.format=e.stencilBuffer?de:ce,l.type=e.stencilBuffer?he:b,l.image.width=a,l.image.height=u,i[t]=l),r.width===o.width&&o.height===r.height||(c=!0,l&&(l.needsUpdate=!0,l.image.width=a,l.image.height=u)),r.width=o.width,r.height=o.height,r.textures=n,r.depthTexture=l||null,r.depth=e.depthBuffer,r.stencil=e.stencilBuffer,r.renderTarget=e,r.sampleCount!==s&&(c=!0,l&&(l.needsUpdate=!0),r.sampleCount=s);const h={sampleCount:s};for(let e=0;e<n.length;e++){const t=n[e];c&&(t.needsUpdate=!0),this.updateTexture(t,h)}if(l&&this.updateTexture(l,h),!0!==r.initialized){r.initialized=!0;const t=()=>{e.removeEventListener("dispose",t);for(let e=0;e<n.length;e++)this._destroyTexture(n[e]);l&&this._destroyTexture(l),this.delete(e)};e.addEventListener("dispose",t)}}updateTexture(e,t={}){const r=this.get(e);if(!0===r.initialized&&r.version===e.version)return;const s=e.isRenderTargetTexture||e.isDepthTexture||e.isFramebufferTexture,i=this.backend;if(s&&!0===r.initialized&&(i.destroySampler(e),i.destroyTexture(e)),e.isFramebufferTexture){const t=this.renderer.getRenderTarget();e.type=t?t.texture.type:pe}const{width:n,height:o,depth:a}=this.getSize(e);if(t.width=n,t.height=o,t.depth=a,t.needsMipmaps=this.needsMipmaps(e),t.levels=t.needsMipmaps?this.getMipLevels(e,n,o):1,s||!0===e.isStorageTexture)i.createSampler(e),i.createTexture(e,t),r.generation=e.version;else{if(!0!==r.initialized&&i.createSampler(e),e.version>0){const s=e.image;if(void 0===s)console.warn("THREE.Renderer: Texture marked for update but image is undefined.");else if(!1===s.complete)console.warn("THREE.Renderer: Texture marked for update but image is incomplete.");else{if(e.images){const r=[];for(const t of e.images)r.push(t);t.images=r}else t.image=s;void 0!==r.isDefaultTexture&&!0!==r.isDefaultTexture||(i.createTexture(e,t),r.isDefaultTexture=!1,r.generation=e.version),!0===e.source.dataReady&&i.updateTexture(e,t),t.needsMipmaps&&0===e.mipmaps.length&&i.generateMipmaps(e)}}else i.createDefaultTexture(e),r.isDefaultTexture=!0,r.generation=e.version}if(!0!==r.initialized){r.initialized=!0,r.generation=e.version,this.info.memory.textures++;const t=()=>{e.removeEventListener("dispose",t),this._destroyTexture(e),this.info.memory.textures--};e.addEventListener("dispose",t)}r.version=e.version}getSize(e,t=dm){let r=e.images?e.images[0]:e.image;return r?(void 0!==r.image&&(r=r.image),t.width=r.width||1,t.height=r.height||1,t.depth=e.isCubeTexture?6:r.depth||1):t.width=t.height=t.depth=1,t}getMipLevels(e,t,r){let s;return s=e.isCompressedTexture?e.mipmaps?e.mipmaps.length:1:Math.floor(Math.log2(Math.max(t,r)))+1,s}needsMipmaps(e){return this.isEnvironmentTexture(e)||!0===e.isCompressedTexture||e.generateMipmaps}isEnvironmentTexture(e){const t=e.mapping;return t===j||t===q||t===_||t===v}_destroyTexture(e){this.backend.destroySampler(e),this.backend.destroyTexture(e),this.delete(e)}}class hm extends e{constructor(e,t,r,s=1){super(e,t,r),this.a=s}set(e,t,r,s=1){return this.a=s,super.set(e,t,r)}copy(e){return void 0!==e.a&&(this.a=e.a),super.copy(e)}clone(){return new this.constructor(this.r,this.g,this.b,this.a)}}class pm extends on{static get type(){return"ParameterNode"}constructor(e,t=null){super(e,t),this.isParameterNode=!0}getHash(){return this.uuid}generate(){return this.name}}class gm extends Us{static get type(){return"StackNode"}constructor(e=null){super(),this.nodes=[],this.outputNode=null,this.parent=e,this._currentCond=null,this.isStackNode=!0}getNodeType(e){return this.outputNode?this.outputNode.getNodeType(e):"void"}add(e){return this.nodes.push(e),this}If(e,t){const r=new bi(t);return this._currentCond=Aa(e,r),this.add(this._currentCond)}ElseIf(e,t){const r=new bi(t),s=Aa(e,r);return this._currentCond.elseNode=s,this._currentCond=s,this}Else(e){return this._currentCond.elseNode=new bi(e),this}build(e,...t){const r=Ri();Ai(this);for(const t of this.nodes)t.build(e,"void");return Ai(r),this.outputNode?this.outputNode.build(e,...t):super.build(e,...t)}else(...e){return console.warn("TSL.StackNode: .else() has been renamed to .Else()."),this.Else(...e)}elseif(...e){return console.warn("TSL.StackNode: .elseif() has been renamed to .ElseIf()."),this.ElseIf(...e)}}const mm=vi(gm);class fm extends Us{static get type(){return"OutputStructNode"}constructor(...e){super(),this.members=e,this.isOutputStructNode=!0}setup(e){super.setup(e);const t=this.members,r=[];for(let s=0;s<t.length;s++)r.push(t[s].getNodeType(e));this.nodeType=e.getStructTypeFromNode(this,r).name}generate(e,t){const r=e.getOutputStructName(),s=this.members,i=""!==r?r+".":"";for(let r=0;r<s.length;r++){const n=s[r].build(e,t);e.addLineFlowCode(`${i}m${r} = ${n}`,this)}return r}}const ym=vi(fm);function bm(e,t){for(let r=0;r<e.length;r++)if(e[r].name===t)return r;return-1}class xm extends fm{static get type(){return"MRTNode"}constructor(e){super(),this.outputNodes=e,this.isMRTNode=!0}has(e){return void 0!==this.outputNodes[e]}get(e){return this.outputNodes[e]}merge(e){const t={...this.outputNodes,...e.outputNodes};return Tm(t)}setup(e){const t=this.outputNodes,r=[],s=e.renderer.getRenderTarget().textures;for(const e in t){r[bm(s,e)]=zi(t[e])}return this.members=r,super.setup(e)}}const Tm=vi(xm),_m=Si((([e])=>{const t=e.toUint().mul(747796405).add(2891336453),r=t.shiftRight(t.shiftRight(28).add(4)).bitXor(t).mul(277803737);return r.shiftRight(22).bitXor(r).toFloat().mul(1/2**32)})),vm=(e,t)=>na(Hn(4,e.mul($n(1,e))),t),Nm=Si((([e])=>e.fract().sub(.5).abs())).setLayout({name:"tri",type:"float",inputs:[{name:"x",type:"float"}]}),Sm=Si((([e])=>Vi(Nm(e.z.add(Nm(e.y.mul(1)))),Nm(e.z.add(Nm(e.x.mul(1)))),Nm(e.y.add(Nm(e.x.mul(1))))))).setLayout({name:"tri3",type:"vec3",inputs:[{name:"p",type:"vec3"}]}),Am=Si((([e,t,r])=>{const s=Vi(e).toVar(),i=Mi(1.4).toVar(),n=Mi(0).toVar(),o=Vi(s).toVar();return dc({start:Mi(0),end:Mi(3),type:"float",condition:"<="},(()=>{const e=Vi(Sm(o.mul(2))).toVar();s.addAssign(e.add(r.mul(Mi(.1).mul(t)))),o.mulAssign(1.8),i.mulAssign(1.5),s.mulAssign(1.2);const a=Mi(Nm(s.z.add(Nm(s.x.add(Nm(s.y)))))).toVar();n.addAssign(a.div(i)),o.addAssign(.14)})),n})).setLayout({name:"triNoise3D",type:"float",inputs:[{name:"position",type:"vec3"},{name:"speed",type:"float"},{name:"time",type:"float"}]});class Rm extends Us{static get type(){return"FunctionOverloadingNode"}constructor(e=[],...t){super(),this.functionNodes=e,this.parametersNodes=t,this._candidateFnCall=null,this.global=!0}getNodeType(){return this.functionNodes[0].shaderNode.layout.type}setup(e){const t=this.parametersNodes;let r=this._candidateFnCall;if(null===r){let s=null,i=-1;for(const r of this.functionNodes){const n=r.shaderNode.layout;if(null===n)throw new Error("FunctionOverloadingNode: FunctionNode must be a layout.");const o=n.inputs;if(t.length===o.length){let n=0;for(let r=0;r<t.length;r++){const s=t[r],i=o[r];s.getNodeType(e)===i.type?n++:n=0}n>i&&(s=r,i=n)}}this._candidateFnCall=r=s(...t)}return r}}const Cm=vi(Rm),Em=e=>(...t)=>Cm(e,...t),wm=nn(0).setGroup(tn).onRenderUpdate((e=>e.time)),Mm=nn(0).setGroup(tn).onRenderUpdate((e=>e.deltaTime)),Bm=nn(0,"uint").setGroup(tn).onRenderUpdate((e=>e.frameId)),Fm=Si((([e,t,r=Pi(.5)])=>_g(e.sub(r),t).add(r))),Um=Si((([e,t,r=Pi(.5)])=>{const s=e.sub(r),i=s.dot(s),n=i.mul(i).mul(t);return e.add(s.mul(n))})),Pm=Si((({position:e=null,horizontal:t=!0,vertical:r=!1})=>{let s;null!==e?(s=$u.toVar(),s[3][0]=e.x,s[3][1]=e.y,s[3][2]=e.z):s=$u;const i=Bu.mul(s);return fi(t)&&(i[0][0]=$u[0].length(),i[0][1]=0,i[0][2]=0),fi(r)&&(i[1][0]=0,i[1][1]=$u[1].length(),i[1][2]=0),i[2][0]=0,i[2][1]=0,i[2][2]=1,wu.mul(i).mul(el)})),Im=Si((([e=null])=>{const t=Xc();return Xc(kc(e)).sub(t).lessThan(0).select(Ac,e)}));class Dm extends Us{static get type(){return"SpriteSheetUVNode"}constructor(e,t=xu(),r=Mi(0)){super("vec2"),this.countNode=e,this.uvNode=t,this.frameNode=r}setup(){const{frameNode:e,uvNode:t,countNode:r}=this,{width:s,height:i}=r,n=e.mod(s.mul(i)).floor(),o=n.mod(s),a=i.sub(n.add(1).div(s).ceil()),u=r.reciprocal(),l=Pi(o,a);return t.add(l).mul(u)}}const Lm=vi(Dm);class Vm extends Us{static get type(){return"TriplanarTexturesNode"}constructor(e,t=null,r=null,s=Mi(1),i=el,n=dl){super("vec4"),this.textureXNode=e,this.textureYNode=t,this.textureZNode=r,this.scaleNode=s,this.positionNode=i,this.normalNode=n}setup(){const{textureXNode:e,textureYNode:t,textureZNode:r,scaleNode:s,positionNode:i,normalNode:n}=this;let o=n.abs().normalize();o=o.div(o.dot(Vi(1)));const a=i.yz.mul(s),u=i.zx.mul(s),l=i.xy.mul(s),d=e.value,c=null!==t?t.value:d,h=null!==r?r.value:d,p=Au(d,a).mul(o.x),g=Au(c,u).mul(o.y),m=Au(h,l).mul(o.z);return zn(p,g,m)}}const Om=vi(Vm),Gm=new me,km=new r,zm=new r,$m=new r,Hm=new n,Wm=new r(0,0,-1),jm=new s,qm=new r,Km=new r,Xm=new s,Ym=new t,Qm=new ge,Zm=Ac.flipX();Qm.depthTexture=new B(1,1);let Jm=!1;class ef extends Su{static get type(){return"ReflectorNode"}constructor(e={}){super(e.defaultTexture||Qm.texture,Zm),this._reflectorBaseNode=e.reflector||new tf(this,e),this._depthNode=null,this.setUpdateMatrix(!1)}get reflector(){return this._reflectorBaseNode}get target(){return this._reflectorBaseNode.target}getDepthNode(){if(null===this._depthNode){if(!0!==this._reflectorBaseNode.depth)throw new Error("THREE.ReflectorNode: Depth node can only be requested when the reflector is created with { depth: true }. ");this._depthNode=xi(new ef({defaultTexture:Qm.depthTexture,reflector:this._reflectorBaseNode}))}return this._depthNode}setup(e){return e.object.isQuadMesh||this._reflectorBaseNode.build(e),super.setup(e)}clone(){const e=new this.constructor(this.reflectorNode);return e._reflectorBaseNode=this._reflectorBaseNode,e}}class tf extends Us{static get type(){return"ReflectorBaseNode"}constructor(e,t={}){super();const{target:r=new fe,resolution:s=1,generateMipmaps:i=!1,bounces:n=!0,depth:o=!1}=t;this.textureNode=e,this.target=r,this.resolution=s,this.generateMipmaps=i,this.bounces=n,this.depth=o,this.updateBeforeType=n?As.RENDER:As.FRAME,this.virtualCameras=new WeakMap,this.renderTargets=new WeakMap}_updateResolution(e,t){const r=this.resolution;t.getDrawingBufferSize(Ym),e.setSize(Math.round(Ym.width*r),Math.round(Ym.height*r))}setup(e){return this._updateResolution(Qm,e.renderer),super.setup(e)}getVirtualCamera(e){let t=this.virtualCameras.get(e);return void 0===t&&(t=e.clone(),this.virtualCameras.set(e,t)),t}getRenderTarget(e){let t=this.renderTargets.get(e);return void 0===t&&(t=new ge(0,0,{type:ye}),!0===this.generateMipmaps&&(t.texture.minFilter=be,t.texture.generateMipmaps=!0),!0===this.depth&&(t.depthTexture=new B),this.renderTargets.set(e,t)),t}updateBefore(e){if(!1===this.bounces&&Jm)return!1;Jm=!0;const{scene:t,camera:r,renderer:s,material:i}=e,{target:n}=this,o=this.getVirtualCamera(r),a=this.getRenderTarget(o);if(s.getDrawingBufferSize(Ym),this._updateResolution(a,s),zm.setFromMatrixPosition(n.matrixWorld),$m.setFromMatrixPosition(r.matrixWorld),Hm.extractRotation(n.matrixWorld),km.set(0,0,1),km.applyMatrix4(Hm),qm.subVectors(zm,$m),qm.dot(km)>0)return;qm.reflect(km).negate(),qm.add(zm),Hm.extractRotation(r.matrixWorld),Wm.set(0,0,-1),Wm.applyMatrix4(Hm),Wm.add($m),Km.subVectors(zm,Wm),Km.reflect(km).negate(),Km.add(zm),o.coordinateSystem=r.coordinateSystem,o.position.copy(qm),o.up.set(0,1,0),o.up.applyMatrix4(Hm),o.up.reflect(km),o.lookAt(Km),o.near=r.near,o.far=r.far,o.updateMatrixWorld(),o.projectionMatrix.copy(r.projectionMatrix),Gm.setFromNormalAndCoplanarPoint(km,zm),Gm.applyMatrix4(o.matrixWorldInverse),jm.set(Gm.normal.x,Gm.normal.y,Gm.normal.z,Gm.constant);const u=o.projectionMatrix;Xm.x=(Math.sign(jm.x)+u.elements[8])/u.elements[0],Xm.y=(Math.sign(jm.y)+u.elements[9])/u.elements[5],Xm.z=-1,Xm.w=(1+u.elements[10])/u.elements[14],jm.multiplyScalar(1/jm.dot(Xm));u.elements[2]=jm.x,u.elements[6]=jm.y,u.elements[10]=s.coordinateSystem===l?jm.z-0:jm.z+1-0,u.elements[14]=jm.w,this.textureNode.value=a.texture,!0===this.depth&&(this.textureNode.getDepthNode().value=a.depthTexture),i.visible=!1;const d=s.getRenderTarget(),c=s.getMRT(),h=s.autoClear;s.setMRT(null),s.setRenderTarget(a),s.autoClear=!0,s.render(t,o),s.setMRT(c),s.setRenderTarget(d),s.autoClear=h,i.visible=!0,Jm=!1}}const rf=new xe(-1,1,1,-1,0,1);class sf extends Te{constructor(e=!1){super();const t=!1===e?[0,-1,0,1,2,1]:[0,2,0,0,2,0];this.setAttribute("position",new _e([-1,3,0,-1,-1,0,3,-1,0],3)),this.setAttribute("uv",new _e(t,2))}}const nf=new sf;class of extends k{constructor(e=null){super(nf,e),this.camera=rf,this.isQuadMesh=!0}async renderAsync(e){return e.renderAsync(this,rf)}render(e){e.render(this,rf)}}const af=new t;class uf extends Su{static get type(){return"RTTNode"}constructor(e,t=null,r=null,s={type:ye}){const i=new ge(t,r,s);super(i.texture,xu()),this.node=e,this.width=t,this.height=r,this.pixelRatio=1,this.renderTarget=i,this.textureNeedsUpdate=!0,this.autoUpdate=!0,this._rttNode=null,this._quadMesh=new of(new rh),this.updateBeforeType=As.RENDER}get autoSize(){return null===this.width}setup(e){return this._rttNode=this.node.context(e.getSharedContext()),this._quadMesh.material.name="RTT",this._quadMesh.material.needsUpdate=!0,super.setup(e)}setSize(e,t){this.width=e,this.height=t;const r=e*this.pixelRatio,s=t*this.pixelRatio;this.renderTarget.setSize(r,s),this.textureNeedsUpdate=!0}setPixelRatio(e){this.pixelRatio=e,this.setSize(this.width,this.height)}updateBefore({renderer:e}){if(!1===this.textureNeedsUpdate&&!1===this.autoUpdate)return;if(this.textureNeedsUpdate=!1,!0===this.autoSize){this.pixelRatio=e.getPixelRatio();const t=e.getSize(af);this.setSize(t.width,t.height)}this._quadMesh.material.fragmentNode=this._rttNode;const t=e.getRenderTarget();e.setRenderTarget(this.renderTarget),this._quadMesh.render(e),e.setRenderTarget(t)}clone(){const e=new Su(this.value,this.uvNode,this.levelNode);return e.sampler=this.sampler,e.referenceNode=this,e}}const lf=(e,...t)=>xi(new uf(xi(e),...t)),df=Si((([e,t,r],s)=>{let i;s.renderer.coordinateSystem===l?(e=Pi(e.x,e.y.oneMinus()).mul(2).sub(1),i=zi(Vi(e,t),1)):i=zi(Vi(e.x,e.y.oneMinus(),t).mul(2).sub(1),1);const n=zi(r.mul(i));return n.xyz.div(n.w)})),cf=Si((([e,t])=>{const r=t.mul(zi(e,1)),s=r.xy.div(r.w).mul(.5).add(.5).toVar();return Pi(s.x,s.y.oneMinus())})),hf=Si((([e,t,r])=>{const s=_u(Ru(t)),i=Ii(e.mul(s)).toVar(),n=Ru(t,i).toVar(),o=Ru(t,i.sub(Ii(2,0))).toVar(),a=Ru(t,i.sub(Ii(1,0))).toVar(),u=Ru(t,i.add(Ii(1,0))).toVar(),l=Ru(t,i.add(Ii(2,0))).toVar(),d=Ru(t,i.add(Ii(0,2))).toVar(),c=Ru(t,i.add(Ii(0,1))).toVar(),h=Ru(t,i.sub(Ii(0,1))).toVar(),p=Ru(t,i.sub(Ii(0,2))).toVar(),g=Do($n(Mi(2).mul(a).sub(o),n)).toVar(),m=Do($n(Mi(2).mul(u).sub(l),n)).toVar(),f=Do($n(Mi(2).mul(c).sub(d),n)).toVar(),y=Do($n(Mi(2).mul(h).sub(p),n)).toVar(),b=df(e,n,r).toVar(),x=g.lessThan(m).select(b.sub(df(e.sub(Pi(Mi(1).div(s.x),0)),a,r)),b.negate().add(df(e.add(Pi(Mi(1).div(s.x),0)),u,r))),T=f.lessThan(y).select(b.sub(df(e.add(Pi(0,Mi(1).div(s.y))),c,r)),b.negate().add(df(e.sub(Pi(0,Mi(1).div(s.y))),h,r)));return Eo(ia(x,T))}));class pf extends R{constructor(e,t,r=Float32Array){super(ArrayBuffer.isView(e)?e:new r(e*t),t),this.isStorageInstancedBufferAttribute=!0}}class gf extends ve{constructor(e,t,r=Float32Array){super(ArrayBuffer.isView(e)?e:new r(e*t),t),this.isStorageBufferAttribute=!0}}class mf extends Ps{static get type(){return"StorageArrayElementNode"}constructor(e,t){super(e,t),this.isStorageArrayElementNode=!0}set storageBufferNode(e){this.node=e}get storageBufferNode(){return this.node}setup(e){return!1===e.isAvailable("storageBuffer")&&!0===this.node.isPBO&&e.setupPBO(this.node),super.setup(e)}generate(e,t){let r;const s=e.context.assign;if(r=!1===e.isAvailable("storageBuffer")?!0!==this.node.isPBO||!0===s||!this.node.value.isInstancedBufferAttribute&&"compute"===e.shaderStage?this.node.build(e):e.generatePBO(this):super.generate(e),!0!==s){const s=this.getNodeType(e);r=e.format(r,s,t)}return r}}const ff=vi(mf);class yf extends Rl{static get type(){return"StorageBufferNode"}constructor(e,t=null,r=0){null===t&&(e.isStorageBufferAttribute||e.isStorageInstancedBufferAttribute)&&(t=ms(e.itemSize),r=e.count),super(e,t,r),this.isStorageBufferNode=!0,this.access=Cs.READ_WRITE,this.isAtomic=!1,this.isPBO=!1,this._attribute=null,this._varying=null,this.global=!0,!0!==e.isStorageBufferAttribute&&!0!==e.isStorageInstancedBufferAttribute&&(e.isInstancedBufferAttribute?e.isStorageInstancedBufferAttribute=!0:e.isStorageBufferAttribute=!0)}getHash(e){if(0===this.bufferCount){let t=e.globalCache.getData(this.value);return void 0===t&&(t={node:this},e.globalCache.setData(this.value,t)),t.node.uuid}return this.uuid}getInputType(){return this.value.isIndirectStorageBufferAttribute?"indirectStorageBuffer":"storageBuffer"}element(e){return ff(this,e)}setPBO(e){return this.isPBO=e,this}getPBO(){return this.isPBO}setAccess(e){return this.access=e,this}toReadOnly(){return this.setAccess(Cs.READ_ONLY)}setAtomic(e){return this.isAtomic=e,this}toAtomic(){return this.setAtomic(!0)}getAttributeData(){return null===this._attribute&&(this._attribute=Ja(this.value),this._varying=Pa(this._attribute)),{attribute:this._attribute,varying:this._varying}}getNodeType(e){if(e.isAvailable("storageBuffer")||e.isAvailable("indirectStorageBuffer"))return super.getNodeType(e);const{attribute:t}=this.getAttributeData();return t.getNodeType(e)}generate(e){if(e.isAvailable("storageBuffer")||e.isAvailable("indirectStorageBuffer"))return super.generate(e);const{attribute:t,varying:r}=this.getAttributeData(),s=r.build(e);return e.registerTransform(s,t),s}}const bf=(e,t=null,r=0)=>xi(new yf(e,t,r));class xf extends yu{static get type(){return"VertexColorNode"}constructor(e=0){super(null,"vec4"),this.isVertexColorNode=!0,this.index=e}getAttributeName(){const e=this.index;return"color"+(e>0?e:"")}generate(e){const t=this.getAttributeName(e);let r;return r=!0===e.hasGeometryAttribute(t)?super.generate(e):e.generateConst(this.nodeType,new s(1,1,1,1)),r}serialize(e){super.serialize(e),e.index=this.index}deserialize(e){super.deserialize(e),this.index=e.index}}class Tf extends Us{static get type(){return"PointUVNode"}constructor(){super("vec2"),this.isPointUVNode=!0}generate(){return"vec2( gl_PointCoord.x, 1.0 - gl_PointCoord.y )"}}const _f=Ni(Tf),vf=new Se,Nf=new n;class Sf extends Us{static get type(){return"SceneNode"}constructor(e=Sf.BACKGROUND_BLURRINESS,t=null){super(),this.scope=e,this.scene=t}setup(e){const t=this.scope,r=null!==this.scene?this.scene:e.scene;let s;return t===Sf.BACKGROUND_BLURRINESS?s=Ul("backgroundBlurriness","float",r):t===Sf.BACKGROUND_INTENSITY?s=Ul("backgroundIntensity","float",r):t===Sf.BACKGROUND_ROTATION?s=nn("mat4").label("backgroundRotation").setGroup(tn).onRenderUpdate((()=>{const e=r.background;return null!==e&&e.isTexture&&e.mapping!==Ne?(vf.copy(r.backgroundRotation),vf.x*=-1,vf.y*=-1,vf.z*=-1,Nf.makeRotationFromEuler(vf)):Nf.identity(),Nf})):console.error("THREE.SceneNode: Unknown scope:",t),s}}Sf.BACKGROUND_BLURRINESS="backgroundBlurriness",Sf.BACKGROUND_INTENSITY="backgroundIntensity",Sf.BACKGROUND_ROTATION="backgroundRotation";const Af=Ni(Sf,Sf.BACKGROUND_BLURRINESS),Rf=Ni(Sf,Sf.BACKGROUND_INTENSITY),Cf=Ni(Sf,Sf.BACKGROUND_ROTATION);class Ef extends Su{static get type(){return"StorageTextureNode"}constructor(e,t,r=null){super(e,t),this.storeNode=r,this.isStorageTextureNode=!0,this.access=Cs.WRITE_ONLY}getInputType(){return"storageTexture"}setup(e){super.setup(e);e.getNodeProperties(this).storeNode=this.storeNode}setAccess(e){return this.access=e,this}generate(e,t){let r;return r=null!==this.storeNode?this.generateStore(e):super.generate(e,t),r}toReadWrite(){return this.setAccess(Cs.READ_WRITE)}toReadOnly(){return this.setAccess(Cs.READ_ONLY)}toWriteOnly(){return this.setAccess(Cs.WRITE_ONLY)}generateStore(e){const t=e.getNodeProperties(this),{uvNode:r,storeNode:s}=t,i=super.generate(e,"property"),n=r.build(e,"uvec2"),o=s.build(e,"vec4"),a=e.generateTextureStore(e,i,n,o);e.addLineFlowCode(a,this)}}const wf=vi(Ef);class Mf extends Fl{static get type(){return"UserDataNode"}constructor(e,t,r=null){super(e,t,r),this.userData=r}updateReference(e){return this.reference=null!==this.userData?this.userData:e.object.userData,this.reference}}const Bf=new WeakMap;class Ff extends Ds{static get type(){return"VelocityNode"}constructor(){super("vec2"),this.projectionMatrix=null,this.updateType=As.OBJECT,this.updateAfterType=As.OBJECT,this.previousModelWorldMatrix=nn(new n),this.previousProjectionMatrix=nn(new n).setGroup(tn),this.previousCameraViewMatrix=nn(new n)}setProjectionMatrix(e){this.projectionMatrix=e}update({frameId:e,camera:t,object:r}){const s=Pf(r);this.previousModelWorldMatrix.value.copy(s);const i=Uf(t);i.frameId!==e&&(i.frameId=e,void 0===i.previousProjectionMatrix?(i.previousProjectionMatrix=new n,i.previousCameraViewMatrix=new n,i.currentProjectionMatrix=new n,i.currentCameraViewMatrix=new n,i.previousProjectionMatrix.copy(this.projectionMatrix||t.projectionMatrix),i.previousCameraViewMatrix.copy(t.matrixWorldInverse)):(i.previousProjectionMatrix.copy(i.currentProjectionMatrix),i.previousCameraViewMatrix.copy(i.currentCameraViewMatrix)),i.currentProjectionMatrix.copy(this.projectionMatrix||t.projectionMatrix),i.currentCameraViewMatrix.copy(t.matrixWorldInverse),this.previousProjectionMatrix.value.copy(i.previousProjectionMatrix),this.previousCameraViewMatrix.value.copy(i.previousCameraViewMatrix))}updateAfter({object:e}){Pf(e).copy(e.matrixWorld)}setup(){const e=null===this.projectionMatrix?wu:nn(this.projectionMatrix),t=this.previousCameraViewMatrix.mul(this.previousModelWorldMatrix),r=e.mul(Xu).mul(el),s=this.previousProjectionMatrix.mul(t).mul(tl),i=r.xy.div(r.w),n=s.xy.div(s.w);return $n(i,n)}}function Uf(e){let t=Bf.get(e);return void 0===t&&(t={},Bf.set(e,t)),t}function Pf(e,t=0){const r=Uf(e);let s=r[t];return void 0===s&&(r[t]=s=new n),s}const If=Ni(Ff),Df=Si((([e,t])=>Yo(1,e.oneMinus().div(t)).oneMinus())).setLayout({name:"blendBurn",type:"vec3",inputs:[{name:"base",type:"vec3"},{name:"blend",type:"vec3"}]}),Lf=Si((([e,t])=>Yo(e.div(t.oneMinus()),1))).setLayout({name:"blendDodge",type:"vec3",inputs:[{name:"base",type:"vec3"},{name:"blend",type:"vec3"}]}),Vf=Si((([e,t])=>e.oneMinus().mul(t.oneMinus()).oneMinus())).setLayout({name:"blendScreen",type:"vec3",inputs:[{name:"base",type:"vec3"},{name:"blend",type:"vec3"}]}),Of=Si((([e,t])=>ha(e.mul(2).mul(t),e.oneMinus().mul(2).mul(t.oneMinus()).oneMinus(),Jo(.5,e)))).setLayout({name:"blendOverlay",type:"vec3",inputs:[{name:"base",type:"vec3"},{name:"blend",type:"vec3"}]}),Gf=Si((([e,t])=>{const r=t.a.add(e.a.mul(t.a.oneMinus()));return zi(t.rgb.mul(t.a).add(e.rgb.mul(e.a).mul(t.a.oneMinus())).div(r),r)})).setLayout({name:"blendColor",type:"vec4",inputs:[{name:"base",type:"vec4"},{name:"blend",type:"vec4"}]}),kf=Si((([e])=>Wf(e.rgb))),zf=Si((([e,t=Mi(1)])=>t.mix(Wf(e.rgb),e.rgb))),$f=Si((([e,t=Mi(1)])=>{const r=zn(e.r,e.g,e.b).div(3),s=e.r.max(e.g.max(e.b)),i=s.sub(r).mul(t).mul(-3);return ha(e.rgb,s,i)})),Hf=Si((([e,t=Mi(1)])=>{const r=Vi(.57735,.57735,.57735),s=t.cos();return Vi(e.rgb.mul(s).add(r.cross(e.rgb).mul(t.sin()).add(r.mul(sa(r,e.rgb).mul(s.oneMinus())))))})),Wf=(e,t=Vi(d.getLuminanceCoefficients(new r)))=>sa(e,t),jf=Si((([e,t=Vi(1),s=Vi(0),i=Vi(1),n=Mi(1),o=Vi(d.getLuminanceCoefficients(new r,Ae))])=>{const a=e.rgb.dot(Vi(o)),u=Qo(e.rgb.mul(t).add(s),0).toVar(),l=u.pow(i).toVar();return Ci(u.r.greaterThan(0),(()=>{u.r.assign(l.r)})),Ci(u.g.greaterThan(0),(()=>{u.g.assign(l.g)})),Ci(u.b.greaterThan(0),(()=>{u.b.assign(l.b)})),u.assign(a.add(u.sub(a).mul(n))),zi(u.rgb,e.a)}));class qf extends Ds{static get type(){return"PosterizeNode"}constructor(e,t){super(),this.sourceNode=e,this.stepsNode=t}setup(){const{sourceNode:e,stepsNode:t}=this;return e.mul(t).floor().div(t)}}const Kf=vi(qf),Xf=new t;class Yf extends Su{static get type(){return"PassTextureNode"}constructor(e,t){super(t),this.passNode=e,this.setUpdateMatrix(!1)}setup(e){return e.object.isQuadMesh&&this.passNode.build(e),super.setup(e)}clone(){return new this.constructor(this.passNode,this.value)}}class Qf extends Yf{static get type(){return"PassMultipleTextureNode"}constructor(e,t,r=!1){super(e,null),this.textureName=t,this.previousTexture=r}updateTexture(){this.value=this.previousTexture?this.passNode.getPreviousTexture(this.textureName):this.passNode.getTexture(this.textureName)}setup(e){return this.updateTexture(),super.setup(e)}clone(){return new this.constructor(this.passNode,this.textureName,this.previousTexture)}}class Zf extends Ds{static get type(){return"PassNode"}constructor(e,t,r,s={}){super("vec4"),this.scope=e,this.scene=t,this.camera=r,this.options=s,this._pixelRatio=1,this._width=1,this._height=1;const i=new B;i.isRenderTargetTexture=!0,i.name="depth";const n=new ge(this._width*this._pixelRatio,this._height*this._pixelRatio,{type:ye,...s});n.texture.name="output",n.depthTexture=i,this.renderTarget=n,this._textures={output:n.texture,depth:i},this._textureNodes={},this._linearDepthNodes={},this._viewZNodes={},this._previousTextures={},this._previousTextureNodes={},this._cameraNear=nn(0),this._cameraFar=nn(0),this._mrt=null,this.isPassNode=!0,this.updateBeforeType=As.FRAME}setMRT(e){return this._mrt=e,this}getMRT(){return this._mrt}isGlobal(){return!0}getTexture(e){let t=this._textures[e];if(void 0===t){t=this.renderTarget.texture.clone(),t.name=e,this._textures[e]=t,this.renderTarget.textures.push(t)}return t}getPreviousTexture(e){let t=this._previousTextures[e];return void 0===t&&(t=this.getTexture(e).clone(),this._previousTextures[e]=t),t}toggleTexture(e){const t=this._previousTextures[e];if(void 0!==t){const r=this._textures[e],s=this.renderTarget.textures.indexOf(r);this.renderTarget.textures[s]=t,this._textures[e]=t,this._previousTextures[e]=r,this._textureNodes[e].updateTexture(),this._previousTextureNodes[e].updateTexture()}}getTextureNode(e="output"){let t=this._textureNodes[e];return void 0===t&&(t=xi(new Qf(this,e)),t.updateTexture(),this._textureNodes[e]=t),t}getPreviousTextureNode(e="output"){let t=this._previousTextureNodes[e];return void 0===t&&(void 0===this._textureNodes[e]&&this.getTextureNode(e),t=xi(new Qf(this,e,!0)),t.updateTexture(),this._previousTextureNodes[e]=t),t}getViewZNode(e="depth"){let t=this._viewZNodes[e];if(void 0===t){const r=this._cameraNear,s=this._cameraFar;this._viewZNodes[e]=t=Wc(this.getTextureNode(e),r,s)}return t}getLinearDepthNode(e="depth"){let t=this._linearDepthNodes[e];if(void 0===t){const r=this._cameraNear,s=this._cameraFar,i=this.getViewZNode(e);this._linearDepthNodes[e]=t=$c(i,r,s)}return t}setup({renderer:e}){return this.renderTarget.samples=void 0===this.options.samples?e.samples:this.options.samples,!0===e.backend.isWebGLBackend&&(this.renderTarget.samples=0),this.scope===Zf.COLOR?this.getTextureNode():this.getLinearDepthNode()}updateBefore(e){const{renderer:t}=e,{scene:r,camera:s}=this;this._pixelRatio=t.getPixelRatio();const i=t.getSize(Xf);this.setSize(i.width,i.height);const n=t.getRenderTarget(),o=t.getMRT();this._cameraNear.value=s.near,this._cameraFar.value=s.far;for(const e in this._previousTextures)this.toggleTexture(e);t.setRenderTarget(this.renderTarget),t.setMRT(this._mrt),t.render(r,s),t.setRenderTarget(n),t.setMRT(o)}setSize(e,t){this._width=e,this._height=t;const r=this._width*this._pixelRatio,s=this._height*this._pixelRatio;this.renderTarget.setSize(r,s)}setPixelRatio(e){this._pixelRatio=e,this.setSize(this._width,this._height)}dispose(){this.renderTarget.dispose()}}Zf.COLOR="color",Zf.DEPTH="depth";class Jf extends Zf{static get type(){return"ToonOutlinePassNode"}constructor(e,t,r,s,i){super(Zf.COLOR,e,t),this.colorNode=r,this.thicknessNode=s,this.alphaNode=i,this._materialCache=new WeakMap}updateBefore(e){const{renderer:t}=e,r=t.getRenderObjectFunction();t.setRenderObjectFunction(((e,r,s,i,n,o,a,u)=>{if((n.isMeshToonMaterial||n.isMeshToonNodeMaterial)&&!1===n.wireframe){const l=this._getOutlineMaterial(n);t.renderObject(e,r,s,i,l,o,a,u)}t.renderObject(e,r,s,i,n,o,a,u)})),super.updateBefore(e),t.setRenderObjectFunction(r)}_createMaterial(){const e=new rh;e.isMeshToonOutlineMaterial=!0,e.name="Toon_Outline",e.side=T;const t=dl.negate(),r=wu.mul(Xu),s=Mi(1),i=r.mul(zi(el,1)),n=r.mul(zi(el.add(t),1)),o=Eo(i.sub(n));return e.vertexNode=i.add(o.mul(this.thicknessNode).mul(i.w).mul(s)),e.colorNode=zi(this.colorNode,this.alphaNode),e}_getOutlineMaterial(e){let t=this._materialCache.get(e);return void 0===t&&(t=this._createMaterial(),this._materialCache.set(e,t)),t}}const ey=Si((([e,t])=>e.mul(t).clamp())).setLayout({name:"linearToneMapping",type:"vec3",inputs:[{name:"color",type:"vec3"},{name:"exposure",type:"float"}]}),ty=Si((([e,t])=>(e=e.mul(t)).div(e.add(1)).clamp())).setLayout({name:"reinhardToneMapping",type:"vec3",inputs:[{name:"color",type:"vec3"},{name:"exposure",type:"float"}]}),ry=Si((([e,t])=>{const r=(e=(e=e.mul(t)).sub(.004).max(0)).mul(e.mul(6.2).add(.5)),s=e.mul(e.mul(6.2).add(1.7)).add(.06);return r.div(s).pow(2.2)})).setLayout({name:"cineonToneMapping",type:"vec3",inputs:[{name:"color",type:"vec3"},{name:"exposure",type:"float"}]}),sy=Si((([e])=>{const t=e.mul(e.add(.0245786)).sub(90537e-9),r=e.mul(e.add(.432951).mul(.983729)).add(.238081);return t.div(r)})),iy=Si((([e,t])=>{const r=qi(.59719,.35458,.04823,.076,.90834,.01566,.0284,.13383,.83777),s=qi(1.60475,-.53108,-.07367,-.10208,1.10813,-.00605,-.00327,-.07276,1.07602);return e=e.mul(t).div(.6),e=r.mul(e),e=sy(e),(e=s.mul(e)).clamp()})).setLayout({name:"acesFilmicToneMapping",type:"vec3",inputs:[{name:"color",type:"vec3"},{name:"exposure",type:"float"}]}),ny=qi(Vi(1.6605,-.1246,-.0182),Vi(-.5876,1.1329,-.1006),Vi(-.0728,-.0083,1.1187)),oy=qi(Vi(.6274,.0691,.0164),Vi(.3293,.9195,.088),Vi(.0433,.0113,.8956)),ay=Si((([e])=>{const t=Vi(e).toVar(),r=Vi(t.mul(t)).toVar(),s=Vi(r.mul(r)).toVar();return Mi(15.5).mul(s.mul(r)).sub(Hn(40.14,s.mul(t))).add(Hn(31.96,s).sub(Hn(6.868,r.mul(t))).add(Hn(.4298,r).add(Hn(.1191,t).sub(.00232))))})),uy=Si((([e,t])=>{const r=Vi(e).toVar(),s=qi(Vi(.856627153315983,.137318972929847,.11189821299995),Vi(.0951212405381588,.761241990602591,.0767994186031903),Vi(.0482516061458583,.101439036467562,.811302368396859)),i=qi(Vi(1.1271005818144368,-.1413297634984383,-.14132976349843826),Vi(-.11060664309660323,1.157823702216272,-.11060664309660294),Vi(-.016493938717834573,-.016493938717834257,1.2519364065950405)),n=Mi(-12.47393),o=Mi(4.026069);return r.mulAssign(t),r.assign(oy.mul(r)),r.assign(s.mul(r)),r.assign(Qo(r,1e-10)),r.assign(No(r)),r.assign(r.sub(n).div(o.sub(n))),r.assign(pa(r,0,1)),r.assign(ay(r)),r.assign(i.mul(r)),r.assign(na(Qo(Vi(0),r),Vi(2.2))),r.assign(ny.mul(r)),r.assign(pa(r,0,1)),r})).setLayout({name:"agxToneMapping",type:"vec3",inputs:[{name:"color",type:"vec3"},{name:"exposure",type:"float"}]}),ly=Si((([e,t])=>{const r=Mi(.76),s=Mi(.15);e=e.mul(t);const i=Yo(e.r,Yo(e.g,e.b)),n=Aa(i.lessThan(.08),i.sub(Hn(6.25,i.mul(i))),.04);e.subAssign(n);const o=Qo(e.r,Qo(e.g,e.b));Ci(o.lessThan(r),(()=>e));const a=$n(1,r),u=$n(1,a.mul(a).div(o.add(a.sub(r))));e.mulAssign(u.div(o));const l=$n(1,Wn(1,s.mul(o.sub(u)).add(1)));return ha(e,Vi(u),l)})).setLayout({name:"neutralToneMapping",type:"vec3",inputs:[{name:"color",type:"vec3"},{name:"exposure",type:"float"}]});class dy extends Us{static get type(){return"CodeNode"}constructor(e="",t=[],r=""){super("code"),this.isCodeNode=!0,this.code=e,this.includes=t,this.language=r}isGlobal(){return!0}setIncludes(e){return this.includes=e,this}getIncludes(){return this.includes}generate(e){const t=this.getIncludes(e);for(const r of t)r.build(e);const r=e.getCodeFromNode(this,this.getNodeType(e));return r.code=this.code,r.code}serialize(e){super.serialize(e),e.code=this.code,e.language=this.language}deserialize(e){super.deserialize(e),this.code=e.code,this.language=e.language}}const cy=vi(dy);class hy extends dy{static get type(){return"FunctionNode"}constructor(e="",t=[],r=""){super(e,t,r)}getNodeType(e){return this.getNodeFunction(e).type}getInputs(e){return this.getNodeFunction(e).inputs}getNodeFunction(e){const t=e.getDataFromNode(this);let r=t.nodeFunction;return void 0===r&&(r=e.parser.parseFunction(this.code),t.nodeFunction=r),r}generate(e,t){super.generate(e);const r=this.getNodeFunction(e),s=r.name,i=r.type,n=e.getCodeFromNode(this,i);""!==s&&(n.name=s);const o=e.getPropertyName(n),a=this.getNodeFunction(e).getCode(o);return n.code=a+"\n","property"===t?o:e.format(`${o}()`,i,t)}}const py=(e,t=[],r="")=>{for(let e=0;e<t.length;e++){const r=t[e];"function"==typeof r&&(t[e]=r.functionNode)}const s=xi(new hy(e,t,r)),i=(...e)=>s.call(...e);return i.functionNode=s,i};class gy extends Us{static get type(){return"ScriptableValueNode"}constructor(e=null){super(),this._value=e,this._cache=null,this.inputType=null,this.outputType=null,this.events=new o,this.isScriptableValueNode=!0}get isScriptableOutputNode(){return null!==this.outputType}set value(e){this._value!==e&&(this._cache&&"URL"===this.inputType&&this.value.value instanceof ArrayBuffer&&(URL.revokeObjectURL(this._cache),this._cache=null),this._value=e,this.events.dispatchEvent({type:"change"}),this.refresh())}get value(){return this._value}refresh(){this.events.dispatchEvent({type:"refresh"})}getValue(){const e=this.value;if(e&&null===this._cache&&"URL"===this.inputType&&e.value instanceof ArrayBuffer)this._cache=URL.createObjectURL(new Blob([e.value]));else if(e&&null!==e.value&&void 0!==e.value&&(("URL"===this.inputType||"String"===this.inputType)&&"string"==typeof e.value||"Number"===this.inputType&&"number"==typeof e.value||"Vector2"===this.inputType&&e.value.isVector2||"Vector3"===this.inputType&&e.value.isVector3||"Vector4"===this.inputType&&e.value.isVector4||"Color"===this.inputType&&e.value.isColor||"Matrix3"===this.inputType&&e.value.isMatrix3||"Matrix4"===this.inputType&&e.value.isMatrix4))return e.value;return this._cache||e}getNodeType(e){return this.value&&this.value.isNode?this.value.getNodeType(e):"float"}setup(){return this.value&&this.value.isNode?this.value:Mi()}serialize(e){super.serialize(e),null!==this.value?"ArrayBuffer"===this.inputType?e.value=_s(this.value):e.value=this.value?this.value.toJSON(e.meta).uuid:null:e.value=null,e.inputType=this.inputType,e.outputType=this.outputType}deserialize(e){super.deserialize(e);let t=null;null!==e.value&&(t="ArrayBuffer"===e.inputType?vs(e.value):"Texture"===e.inputType?e.meta.textures[e.value]:e.meta.nodes[e.value]||null),this.value=t,this.inputType=e.inputType,this.outputType=e.outputType}}const my=vi(gy);class fy extends Map{get(e,t=null,...r){if(this.has(e))return super.get(e);if(null!==t){const s=t(...r);return this.set(e,s),s}}}class yy{constructor(e){this.scriptableNode=e}get parameters(){return this.scriptableNode.parameters}get layout(){return this.scriptableNode.getLayout()}getInputLayout(e){return this.scriptableNode.getInputLayout(e)}get(e){const t=this.parameters[e];return t?t.getValue():null}}const by=new fy;class xy extends Us{static get type(){return"ScriptableNode"}constructor(e=null,t={}){super(),this.codeNode=e,this.parameters=t,this._local=new fy,this._output=my(),this._outputs={},this._source=this.source,this._method=null,this._object=null,this._value=null,this._needsOutputUpdate=!0,this.onRefresh=this.onRefresh.bind(this),this.isScriptableNode=!0}get source(){return this.codeNode?this.codeNode.code:""}setLocal(e,t){return this._local.set(e,t)}getLocal(e){return this._local.get(e)}onRefresh(){this._refresh()}getInputLayout(e){for(const t of this.getLayout())if(t.inputType&&(t.id===e||t.name===e))return t}getOutputLayout(e){for(const t of this.getLayout())if(t.outputType&&(t.id===e||t.name===e))return t}setOutput(e,t){const r=this._outputs;return void 0===r[e]?r[e]=my(t):r[e].value=t,this}getOutput(e){return this._outputs[e]}getParameter(e){return this.parameters[e]}setParameter(e,t){const r=this.parameters;return t&&t.isScriptableNode?(this.deleteParameter(e),r[e]=t,r[e].getDefaultOutput().events.addEventListener("refresh",this.onRefresh)):t&&t.isScriptableValueNode?(this.deleteParameter(e),r[e]=t,r[e].events.addEventListener("refresh",this.onRefresh)):void 0===r[e]?(r[e]=my(t),r[e].events.addEventListener("refresh",this.onRefresh)):r[e].value=t,this}getValue(){return this.getDefaultOutput().getValue()}deleteParameter(e){let t=this.parameters[e];return t&&(t.isScriptableNode&&(t=t.getDefaultOutput()),t.events.removeEventListener("refresh",this.onRefresh)),this}clearParameters(){for(const e of Object.keys(this.parameters))this.deleteParameter(e);return this.needsUpdate=!0,this}call(e,...t){const r=this.getObject()[e];if("function"==typeof r)return r(...t)}async callAsync(e,...t){const r=this.getObject()[e];if("function"==typeof r)return"AsyncFunction"===r.constructor.name?await r(...t):r(...t)}getNodeType(e){return this.getDefaultOutputNode().getNodeType(e)}refresh(e=null){null!==e?this.getOutput(e).refresh():this._refresh()}getObject(){if(this.needsUpdate&&this.dispose(),null!==this._object)return this._object;const e=new yy(this),t=by.get("THREE"),r=by.get("TSL"),s=this.getMethod(),i=[e,this._local,by,()=>this.refresh(),(e,t)=>this.setOutput(e,t),t,r];this._object=s(...i);const n=this._object.layout;if(n&&(!1===n.cache&&this._local.clear(),this._output.outputType=n.outputType||null,Array.isArray(n.elements)))for(const e of n.elements){const t=e.id||e.name;e.inputType&&(void 0===this.getParameter(t)&&this.setParameter(t,null),this.getParameter(t).inputType=e.inputType),e.outputType&&(void 0===this.getOutput(t)&&this.setOutput(t,null),this.getOutput(t).outputType=e.outputType)}return this._object}deserialize(e){super.deserialize(e);for(const e in this.parameters){let t=this.parameters[e];t.isScriptableNode&&(t=t.getDefaultOutput()),t.events.addEventListener("refresh",this.onRefresh)}}getLayout(){return this.getObject().layout}getDefaultOutputNode(){const e=this.getDefaultOutput().value;return e&&e.isNode?e:Mi()}getDefaultOutput(){return this._exec()._output}getMethod(){if(this.needsUpdate&&this.dispose(),null!==this._method)return this._method;const e=["layout","init","main","dispose"].join(", "),t="\nreturn { ...output, "+e+" };",r="var "+e+"; var output = {};\n"+this.codeNode.code+t;return this._method=new Function(...["parameters","local","global","refresh","setOutput","THREE","TSL"],r),this._method}dispose(){null!==this._method&&(this._object&&"function"==typeof this._object.dispose&&this._object.dispose(),this._method=null,this._object=null,this._source=null,this._value=null,this._needsOutputUpdate=!0,this._output.value=null,this._outputs={})}setup(){return this.getDefaultOutputNode()}getCacheKey(e){const t=[us(this.source),this.getDefaultOutputNode().getCacheKey(e)];for(const r in this.parameters)t.push(this.parameters[r].getCacheKey(e));return ls(t)}set needsUpdate(e){!0===e&&this.dispose()}get needsUpdate(){return this.source!==this._source}_exec(){return null===this.codeNode||(!0===this._needsOutputUpdate&&(this._value=this.call("main"),this._needsOutputUpdate=!1),this._output.value=this._value),this}_refresh(){this.needsUpdate=!0,this._exec(),this._output.refresh()}}const Ty=vi(xy);function _y(e){let t;const r=e.context.getViewZ;return void 0!==r&&(t=r(this)),(t||il.z).negate()}const vy=Si((([e,t],r)=>{const s=_y(r);return fa(e,t,s)})),Ny=Si((([e],t)=>{const r=_y(t);return e.mul(e,r,r).negate().exp().oneMinus()})),Sy=Si((([e,t])=>zi(t.toFloat().mix(Cn.rgb,e.toVec3()),Cn.a)));let Ay=null,Ry=null;class Cy extends Us{static get type(){return"RangeNode"}constructor(e=Mi(),t=Mi()){super(),this.minNode=e,this.maxNode=t}getVectorLength(e){const t=e.getTypeLength(bs(this.minNode.value)),r=e.getTypeLength(bs(this.maxNode.value));return t>r?t:r}getNodeType(e){return e.object.count>1?e.getTypeFromLength(this.getVectorLength(e)):"float"}setup(e){const t=e.object;let r=null;if(t.count>1){const i=this.minNode.value,n=this.maxNode.value,o=e.getTypeLength(bs(i)),u=e.getTypeLength(bs(n));Ay=Ay||new s,Ry=Ry||new s,Ay.setScalar(0),Ry.setScalar(0),1===o?Ay.setScalar(i):i.isColor?Ay.set(i.r,i.g,i.b,1):Ay.set(i.x,i.y,i.z||0,i.w||0),1===u?Ry.setScalar(n):n.isColor?Ry.set(n.r,n.g,n.b,1):Ry.set(n.x,n.y,n.z||0,n.w||0);const l=4,d=l*t.count,c=new Float32Array(d);for(let e=0;e<d;e++){const t=e%l,r=Ay.getComponent(t),s=Ry.getComponent(t);c[e]=a.lerp(r,s,Math.random())}const h=this.getNodeType(e);if(t.count<=4096)r=Cl(c,"vec4",t.count).element(Xd).convert(h);else{const t=new R(c,4);e.geometry.setAttribute("__range"+this.id,t),r=tu(t).convert(h)}}else r=Mi(0);return r}}const Ey=vi(Cy);class wy extends Us{static get type(){return"ComputeBuiltinNode"}constructor(e,t){super(t),this._builtinName=e}getHash(e){return this.getBuiltinName(e)}getNodeType(){return this.nodeType}setBuiltinName(e){return this._builtinName=e,this}getBuiltinName(){return this._builtinName}hasBuiltin(e){e.hasBuiltin(this._builtinName)}generate(e,t){const r=this.getBuiltinName(e),s=this.getNodeType(e);return"compute"===e.shaderStage?e.format(r,s,t):(console.warn(`ComputeBuiltinNode: Compute built-in value ${r} can not be accessed in the ${e.shaderStage} stage`),e.generateConst(s))}serialize(e){super.serialize(e),e.global=this.global,e._builtinName=this._builtinName}deserialize(e){super.deserialize(e),this.global=e.global,this._builtinName=e._builtinName}}const My=(e,t)=>xi(new wy(e,t)),By=My("numWorkgroups","uvec3"),Fy=My("workgroupId","uvec3"),Uy=My("localId","uvec3"),Py=My("subgroupSize","uint");const Iy=vi(class extends Us{constructor(e){super(),this.scope=e}generate(e){const{scope:t}=this,{renderer:r}=e;!0===r.backend.isWebGLBackend?e.addFlowCode(`\t// ${t}Barrier \n`):e.addLineFlowCode(`${t}Barrier()`,this)}});class Dy extends Ps{constructor(e,t){super(e,t),this.isWorkgroupInfoElementNode=!0}generate(e,t){let r;const s=e.context.assign;if(r=super.generate(e),!0!==s){const s=this.getNodeType(e);r=e.format(r,s,t)}return r}}class Ly extends Us{constructor(e,t,r=0){super(t),this.bufferType=t,this.bufferCount=r,this.isWorkgroupInfoNode=!0,this.elementType=t,this.scope=e}label(e){return this.name=e,this}setScope(e){return this.scope=e,this}getElementType(){return this.elementType}getInputType(){return`${this.scope}Array`}element(e){return xi(new Dy(this,e))}generate(e){return e.getScopedArray(this.name||`${this.scope}Array_${this.id}`,this.scope.toLowerCase(),this.bufferType,this.bufferCount)}}class Vy extends Ds{static get type(){return"AtomicFunctionNode"}constructor(e,t,r,s=null){super("uint"),this.method=e,this.pointerNode=t,this.valueNode=r,this.storeNode=s}getInputType(e){return this.pointerNode.getNodeType(e)}getNodeType(e){return this.getInputType(e)}generate(e){const t=this.method,r=this.getNodeType(e),s=this.getInputType(e),i=this.pointerNode,n=this.valueNode,o=[];o.push(`&${i.build(e,s)}`),o.push(n.build(e,s));const a=`${e.getMethod(t,r)}( ${o.join(", ")} )`;if(null!==this.storeNode){const t=this.storeNode.build(e,s);e.addLineFlowCode(`${t} = ${a}`,this)}else e.addLineFlowCode(a,this)}}Vy.ATOMIC_LOAD="atomicLoad",Vy.ATOMIC_STORE="atomicStore",Vy.ATOMIC_ADD="atomicAdd",Vy.ATOMIC_SUB="atomicSub",Vy.ATOMIC_MAX="atomicMax",Vy.ATOMIC_MIN="atomicMin",Vy.ATOMIC_AND="atomicAnd",Vy.ATOMIC_OR="atomicOr",Vy.ATOMIC_XOR="atomicXor";const Oy=vi(Vy),Gy=(e,t,r,s=null)=>{const i=Oy(e,t,r,s);return i.append(),i};let ky;function zy(e){ky=ky||new WeakMap;let t=ky.get(e);return void 0===t&&ky.set(e,t={}),t}function $y(e){const t=zy(e);return t.shadowMatrix||(t.shadowMatrix=nn("mat4").setGroup(tn).onRenderUpdate((()=>(!0!==e.castShadow&&e.shadow.updateMatrices(e),e.shadow.matrix))))}function Hy(e){const t=zy(e);if(void 0===t.projectionUV){const r=$y(e).mul(rl);t.projectionUV=r.xyz.div(r.w)}return t.projectionUV}function Wy(e){const t=zy(e);return t.position||(t.position=nn(new r).setGroup(tn).onRenderUpdate(((t,r)=>r.value.setFromMatrixPosition(e.matrixWorld))))}function jy(e){const t=zy(e);return t.targetPosition||(t.targetPosition=nn(new r).setGroup(tn).onRenderUpdate(((t,r)=>r.value.setFromMatrixPosition(e.target.matrixWorld))))}function qy(e){const t=zy(e);return t.viewPosition||(t.viewPosition=nn(new r).setGroup(tn).onRenderUpdate((({camera:t},s)=>{s.value=s.value||new r,s.value.setFromMatrixPosition(e.matrixWorld),s.value.applyMatrix4(t.matrixWorldInverse)})))}const Ky=e=>Bu.transformDirection(Wy(e).sub(jy(e))),Xy=(e,t)=>{for(const r of t)if(r.isAnalyticLightNode&&r.light.id===e)return r;return null},Yy=new WeakMap;class Qy extends Us{static get type(){return"LightsNode"}constructor(){super("vec3"),this.totalDiffuseNode=Vi().toVar("totalDiffuse"),this.totalSpecularNode=Vi().toVar("totalSpecular"),this.outgoingLightNode=Vi().toVar("outgoingLight"),this._lights=[],this._lightNodes=null,this._lightNodesHash=null,this.global=!0}customCacheKey(){const e=[],t=this._lights;for(let r=0;r<t.length;r++)e.push(t[r].id);return ls(e)}getHash(e){if(null===this._lightNodesHash){null===this._lightNodes&&this.setupLightsNode(e);const t=[];for(const e of this._lightNodes)t.push(e.getSelf().getHash());this._lightNodesHash="lights-"+t.join(",")}return this._lightNodesHash}analyze(e){const t=e.getDataFromNode(this);for(const r of t.nodes)r.build(e)}setupLightsNode(e){const t=[],r=this._lightNodes,s=(e=>e.sort(((e,t)=>e.id-t.id)))(this._lights),i=e.renderer.library;for(const e of s)if(e.isNode)t.push(xi(e));else{let s=null;if(null!==r&&(s=Xy(e.id,r)),null===s){const r=i.getLightNodeClass(e.constructor);if(null===r){console.warn(`LightsNode.setupNodeLights: Light node not found for ${e.constructor.name}`);continue}let s=null;Yy.has(e)?s=Yy.get(e):(s=xi(new r(e)),Yy.set(e,s)),t.push(s)}}this._lightNodes=t}setupLights(e,t){for(const r of t)r.build(e)}setup(e){null===this._lightNodes&&this.setupLightsNode(e);const t=e.context,r=t.lightingModel;let s=this.outgoingLightNode;if(r){const{_lightNodes:i,totalDiffuseNode:n,totalSpecularNode:o}=this;t.outgoingLight=s;const a=e.addStack();e.getDataFromNode(this).nodes=a.nodes,r.start(t,a,e),this.setupLights(e,i),r.indirect(t,a,e);const{backdrop:u,backdropAlpha:l}=t,{directDiffuse:d,directSpecular:c,indirectDiffuse:h,indirectSpecular:p}=t.reflectedLight;let g=d.add(h);null!==u&&(g=Vi(null!==l?l.mix(g,u):u),t.material.transparent=!0),n.assign(g),o.assign(c.add(p)),s.assign(n.add(o)),r.finish(t,a,e),s=s.bypass(e.removeStack())}return s}setLights(e){return this._lights=e,this._lightNodes=null,this._lightNodesHash=null,this}getLights(){return this._lights}get hasLights(){return this._lights.length>0}}class Zy extends Us{static get type(){return"ShadowBaseNode"}constructor(e){super(),this.light=e,this.updateBeforeType=As.RENDER,this.isShadowBaseNode=!0}setupShadowPosition({material:e}){Jy.assign(e.shadowPositionNode||rl)}dispose(){this.updateBeforeType=As.NONE}}const Jy=Vi().toVar("shadowPositionWorld");function eb(t,r={}){return r.toneMapping=t.toneMapping,r.toneMappingExposure=t.toneMappingExposure,r.outputColorSpace=t.outputColorSpace,r.renderTarget=t.getRenderTarget(),r.activeCubeFace=t.getActiveCubeFace(),r.activeMipmapLevel=t.getActiveMipmapLevel(),r.renderObjectFunction=t.getRenderObjectFunction(),r.pixelRatio=t.getPixelRatio(),r.mrt=t.getMRT(),r.clearColor=t.getClearColor(r.clearColor||new e),r.clearAlpha=t.getClearAlpha(),r.autoClear=t.autoClear,r.scissorTest=t.getScissorTest(),r}function tb(e,t){return t=eb(e,t),e.setMRT(null),e.setRenderObjectFunction(null),e.setClearColor(0,1),e.autoClear=!0,t}function rb(e,t){e.toneMapping=t.toneMapping,e.toneMappingExposure=t.toneMappingExposure,e.outputColorSpace=t.outputColorSpace,e.setRenderTarget(t.renderTarget,t.activeCubeFace,t.activeMipmapLevel),e.setRenderObjectFunction(t.renderObjectFunction),e.setPixelRatio(t.pixelRatio),e.setMRT(t.mrt),e.setClearColor(t.clearColor,t.clearAlpha),e.autoClear=t.autoClear,e.setScissorTest(t.scissorTest)}function sb(e,t={}){return t.background=e.background,t.backgroundNode=e.backgroundNode,t.overrideMaterial=e.overrideMaterial,t}function ib(e,t){return t=sb(e,t),e.background=null,e.backgroundNode=null,e.overrideMaterial=null,t}function nb(e,t){e.background=t.background,e.backgroundNode=t.backgroundNode,e.overrideMaterial=t.overrideMaterial}function ob(e,t,r){return r=ib(t,r=tb(e,r))}function ab(e,t,r){rb(e,r),nb(t,r)}var ub=Object.freeze({__proto__:null,resetRendererAndSceneState:ob,resetRendererState:tb,resetSceneState:ib,restoreRendererAndSceneState:ab,restoreRendererState:rb,restoreSceneState:nb,saveRendererAndSceneState:function(e,t,r={}){return r=sb(t,r=eb(e,r))},saveRendererState:eb,saveSceneState:sb});const lb=new WeakMap,db=Si((([e,t,r])=>{let s=rl.sub(e).length();return s=s.sub(t).div(r.sub(t)),s=s.saturate(),s})),cb=e=>{let t=lb.get(e);if(void 0===t){const r=e.isPointLight?(e=>{const t=e.shadow.camera,r=Ul("near","float",t).setGroup(tn),s=Ul("far","float",t).setGroup(tn),i=Vu(e);return db(i,r,s)})(e):null;t=new rh,t.colorNode=zi(0,0,0,1),t.depthNode=r,t.isShadowNodeMaterial=!0,t.name="ShadowMaterial",t.fog=!1,lb.set(e,t)}return t},hb=Si((({depthTexture:e,shadowCoord:t})=>Au(e,t.xy).compare(t.z))),pb=Si((({depthTexture:e,shadowCoord:t,shadow:r})=>{const s=(t,r)=>Au(e,t).compare(r),i=Ul("mapSize","vec2",r).setGroup(tn),n=Ul("radius","float",r).setGroup(tn),o=Pi(1).div(i),a=o.x.negate().mul(n),u=o.y.negate().mul(n),l=o.x.mul(n),d=o.y.mul(n),c=a.div(2),h=u.div(2),p=l.div(2),g=d.div(2);return zn(s(t.xy.add(Pi(a,u)),t.z),s(t.xy.add(Pi(0,u)),t.z),s(t.xy.add(Pi(l,u)),t.z),s(t.xy.add(Pi(c,h)),t.z),s(t.xy.add(Pi(0,h)),t.z),s(t.xy.add(Pi(p,h)),t.z),s(t.xy.add(Pi(a,0)),t.z),s(t.xy.add(Pi(c,0)),t.z),s(t.xy,t.z),s(t.xy.add(Pi(p,0)),t.z),s(t.xy.add(Pi(l,0)),t.z),s(t.xy.add(Pi(c,g)),t.z),s(t.xy.add(Pi(0,g)),t.z),s(t.xy.add(Pi(p,g)),t.z),s(t.xy.add(Pi(a,d)),t.z),s(t.xy.add(Pi(0,d)),t.z),s(t.xy.add(Pi(l,d)),t.z)).mul(1/17)})),gb=Si((({depthTexture:e,shadowCoord:t,shadow:r})=>{const s=(t,r)=>Au(e,t).compare(r),i=Ul("mapSize","vec2",r).setGroup(tn),n=Pi(1).div(i),o=n.x,a=n.y,u=t.xy,l=wo(u.mul(i).add(.5));return u.subAssign(l.mul(n)),zn(s(u,t.z),s(u.add(Pi(o,0)),t.z),s(u.add(Pi(0,a)),t.z),s(u.add(n),t.z),ha(s(u.add(Pi(o.negate(),0)),t.z),s(u.add(Pi(o.mul(2),0)),t.z),l.x),ha(s(u.add(Pi(o.negate(),a)),t.z),s(u.add(Pi(o.mul(2),a)),t.z),l.x),ha(s(u.add(Pi(0,a.negate())),t.z),s(u.add(Pi(0,a.mul(2))),t.z),l.y),ha(s(u.add(Pi(o,a.negate())),t.z),s(u.add(Pi(o,a.mul(2))),t.z),l.y),ha(ha(s(u.add(Pi(o.negate(),a.negate())),t.z),s(u.add(Pi(o.mul(2),a.negate())),t.z),l.x),ha(s(u.add(Pi(o.negate(),a.mul(2))),t.z),s(u.add(Pi(o.mul(2),a.mul(2))),t.z),l.x),l.y)).mul(1/9)})),mb=Si((({depthTexture:e,shadowCoord:t})=>{const r=Mi(1).toVar(),s=Au(e).sample(t.xy).rg,i=Jo(t.z,s.x);return Ci(i.notEqual(Mi(1)),(()=>{const e=t.z.sub(s.x),n=Qo(0,s.y.mul(s.y));let o=n.div(n.add(e.mul(e)));o=pa($n(o,.3).div(.95-.3)),r.assign(pa(Qo(i,o)))})),r})),fb=Si((({samples:e,radius:t,size:r,shadowPass:s})=>{const i=Mi(0).toVar(),n=Mi(0).toVar(),o=e.lessThanEqual(Mi(1)).select(Mi(0),Mi(2).div(e.sub(1))),a=e.lessThanEqual(Mi(1)).select(Mi(0),Mi(-1));dc({start:Bi(0),end:Bi(e),type:"int",condition:"<"},(({i:e})=>{const u=a.add(Mi(e).mul(o)),l=s.sample(zn(Cc.xy,Pi(0,u).mul(t)).div(r)).x;i.addAssign(l),n.addAssign(l.mul(l))})),i.divAssign(e),n.divAssign(e);const u=So(n.sub(i.mul(i)));return Pi(i,u)})),yb=Si((({samples:e,radius:t,size:r,shadowPass:s})=>{const i=Mi(0).toVar(),n=Mi(0).toVar(),o=e.lessThanEqual(Mi(1)).select(Mi(0),Mi(2).div(e.sub(1))),a=e.lessThanEqual(Mi(1)).select(Mi(0),Mi(-1));dc({start:Bi(0),end:Bi(e),type:"int",condition:"<"},(({i:e})=>{const u=a.add(Mi(e).mul(o)),l=s.sample(zn(Cc.xy,Pi(u,0).mul(t)).div(r));i.addAssign(l.x),n.addAssign(zn(l.y.mul(l.y),l.x.mul(l.x)))})),i.divAssign(e),n.divAssign(e);const u=So(n.sub(i.mul(i)));return Pi(i,u)})),bb=[hb,pb,gb,mb];let xb;const Tb=new of;class _b extends Zy{static get type(){return"ShadowNode"}constructor(e,t=null){super(e),this.shadow=t||e.shadow,this.shadowMap=null,this.vsmShadowMapVertical=null,this.vsmShadowMapHorizontal=null,this.vsmMaterialVertical=null,this.vsmMaterialHorizontal=null,this._node=null,this.isShadowNode=!0}setupShadowFilter(e,{filterFn:t,depthTexture:r,shadowCoord:s,shadow:i}){const n=s.x.greaterThanEqual(0).and(s.x.lessThanEqual(1)).and(s.y.greaterThanEqual(0)).and(s.y.lessThanEqual(1)).and(s.z.lessThanEqual(1)),o=t({depthTexture:r,shadowCoord:s,shadow:i});return n.select(o,Mi(1))}setupShadowCoord(e,t){const{shadow:r}=this,{renderer:s}=e,i=Ul("bias","float",r).setGroup(tn);let n,o=t;if(r.camera.isOrthographicCamera||!0!==s.logarithmicDepthBuffer)o=o.xyz.div(o.w),n=o.z,s.coordinateSystem===l&&(n=n.mul(2).sub(1));else{const e=o.w;o=o.xy.div(e);const t=Ul("near","float",r.camera).setGroup(tn),s=Ul("far","float",r.camera).setGroup(tn);n=jc(e.negate(),t,s)}return o=Vi(o.x,o.y.oneMinus(),n.add(i)),o}getShadowFilterFn(e){return bb[e]}setupShadow(e){const{renderer:t}=e,{light:r,shadow:s}=this,i=t.shadowMap.type,n=new B(s.mapSize.width,s.mapSize.height);n.compareFunction=Re;const o=e.createRenderTarget(s.mapSize.width,s.mapSize.height);if(o.depthTexture=n,s.camera.updateProjectionMatrix(),i===Ce){n.compareFunction=null,this.vsmShadowMapVertical=e.createRenderTarget(s.mapSize.width,s.mapSize.height,{format:Ee,type:ye}),this.vsmShadowMapHorizontal=e.createRenderTarget(s.mapSize.width,s.mapSize.height,{format:Ee,type:ye});const t=Au(n),r=Au(this.vsmShadowMapVertical.texture),i=Ul("blurSamples","float",s).setGroup(tn),o=Ul("radius","float",s).setGroup(tn),a=Ul("mapSize","vec2",s).setGroup(tn);let u=this.vsmMaterialVertical||(this.vsmMaterialVertical=new rh);u.fragmentNode=fb({samples:i,radius:o,size:a,shadowPass:t}).context(e.getSharedContext()),u.name="VSMVertical",u=this.vsmMaterialHorizontal||(this.vsmMaterialHorizontal=new rh),u.fragmentNode=yb({samples:i,radius:o,size:a,shadowPass:r}).context(e.getSharedContext()),u.name="VSMHorizontal"}const a=Ul("intensity","float",s).setGroup(tn),u=Ul("normalBias","float",s).setGroup(tn),l=$y(r).mul(Jy.add(ml.mul(u))),d=this.setupShadowCoord(e,l),c=s.filterNode||this.getShadowFilterFn(t.shadowMap.type)||null;if(null===c)throw new Error("THREE.WebGPURenderer: Shadow map type not supported yet.");const h=i===Ce?this.vsmShadowMapHorizontal.texture:n,p=this.setupShadowFilter(e,{filterFn:c,shadowTexture:o.texture,depthTexture:h,shadowCoord:d,shadow:s}),g=Au(o.texture,d),m=ha(1,p.rgb.mix(g,1),a.mul(g.a)).toVar();return this.shadowMap=o,this.shadow.map=o,m}setup(e){if(!1!==e.renderer.shadowMap.enabled)return Si((()=>{let t=this._node;return this.setupShadowPosition(e),null===t&&(this._node=t=this.setupShadow(e)),e.material.shadowNode&&console.warn('THREE.NodeMaterial: ".shadowNode" is deprecated. Use ".castShadowNode" instead.'),e.material.receivedShadowNode&&(t=e.material.receivedShadowNode(t)),t}))()}renderShadow(e){const{shadow:t,shadowMap:r,light:s}=this,{renderer:i,scene:n}=e;t.updateMatrices(s),r.setSize(t.mapSize.width,t.mapSize.height),i.render(n,t.camera)}updateShadow(e){const{shadowMap:t,light:r,shadow:s}=this,{renderer:i,scene:n,camera:o}=e,a=i.shadowMap.type,u=t.depthTexture.version;this._depthVersionCached=u,s.camera.layers.mask=o.layers.mask;const l=i.getRenderObjectFunction(),d=i.getMRT(),c=!!d&&d.has("velocity");xb=ob(i,n,xb),n.overrideMaterial=cb(r),i.setRenderObjectFunction(((e,t,r,n,u,l,...d)=>{(!0===e.castShadow||e.receiveShadow&&a===Ce)&&(c&&(Ts(e).useVelocity=!0),e.onBeforeShadow(i,e,o,s.camera,n,t.overrideMaterial,l),i.renderObject(e,t,r,n,u,l,...d),e.onAfterShadow(i,e,o,s.camera,n,t.overrideMaterial,l))})),i.setRenderTarget(t),this.renderShadow(e),i.setRenderObjectFunction(l),!0!==r.isPointLight&&a===Ce&&this.vsmPass(i),ab(i,n,xb)}vsmPass(e){const{shadow:t}=this;this.vsmShadowMapVertical.setSize(t.mapSize.width,t.mapSize.height),this.vsmShadowMapHorizontal.setSize(t.mapSize.width,t.mapSize.height),e.setRenderTarget(this.vsmShadowMapVertical),Tb.material=this.vsmMaterialVertical,Tb.render(e),e.setRenderTarget(this.vsmShadowMapHorizontal),Tb.material=this.vsmMaterialHorizontal,Tb.render(e)}dispose(){this.shadowMap.dispose(),this.shadowMap=null,null!==this.vsmShadowMapVertical&&(this.vsmShadowMapVertical.dispose(),this.vsmShadowMapVertical=null,this.vsmMaterialVertical.dispose(),this.vsmMaterialVertical=null),null!==this.vsmShadowMapHorizontal&&(this.vsmShadowMapHorizontal.dispose(),this.vsmShadowMapHorizontal=null,this.vsmMaterialHorizontal.dispose(),this.vsmMaterialHorizontal=null),super.dispose()}updateBefore(e){const{shadow:t}=this;(t.needsUpdate||t.autoUpdate)&&(this.updateShadow(e),this.shadowMap.depthTexture.version===this._depthVersionCached&&(t.needsUpdate=!1))}}const vb=(e,t)=>xi(new _b(e,t));class Nb extends yc{static get type(){return"AnalyticLightNode"}constructor(t=null){super(),this.light=t,this.color=new e,this.colorNode=t&&t.colorNode||nn(this.color).setGroup(tn),this.baseColorNode=null,this.shadowNode=null,this.shadowColorNode=null,this.isAnalyticLightNode=!0,this.updateType=As.FRAME}customCacheKey(){return ds(this.light.id,this.light.castShadow?1:0)}getHash(){return this.light.uuid}setupShadowNode(){return vb(this.light)}setupShadow(e){const{renderer:t}=e;if(!1===t.shadowMap.enabled)return;let r=this.shadowColorNode;if(null===r){const t=this.light.shadow.shadowNode;let s;s=void 0!==t?xi(t):this.setupShadowNode(e),this.shadowNode=s,this.shadowColorNode=r=this.colorNode.mul(s),this.baseColorNode=this.colorNode}this.colorNode=r}setup(e){this.colorNode=this.baseColorNode||this.colorNode,this.light.castShadow?e.object.receiveShadow&&this.setupShadow(e):null!==this.shadowNode&&(this.shadowNode.dispose(),this.shadowNode=null,this.shadowColorNode=null)}update(){const{light:e}=this;this.color.copy(e.color).multiplyScalar(e.intensity)}}const Sb=Si((e=>{const{lightDistance:t,cutoffDistance:r,decayExponent:s}=e,i=t.pow(s).max(.01).reciprocal();return r.greaterThan(0).select(i.mul(t.div(r).pow4().oneMinus().clamp().pow2()),i)})),Ab=new e,Rb=Si((([e,t])=>{const r=e.toVar(),s=Do(r),i=Wn(1,Qo(s.x,Qo(s.y,s.z)));s.mulAssign(i),r.mulAssign(i.mul(t.mul(2).oneMinus()));const n=Pi(r.xy).toVar(),o=t.mul(1.5).oneMinus();return Ci(s.z.greaterThanEqual(o),(()=>{Ci(r.z.greaterThan(0),(()=>{n.x.assign($n(4,r.x))}))})).ElseIf(s.x.greaterThanEqual(o),(()=>{const e=Lo(r.x);n.x.assign(r.z.mul(e).add(e.mul(2)))})).ElseIf(s.y.greaterThanEqual(o),(()=>{const e=Lo(r.y);n.x.assign(r.x.add(e.mul(2)).add(2)),n.y.assign(r.z.mul(e).sub(2))})),Pi(.125,.25).mul(n).add(Pi(.375,.75)).flipY()})).setLayout({name:"cubeToUV",type:"vec2",inputs:[{name:"pos",type:"vec3"},{name:"texelSizeY",type:"float"}]}),Cb=Si((({depthTexture:e,bd3D:t,dp:r,texelSize:s})=>Au(e,Rb(t,s.y)).compare(r))),Eb=Si((({depthTexture:e,bd3D:t,dp:r,texelSize:s,shadow:i})=>{const n=Ul("radius","float",i).setGroup(tn),o=Pi(-1,1).mul(n).mul(s.y);return Au(e,Rb(t.add(o.xyy),s.y)).compare(r).add(Au(e,Rb(t.add(o.yyy),s.y)).compare(r)).add(Au(e,Rb(t.add(o.xyx),s.y)).compare(r)).add(Au(e,Rb(t.add(o.yyx),s.y)).compare(r)).add(Au(e,Rb(t,s.y)).compare(r)).add(Au(e,Rb(t.add(o.xxy),s.y)).compare(r)).add(Au(e,Rb(t.add(o.yxy),s.y)).compare(r)).add(Au(e,Rb(t.add(o.xxx),s.y)).compare(r)).add(Au(e,Rb(t.add(o.yxx),s.y)).compare(r)).mul(1/9)})),wb=Si((({filterFn:e,depthTexture:t,shadowCoord:r,shadow:s})=>{const i=r.xyz.toVar(),n=i.length(),o=nn("float").setGroup(tn).onRenderUpdate((()=>s.camera.near)),a=nn("float").setGroup(tn).onRenderUpdate((()=>s.camera.far)),u=Ul("bias","float",s).setGroup(tn),l=nn(s.mapSize).setGroup(tn),d=Mi(1).toVar();return Ci(n.sub(a).lessThanEqual(0).and(n.sub(o).greaterThanEqual(0)),(()=>{const r=n.sub(o).div(a.sub(o)).toVar();r.addAssign(u);const c=i.normalize(),h=Pi(1).div(l.mul(Pi(4,2)));d.assign(e({depthTexture:t,bd3D:c,dp:r,texelSize:h,shadow:s}))})),d})),Mb=new s,Bb=new t,Fb=new t;class Ub extends _b{static get type(){return"PointShadowNode"}constructor(e,t=null){super(e,t)}getShadowFilterFn(e){return e===we?Cb:Eb}setupShadowCoord(e,t){return t}setupShadowFilter(e,{filterFn:t,shadowTexture:r,depthTexture:s,shadowCoord:i,shadow:n}){return wb({filterFn:t,shadowTexture:r,depthTexture:s,shadowCoord:i,shadow:n})}renderShadow(e){const{shadow:t,shadowMap:r,light:s}=this,{renderer:i,scene:n}=e,o=t.getFrameExtents();Fb.copy(t.mapSize),Fb.multiply(o),r.setSize(Fb.width,Fb.height),Bb.copy(t.mapSize);const a=i.autoClear,u=i.getClearColor(Ab),l=i.getClearAlpha();i.autoClear=!1,i.setClearColor(t.clearColor,t.clearAlpha),i.clear();const d=t.getViewportCount();for(let e=0;e<d;e++){const o=t.getViewport(e),a=Bb.x*o.x,u=Fb.y-Bb.y-Bb.y*o.y;Mb.set(a,u,Bb.x*o.z,Bb.y*o.w),r.viewport.copy(Mb),t.updateMatrices(s,e),i.render(n,t.camera)}i.autoClear=a,i.setClearColor(u,l)}}const Pb=Si((({color:e,lightViewPosition:t,cutoffDistance:r,decayExponent:s},i)=>{const n=i.context.lightingModel,o=t.sub(il),a=o.normalize(),u=o.length(),l=Sb({lightDistance:u,cutoffDistance:r,decayExponent:s}),d=e.mul(l),c=i.context.reflectedLight;n.direct({lightDirection:a,lightColor:d,reflectedLight:c},i.stack,i)}));class Ib extends Nb{static get type(){return"PointLightNode"}constructor(e=null){super(e),this.cutoffDistanceNode=nn(0).setGroup(tn),this.decayExponentNode=nn(2).setGroup(tn)}update(e){const{light:t}=this;super.update(e),this.cutoffDistanceNode.value=t.distance,this.decayExponentNode.value=t.decay}setupShadowNode(){return((e,t)=>xi(new Ub(e,t)))(this.light)}setup(e){super.setup(e),Pb({color:this.colorNode,lightViewPosition:qy(this.light),cutoffDistance:this.cutoffDistanceNode,decayExponent:this.decayExponentNode}).append()}}const Db=Si((([e=t()])=>{const t=e.mul(2),r=t.x.floor(),s=t.y.floor();return r.add(s).mod(2).sign()})),Lb=Si((([e,t,r])=>{const s=Mi(r).toVar(),i=Mi(t).toVar(),n=Ui(e).toVar();return Aa(n,i,s)})).setLayout({name:"mx_select",type:"float",inputs:[{name:"b",type:"bool"},{name:"t",type:"float"},{name:"f",type:"float"}]}),Vb=Si((([e,t])=>{const r=Ui(t).toVar(),s=Mi(e).toVar();return Aa(r,s.negate(),s)})).setLayout({name:"mx_negate_if",type:"float",inputs:[{name:"val",type:"float"},{name:"b",type:"bool"}]}),Ob=Si((([e])=>{const t=Mi(e).toVar();return Bi(Ro(t))})).setLayout({name:"mx_floor",type:"int",inputs:[{name:"x",type:"float"}]}),Gb=Si((([e,t])=>{const r=Mi(e).toVar();return t.assign(Ob(r)),r.sub(Mi(t))})),kb=Em([Si((([e,t,r,s,i,n])=>{const o=Mi(n).toVar(),a=Mi(i).toVar(),u=Mi(s).toVar(),l=Mi(r).toVar(),d=Mi(t).toVar(),c=Mi(e).toVar(),h=Mi($n(1,a)).toVar();return $n(1,o).mul(c.mul(h).add(d.mul(a))).add(o.mul(l.mul(h).add(u.mul(a))))})).setLayout({name:"mx_bilerp_0",type:"float",inputs:[{name:"v0",type:"float"},{name:"v1",type:"float"},{name:"v2",type:"float"},{name:"v3",type:"float"},{name:"s",type:"float"},{name:"t",type:"float"}]}),Si((([e,t,r,s,i,n])=>{const o=Mi(n).toVar(),a=Mi(i).toVar(),u=Vi(s).toVar(),l=Vi(r).toVar(),d=Vi(t).toVar(),c=Vi(e).toVar(),h=Mi($n(1,a)).toVar();return $n(1,o).mul(c.mul(h).add(d.mul(a))).add(o.mul(l.mul(h).add(u.mul(a))))})).setLayout({name:"mx_bilerp_1",type:"vec3",inputs:[{name:"v0",type:"vec3"},{name:"v1",type:"vec3"},{name:"v2",type:"vec3"},{name:"v3",type:"vec3"},{name:"s",type:"float"},{name:"t",type:"float"}]})]),zb=Em([Si((([e,t,r,s,i,n,o,a,u,l,d])=>{const c=Mi(d).toVar(),h=Mi(l).toVar(),p=Mi(u).toVar(),g=Mi(a).toVar(),m=Mi(o).toVar(),f=Mi(n).toVar(),y=Mi(i).toVar(),b=Mi(s).toVar(),x=Mi(r).toVar(),T=Mi(t).toVar(),_=Mi(e).toVar(),v=Mi($n(1,p)).toVar(),N=Mi($n(1,h)).toVar();return Mi($n(1,c)).toVar().mul(N.mul(_.mul(v).add(T.mul(p))).add(h.mul(x.mul(v).add(b.mul(p))))).add(c.mul(N.mul(y.mul(v).add(f.mul(p))).add(h.mul(m.mul(v).add(g.mul(p))))))})).setLayout({name:"mx_trilerp_0",type:"float",inputs:[{name:"v0",type:"float"},{name:"v1",type:"float"},{name:"v2",type:"float"},{name:"v3",type:"float"},{name:"v4",type:"float"},{name:"v5",type:"float"},{name:"v6",type:"float"},{name:"v7",type:"float"},{name:"s",type:"float"},{name:"t",type:"float"},{name:"r",type:"float"}]}),Si((([e,t,r,s,i,n,o,a,u,l,d])=>{const c=Mi(d).toVar(),h=Mi(l).toVar(),p=Mi(u).toVar(),g=Vi(a).toVar(),m=Vi(o).toVar(),f=Vi(n).toVar(),y=Vi(i).toVar(),b=Vi(s).toVar(),x=Vi(r).toVar(),T=Vi(t).toVar(),_=Vi(e).toVar(),v=Mi($n(1,p)).toVar(),N=Mi($n(1,h)).toVar();return Mi($n(1,c)).toVar().mul(N.mul(_.mul(v).add(T.mul(p))).add(h.mul(x.mul(v).add(b.mul(p))))).add(c.mul(N.mul(y.mul(v).add(f.mul(p))).add(h.mul(m.mul(v).add(g.mul(p))))))})).setLayout({name:"mx_trilerp_1",type:"vec3",inputs:[{name:"v0",type:"vec3"},{name:"v1",type:"vec3"},{name:"v2",type:"vec3"},{name:"v3",type:"vec3"},{name:"v4",type:"vec3"},{name:"v5",type:"vec3"},{name:"v6",type:"vec3"},{name:"v7",type:"vec3"},{name:"s",type:"float"},{name:"t",type:"float"},{name:"r",type:"float"}]})]),$b=Si((([e,t,r])=>{const s=Mi(r).toVar(),i=Mi(t).toVar(),n=Fi(e).toVar(),o=Fi(n.bitAnd(Fi(7))).toVar(),a=Mi(Lb(o.lessThan(Fi(4)),i,s)).toVar(),u=Mi(Hn(2,Lb(o.lessThan(Fi(4)),s,i))).toVar();return Vb(a,Ui(o.bitAnd(Fi(1)))).add(Vb(u,Ui(o.bitAnd(Fi(2)))))})).setLayout({name:"mx_gradient_float_0",type:"float",inputs:[{name:"hash",type:"uint"},{name:"x",type:"float"},{name:"y",type:"float"}]}),Hb=Si((([e,t,r,s])=>{const i=Mi(s).toVar(),n=Mi(r).toVar(),o=Mi(t).toVar(),a=Fi(e).toVar(),u=Fi(a.bitAnd(Fi(15))).toVar(),l=Mi(Lb(u.lessThan(Fi(8)),o,n)).toVar(),d=Mi(Lb(u.lessThan(Fi(4)),n,Lb(u.equal(Fi(12)).or(u.equal(Fi(14))),o,i))).toVar();return Vb(l,Ui(u.bitAnd(Fi(1)))).add(Vb(d,Ui(u.bitAnd(Fi(2)))))})).setLayout({name:"mx_gradient_float_1",type:"float",inputs:[{name:"hash",type:"uint"},{name:"x",type:"float"},{name:"y",type:"float"},{name:"z",type:"float"}]}),Wb=Em([$b,Hb]),jb=Si((([e,t,r])=>{const s=Mi(r).toVar(),i=Mi(t).toVar(),n=Gi(e).toVar();return Vi(Wb(n.x,i,s),Wb(n.y,i,s),Wb(n.z,i,s))})).setLayout({name:"mx_gradient_vec3_0",type:"vec3",inputs:[{name:"hash",type:"uvec3"},{name:"x",type:"float"},{name:"y",type:"float"}]}),qb=Si((([e,t,r,s])=>{const i=Mi(s).toVar(),n=Mi(r).toVar(),o=Mi(t).toVar(),a=Gi(e).toVar();return Vi(Wb(a.x,o,n,i),Wb(a.y,o,n,i),Wb(a.z,o,n,i))})).setLayout({name:"mx_gradient_vec3_1",type:"vec3",inputs:[{name:"hash",type:"uvec3"},{name:"x",type:"float"},{name:"y",type:"float"},{name:"z",type:"float"}]}),Kb=Em([jb,qb]),Xb=Si((([e])=>{const t=Mi(e).toVar();return Hn(.6616,t)})).setLayout({name:"mx_gradient_scale2d_0",type:"float",inputs:[{name:"v",type:"float"}]}),Yb=Si((([e])=>{const t=Mi(e).toVar();return Hn(.982,t)})).setLayout({name:"mx_gradient_scale3d_0",type:"float",inputs:[{name:"v",type:"float"}]}),Qb=Em([Xb,Si((([e])=>{const t=Vi(e).toVar();return Hn(.6616,t)})).setLayout({name:"mx_gradient_scale2d_1",type:"vec3",inputs:[{name:"v",type:"vec3"}]})]),Zb=Em([Yb,Si((([e])=>{const t=Vi(e).toVar();return Hn(.982,t)})).setLayout({name:"mx_gradient_scale3d_1",type:"vec3",inputs:[{name:"v",type:"vec3"}]})]),Jb=Si((([e,t])=>{const r=Bi(t).toVar(),s=Fi(e).toVar();return s.shiftLeft(r).bitOr(s.shiftRight(Bi(32).sub(r)))})).setLayout({name:"mx_rotl32",type:"uint",inputs:[{name:"x",type:"uint"},{name:"k",type:"int"}]}),ex=Si((([e,t,r])=>{e.subAssign(r),e.bitXorAssign(Jb(r,Bi(4))),r.addAssign(t),t.subAssign(e),t.bitXorAssign(Jb(e,Bi(6))),e.addAssign(r),r.subAssign(t),r.bitXorAssign(Jb(t,Bi(8))),t.addAssign(e),e.subAssign(r),e.bitXorAssign(Jb(r,Bi(16))),r.addAssign(t),t.subAssign(e),t.bitXorAssign(Jb(e,Bi(19))),e.addAssign(r),r.subAssign(t),r.bitXorAssign(Jb(t,Bi(4))),t.addAssign(e)})),tx=Si((([e,t,r])=>{const s=Fi(r).toVar(),i=Fi(t).toVar(),n=Fi(e).toVar();return s.bitXorAssign(i),s.subAssign(Jb(i,Bi(14))),n.bitXorAssign(s),n.subAssign(Jb(s,Bi(11))),i.bitXorAssign(n),i.subAssign(Jb(n,Bi(25))),s.bitXorAssign(i),s.subAssign(Jb(i,Bi(16))),n.bitXorAssign(s),n.subAssign(Jb(s,Bi(4))),i.bitXorAssign(n),i.subAssign(Jb(n,Bi(14))),s.bitXorAssign(i),s.subAssign(Jb(i,Bi(24))),s})).setLayout({name:"mx_bjfinal",type:"uint",inputs:[{name:"a",type:"uint"},{name:"b",type:"uint"},{name:"c",type:"uint"}]}),rx=Si((([e])=>{const t=Fi(e).toVar();return Mi(t).div(Mi(Fi(Bi(4294967295))))})).setLayout({name:"mx_bits_to_01",type:"float",inputs:[{name:"bits",type:"uint"}]}),sx=Si((([e])=>{const t=Mi(e).toVar();return t.mul(t).mul(t).mul(t.mul(t.mul(6).sub(15)).add(10))})).setLayout({name:"mx_fade",type:"float",inputs:[{name:"t",type:"float"}]}),ix=Em([Si((([e])=>{const t=Bi(e).toVar(),r=Fi(Fi(1)).toVar(),s=Fi(Fi(Bi(3735928559)).add(r.shiftLeft(Fi(2))).add(Fi(13))).toVar();return tx(s.add(Fi(t)),s,s)})).setLayout({name:"mx_hash_int_0",type:"uint",inputs:[{name:"x",type:"int"}]}),Si((([e,t])=>{const r=Bi(t).toVar(),s=Bi(e).toVar(),i=Fi(Fi(2)).toVar(),n=Fi().toVar(),o=Fi().toVar(),a=Fi().toVar();return n.assign(o.assign(a.assign(Fi(Bi(3735928559)).add(i.shiftLeft(Fi(2))).add(Fi(13))))),n.addAssign(Fi(s)),o.addAssign(Fi(r)),tx(n,o,a)})).setLayout({name:"mx_hash_int_1",type:"uint",inputs:[{name:"x",type:"int"},{name:"y",type:"int"}]}),Si((([e,t,r])=>{const s=Bi(r).toVar(),i=Bi(t).toVar(),n=Bi(e).toVar(),o=Fi(Fi(3)).toVar(),a=Fi().toVar(),u=Fi().toVar(),l=Fi().toVar();return a.assign(u.assign(l.assign(Fi(Bi(3735928559)).add(o.shiftLeft(Fi(2))).add(Fi(13))))),a.addAssign(Fi(n)),u.addAssign(Fi(i)),l.addAssign(Fi(s)),tx(a,u,l)})).setLayout({name:"mx_hash_int_2",type:"uint",inputs:[{name:"x",type:"int"},{name:"y",type:"int"},{name:"z",type:"int"}]}),Si((([e,t,r,s])=>{const i=Bi(s).toVar(),n=Bi(r).toVar(),o=Bi(t).toVar(),a=Bi(e).toVar(),u=Fi(Fi(4)).toVar(),l=Fi().toVar(),d=Fi().toVar(),c=Fi().toVar();return l.assign(d.assign(c.assign(Fi(Bi(3735928559)).add(u.shiftLeft(Fi(2))).add(Fi(13))))),l.addAssign(Fi(a)),d.addAssign(Fi(o)),c.addAssign(Fi(n)),ex(l,d,c),l.addAssign(Fi(i)),tx(l,d,c)})).setLayout({name:"mx_hash_int_3",type:"uint",inputs:[{name:"x",type:"int"},{name:"y",type:"int"},{name:"z",type:"int"},{name:"xx",type:"int"}]}),Si((([e,t,r,s,i])=>{const n=Bi(i).toVar(),o=Bi(s).toVar(),a=Bi(r).toVar(),u=Bi(t).toVar(),l=Bi(e).toVar(),d=Fi(Fi(5)).toVar(),c=Fi().toVar(),h=Fi().toVar(),p=Fi().toVar();return c.assign(h.assign(p.assign(Fi(Bi(3735928559)).add(d.shiftLeft(Fi(2))).add(Fi(13))))),c.addAssign(Fi(l)),h.addAssign(Fi(u)),p.addAssign(Fi(a)),ex(c,h,p),c.addAssign(Fi(o)),h.addAssign(Fi(n)),tx(c,h,p)})).setLayout({name:"mx_hash_int_4",type:"uint",inputs:[{name:"x",type:"int"},{name:"y",type:"int"},{name:"z",type:"int"},{name:"xx",type:"int"},{name:"yy",type:"int"}]})]),nx=Em([Si((([e,t])=>{const r=Bi(t).toVar(),s=Bi(e).toVar(),i=Fi(ix(s,r)).toVar(),n=Gi().toVar();return n.x.assign(i.bitAnd(Bi(255))),n.y.assign(i.shiftRight(Bi(8)).bitAnd(Bi(255))),n.z.assign(i.shiftRight(Bi(16)).bitAnd(Bi(255))),n})).setLayout({name:"mx_hash_vec3_0",type:"uvec3",inputs:[{name:"x",type:"int"},{name:"y",type:"int"}]}),Si((([e,t,r])=>{const s=Bi(r).toVar(),i=Bi(t).toVar(),n=Bi(e).toVar(),o=Fi(ix(n,i,s)).toVar(),a=Gi().toVar();return a.x.assign(o.bitAnd(Bi(255))),a.y.assign(o.shiftRight(Bi(8)).bitAnd(Bi(255))),a.z.assign(o.shiftRight(Bi(16)).bitAnd(Bi(255))),a})).setLayout({name:"mx_hash_vec3_1",type:"uvec3",inputs:[{name:"x",type:"int"},{name:"y",type:"int"},{name:"z",type:"int"}]})]),ox=Em([Si((([e])=>{const t=Pi(e).toVar(),r=Bi().toVar(),s=Bi().toVar(),i=Mi(Gb(t.x,r)).toVar(),n=Mi(Gb(t.y,s)).toVar(),o=Mi(sx(i)).toVar(),a=Mi(sx(n)).toVar(),u=Mi(kb(Wb(ix(r,s),i,n),Wb(ix(r.add(Bi(1)),s),i.sub(1),n),Wb(ix(r,s.add(Bi(1))),i,n.sub(1)),Wb(ix(r.add(Bi(1)),s.add(Bi(1))),i.sub(1),n.sub(1)),o,a)).toVar();return Qb(u)})).setLayout({name:"mx_perlin_noise_float_0",type:"float",inputs:[{name:"p",type:"vec2"}]}),Si((([e])=>{const t=Vi(e).toVar(),r=Bi().toVar(),s=Bi().toVar(),i=Bi().toVar(),n=Mi(Gb(t.x,r)).toVar(),o=Mi(Gb(t.y,s)).toVar(),a=Mi(Gb(t.z,i)).toVar(),u=Mi(sx(n)).toVar(),l=Mi(sx(o)).toVar(),d=Mi(sx(a)).toVar(),c=Mi(zb(Wb(ix(r,s,i),n,o,a),Wb(ix(r.add(Bi(1)),s,i),n.sub(1),o,a),Wb(ix(r,s.add(Bi(1)),i),n,o.sub(1),a),Wb(ix(r.add(Bi(1)),s.add(Bi(1)),i),n.sub(1),o.sub(1),a),Wb(ix(r,s,i.add(Bi(1))),n,o,a.sub(1)),Wb(ix(r.add(Bi(1)),s,i.add(Bi(1))),n.sub(1),o,a.sub(1)),Wb(ix(r,s.add(Bi(1)),i.add(Bi(1))),n,o.sub(1),a.sub(1)),Wb(ix(r.add(Bi(1)),s.add(Bi(1)),i.add(Bi(1))),n.sub(1),o.sub(1),a.sub(1)),u,l,d)).toVar();return Zb(c)})).setLayout({name:"mx_perlin_noise_float_1",type:"float",inputs:[{name:"p",type:"vec3"}]})]),ax=Em([Si((([e])=>{const t=Pi(e).toVar(),r=Bi().toVar(),s=Bi().toVar(),i=Mi(Gb(t.x,r)).toVar(),n=Mi(Gb(t.y,s)).toVar(),o=Mi(sx(i)).toVar(),a=Mi(sx(n)).toVar(),u=Vi(kb(Kb(nx(r,s),i,n),Kb(nx(r.add(Bi(1)),s),i.sub(1),n),Kb(nx(r,s.add(Bi(1))),i,n.sub(1)),Kb(nx(r.add(Bi(1)),s.add(Bi(1))),i.sub(1),n.sub(1)),o,a)).toVar();return Qb(u)})).setLayout({name:"mx_perlin_noise_vec3_0",type:"vec3",inputs:[{name:"p",type:"vec2"}]}),Si((([e])=>{const t=Vi(e).toVar(),r=Bi().toVar(),s=Bi().toVar(),i=Bi().toVar(),n=Mi(Gb(t.x,r)).toVar(),o=Mi(Gb(t.y,s)).toVar(),a=Mi(Gb(t.z,i)).toVar(),u=Mi(sx(n)).toVar(),l=Mi(sx(o)).toVar(),d=Mi(sx(a)).toVar(),c=Vi(zb(Kb(nx(r,s,i),n,o,a),Kb(nx(r.add(Bi(1)),s,i),n.sub(1),o,a),Kb(nx(r,s.add(Bi(1)),i),n,o.sub(1),a),Kb(nx(r.add(Bi(1)),s.add(Bi(1)),i),n.sub(1),o.sub(1),a),Kb(nx(r,s,i.add(Bi(1))),n,o,a.sub(1)),Kb(nx(r.add(Bi(1)),s,i.add(Bi(1))),n.sub(1),o,a.sub(1)),Kb(nx(r,s.add(Bi(1)),i.add(Bi(1))),n,o.sub(1),a.sub(1)),Kb(nx(r.add(Bi(1)),s.add(Bi(1)),i.add(Bi(1))),n.sub(1),o.sub(1),a.sub(1)),u,l,d)).toVar();return Zb(c)})).setLayout({name:"mx_perlin_noise_vec3_1",type:"vec3",inputs:[{name:"p",type:"vec3"}]})]),ux=Em([Si((([e])=>{const t=Mi(e).toVar(),r=Bi(Ob(t)).toVar();return rx(ix(r))})).setLayout({name:"mx_cell_noise_float_0",type:"float",inputs:[{name:"p",type:"float"}]}),Si((([e])=>{const t=Pi(e).toVar(),r=Bi(Ob(t.x)).toVar(),s=Bi(Ob(t.y)).toVar();return rx(ix(r,s))})).setLayout({name:"mx_cell_noise_float_1",type:"float",inputs:[{name:"p",type:"vec2"}]}),Si((([e])=>{const t=Vi(e).toVar(),r=Bi(Ob(t.x)).toVar(),s=Bi(Ob(t.y)).toVar(),i=Bi(Ob(t.z)).toVar();return rx(ix(r,s,i))})).setLayout({name:"mx_cell_noise_float_2",type:"float",inputs:[{name:"p",type:"vec3"}]}),Si((([e])=>{const t=zi(e).toVar(),r=Bi(Ob(t.x)).toVar(),s=Bi(Ob(t.y)).toVar(),i=Bi(Ob(t.z)).toVar(),n=Bi(Ob(t.w)).toVar();return rx(ix(r,s,i,n))})).setLayout({name:"mx_cell_noise_float_3",type:"float",inputs:[{name:"p",type:"vec4"}]})]),lx=Em([Si((([e])=>{const t=Mi(e).toVar(),r=Bi(Ob(t)).toVar();return Vi(rx(ix(r,Bi(0))),rx(ix(r,Bi(1))),rx(ix(r,Bi(2))))})).setLayout({name:"mx_cell_noise_vec3_0",type:"vec3",inputs:[{name:"p",type:"float"}]}),Si((([e])=>{const t=Pi(e).toVar(),r=Bi(Ob(t.x)).toVar(),s=Bi(Ob(t.y)).toVar();return Vi(rx(ix(r,s,Bi(0))),rx(ix(r,s,Bi(1))),rx(ix(r,s,Bi(2))))})).setLayout({name:"mx_cell_noise_vec3_1",type:"vec3",inputs:[{name:"p",type:"vec2"}]}),Si((([e])=>{const t=Vi(e).toVar(),r=Bi(Ob(t.x)).toVar(),s=Bi(Ob(t.y)).toVar(),i=Bi(Ob(t.z)).toVar();return Vi(rx(ix(r,s,i,Bi(0))),rx(ix(r,s,i,Bi(1))),rx(ix(r,s,i,Bi(2))))})).setLayout({name:"mx_cell_noise_vec3_2",type:"vec3",inputs:[{name:"p",type:"vec3"}]}),Si((([e])=>{const t=zi(e).toVar(),r=Bi(Ob(t.x)).toVar(),s=Bi(Ob(t.y)).toVar(),i=Bi(Ob(t.z)).toVar(),n=Bi(Ob(t.w)).toVar();return Vi(rx(ix(r,s,i,n,Bi(0))),rx(ix(r,s,i,n,Bi(1))),rx(ix(r,s,i,n,Bi(2))))})).setLayout({name:"mx_cell_noise_vec3_3",type:"vec3",inputs:[{name:"p",type:"vec4"}]})]),dx=Si((([e,t,r,s])=>{const i=Mi(s).toVar(),n=Mi(r).toVar(),o=Bi(t).toVar(),a=Vi(e).toVar(),u=Mi(0).toVar(),l=Mi(1).toVar();return dc(o,(()=>{u.addAssign(l.mul(ox(a))),l.mulAssign(i),a.mulAssign(n)})),u})).setLayout({name:"mx_fractal_noise_float",type:"float",inputs:[{name:"p",type:"vec3"},{name:"octaves",type:"int"},{name:"lacunarity",type:"float"},{name:"diminish",type:"float"}]}),cx=Si((([e,t,r,s])=>{const i=Mi(s).toVar(),n=Mi(r).toVar(),o=Bi(t).toVar(),a=Vi(e).toVar(),u=Vi(0).toVar(),l=Mi(1).toVar();return dc(o,(()=>{u.addAssign(l.mul(ax(a))),l.mulAssign(i),a.mulAssign(n)})),u})).setLayout({name:"mx_fractal_noise_vec3",type:"vec3",inputs:[{name:"p",type:"vec3"},{name:"octaves",type:"int"},{name:"lacunarity",type:"float"},{name:"diminish",type:"float"}]}),hx=Si((([e,t,r,s])=>{const i=Mi(s).toVar(),n=Mi(r).toVar(),o=Bi(t).toVar(),a=Vi(e).toVar();return Pi(dx(a,o,n,i),dx(a.add(Vi(Bi(19),Bi(193),Bi(17))),o,n,i))})).setLayout({name:"mx_fractal_noise_vec2",type:"vec2",inputs:[{name:"p",type:"vec3"},{name:"octaves",type:"int"},{name:"lacunarity",type:"float"},{name:"diminish",type:"float"}]}),px=Si((([e,t,r,s])=>{const i=Mi(s).toVar(),n=Mi(r).toVar(),o=Bi(t).toVar(),a=Vi(e).toVar(),u=Vi(cx(a,o,n,i)).toVar(),l=Mi(dx(a.add(Vi(Bi(19),Bi(193),Bi(17))),o,n,i)).toVar();return zi(u,l)})).setLayout({name:"mx_fractal_noise_vec4",type:"vec4",inputs:[{name:"p",type:"vec3"},{name:"octaves",type:"int"},{name:"lacunarity",type:"float"},{name:"diminish",type:"float"}]}),gx=Em([Si((([e,t,r,s,i,n,o])=>{const a=Bi(o).toVar(),u=Mi(n).toVar(),l=Bi(i).toVar(),d=Bi(s).toVar(),c=Bi(r).toVar(),h=Bi(t).toVar(),p=Pi(e).toVar(),g=Vi(lx(Pi(h.add(d),c.add(l)))).toVar(),m=Pi(g.x,g.y).toVar();m.subAssign(.5),m.mulAssign(u),m.addAssign(.5);const f=Pi(Pi(Mi(h),Mi(c)).add(m)).toVar(),y=Pi(f.sub(p)).toVar();return Ci(a.equal(Bi(2)),(()=>Do(y.x).add(Do(y.y)))),Ci(a.equal(Bi(3)),(()=>Qo(Do(y.x),Do(y.y)))),sa(y,y)})).setLayout({name:"mx_worley_distance_0",type:"float",inputs:[{name:"p",type:"vec2"},{name:"x",type:"int"},{name:"y",type:"int"},{name:"xoff",type:"int"},{name:"yoff",type:"int"},{name:"jitter",type:"float"},{name:"metric",type:"int"}]}),Si((([e,t,r,s,i,n,o,a,u])=>{const l=Bi(u).toVar(),d=Mi(a).toVar(),c=Bi(o).toVar(),h=Bi(n).toVar(),p=Bi(i).toVar(),g=Bi(s).toVar(),m=Bi(r).toVar(),f=Bi(t).toVar(),y=Vi(e).toVar(),b=Vi(lx(Vi(f.add(p),m.add(h),g.add(c)))).toVar();b.subAssign(.5),b.mulAssign(d),b.addAssign(.5);const x=Vi(Vi(Mi(f),Mi(m),Mi(g)).add(b)).toVar(),T=Vi(x.sub(y)).toVar();return Ci(l.equal(Bi(2)),(()=>Do(T.x).add(Do(T.y)).add(Do(T.z)))),Ci(l.equal(Bi(3)),(()=>Qo(Qo(Do(T.x),Do(T.y)),Do(T.z)))),sa(T,T)})).setLayout({name:"mx_worley_distance_1",type:"float",inputs:[{name:"p",type:"vec3"},{name:"x",type:"int"},{name:"y",type:"int"},{name:"z",type:"int"},{name:"xoff",type:"int"},{name:"yoff",type:"int"},{name:"zoff",type:"int"},{name:"jitter",type:"float"},{name:"metric",type:"int"}]})]),mx=Si((([e,t,r])=>{const s=Bi(r).toVar(),i=Mi(t).toVar(),n=Pi(e).toVar(),o=Bi().toVar(),a=Bi().toVar(),u=Pi(Gb(n.x,o),Gb(n.y,a)).toVar(),l=Mi(1e6).toVar();return dc({start:-1,end:Bi(1),name:"x",condition:"<="},(({x:e})=>{dc({start:-1,end:Bi(1),name:"y",condition:"<="},(({y:t})=>{const r=Mi(gx(u,e,t,o,a,i,s)).toVar();l.assign(Yo(l,r))}))})),Ci(s.equal(Bi(0)),(()=>{l.assign(So(l))})),l})).setLayout({name:"mx_worley_noise_float_0",type:"float",inputs:[{name:"p",type:"vec2"},{name:"jitter",type:"float"},{name:"metric",type:"int"}]}),fx=Si((([e,t,r])=>{const s=Bi(r).toVar(),i=Mi(t).toVar(),n=Pi(e).toVar(),o=Bi().toVar(),a=Bi().toVar(),u=Pi(Gb(n.x,o),Gb(n.y,a)).toVar(),l=Pi(1e6,1e6).toVar();return dc({start:-1,end:Bi(1),name:"x",condition:"<="},(({x:e})=>{dc({start:-1,end:Bi(1),name:"y",condition:"<="},(({y:t})=>{const r=Mi(gx(u,e,t,o,a,i,s)).toVar();Ci(r.lessThan(l.x),(()=>{l.y.assign(l.x),l.x.assign(r)})).ElseIf(r.lessThan(l.y),(()=>{l.y.assign(r)}))}))})),Ci(s.equal(Bi(0)),(()=>{l.assign(So(l))})),l})).setLayout({name:"mx_worley_noise_vec2_0",type:"vec2",inputs:[{name:"p",type:"vec2"},{name:"jitter",type:"float"},{name:"metric",type:"int"}]}),yx=Si((([e,t,r])=>{const s=Bi(r).toVar(),i=Mi(t).toVar(),n=Pi(e).toVar(),o=Bi().toVar(),a=Bi().toVar(),u=Pi(Gb(n.x,o),Gb(n.y,a)).toVar(),l=Vi(1e6,1e6,1e6).toVar();return dc({start:-1,end:Bi(1),name:"x",condition:"<="},(({x:e})=>{dc({start:-1,end:Bi(1),name:"y",condition:"<="},(({y:t})=>{const r=Mi(gx(u,e,t,o,a,i,s)).toVar();Ci(r.lessThan(l.x),(()=>{l.z.assign(l.y),l.y.assign(l.x),l.x.assign(r)})).ElseIf(r.lessThan(l.y),(()=>{l.z.assign(l.y),l.y.assign(r)})).ElseIf(r.lessThan(l.z),(()=>{l.z.assign(r)}))}))})),Ci(s.equal(Bi(0)),(()=>{l.assign(So(l))})),l})).setLayout({name:"mx_worley_noise_vec3_0",type:"vec3",inputs:[{name:"p",type:"vec2"},{name:"jitter",type:"float"},{name:"metric",type:"int"}]}),bx=Em([mx,Si((([e,t,r])=>{const s=Bi(r).toVar(),i=Mi(t).toVar(),n=Vi(e).toVar(),o=Bi().toVar(),a=Bi().toVar(),u=Bi().toVar(),l=Vi(Gb(n.x,o),Gb(n.y,a),Gb(n.z,u)).toVar(),d=Mi(1e6).toVar();return dc({start:-1,end:Bi(1),name:"x",condition:"<="},(({x:e})=>{dc({start:-1,end:Bi(1),name:"y",condition:"<="},(({y:t})=>{dc({start:-1,end:Bi(1),name:"z",condition:"<="},(({z:r})=>{const n=Mi(gx(l,e,t,r,o,a,u,i,s)).toVar();d.assign(Yo(d,n))}))}))})),Ci(s.equal(Bi(0)),(()=>{d.assign(So(d))})),d})).setLayout({name:"mx_worley_noise_float_1",type:"float",inputs:[{name:"p",type:"vec3"},{name:"jitter",type:"float"},{name:"metric",type:"int"}]})]),xx=Em([fx,Si((([e,t,r])=>{const s=Bi(r).toVar(),i=Mi(t).toVar(),n=Vi(e).toVar(),o=Bi().toVar(),a=Bi().toVar(),u=Bi().toVar(),l=Vi(Gb(n.x,o),Gb(n.y,a),Gb(n.z,u)).toVar(),d=Pi(1e6,1e6).toVar();return dc({start:-1,end:Bi(1),name:"x",condition:"<="},(({x:e})=>{dc({start:-1,end:Bi(1),name:"y",condition:"<="},(({y:t})=>{dc({start:-1,end:Bi(1),name:"z",condition:"<="},(({z:r})=>{const n=Mi(gx(l,e,t,r,o,a,u,i,s)).toVar();Ci(n.lessThan(d.x),(()=>{d.y.assign(d.x),d.x.assign(n)})).ElseIf(n.lessThan(d.y),(()=>{d.y.assign(n)}))}))}))})),Ci(s.equal(Bi(0)),(()=>{d.assign(So(d))})),d})).setLayout({name:"mx_worley_noise_vec2_1",type:"vec2",inputs:[{name:"p",type:"vec3"},{name:"jitter",type:"float"},{name:"metric",type:"int"}]})]),Tx=Em([yx,Si((([e,t,r])=>{const s=Bi(r).toVar(),i=Mi(t).toVar(),n=Vi(e).toVar(),o=Bi().toVar(),a=Bi().toVar(),u=Bi().toVar(),l=Vi(Gb(n.x,o),Gb(n.y,a),Gb(n.z,u)).toVar(),d=Vi(1e6,1e6,1e6).toVar();return dc({start:-1,end:Bi(1),name:"x",condition:"<="},(({x:e})=>{dc({start:-1,end:Bi(1),name:"y",condition:"<="},(({y:t})=>{dc({start:-1,end:Bi(1),name:"z",condition:"<="},(({z:r})=>{const n=Mi(gx(l,e,t,r,o,a,u,i,s)).toVar();Ci(n.lessThan(d.x),(()=>{d.z.assign(d.y),d.y.assign(d.x),d.x.assign(n)})).ElseIf(n.lessThan(d.y),(()=>{d.z.assign(d.y),d.y.assign(n)})).ElseIf(n.lessThan(d.z),(()=>{d.z.assign(n)}))}))}))})),Ci(s.equal(Bi(0)),(()=>{d.assign(So(d))})),d})).setLayout({name:"mx_worley_noise_vec3_1",type:"vec3",inputs:[{name:"p",type:"vec3"},{name:"jitter",type:"float"},{name:"metric",type:"int"}]})]),_x=Si((([e])=>{const t=e.y,r=e.z,s=Vi().toVar();return Ci(t.lessThan(1e-4),(()=>{s.assign(Vi(r,r,r))})).Else((()=>{let i=e.x;i=i.sub(Ro(i)).mul(6).toVar();const n=Bi(Wo(i)),o=i.sub(Mi(n)),a=r.mul(t.oneMinus()),u=r.mul(t.mul(o).oneMinus()),l=r.mul(t.mul(o.oneMinus()).oneMinus());Ci(n.equal(Bi(0)),(()=>{s.assign(Vi(r,l,a))})).ElseIf(n.equal(Bi(1)),(()=>{s.assign(Vi(u,r,a))})).ElseIf(n.equal(Bi(2)),(()=>{s.assign(Vi(a,r,l))})).ElseIf(n.equal(Bi(3)),(()=>{s.assign(Vi(a,u,r))})).ElseIf(n.equal(Bi(4)),(()=>{s.assign(Vi(l,a,r))})).Else((()=>{s.assign(Vi(r,a,u))}))})),s})).setLayout({name:"mx_hsvtorgb",type:"vec3",inputs:[{name:"hsv",type:"vec3"}]}),vx=Si((([e])=>{const t=Vi(e).toVar(),r=Mi(t.x).toVar(),s=Mi(t.y).toVar(),i=Mi(t.z).toVar(),n=Mi(Yo(r,Yo(s,i))).toVar(),o=Mi(Qo(r,Qo(s,i))).toVar(),a=Mi(o.sub(n)).toVar(),u=Mi().toVar(),l=Mi().toVar(),d=Mi().toVar();return d.assign(o),Ci(o.greaterThan(0),(()=>{l.assign(a.div(o))})).Else((()=>{l.assign(0)})),Ci(l.lessThanEqual(0),(()=>{u.assign(0)})).Else((()=>{Ci(r.greaterThanEqual(o),(()=>{u.assign(s.sub(i).div(a))})).ElseIf(s.greaterThanEqual(o),(()=>{u.assign(zn(2,i.sub(r).div(a)))})).Else((()=>{u.assign(zn(4,r.sub(s).div(a)))})),u.mulAssign(1/6),Ci(u.lessThan(0),(()=>{u.addAssign(1)}))})),Vi(u,l,d)})).setLayout({name:"mx_rgbtohsv",type:"vec3",inputs:[{name:"c",type:"vec3"}]}),Nx=Si((([e])=>{const t=Vi(e).toVar(),r=ki(Yn(t,Vi(.04045))).toVar(),s=Vi(t.div(12.92)).toVar(),i=Vi(na(Qo(t.add(Vi(.055)),Vi(0)).div(1.055),Vi(2.4))).toVar();return ha(s,i,r)})).setLayout({name:"mx_srgb_texture_to_lin_rec709",type:"vec3",inputs:[{name:"color",type:"vec3"}]}),Sx=(e,t)=>{e=Mi(e),t=Mi(t);const r=Pi(t.dFdx(),t.dFdy()).length().mul(.7071067811865476);return fa(e.sub(r),e.add(r),t)},Ax=(e,t,r,s)=>ha(e,t,r[s].clamp()),Rx=(e,t,r,s,i)=>ha(e,t,Sx(r,s[i])),Cx=Si((([e,t,r])=>{const s=Eo(e).toVar("nDir"),i=$n(Mi(.5).mul(t.sub(r)),rl).div(s).toVar("rbmax"),n=$n(Mi(-.5).mul(t.sub(r)),rl).div(s).toVar("rbmin"),o=Vi().toVar("rbminmax");o.x=s.x.greaterThan(Mi(0)).select(i.x,n.x),o.y=s.y.greaterThan(Mi(0)).select(i.y,n.y),o.z=s.z.greaterThan(Mi(0)).select(i.z,n.z);const a=Yo(Yo(o.x,o.y),o.z).toVar("correction");return rl.add(s.mul(a)).toVar("boxIntersection").sub(r)})),Ex=Si((([e,t])=>{const r=e.x,s=e.y,i=e.z;let n=t.element(0).mul(.886227);return n=n.add(t.element(1).mul(1.023328).mul(s)),n=n.add(t.element(2).mul(1.023328).mul(i)),n=n.add(t.element(3).mul(1.023328).mul(r)),n=n.add(t.element(4).mul(.858086).mul(r).mul(s)),n=n.add(t.element(5).mul(.858086).mul(s).mul(i)),n=n.add(t.element(6).mul(i.mul(i).mul(.743125).sub(.247708))),n=n.add(t.element(7).mul(.858086).mul(r).mul(i)),n=n.add(t.element(8).mul(.429043).mul(Hn(r,r).sub(Hn(s,s)))),n}));var wx=Object.freeze({__proto__:null,BRDF_GGX:qh,BRDF_Lambert:Fh,BasicShadowFilter:hb,Break:cc,Continue:()=>pu("continue").append(),DFGApprox:Kh,D_GGX:Hh,Discard:gu,EPSILON:ho,F_Schlick:Bh,Fn:Si,INFINITY:po,If:Ci,Loop:dc,NodeAccess:Cs,NodeShaderStage:Ss,NodeType:Rs,NodeUpdateType:As,PCFShadowFilter:pb,PCFSoftShadowFilter:gb,PI:go,PI2:mo,Return:()=>pu("return").append(),Schlick_to_F0:Yh,ScriptableNodeResources:by,ShaderNode:bi,TBNViewMatrix:Yl,VSMShadowFilter:mb,V_GGX_SmithCorrelated:zh,abs:Do,acesFilmicToneMapping:iy,acos:Po,add:zn,addMethodChaining:js,addNodeElement:function(e){console.warn("THREE.TSLBase: AddNodeElement has been removed in favor of tree-shaking. Trying add",e)},agxToneMapping:uy,all:fo,alphaT:Tn,and:Jn,anisotropy:_n,anisotropyB:Nn,anisotropyT:vn,any:yo,append:Ei,arrayBuffer:e=>xi(new $s(e,"ArrayBuffer")),asin:Uo,assign:Vn,atan:Io,atan2:_a,atomicAdd:(e,t,r=null)=>Gy(Vy.ATOMIC_ADD,e,t,r),atomicAnd:(e,t,r=null)=>Gy(Vy.ATOMIC_AND,e,t,r),atomicFunc:Gy,atomicMax:(e,t,r=null)=>Gy(Vy.ATOMIC_MAX,e,t,r),atomicMin:(e,t,r=null)=>Gy(Vy.ATOMIC_MIN,e,t,r),atomicOr:(e,t,r=null)=>Gy(Vy.ATOMIC_OR,e,t,r),atomicStore:(e,t,r=null)=>Gy(Vy.ATOMIC_STORE,e,t,r),atomicSub:(e,t,r=null)=>Gy(Vy.ATOMIC_SUB,e,t,r),atomicXor:(e,t,r=null)=>Gy(Vy.ATOMIC_XOR,e,t,r),attenuationColor:In,attenuationDistance:Pn,attribute:bu,attributeArray:(e,t="float")=>{const r=ys(t),s=fs(t),i=new gf(e,r,s);return bf(i,t,e)},backgroundBlurriness:Af,backgroundIntensity:Rf,backgroundRotation:Cf,batch:nc,billboarding:Pm,bitAnd:so,bitNot:io,bitOr:no,bitXor:oo,bitangentGeometry:Hl,bitangentLocal:Wl,bitangentView:jl,bitangentWorld:ql,bitcast:Ko,blendBurn:Df,blendColor:Gf,blendDodge:Lf,blendOverlay:Of,blendScreen:Vf,blur:Kp,bool:Ui,buffer:Cl,bufferAttribute:Ja,bumpMap:nd,burn:(...e)=>(console.warn('THREE.TSL: "burn" has been renamed. Use "blendBurn" instead.'),Df(e)),bvec2:Li,bvec3:ki,bvec4:Wi,bypass:uu,cache:ou,call:Gn,cameraFar:Eu,cameraNear:Cu,cameraNormalMatrix:Uu,cameraPosition:Pu,cameraProjectionMatrix:wu,cameraProjectionMatrixInverse:Mu,cameraViewMatrix:Bu,cameraWorldMatrix:Fu,cbrt:da,cdl:jf,ceil:Co,checker:Db,cineonToneMapping:ry,clamp:pa,clearcoat:pn,clearcoatRoughness:gn,code:cy,color:wi,colorSpaceToWorking:Ha,colorToDirection:e=>xi(e).mul(2).sub(1),compute:iu,cond:Ra,context:Ea,convert:Yi,convertColorSpace:(e,t,r)=>xi(new Ga(xi(e),t,r)),convertToTexture:(e,...t)=>e.isTextureNode?e:e.isPassNode?e.getTextureNode():lf(e,...t),cos:Bo,cross:ia,cubeTexture:Al,dFdx:ko,dFdy:zo,dashSize:En,defaultBuildStages:ws,defaultShaderStages:Es,defined:fi,degrees:xo,deltaTime:Mm,densityFog:function(e,t){return console.warn('THREE.TSL: "densityFog( color, density )" is deprecated. Use "fog( color, densityFogFactor( density ) )" instead.'),Sy(e,Ny(t))},densityFogFactor:Ny,depth:Kc,depthPass:(e,t,r)=>xi(new Zf(Zf.DEPTH,e,t,r)),difference:ra,diffuseColor:ln,directPointLight:Pb,directionToColor:gh,dispersion:Dn,distance:ta,div:Wn,dodge:(...e)=>(console.warn('THREE.TSL: "dodge" has been renamed. Use "blendDodge" instead.'),Lf(e)),dot:sa,drawIndex:Jd,dynamicBufferAttribute:eu,element:Xi,emissive:dn,equal:qn,equals:Xo,equirectUV:bh,exp:To,exp2:_o,expression:pu,faceDirection:ul,faceForward:ya,faceforward:va,float:Mi,floor:Ro,fog:Sy,fract:wo,frameGroup:en,frameId:Bm,frontFacing:al,fwidth:jo,gain:(e,t)=>e.lessThan(.5)?vm(e.mul(2),t).div(2):$n(1,vm(Hn($n(1,e),2),t).div(2)),gapSize:wn,getConstNodeType:yi,getCurrentStack:Ri,getDirection:Hp,getDistanceAttenuation:Sb,getGeometryRoughness:Gh,getNormalFromDepth:hf,getParallaxCorrectNormal:Cx,getRoughness:kh,getScreenPosition:cf,getShIrradianceAt:Ex,getTextureIndex:bm,getViewPosition:df,glsl:(e,t)=>cy(e,t,"glsl"),glslFn:(e,t)=>py(e,t,"glsl"),grayscale:kf,greaterThan:Yn,greaterThanEqual:Zn,hash:_m,highpModelNormalViewMatrix:Zu,highpModelViewMatrix:Qu,hue:Hf,instance:tc,instanceIndex:Xd,instancedArray:(e,t="float")=>{const r=ys(t),s=fs(t),i=new pf(e,r,s);return bf(i,t,e)},instancedBufferAttribute:tu,instancedDynamicBufferAttribute:ru,instancedMesh:sc,int:Bi,inverseSqrt:Ao,inversesqrt:Na,invocationLocalIndex:Zd,invocationSubgroupIndex:Qd,ior:Bn,iridescence:yn,iridescenceIOR:bn,iridescenceThickness:xn,ivec2:Ii,ivec3:Oi,ivec4:$i,js:(e,t)=>cy(e,t,"js"),label:wa,length:Vo,lengthSq:ca,lessThan:Xn,lessThanEqual:Qn,lightPosition:Wy,lightProjectionUV:Hy,lightShadowMatrix:$y,lightTargetDirection:Ky,lightTargetPosition:jy,lightViewPosition:qy,lightingContext:Tc,lights:(e=[])=>xi(new Qy).setLights(e),linearDepth:Xc,linearToneMapping:ey,localId:Uy,log:vo,log2:No,logarithmicDepthToViewZ:(e,t,r)=>{const s=e.mul(vo(r.div(t)));return Mi(Math.E).pow(s).mul(t).negate()},loop:(...e)=>(console.warn("TSL.LoopNode: loop() has been renamed to Loop()."),dc(...e)),luminance:Wf,mat2:ji,mat3:qi,mat4:Ki,matcapUV:mg,materialAO:Hd,materialAlphaTest:ud,materialAnisotropy:Cd,materialAnisotropyVector:Wd,materialAttenuationColor:Id,materialAttenuationDistance:Pd,materialClearcoat:_d,materialClearcoatNormal:Nd,materialClearcoatRoughness:vd,materialColor:ld,materialDispersion:zd,materialEmissive:cd,materialIOR:Ud,materialIridescence:Ed,materialIridescenceIOR:wd,materialIridescenceThickness:Md,materialLightMap:$d,materialLineDashOffset:Gd,materialLineDashSize:Ld,materialLineGapSize:Vd,materialLineScale:Dd,materialLineWidth:Od,materialMetalness:xd,materialNormal:Td,materialOpacity:hd,materialPointWidth:kd,materialReference:Dl,materialReflectivity:yd,materialRefractionRatio:xl,materialRotation:Sd,materialRoughness:bd,materialSheen:Ad,materialSheenRoughness:Rd,materialShininess:dd,materialSpecular:pd,materialSpecularColor:md,materialSpecularIntensity:gd,materialSpecularStrength:fd,materialThickness:Fd,materialTransmission:Bd,max:Qo,maxMipLevel:Nu,mediumpModelViewMatrix:Yu,metalness:hn,min:Yo,mix:ha,mixElement:xa,mod:Zo,modInt:jn,modelDirection:zu,modelNormalMatrix:qu,modelPosition:Hu,modelScale:Wu,modelViewMatrix:Xu,modelViewPosition:ju,modelViewProjection:jd,modelWorldMatrix:$u,modelWorldMatrixInverse:Ku,morphReference:fc,mrt:Tm,mul:Hn,mx_aastep:Sx,mx_cell_noise_float:(e=xu())=>ux(e.convert("vec2|vec3")),mx_contrast:(e,t=1,r=.5)=>Mi(e).sub(r).mul(t).add(r),mx_fractal_noise_float:(e=xu(),t=3,r=2,s=.5,i=1)=>dx(e,Bi(t),r,s).mul(i),mx_fractal_noise_vec2:(e=xu(),t=3,r=2,s=.5,i=1)=>hx(e,Bi(t),r,s).mul(i),mx_fractal_noise_vec3:(e=xu(),t=3,r=2,s=.5,i=1)=>cx(e,Bi(t),r,s).mul(i),mx_fractal_noise_vec4:(e=xu(),t=3,r=2,s=.5,i=1)=>px(e,Bi(t),r,s).mul(i),mx_hsvtorgb:_x,mx_noise_float:(e=xu(),t=1,r=0)=>ox(e.convert("vec2|vec3")).mul(t).add(r),mx_noise_vec3:(e=xu(),t=1,r=0)=>ax(e.convert("vec2|vec3")).mul(t).add(r),mx_noise_vec4:(e=xu(),t=1,r=0)=>{e=e.convert("vec2|vec3");return zi(ax(e),ox(e.add(Pi(19,73)))).mul(t).add(r)},mx_ramplr:(e,t,r=xu())=>Ax(e,t,r,"x"),mx_ramptb:(e,t,r=xu())=>Ax(e,t,r,"y"),mx_rgbtohsv:vx,mx_safepower:(e,t=1)=>(e=Mi(e)).abs().pow(t).mul(e.sign()),mx_splitlr:(e,t,r,s=xu())=>Rx(e,t,r,s,"x"),mx_splittb:(e,t,r,s=xu())=>Rx(e,t,r,s,"y"),mx_srgb_texture_to_lin_rec709:Nx,mx_transform_uv:(e=1,t=0,r=xu())=>r.mul(e).add(t),mx_worley_noise_float:(e=xu(),t=1)=>bx(e.convert("vec2|vec3"),t,Bi(1)),mx_worley_noise_vec2:(e=xu(),t=1)=>xx(e.convert("vec2|vec3"),t,Bi(1)),mx_worley_noise_vec3:(e=xu(),t=1)=>Tx(e.convert("vec2|vec3"),t,Bi(1)),negate:Oo,neutralToneMapping:ly,nodeArray:_i,nodeImmutable:Ni,nodeObject:xi,nodeObjects:Ti,nodeProxy:vi,normalFlat:cl,normalGeometry:ll,normalLocal:dl,normalMap:td,normalView:hl,normalWorld:pl,normalize:Eo,not:to,notEqual:Kn,numWorkgroups:By,objectDirection:Du,objectGroup:rn,objectPosition:Vu,objectScale:Ou,objectViewPosition:Gu,objectWorldMatrix:Lu,oneMinus:Go,or:eo,orthographicDepthToViewZ:(e,t,r)=>t.sub(r).mul(e).sub(t),oscSawtooth:(e=wm)=>e.fract(),oscSine:(e=wm)=>e.add(.75).mul(2*Math.PI).sin().mul(.5).add(.5),oscSquare:(e=wm)=>e.fract().round(),oscTriangle:(e=wm)=>e.add(.5).fract().mul(2).sub(1).abs(),output:Cn,outputStruct:ym,overlay:(...e)=>(console.warn('THREE.TSL: "overlay" has been renamed. Use "blendOverlay" instead.'),Of(e)),overloadingFn:Em,parabola:vm,parallaxDirection:Ql,parallaxUV:(e,t)=>e.sub(Ql.mul(t)),parameter:(e,t)=>xi(new pm(e,t)),pass:(e,t,r)=>xi(new Zf(Zf.COLOR,e,t,r)),passTexture:(e,t)=>xi(new Yf(e,t)),pcurve:(e,t,r)=>na(Wn(na(e,t),zn(na(e,t),na($n(1,e),r))),1/t),perspectiveDepthToViewZ:Wc,pmremTexture:Jp,pointUV:_f,pointWidth:Mn,positionGeometry:Ju,positionLocal:el,positionPrevious:tl,positionView:il,positionViewDirection:nl,positionWorld:rl,positionWorldDirection:sl,posterize:Kf,pow:na,pow2:oa,pow3:aa,pow4:ua,property:an,radians:bo,rand:ba,range:Ey,rangeFog:function(e,t,r){return console.warn('THREE.TSL: "rangeFog( color, near, far )" is deprecated. Use "fog( color, rangeFogFactor( near, far ) )" instead.'),Sy(e,vy(t,r))},rangeFogFactor:vy,reciprocal:Ho,reference:Ul,referenceBuffer:Pl,reflect:ea,reflectVector:vl,reflectView:Tl,reflector:e=>xi(new ef(e)),refract:ma,refractVector:Nl,refractView:_l,reinhardToneMapping:ty,remainder:lo,remap:du,remapClamp:cu,renderGroup:tn,renderOutput:fu,rendererReference:Ka,rotate:_g,rotateUV:Fm,roughness:cn,round:$o,rtt:lf,sRGBTransferEOTF:Da,sRGBTransferOETF:La,sampler:e=>(!0===e.isNode?e:Au(e)).convert("sampler"),saturate:ga,saturation:zf,screen:(...e)=>(console.warn('THREE.TSL: "screen" has been renamed. Use "blendScreen" instead.'),Vf(e)),screenCoordinate:Cc,screenSize:Rc,screenUV:Ac,scriptable:Ty,scriptableValue:my,select:Aa,setCurrentStack:Ai,shaderStages:Ms,shadow:vb,shadowPositionWorld:Jy,sharedUniformGroup:Ji,sheen:mn,sheenRoughness:fn,shiftLeft:ao,shiftRight:uo,shininess:Rn,sign:Lo,sin:Mo,sinc:(e,t)=>Mo(go.mul(t.mul(e).sub(1))).div(go.mul(t.mul(e).sub(1))),skinning:e=>xi(new ac(e)),skinningReference:uc,smoothstep:fa,smoothstepElement:Ta,specularColor:Sn,specularF90:An,spherizeUV:Um,split:(e,t)=>xi(new Os(xi(e),t)),spritesheetUV:Lm,sqrt:So,stack:mm,step:Jo,storage:bf,storageBarrier:()=>Iy("storage").append(),storageObject:(e,t,r)=>(console.warn('THREE.TSL: "storageObject()" is deprecated. Use "storage().setPBO( true )" instead.'),bf(e,t,r).setPBO(!0)),storageTexture:wf,string:(e="")=>xi(new $s(e,"string")),sub:$n,subgroupIndex:Yd,subgroupSize:Py,tan:Fo,tangentGeometry:Ll,tangentLocal:Vl,tangentView:Ol,tangentWorld:Gl,temp:Fa,texture:Au,texture3D:wg,textureBarrier:()=>Iy("texture").append(),textureBicubic:gp,textureCubeUV:Wp,textureLoad:Ru,textureSize:_u,textureStore:(e,t,r)=>{const s=wf(e,t,r);return null!==r&&s.append(),s},thickness:Un,time:wm,timerDelta:(e=1)=>(console.warn('TSL: timerDelta() is deprecated. Use "deltaTime" instead.'),Mm.mul(e)),timerGlobal:(e=1)=>(console.warn('TSL: timerGlobal() is deprecated. Use "time" instead.'),wm.mul(e)),timerLocal:(e=1)=>(console.warn('TSL: timerLocal() is deprecated. Use "time" instead.'),wm.mul(e)),toOutputColorSpace:ka,toWorkingColorSpace:za,toneMapping:Ya,toneMappingExposure:Qa,toonOutlinePass:(t,r,s=new e(0,0,0),i=.003,n=1)=>xi(new Jf(t,r,xi(s),xi(i),xi(n))),transformDirection:la,transformNormal:yl,transformNormalToView:bl,transformedBentNormalView:Zl,transformedBitangentView:Kl,transformedBitangentWorld:Xl,transformedClearcoatNormalView:fl,transformedNormalView:gl,transformedNormalWorld:ml,transformedTangentView:kl,transformedTangentWorld:zl,transmission:Fn,transpose:qo,triNoise3D:Am,triplanarTexture:(...e)=>Om(...e),triplanarTextures:Om,trunc:Wo,tslFn:(...e)=>(console.warn("TSL.ShaderNode: tslFn() has been renamed to Fn()."),Si(...e)),uint:Fi,uniform:nn,uniformArray:Ml,uniformGroup:Zi,uniforms:(e,t)=>(console.warn("TSL.UniformArrayNode: uniforms() has been renamed to uniformArray()."),xi(new wl(e,t))),userData:(e,t,r)=>xi(new Mf(e,t,r)),uv:xu,uvec2:Di,uvec3:Gi,uvec4:Hi,varying:Pa,varyingProperty:un,vec2:Pi,vec3:Vi,vec4:zi,vectorComponents:Bs,velocity:If,vertexColor:e=>xi(new xf(e)),vertexIndex:Kd,vertexStage:Ia,vibrance:$f,viewZToLogarithmicDepth:jc,viewZToOrthographicDepth:$c,viewZToPerspectiveDepth:Hc,viewport:Ec,viewportBottomLeft:Pc,viewportCoordinate:Mc,viewportDepthTexture:kc,viewportLinearDepth:Yc,viewportMipTexture:Vc,viewportResolution:Fc,viewportSafeUV:Im,viewportSharedTexture:ch,viewportSize:wc,viewportTexture:Lc,viewportTopLeft:Uc,viewportUV:Bc,wgsl:(e,t)=>cy(e,t,"wgsl"),wgslFn:(e,t)=>py(e,t,"wgsl"),workgroupArray:(e,t)=>xi(new Ly("Workgroup",e,t)),workgroupBarrier:()=>Iy("workgroup").append(),workgroupId:Fy,workingToColorSpace:$a,xor:ro});const Mx=new hm;class Bx extends Lg{constructor(e,t){super(),this.renderer=e,this.nodes=t}update(e,t,r){const s=this.renderer,i=this.nodes.getBackgroundNode(e)||e.background;let n=!1;if(null===i)s._clearColor.getRGB(Mx,Ae),Mx.a=s._clearColor.a;else if(!0===i.isColor)i.getRGB(Mx,Ae),Mx.a=1,n=!0;else if(!0===i.isNode){const r=this.get(e),n=i;Mx.copy(s._clearColor);let o=r.backgroundMesh;if(void 0===o){const e=Ea(zi(n).mul(Rf),{getUV:()=>Cf.mul(pl),getTextureLevel:()=>Af});let t=jd;t=t.setZ(t.w);const s=new rh;s.name="Background.material",s.side=T,s.depthTest=!1,s.depthWrite=!1,s.fog=!1,s.lights=!1,s.vertexNode=t,s.colorNode=e,r.backgroundMeshNode=e,r.backgroundMesh=o=new k(new Me(1,32,32),s),o.frustumCulled=!1,o.name="Background.mesh",o.onBeforeRender=function(e,t,r){this.matrixWorld.copyPosition(r.matrixWorld)}}const a=n.getCacheKey();r.backgroundCacheKey!==a&&(r.backgroundMeshNode.node=zi(n).mul(Rf),r.backgroundMeshNode.needsUpdate=!0,o.material.needsUpdate=!0,r.backgroundCacheKey=a),t.unshift(o,o.geometry,o.material,0,0,null,null)}else console.error("THREE.Renderer: Unsupported background configuration.",i);if(!0===s.autoClear||!0===n){const e=r.clearColorValue;e.r=Mx.r,e.g=Mx.g,e.b=Mx.b,e.a=Mx.a,!0!==s.backend.isWebGLBackend&&!0!==s.alpha||(e.r*=e.a,e.g*=e.a,e.b*=e.a),r.depthClearValue=s._clearDepth,r.stencilClearValue=s._clearStencil,r.clearColor=!0===s.autoClearColor,r.clearDepth=!0===s.autoClearDepth,r.clearStencil=!0===s.autoClearStencil}else r.clearColor=!1,r.clearDepth=!1,r.clearStencil=!1}}let Fx=0;class Ux{constructor(e="",t=[],r=0,s=[]){this.name=e,this.bindings=t,this.index=r,this.bindingsReference=s,this.id=Fx++}}class Px{constructor(e,t,r,s,i,n,o,a,u,l=[]){this.vertexShader=e,this.fragmentShader=t,this.computeShader=r,this.transforms=l,this.nodeAttributes=s,this.bindings=i,this.updateNodes=n,this.updateBeforeNodes=o,this.updateAfterNodes=a,this.monitor=u,this.usedTimes=0}createBindings(){const e=[];for(const t of this.bindings){if(!0!==t.bindings[0].groupNode.shared){const r=new Ux(t.name,[],t.index,t);e.push(r);for(const e of t.bindings)r.bindings.push(e.clone())}else e.push(t)}return e}}class Ix{constructor(e,t,r=null){this.isNodeAttribute=!0,this.name=e,this.type=t,this.node=r}}class Dx{constructor(e,t,r){this.isNodeUniform=!0,this.name=e,this.type=t,this.node=r.getSelf()}get value(){return this.node.value}set value(e){this.node.value=e}get id(){return this.node.id}get groupNode(){return this.node.groupNode}}class Lx{constructor(e,t){this.isNodeVar=!0,this.name=e,this.type=t}}class Vx extends Lx{constructor(e,t){super(e,t),this.needsInterpolation=!1,this.isNodeVarying=!0}}class Ox{constructor(e,t,r=""){this.name=e,this.type=t,this.code=r,Object.defineProperty(this,"isNodeCode",{value:!0})}}let Gx=0;class kx{constructor(e=null){this.id=Gx++,this.nodesData=new WeakMap,this.parent=e}getData(e){let t=this.nodesData.get(e);return void 0===t&&null!==this.parent&&(t=this.parent.getData(e)),t}setData(e,t){this.nodesData.set(e,t)}}class zx extends Us{static get type(){return"StructTypeNode"}constructor(e,t){super(),this.name=e,this.types=t,this.isStructTypeNode=!0}getMemberTypes(){return this.types}}class $x{constructor(e,t){this.name=e,this.value=t,this.boundary=0,this.itemSize=0,this.offset=0}setValue(e){this.value=e}getValue(){return this.value}}class Hx extends $x{constructor(e,t=0){super(e,t),this.isNumberUniform=!0,this.boundary=4,this.itemSize=1}}class Wx extends $x{constructor(e,r=new t){super(e,r),this.isVector2Uniform=!0,this.boundary=8,this.itemSize=2}}class jx extends $x{constructor(e,t=new r){super(e,t),this.isVector3Uniform=!0,this.boundary=16,this.itemSize=3}}class qx extends $x{constructor(e,t=new s){super(e,t),this.isVector4Uniform=!0,this.boundary=16,this.itemSize=4}}class Kx extends $x{constructor(t,r=new e){super(t,r),this.isColorUniform=!0,this.boundary=16,this.itemSize=3}}class Xx extends $x{constructor(e,t=new i){super(e,t),this.isMatrix3Uniform=!0,this.boundary=48,this.itemSize=12}}class Yx extends $x{constructor(e,t=new n){super(e,t),this.isMatrix4Uniform=!0,this.boundary=64,this.itemSize=16}}class Qx extends Hx{constructor(e){super(e.name,e.value),this.nodeUniform=e}getValue(){return this.nodeUniform.value}getType(){return this.nodeUniform.type}}class Zx extends Wx{constructor(e){super(e.name,e.value),this.nodeUniform=e}getValue(){return this.nodeUniform.value}getType(){return this.nodeUniform.type}}class Jx extends jx{constructor(e){super(e.name,e.value),this.nodeUniform=e}getValue(){return this.nodeUniform.value}getType(){return this.nodeUniform.type}}class eT extends qx{constructor(e){super(e.name,e.value),this.nodeUniform=e}getValue(){return this.nodeUniform.value}getType(){return this.nodeUniform.type}}class tT extends Kx{constructor(e){super(e.name,e.value),this.nodeUniform=e}getValue(){return this.nodeUniform.value}getType(){return this.nodeUniform.type}}class rT extends Xx{constructor(e){super(e.name,e.value),this.nodeUniform=e}getValue(){return this.nodeUniform.value}getType(){return this.nodeUniform.type}}class sT extends Yx{constructor(e){super(e.name,e.value),this.nodeUniform=e}getValue(){return this.nodeUniform.value}getType(){return this.nodeUniform.type}}const iT=[.125,.215,.35,.446,.526,.582],nT=20,oT=new xe(-1,1,1,-1,0,1),aT=new Fe(90,1),uT=new e;let lT=null,dT=0,cT=0;const hT=(1+Math.sqrt(5))/2,pT=1/hT,gT=[new r(-hT,pT,0),new r(hT,pT,0),new r(-pT,0,hT),new r(pT,0,hT),new r(0,hT,-pT),new r(0,hT,pT),new r(-1,1,-1),new r(1,1,-1),new r(-1,1,1),new r(1,1,1)],mT=[3,1,5,0,4,2],fT=Hp(xu(),bu("faceIndex")).normalize(),yT=Vi(fT.x,fT.y,fT.z);class bT{constructor(e){this._renderer=e,this._pingPongRenderTarget=null,this._lodMax=0,this._cubeSize=0,this._lodPlanes=[],this._sizeLods=[],this._sigmas=[],this._lodMeshes=[],this._blurMaterial=null,this._cubemapMaterial=null,this._equirectMaterial=null,this._backgroundBox=null}get _hasInitialized(){return this._renderer.hasInitialized()}fromScene(e,t=0,r=.1,s=100,i=null){if(this._setSize(256),!1===this._hasInitialized){console.warn("THREE.PMREMGenerator: .fromScene() called before the backend is initialized. Try using .fromSceneAsync() instead.");const n=i||this._allocateTargets();return this.fromSceneAsync(e,t,r,s,n),n}lT=this._renderer.getRenderTarget(),dT=this._renderer.getActiveCubeFace(),cT=this._renderer.getActiveMipmapLevel();const n=i||this._allocateTargets();return n.depthBuffer=!0,this._sceneToCubeUV(e,r,s,n),t>0&&this._blur(n,0,0,t),this._applyPMREM(n),this._cleanup(n),n}async fromSceneAsync(e,t=0,r=.1,s=100,i=null){return!1===this._hasInitialized&&await this._renderer.init(),this.fromScene(e,t,r,s,i)}fromEquirectangular(e,t=null){if(!1===this._hasInitialized){console.warn("THREE.PMREMGenerator: .fromEquirectangular() called before the backend is initialized. Try using .fromEquirectangularAsync() instead."),this._setSizeFromTexture(e);const r=t||this._allocateTargets();return this.fromEquirectangularAsync(e,r),r}return this._fromTexture(e,t)}async fromEquirectangularAsync(e,t=null){return!1===this._hasInitialized&&await this._renderer.init(),this._fromTexture(e,t)}fromCubemap(e,t=null){if(!1===this._hasInitialized){console.warn("THREE.PMREMGenerator: .fromCubemap() called before the backend is initialized. Try using .fromCubemapAsync() instead."),this._setSizeFromTexture(e);const r=t||this._allocateTargets();return this.fromCubemapAsync(e,t),r}return this._fromTexture(e,t)}async fromCubemapAsync(e,t=null){return!1===this._hasInitialized&&await this._renderer.init(),this._fromTexture(e,t)}async compileCubemapShader(){null===this._cubemapMaterial&&(this._cubemapMaterial=vT(),await this._compileMaterial(this._cubemapMaterial))}async compileEquirectangularShader(){null===this._equirectMaterial&&(this._equirectMaterial=NT(),await this._compileMaterial(this._equirectMaterial))}dispose(){this._dispose(),null!==this._cubemapMaterial&&this._cubemapMaterial.dispose(),null!==this._equirectMaterial&&this._equirectMaterial.dispose(),null!==this._backgroundBox&&(this._backgroundBox.geometry.dispose(),this._backgroundBox.material.dispose())}_setSizeFromTexture(e){e.mapping===_||e.mapping===v?this._setSize(0===e.image.length?16:e.image[0].width||e.image[0].image.width):this._setSize(e.image.width/4)}_setSize(e){this._lodMax=Math.floor(Math.log2(e)),this._cubeSize=Math.pow(2,this._lodMax)}_dispose(){null!==this._blurMaterial&&this._blurMaterial.dispose(),null!==this._pingPongRenderTarget&&this._pingPongRenderTarget.dispose();for(let e=0;e<this._lodPlanes.length;e++)this._lodPlanes[e].dispose()}_cleanup(e){this._renderer.setRenderTarget(lT,dT,cT),e.scissorTest=!1,TT(e,0,0,e.width,e.height)}_fromTexture(e,t){this._setSizeFromTexture(e),lT=this._renderer.getRenderTarget(),dT=this._renderer.getActiveCubeFace(),cT=this._renderer.getActiveMipmapLevel();const r=t||this._allocateTargets();return this._textureToCubeUV(e,r),this._applyPMREM(r),this._cleanup(r),r}_allocateTargets(){const e=3*Math.max(this._cubeSize,112),t=4*this._cubeSize,s={magFilter:$,minFilter:$,generateMipmaps:!1,type:ye,format:Ue,colorSpace:Ae},i=xT(e,t,s);if(null===this._pingPongRenderTarget||this._pingPongRenderTarget.width!==e||this._pingPongRenderTarget.height!==t){null!==this._pingPongRenderTarget&&this._dispose(),this._pingPongRenderTarget=xT(e,t,s);const{_lodMax:i}=this;({sizeLods:this._sizeLods,lodPlanes:this._lodPlanes,sigmas:this._sigmas,lodMeshes:this._lodMeshes}=function(e){const t=[],r=[],s=[],i=[];let n=e;const o=e-4+1+iT.length;for(let a=0;a<o;a++){const o=Math.pow(2,n);r.push(o);let u=1/o;a>e-4?u=iT[a-e+4-1]:0===a&&(u=0),s.push(u);const l=1/(o-2),d=-l,c=1+l,h=[d,d,c,d,c,c,d,d,c,c,d,c],p=6,g=6,m=3,f=2,y=1,b=new Float32Array(m*g*p),x=new Float32Array(f*g*p),T=new Float32Array(y*g*p);for(let e=0;e<p;e++){const t=e%3*2/3-1,r=e>2?0:-1,s=[t,r,0,t+2/3,r,0,t+2/3,r+1,0,t,r,0,t+2/3,r+1,0,t,r+1,0],i=mT[e];b.set(s,m*g*i),x.set(h,f*g*i);const n=[i,i,i,i,i,i];T.set(n,y*g*i)}const _=new Te;_.setAttribute("position",new ve(b,m)),_.setAttribute("uv",new ve(x,f)),_.setAttribute("faceIndex",new ve(T,y)),t.push(_),i.push(new k(_,null)),n>4&&n--}return{lodPlanes:t,sizeLods:r,sigmas:s,lodMeshes:i}}(i)),this._blurMaterial=function(e,t,s){const i=Ml(new Array(nT).fill(0)),n=nn(new r(0,1,0)),o=nn(0),a=Mi(nT),u=nn(0),l=nn(1),d=Au(null),c=nn(0),h=Mi(1/t),p=Mi(1/s),g=Mi(e),m={n:a,latitudinal:u,weights:i,poleAxis:n,outputDirection:yT,dTheta:o,samples:l,envMap:d,mipInt:c,CUBEUV_TEXEL_WIDTH:h,CUBEUV_TEXEL_HEIGHT:p,CUBEUV_MAX_MIP:g},f=_T("blur");return f.uniforms=m,f.fragmentNode=Kp({...m,latitudinal:u.equal(1)}),f}(i,e,t)}return i}async _compileMaterial(e){const t=new k(this._lodPlanes[0],e);await this._renderer.compile(t,oT)}_sceneToCubeUV(e,t,r,s){const i=aT;i.near=t,i.far=r;const n=[1,1,1,1,-1,1],o=[1,-1,1,-1,1,-1],a=this._renderer,u=a.autoClear;a.getClearColor(uT),a.autoClear=!1;let l=this._backgroundBox;if(null===l){const e=new Q({name:"PMREM.Background",side:T,depthWrite:!1,depthTest:!1});l=new k(new G,e)}let d=!1;const c=e.background;c?c.isColor&&(l.material.color.copy(c),e.background=null,d=!0):(l.material.color.copy(uT),d=!0),a.setRenderTarget(s),a.clear(),d&&a.render(l,i);for(let t=0;t<6;t++){const r=t%3;0===r?(i.up.set(0,n[t],0),i.lookAt(o[t],0,0)):1===r?(i.up.set(0,0,n[t]),i.lookAt(0,o[t],0)):(i.up.set(0,n[t],0),i.lookAt(0,0,o[t]));const u=this._cubeSize;TT(s,r*u,t>2?u:0,u,u),a.render(e,i)}a.autoClear=u,e.background=c}_textureToCubeUV(e,t){const r=this._renderer,s=e.mapping===_||e.mapping===v;s?null===this._cubemapMaterial&&(this._cubemapMaterial=vT(e)):null===this._equirectMaterial&&(this._equirectMaterial=NT(e));const i=s?this._cubemapMaterial:this._equirectMaterial;i.fragmentNode.value=e;const n=this._lodMeshes[0];n.material=i;const o=this._cubeSize;TT(t,0,0,3*o,2*o),r.setRenderTarget(t),r.render(n,oT)}_applyPMREM(e){const t=this._renderer,r=t.autoClear;t.autoClear=!1;const s=this._lodPlanes.length;for(let t=1;t<s;t++){const r=Math.sqrt(this._sigmas[t]*this._sigmas[t]-this._sigmas[t-1]*this._sigmas[t-1]),i=gT[(s-t-1)%gT.length];this._blur(e,t-1,t,r,i)}t.autoClear=r}_blur(e,t,r,s,i){const n=this._pingPongRenderTarget;this._halfBlur(e,n,t,r,s,"latitudinal",i),this._halfBlur(n,e,r,r,s,"longitudinal",i)}_halfBlur(e,t,r,s,i,n,o){const a=this._renderer,u=this._blurMaterial;"latitudinal"!==n&&"longitudinal"!==n&&console.error("blur direction must be either latitudinal or longitudinal!");const l=this._lodMeshes[s];l.material=u;const d=u.uniforms,c=this._sizeLods[r]-1,h=isFinite(i)?Math.PI/(2*c):2*Math.PI/39,p=i/h,g=isFinite(i)?1+Math.floor(3*p):nT;g>nT&&console.warn(`sigmaRadians, ${i}, is too large and will clip, as it requested ${g} samples when the maximum is set to 20`);const m=[];let f=0;for(let e=0;e<nT;++e){const t=e/p,r=Math.exp(-t*t/2);m.push(r),0===e?f+=r:e<g&&(f+=2*r)}for(let e=0;e<m.length;e++)m[e]=m[e]/f;e.texture.frame=(e.texture.frame||0)+1,d.envMap.value=e.texture,d.samples.value=g,d.weights.array=m,d.latitudinal.value="latitudinal"===n?1:0,o&&(d.poleAxis.value=o);const{_lodMax:y}=this;d.dTheta.value=h,d.mipInt.value=y-r;const b=this._sizeLods[s];TT(t,3*b*(s>y-4?s-y+4:0),4*(this._cubeSize-b),3*b,2*b),a.setRenderTarget(t),a.render(l,oT)}}function xT(e,t,r){const s=new ge(e,t,r);return s.texture.mapping=Be,s.texture.name="PMREM.cubeUv",s.texture.isPMREMTexture=!0,s.scissorTest=!0,s}function TT(e,t,r,s,i){e.viewport.set(t,r,s,i),e.scissor.set(t,r,s,i)}function _T(e){const t=new rh;return t.depthTest=!1,t.depthWrite=!1,t.blending=L,t.name=`PMREM_${e}`,t}function vT(e){const t=_T("cubemap");return t.fragmentNode=Al(e,yT),t}function NT(e){const t=_T("equirect");return t.fragmentNode=Au(e,bh(yT),0),t}const ST=new WeakMap,AT=new Map([[Int8Array,"int"],[Int16Array,"int"],[Int32Array,"int"],[Uint8Array,"uint"],[Uint16Array,"uint"],[Uint32Array,"uint"],[Float32Array,"float"]]),RT=e=>/e/g.test(e)?String(e).replace(/\+/g,""):(e=Number(e))+(e%1?"":".0");class CT{constructor(e,t,r){this.object=e,this.material=e&&e.material||null,this.geometry=e&&e.geometry||null,this.renderer=t,this.parser=r,this.scene=null,this.camera=null,this.nodes=[],this.sequentialNodes=[],this.updateNodes=[],this.updateBeforeNodes=[],this.updateAfterNodes=[],this.hashNodes={},this.monitor=null,this.lightsNode=null,this.environmentNode=null,this.fogNode=null,this.clippingContext=null,this.vertexShader=null,this.fragmentShader=null,this.computeShader=null,this.flowNodes={vertex:[],fragment:[],compute:[]},this.flowCode={vertex:"",fragment:"",compute:""},this.uniforms={vertex:[],fragment:[],compute:[],index:0},this.structs={vertex:[],fragment:[],compute:[],index:0},this.bindings={vertex:{},fragment:{},compute:{}},this.bindingsIndexes={},this.bindGroups=null,this.attributes=[],this.bufferAttributes=[],this.varyings=[],this.codes={},this.vars={},this.flow={code:""},this.chaining=[],this.stack=mm(),this.stacks=[],this.tab="\t",this.currentFunctionNode=null,this.context={material:this.material},this.cache=new kx,this.globalCache=this.cache,this.flowsData=new WeakMap,this.shaderStage=null,this.buildStage=null,this.useComparisonMethod=!1}getBindGroupsCache(){let e=ST.get(this.renderer);return void 0===e&&(e=new Fg,ST.set(this.renderer,e)),e}createRenderTarget(e,t,r){return new ge(e,t,r)}createCubeRenderTarget(e,t){return new xh(e,t)}createPMREMGenerator(){return new bT(this.renderer)}includes(e){return this.nodes.includes(e)}_getBindGroup(e,t){const r=this.getBindGroupsCache(),s=[];let i,n=!0;for(const e of t)s.push(e),n=n&&!0!==e.groupNode.shared;return n?(i=r.get(s),void 0===i&&(i=new Ux(e,s,this.bindingsIndexes[e].group,s),r.set(s,i))):i=new Ux(e,s,this.bindingsIndexes[e].group,s),i}getBindGroupArray(e,t){const r=this.bindings[t];let s=r[e];return void 0===s&&(void 0===this.bindingsIndexes[e]&&(this.bindingsIndexes[e]={binding:0,group:Object.keys(this.bindingsIndexes).length}),r[e]=s=[]),s}getBindings(){let e=this.bindGroups;if(null===e){const t={},r=this.bindings;for(const e of Ms)for(const s in r[e]){const i=r[e][s];(t[s]||(t[s]=[])).push(...i)}e=[];for(const r in t){const s=t[r],i=this._getBindGroup(r,s);e.push(i)}this.bindGroups=e}return e}sortBindingGroups(){const e=this.getBindings();e.sort(((e,t)=>e.bindings[0].groupNode.order-t.bindings[0].groupNode.order));for(let t=0;t<e.length;t++){const r=e[t];this.bindingsIndexes[r.name].group=t,r.index=t}}setHashNode(e,t){this.hashNodes[t]=e}addNode(e){!1===this.nodes.includes(e)&&(this.nodes.push(e),this.setHashNode(e,e.getHash(this)))}addSequentialNode(e){!1===this.sequentialNodes.includes(e)&&this.sequentialNodes.push(e)}buildUpdateNodes(){for(const e of this.nodes){e.getUpdateType()!==As.NONE&&this.updateNodes.push(e.getSelf())}for(const e of this.sequentialNodes){const t=e.getUpdateBeforeType(),r=e.getUpdateAfterType();t!==As.NONE&&this.updateBeforeNodes.push(e.getSelf()),r!==As.NONE&&this.updateAfterNodes.push(e.getSelf())}}get currentNode(){return this.chaining[this.chaining.length-1]}isFilteredTexture(e){return e.magFilter===$||e.magFilter===Pe||e.magFilter===Ie||e.magFilter===M||e.minFilter===$||e.minFilter===Pe||e.minFilter===Ie||e.minFilter===M}addChain(e){this.chaining.push(e)}removeChain(e){if(this.chaining.pop()!==e)throw new Error("NodeBuilder: Invalid node chaining!")}getMethod(e){return e}getNodeFromHash(e){return this.hashNodes[e]}addFlow(e,t){return this.flowNodes[e].push(t),t}setContext(e){this.context=e}getContext(){return this.context}getSharedContext(){return this.context,this.context}setCache(e){this.cache=e}getCache(){return this.cache}getCacheFromNode(e,t=!0){const r=this.getDataFromNode(e);return void 0===r.cache&&(r.cache=new kx(t?this.getCache():null)),r.cache}isAvailable(){return!1}getVertexIndex(){console.warn("Abstract function.")}getInstanceIndex(){console.warn("Abstract function.")}getDrawIndex(){console.warn("Abstract function.")}getFrontFacing(){console.warn("Abstract function.")}getFragCoord(){console.warn("Abstract function.")}isFlipY(){return!1}increaseUsage(e){const t=this.getDataFromNode(e);return t.usageCount=void 0===t.usageCount?1:t.usageCount+1,t.usageCount}generateTexture(){console.warn("Abstract function.")}generateTextureLod(){console.warn("Abstract function.")}generateConst(i,n=null){if(null===n&&("float"===i||"int"===i||"uint"===i?n=0:"bool"===i?n=!1:"color"===i?n=new e:"vec2"===i?n=new t:"vec3"===i?n=new r:"vec4"===i&&(n=new s)),"float"===i)return RT(n);if("int"===i)return`${Math.round(n)}`;if("uint"===i)return n>=0?`${Math.round(n)}u`:"0u";if("bool"===i)return n?"true":"false";if("color"===i)return`${this.getType("vec3")}( ${RT(n.r)}, ${RT(n.g)}, ${RT(n.b)} )`;const o=this.getTypeLength(i),a=this.getComponentType(i),u=e=>this.generateConst(a,e);if(2===o)return`${this.getType(i)}( ${u(n.x)}, ${u(n.y)} )`;if(3===o)return`${this.getType(i)}( ${u(n.x)}, ${u(n.y)}, ${u(n.z)} )`;if(4===o)return`${this.getType(i)}( ${u(n.x)}, ${u(n.y)}, ${u(n.z)}, ${u(n.w)} )`;if(o>4&&n&&(n.isMatrix3||n.isMatrix4))return`${this.getType(i)}( ${n.elements.map(u).join(", ")} )`;if(o>4)return`${this.getType(i)}()`;throw new Error(`NodeBuilder: Type '${i}' not found in generate constant attempt.`)}getType(e){return"color"===e?"vec3":e}hasGeometryAttribute(e){return this.geometry&&void 0!==this.geometry.getAttribute(e)}getAttribute(e,t){const r=this.attributes;for(const t of r)if(t.name===e)return t;const s=new Ix(e,t);return r.push(s),s}getPropertyName(e){return e.name}isVector(e){return/vec\d/.test(e)}isMatrix(e){return/mat\d/.test(e)}isReference(e){return"void"===e||"property"===e||"sampler"===e||"texture"===e||"cubeTexture"===e||"storageTexture"===e||"depthTexture"===e||"texture3D"===e}needsToWorkingColorSpace(){return!1}getComponentTypeFromTexture(e){const t=e.type;if(e.isDataTexture){if(t===x)return"int";if(t===b)return"uint"}return"float"}getElementType(e){return"mat2"===e?"vec2":"mat3"===e?"vec3":"mat4"===e?"vec4":this.getComponentType(e)}getComponentType(e){if("float"===(e=this.getVectorType(e))||"bool"===e||"int"===e||"uint"===e)return e;const t=/(b|i|u|)(vec|mat)([2-4])/.exec(e);return null===t?null:"b"===t[1]?"bool":"i"===t[1]?"int":"u"===t[1]?"uint":"float"}getVectorType(e){return"color"===e?"vec3":"texture"===e||"cubeTexture"===e||"storageTexture"===e||"texture3D"===e?"vec4":e}getTypeFromLength(e,t="float"){if(1===e)return t;const r=ms(e);return("float"===t?"":t[0])+r}getTypeFromArray(e){return AT.get(e.constructor)}getTypeFromAttribute(e){let t=e;e.isInterleavedBufferAttribute&&(t=e.data);const r=t.array,s=e.itemSize,i=e.normalized;let n;return e instanceof De||!0===i||(n=this.getTypeFromArray(r)),this.getTypeFromLength(s,n)}getTypeLength(e){const t=this.getVectorType(e),r=/vec([2-4])/.exec(t);return null!==r?Number(r[1]):"float"===t||"bool"===t||"int"===t||"uint"===t?1:!0===/mat2/.test(e)?4:!0===/mat3/.test(e)?9:!0===/mat4/.test(e)?16:0}getVectorFromMatrix(e){return e.replace("mat","vec")}changeComponentType(e,t){return this.getTypeFromLength(this.getTypeLength(e),t)}getIntegerType(e){const t=this.getComponentType(e);return"int"===t||"uint"===t?e:this.changeComponentType(e,"int")}addStack(){return this.stack=mm(this.stack),this.stacks.push(Ri()||this.stack),Ai(this.stack),this.stack}removeStack(){const e=this.stack;return this.stack=e.parent,Ai(this.stacks.pop()),e}getDataFromNode(e,t=this.shaderStage,r=null){let s=(r=null===r?e.isGlobal(this)?this.globalCache:this.cache:r).getData(e);return void 0===s&&(s={},r.setData(e,s)),void 0===s[t]&&(s[t]={}),s[t]}getNodeProperties(e,t="any"){const r=this.getDataFromNode(e,t);return r.properties||(r.properties={outputNode:null})}getBufferAttributeFromNode(e,t){const r=this.getDataFromNode(e);let s=r.bufferAttribute;if(void 0===s){const i=this.uniforms.index++;s=new Ix("nodeAttribute"+i,t,e),this.bufferAttributes.push(s),r.bufferAttribute=s}return s}getStructTypeFromNode(e,t,r=this.shaderStage){const s=this.getDataFromNode(e,r);let i=s.structType;if(void 0===i){const e=this.structs.index++;i=new zx("StructType"+e,t),this.structs[r].push(i),s.structType=i}return i}getUniformFromNode(e,t,r=this.shaderStage,s=null){const i=this.getDataFromNode(e,r,this.globalCache);let n=i.uniform;if(void 0===n){const o=this.uniforms.index++;n=new Dx(s||"nodeUniform"+o,t,e),this.uniforms[r].push(n),i.uniform=n}return n}getVarFromNode(e,t=null,r=e.getNodeType(this),s=this.shaderStage){const i=this.getDataFromNode(e,s);let n=i.variable;if(void 0===n){const e=this.vars[s]||(this.vars[s]=[]);null===t&&(t="nodeVar"+e.length),n=new Lx(t,r),e.push(n),i.variable=n}return n}getVaryingFromNode(e,t=null,r=e.getNodeType(this)){const s=this.getDataFromNode(e,"any");let i=s.varying;if(void 0===i){const e=this.varyings,n=e.length;null===t&&(t="nodeVarying"+n),i=new Vx(t,r),e.push(i),s.varying=i}return i}getCodeFromNode(e,t,r=this.shaderStage){const s=this.getDataFromNode(e);let i=s.code;if(void 0===i){const e=this.codes[r]||(this.codes[r]=[]),n=e.length;i=new Ox("nodeCode"+n,t),e.push(i),s.code=i}return i}addFlowCodeHierarchy(e,t){const{flowCodes:r,flowCodeBlock:s}=this.getDataFromNode(e);let i=!0,n=t;for(;n;){if(!0===s.get(n)){i=!1;break}n=this.getDataFromNode(n).parentNodeBlock}if(i)for(const e of r)this.addLineFlowCode(e)}addLineFlowCodeBlock(e,t,r){const s=this.getDataFromNode(e),i=s.flowCodes||(s.flowCodes=[]),n=s.flowCodeBlock||(s.flowCodeBlock=new WeakMap);i.push(t),n.set(r,!0)}addLineFlowCode(e,t=null){return""===e||(null!==t&&this.context.nodeBlock&&this.addLineFlowCodeBlock(t,e,this.context.nodeBlock),e=this.tab+e,/;\s*$/.test(e)||(e+=";\n"),this.flow.code+=e),this}addFlowCode(e){return this.flow.code+=e,this}addFlowTab(){return this.tab+="\t",this}removeFlowTab(){return this.tab=this.tab.slice(0,-1),this}getFlowData(e){return this.flowsData.get(e)}flowNode(e){const t=e.getNodeType(this),r=this.flowChildNode(e,t);return this.flowsData.set(e,r),r}buildFunctionNode(e){const t=new hy,r=this.currentFunctionNode;return this.currentFunctionNode=t,t.code=this.buildFunctionCode(e),this.currentFunctionNode=r,t}flowShaderNode(e){const t=e.layout,r={[Symbol.iterator](){let e=0;const t=Object.values(this);return{next:()=>({value:t[e],done:e++>=t.length})}}};for(const e of t.inputs)r[e.name]=new pm(e.type,e.name);e.layout=null;const s=e.call(r),i=this.flowStagesNode(s,t.type);return e.layout=t,i}flowStagesNode(e,t=null){const r=this.flow,s=this.vars,i=this.cache,n=this.buildStage,o=this.stack,a={code:""};this.flow=a,this.vars={},this.cache=new kx,this.stack=mm();for(const r of ws)this.setBuildStage(r),a.result=e.build(this,t);return a.vars=this.getVars(this.shaderStage),this.flow=r,this.vars=s,this.cache=i,this.stack=o,this.setBuildStage(n),a}getFunctionOperator(){return null}flowChildNode(e,t=null){const r=this.flow,s={code:""};return this.flow=s,s.result=e.build(this,t),this.flow=r,s}flowNodeFromShaderStage(e,t,r=null,s=null){const i=this.shaderStage;this.setShaderStage(e);const n=this.flowChildNode(t,r);return null!==s&&(n.code+=`${this.tab+s} = ${n.result};\n`),this.flowCode[e]=this.flowCode[e]+n.code,this.setShaderStage(i),n}getAttributesArray(){return this.attributes.concat(this.bufferAttributes)}getAttributes(){console.warn("Abstract function.")}getVaryings(){console.warn("Abstract function.")}getVar(e,t){return`${this.getType(e)} ${t}`}getVars(e){let t="";const r=this.vars[e];if(void 0!==r)for(const e of r)t+=`${this.getVar(e.type,e.name)}; `;return t}getUniforms(){console.warn("Abstract function.")}getCodes(e){const t=this.codes[e];let r="";if(void 0!==t)for(const e of t)r+=e.code+"\n";return r}getHash(){return this.vertexShader+this.fragmentShader+this.computeShader}setShaderStage(e){this.shaderStage=e}getShaderStage(){return this.shaderStage}setBuildStage(e){this.buildStage=e}getBuildStage(){return this.buildStage}buildCode(){console.warn("Abstract function.")}build(){const{object:e,material:t,renderer:r}=this;if(null!==t){let e=r.library.fromMaterial(t);null===e&&(console.error(`NodeMaterial: Material "${t.type}" is not compatible.`),e=new rh),e.build(this)}else this.addFlow("compute",e);for(const e of ws){this.setBuildStage(e),this.context.vertex&&this.context.vertex.isNode&&this.flowNodeFromShaderStage("vertex",this.context.vertex);for(const t of Ms){this.setShaderStage(t);const r=this.flowNodes[t];for(const t of r)"generate"===e?this.flowNode(t):t.build(this)}}return this.setBuildStage(null),this.setShaderStage(null),this.buildCode(),this.buildUpdateNodes(),this}getNodeUniform(e,t){if("float"===t||"int"===t||"uint"===t)return new Qx(e);if("vec2"===t||"ivec2"===t||"uvec2"===t)return new Zx(e);if("vec3"===t||"ivec3"===t||"uvec3"===t)return new Jx(e);if("vec4"===t||"ivec4"===t||"uvec4"===t)return new eT(e);if("color"===t)return new tT(e);if("mat3"===t)return new rT(e);if("mat4"===t)return new sT(e);throw new Error(`Uniform "${t}" not declared.`)}format(e,t,r){if((t=this.getVectorType(t))===(r=this.getVectorType(r))||null===r||this.isReference(r))return e;const s=this.getTypeLength(t),i=this.getTypeLength(r);return 16===s&&9===i?`${this.getType(r)}(${e}[0].xyz, ${e}[1].xyz, ${e}[2].xyz)`:9===s&&4===i?`${this.getType(r)}(${e}[0].xy, ${e}[1].xy)`:s>4||i>4||0===i?e:s===i?`${this.getType(r)}( ${e} )`:s>i?this.format(`${e}.${"xyz".slice(0,i)}`,this.getTypeFromLength(i,this.getComponentType(t)),r):4===i&&s>1?`${this.getType(r)}( ${this.format(e,t,"vec3")}, 1.0 )`:2===s?`${this.getType(r)}( ${this.format(e,t,"vec2")}, 0.0 )`:(1===s&&i>1&&t!==this.getComponentType(r)&&(e=`${this.getType(this.getComponentType(r))}( ${e} )`),`${this.getType(r)}( ${e} )`)}getSignature(){return`// Three.js r${Le} - Node System\n`}createNodeMaterial(e="NodeMaterial"){throw new Error(`THREE.NodeBuilder: createNodeMaterial() was deprecated. Use new ${e}() instead.`)}}class ET{constructor(){this.time=0,this.deltaTime=0,this.frameId=0,this.renderId=0,this.updateMap=new WeakMap,this.updateBeforeMap=new WeakMap,this.updateAfterMap=new WeakMap,this.renderer=null,this.material=null,this.camera=null,this.object=null,this.scene=null}_getMaps(e,t){let r=e.get(t);return void 0===r&&(r={renderMap:new WeakMap,frameMap:new WeakMap},e.set(t,r)),r}updateBeforeNode(e){const t=e.getUpdateBeforeType(),r=e.updateReference(this);if(t===As.FRAME){const{frameMap:t}=this._getMaps(this.updateBeforeMap,r);t.get(r)!==this.frameId&&!1!==e.updateBefore(this)&&t.set(r,this.frameId)}else if(t===As.RENDER){const{renderMap:t}=this._getMaps(this.updateBeforeMap,r);t.get(r)!==this.renderId&&!1!==e.updateBefore(this)&&t.set(r,this.renderId)}else t===As.OBJECT&&e.updateBefore(this)}updateAfterNode(e){const t=e.getUpdateAfterType(),r=e.updateReference(this);if(t===As.FRAME){const{frameMap:t}=this._getMaps(this.updateAfterMap,r);t.get(r)!==this.frameId&&!1!==e.updateAfter(this)&&t.set(r,this.frameId)}else if(t===As.RENDER){const{renderMap:t}=this._getMaps(this.updateAfterMap,r);t.get(r)!==this.renderId&&!1!==e.updateAfter(this)&&t.set(r,this.renderId)}else t===As.OBJECT&&e.updateAfter(this)}updateNode(e){const t=e.getUpdateType(),r=e.updateReference(this);if(t===As.FRAME){const{frameMap:t}=this._getMaps(this.updateMap,r);t.get(r)!==this.frameId&&!1!==e.update(this)&&t.set(r,this.frameId)}else if(t===As.RENDER){const{renderMap:t}=this._getMaps(this.updateMap,r);t.get(r)!==this.renderId&&!1!==e.update(this)&&t.set(r,this.renderId)}else t===As.OBJECT&&e.update(this)}update(){this.frameId++,void 0===this.lastTime&&(this.lastTime=performance.now()),this.deltaTime=(performance.now()-this.lastTime)/1e3,this.lastTime=performance.now(),this.time+=this.deltaTime}}class wT{constructor(e,t,r=null,s="",i=!1){this.type=e,this.name=t,this.count=r,this.qualifier=s,this.isConst=i}}wT.isNodeFunctionInput=!0;class MT extends Nb{static get type(){return"DirectionalLightNode"}constructor(e=null){super(e)}setup(e){super.setup(e);const t=e.context.lightingModel,r=this.colorNode,s=Ky(this.light),i=e.context.reflectedLight;t.direct({lightDirection:s,lightColor:r,reflectedLight:i},e.stack,e)}}const BT=new n,FT=new n;let UT=null;class PT extends Nb{static get type(){return"RectAreaLightNode"}constructor(e=null){super(e),this.halfHeight=nn(new r).setGroup(tn),this.halfWidth=nn(new r).setGroup(tn),this.updateType=As.RENDER}update(e){super.update(e);const{light:t}=this,r=e.camera.matrixWorldInverse;FT.identity(),BT.copy(t.matrixWorld),BT.premultiply(r),FT.extractRotation(BT),this.halfWidth.value.set(.5*t.width,0,0),this.halfHeight.value.set(0,.5*t.height,0),this.halfWidth.value.applyMatrix4(FT),this.halfHeight.value.applyMatrix4(FT)}setup(e){let t,r;super.setup(e),e.isAvailable("float32Filterable")?(t=Au(UT.LTC_FLOAT_1),r=Au(UT.LTC_FLOAT_2)):(t=Au(UT.LTC_HALF_1),r=Au(UT.LTC_HALF_2));const{colorNode:s,light:i}=this,n=e.context.lightingModel,o=qy(i),a=e.context.reflectedLight;n.directRectArea({lightColor:s,lightPosition:o,halfWidth:this.halfWidth,halfHeight:this.halfHeight,reflectedLight:a,ltc_1:t,ltc_2:r},e.stack,e)}static setLTC(e){UT=e}}class IT extends Nb{static get type(){return"SpotLightNode"}constructor(e=null){super(e),this.coneCosNode=nn(0).setGroup(tn),this.penumbraCosNode=nn(0).setGroup(tn),this.cutoffDistanceNode=nn(0).setGroup(tn),this.decayExponentNode=nn(0).setGroup(tn)}update(e){super.update(e);const{light:t}=this;this.coneCosNode.value=Math.cos(t.angle),this.penumbraCosNode.value=Math.cos(t.angle*(1-t.penumbra)),this.cutoffDistanceNode.value=t.distance,this.decayExponentNode.value=t.decay}getSpotAttenuation(e){const{coneCosNode:t,penumbraCosNode:r}=this;return fa(t,r,e)}setup(e){super.setup(e);const t=e.context.lightingModel,{colorNode:r,cutoffDistanceNode:s,decayExponentNode:i,light:n}=this,o=qy(n).sub(il),a=o.normalize(),u=a.dot(Ky(n)),l=this.getSpotAttenuation(u),d=o.length(),c=Sb({lightDistance:d,cutoffDistance:s,decayExponent:i});let h=r.mul(l).mul(c);if(n.map){const e=Hy(n),t=Au(n.map,e.xy).onRenderUpdate((()=>n.map));h=e.mul(2).sub(1).abs().lessThan(1).all().select(h.mul(t),h)}const p=e.context.reflectedLight;t.direct({lightDirection:a,lightColor:h,reflectedLight:p},e.stack,e)}}class DT extends IT{static get type(){return"IESSpotLightNode"}getSpotAttenuation(e){const t=this.light.iesMap;let r=null;if(t&&!0===t.isTexture){const s=e.acos().mul(1/Math.PI);r=Au(t,Pi(s,0),0).r}else r=super.getSpotAttenuation(e);return r}}class LT extends Nb{static get type(){return"AmbientLightNode"}constructor(e=null){super(e)}setup({context:e}){e.irradiance.addAssign(this.colorNode)}}class VT extends Nb{static get type(){return"HemisphereLightNode"}constructor(t=null){super(t),this.lightPositionNode=Wy(t),this.lightDirectionNode=this.lightPositionNode.normalize(),this.groundColorNode=nn(new e).setGroup(tn)}update(e){const{light:t}=this;super.update(e),this.lightPositionNode.object3d=t,this.groundColorNode.value.copy(t.groundColor).multiplyScalar(t.intensity)}setup(e){const{colorNode:t,groundColorNode:r,lightDirectionNode:s}=this,i=hl.dot(s).mul(.5).add(.5),n=ha(r,t,i);e.context.irradiance.addAssign(n)}}class OT extends Nb{static get type(){return"LightProbeNode"}constructor(e=null){super(e);const t=[];for(let e=0;e<9;e++)t.push(new r);this.lightProbe=Ml(t)}update(e){const{light:t}=this;super.update(e);for(let e=0;e<9;e++)this.lightProbe.array[e].copy(t.sh.coefficients[e]).multiplyScalar(t.intensity)}setup(e){const t=Ex(pl,this.lightProbe);e.context.irradiance.addAssign(t)}}class GT{parseFunction(){console.warn("Abstract function.")}}class kT{constructor(e,t,r="",s=""){this.type=e,this.inputs=t,this.name=r,this.precision=s}getCode(){console.warn("Abstract function.")}}kT.isNodeFunction=!0;const zT=/^\s*(highp|mediump|lowp)?\s*([a-z_0-9]+)\s*([a-z_0-9]+)?\s*\(([\s\S]*?)\)/i,$T=/[a-z_0-9]+/gi,HT="#pragma main";class WT extends kT{constructor(e){const{type:t,inputs:r,name:s,precision:i,inputsCode:n,blockCode:o,headerCode:a}=(e=>{const t=(e=e.trim()).indexOf(HT),r=-1!==t?e.slice(t+12):e,s=r.match(zT);if(null!==s&&5===s.length){const i=s[4],n=[];let o=null;for(;null!==(o=$T.exec(i));)n.push(o);const a=[];let u=0;for(;u<n.length;){const e="const"===n[u][0];!0===e&&u++;let t=n[u][0];"in"===t||"out"===t||"inout"===t?u++:t="";const r=n[u++][0];let s=Number.parseInt(n[u][0]);!1===Number.isNaN(s)?u++:s=null;const i=n[u++][0];a.push(new wT(r,i,s,t,e))}const l=r.substring(s[0].length),d=void 0!==s[3]?s[3]:"";return{type:s[2],inputs:a,name:d,precision:void 0!==s[1]?s[1]:"",inputsCode:i,blockCode:l,headerCode:-1!==t?e.slice(0,t):""}}throw new Error("FunctionNode: Function is not a GLSL code.")})(e);super(t,r,s,i),this.inputsCode=n,this.blockCode=o,this.headerCode=a}getCode(e=this.name){let t;const r=this.blockCode;if(""!==r){const{type:s,inputsCode:i,headerCode:n,precision:o}=this;let a=`${s} ${e} ( ${i.trim()} )`;""!==o&&(a=`${o} ${a}`),t=n+a+r}else t="";return t}}class jT extends GT{parseFunction(e){return new WT(e)}}const qT=new WeakMap;class KT extends Lg{constructor(e,t){super(),this.renderer=e,this.backend=t,this.nodeFrame=new ET,this.nodeBuilderCache=new Map,this.callHashCache=new Fg,this.groupsData=new Fg,this.cacheLib={}}updateGroup(e){const t=e.groupNode,r=t.name;if(r===rn.name)return!0;if(r===tn.name){const t=this.get(e),r=this.nodeFrame.renderId;return t.renderId!==r&&(t.renderId=r,!0)}if(r===en.name){const t=this.get(e),r=this.nodeFrame.frameId;return t.frameId!==r&&(t.frameId=r,!0)}const s=[t,e];let i=this.groupsData.get(s);return void 0===i&&this.groupsData.set(s,i={}),i.version!==t.version&&(i.version=t.version,!0)}getForRenderCacheKey(e){return e.initialCacheKey}getForRender(e){const t=this.get(e);let r=t.nodeBuilderState;if(void 0===r){const{nodeBuilderCache:s}=this,i=this.getForRenderCacheKey(e);if(r=s.get(i),void 0===r){const t=this.backend.createNodeBuilder(e.object,this.renderer);t.scene=e.scene,t.material=e.material,t.camera=e.camera,t.context.material=e.material,t.lightsNode=e.lightsNode,t.environmentNode=this.getEnvironmentNode(e.scene),t.fogNode=this.getFogNode(e.scene),t.clippingContext=e.clippingContext,t.build(),r=this._createNodeBuilderState(t),s.set(i,r)}r.usedTimes++,t.nodeBuilderState=r}return r}delete(e){if(e.isRenderObject){const t=this.get(e).nodeBuilderState;t.usedTimes--,0===t.usedTimes&&this.nodeBuilderCache.delete(this.getForRenderCacheKey(e))}return super.delete(e)}getForCompute(e){const t=this.get(e);let r=t.nodeBuilderState;if(void 0===r){const s=this.backend.createNodeBuilder(e,this.renderer);s.build(),r=this._createNodeBuilderState(s),t.nodeBuilderState=r}return r}_createNodeBuilderState(e){return new Px(e.vertexShader,e.fragmentShader,e.computeShader,e.getAttributesArray(),e.getBindings(),e.updateNodes,e.updateBeforeNodes,e.updateAfterNodes,e.monitor,e.transforms)}getEnvironmentNode(e){this.updateEnvironment(e);let t=null;if(e.environmentNode&&e.environmentNode.isNode)t=e.environmentNode;else{const r=this.get(e);r.environmentNode&&(t=r.environmentNode)}return t}getBackgroundNode(e){this.updateBackground(e);let t=null;if(e.backgroundNode&&e.backgroundNode.isNode)t=e.backgroundNode;else{const r=this.get(e);r.backgroundNode&&(t=r.backgroundNode)}return t}getFogNode(e){return this.updateFog(e),e.fogNode||this.get(e).fogNode||null}getCacheKey(e,t){const r=[e,t],s=this.renderer.info.calls;let i=this.callHashCache.get(r);if(void 0===i||i.callId!==s){const n=this.getEnvironmentNode(e),o=this.getFogNode(e),a=[];t&&a.push(t.getCacheKey(!0)),n&&a.push(n.getCacheKey()),o&&a.push(o.getCacheKey()),a.push(this.renderer.shadowMap.enabled?1:0),i={callId:s,cacheKey:ls(a)},this.callHashCache.set(r,i)}return i.cacheKey}get isToneMappingState(){return!this.renderer.getRenderTarget()}updateBackground(e){const t=this.get(e),r=e.background;if(r){const s=0===e.backgroundBlurriness&&t.backgroundBlurriness>0||e.backgroundBlurriness>0&&0===t.backgroundBlurriness;if(t.background!==r||s){const i=this.getCacheNode("background",r,(()=>{if(!0===r.isCubeTexture||r.mapping===j||r.mapping===q||r.mapping===Be){if(e.backgroundBlurriness>0||r.mapping===Be)return Jp(r);{let e;return e=!0===r.isCubeTexture?Al(r):Au(r),Sh(e)}}if(!0===r.isTexture)return Au(r,Ac.flipY()).setUpdateMatrix(!0);!0!==r.isColor&&console.error("WebGPUNodes: Unsupported background configuration.",r)}),s);t.backgroundNode=i,t.background=r,t.backgroundBlurriness=e.backgroundBlurriness}}else t.backgroundNode&&(delete t.backgroundNode,delete t.background)}getCacheNode(e,t,r,s=!1){const i=this.cacheLib[e]||(this.cacheLib[e]=new WeakMap);let n=i.get(t);return(void 0===n||s)&&(n=r(),i.set(t,n)),n}updateFog(e){const t=this.get(e),r=e.fog;if(r){if(t.fog!==r){const e=this.getCacheNode("fog",r,(()=>{if(r.isFogExp2){const e=Ul("color","color",r).setGroup(tn),t=Ul("density","float",r).setGroup(tn);return Sy(e,Ny(t))}if(r.isFog){const e=Ul("color","color",r).setGroup(tn),t=Ul("near","float",r).setGroup(tn),s=Ul("far","float",r).setGroup(tn);return Sy(e,vy(t,s))}console.error("THREE.Renderer: Unsupported fog configuration.",r)}));t.fogNode=e,t.fog=r}}else delete t.fogNode,delete t.fog}updateEnvironment(e){const t=this.get(e),r=e.environment;if(r){if(t.environment!==r){const e=this.getCacheNode("environment",r,(()=>!0===r.isCubeTexture?Al(r):!0===r.isTexture?Au(r):void console.error("Nodes: Unsupported environment configuration.",r)));t.environmentNode=e,t.environment=r}}else t.environmentNode&&(delete t.environmentNode,delete t.environment)}getNodeFrame(e=this.renderer,t=null,r=null,s=null,i=null){const n=this.nodeFrame;return n.renderer=e,n.scene=t,n.object=r,n.camera=s,n.material=i,n}getNodeFrameForRender(e){return this.getNodeFrame(e.renderer,e.scene,e.object,e.camera,e.material)}getOutputCacheKey(){const e=this.renderer;return e.toneMapping+","+e.currentColorSpace}hasOutputChange(e){return qT.get(e)!==this.getOutputCacheKey()}getOutputNode(e){const t=this.renderer,r=this.getOutputCacheKey(),s=Au(e,Ac).renderOutput(t.toneMapping,t.currentColorSpace);return qT.set(e,r),s}updateBefore(e){const t=e.getNodeBuilderState();for(const r of t.updateBeforeNodes)this.getNodeFrameForRender(e).updateBeforeNode(r)}updateAfter(e){const t=e.getNodeBuilderState();for(const r of t.updateAfterNodes)this.getNodeFrameForRender(e).updateAfterNode(r)}updateForCompute(e){const t=this.getNodeFrame(),r=this.getForCompute(e);for(const e of r.updateNodes)t.updateNode(e)}updateForRender(e){const t=this.getNodeFrameForRender(e),r=e.getNodeBuilderState();for(const e of r.updateNodes)t.updateNode(e)}needsRefresh(e){const t=this.getNodeFrameForRender(e);return e.getMonitor().needsRefresh(e,t)}dispose(){super.dispose(),this.nodeFrame=new ET,this.nodeBuilderCache=new Map,this.cacheLib={}}}const XT=new me;class YT{constructor(e=null){this.version=0,this.clipIntersection=null,this.cacheKey="",this.shadowPass=!1,this.viewNormalMatrix=new i,this.clippingGroupContexts=new WeakMap,this.intersectionPlanes=[],this.unionPlanes=[],this.parentVersion=null,null!==e&&(this.viewNormalMatrix=e.viewNormalMatrix,this.clippingGroupContexts=e.clippingGroupContexts,this.shadowPass=e.shadowPass,this.viewMatrix=e.viewMatrix)}projectPlanes(e,t,r){const s=e.length;for(let i=0;i<s;i++){XT.copy(e[i]).applyMatrix4(this.viewMatrix,this.viewNormalMatrix);const s=t[r+i],n=XT.normal;s.x=-n.x,s.y=-n.y,s.z=-n.z,s.w=XT.constant}}updateGlobal(e,t){this.shadowPass=null!==e.overrideMaterial&&e.overrideMaterial.isShadowNodeMaterial,this.viewMatrix=t.matrixWorldInverse,this.viewNormalMatrix.getNormalMatrix(this.viewMatrix)}update(e,t){let r=!1;e.version!==this.parentVersion&&(this.intersectionPlanes=Array.from(e.intersectionPlanes),this.unionPlanes=Array.from(e.unionPlanes),this.parentVersion=e.version),this.clipIntersection!==t.clipIntersection&&(this.clipIntersection=t.clipIntersection,this.clipIntersection?this.unionPlanes.length=e.unionPlanes.length:this.intersectionPlanes.length=e.intersectionPlanes.length);const i=t.clippingPlanes,n=i.length;let o,a;if(this.clipIntersection?(o=this.intersectionPlanes,a=e.intersectionPlanes.length):(o=this.unionPlanes,a=e.unionPlanes.length),o.length!==a+n){o.length=a+n;for(let e=0;e<n;e++)o[a+e]=new s;r=!0}this.projectPlanes(i,o,a),r&&(this.version++,this.cacheKey=`${this.intersectionPlanes.length}:${this.unionPlanes.length}`)}getGroupContext(e){if(this.shadowPass&&!e.clipShadows)return this;let t=this.clippingGroupContexts.get(e);return void 0===t&&(t=new YT(this),this.clippingGroupContexts.set(e,t)),t.update(this,e),t}get unionClippingCount(){return this.unionPlanes.length}}class QT{constructor(e,t){this.bundleGroup=e,this.camera=t}}class ZT{constructor(){this.bundles=new Fg}get(e,t){const r=this.bundles,s=[e,t];let i=r.get(s);return void 0===i&&(i=new QT(e,t),r.set(s,i)),i}dispose(){this.bundles=new Fg}}class JT{constructor(){this.lightNodes=new WeakMap,this.materialNodes=new Map,this.toneMappingNodes=new Map}fromMaterial(e){if(e.isNodeMaterial)return e;let t=null;const r=this.getMaterialNodeClass(e.type);if(null!==r){t=new r;for(const r in e)t[r]=e[r]}return t}addToneMapping(e,t){this.addType(e,t,this.toneMappingNodes)}getToneMappingFunction(e){return this.toneMappingNodes.get(e)||null}getMaterialNodeClass(e){return this.materialNodes.get(e)||null}addMaterial(e,t){this.addType(e,t,this.materialNodes)}getLightNodeClass(e){return this.lightNodes.get(e)||null}addLight(e,t){this.addClass(e,t,this.lightNodes)}addType(e,t,r){if(r.has(t))console.warn(`Redefinition of node ${t}`);else{if("function"!=typeof e)throw new Error(`Node class ${e.name} is not a class.`);if("function"==typeof t||"object"==typeof t)throw new Error(`Base class ${t} is not a class.`);r.set(t,e)}}addClass(e,t,r){if(r.has(t))console.warn(`Redefinition of node ${t.name}`);else{if("function"!=typeof e)throw new Error(`Node class ${e.name} is not a class.`);if("function"!=typeof t)throw new Error(`Base class ${t.name} is not a class.`);r.set(t,e)}}}const e_=new Qy;class t_ extends Fg{constructor(){super()}createNode(e=[]){return(new Qy).setLights(e)}getNode(e,t){if(e.isQuadMesh)return e_;const r=[e,t];let s=this.get(r);return void 0===s&&(s=this.createNode(),this.set(r,s)),s}}const r_=new z,s_=new t,i_=new s,n_=new ke,o_=new n,a_=new s;class u_{constructor(e,t={}){this.isRenderer=!0;const{logarithmicDepthBuffer:r=!1,alpha:i=!0,depth:n=!0,stencil:o=!1,antialias:a=!1,samples:u=0,getFallback:l=null}=t;this.domElement=e.getDomElement(),this.backend=e,this.samples=u||!0===a?4:0,this.autoClear=!0,this.autoClearColor=!0,this.autoClearDepth=!0,this.autoClearStencil=!0,this.alpha=i,this.logarithmicDepthBuffer=r,this.outputColorSpace=Ve,this.toneMapping=h,this.toneMappingExposure=1,this.sortObjects=!0,this.depth=n,this.stencil=o,this.info=new qg,this.nodes={modelViewMatrix:null,modelNormalViewMatrix:null},this.library=new JT,this.lighting=new t_,this._getFallback=l,this._pixelRatio=1,this._width=this.domElement.width,this._height=this.domElement.height,this._viewport=new s(0,0,this._width,this._height),this._scissor=new s(0,0,this._width,this._height),this._scissorTest=!1,this._attributes=null,this._geometries=null,this._nodes=null,this._animation=null,this._bindings=null,this._objects=null,this._pipelines=null,this._bundles=null,this._renderLists=null,this._renderContexts=null,this._textures=null,this._background=null,this._quad=new of(new rh),this._quad.material.name="Renderer_output",this._currentRenderContext=null,this._opaqueSort=null,this._transparentSort=null,this._frameBufferTarget=null;const d=!0===this.alpha?0:1;this._clearColor=new hm(0,0,0,d),this._clearDepth=1,this._clearStencil=0,this._renderTarget=null,this._activeCubeFace=0,this._activeMipmapLevel=0,this._mrt=null,this._renderObjectFunction=null,this._currentRenderObjectFunction=null,this._currentRenderBundle=null,this._handleObjectFunction=this._renderObjectDirect,this._isDeviceLost=!1,this.onDeviceLost=this._onDeviceLost,this._initialized=!1,this._initPromise=null,this._compilationPromises=null,this.transparent=!0,this.opaque=!0,this.shadowMap={enabled:!1,type:Oe},this.xr={enabled:!1},this.debug={checkShaderErrors:!0,onShaderError:null,getShaderAsync:async(e,t,r)=>{await this.compileAsync(e,t);const s=this._renderLists.get(e,t),i=this._renderContexts.get(e,t,this._renderTarget),n=e.overrideMaterial||r.material,o=this._objects.get(r,n,e,t,s.lightsNode,i,i.clippingContext),{fragmentShader:a,vertexShader:u}=o.getNodeBuilderState();return{fragmentShader:a,vertexShader:u}}}}async init(){if(this._initialized)throw new Error("Renderer: Backend has already been initialized.");return null!==this._initPromise||(this._initPromise=new Promise((async(e,t)=>{let r=this.backend;try{await r.init(this)}catch(e){if(null===this._getFallback)return void t(e);try{this.backend=r=this._getFallback(e),await r.init(this)}catch(e){return void t(e)}}this._nodes=new KT(this,r),this._animation=new Bg(this._nodes,this.info),this._attributes=new $g(r),this._background=new Bx(this,this._nodes),this._geometries=new jg(this._attributes,this.info),this._textures=new cm(this,r,this.info),this._pipelines=new Jg(r,this._nodes),this._bindings=new em(r,this._nodes,this._textures,this._attributes,this._pipelines,this.info),this._objects=new Dg(this,this._nodes,this._geometries,this._pipelines,this._bindings,this.info),this._renderLists=new nm(this.lighting),this._bundles=new ZT,this._renderContexts=new lm,this._animation.start(),this._initialized=!0,e()}))),this._initPromise}get coordinateSystem(){return this.backend.coordinateSystem}async compileAsync(e,t,r=null){if(!0===this._isDeviceLost)return;!1===this._initialized&&await this.init();const s=this._nodes.nodeFrame,i=s.renderId,n=this._currentRenderContext,o=this._currentRenderObjectFunction,a=this._compilationPromises,u=!0===e.isScene?e:r_;null===r&&(r=e);const l=this._renderTarget,d=this._renderContexts.get(r,t,l),c=this._activeMipmapLevel,h=[];this._currentRenderContext=d,this._currentRenderObjectFunction=this.renderObject,this._handleObjectFunction=this._createObjectPipeline,this._compilationPromises=h,s.renderId++,s.update(),d.depth=this.depth,d.stencil=this.stencil,d.clippingContext||(d.clippingContext=new YT),d.clippingContext.updateGlobal(u,t),u.onBeforeRender(this,e,t,l);const p=this._renderLists.get(e,t);if(p.begin(),this._projectObject(e,t,0,p,d.clippingContext),r!==e&&r.traverseVisible((function(e){e.isLight&&e.layers.test(t.layers)&&p.pushLight(e)})),p.finish(),null!==l){this._textures.updateRenderTarget(l,c);const e=this._textures.get(l);d.textures=e.textures,d.depthTexture=e.depthTexture}else d.textures=null,d.depthTexture=null;this._background.update(u,p,d);const g=p.opaque,m=p.transparent,f=p.transparentDoublePass,y=p.lightsNode;!0===this.opaque&&g.length>0&&this._renderObjects(g,t,u,y),!0===this.transparent&&m.length>0&&this._renderTransparents(m,f,t,u,y),s.renderId=i,this._currentRenderContext=n,this._currentRenderObjectFunction=o,this._compilationPromises=a,this._handleObjectFunction=this._renderObjectDirect,await Promise.all(h)}async renderAsync(e,t){!1===this._initialized&&await this.init();const r=this._renderScene(e,t);await this.backend.resolveTimestampAsync(r,"render")}async waitForGPU(){await this.backend.waitForGPU()}setMRT(e){return this._mrt=e,this}getMRT(){return this._mrt}_onDeviceLost(e){let t=`THREE.WebGPURenderer: ${e.api} Device Lost:\n\nMessage: ${e.message}`;e.reason&&(t+=`\nReason: ${e.reason}`),console.error(t),this._isDeviceLost=!0}_renderBundle(e,t,r){const{bundleGroup:s,camera:i,renderList:n}=e,o=this._currentRenderContext,a=this._bundles.get(s,i),u=this.backend.get(a);void 0===u.renderContexts&&(u.renderContexts=new Set);const l=s.version!==u.version,d=!1===u.renderContexts.has(o)||l;if(u.renderContexts.add(o),d){this.backend.beginBundle(o),(void 0===u.renderObjects||l)&&(u.renderObjects=[]),this._currentRenderBundle=a;const e=n.opaque;!0===this.opaque&&e.length>0&&this._renderObjects(e,i,t,r),this._currentRenderBundle=null,this.backend.finishBundle(o,a),u.version=s.version}else{const{renderObjects:e}=u;for(let t=0,r=e.length;t<r;t++){const r=e[t];this._nodes.needsRefresh(r)&&(this._nodes.updateBefore(r),this._nodes.updateForRender(r),this._bindings.updateForRender(r),this._nodes.updateAfter(r))}}this.backend.addBundle(o,a)}render(e,t){if(!1===this._initialized)return console.warn("THREE.Renderer: .render() called before the backend is initialized. Try using .renderAsync() instead."),this.renderAsync(e,t);this._renderScene(e,t)}_getFrameBufferTarget(){const{currentToneMapping:e,currentColorSpace:t}=this;if(!1===(e!==h)&&!1===(t!==Ae))return null;const{width:r,height:s}=this.getDrawingBufferSize(s_),{depth:i,stencil:n}=this;let o=this._frameBufferTarget;return null===o&&(o=new ge(r,s,{depthBuffer:i,stencilBuffer:n,type:ye,format:Ue,colorSpace:Ae,generateMipmaps:!1,minFilter:$,magFilter:$,samples:this.samples}),o.isPostProcessingRenderTarget=!0,this._frameBufferTarget=o),o.depthBuffer=i,o.stencilBuffer=n,o.setSize(r,s),o.viewport.copy(this._viewport),o.scissor.copy(this._scissor),o.viewport.multiplyScalar(this._pixelRatio),o.scissor.multiplyScalar(this._pixelRatio),o.scissorTest=this._scissorTest,o}_renderScene(e,t,r=!0){if(!0===this._isDeviceLost)return;const s=r?this._getFrameBufferTarget():null,i=this._nodes.nodeFrame,n=i.renderId,o=this._currentRenderContext,a=this._currentRenderObjectFunction,u=!0===e.isScene?e:r_,l=this._renderTarget,d=this._activeCubeFace,c=this._activeMipmapLevel;let h;null!==s?(h=s,this.setRenderTarget(h)):h=l;const p=this._renderContexts.get(e,t,h);this._currentRenderContext=p,this._currentRenderObjectFunction=this._renderObjectFunction||this.renderObject,this.info.calls++,this.info.render.calls++,this.info.render.frameCalls++,i.renderId=this.info.calls;const g=this.coordinateSystem;t.coordinateSystem!==g&&(t.coordinateSystem=g,t.updateProjectionMatrix()),!0===e.matrixWorldAutoUpdate&&e.updateMatrixWorld(),null===t.parent&&!0===t.matrixWorldAutoUpdate&&t.updateMatrixWorld();let m=this._viewport,f=this._scissor,y=this._pixelRatio;null!==h&&(m=h.viewport,f=h.scissor,y=1),this.getDrawingBufferSize(s_),i_.set(0,0,s_.width,s_.height);const b=void 0===m.minDepth?0:m.minDepth,x=void 0===m.maxDepth?1:m.maxDepth;p.viewportValue.copy(m).multiplyScalar(y).floor(),p.viewportValue.width>>=c,p.viewportValue.height>>=c,p.viewportValue.minDepth=b,p.viewportValue.maxDepth=x,p.viewport=!1===p.viewportValue.equals(i_),p.scissorValue.copy(f).multiplyScalar(y).floor(),p.scissor=this._scissorTest&&!1===p.scissorValue.equals(i_),p.scissorValue.width>>=c,p.scissorValue.height>>=c,p.clippingContext||(p.clippingContext=new YT),p.clippingContext.updateGlobal(u,t),u.onBeforeRender(this,e,t,h),o_.multiplyMatrices(t.projectionMatrix,t.matrixWorldInverse),n_.setFromProjectionMatrix(o_,g);const T=this._renderLists.get(e,t);if(T.begin(),this._projectObject(e,t,0,T,p.clippingContext),T.finish(),!0===this.sortObjects&&T.sort(this._opaqueSort,this._transparentSort),null!==h){this._textures.updateRenderTarget(h,c);const e=this._textures.get(h);p.textures=e.textures,p.depthTexture=e.depthTexture,p.width=e.width,p.height=e.height,p.renderTarget=h,p.depth=h.depthBuffer,p.stencil=h.stencilBuffer}else p.textures=null,p.depthTexture=null,p.width=this.domElement.width,p.height=this.domElement.height,p.depth=this.depth,p.stencil=this.stencil;p.width>>=c,p.height>>=c,p.activeCubeFace=d,p.activeMipmapLevel=c,p.occlusionQueryCount=T.occlusionQueryCount,this._background.update(u,T,p),this.backend.beginRender(p);const{bundles:_,lightsNode:v,transparentDoublePass:N,transparent:S,opaque:A}=T;if(_.length>0&&this._renderBundles(_,u,v),!0===this.opaque&&A.length>0&&this._renderObjects(A,t,u,v),!0===this.transparent&&S.length>0&&this._renderTransparents(S,N,t,u,v),this.backend.finishRender(p),i.renderId=n,this._currentRenderContext=o,this._currentRenderObjectFunction=a,null!==s){this.setRenderTarget(l,d,c);const e=this._quad;this._nodes.hasOutputChange(h.texture)&&(e.material.fragmentNode=this._nodes.getOutputNode(h.texture),e.material.needsUpdate=!0),this._renderScene(e,e.camera,!1)}return u.onAfterRender(this,e,t,h),p}getMaxAnisotropy(){return this.backend.getMaxAnisotropy()}getActiveCubeFace(){return this._activeCubeFace}getActiveMipmapLevel(){return this._activeMipmapLevel}async setAnimationLoop(e){!1===this._initialized&&await this.init(),this._animation.setAnimationLoop(e)}async getArrayBufferAsync(e){return await this.backend.getArrayBufferAsync(e)}getContext(){return this.backend.getContext()}getPixelRatio(){return this._pixelRatio}getDrawingBufferSize(e){return e.set(this._width*this._pixelRatio,this._height*this._pixelRatio).floor()}getSize(e){return e.set(this._width,this._height)}setPixelRatio(e=1){this._pixelRatio!==e&&(this._pixelRatio=e,this.setSize(this._width,this._height,!1))}setDrawingBufferSize(e,t,r){this._width=e,this._height=t,this._pixelRatio=r,this.domElement.width=Math.floor(e*r),this.domElement.height=Math.floor(t*r),this.setViewport(0,0,e,t),this._initialized&&this.backend.updateSize()}setSize(e,t,r=!0){this._width=e,this._height=t,this.domElement.width=Math.floor(e*this._pixelRatio),this.domElement.height=Math.floor(t*this._pixelRatio),!0===r&&(this.domElement.style.width=e+"px",this.domElement.style.height=t+"px"),this.setViewport(0,0,e,t),this._initialized&&this.backend.updateSize()}setOpaqueSort(e){this._opaqueSort=e}setTransparentSort(e){this._transparentSort=e}getScissor(e){const t=this._scissor;return e.x=t.x,e.y=t.y,e.width=t.width,e.height=t.height,e}setScissor(e,t,r,s){const i=this._scissor;e.isVector4?i.copy(e):i.set(e,t,r,s)}getScissorTest(){return this._scissorTest}setScissorTest(e){this._scissorTest=e,this.backend.setScissorTest(e)}getViewport(e){return e.copy(this._viewport)}setViewport(e,t,r,s,i=0,n=1){const o=this._viewport;e.isVector4?o.copy(e):o.set(e,t,r,s),o.minDepth=i,o.maxDepth=n}getClearColor(e){return e.copy(this._clearColor)}setClearColor(e,t=1){this._clearColor.set(e),this._clearColor.a=t}getClearAlpha(){return this._clearColor.a}setClearAlpha(e){this._clearColor.a=e}getClearDepth(){return this._clearDepth}setClearDepth(e){this._clearDepth=e}getClearStencil(){return this._clearStencil}setClearStencil(e){this._clearStencil=e}isOccluded(e){const t=this._currentRenderContext;return t&&this.backend.isOccluded(t,e)}clear(e=!0,t=!0,r=!0){if(!1===this._initialized)return console.warn("THREE.Renderer: .clear() called before the backend is initialized. Try using .clearAsync() instead."),this.clearAsync(e,t,r);const s=this._renderTarget||this._getFrameBufferTarget();let i=null;if(null!==s){this._textures.updateRenderTarget(s);const e=this._textures.get(s);i=this._renderContexts.get(null,null,s),i.textures=e.textures,i.depthTexture=e.depthTexture,i.width=e.width,i.height=e.height,i.renderTarget=s,i.depth=s.depthBuffer,i.stencil=s.stencilBuffer}if(this.backend.clear(e,t,r,i),null!==s&&null===this._renderTarget){const e=this._quad;this._nodes.hasOutputChange(s.texture)&&(e.material.fragmentNode=this._nodes.getOutputNode(s.texture),e.material.needsUpdate=!0),this._renderScene(e,e.camera,!1)}}clearColor(){return this.clear(!0,!1,!1)}clearDepth(){return this.clear(!1,!0,!1)}clearStencil(){return this.clear(!1,!1,!0)}async clearAsync(e=!0,t=!0,r=!0){!1===this._initialized&&await this.init(),this.clear(e,t,r)}async clearColorAsync(){this.clearAsync(!0,!1,!1)}async clearDepthAsync(){this.clearAsync(!1,!0,!1)}async clearStencilAsync(){this.clearAsync(!1,!1,!0)}get currentToneMapping(){return null!==this._renderTarget?h:this.toneMapping}get currentColorSpace(){return null!==this._renderTarget?Ae:this.outputColorSpace}dispose(){this.info.dispose(),this.backend.dispose(),this._animation.dispose(),this._objects.dispose(),this._pipelines.dispose(),this._nodes.dispose(),this._bindings.dispose(),this._renderLists.dispose(),this._renderContexts.dispose(),this._textures.dispose(),this.setRenderTarget(null),this.setAnimationLoop(null)}setRenderTarget(e,t=0,r=0){this._renderTarget=e,this._activeCubeFace=t,this._activeMipmapLevel=r}getRenderTarget(){return this._renderTarget}setRenderObjectFunction(e){this._renderObjectFunction=e}getRenderObjectFunction(){return this._renderObjectFunction}compute(e){if(!0===this.isDeviceLost)return;if(!1===this._initialized)return console.warn("THREE.Renderer: .compute() called before the backend is initialized. Try using .computeAsync() instead."),this.computeAsync(e);const t=this._nodes.nodeFrame,r=t.renderId;this.info.calls++,this.info.compute.calls++,this.info.compute.frameCalls++,t.renderId=this.info.calls;const s=this.backend,i=this._pipelines,n=this._bindings,o=this._nodes,a=Array.isArray(e)?e:[e];if(void 0===a[0]||!0!==a[0].isComputeNode)throw new Error("THREE.Renderer: .compute() expects a ComputeNode.");s.beginCompute(e);for(const t of a){if(!1===i.has(t)){const e=()=>{t.removeEventListener("dispose",e),i.delete(t),n.delete(t),o.delete(t)};t.addEventListener("dispose",e);const r=t.onInitFunction;null!==r&&r.call(t,{renderer:this})}o.updateForCompute(t),n.updateForCompute(t);const r=n.getForCompute(t),a=i.getForCompute(t,r);s.compute(e,t,r,a)}s.finishCompute(e),t.renderId=r}async computeAsync(e){!1===this._initialized&&await this.init(),this.compute(e),await this.backend.resolveTimestampAsync(e,"compute")}async hasFeatureAsync(e){return!1===this._initialized&&await this.init(),this.backend.hasFeature(e)}hasFeature(e){return!1===this._initialized?(console.warn("THREE.Renderer: .hasFeature() called before the backend is initialized. Try using .hasFeatureAsync() instead."),!1):this.backend.hasFeature(e)}hasInitialized(){return this._initialized}async initTextureAsync(e){!1===this._initialized&&await this.init(),this._textures.updateTexture(e)}initTexture(e){!1===this._initialized&&console.warn("THREE.Renderer: .initTexture() called before the backend is initialized. Try using .initTextureAsync() instead."),this._textures.updateTexture(e)}copyFramebufferToTexture(e,t=null){if(null!==t)if(t.isVector2)t=a_.set(t.x,t.y,e.image.width,e.image.height).floor();else{if(!t.isVector4)return void console.error("THREE.Renderer.copyFramebufferToTexture: Invalid rectangle.");t=a_.copy(t).floor()}else t=a_.set(0,0,e.image.width,e.image.height);let r,s=this._currentRenderContext;null!==s?r=s.renderTarget:(r=this._renderTarget||this._getFrameBufferTarget(),null!==r&&(this._textures.updateRenderTarget(r),s=this._textures.get(r))),this._textures.updateTexture(e,{renderTarget:r}),this.backend.copyFramebufferToTexture(e,s,t)}copyTextureToTexture(e,t,r=null,s=null,i=0){this._textures.updateTexture(e),this._textures.updateTexture(t),this.backend.copyTextureToTexture(e,t,r,s,i)}async readRenderTargetPixelsAsync(e,t,r,s,i,n=0,o=0){return this.backend.copyTextureToBuffer(e.textures[n],t,r,s,i,o)}_projectObject(e,t,r,s,i){if(!1===e.visible)return;if(e.layers.test(t.layers))if(e.isGroup)r=e.renderOrder,e.isClippingGroup&&e.enabled&&(i=i.getGroupContext(e));else if(e.isLOD)!0===e.autoUpdate&&e.update(t);else if(e.isLight)s.pushLight(e);else if(e.isSprite){if(!e.frustumCulled||n_.intersectsSprite(e)){!0===this.sortObjects&&a_.setFromMatrixPosition(e.matrixWorld).applyMatrix4(o_);const{geometry:t,material:n}=e;n.visible&&s.push(e,t,n,r,a_.z,null,i)}}else if(e.isLineLoop)console.error("THREE.Renderer: Objects of type THREE.LineLoop are not supported. Please use THREE.Line or THREE.LineSegments.");else if((e.isMesh||e.isLine||e.isPoints)&&(!e.frustumCulled||n_.intersectsObject(e))){const{geometry:t,material:n}=e;if(!0===this.sortObjects&&(null===t.boundingSphere&&t.computeBoundingSphere(),a_.copy(t.boundingSphere.center).applyMatrix4(e.matrixWorld).applyMatrix4(o_)),Array.isArray(n)){const o=t.groups;for(let a=0,u=o.length;a<u;a++){const u=o[a],l=n[u.materialIndex];l&&l.visible&&s.push(e,t,l,r,a_.z,u,i)}}else n.visible&&s.push(e,t,n,r,a_.z,null,i)}if(!0===e.isBundleGroup&&void 0!==this.backend.beginBundle){const r=s;(s=this._renderLists.get(e,t)).begin(),r.pushBundle({bundleGroup:e,camera:t,renderList:s}),s.finish()}const n=e.children;for(let e=0,o=n.length;e<o;e++)this._projectObject(n[e],t,r,s,i)}_renderBundles(e,t,r){for(const s of e)this._renderBundle(s,t,r)}_renderTransparents(e,t,r,s,i){if(t.length>0){for(const{material:e}of t)e.side=T;this._renderObjects(t,r,s,i,"backSide");for(const{material:e}of t)e.side=Ge;this._renderObjects(e,r,s,i);for(const{material:e}of t)e.side=le}else this._renderObjects(e,r,s,i)}_renderObjects(e,t,r,s,i=null){for(let n=0,o=e.length;n<o;n++){const o=e[n],{object:a,geometry:u,material:l,group:d,clippingContext:c}=o;if(t.isArrayCamera){const e=t.cameras;for(let t=0,n=e.length;t<n;t++){const n=e[t];if(a.layers.test(n.layers)){const e=n.viewport,t=void 0===e.minDepth?0:e.minDepth,o=void 0===e.maxDepth?1:e.maxDepth,h=this._currentRenderContext.viewportValue;h.copy(e).multiplyScalar(this._pixelRatio).floor(),h.minDepth=t,h.maxDepth=o,this.backend.updateViewport(this._currentRenderContext),this._currentRenderObjectFunction(a,r,n,u,l,d,s,c,i)}}}else this._currentRenderObjectFunction(a,r,t,u,l,d,s,c,i)}}renderObject(e,t,r,s,i,n,o,a=null,u=null){let l,d,c;if(e.onBeforeRender(this,t,r,s,i,n),null!==t.overrideMaterial){const e=t.overrideMaterial;i.positionNode&&i.positionNode.isNode&&(l=e.positionNode,e.positionNode=i.positionNode),e.alphaTest=i.alphaTest,e.alphaMap=i.alphaMap,e.transparent=i.transparent||i.transmission>0,e.isShadowNodeMaterial&&(e.side=null===i.shadowSide?i.side:i.shadowSide,i.depthNode&&i.depthNode.isNode&&(c=e.depthNode,e.depthNode=i.depthNode),i.castShadowNode&&i.castShadowNode.isNode&&(d=e.colorNode,e.colorNode=i.castShadowNode)),i=e}!0===i.transparent&&i.side===le&&!1===i.forceSinglePass?(i.side=T,this._handleObjectFunction(e,i,t,r,o,n,a,"backSide"),i.side=Ge,this._handleObjectFunction(e,i,t,r,o,n,a,u),i.side=le):this._handleObjectFunction(e,i,t,r,o,n,a,u),void 0!==l&&(t.overrideMaterial.positionNode=l),void 0!==c&&(t.overrideMaterial.depthNode=c),void 0!==d&&(t.overrideMaterial.colorNode=d),e.onAfterRender(this,t,r,s,i,n)}_renderObjectDirect(e,t,r,s,i,n,o,a){const u=this._objects.get(e,t,r,s,i,this._currentRenderContext,o,a);u.drawRange=e.geometry.drawRange,u.group=n;const l=this._nodes.needsRefresh(u);if(l&&(this._nodes.updateBefore(u),this._geometries.updateForRender(u),this._nodes.updateForRender(u),this._bindings.updateForRender(u)),this._pipelines.updateForRender(u),null!==this._currentRenderBundle){this.backend.get(this._currentRenderBundle).renderObjects.push(u),u.bundle=this._currentRenderBundle.bundleGroup}this.backend.draw(u,this.info),l&&this._nodes.updateAfter(u)}_createObjectPipeline(e,t,r,s,i,n,o,a){const u=this._objects.get(e,t,r,s,i,this._currentRenderContext,o,a);u.drawRange=e.geometry.drawRange,u.group=n,this._nodes.updateBefore(u),this._geometries.updateForRender(u),this._nodes.updateForRender(u),this._bindings.updateForRender(u),this._pipelines.getForRender(u,this._compilationPromises),this._nodes.updateAfter(u)}get compile(){return this.compileAsync}}class l_{constructor(e=""){this.name=e,this.visibility=0}setVisibility(e){this.visibility|=e}clone(){return Object.assign(new this.constructor,this)}}class d_ extends l_{constructor(e,t=null){super(e),this.isBuffer=!0,this.bytesPerElement=Float32Array.BYTES_PER_ELEMENT,this._buffer=t}get byteLength(){return(e=this._buffer.byteLength)+(zg-e%zg)%zg;var e}get buffer(){return this._buffer}update(){return!0}}class c_ extends d_{constructor(e,t=null){super(e,t),this.isUniformBuffer=!0}}let h_=0;class p_ extends c_{constructor(e,t){super("UniformBuffer_"+h_++,e?e.value:null),this.nodeUniform=e,this.groupNode=t}get buffer(){return this.nodeUniform.value}}class g_ extends c_{constructor(e){super(e),this.isUniformsGroup=!0,this._values=null,this.uniforms=[]}addUniform(e){return this.uniforms.push(e),this}removeUniform(e){const t=this.uniforms.indexOf(e);return-1!==t&&this.uniforms.splice(t,1),this}get values(){return null===this._values&&(this._values=Array.from(this.buffer)),this._values}get buffer(){let e=this._buffer;if(null===e){const t=this.byteLength;e=new Float32Array(new ArrayBuffer(t)),this._buffer=e}return e}get byteLength(){let e=0;for(let t=0,r=this.uniforms.length;t<r;t++){const r=this.uniforms[t],{boundary:s,itemSize:i}=r,n=e%zg;0!==n&&zg-n-s<0?e+=zg-n:n%s!=0&&(e+=n%s),r.offset=e/this.bytesPerElement,e+=i*this.bytesPerElement}return Math.ceil(e/zg)*zg}update(){let e=!1;for(const t of this.uniforms)!0===this.updateByType(t)&&(e=!0);return e}updateByType(e){return e.isNumberUniform?this.updateNumber(e):e.isVector2Uniform?this.updateVector2(e):e.isVector3Uniform?this.updateVector3(e):e.isVector4Uniform?this.updateVector4(e):e.isColorUniform?this.updateColor(e):e.isMatrix3Uniform?this.updateMatrix3(e):e.isMatrix4Uniform?this.updateMatrix4(e):void console.error("THREE.WebGPUUniformsGroup: Unsupported uniform type.",e)}updateNumber(e){let t=!1;const r=this.values,s=e.getValue(),i=e.offset,n=e.getType();if(r[i]!==s){this._getBufferForType(n)[i]=r[i]=s,t=!0}return t}updateVector2(e){let t=!1;const r=this.values,s=e.getValue(),i=e.offset,n=e.getType();if(r[i+0]!==s.x||r[i+1]!==s.y){const e=this._getBufferForType(n);e[i+0]=r[i+0]=s.x,e[i+1]=r[i+1]=s.y,t=!0}return t}updateVector3(e){let t=!1;const r=this.values,s=e.getValue(),i=e.offset,n=e.getType();if(r[i+0]!==s.x||r[i+1]!==s.y||r[i+2]!==s.z){const e=this._getBufferForType(n);e[i+0]=r[i+0]=s.x,e[i+1]=r[i+1]=s.y,e[i+2]=r[i+2]=s.z,t=!0}return t}updateVector4(e){let t=!1;const r=this.values,s=e.getValue(),i=e.offset,n=e.getType();if(r[i+0]!==s.x||r[i+1]!==s.y||r[i+2]!==s.z||r[i+4]!==s.w){const e=this._getBufferForType(n);e[i+0]=r[i+0]=s.x,e[i+1]=r[i+1]=s.y,e[i+2]=r[i+2]=s.z,e[i+3]=r[i+3]=s.w,t=!0}return t}updateColor(e){let t=!1;const r=this.values,s=e.getValue(),i=e.offset;if(r[i+0]!==s.r||r[i+1]!==s.g||r[i+2]!==s.b){const e=this.buffer;e[i+0]=r[i+0]=s.r,e[i+1]=r[i+1]=s.g,e[i+2]=r[i+2]=s.b,t=!0}return t}updateMatrix3(e){let t=!1;const r=this.values,s=e.getValue().elements,i=e.offset;if(r[i+0]!==s[0]||r[i+1]!==s[1]||r[i+2]!==s[2]||r[i+4]!==s[3]||r[i+5]!==s[4]||r[i+6]!==s[5]||r[i+8]!==s[6]||r[i+9]!==s[7]||r[i+10]!==s[8]){const e=this.buffer;e[i+0]=r[i+0]=s[0],e[i+1]=r[i+1]=s[1],e[i+2]=r[i+2]=s[2],e[i+4]=r[i+4]=s[3],e[i+5]=r[i+5]=s[4],e[i+6]=r[i+6]=s[5],e[i+8]=r[i+8]=s[6],e[i+9]=r[i+9]=s[7],e[i+10]=r[i+10]=s[8],t=!0}return t}updateMatrix4(e){let t=!1;const r=this.values,s=e.getValue().elements,i=e.offset;if(!1===function(e,t,r){for(let s=0,i=t.length;s<i;s++)if(e[r+s]!==t[s])return!1;return!0}(r,s,i)){this.buffer.set(s,i),function(e,t,r){for(let s=0,i=t.length;s<i;s++)e[r+s]=t[s]}(r,s,i),t=!0}return t}_getBufferForType(e){return"int"===e||"ivec2"===e||"ivec3"===e||"ivec4"===e?new Int32Array(this.buffer.buffer):"uint"===e||"uvec2"===e||"uvec3"===e||"uvec4"===e?new Uint32Array(this.buffer.buffer):this.buffer}}let m_=0;class f_ extends g_{constructor(e,t){super(e),this.id=m_++,this.groupNode=t,this.isNodeUniformsGroup=!0}}let y_=0;class b_ extends l_{constructor(e,t){super(e),this.id=y_++,this.texture=t,this.version=t?t.version:0,this.store=!1,this.generation=null,this.isSampledTexture=!0}needsBindingsUpdate(e){const{texture:t}=this;return e!==this.generation?(this.generation=e,!0):t.isVideoTexture}update(){const{texture:e,version:t}=this;return t!==e.version&&(this.version=e.version,!0)}}class x_ extends b_{constructor(e,t,r,s=null){super(e,t?t.value:null),this.textureNode=t,this.groupNode=r,this.access=s}needsBindingsUpdate(e){return this.textureNode.value!==this.texture||super.needsBindingsUpdate(e)}update(){const{textureNode:e}=this;return this.texture!==e.value?(this.texture=e.value,!0):super.update()}}class T_ extends x_{constructor(e,t,r,s=null){super(e,t,r,s),this.isSampledCubeTexture=!0}}class __ extends x_{constructor(e,t,r,s=null){super(e,t,r,s),this.isSampledTexture3D=!0}}const v_={textureDimensions:"textureSize",equals:"equal"},N_={low:"lowp",medium:"mediump",high:"highp"},S_={swizzleAssign:!0,storageBuffer:!1},A_="\nprecision highp float;\nprecision highp int;\nprecision highp sampler2D;\nprecision highp sampler3D;\nprecision highp samplerCube;\nprecision highp sampler2DArray;\n\nprecision highp usampler2D;\nprecision highp usampler3D;\nprecision highp usamplerCube;\nprecision highp usampler2DArray;\n\nprecision highp isampler2D;\nprecision highp isampler3D;\nprecision highp isamplerCube;\nprecision highp isampler2DArray;\n\nprecision lowp sampler2DShadow;\n";class R_ extends CT{constructor(e,t){super(e,t,new jT),this.uniformGroups={},this.transforms=[],this.extensions={},this.builtins={vertex:[],fragment:[],compute:[]},this.useComparisonMethod=!0}needsToWorkingColorSpace(e){return!0===e.isVideoTexture&&e.colorSpace!==y}getMethod(e){return v_[e]||e}getOutputStructName(){return""}buildFunctionCode(e){const t=e.layout,r=this.flowShaderNode(e),s=[];for(const e of t.inputs)s.push(this.getType(e.type)+" "+e.name);return`${this.getType(t.type)} ${t.name}( ${s.join(", ")} ) {\n\n\t${r.vars}\n\n${r.code}\n\treturn ${r.result};\n\n}`}setupPBO(e){const t=e.value;if(void 0===t.pbo){const e=t.array,r=t.count*t.itemSize,{itemSize:s}=t,i=t.array.constructor.name.toLowerCase().includes("int");let n=i?$e:He;2===s?n=i?We:Ee:3===s?n=i?je:qe:4===s&&(n=i?Ke:Ue);const o={Float32Array:E,Uint8Array:pe,Uint16Array:Xe,Uint32Array:b,Int8Array:Ye,Int16Array:Qe,Int32Array:x,Uint8ClampedArray:pe},a=Math.pow(2,Math.ceil(Math.log2(Math.sqrt(r/s))));let u=Math.ceil(r/s/a);a*u*s<r&&u++;const l=a*u*s,d=new e.constructor(l);d.set(e,0),t.array=d;const c=new ze(t.array,a,u,n,o[t.array.constructor.name]||E);c.needsUpdate=!0,c.isPBOTexture=!0;const h=new Su(c,null,null);h.setPrecision("high"),t.pboNode=h,t.pbo=h.value,this.getUniformFromNode(t.pboNode,"texture",this.shaderStage,this.context.label)}}getPropertyName(e,t=this.shaderStage){return e.isNodeUniform&&!0!==e.node.isTextureNode&&!0!==e.node.isBufferNode?t.charAt(0)+"_"+e.name:super.getPropertyName(e,t)}generatePBO(e){const{node:t,indexNode:r}=e,s=t.value;if(this.renderer.backend.has(s)){this.renderer.backend.get(s).pbo=s.pbo}const i=this.getUniformFromNode(s.pboNode,"texture",this.shaderStage,this.context.label),n=this.getPropertyName(i);this.increaseUsage(r);const o=r.build(this,"uint"),a=this.getDataFromNode(e);let u=a.propertyName;if(void 0===u){const r=this.getVarFromNode(e);u=this.getPropertyName(r);const i=this.getDataFromNode(t);let l=i.propertySizeName;void 0===l&&(l=u+"Size",this.getVarFromNode(t,l,"uint"),this.addLineFlowCode(`${l} = uint( textureSize( ${n}, 0 ).x )`,e),i.propertySizeName=l);const{itemSize:d}=s,c="."+Bs.join("").slice(0,d),h=`ivec2(${o} % ${l}, ${o} / ${l})`,p=this.generateTextureLoad(null,n,h,null,"0");let g="vec4";s.pbo.type===b?g="uvec4":s.pbo.type===x&&(g="ivec4"),this.addLineFlowCode(`${u} = ${g}(${p})${c}`,e),a.propertyName=u}return u}generateTextureLoad(e,t,r,s,i="0"){return s?`texelFetch( ${t}, ivec3( ${r}, ${s} ), ${i} )`:`texelFetch( ${t}, ${r}, ${i} )`}generateTexture(e,t,r,s){return e.isDepthTexture?`texture( ${t}, ${r} ).x`:(s&&(r=`vec3( ${r}, ${s} )`),`texture( ${t}, ${r} )`)}generateTextureLevel(e,t,r,s){return`textureLod( ${t}, ${r}, ${s} )`}generateTextureBias(e,t,r,s){return`texture( ${t}, ${r}, ${s} )`}generateTextureGrad(e,t,r,s){return`textureGrad( ${t}, ${r}, ${s[0]}, ${s[1]} )`}generateTextureCompare(e,t,r,s,i,n=this.shaderStage){if("fragment"===n)return`texture( ${t}, vec3( ${r}, ${s} ) )`;console.error(`WebGPURenderer: THREE.DepthTexture.compareFunction() does not support ${n} shader.`)}getVars(e){const t=[],r=this.vars[e];if(void 0!==r)for(const e of r)t.push(`${this.getVar(e.type,e.name)};`);return t.join("\n\t")}getUniforms(e){const t=this.uniforms[e],r=[],s={};for(const i of t){let t=null,n=!1;if("texture"===i.type){const e=i.node.value;let r="";!0===e.isDataTexture&&(e.type===b?r="u":e.type===x&&(r="i")),t=e.compareFunction?`sampler2DShadow ${i.name};`:!0===e.isDataArrayTexture||!0===e.isCompressedArrayTexture?`${r}sampler2DArray ${i.name};`:`${r}sampler2D ${i.name};`}else if("cubeTexture"===i.type)t=`samplerCube ${i.name};`;else if("texture3D"===i.type)t=`sampler3D ${i.name};`;else if("buffer"===i.type){const e=i.node,r=this.getType(e.bufferType),s=e.bufferCount,n=s>0?s:"";t=`${e.name} {\n\t${r} ${i.name}[${n}];\n};\n`}else{t=`${this.getVectorType(i.type)} ${this.getPropertyName(i,e)};`,n=!0}const o=i.node.precision;if(null!==o&&(t=N_[o]+" "+t),n){t="\t"+t;const e=i.groupNode.name;(s[e]||(s[e]=[])).push(t)}else t="uniform "+t,r.push(t)}let i="";for(const t in s){const r=s[t];i+=this._getGLSLUniformStruct(e+"_"+t,r.join("\n"))+"\n"}return i+=r.join("\n"),i}getTypeFromAttribute(e){let t=super.getTypeFromAttribute(e);if(/^[iu]/.test(t)&&e.gpuType!==x){let r=e;e.isInterleavedBufferAttribute&&(r=e.data);const s=r.array;!1==(s instanceof Uint32Array||s instanceof Int32Array)&&(t=t.slice(1))}return t}getAttributes(e){let t="";if("vertex"===e||"compute"===e){const e=this.getAttributesArray();let r=0;for(const s of e)t+=`layout( location = ${r++} ) in ${s.type} ${s.name};\n`}return t}getStructMembers(e){const t=[],r=e.getMemberTypes();for(let e=0;e<r.length;e++){const s=r[e];t.push(`layout( location = ${e} ) out ${s} m${e};`)}return t.join("\n")}getStructs(e){const t=[],r=this.structs[e];if(0===r.length)return"layout( location = 0 ) out vec4 fragColor;\n";for(let e=0,s=r.length;e<s;e++){const s=r[e];let i="\n";i+=this.getStructMembers(s),i+="\n",t.push(i)}return t.join("\n\n")}getVaryings(e){let t="";const r=this.varyings;if("vertex"===e||"compute"===e)for(const s of r){"compute"===e&&(s.needsInterpolation=!0);const r=this.getType(s.type);t+=`${r.includes("int")||r.includes("uv")||r.includes("iv")?"flat ":""}${s.needsInterpolation?"out":"/*out*/"} ${r} ${s.name};\n`}else if("fragment"===e)for(const e of r)if(e.needsInterpolation){const r=this.getType(e.type);t+=`${r.includes("int")||r.includes("uv")||r.includes("iv")?"flat ":""}in ${r} ${e.name};\n`}for(const r of this.builtins[e])t+=`${r};\n`;return t}getVertexIndex(){return"uint( gl_VertexID )"}getInstanceIndex(){return"uint( gl_InstanceID )"}getInvocationLocalIndex(){return`uint( gl_InstanceID ) % ${this.object.workgroupSize.reduce(((e,t)=>e*t),1)}u`}getDrawIndex(){return this.renderer.backend.extensions.has("WEBGL_multi_draw")?"uint( gl_DrawID )":null}getFrontFacing(){return"gl_FrontFacing"}getFragCoord(){return"gl_FragCoord.xy"}getFragDepth(){return"gl_FragDepth"}enableExtension(e,t,r=this.shaderStage){const s=this.extensions[r]||(this.extensions[r]=new Map);!1===s.has(e)&&s.set(e,{name:e,behavior:t})}getExtensions(e){const t=[];if("vertex"===e){const t=this.renderer.backend.extensions;this.object.isBatchedMesh&&t.has("WEBGL_multi_draw")&&this.enableExtension("GL_ANGLE_multi_draw","require",e)}const r=this.extensions[e];if(void 0!==r)for(const{name:e,behavior:s}of r.values())t.push(`#extension ${e} : ${s}`);return t.join("\n")}getClipDistance(){return"gl_ClipDistance"}isAvailable(e){let t=S_[e];if(void 0===t){let r;switch(t=!1,e){case"float32Filterable":r="OES_texture_float_linear";break;case"clipDistance":r="WEBGL_clip_cull_distance"}if(void 0!==r){const e=this.renderer.backend.extensions;e.has(r)&&(e.get(r),t=!0)}S_[e]=t}return t}isFlipY(){return!0}enableHardwareClipping(e){this.enableExtension("GL_ANGLE_clip_cull_distance","require"),this.builtins.vertex.push(`out float gl_ClipDistance[ ${e} ]`)}registerTransform(e,t){this.transforms.push({varyingName:e,attributeNode:t})}getTransforms(){const e=this.transforms;let t="";for(let r=0;r<e.length;r++){const s=e[r],i=this.getPropertyName(s.attributeNode);t+=`${s.varyingName} = ${i};\n\t`}return t}_getGLSLUniformStruct(e,t){return`\nlayout( std140 ) uniform ${e} {\n${t}\n};`}_getGLSLVertexCode(e){return`#version 300 es\n\n${this.getSignature()}\n\n// extensions \n${e.extensions}\n\n// precision\n${A_}\n\n// uniforms\n${e.uniforms}\n\n// varyings\n${e.varyings}\n\n// attributes\n${e.attributes}\n\n// codes\n${e.codes}\n\nvoid main() {\n\n\t// vars\n\t${e.vars}\n\n\t// transforms\n\t${e.transforms}\n\n\t// flow\n\t${e.flow}\n\n\tgl_PointSize = 1.0;\n\n}\n`}_getGLSLFragmentCode(e){return`#version 300 es\n\n${this.getSignature()}\n\n// precision\n${A_}\n\n// uniforms\n${e.uniforms}\n\n// varyings\n${e.varyings}\n\n// codes\n${e.codes}\n\n${e.structs}\n\nvoid main() {\n\n\t// vars\n\t${e.vars}\n\n\t// flow\n\t${e.flow}\n\n}\n`}buildCode(){const e=null!==this.material?{fragment:{},vertex:{}}:{compute:{}};this.sortBindingGroups();for(const t in e){let r="// code\n\n";r+=this.flowCode[t];const s=this.flowNodes[t],i=s[s.length-1];for(const e of s){const s=this.getFlowData(e),n=e.name;n&&(r.length>0&&(r+="\n"),r+=`\t// flow -> ${n}\n\t`),r+=`${s.code}\n\t`,e===i&&"compute"!==t&&(r+="// result\n\t","vertex"===t?(r+="gl_Position = ",r+=`${s.result};`):"fragment"===t&&(e.outputNode.isOutputStructNode||(r+="fragColor = ",r+=`${s.result};`)))}const n=e[t];n.extensions=this.getExtensions(t),n.uniforms=this.getUniforms(t),n.attributes=this.getAttributes(t),n.varyings=this.getVaryings(t),n.vars=this.getVars(t),n.structs=this.getStructs(t),n.codes=this.getCodes(t),n.transforms=this.getTransforms(t),n.flow=r}null!==this.material?(this.vertexShader=this._getGLSLVertexCode(e.vertex),this.fragmentShader=this._getGLSLFragmentCode(e.fragment)):this.computeShader=this._getGLSLVertexCode(e.compute)}getUniformFromNode(e,t,r,s=null){const i=super.getUniformFromNode(e,t,r,s),n=this.getDataFromNode(e,r,this.globalCache);let o=n.uniformGPU;if(void 0===o){const s=e.groupNode,a=s.name,u=this.getBindGroupArray(a,r);if("texture"===t)o=new x_(i.name,i.node,s),u.push(o);else if("cubeTexture"===t)o=new T_(i.name,i.node,s),u.push(o);else if("texture3D"===t)o=new __(i.name,i.node,s),u.push(o);else if("buffer"===t){e.name=`NodeBuffer_${e.id}`,i.name=`buffer${e.id}`;const t=new p_(e,s);t.name=e.name,u.push(t),o=t}else{const e=this.uniformGroups[r]||(this.uniformGroups[r]={});let n=e[a];void 0===n&&(n=new f_(r+"_"+a,s),e[a]=n,u.push(n)),o=this.getNodeUniform(i,t),n.addUniform(o)}n.uniformGPU=o}return i}}let C_=null,E_=null;class w_{constructor(e={}){this.parameters=Object.assign({},e),this.data=new WeakMap,this.renderer=null,this.domElement=null}async init(e){this.renderer=e}get coordinateSystem(){}beginRender(){}finishRender(){}beginCompute(){}finishCompute(){}draw(){}compute(){}createProgram(){}destroyProgram(){}createBindings(){}updateBindings(){}updateBinding(){}createRenderPipeline(){}createComputePipeline(){}needsRenderUpdate(){}getRenderCacheKey(){}createNodeBuilder(){}createSampler(){}destroySampler(){}createDefaultTexture(){}createTexture(){}updateTexture(){}generateMipmaps(){}destroyTexture(){}copyTextureToBuffer(){}copyTextureToTexture(){}copyFramebufferToTexture(){}createAttribute(){}createIndexAttribute(){}createStorageAttribute(){}updateAttribute(){}destroyAttribute(){}getContext(){}updateSize(){}updateViewport(){}isOccluded(){}async resolveTimestampAsync(){}async waitForGPU(){}async hasFeatureAsync(){}hasFeature(){}getMaxAnisotropy(){}getDrawingBufferSize(){return C_=C_||new t,this.renderer.getDrawingBufferSize(C_)}setScissorTest(){}getClearColor(){const e=this.renderer;return E_=E_||new hm,e.getClearColor(E_),E_.getRGB(E_,this.renderer.currentColorSpace),E_}getDomElement(){let e=this.domElement;return null===e&&(e=void 0!==this.parameters.canvas?this.parameters.canvas:Ze(),"setAttribute"in e&&e.setAttribute("data-engine",`three.js r${Le} webgpu`),this.domElement=e),e}set(e,t){this.data.set(e,t)}get(e){let t=this.data.get(e);return void 0===t&&(t={},this.data.set(e,t)),t}has(e){return this.data.has(e)}delete(e){this.data.delete(e)}dispose(){}}let M_=0;class B_{constructor(e,t){this.buffers=[e.bufferGPU,t],this.type=e.type,this.bufferType=e.bufferType,this.pbo=e.pbo,this.byteLength=e.byteLength,this.bytesPerElement=e.BYTES_PER_ELEMENT,this.version=e.version,this.isInteger=e.isInteger,this.activeBufferIndex=0,this.baseId=e.id}get id(){return`${this.baseId}|${this.activeBufferIndex}`}get bufferGPU(){return this.buffers[this.activeBufferIndex]}get transformBuffer(){return this.buffers[1^this.activeBufferIndex]}switchBuffers(){this.activeBufferIndex^=1}}class F_{constructor(e){this.backend=e}createAttribute(e,t){const r=this.backend,{gl:s}=r,i=e.array,n=e.usage||s.STATIC_DRAW,o=e.isInterleavedBufferAttribute?e.data:e,a=r.get(o);let u,l=a.bufferGPU;if(void 0===l&&(l=this._createBuffer(s,t,i,n),a.bufferGPU=l,a.bufferType=t,a.version=o.version),i instanceof Float32Array)u=s.FLOAT;else if(i instanceof Uint16Array)u=e.isFloat16BufferAttribute?s.HALF_FLOAT:s.UNSIGNED_SHORT;else if(i instanceof Int16Array)u=s.SHORT;else if(i instanceof Uint32Array)u=s.UNSIGNED_INT;else if(i instanceof Int32Array)u=s.INT;else if(i instanceof Int8Array)u=s.BYTE;else if(i instanceof Uint8Array)u=s.UNSIGNED_BYTE;else{if(!(i instanceof Uint8ClampedArray))throw new Error("THREE.WebGLBackend: Unsupported buffer data format: "+i);u=s.UNSIGNED_BYTE}let d={bufferGPU:l,bufferType:t,type:u,byteLength:i.byteLength,bytesPerElement:i.BYTES_PER_ELEMENT,version:e.version,pbo:e.pbo,isInteger:u===s.INT||u===s.UNSIGNED_INT||e.gpuType===x,id:M_++};if(e.isStorageBufferAttribute||e.isStorageInstancedBufferAttribute){const e=this._createBuffer(s,t,i,n);d=new B_(d,e)}r.set(e,d)}updateAttribute(e){const t=this.backend,{gl:r}=t,s=e.array,i=e.isInterleavedBufferAttribute?e.data:e,n=t.get(i),o=n.bufferType,a=e.isInterleavedBufferAttribute?e.data.updateRanges:e.updateRanges;if(r.bindBuffer(o,n.bufferGPU),0===a.length)r.bufferSubData(o,0,s);else{for(let e=0,t=a.length;e<t;e++){const t=a[e];r.bufferSubData(o,t.start*s.BYTES_PER_ELEMENT,s,t.start,t.count)}i.clearUpdateRanges()}r.bindBuffer(o,null),n.version=i.version}destroyAttribute(e){const t=this.backend,{gl:r}=t;e.isInterleavedBufferAttribute&&t.delete(e.data);const s=t.get(e);r.deleteBuffer(s.bufferGPU),t.delete(e)}async getArrayBufferAsync(e){const t=this.backend,{gl:r}=t,s=e.isInterleavedBufferAttribute?e.data:e,{bufferGPU:i}=t.get(s),n=e.array,o=n.byteLength;r.bindBuffer(r.COPY_READ_BUFFER,i);const a=r.createBuffer();r.bindBuffer(r.COPY_WRITE_BUFFER,a),r.bufferData(r.COPY_WRITE_BUFFER,o,r.STREAM_READ),r.copyBufferSubData(r.COPY_READ_BUFFER,r.COPY_WRITE_BUFFER,0,0,o),await t.utils._clientWaitAsync();const u=new e.array.constructor(n.length);return r.bindBuffer(r.COPY_WRITE_BUFFER,a),r.getBufferSubData(r.COPY_WRITE_BUFFER,0,u),r.deleteBuffer(a),r.bindBuffer(r.COPY_READ_BUFFER,null),r.bindBuffer(r.COPY_WRITE_BUFFER,null),u.buffer}_createBuffer(e,t,r,s){const i=e.createBuffer();return e.bindBuffer(t,i),e.bufferData(t,r,s),e.bindBuffer(t,null),i}}let U_,P_,I_=!1;class D_{constructor(e){this.backend=e,this.gl=this.backend.gl,this.enabled={},this.currentFlipSided=null,this.currentCullFace=null,this.currentProgram=null,this.currentBlendingEnabled=!1,this.currentBlending=null,this.currentBlendSrc=null,this.currentBlendDst=null,this.currentBlendSrcAlpha=null,this.currentBlendDstAlpha=null,this.currentPremultipledAlpha=null,this.currentPolygonOffsetFactor=null,this.currentPolygonOffsetUnits=null,this.currentColorMask=null,this.currentDepthFunc=null,this.currentDepthMask=null,this.currentStencilFunc=null,this.currentStencilRef=null,this.currentStencilFuncMask=null,this.currentStencilFail=null,this.currentStencilZFail=null,this.currentStencilZPass=null,this.currentStencilMask=null,this.currentLineWidth=null,this.currentClippingPlanes=0,this.currentBoundFramebuffers={},this.currentDrawbuffers=new WeakMap,this.maxTextures=this.gl.getParameter(this.gl.MAX_TEXTURE_IMAGE_UNITS),this.currentTextureSlot=null,this.currentBoundTextures={},this.currentBoundBufferBases={},!1===I_&&(this._init(this.gl),I_=!0)}_init(e){U_={[Je]:e.FUNC_ADD,[et]:e.FUNC_SUBTRACT,[tt]:e.FUNC_REVERSE_SUBTRACT},P_={[rt]:e.ZERO,[st]:e.ONE,[it]:e.SRC_COLOR,[nt]:e.SRC_ALPHA,[ot]:e.SRC_ALPHA_SATURATE,[at]:e.DST_COLOR,[ut]:e.DST_ALPHA,[lt]:e.ONE_MINUS_SRC_COLOR,[dt]:e.ONE_MINUS_SRC_ALPHA,[ct]:e.ONE_MINUS_DST_COLOR,[ht]:e.ONE_MINUS_DST_ALPHA}}enable(e){const{enabled:t}=this;!0!==t[e]&&(this.gl.enable(e),t[e]=!0)}disable(e){const{enabled:t}=this;!1!==t[e]&&(this.gl.disable(e),t[e]=!1)}setFlipSided(e){if(this.currentFlipSided!==e){const{gl:t}=this;e?t.frontFace(t.CW):t.frontFace(t.CCW),this.currentFlipSided=e}}setCullFace(e){const{gl:t}=this;e!==pt?(this.enable(t.CULL_FACE),e!==this.currentCullFace&&(e===gt?t.cullFace(t.BACK):e===mt?t.cullFace(t.FRONT):t.cullFace(t.FRONT_AND_BACK))):this.disable(t.CULL_FACE),this.currentCullFace=e}setLineWidth(e){const{currentLineWidth:t,gl:r}=this;e!==t&&(r.lineWidth(e),this.currentLineWidth=e)}setBlending(e,t,r,s,i,n,o,a){const{gl:u}=this;if(e!==L){if(!1===this.currentBlendingEnabled&&(this.enable(u.BLEND),this.currentBlendingEnabled=!0),e===ft)i=i||t,n=n||r,o=o||s,t===this.currentBlendEquation&&i===this.currentBlendEquationAlpha||(u.blendEquationSeparate(U_[t],U_[i]),this.currentBlendEquation=t,this.currentBlendEquationAlpha=i),r===this.currentBlendSrc&&s===this.currentBlendDst&&n===this.currentBlendSrcAlpha&&o===this.currentBlendDstAlpha||(u.blendFuncSeparate(P_[r],P_[s],P_[n],P_[o]),this.currentBlendSrc=r,this.currentBlendDst=s,this.currentBlendSrcAlpha=n,this.currentBlendDstAlpha=o),this.currentBlending=e,this.currentPremultipledAlpha=!1;else if(e!==this.currentBlending||a!==this.currentPremultipledAlpha){if(this.currentBlendEquation===Je&&this.currentBlendEquationAlpha===Je||(u.blendEquation(u.FUNC_ADD),this.currentBlendEquation=Je,this.currentBlendEquationAlpha=Je),a)switch(e){case U:u.blendFuncSeparate(u.ONE,u.ONE_MINUS_SRC_ALPHA,u.ONE,u.ONE_MINUS_SRC_ALPHA);break;case xt:u.blendFunc(u.ONE,u.ONE);break;case bt:u.blendFuncSeparate(u.ZERO,u.ONE_MINUS_SRC_COLOR,u.ZERO,u.ONE);break;case yt:u.blendFuncSeparate(u.ZERO,u.SRC_COLOR,u.ZERO,u.SRC_ALPHA);break;default:console.error("THREE.WebGLState: Invalid blending: ",e)}else switch(e){case U:u.blendFuncSeparate(u.SRC_ALPHA,u.ONE_MINUS_SRC_ALPHA,u.ONE,u.ONE_MINUS_SRC_ALPHA);break;case xt:u.blendFunc(u.SRC_ALPHA,u.ONE);break;case bt:u.blendFuncSeparate(u.ZERO,u.ONE_MINUS_SRC_COLOR,u.ZERO,u.ONE);break;case yt:u.blendFunc(u.ZERO,u.SRC_COLOR);break;default:console.error("THREE.WebGLState: Invalid blending: ",e)}this.currentBlendSrc=null,this.currentBlendDst=null,this.currentBlendSrcAlpha=null,this.currentBlendDstAlpha=null,this.currentBlending=e,this.currentPremultipledAlpha=a}}else!0===this.currentBlendingEnabled&&(this.disable(u.BLEND),this.currentBlendingEnabled=!1)}setColorMask(e){this.currentColorMask!==e&&(this.gl.colorMask(e,e,e,e),this.currentColorMask=e)}setDepthTest(e){const{gl:t}=this;e?this.enable(t.DEPTH_TEST):this.disable(t.DEPTH_TEST)}setDepthMask(e){this.currentDepthMask!==e&&(this.gl.depthMask(e),this.currentDepthMask=e)}setDepthFunc(e){if(this.currentDepthFunc!==e){const{gl:t}=this;switch(e){case Ct:t.depthFunc(t.NEVER);break;case Rt:t.depthFunc(t.ALWAYS);break;case At:t.depthFunc(t.LESS);break;case St:t.depthFunc(t.LEQUAL);break;case Nt:t.depthFunc(t.EQUAL);break;case vt:t.depthFunc(t.GEQUAL);break;case _t:t.depthFunc(t.GREATER);break;case Tt:t.depthFunc(t.NOTEQUAL);break;default:t.depthFunc(t.LEQUAL)}this.currentDepthFunc=e}}setStencilTest(e){const{gl:t}=this;e?this.enable(t.STENCIL_TEST):this.disable(t.STENCIL_TEST)}setStencilMask(e){this.currentStencilMask!==e&&(this.gl.stencilMask(e),this.currentStencilMask=e)}setStencilFunc(e,t,r){this.currentStencilFunc===e&&this.currentStencilRef===t&&this.currentStencilFuncMask===r||(this.gl.stencilFunc(e,t,r),this.currentStencilFunc=e,this.currentStencilRef=t,this.currentStencilFuncMask=r)}setStencilOp(e,t,r){this.currentStencilFail===e&&this.currentStencilZFail===t&&this.currentStencilZPass===r||(this.gl.stencilOp(e,t,r),this.currentStencilFail=e,this.currentStencilZFail=t,this.currentStencilZPass=r)}setMaterial(e,t,r){const{gl:s}=this;e.side===le?this.disable(s.CULL_FACE):this.enable(s.CULL_FACE);let i=e.side===T;t&&(i=!i),this.setFlipSided(i),e.blending===U&&!1===e.transparent?this.setBlending(L):this.setBlending(e.blending,e.blendEquation,e.blendSrc,e.blendDst,e.blendEquationAlpha,e.blendSrcAlpha,e.blendDstAlpha,e.premultipliedAlpha),this.setDepthFunc(e.depthFunc),this.setDepthTest(e.depthTest),this.setDepthMask(e.depthWrite),this.setColorMask(e.colorWrite);const n=e.stencilWrite;if(this.setStencilTest(n),n&&(this.setStencilMask(e.stencilWriteMask),this.setStencilFunc(e.stencilFunc,e.stencilRef,e.stencilFuncMask),this.setStencilOp(e.stencilFail,e.stencilZFail,e.stencilZPass)),this.setPolygonOffset(e.polygonOffset,e.polygonOffsetFactor,e.polygonOffsetUnits),!0===e.alphaToCoverage&&this.backend.renderer.samples>1?this.enable(s.SAMPLE_ALPHA_TO_COVERAGE):this.disable(s.SAMPLE_ALPHA_TO_COVERAGE),r>0&&this.currentClippingPlanes!==r){const e=12288;for(let t=0;t<8;t++)t<r?this.enable(e+t):this.disable(e+t)}}setPolygonOffset(e,t,r){const{gl:s}=this;e?(this.enable(s.POLYGON_OFFSET_FILL),this.currentPolygonOffsetFactor===t&&this.currentPolygonOffsetUnits===r||(s.polygonOffset(t,r),this.currentPolygonOffsetFactor=t,this.currentPolygonOffsetUnits=r)):this.disable(s.POLYGON_OFFSET_FILL)}useProgram(e){return this.currentProgram!==e&&(this.gl.useProgram(e),this.currentProgram=e,!0)}bindFramebuffer(e,t){const{gl:r,currentBoundFramebuffers:s}=this;return s[e]!==t&&(r.bindFramebuffer(e,t),s[e]=t,e===r.DRAW_FRAMEBUFFER&&(s[r.FRAMEBUFFER]=t),e===r.FRAMEBUFFER&&(s[r.DRAW_FRAMEBUFFER]=t),!0)}drawBuffers(e,t){const{gl:r}=this;let s=[],i=!1;if(null!==e.textures){s=this.currentDrawbuffers.get(t),void 0===s&&(s=[],this.currentDrawbuffers.set(t,s));const n=e.textures;if(s.length!==n.length||s[0]!==r.COLOR_ATTACHMENT0){for(let e=0,t=n.length;e<t;e++)s[e]=r.COLOR_ATTACHMENT0+e;s.length=n.length,i=!0}}else s[0]!==r.BACK&&(s[0]=r.BACK,i=!0);i&&r.drawBuffers(s)}activeTexture(e){const{gl:t,currentTextureSlot:r,maxTextures:s}=this;void 0===e&&(e=t.TEXTURE0+s-1),r!==e&&(t.activeTexture(e),this.currentTextureSlot=e)}bindTexture(e,t,r){const{gl:s,currentTextureSlot:i,currentBoundTextures:n,maxTextures:o}=this;void 0===r&&(r=null===i?s.TEXTURE0+o-1:i);let a=n[r];void 0===a&&(a={type:void 0,texture:void 0},n[r]=a),a.type===e&&a.texture===t||(i!==r&&(s.activeTexture(r),this.currentTextureSlot=r),s.bindTexture(e,t),a.type=e,a.texture=t)}bindBufferBase(e,t,r){const{gl:s}=this,i=`${e}-${t}`;return this.currentBoundBufferBases[i]!==r&&(s.bindBufferBase(e,t,r),this.currentBoundBufferBases[i]=r,!0)}unbindTexture(){const{gl:e,currentTextureSlot:t,currentBoundTextures:r}=this,s=r[t];void 0!==s&&void 0!==s.type&&(e.bindTexture(s.type,null),s.type=void 0,s.texture=void 0)}}class L_{constructor(e){this.backend=e,this.gl=this.backend.gl,this.extensions=e.extensions}convert(e,t=y){const{gl:r,extensions:s}=this;let i;if(e===pe)return r.UNSIGNED_BYTE;if(e===Et)return r.UNSIGNED_SHORT_4_4_4_4;if(e===wt)return r.UNSIGNED_SHORT_5_5_5_1;if(e===Mt)return r.UNSIGNED_INT_5_9_9_9_REV;if(e===Ye)return r.BYTE;if(e===Qe)return r.SHORT;if(e===Xe)return r.UNSIGNED_SHORT;if(e===x)return r.INT;if(e===b)return r.UNSIGNED_INT;if(e===E)return r.FLOAT;if(e===ye)return r.HALF_FLOAT;if(e===Bt)return r.ALPHA;if(e===qe)return r.RGB;if(e===Ue)return r.RGBA;if(e===Ft)return r.LUMINANCE;if(e===Ut)return r.LUMINANCE_ALPHA;if(e===ce)return r.DEPTH_COMPONENT;if(e===de)return r.DEPTH_STENCIL;if(e===He)return r.RED;if(e===$e)return r.RED_INTEGER;if(e===Ee)return r.RG;if(e===We)return r.RG_INTEGER;if(e===Ke)return r.RGBA_INTEGER;if(e===Pt||e===It||e===Dt||e===Lt)if(t===Ve){if(i=s.get("WEBGL_compressed_texture_s3tc_srgb"),null===i)return null;if(e===Pt)return i.COMPRESSED_SRGB_S3TC_DXT1_EXT;if(e===It)return i.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT;if(e===Dt)return i.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT;if(e===Lt)return i.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT}else{if(i=s.get("WEBGL_compressed_texture_s3tc"),null===i)return null;if(e===Pt)return i.COMPRESSED_RGB_S3TC_DXT1_EXT;if(e===It)return i.COMPRESSED_RGBA_S3TC_DXT1_EXT;if(e===Dt)return i.COMPRESSED_RGBA_S3TC_DXT3_EXT;if(e===Lt)return i.COMPRESSED_RGBA_S3TC_DXT5_EXT}if(e===Vt||e===Ot||e===Gt||e===kt){if(i=s.get("WEBGL_compressed_texture_pvrtc"),null===i)return null;if(e===Vt)return i.COMPRESSED_RGB_PVRTC_4BPPV1_IMG;if(e===Ot)return i.COMPRESSED_RGB_PVRTC_2BPPV1_IMG;if(e===Gt)return i.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG;if(e===kt)return i.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG}if(e===zt||e===$t||e===Ht){if(i=s.get("WEBGL_compressed_texture_etc"),null===i)return null;if(e===zt||e===$t)return t===Ve?i.COMPRESSED_SRGB8_ETC2:i.COMPRESSED_RGB8_ETC2;if(e===Ht)return t===Ve?i.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC:i.COMPRESSED_RGBA8_ETC2_EAC}if(e===Wt||e===jt||e===qt||e===Kt||e===Xt||e===Yt||e===Qt||e===Zt||e===Jt||e===er||e===tr||e===rr||e===sr||e===ir){if(i=s.get("WEBGL_compressed_texture_astc"),null===i)return null;if(e===Wt)return t===Ve?i.COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR:i.COMPRESSED_RGBA_ASTC_4x4_KHR;if(e===jt)return t===Ve?i.COMPRESSED_SRGB8_ALPHA8_ASTC_5x4_KHR:i.COMPRESSED_RGBA_ASTC_5x4_KHR;if(e===qt)return t===Ve?i.COMPRESSED_SRGB8_ALPHA8_ASTC_5x5_KHR:i.COMPRESSED_RGBA_ASTC_5x5_KHR;if(e===Kt)return t===Ve?i.COMPRESSED_SRGB8_ALPHA8_ASTC_6x5_KHR:i.COMPRESSED_RGBA_ASTC_6x5_KHR;if(e===Xt)return t===Ve?i.COMPRESSED_SRGB8_ALPHA8_ASTC_6x6_KHR:i.COMPRESSED_RGBA_ASTC_6x6_KHR;if(e===Yt)return t===Ve?i.COMPRESSED_SRGB8_ALPHA8_ASTC_8x5_KHR:i.COMPRESSED_RGBA_ASTC_8x5_KHR;if(e===Qt)return t===Ve?i.COMPRESSED_SRGB8_ALPHA8_ASTC_8x6_KHR:i.COMPRESSED_RGBA_ASTC_8x6_KHR;if(e===Zt)return t===Ve?i.COMPRESSED_SRGB8_ALPHA8_ASTC_8x8_KHR:i.COMPRESSED_RGBA_ASTC_8x8_KHR;if(e===Jt)return t===Ve?i.COMPRESSED_SRGB8_ALPHA8_ASTC_10x5_KHR:i.COMPRESSED_RGBA_ASTC_10x5_KHR;if(e===er)return t===Ve?i.COMPRESSED_SRGB8_ALPHA8_ASTC_10x6_KHR:i.COMPRESSED_RGBA_ASTC_10x6_KHR;if(e===tr)return t===Ve?i.COMPRESSED_SRGB8_ALPHA8_ASTC_10x8_KHR:i.COMPRESSED_RGBA_ASTC_10x8_KHR;if(e===rr)return t===Ve?i.COMPRESSED_SRGB8_ALPHA8_ASTC_10x10_KHR:i.COMPRESSED_RGBA_ASTC_10x10_KHR;if(e===sr)return t===Ve?i.COMPRESSED_SRGB8_ALPHA8_ASTC_12x10_KHR:i.COMPRESSED_RGBA_ASTC_12x10_KHR;if(e===ir)return t===Ve?i.COMPRESSED_SRGB8_ALPHA8_ASTC_12x12_KHR:i.COMPRESSED_RGBA_ASTC_12x12_KHR}if(e===nr){if(i=s.get("EXT_texture_compression_bptc"),null===i)return null;if(e===nr)return t===Ve?i.COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT:i.COMPRESSED_RGBA_BPTC_UNORM_EXT}if(e===or||e===ar||e===ur||e===lr){if(i=s.get("EXT_texture_compression_rgtc"),null===i)return null;if(e===nr)return i.COMPRESSED_RED_RGTC1_EXT;if(e===ar)return i.COMPRESSED_SIGNED_RED_RGTC1_EXT;if(e===ur)return i.COMPRESSED_RED_GREEN_RGTC2_EXT;if(e===lr)return i.COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT}return e===he?r.UNSIGNED_INT_24_8:void 0!==r[e]?r[e]:null}_clientWaitAsync(){const{gl:e}=this,t=e.fenceSync(e.SYNC_GPU_COMMANDS_COMPLETE,0);return e.flush(),new Promise(((r,s)=>{!function i(){const n=e.clientWaitSync(t,e.SYNC_FLUSH_COMMANDS_BIT,0);if(n===e.WAIT_FAILED)return e.deleteSync(t),void s();n!==e.TIMEOUT_EXPIRED?(e.deleteSync(t),r()):requestAnimationFrame(i)}()}))}}let V_,O_,G_,k_=!1;class z_{constructor(e){this.backend=e,this.gl=e.gl,this.extensions=e.extensions,this.defaultTextures={},!1===k_&&(this._init(this.gl),k_=!0)}_init(e){V_={[dr]:e.REPEAT,[cr]:e.CLAMP_TO_EDGE,[hr]:e.MIRRORED_REPEAT},O_={[pr]:e.NEAREST,[gr]:e.NEAREST_MIPMAP_NEAREST,[Ie]:e.NEAREST_MIPMAP_LINEAR,[$]:e.LINEAR,[Pe]:e.LINEAR_MIPMAP_NEAREST,[M]:e.LINEAR_MIPMAP_LINEAR},G_={[mr]:e.NEVER,[fr]:e.ALWAYS,[Re]:e.LESS,[yr]:e.LEQUAL,[br]:e.EQUAL,[xr]:e.GEQUAL,[Tr]:e.GREATER,[_r]:e.NOTEQUAL}}filterFallback(e){const{gl:t}=this;return e===pr||e===gr||e===Ie?t.NEAREST:t.LINEAR}getGLTextureType(e){const{gl:t}=this;let r;return r=!0===e.isCubeTexture?t.TEXTURE_CUBE_MAP:!0===e.isDataArrayTexture||!0===e.isCompressedArrayTexture?t.TEXTURE_2D_ARRAY:!0===e.isData3DTexture?t.TEXTURE_3D:t.TEXTURE_2D,r}getInternalFormat(e,t,r,s,i=!1){const{gl:n,extensions:o}=this;if(null!==e){if(void 0!==n[e])return n[e];console.warn("THREE.WebGLRenderer: Attempt to use non-existing WebGL internal format '"+e+"'")}let a=t;return t===n.RED&&(r===n.FLOAT&&(a=n.R32F),r===n.HALF_FLOAT&&(a=n.R16F),r===n.UNSIGNED_BYTE&&(a=n.R8),r===n.UNSIGNED_SHORT&&(a=n.R16),r===n.UNSIGNED_INT&&(a=n.R32UI),r===n.BYTE&&(a=n.R8I),r===n.SHORT&&(a=n.R16I),r===n.INT&&(a=n.R32I)),t===n.RED_INTEGER&&(r===n.UNSIGNED_BYTE&&(a=n.R8UI),r===n.UNSIGNED_SHORT&&(a=n.R16UI),r===n.UNSIGNED_INT&&(a=n.R32UI),r===n.BYTE&&(a=n.R8I),r===n.SHORT&&(a=n.R16I),r===n.INT&&(a=n.R32I)),t===n.RG&&(r===n.FLOAT&&(a=n.RG32F),r===n.HALF_FLOAT&&(a=n.RG16F),r===n.UNSIGNED_BYTE&&(a=n.RG8),r===n.UNSIGNED_SHORT&&(a=n.RG16),r===n.UNSIGNED_INT&&(a=n.RG32UI),r===n.BYTE&&(a=n.RG8I),r===n.SHORT&&(a=n.RG16I),r===n.INT&&(a=n.RG32I)),t===n.RG_INTEGER&&(r===n.UNSIGNED_BYTE&&(a=n.RG8UI),r===n.UNSIGNED_SHORT&&(a=n.RG16UI),r===n.UNSIGNED_INT&&(a=n.RG32UI),r===n.BYTE&&(a=n.RG8I),r===n.SHORT&&(a=n.RG16I),r===n.INT&&(a=n.RG32I)),t===n.RGB&&(r===n.FLOAT&&(a=n.RGB32F),r===n.HALF_FLOAT&&(a=n.RGB16F),r===n.UNSIGNED_BYTE&&(a=n.RGB8),r===n.UNSIGNED_SHORT&&(a=n.RGB16),r===n.UNSIGNED_INT&&(a=n.RGB32UI),r===n.BYTE&&(a=n.RGB8I),r===n.SHORT&&(a=n.RGB16I),r===n.INT&&(a=n.RGB32I),r===n.UNSIGNED_BYTE&&(a=s===Ve&&!1===i?n.SRGB8:n.RGB8),r===n.UNSIGNED_SHORT_5_6_5&&(a=n.RGB565),r===n.UNSIGNED_SHORT_5_5_5_1&&(a=n.RGB5_A1),r===n.UNSIGNED_SHORT_4_4_4_4&&(a=n.RGB4),r===n.UNSIGNED_INT_5_9_9_9_REV&&(a=n.RGB9_E5)),t===n.RGB_INTEGER&&(r===n.UNSIGNED_BYTE&&(a=n.RGB8UI),r===n.UNSIGNED_SHORT&&(a=n.RGB16UI),r===n.UNSIGNED_INT&&(a=n.RGB32UI),r===n.BYTE&&(a=n.RGB8I),r===n.SHORT&&(a=n.RGB16I),r===n.INT&&(a=n.RGB32I)),t===n.RGBA&&(r===n.FLOAT&&(a=n.RGBA32F),r===n.HALF_FLOAT&&(a=n.RGBA16F),r===n.UNSIGNED_BYTE&&(a=n.RGBA8),r===n.UNSIGNED_SHORT&&(a=n.RGBA16),r===n.UNSIGNED_INT&&(a=n.RGBA32UI),r===n.BYTE&&(a=n.RGBA8I),r===n.SHORT&&(a=n.RGBA16I),r===n.INT&&(a=n.RGBA32I),r===n.UNSIGNED_BYTE&&(a=s===Ve&&!1===i?n.SRGB8_ALPHA8:n.RGBA8),r===n.UNSIGNED_SHORT_4_4_4_4&&(a=n.RGBA4),r===n.UNSIGNED_SHORT_5_5_5_1&&(a=n.RGB5_A1)),t===n.RGBA_INTEGER&&(r===n.UNSIGNED_BYTE&&(a=n.RGBA8UI),r===n.UNSIGNED_SHORT&&(a=n.RGBA16UI),r===n.UNSIGNED_INT&&(a=n.RGBA32UI),r===n.BYTE&&(a=n.RGBA8I),r===n.SHORT&&(a=n.RGBA16I),r===n.INT&&(a=n.RGBA32I)),t===n.DEPTH_COMPONENT&&(r===n.UNSIGNED_INT&&(a=n.DEPTH24_STENCIL8),r===n.FLOAT&&(a=n.DEPTH_COMPONENT32F)),t===n.DEPTH_STENCIL&&r===n.UNSIGNED_INT_24_8&&(a=n.DEPTH24_STENCIL8),a!==n.R16F&&a!==n.R32F&&a!==n.RG16F&&a!==n.RG32F&&a!==n.RGBA16F&&a!==n.RGBA32F||o.get("EXT_color_buffer_float"),a}setTextureParameters(e,t){const{gl:r,extensions:s,backend:i}=this;r.pixelStorei(r.UNPACK_FLIP_Y_WEBGL,t.flipY),r.pixelStorei(r.UNPACK_PREMULTIPLY_ALPHA_WEBGL,t.premultiplyAlpha),r.pixelStorei(r.UNPACK_ALIGNMENT,t.unpackAlignment),r.pixelStorei(r.UNPACK_COLORSPACE_CONVERSION_WEBGL,r.NONE),r.texParameteri(e,r.TEXTURE_WRAP_S,V_[t.wrapS]),r.texParameteri(e,r.TEXTURE_WRAP_T,V_[t.wrapT]),e!==r.TEXTURE_3D&&e!==r.TEXTURE_2D_ARRAY||r.texParameteri(e,r.TEXTURE_WRAP_R,V_[t.wrapR]),r.texParameteri(e,r.TEXTURE_MAG_FILTER,O_[t.magFilter]);const n=void 0!==t.mipmaps&&t.mipmaps.length>0,o=t.minFilter===$&&n?M:t.minFilter;if(r.texParameteri(e,r.TEXTURE_MIN_FILTER,O_[o]),t.compareFunction&&(r.texParameteri(e,r.TEXTURE_COMPARE_MODE,r.COMPARE_REF_TO_TEXTURE),r.texParameteri(e,r.TEXTURE_COMPARE_FUNC,G_[t.compareFunction])),!0===s.has("EXT_texture_filter_anisotropic")){if(t.magFilter===pr)return;if(t.minFilter!==Ie&&t.minFilter!==M)return;if(t.type===E&&!1===s.has("OES_texture_float_linear"))return;if(t.anisotropy>1){const n=s.get("EXT_texture_filter_anisotropic");r.texParameterf(e,n.TEXTURE_MAX_ANISOTROPY_EXT,Math.min(t.anisotropy,i.getMaxAnisotropy()))}}}createDefaultTexture(e){const{gl:t,backend:r,defaultTextures:s}=this,i=this.getGLTextureType(e);let n=s[i];void 0===n&&(n=t.createTexture(),r.state.bindTexture(i,n),t.texParameteri(i,t.TEXTURE_MIN_FILTER,t.NEAREST),t.texParameteri(i,t.TEXTURE_MAG_FILTER,t.NEAREST),s[i]=n),r.set(e,{textureGPU:n,glTextureType:i,isDefault:!0})}createTexture(e,t){const{gl:r,backend:s}=this,{levels:i,width:n,height:o,depth:a}=t,u=s.utils.convert(e.format,e.colorSpace),l=s.utils.convert(e.type),d=this.getInternalFormat(e.internalFormat,u,l,e.colorSpace,e.isVideoTexture),c=r.createTexture(),h=this.getGLTextureType(e);s.state.bindTexture(h,c),this.setTextureParameters(h,e),e.isDataArrayTexture||e.isCompressedArrayTexture?r.texStorage3D(r.TEXTURE_2D_ARRAY,i,d,n,o,a):e.isData3DTexture?r.texStorage3D(r.TEXTURE_3D,i,d,n,o,a):e.isVideoTexture||r.texStorage2D(h,i,d,n,o),s.set(e,{textureGPU:c,glTextureType:h,glFormat:u,glType:l,glInternalFormat:d})}copyBufferToTexture(e,t){const{gl:r,backend:s}=this,{textureGPU:i,glTextureType:n,glFormat:o,glType:a}=s.get(t),{width:u,height:l}=t.source.data;r.bindBuffer(r.PIXEL_UNPACK_BUFFER,e),s.state.bindTexture(n,i),r.pixelStorei(r.UNPACK_FLIP_Y_WEBGL,!1),r.pixelStorei(r.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),r.texSubImage2D(n,0,0,0,u,l,o,a,0),r.bindBuffer(r.PIXEL_UNPACK_BUFFER,null),s.state.unbindTexture()}updateTexture(e,t){const{gl:r}=this,{width:s,height:i}=t,{textureGPU:n,glTextureType:o,glFormat:a,glType:u,glInternalFormat:l}=this.backend.get(e);if(e.isRenderTargetTexture||void 0===n)return;const d=e=>e.isDataTexture?e.image.data:"undefined"!=typeof HTMLImageElement&&e instanceof HTMLImageElement||"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement||"undefined"!=typeof ImageBitmap&&e instanceof ImageBitmap||e instanceof OffscreenCanvas?e:e.data;if(this.backend.state.bindTexture(o,n),this.setTextureParameters(o,e),e.isCompressedTexture){const s=e.mipmaps,i=t.image;for(let t=0;t<s.length;t++){const n=s[t];e.isCompressedArrayTexture?e.format!==r.RGBA?null!==a?r.compressedTexSubImage3D(r.TEXTURE_2D_ARRAY,t,0,0,0,n.width,n.height,i.depth,a,n.data):console.warn("THREE.WebGLRenderer: Attempt to load unsupported compressed texture format in .uploadTexture()"):r.texSubImage3D(r.TEXTURE_2D_ARRAY,t,0,0,0,n.width,n.height,i.depth,a,u,n.data):null!==a?r.compressedTexSubImage2D(r.TEXTURE_2D,t,0,0,n.width,n.height,a,n.data):console.warn("Unsupported compressed texture format")}}else if(e.isCubeTexture){const e=t.images;for(let t=0;t<6;t++){const n=d(e[t]);r.texSubImage2D(r.TEXTURE_CUBE_MAP_POSITIVE_X+t,0,0,0,s,i,a,u,n)}}else if(e.isDataArrayTexture){const e=t.image;r.texSubImage3D(r.TEXTURE_2D_ARRAY,0,0,0,0,e.width,e.height,e.depth,a,u,e.data)}else if(e.isData3DTexture){const e=t.image;r.texSubImage3D(r.TEXTURE_3D,0,0,0,0,e.width,e.height,e.depth,a,u,e.data)}else if(e.isVideoTexture)e.update(),r.texImage2D(o,0,l,a,u,t.image);else{const e=d(t.image);r.texSubImage2D(o,0,0,0,s,i,a,u,e)}}generateMipmaps(e){const{gl:t,backend:r}=this,{textureGPU:s,glTextureType:i}=r.get(e);r.state.bindTexture(i,s),t.generateMipmap(i)}deallocateRenderBuffers(e){const{gl:t,backend:r}=this;if(e){const s=r.get(e);if(s.renderBufferStorageSetup=void 0,s.framebuffers){for(const e in s.framebuffers)t.deleteFramebuffer(s.framebuffers[e]);delete s.framebuffers}if(s.depthRenderbuffer&&(t.deleteRenderbuffer(s.depthRenderbuffer),delete s.depthRenderbuffer),s.stencilRenderbuffer&&(t.deleteRenderbuffer(s.stencilRenderbuffer),delete s.stencilRenderbuffer),s.msaaFrameBuffer&&(t.deleteFramebuffer(s.msaaFrameBuffer),delete s.msaaFrameBuffer),s.msaaRenderbuffers){for(let e=0;e<s.msaaRenderbuffers.length;e++)t.deleteRenderbuffer(s.msaaRenderbuffers[e]);delete s.msaaRenderbuffers}}}destroyTexture(e){const{gl:t,backend:r}=this,{textureGPU:s,renderTarget:i}=r.get(e);this.deallocateRenderBuffers(i),t.deleteTexture(s),r.delete(e)}copyTextureToTexture(e,t,r=null,s=null,i=0){const{gl:n,backend:o}=this,{state:a}=this.backend,{textureGPU:u,glTextureType:l,glType:d,glFormat:c}=o.get(t);let h,p,g,m,f,y;null!==r?(h=r.max.x-r.min.x,p=r.max.y-r.min.y,g=r.min.x,m=r.min.y):(h=e.image.width,p=e.image.height,g=0,m=0),null!==s?(f=s.x,y=s.y):(f=0,y=0),a.bindTexture(l,u),n.pixelStorei(n.UNPACK_ALIGNMENT,t.unpackAlignment),n.pixelStorei(n.UNPACK_FLIP_Y_WEBGL,t.flipY),n.pixelStorei(n.UNPACK_PREMULTIPLY_ALPHA_WEBGL,t.premultiplyAlpha),n.pixelStorei(n.UNPACK_ALIGNMENT,t.unpackAlignment);const b=n.getParameter(n.UNPACK_ROW_LENGTH),x=n.getParameter(n.UNPACK_IMAGE_HEIGHT),T=n.getParameter(n.UNPACK_SKIP_PIXELS),_=n.getParameter(n.UNPACK_SKIP_ROWS),v=n.getParameter(n.UNPACK_SKIP_IMAGES),N=e.isCompressedTexture?e.mipmaps[i]:e.image;if(n.pixelStorei(n.UNPACK_ROW_LENGTH,N.width),n.pixelStorei(n.UNPACK_IMAGE_HEIGHT,N.height),n.pixelStorei(n.UNPACK_SKIP_PIXELS,g),n.pixelStorei(n.UNPACK_SKIP_ROWS,m),e.isRenderTargetTexture||e.isDepthTexture){const r=o.get(e),s=o.get(t),i=o.get(r.renderTarget),u=o.get(s.renderTarget),l=i.framebuffers[r.cacheKey],d=u.framebuffers[s.cacheKey];a.bindFramebuffer(n.READ_FRAMEBUFFER,l),a.bindFramebuffer(n.DRAW_FRAMEBUFFER,d);let c=n.COLOR_BUFFER_BIT;e.isDepthTexture&&(c=n.DEPTH_BUFFER_BIT),n.blitFramebuffer(g,m,h,p,f,y,h,p,c,n.NEAREST),a.bindFramebuffer(n.READ_FRAMEBUFFER,null),a.bindFramebuffer(n.DRAW_FRAMEBUFFER,null)}else e.isDataTexture?n.texSubImage2D(n.TEXTURE_2D,i,f,y,h,p,c,d,N.data):e.isCompressedTexture?n.compressedTexSubImage2D(n.TEXTURE_2D,i,f,y,N.width,N.height,c,N.data):n.texSubImage2D(n.TEXTURE_2D,i,f,y,h,p,c,d,N);n.pixelStorei(n.UNPACK_ROW_LENGTH,b),n.pixelStorei(n.UNPACK_IMAGE_HEIGHT,x),n.pixelStorei(n.UNPACK_SKIP_PIXELS,T),n.pixelStorei(n.UNPACK_SKIP_ROWS,_),n.pixelStorei(n.UNPACK_SKIP_IMAGES,v),0===i&&t.generateMipmaps&&n.generateMipmap(n.TEXTURE_2D),a.unbindTexture()}copyFramebufferToTexture(e,t,r){const{gl:s}=this,{state:i}=this.backend,{textureGPU:n}=this.backend.get(e),{x:o,y:a,z:u,w:l}=r,d=!0===e.isDepthTexture||t.renderTarget&&t.renderTarget.samples>0,c=t.renderTarget?t.renderTarget.height:this.backend.getDrawingBufferSize().y;if(d){const r=0!==o||0!==a;let d,h;if(!0===e.isDepthTexture?(d=s.DEPTH_BUFFER_BIT,h=s.DEPTH_ATTACHMENT,t.stencil&&(d|=s.STENCIL_BUFFER_BIT)):(d=s.COLOR_BUFFER_BIT,h=s.COLOR_ATTACHMENT0),r){const e=this.backend.get(t.renderTarget),r=e.framebuffers[t.getCacheKey()],h=e.msaaFrameBuffer;i.bindFramebuffer(s.DRAW_FRAMEBUFFER,r),i.bindFramebuffer(s.READ_FRAMEBUFFER,h);const p=c-a-l;s.blitFramebuffer(o,p,o+u,p+l,o,p,o+u,p+l,d,s.NEAREST),i.bindFramebuffer(s.READ_FRAMEBUFFER,r),i.bindTexture(s.TEXTURE_2D,n),s.copyTexSubImage2D(s.TEXTURE_2D,0,0,0,o,p,u,l),i.unbindTexture()}else{const e=s.createFramebuffer();i.bindFramebuffer(s.DRAW_FRAMEBUFFER,e),s.framebufferTexture2D(s.DRAW_FRAMEBUFFER,h,s.TEXTURE_2D,n,0),s.blitFramebuffer(0,0,u,l,0,0,u,l,d,s.NEAREST),s.deleteFramebuffer(e)}}else i.bindTexture(s.TEXTURE_2D,n),s.copyTexSubImage2D(s.TEXTURE_2D,0,0,0,o,c-l-a,u,l),i.unbindTexture();e.generateMipmaps&&this.generateMipmaps(e),this.backend._setFramebuffer(t)}setupRenderBufferStorage(e,t){const{gl:r}=this,s=t.renderTarget,{samples:i,depthTexture:n,depthBuffer:o,stencilBuffer:a,width:u,height:l}=s;if(r.bindRenderbuffer(r.RENDERBUFFER,e),o&&!a){let t=r.DEPTH_COMPONENT24;i>0?(n&&n.isDepthTexture&&n.type===r.FLOAT&&(t=r.DEPTH_COMPONENT32F),r.renderbufferStorageMultisample(r.RENDERBUFFER,i,t,u,l)):r.renderbufferStorage(r.RENDERBUFFER,t,u,l),r.framebufferRenderbuffer(r.FRAMEBUFFER,r.DEPTH_ATTACHMENT,r.RENDERBUFFER,e)}else o&&a&&(i>0?r.renderbufferStorageMultisample(r.RENDERBUFFER,i,r.DEPTH24_STENCIL8,u,l):r.renderbufferStorage(r.RENDERBUFFER,r.DEPTH_STENCIL,u,l),r.framebufferRenderbuffer(r.FRAMEBUFFER,r.DEPTH_STENCIL_ATTACHMENT,r.RENDERBUFFER,e))}async copyTextureToBuffer(e,t,r,s,i,n){const{backend:o,gl:a}=this,{textureGPU:u,glFormat:l,glType:d}=this.backend.get(e),c=a.createFramebuffer();a.bindFramebuffer(a.READ_FRAMEBUFFER,c);const h=e.isCubeTexture?a.TEXTURE_CUBE_MAP_POSITIVE_X+n:a.TEXTURE_2D;a.framebufferTexture2D(a.READ_FRAMEBUFFER,a.COLOR_ATTACHMENT0,h,u,0);const p=this._getTypedArrayType(d),g=s*i*this._getBytesPerTexel(d,l),m=a.createBuffer();a.bindBuffer(a.PIXEL_PACK_BUFFER,m),a.bufferData(a.PIXEL_PACK_BUFFER,g,a.STREAM_READ),a.readPixels(t,r,s,i,l,d,0),a.bindBuffer(a.PIXEL_PACK_BUFFER,null),await o.utils._clientWaitAsync();const f=new p(g/p.BYTES_PER_ELEMENT);return a.bindBuffer(a.PIXEL_PACK_BUFFER,m),a.getBufferSubData(a.PIXEL_PACK_BUFFER,0,f),a.bindBuffer(a.PIXEL_PACK_BUFFER,null),a.deleteFramebuffer(c),f}_getTypedArrayType(e){const{gl:t}=this;if(e===t.UNSIGNED_BYTE)return Uint8Array;if(e===t.UNSIGNED_SHORT_4_4_4_4)return Uint16Array;if(e===t.UNSIGNED_SHORT_5_5_5_1)return Uint16Array;if(e===t.UNSIGNED_SHORT_5_6_5)return Uint16Array;if(e===t.UNSIGNED_SHORT)return Uint16Array;if(e===t.UNSIGNED_INT)return Uint32Array;if(e===t.HALF_FLOAT)return Uint16Array;if(e===t.FLOAT)return Float32Array;throw new Error(`Unsupported WebGL type: ${e}`)}_getBytesPerTexel(e,t){const{gl:r}=this;let s=0;return e===r.UNSIGNED_BYTE&&(s=1),e!==r.UNSIGNED_SHORT_4_4_4_4&&e!==r.UNSIGNED_SHORT_5_5_5_1&&e!==r.UNSIGNED_SHORT_5_6_5&&e!==r.UNSIGNED_SHORT&&e!==r.HALF_FLOAT||(s=2),e!==r.UNSIGNED_INT&&e!==r.FLOAT||(s=4),t===r.RGBA?4*s:t===r.RGB?3*s:t===r.ALPHA?s:void 0}}class $_{constructor(e){this.backend=e,this.gl=this.backend.gl,this.availableExtensions=this.gl.getSupportedExtensions(),this.extensions={}}get(e){let t=this.extensions[e];return void 0===t&&(t=this.gl.getExtension(e),this.extensions[e]=t),t}has(e){return this.availableExtensions.includes(e)}}class H_{constructor(e){this.backend=e,this.maxAnisotropy=null}getMaxAnisotropy(){if(null!==this.maxAnisotropy)return this.maxAnisotropy;const e=this.backend.gl,t=this.backend.extensions;if(!0===t.has("EXT_texture_filter_anisotropic")){const r=t.get("EXT_texture_filter_anisotropic");this.maxAnisotropy=e.getParameter(r.MAX_TEXTURE_MAX_ANISOTROPY_EXT)}else this.maxAnisotropy=0;return this.maxAnisotropy}}const W_={WEBGL_multi_draw:"WEBGL_multi_draw",WEBGL_compressed_texture_astc:"texture-compression-astc",WEBGL_compressed_texture_etc:"texture-compression-etc2",WEBGL_compressed_texture_etc1:"texture-compression-etc1",WEBGL_compressed_texture_pvrtc:"texture-compression-pvrtc",WEBKIT_WEBGL_compressed_texture_pvrtc:"texture-compression-pvrtc",WEBGL_compressed_texture_s3tc:"texture-compression-bc",EXT_texture_compression_bptc:"texture-compression-bptc",EXT_disjoint_timer_query_webgl2:"timestamp-query"};class j_{constructor(e){this.gl=e.gl,this.extensions=e.extensions,this.info=e.renderer.info,this.mode=null,this.index=0,this.type=null,this.object=null}render(e,t){const{gl:r,mode:s,object:i,type:n,info:o,index:a}=this;0!==a?r.drawElements(s,t,n,e):r.drawArrays(s,e,t),o.update(i,t,s,1)}renderInstances(e,t,r){const{gl:s,mode:i,type:n,index:o,object:a,info:u}=this;0!==r&&(0!==o?s.drawElementsInstanced(i,t,n,e,r):s.drawArraysInstanced(i,e,t,r),u.update(a,t,i,r))}renderMultiDraw(e,t,r){const{extensions:s,mode:i,object:n,info:o}=this;if(0===r)return;const a=s.get("WEBGL_multi_draw");if(null===a)for(let s=0;s<r;s++)this.render(e[s],t[s]);else{0!==this.index?a.multiDrawElementsWEBGL(i,t,0,this.type,e,0,r):a.multiDrawArraysWEBGL(i,e,0,t,0,r);let s=0;for(let e=0;e<r;e++)s+=t[e];o.update(n,s,i,1)}}renderMultiDrawInstances(e,t,r,s){const{extensions:i,mode:n,object:o,info:a}=this;if(0===r)return;const u=i.get("WEBGL_multi_draw");if(null===u)for(let i=0;i<r;i++)this.renderInstances(e[i],t[i],s[i]);else{0!==this.index?u.multiDrawElementsInstancedWEBGL(n,t,0,this.type,e,0,s,0,r):u.multiDrawArraysInstancedWEBGL(n,e,0,t,0,s,0,r);let i=0;for(let e=0;e<r;e++)i+=t[e]*s[e];a.update(o,i,n,1)}}}class q_ extends w_{constructor(e={}){super(e),this.isWebGLBackend=!0,this.attributeUtils=null,this.extensions=null,this.capabilities=null,this.textureUtils=null,this.bufferRenderer=null,this.gl=null,this.state=null,this.utils=null,this.vaoCache={},this.transformFeedbackCache={},this.discard=!1,this.disjoint=null,this.parallel=null,this.trackTimestamp=!0===e.trackTimestamp,this._currentContext=null,this._knownBindings=new WeakSet}init(e){super.init(e);const t=this.parameters,r=void 0!==t.context?t.context:e.domElement.getContext("webgl2");function s(t){t.preventDefault();const r={api:"WebGL",message:t.statusMessage||"Unknown reason",reason:null,originalEvent:t};e.onDeviceLost(r)}this._onContextLost=s,e.domElement.addEventListener("webglcontextlost",s,!1),this.gl=r,this.extensions=new $_(this),this.capabilities=new H_(this),this.attributeUtils=new F_(this),this.textureUtils=new z_(this),this.bufferRenderer=new j_(this),this.state=new D_(this),this.utils=new L_(this),this.extensions.get("EXT_color_buffer_float"),this.extensions.get("WEBGL_clip_cull_distance"),this.extensions.get("OES_texture_float_linear"),this.extensions.get("EXT_color_buffer_half_float"),this.extensions.get("WEBGL_multisampled_render_to_texture"),this.extensions.get("WEBGL_render_shared_exponent"),this.extensions.get("WEBGL_multi_draw"),this.disjoint=this.extensions.get("EXT_disjoint_timer_query_webgl2"),this.parallel=this.extensions.get("KHR_parallel_shader_compile")}get coordinateSystem(){return u}async getArrayBufferAsync(e){return await this.attributeUtils.getArrayBufferAsync(e)}async waitForGPU(){await this.utils._clientWaitAsync()}initTimestampQuery(e){if(!this.disjoint||!this.trackTimestamp)return;const t=this.get(e);if(this.queryRunning)return t.queryQueue||(t.queryQueue=[]),void t.queryQueue.push(e);t.activeQuery&&(this.gl.endQuery(this.disjoint.TIME_ELAPSED_EXT),t.activeQuery=null),t.activeQuery=this.gl.createQuery(),null!==t.activeQuery&&(this.gl.beginQuery(this.disjoint.TIME_ELAPSED_EXT,t.activeQuery),this.queryRunning=!0)}prepareTimestampBuffer(e){if(!this.disjoint||!this.trackTimestamp)return;const t=this.get(e);if(t.activeQuery&&(this.gl.endQuery(this.disjoint.TIME_ELAPSED_EXT),t.gpuQueries||(t.gpuQueries=[]),t.gpuQueries.push({query:t.activeQuery}),t.activeQuery=null,this.queryRunning=!1,t.queryQueue&&t.queryQueue.length>0)){const e=t.queryQueue.shift();this.initTimestampQuery(e)}}async resolveTimestampAsync(e,t="render"){if(!this.disjoint||!this.trackTimestamp)return;const r=this.get(e);r.gpuQueries||(r.gpuQueries=[]);for(let e=0;e<r.gpuQueries.length;e++){const s=r.gpuQueries[e],i=this.gl.getQueryParameter(s.query,this.gl.QUERY_RESULT_AVAILABLE),n=this.gl.getParameter(this.disjoint.GPU_DISJOINT_EXT);if(i&&!n){const i=this.gl.getQueryParameter(s.query,this.gl.QUERY_RESULT),n=Number(i)/1e6;this.gl.deleteQuery(s.query),r.gpuQueries.splice(e,1),e--,this.renderer.info.updateTimestamp(t,n)}}}getContext(){return this.gl}beginRender(e){const{gl:t}=this,r=this.get(e);if(this.initTimestampQuery(e),r.previousContext=this._currentContext,this._currentContext=e,this._setFramebuffer(e),this.clear(e.clearColor,e.clearDepth,e.clearStencil,e,!1),e.viewport?this.updateViewport(e):t.viewport(0,0,t.drawingBufferWidth,t.drawingBufferHeight),e.scissor){const{x:r,y:s,width:i,height:n}=e.scissorValue;t.scissor(r,e.height-n-s,i,n)}const s=e.occlusionQueryCount;s>0&&(r.currentOcclusionQueries=r.occlusionQueries,r.currentOcclusionQueryObjects=r.occlusionQueryObjects,r.lastOcclusionObject=null,r.occlusionQueries=new Array(s),r.occlusionQueryObjects=new Array(s),r.occlusionQueryIndex=0)}finishRender(e){const{gl:t,state:r}=this,s=this.get(e),i=s.previousContext,n=e.occlusionQueryCount;n>0&&(n>s.occlusionQueryIndex&&t.endQuery(t.ANY_SAMPLES_PASSED),this.resolveOccludedAsync(e));const o=e.textures;if(null!==o)for(let e=0;e<o.length;e++){const t=o[e];t.generateMipmaps&&this.generateMipmaps(t)}if(this._currentContext=i,null!==e.textures&&e.renderTarget){const s=this.get(e.renderTarget),{samples:i}=e.renderTarget;if(i>0){const i=s.framebuffers[e.getCacheKey()],n=t.COLOR_BUFFER_BIT,o=s.msaaFrameBuffer,a=e.textures;r.bindFramebuffer(t.READ_FRAMEBUFFER,o),r.bindFramebuffer(t.DRAW_FRAMEBUFFER,i);for(let r=0;r<a.length;r++)if(e.scissor){const{x:r,y:i,width:o,height:a}=e.scissorValue,u=e.height-a-i;t.blitFramebuffer(r,u,r+o,u+a,r,u,r+o,u+a,n,t.NEAREST),t.invalidateSubFramebuffer(t.READ_FRAMEBUFFER,s.invalidationArray,r,u,o,a)}else t.blitFramebuffer(0,0,e.width,e.height,0,0,e.width,e.height,n,t.NEAREST),t.invalidateFramebuffer(t.READ_FRAMEBUFFER,s.invalidationArray)}}null!==i&&(this._setFramebuffer(i),i.viewport?this.updateViewport(i):t.viewport(0,0,t.drawingBufferWidth,t.drawingBufferHeight)),this.prepareTimestampBuffer(e)}resolveOccludedAsync(e){const t=this.get(e),{currentOcclusionQueries:r,currentOcclusionQueryObjects:s}=t;if(r&&s){const e=new WeakSet,{gl:i}=this;t.currentOcclusionQueryObjects=null,t.currentOcclusionQueries=null;const n=()=>{let o=0;for(let t=0;t<r.length;t++){const n=r[t];null!==n&&(i.getQueryParameter(n,i.QUERY_RESULT_AVAILABLE)&&(i.getQueryParameter(n,i.QUERY_RESULT)>0&&e.add(s[t]),r[t]=null,i.deleteQuery(n),o++))}o<r.length?requestAnimationFrame(n):t.occluded=e};n()}}isOccluded(e,t){const r=this.get(e);return r.occluded&&r.occluded.has(t)}updateViewport(e){const t=this.gl,{x:r,y:s,width:i,height:n}=e.viewportValue;t.viewport(r,e.height-n-s,i,n)}setScissorTest(e){const t=this.gl;e?t.enable(t.SCISSOR_TEST):t.disable(t.SCISSOR_TEST)}clear(e,t,r,s=null,i=!0){const{gl:n}=this;if(null===s){const e=this.getClearColor();e.r*=e.a,e.g*=e.a,e.b*=e.a,s={textures:null,clearColorValue:e}}let o=0;if(e&&(o|=n.COLOR_BUFFER_BIT),t&&(o|=n.DEPTH_BUFFER_BIT),r&&(o|=n.STENCIL_BUFFER_BIT),0!==o){let a;if(s.clearColorValue?a=s.clearColorValue:(a=this.getClearColor(),a.r*=a.a,a.g*=a.a,a.b*=a.a),t&&this.state.setDepthMask(!0),null===s.textures)n.clearColor(a.r,a.g,a.b,a.a),n.clear(o);else{if(i&&this._setFramebuffer(s),e)for(let e=0;e<s.textures.length;e++)n.clearBufferfv(n.COLOR,e,[a.r,a.g,a.b,a.a]);t&&r?n.clearBufferfi(n.DEPTH_STENCIL,0,1,0):t?n.clearBufferfv(n.DEPTH,0,[1]):r&&n.clearBufferiv(n.STENCIL,0,[0])}}}beginCompute(e){const{state:t,gl:r}=this;t.bindFramebuffer(r.FRAMEBUFFER,null),this.initTimestampQuery(e)}compute(e,t,r,s){const{state:i,gl:n}=this;!1===this.discard&&(n.enable(n.RASTERIZER_DISCARD),this.discard=!0);const{programGPU:o,transformBuffers:a,attributes:u}=this.get(s),l=this._getVaoKey(null,u),d=this.vaoCache[l];void 0===d?this._createVao(null,u):n.bindVertexArray(d),i.useProgram(o),this._bindUniforms(r);const c=this._getTransformFeedback(a);n.bindTransformFeedback(n.TRANSFORM_FEEDBACK,c),n.beginTransformFeedback(n.POINTS),u[0].isStorageInstancedBufferAttribute?n.drawArraysInstanced(n.POINTS,0,1,t.count):n.drawArrays(n.POINTS,0,t.count),n.endTransformFeedback(),n.bindTransformFeedback(n.TRANSFORM_FEEDBACK,null);for(let e=0;e<a.length;e++){const t=a[e];t.pbo&&this.textureUtils.copyBufferToTexture(t.transformBuffer,t.pbo),t.switchBuffers()}}finishCompute(e){const t=this.gl;this.discard=!1,t.disable(t.RASTERIZER_DISCARD),this.prepareTimestampBuffer(e),this._currentContext&&this._setFramebuffer(this._currentContext)}draw(e){const{object:t,pipeline:r,material:s,context:i,hardwareClippingPlanes:n}=e,{programGPU:o}=this.get(r),{gl:a,state:u}=this,l=this.get(i),d=e.getDrawParameters();if(null===d)return;this._bindUniforms(e.getBindings());const c=t.isMesh&&t.matrixWorld.determinant()<0;u.setMaterial(s,c,n),u.useProgram(o);const h=this.get(e);let p=h.staticVao;if(void 0===p||h.geometryId!==e.geometry.id){const t=this._getVaoKey(e.getIndex(),e.getAttributes());if(p=this.vaoCache[t],void 0===p){let t;({vaoGPU:p,staticVao:t}=this._createVao(e.getIndex(),e.getAttributes())),t&&(h.staticVao=p,h.geometryId=e.geometry.id)}}a.bindVertexArray(p);const g=e.getIndex(),m=l.lastOcclusionObject;if(m!==t&&void 0!==m){if(null!==m&&!0===m.occlusionTest&&(a.endQuery(a.ANY_SAMPLES_PASSED),l.occlusionQueryIndex++),!0===t.occlusionTest){const e=a.createQuery();a.beginQuery(a.ANY_SAMPLES_PASSED,e),l.occlusionQueries[l.occlusionQueryIndex]=e,l.occlusionQueryObjects[l.occlusionQueryIndex]=t}l.lastOcclusionObject=t}const f=this.bufferRenderer;t.isPoints?f.mode=a.POINTS:t.isLineSegments?f.mode=a.LINES:t.isLine?f.mode=a.LINE_STRIP:t.isLineLoop?f.mode=a.LINE_LOOP:!0===s.wireframe?(u.setLineWidth(s.wireframeLinewidth*this.renderer.getPixelRatio()),f.mode=a.LINES):f.mode=a.TRIANGLES;const{vertexCount:y,instanceCount:b}=d;let{firstVertex:x}=d;if(f.object=t,null!==g){x*=g.array.BYTES_PER_ELEMENT;const e=this.get(g);f.index=g.count,f.type=e.type}else f.index=0;t.isBatchedMesh?null!==t._multiDrawInstances?f.renderMultiDrawInstances(t._multiDrawStarts,t._multiDrawCounts,t._multiDrawCount,t._multiDrawInstances):this.hasFeature("WEBGL_multi_draw")?f.renderMultiDraw(t._multiDrawStarts,t._multiDrawCounts,t._multiDrawCount):vr("THREE.WebGLRenderer: WEBGL_multi_draw not supported."):b>1?f.renderInstances(x,y,b):f.render(x,y),a.bindVertexArray(null)}needsRenderUpdate(){return!1}getRenderCacheKey(){return""}createDefaultTexture(e){this.textureUtils.createDefaultTexture(e)}createTexture(e,t){this.textureUtils.createTexture(e,t)}updateTexture(e,t){this.textureUtils.updateTexture(e,t)}generateMipmaps(e){this.textureUtils.generateMipmaps(e)}destroyTexture(e){this.textureUtils.destroyTexture(e)}copyTextureToBuffer(e,t,r,s,i,n){return this.textureUtils.copyTextureToBuffer(e,t,r,s,i,n)}createSampler(){}destroySampler(){}createNodeBuilder(e,t){return new R_(e,t)}createProgram(e){const t=this.gl,{stage:r,code:s}=e,i="fragment"===r?t.createShader(t.FRAGMENT_SHADER):t.createShader(t.VERTEX_SHADER);t.shaderSource(i,s),t.compileShader(i),this.set(e,{shaderGPU:i})}destroyProgram(e){this.delete(e)}createRenderPipeline(e,t){const r=this.gl,s=e.pipeline,{fragmentProgram:i,vertexProgram:n}=s,o=r.createProgram(),a=this.get(i).shaderGPU,u=this.get(n).shaderGPU;if(r.attachShader(o,a),r.attachShader(o,u),r.linkProgram(o),this.set(s,{programGPU:o,fragmentShader:a,vertexShader:u}),null!==t&&this.parallel){const i=new Promise((t=>{const i=this.parallel,n=()=>{r.getProgramParameter(o,i.COMPLETION_STATUS_KHR)?(this._completeCompile(e,s),t()):requestAnimationFrame(n)};n()}));t.push(i)}else this._completeCompile(e,s)}_handleSource(e,t){const r=e.split("\n"),s=[],i=Math.max(t-6,0),n=Math.min(t+6,r.length);for(let e=i;e<n;e++){const i=e+1;s.push(`${i===t?">":" "} ${i}: ${r[e]}`)}return s.join("\n")}_getShaderErrors(e,t,r){const s=e.getShaderParameter(t,e.COMPILE_STATUS),i=e.getShaderInfoLog(t).trim();if(s&&""===i)return"";const n=/ERROR: 0:(\d+)/.exec(i);if(n){const s=parseInt(n[1]);return r.toUpperCase()+"\n\n"+i+"\n\n"+this._handleSource(e.getShaderSource(t),s)}return i}_logProgramError(e,t,r){if(this.renderer.debug.checkShaderErrors){const s=this.gl,i=s.getProgramInfoLog(e).trim();if(!1===s.getProgramParameter(e,s.LINK_STATUS))if("function"==typeof this.renderer.debug.onShaderError)this.renderer.debug.onShaderError(s,e,r,t);else{const n=this._getShaderErrors(s,r,"vertex"),o=this._getShaderErrors(s,t,"fragment");console.error("THREE.WebGLProgram: Shader Error "+s.getError()+" - VALIDATE_STATUS "+s.getProgramParameter(e,s.VALIDATE_STATUS)+"\n\nProgram Info Log: "+i+"\n"+n+"\n"+o)}else""!==i&&console.warn("THREE.WebGLProgram: Program Info Log:",i)}}_completeCompile(e,t){const{state:r,gl:s}=this,i=this.get(t),{programGPU:n,fragmentShader:o,vertexShader:a}=i;!1===s.getProgramParameter(n,s.LINK_STATUS)&&this._logProgramError(n,o,a),r.useProgram(n);const u=e.getBindings();this._setupBindings(u,n),this.set(t,{programGPU:n})}createComputePipeline(e,t){const{state:r,gl:s}=this,i={stage:"fragment",code:"#version 300 es\nprecision highp float;\nvoid main() {}"};this.createProgram(i);const{computeProgram:n}=e,o=s.createProgram(),a=this.get(i).shaderGPU,u=this.get(n).shaderGPU,l=n.transforms,d=[],c=[];for(let e=0;e<l.length;e++){const t=l[e];d.push(t.varyingName),c.push(t.attributeNode)}s.attachShader(o,a),s.attachShader(o,u),s.transformFeedbackVaryings(o,d,s.SEPARATE_ATTRIBS),s.linkProgram(o),!1===s.getProgramParameter(o,s.LINK_STATUS)&&this._logProgramError(o,a,u),r.useProgram(o),this._setupBindings(t,o);const h=n.attributes,p=[],g=[];for(let e=0;e<h.length;e++){const t=h[e].node.attribute;p.push(t),this.has(t)||this.attributeUtils.createAttribute(t,s.ARRAY_BUFFER)}for(let e=0;e<c.length;e++){const t=c[e].attribute;this.has(t)||this.attributeUtils.createAttribute(t,s.ARRAY_BUFFER);const r=this.get(t);g.push(r)}this.set(e,{programGPU:o,transformBuffers:g,attributes:p})}createBindings(e,t){if(!1===this._knownBindings.has(t)){this._knownBindings.add(t);let e=0,r=0;for(const s of t){this.set(s,{textures:r,uniformBuffers:e});for(const t of s.bindings)t.isUniformBuffer&&e++,t.isSampledTexture&&r++}}this.updateBindings(e,t)}updateBindings(e){const{gl:t}=this,r=this.get(e);let s=r.uniformBuffers,i=r.textures;for(const r of e.bindings)if(r.isUniformsGroup||r.isUniformBuffer){const e=r.buffer,i=t.createBuffer();t.bindBuffer(t.UNIFORM_BUFFER,i),t.bufferData(t.UNIFORM_BUFFER,e,t.DYNAMIC_DRAW),this.set(r,{index:s++,bufferGPU:i})}else if(r.isSampledTexture){const{textureGPU:e,glTextureType:t}=this.get(r.texture);this.set(r,{index:i++,textureGPU:e,glTextureType:t})}}updateBinding(e){const t=this.gl;if(e.isUniformsGroup||e.isUniformBuffer){const r=this.get(e).bufferGPU,s=e.buffer;t.bindBuffer(t.UNIFORM_BUFFER,r),t.bufferData(t.UNIFORM_BUFFER,s,t.DYNAMIC_DRAW)}}createIndexAttribute(e){const t=this.gl;this.attributeUtils.createAttribute(e,t.ELEMENT_ARRAY_BUFFER)}createAttribute(e){if(this.has(e))return;const t=this.gl;this.attributeUtils.createAttribute(e,t.ARRAY_BUFFER)}createStorageAttribute(e){if(this.has(e))return;const t=this.gl;this.attributeUtils.createAttribute(e,t.ARRAY_BUFFER)}updateAttribute(e){this.attributeUtils.updateAttribute(e)}destroyAttribute(e){this.attributeUtils.destroyAttribute(e)}hasFeature(e){const t=Object.keys(W_).filter((t=>W_[t]===e)),r=this.extensions;for(let e=0;e<t.length;e++)if(r.has(t[e]))return!0;return!1}getMaxAnisotropy(){return this.capabilities.getMaxAnisotropy()}copyTextureToTexture(e,t,r=null,s=null,i=0){this.textureUtils.copyTextureToTexture(e,t,r,s,i)}copyFramebufferToTexture(e,t,r){this.textureUtils.copyFramebufferToTexture(e,t,r)}_setFramebuffer(e){const{gl:t,state:r}=this;let s=null;if(null!==e.textures){const i=e.renderTarget,n=this.get(i),{samples:o,depthBuffer:a,stencilBuffer:u}=i,l=!0===i.isWebGLCubeRenderTarget,d=!0===i.isRenderTarget3D,c=!0===i.isRenderTargetArray;let h=n.msaaFrameBuffer,p=n.depthRenderbuffer;const g=um(e);let m;if(l?(n.cubeFramebuffers||(n.cubeFramebuffers={}),m=n.cubeFramebuffers[g]):(n.framebuffers||(n.framebuffers={}),m=n.framebuffers[g]),void 0===m){m=t.createFramebuffer(),r.bindFramebuffer(t.FRAMEBUFFER,m);const s=e.textures;if(l){n.cubeFramebuffers[g]=m;const{textureGPU:e}=this.get(s[0]),r=this.renderer._activeCubeFace;t.framebufferTexture2D(t.FRAMEBUFFER,t.COLOR_ATTACHMENT0,t.TEXTURE_CUBE_MAP_POSITIVE_X+r,e,0)}else{n.framebuffers[g]=m;for(let r=0;r<s.length;r++){const i=s[r],n=this.get(i);n.renderTarget=e.renderTarget,n.cacheKey=g;const o=t.COLOR_ATTACHMENT0+r;if(d||c){const e=this.renderer._activeCubeFace;t.framebufferTextureLayer(t.FRAMEBUFFER,o,n.textureGPU,0,e)}else t.framebufferTexture2D(t.FRAMEBUFFER,o,t.TEXTURE_2D,n.textureGPU,0)}r.drawBuffers(e,m)}if(null!==e.depthTexture){const r=this.get(e.depthTexture),s=u?t.DEPTH_STENCIL_ATTACHMENT:t.DEPTH_ATTACHMENT;r.renderTarget=e.renderTarget,r.cacheKey=g,t.framebufferTexture2D(t.FRAMEBUFFER,s,t.TEXTURE_2D,r.textureGPU,0)}}if(o>0){if(void 0===h){const s=[];h=t.createFramebuffer(),r.bindFramebuffer(t.FRAMEBUFFER,h);const i=[],l=e.textures;for(let r=0;r<l.length;r++){if(i[r]=t.createRenderbuffer(),t.bindRenderbuffer(t.RENDERBUFFER,i[r]),s.push(t.COLOR_ATTACHMENT0+r),a){const e=u?t.DEPTH_STENCIL_ATTACHMENT:t.DEPTH_ATTACHMENT;s.push(e)}const n=e.textures[r],l=this.get(n);t.renderbufferStorageMultisample(t.RENDERBUFFER,o,l.glInternalFormat,e.width,e.height),t.framebufferRenderbuffer(t.FRAMEBUFFER,t.COLOR_ATTACHMENT0+r,t.RENDERBUFFER,i[r])}if(n.msaaFrameBuffer=h,n.msaaRenderbuffers=i,void 0===p){p=t.createRenderbuffer(),this.textureUtils.setupRenderBufferStorage(p,e),n.depthRenderbuffer=p;const r=u?t.DEPTH_STENCIL_ATTACHMENT:t.DEPTH_ATTACHMENT;s.push(r)}n.invalidationArray=s}s=n.msaaFrameBuffer}else s=m}r.bindFramebuffer(t.FRAMEBUFFER,s)}_getVaoKey(e,t){let r="";if(null!==e){r+=":"+this.get(e).id}for(let e=0;e<t.length;e++){r+=":"+this.get(t[e]).id}return r}_createVao(e,t){const{gl:r}=this,s=r.createVertexArray();let i="",n=!0;if(r.bindVertexArray(s),null!==e){const t=this.get(e);r.bindBuffer(r.ELEMENT_ARRAY_BUFFER,t.bufferGPU),i+=":"+t.id}for(let e=0;e<t.length;e++){const s=t[e],o=this.get(s);let a,u;i+=":"+o.id,r.bindBuffer(r.ARRAY_BUFFER,o.bufferGPU),r.enableVertexAttribArray(e),(s.isStorageBufferAttribute||s.isStorageInstancedBufferAttribute)&&(n=!1),!0===s.isInterleavedBufferAttribute?(a=s.data.stride*o.bytesPerElement,u=s.offset*o.bytesPerElement):(a=0,u=0),o.isInteger?r.vertexAttribIPointer(e,s.itemSize,o.type,a,u):r.vertexAttribPointer(e,s.itemSize,o.type,s.normalized,a,u),s.isInstancedBufferAttribute&&!s.isInterleavedBufferAttribute?r.vertexAttribDivisor(e,s.meshPerAttribute):s.isInterleavedBufferAttribute&&s.data.isInstancedInterleavedBuffer&&r.vertexAttribDivisor(e,s.data.meshPerAttribute)}return r.bindBuffer(r.ARRAY_BUFFER,null),this.vaoCache[i]=s,{vaoGPU:s,staticVao:n}}_getTransformFeedback(e){let t="";for(let r=0;r<e.length;r++)t+=":"+e[r].id;let r=this.transformFeedbackCache[t];if(void 0!==r)return r;const{gl:s}=this;r=s.createTransformFeedback(),s.bindTransformFeedback(s.TRANSFORM_FEEDBACK,r);for(let t=0;t<e.length;t++){const r=e[t];s.bindBufferBase(s.TRANSFORM_FEEDBACK_BUFFER,t,r.transformBuffer)}return s.bindTransformFeedback(s.TRANSFORM_FEEDBACK,null),this.transformFeedbackCache[t]=r,r}_setupBindings(e,t){const r=this.gl;for(const s of e)for(const e of s.bindings){const s=this.get(e).index;if(e.isUniformsGroup||e.isUniformBuffer){const i=r.getUniformBlockIndex(t,e.name);r.uniformBlockBinding(t,i,s)}else if(e.isSampledTexture){const i=r.getUniformLocation(t,e.name);r.uniform1i(i,s)}}}_bindUniforms(e){const{gl:t,state:r}=this;for(const s of e)for(const e of s.bindings){const s=this.get(e),i=s.index;e.isUniformsGroup||e.isUniformBuffer?r.bindBufferBase(t.UNIFORM_BUFFER,i,s.bufferGPU):e.isSampledTexture&&r.bindTexture(s.glTextureType,s.textureGPU,t.TEXTURE0+i)}}dispose(){this.renderer.domElement.removeEventListener("webglcontextlost",this._onContextLost)}}const K_="point-list",X_="line-list",Y_="line-strip",Q_="triangle-list",Z_="triangle-strip",J_="never",ev="less",tv="equal",rv="less-equal",sv="greater",iv="not-equal",nv="greater-equal",ov="always",av="store",uv="load",lv="clear",dv="ccw",cv="none",hv="front",pv="back",gv="uint16",mv="uint32",fv={R8Unorm:"r8unorm",R8Snorm:"r8snorm",R8Uint:"r8uint",R8Sint:"r8sint",R16Uint:"r16uint",R16Sint:"r16sint",R16Float:"r16float",RG8Unorm:"rg8unorm",RG8Snorm:"rg8snorm",RG8Uint:"rg8uint",RG8Sint:"rg8sint",R32Uint:"r32uint",R32Sint:"r32sint",R32Float:"r32float",RG16Uint:"rg16uint",RG16Sint:"rg16sint",RG16Float:"rg16float",RGBA8Unorm:"rgba8unorm",RGBA8UnormSRGB:"rgba8unorm-srgb",RGBA8Snorm:"rgba8snorm",RGBA8Uint:"rgba8uint",RGBA8Sint:"rgba8sint",BGRA8Unorm:"bgra8unorm",BGRA8UnormSRGB:"bgra8unorm-srgb",RGB9E5UFloat:"rgb9e5ufloat",RGB10A2Unorm:"rgb10a2unorm",RG11B10uFloat:"rgb10a2unorm",RG32Uint:"rg32uint",RG32Sint:"rg32sint",RG32Float:"rg32float",RGBA16Uint:"rgba16uint",RGBA16Sint:"rgba16sint",RGBA16Float:"rgba16float",RGBA32Uint:"rgba32uint",RGBA32Sint:"rgba32sint",RGBA32Float:"rgba32float",Stencil8:"stencil8",Depth16Unorm:"depth16unorm",Depth24Plus:"depth24plus",Depth24PlusStencil8:"depth24plus-stencil8",Depth32Float:"depth32float",Depth32FloatStencil8:"depth32float-stencil8",BC1RGBAUnorm:"bc1-rgba-unorm",BC1RGBAUnormSRGB:"bc1-rgba-unorm-srgb",BC2RGBAUnorm:"bc2-rgba-unorm",BC2RGBAUnormSRGB:"bc2-rgba-unorm-srgb",BC3RGBAUnorm:"bc3-rgba-unorm",BC3RGBAUnormSRGB:"bc3-rgba-unorm-srgb",BC4RUnorm:"bc4-r-unorm",BC4RSnorm:"bc4-r-snorm",BC5RGUnorm:"bc5-rg-unorm",BC5RGSnorm:"bc5-rg-snorm",BC6HRGBUFloat:"bc6h-rgb-ufloat",BC6HRGBFloat:"bc6h-rgb-float",BC7RGBAUnorm:"bc7-rgba-unorm",BC7RGBAUnormSRGB:"bc7-rgba-srgb",ETC2RGB8Unorm:"etc2-rgb8unorm",ETC2RGB8UnormSRGB:"etc2-rgb8unorm-srgb",ETC2RGB8A1Unorm:"etc2-rgb8a1unorm",ETC2RGB8A1UnormSRGB:"etc2-rgb8a1unorm-srgb",ETC2RGBA8Unorm:"etc2-rgba8unorm",ETC2RGBA8UnormSRGB:"etc2-rgba8unorm-srgb",EACR11Unorm:"eac-r11unorm",EACR11Snorm:"eac-r11snorm",EACRG11Unorm:"eac-rg11unorm",EACRG11Snorm:"eac-rg11snorm",ASTC4x4Unorm:"astc-4x4-unorm",ASTC4x4UnormSRGB:"astc-4x4-unorm-srgb",ASTC5x4Unorm:"astc-5x4-unorm",ASTC5x4UnormSRGB:"astc-5x4-unorm-srgb",ASTC5x5Unorm:"astc-5x5-unorm",ASTC5x5UnormSRGB:"astc-5x5-unorm-srgb",ASTC6x5Unorm:"astc-6x5-unorm",ASTC6x5UnormSRGB:"astc-6x5-unorm-srgb",ASTC6x6Unorm:"astc-6x6-unorm",ASTC6x6UnormSRGB:"astc-6x6-unorm-srgb",ASTC8x5Unorm:"astc-8x5-unorm",ASTC8x5UnormSRGB:"astc-8x5-unorm-srgb",ASTC8x6Unorm:"astc-8x6-unorm",ASTC8x6UnormSRGB:"astc-8x6-unorm-srgb",ASTC8x8Unorm:"astc-8x8-unorm",ASTC8x8UnormSRGB:"astc-8x8-unorm-srgb",ASTC10x5Unorm:"astc-10x5-unorm",ASTC10x5UnormSRGB:"astc-10x5-unorm-srgb",ASTC10x6Unorm:"astc-10x6-unorm",ASTC10x6UnormSRGB:"astc-10x6-unorm-srgb",ASTC10x8Unorm:"astc-10x8-unorm",ASTC10x8UnormSRGB:"astc-10x8-unorm-srgb",ASTC10x10Unorm:"astc-10x10-unorm",ASTC10x10UnormSRGB:"astc-10x10-unorm-srgb",ASTC12x10Unorm:"astc-12x10-unorm",ASTC12x10UnormSRGB:"astc-12x10-unorm-srgb",ASTC12x12Unorm:"astc-12x12-unorm",ASTC12x12UnormSRGB:"astc-12x12-unorm-srgb"},yv="clamp-to-edge",bv="repeat",xv="mirror-repeat",Tv="linear",_v="nearest",vv="zero",Nv="one",Sv="src",Av="one-minus-src",Rv="src-alpha",Cv="one-minus-src-alpha",Ev="dst",wv="one-minus-dst",Mv="dst-alpha",Bv="one-minus-dst-alpha",Fv="src-alpha-saturated",Uv="constant",Pv="one-minus-constant",Iv="add",Dv="subtract",Lv="reverse-subtract",Vv="min",Ov="max",Gv=0,kv=15,zv="keep",$v="zero",Hv="replace",Wv="invert",jv="increment-clamp",qv="decrement-clamp",Kv="increment-wrap",Xv="decrement-wrap",Yv="storage",Qv="read-only-storage",Zv="write-only",Jv="read-only",eN="read-write",tN="float",rN="unfilterable-float",sN="depth",iN="sint",nN="uint",oN="2d",aN="3d",uN="2d",lN="2d-array",dN="cube",cN="3d",hN="all",pN="vertex",gN="instance",mN={DepthClipControl:"depth-clip-control",Depth32FloatStencil8:"depth32float-stencil8",TextureCompressionBC:"texture-compression-bc",TextureCompressionETC2:"texture-compression-etc2",TextureCompressionASTC:"texture-compression-astc",TimestampQuery:"timestamp-query",IndirectFirstInstance:"indirect-first-instance",ShaderF16:"shader-f16",RG11B10UFloat:"rg11b10ufloat-renderable",BGRA8UNormStorage:"bgra8unorm-storage",Float32Filterable:"float32-filterable",ClipDistances:"clip-distances",DualSourceBlending:"dual-source-blending",Subgroups:"subgroups"};class fN extends l_{constructor(e,t){super(e),this.texture=t,this.version=t?t.version:0,this.isSampler=!0}}class yN extends fN{constructor(e,t,r){super(e,t?t.value:null),this.textureNode=t,this.groupNode=r}update(){this.texture=this.textureNode.value}}class bN extends d_{constructor(e,t){super(e,t?t.array:null),this.attribute=t,this.isStorageBuffer=!0}}let xN=0;class TN extends bN{constructor(e,t){super("StorageBuffer_"+xN++,e?e.value:null),this.nodeUniform=e,this.access=e?e.access:Cs.READ_WRITE,this.groupNode=t}get buffer(){return this.nodeUniform.value}}class _N extends Lg{constructor(e){super(),this.device=e;this.mipmapSampler=e.createSampler({minFilter:Tv}),this.flipYSampler=e.createSampler({minFilter:_v}),this.transferPipelines={},this.flipYPipelines={},this.mipmapVertexShaderModule=e.createShaderModule({label:"mipmapVertex",code:"\nstruct VarysStruct {\n\t@builtin( position ) Position: vec4<f32>,\n\t@location( 0 ) vTex : vec2<f32>\n};\n\n@vertex\nfn main( @builtin( vertex_index ) vertexIndex : u32 ) -> VarysStruct {\n\n\tvar Varys : VarysStruct;\n\n\tvar pos = array< vec2<f32>, 4 >(\n\t\tvec2<f32>( -1.0,  1.0 ),\n\t\tvec2<f32>(  1.0,  1.0 ),\n\t\tvec2<f32>( -1.0, -1.0 ),\n\t\tvec2<f32>(  1.0, -1.0 )\n\t);\n\n\tvar tex = array< vec2<f32>, 4 >(\n\t\tvec2<f32>( 0.0, 0.0 ),\n\t\tvec2<f32>( 1.0, 0.0 ),\n\t\tvec2<f32>( 0.0, 1.0 ),\n\t\tvec2<f32>( 1.0, 1.0 )\n\t);\n\n\tVarys.vTex = tex[ vertexIndex ];\n\tVarys.Position = vec4<f32>( pos[ vertexIndex ], 0.0, 1.0 );\n\n\treturn Varys;\n\n}\n"}),this.mipmapFragmentShaderModule=e.createShaderModule({label:"mipmapFragment",code:"\n@group( 0 ) @binding( 0 )\nvar imgSampler : sampler;\n\n@group( 0 ) @binding( 1 )\nvar img : texture_2d<f32>;\n\n@fragment\nfn main( @location( 0 ) vTex : vec2<f32> ) -> @location( 0 ) vec4<f32> {\n\n\treturn textureSample( img, imgSampler, vTex );\n\n}\n"}),this.flipYFragmentShaderModule=e.createShaderModule({label:"flipYFragment",code:"\n@group( 0 ) @binding( 0 )\nvar imgSampler : sampler;\n\n@group( 0 ) @binding( 1 )\nvar img : texture_2d<f32>;\n\n@fragment\nfn main( @location( 0 ) vTex : vec2<f32> ) -> @location( 0 ) vec4<f32> {\n\n\treturn textureSample( img, imgSampler, vec2( vTex.x, 1.0 - vTex.y ) );\n\n}\n"})}getTransferPipeline(e){let t=this.transferPipelines[e];return void 0===t&&(t=this.device.createRenderPipeline({label:`mipmap-${e}`,vertex:{module:this.mipmapVertexShaderModule,entryPoint:"main"},fragment:{module:this.mipmapFragmentShaderModule,entryPoint:"main",targets:[{format:e}]},primitive:{topology:Z_,stripIndexFormat:mv},layout:"auto"}),this.transferPipelines[e]=t),t}getFlipYPipeline(e){let t=this.flipYPipelines[e];return void 0===t&&(t=this.device.createRenderPipeline({label:`flipY-${e}`,vertex:{module:this.mipmapVertexShaderModule,entryPoint:"main"},fragment:{module:this.flipYFragmentShaderModule,entryPoint:"main",targets:[{format:e}]},primitive:{topology:Z_,stripIndexFormat:mv},layout:"auto"}),this.flipYPipelines[e]=t),t}flipY(e,t,r=0){const s=t.format,{width:i,height:n}=t.size,o=this.getTransferPipeline(s),a=this.getFlipYPipeline(s),u=this.device.createTexture({size:{width:i,height:n,depthOrArrayLayers:1},format:s,usage:GPUTextureUsage.RENDER_ATTACHMENT|GPUTextureUsage.TEXTURE_BINDING}),l=e.createView({baseMipLevel:0,mipLevelCount:1,dimension:uN,baseArrayLayer:r}),d=u.createView({baseMipLevel:0,mipLevelCount:1,dimension:uN,baseArrayLayer:0}),c=this.device.createCommandEncoder({}),h=(e,t,r)=>{const s=e.getBindGroupLayout(0),i=this.device.createBindGroup({layout:s,entries:[{binding:0,resource:this.flipYSampler},{binding:1,resource:t}]}),n=c.beginRenderPass({colorAttachments:[{view:r,loadOp:lv,storeOp:av,clearValue:[0,0,0,0]}]});n.setPipeline(e),n.setBindGroup(0,i),n.draw(4,1,0,0),n.end()};h(o,l,d),h(a,d,l),this.device.queue.submit([c.finish()]),u.destroy()}generateMipmaps(e,t,r=0){const s=this.get(e);void 0===s.useCount&&(s.useCount=0,s.layers=[]);const i=s.layers[r]||this._mipmapCreateBundles(e,t,r),n=this.device.createCommandEncoder({});this._mipmapRunBundles(n,i),this.device.queue.submit([n.finish()]),0!==s.useCount&&(s.layers[r]=i),s.useCount++}_mipmapCreateBundles(e,t,r){const s=this.getTransferPipeline(t.format),i=s.getBindGroupLayout(0);let n=e.createView({baseMipLevel:0,mipLevelCount:1,dimension:uN,baseArrayLayer:r});const o=[];for(let a=1;a<t.mipLevelCount;a++){const u=this.device.createBindGroup({layout:i,entries:[{binding:0,resource:this.mipmapSampler},{binding:1,resource:n}]}),l=e.createView({baseMipLevel:a,mipLevelCount:1,dimension:uN,baseArrayLayer:r}),d={colorAttachments:[{view:l,loadOp:lv,storeOp:av,clearValue:[0,0,0,0]}]},c=this.device.createRenderBundleEncoder({colorFormats:[t.format]});c.setPipeline(s),c.setBindGroup(0,u),c.draw(4,1,0,0),o.push({renderBundles:[c.finish()],passDescriptor:d}),n=l}return o}_mipmapRunBundles(e,t){const r=t.length;for(let s=0;s<r;s++){const r=t[s],i=e.beginRenderPass(r.passDescriptor);i.executeBundles(r.renderBundles),i.end()}}}const vN={[mr]:"never",[Re]:"less",[br]:"equal",[yr]:"less-equal",[Tr]:"greater",[xr]:"greater-equal",[fr]:"always",[_r]:"not-equal"},NN=[0,1,3,2,4,5];class SN{constructor(e){this.backend=e,this._passUtils=null,this.defaultTexture={},this.defaultCubeTexture={},this.defaultVideoFrame=null,this.colorBuffer=null,this.depthTexture=new B,this.depthTexture.name="depthBuffer"}createSampler(e){const t=this.backend,r=t.device,s=t.get(e),i={addressModeU:this._convertAddressMode(e.wrapS),addressModeV:this._convertAddressMode(e.wrapT),addressModeW:this._convertAddressMode(e.wrapR),magFilter:this._convertFilterMode(e.magFilter),minFilter:this._convertFilterMode(e.minFilter),mipmapFilter:this._convertFilterMode(e.minFilter),maxAnisotropy:1};i.magFilter===Tv&&i.minFilter===Tv&&i.mipmapFilter===Tv&&(i.maxAnisotropy=e.anisotropy),e.isDepthTexture&&null!==e.compareFunction&&(i.compare=vN[e.compareFunction]),s.sampler=r.createSampler(i)}createDefaultTexture(e){let t;const r=AN(e);e.isCubeTexture?t=this._getDefaultCubeTextureGPU(r):e.isVideoTexture?this.backend.get(e).externalTexture=this._getDefaultVideoFrame():t=this._getDefaultTextureGPU(r),this.backend.get(e).texture=t}createTexture(e,t={}){const r=this.backend,s=r.get(e);if(s.initialized)throw new Error("WebGPUTextureUtils: Texture already initialized.");void 0===t.needsMipmaps&&(t.needsMipmaps=!1),void 0===t.levels&&(t.levels=1),void 0===t.depth&&(t.depth=1);const{width:i,height:n,depth:o,levels:a}=t;e.isFramebufferTexture&&(t.renderTarget?t.format=this.backend.utils.getCurrentColorFormat(t.renderTarget):t.format=this.backend.utils.getPreferredCanvasFormat());const u=this._getDimension(e),l=e.internalFormat||t.format||AN(e,r.device);s.format=l;const{samples:d,primarySamples:c,isMSAA:h}=r.utils.getTextureSampleData(e);let p=GPUTextureUsage.TEXTURE_BINDING|GPUTextureUsage.COPY_DST|GPUTextureUsage.COPY_SRC;!0===e.isStorageTexture&&(p|=GPUTextureUsage.STORAGE_BINDING),!0!==e.isCompressedTexture&&!0!==e.isCompressedArrayTexture&&(p|=GPUTextureUsage.RENDER_ATTACHMENT);const g={label:e.name,size:{width:i,height:n,depthOrArrayLayers:o},mipLevelCount:a,sampleCount:c,dimension:u,format:l,usage:p};if(e.isVideoTexture){const t=e.source.data,r=new VideoFrame(t);g.size.width=r.displayWidth,g.size.height=r.displayHeight,r.close(),s.externalTexture=t}else{if(void 0===l)return console.warn("WebGPURenderer: Texture format not supported."),this.createDefaultTexture(e);s.texture=r.device.createTexture(g)}if(h){const e=Object.assign({},g);e.label=e.label+"-msaa",e.sampleCount=d,s.msaaTexture=r.device.createTexture(e)}s.initialized=!0,s.textureDescriptorGPU=g}destroyTexture(e){const t=this.backend,r=t.get(e);void 0!==r.texture&&r.texture.destroy(),void 0!==r.msaaTexture&&r.msaaTexture.destroy(),t.delete(e)}destroySampler(e){delete this.backend.get(e).sampler}generateMipmaps(e){const t=this.backend.get(e);if(e.isCubeTexture)for(let e=0;e<6;e++)this._generateMipmaps(t.texture,t.textureDescriptorGPU,e);else{const r=e.image.depth||1;for(let e=0;e<r;e++)this._generateMipmaps(t.texture,t.textureDescriptorGPU,e)}}getColorBuffer(){this.colorBuffer&&this.colorBuffer.destroy();const e=this.backend,{width:t,height:r}=e.getDrawingBufferSize();return this.colorBuffer=e.device.createTexture({label:"colorBuffer",size:{width:t,height:r,depthOrArrayLayers:1},sampleCount:e.utils.getSampleCount(e.renderer.samples),format:e.utils.getPreferredCanvasFormat(),usage:GPUTextureUsage.RENDER_ATTACHMENT|GPUTextureUsage.COPY_SRC}),this.colorBuffer}getDepthBuffer(e=!0,t=!1){const r=this.backend,{width:s,height:i}=r.getDrawingBufferSize(),n=this.depthTexture,o=r.get(n).texture;let a,u;if(t?(a=de,u=he):e&&(a=ce,u=b),void 0!==o){if(n.image.width===s&&n.image.height===i&&n.format===a&&n.type===u)return o;this.destroyTexture(n)}return n.name="depthBuffer",n.format=a,n.type=u,n.image.width=s,n.image.height=i,this.createTexture(n,{width:s,height:i}),r.get(n).texture}updateTexture(e,t){const r=this.backend.get(e),{textureDescriptorGPU:s}=r;if(!e.isRenderTargetTexture&&void 0!==s){if(e.isDataTexture)this._copyBufferToTexture(t.image,r.texture,s,0,e.flipY);else if(e.isDataArrayTexture||e.isData3DTexture)for(let i=0;i<t.image.depth;i++)this._copyBufferToTexture(t.image,r.texture,s,i,e.flipY,i);else if(e.isCompressedTexture||e.isCompressedArrayTexture)this._copyCompressedBufferToTexture(e.mipmaps,r.texture,s);else if(e.isCubeTexture)this._copyCubeMapToTexture(t.images,r.texture,s,e.flipY);else if(e.isVideoTexture){const t=e.source.data;r.externalTexture=t}else this._copyImageToTexture(t.image,r.texture,s,0,e.flipY);r.version=e.version,e.onUpdate&&e.onUpdate(e)}}async copyTextureToBuffer(e,t,r,s,i,n){const o=this.backend.device,a=this.backend.get(e),u=a.texture,l=a.textureDescriptorGPU.format,d=this._getBytesPerTexel(l);let c=s*d;c=256*Math.ceil(c/256);const h=o.createBuffer({size:s*i*d,usage:GPUBufferUsage.COPY_DST|GPUBufferUsage.MAP_READ}),p=o.createCommandEncoder();p.copyTextureToBuffer({texture:u,origin:{x:t,y:r,z:n}},{buffer:h,bytesPerRow:c},{width:s,height:i});const g=this._getTypedArrayType(l);o.queue.submit([p.finish()]),await h.mapAsync(GPUMapMode.READ);return new g(h.getMappedRange())}_isEnvironmentTexture(e){const t=e.mapping;return t===j||t===q||t===_||t===v}_getDefaultTextureGPU(e){let t=this.defaultTexture[e];if(void 0===t){const r=new ee;r.minFilter=pr,r.magFilter=pr,this.createTexture(r,{width:1,height:1,format:e}),this.defaultTexture[e]=t=r}return this.backend.get(t).texture}_getDefaultCubeTextureGPU(e){let t=this.defaultTexture[e];if(void 0===t){const r=new W;r.minFilter=pr,r.magFilter=pr,this.createTexture(r,{width:1,height:1,depth:6}),this.defaultCubeTexture[e]=t=r}return this.backend.get(t).texture}_getDefaultVideoFrame(){let e=this.defaultVideoFrame;if(null===e){const t={timestamp:0,codedWidth:1,codedHeight:1,format:"RGBA"};this.defaultVideoFrame=e=new VideoFrame(new Uint8Array([0,0,0,255]),t)}return e}_copyCubeMapToTexture(e,t,r,s){for(let i=0;i<6;i++){const n=e[i],o=!0===s?NN[i]:i;n.isDataTexture?this._copyBufferToTexture(n.image,t,r,o,s):this._copyImageToTexture(n,t,r,o,s)}}_copyImageToTexture(e,t,r,s,i){this.backend.device.queue.copyExternalImageToTexture({source:e},{texture:t,mipLevel:0,origin:{x:0,y:0,z:s}},{width:e.width,height:e.height,depthOrArrayLayers:1}),!0===i&&this._flipY(t,r,s)}_getPassUtils(){let e=this._passUtils;return null===e&&(this._passUtils=e=new _N(this.backend.device)),e}_generateMipmaps(e,t,r=0){this._getPassUtils().generateMipmaps(e,t,r)}_flipY(e,t,r=0){this._getPassUtils().flipY(e,t,r)}_copyBufferToTexture(e,t,r,s,i,n=0){const o=this.backend.device,a=e.data,u=this._getBytesPerTexel(r.format),l=e.width*u;o.queue.writeTexture({texture:t,mipLevel:0,origin:{x:0,y:0,z:s}},a,{offset:e.width*e.height*u*n,bytesPerRow:l},{width:e.width,height:e.height,depthOrArrayLayers:1}),!0===i&&this._flipY(t,r,s)}_copyCompressedBufferToTexture(e,t,r){const s=this.backend.device,i=this._getBlockData(r.format),n=r.size.depthOrArrayLayers>1;for(let o=0;o<e.length;o++){const a=e[o],u=a.width,l=a.height,d=n?r.size.depthOrArrayLayers:1,c=Math.ceil(u/i.width)*i.byteLength,h=c*Math.ceil(l/i.height);for(let e=0;e<d;e++)s.queue.writeTexture({texture:t,mipLevel:o,origin:{x:0,y:0,z:e}},a.data,{offset:e*h,bytesPerRow:c,rowsPerImage:Math.ceil(l/i.height)},{width:Math.ceil(u/i.width)*i.width,height:Math.ceil(l/i.height)*i.height,depthOrArrayLayers:1})}}_getBlockData(e){return e===fv.BC1RGBAUnorm||e===fv.BC1RGBAUnormSRGB?{byteLength:8,width:4,height:4}:e===fv.BC2RGBAUnorm||e===fv.BC2RGBAUnormSRGB||e===fv.BC3RGBAUnorm||e===fv.BC3RGBAUnormSRGB?{byteLength:16,width:4,height:4}:e===fv.BC4RUnorm||e===fv.BC4RSNorm?{byteLength:8,width:4,height:4}:e===fv.BC5RGUnorm||e===fv.BC5RGSnorm||e===fv.BC6HRGBUFloat||e===fv.BC6HRGBFloat||e===fv.BC7RGBAUnorm||e===fv.BC7RGBAUnormSRGB?{byteLength:16,width:4,height:4}:e===fv.ETC2RGB8Unorm||e===fv.ETC2RGB8UnormSRGB||e===fv.ETC2RGB8A1Unorm||e===fv.ETC2RGB8A1UnormSRGB?{byteLength:8,width:4,height:4}:e===fv.ETC2RGBA8Unorm||e===fv.ETC2RGBA8UnormSRGB?{byteLength:16,width:4,height:4}:e===fv.EACR11Unorm||e===fv.EACR11Snorm?{byteLength:8,width:4,height:4}:e===fv.EACRG11Unorm||e===fv.EACRG11Snorm||e===fv.ASTC4x4Unorm||e===fv.ASTC4x4UnormSRGB?{byteLength:16,width:4,height:4}:e===fv.ASTC5x4Unorm||e===fv.ASTC5x4UnormSRGB?{byteLength:16,width:5,height:4}:e===fv.ASTC5x5Unorm||e===fv.ASTC5x5UnormSRGB?{byteLength:16,width:5,height:5}:e===fv.ASTC6x5Unorm||e===fv.ASTC6x5UnormSRGB?{byteLength:16,width:6,height:5}:e===fv.ASTC6x6Unorm||e===fv.ASTC6x6UnormSRGB?{byteLength:16,width:6,height:6}:e===fv.ASTC8x5Unorm||e===fv.ASTC8x5UnormSRGB?{byteLength:16,width:8,height:5}:e===fv.ASTC8x6Unorm||e===fv.ASTC8x6UnormSRGB?{byteLength:16,width:8,height:6}:e===fv.ASTC8x8Unorm||e===fv.ASTC8x8UnormSRGB?{byteLength:16,width:8,height:8}:e===fv.ASTC10x5Unorm||e===fv.ASTC10x5UnormSRGB?{byteLength:16,width:10,height:5}:e===fv.ASTC10x6Unorm||e===fv.ASTC10x6UnormSRGB?{byteLength:16,width:10,height:6}:e===fv.ASTC10x8Unorm||e===fv.ASTC10x8UnormSRGB?{byteLength:16,width:10,height:8}:e===fv.ASTC10x10Unorm||e===fv.ASTC10x10UnormSRGB?{byteLength:16,width:10,height:10}:e===fv.ASTC12x10Unorm||e===fv.ASTC12x10UnormSRGB?{byteLength:16,width:12,height:10}:e===fv.ASTC12x12Unorm||e===fv.ASTC12x12UnormSRGB?{byteLength:16,width:12,height:12}:void 0}_convertAddressMode(e){let t=yv;return e===dr?t=bv:e===hr&&(t=xv),t}_convertFilterMode(e){let t=Tv;return e!==pr&&e!==gr&&e!==Ie||(t=_v),t}_getBytesPerTexel(e){return e===fv.R8Unorm||e===fv.R8Snorm||e===fv.R8Uint||e===fv.R8Sint?1:e===fv.R16Uint||e===fv.R16Sint||e===fv.R16Float||e===fv.RG8Unorm||e===fv.RG8Snorm||e===fv.RG8Uint||e===fv.RG8Sint?2:e===fv.R32Uint||e===fv.R32Sint||e===fv.R32Float||e===fv.RG16Uint||e===fv.RG16Sint||e===fv.RG16Float||e===fv.RGBA8Unorm||e===fv.RGBA8UnormSRGB||e===fv.RGBA8Snorm||e===fv.RGBA8Uint||e===fv.RGBA8Sint||e===fv.BGRA8Unorm||e===fv.BGRA8UnormSRGB||e===fv.RGB9E5UFloat||e===fv.RGB10A2Unorm||e===fv.RG11B10UFloat||e===fv.Depth32Float||e===fv.Depth24Plus||e===fv.Depth24PlusStencil8||e===fv.Depth32FloatStencil8?4:e===fv.RG32Uint||e===fv.RG32Sint||e===fv.RG32Float||e===fv.RGBA16Uint||e===fv.RGBA16Sint||e===fv.RGBA16Float?8:e===fv.RGBA32Uint||e===fv.RGBA32Sint||e===fv.RGBA32Float?16:void 0}_getTypedArrayType(e){return e===fv.R8Uint?Uint8Array:e===fv.R8Sint?Int8Array:e===fv.R8Unorm?Uint8Array:e===fv.R8Snorm?Int8Array:e===fv.RG8Uint?Uint8Array:e===fv.RG8Sint?Int8Array:e===fv.RG8Unorm?Uint8Array:e===fv.RG8Snorm?Int8Array:e===fv.RGBA8Uint?Uint8Array:e===fv.RGBA8Sint?Int8Array:e===fv.RGBA8Unorm?Uint8Array:e===fv.RGBA8Snorm?Int8Array:e===fv.R16Uint?Uint16Array:e===fv.R16Sint?Int16Array:e===fv.RG16Uint?Uint16Array:e===fv.RG16Sint?Int16Array:e===fv.RGBA16Uint?Uint16Array:e===fv.RGBA16Sint?Int16Array:e===fv.R16Float||e===fv.RG16Float||e===fv.RGBA16Float?Uint16Array:e===fv.R32Uint?Uint32Array:e===fv.R32Sint?Int32Array:e===fv.R32Float?Float32Array:e===fv.RG32Uint?Uint32Array:e===fv.RG32Sint?Int32Array:e===fv.RG32Float?Float32Array:e===fv.RGBA32Uint?Uint32Array:e===fv.RGBA32Sint?Int32Array:e===fv.RGBA32Float?Float32Array:e===fv.BGRA8Unorm||e===fv.BGRA8UnormSRGB?Uint8Array:e===fv.RGB10A2Unorm||e===fv.RGB9E5UFloat||e===fv.RG11B10UFloat?Uint32Array:e===fv.Depth32Float?Float32Array:e===fv.Depth24Plus||e===fv.Depth24PlusStencil8?Uint32Array:e===fv.Depth32FloatStencil8?Float32Array:void 0}_getDimension(e){let t;return t=e.isData3DTexture?aN:oN,t}}function AN(e,t=null){const r=e.format,s=e.type,i=e.colorSpace;let n;if(!0===e.isCompressedTexture||!0===e.isCompressedArrayTexture)switch(r){case It:n=i===Ve?fv.BC1RGBAUnormSRGB:fv.BC1RGBAUnorm;break;case Dt:n=i===Ve?fv.BC2RGBAUnormSRGB:fv.BC2RGBAUnorm;break;case Lt:n=i===Ve?fv.BC3RGBAUnormSRGB:fv.BC3RGBAUnorm;break;case $t:n=i===Ve?fv.ETC2RGB8UnormSRGB:fv.ETC2RGB8Unorm;break;case Ht:n=i===Ve?fv.ETC2RGBA8UnormSRGB:fv.ETC2RGBA8Unorm;break;case Wt:n=i===Ve?fv.ASTC4x4UnormSRGB:fv.ASTC4x4Unorm;break;case jt:n=i===Ve?fv.ASTC5x4UnormSRGB:fv.ASTC5x4Unorm;break;case qt:n=i===Ve?fv.ASTC5x5UnormSRGB:fv.ASTC5x5Unorm;break;case Kt:n=i===Ve?fv.ASTC6x5UnormSRGB:fv.ASTC6x5Unorm;break;case Xt:n=i===Ve?fv.ASTC6x6UnormSRGB:fv.ASTC6x6Unorm;break;case Yt:n=i===Ve?fv.ASTC8x5UnormSRGB:fv.ASTC8x5Unorm;break;case Qt:n=i===Ve?fv.ASTC8x6UnormSRGB:fv.ASTC8x6Unorm;break;case Zt:n=i===Ve?fv.ASTC8x8UnormSRGB:fv.ASTC8x8Unorm;break;case Jt:n=i===Ve?fv.ASTC10x5UnormSRGB:fv.ASTC10x5Unorm;break;case er:n=i===Ve?fv.ASTC10x6UnormSRGB:fv.ASTC10x6Unorm;break;case tr:n=i===Ve?fv.ASTC10x8UnormSRGB:fv.ASTC10x8Unorm;break;case rr:n=i===Ve?fv.ASTC10x10UnormSRGB:fv.ASTC10x10Unorm;break;case sr:n=i===Ve?fv.ASTC12x10UnormSRGB:fv.ASTC12x10Unorm;break;case ir:n=i===Ve?fv.ASTC12x12UnormSRGB:fv.ASTC12x12Unorm;break;case Ue:n=i===Ve?fv.RGBA8UnormSRGB:fv.RGBA8Unorm;break;default:console.error("WebGPURenderer: Unsupported texture format.",r)}else switch(r){case Ue:switch(s){case Ye:n=fv.RGBA8Snorm;break;case Qe:n=fv.RGBA16Sint;break;case Xe:n=fv.RGBA16Uint;break;case b:n=fv.RGBA32Uint;break;case x:n=fv.RGBA32Sint;break;case pe:n=i===Ve?fv.RGBA8UnormSRGB:fv.RGBA8Unorm;break;case ye:n=fv.RGBA16Float;break;case E:n=fv.RGBA32Float;break;default:console.error("WebGPURenderer: Unsupported texture type with RGBAFormat.",s)}break;case qe:if(s===Mt)n=fv.RGB9E5UFloat;else console.error("WebGPURenderer: Unsupported texture type with RGBFormat.",s);break;case He:switch(s){case Ye:n=fv.R8Snorm;break;case Qe:n=fv.R16Sint;break;case Xe:n=fv.R16Uint;break;case b:n=fv.R32Uint;break;case x:n=fv.R32Sint;break;case pe:n=fv.R8Unorm;break;case ye:n=fv.R16Float;break;case E:n=fv.R32Float;break;default:console.error("WebGPURenderer: Unsupported texture type with RedFormat.",s)}break;case Ee:switch(s){case Ye:n=fv.RG8Snorm;break;case Qe:n=fv.RG16Sint;break;case Xe:n=fv.RG16Uint;break;case b:n=fv.RG32Uint;break;case x:n=fv.RG32Sint;break;case pe:n=fv.RG8Unorm;break;case ye:n=fv.RG16Float;break;case E:n=fv.RG32Float;break;default:console.error("WebGPURenderer: Unsupported texture type with RGFormat.",s)}break;case ce:switch(s){case Xe:n=fv.Depth16Unorm;break;case b:n=fv.Depth24Plus;break;case E:n=fv.Depth32Float;break;default:console.error("WebGPURenderer: Unsupported texture type with DepthFormat.",s)}break;case de:switch(s){case he:n=fv.Depth24PlusStencil8;break;case E:t&&!1===t.features.has(mN.Depth32FloatStencil8)&&console.error('WebGPURenderer: Depth textures with DepthStencilFormat + FloatType can only be used with the "depth32float-stencil8" GPU feature.'),n=fv.Depth32FloatStencil8;break;default:console.error("WebGPURenderer: Unsupported texture type with DepthStencilFormat.",s)}break;case $e:switch(s){case x:n=fv.R32Sint;break;case b:n=fv.R32Uint;break;default:console.error("WebGPURenderer: Unsupported texture type with RedIntegerFormat.",s)}break;case We:switch(s){case x:n=fv.RG32Sint;break;case b:n=fv.RG32Uint;break;default:console.error("WebGPURenderer: Unsupported texture type with RGIntegerFormat.",s)}break;case Ke:switch(s){case x:n=fv.RGBA32Sint;break;case b:n=fv.RGBA32Uint;break;default:console.error("WebGPURenderer: Unsupported texture type with RGBAIntegerFormat.",s)}break;default:console.error("WebGPURenderer: Unsupported texture format.",r)}return n}const RN=/^[fn]*\s*([a-z_0-9]+)?\s*\(([\s\S]*?)\)\s*[\-\>]*\s*([a-z_0-9]+(?:<[\s\S]+?>)?)/i,CN=/([a-z_0-9]+)\s*:\s*([a-z_0-9]+(?:<[\s\S]+?>)?)/gi,EN={f32:"float",i32:"int",u32:"uint",bool:"bool","vec2<f32>":"vec2","vec2<i32>":"ivec2","vec2<u32>":"uvec2","vec2<bool>":"bvec2",vec2f:"vec2",vec2i:"ivec2",vec2u:"uvec2",vec2b:"bvec2","vec3<f32>":"vec3","vec3<i32>":"ivec3","vec3<u32>":"uvec3","vec3<bool>":"bvec3",vec3f:"vec3",vec3i:"ivec3",vec3u:"uvec3",vec3b:"bvec3","vec4<f32>":"vec4","vec4<i32>":"ivec4","vec4<u32>":"uvec4","vec4<bool>":"bvec4",vec4f:"vec4",vec4i:"ivec4",vec4u:"uvec4",vec4b:"bvec4","mat2x2<f32>":"mat2",mat2x2f:"mat2","mat3x3<f32>":"mat3",mat3x3f:"mat3","mat4x4<f32>":"mat4",mat4x4f:"mat4",sampler:"sampler",texture_1d:"texture",texture_2d:"texture",texture_2d_array:"texture",texture_multisampled_2d:"cubeTexture",texture_depth_2d:"depthTexture",texture_depth_multisampled_2d:"depthTexture",texture_3d:"texture3D",texture_cube:"cubeTexture",texture_cube_array:"cubeTexture",texture_storage_1d:"storageTexture",texture_storage_2d:"storageTexture",texture_storage_2d_array:"storageTexture",texture_storage_3d:"storageTexture"};class wN extends kT{constructor(e){const{type:t,inputs:r,name:s,inputsCode:i,blockCode:n,outputType:o}=(e=>{const t=(e=e.trim()).match(RN);if(null!==t&&4===t.length){const r=t[2],s=[];let i=null;for(;null!==(i=CN.exec(r));)s.push({name:i[1],type:i[2]});const n=[];for(let e=0;e<s.length;e++){const{name:t,type:r}=s[e];let i=r;i.startsWith("ptr")?i="pointer":(i.startsWith("texture")&&(i=r.split("<")[0]),i=EN[i]),n.push(new wT(i,t))}const o=e.substring(t[0].length),a=t[3]||"void",u=void 0!==t[1]?t[1]:"";return{type:EN[a]||a,inputs:n,name:u,inputsCode:r,blockCode:o,outputType:a}}throw new Error("FunctionNode: Function is not a WGSL code.")})(e);super(t,r,s),this.inputsCode=i,this.blockCode=n,this.outputType=o}getCode(e=this.name){const t="void"!==this.outputType?"-> "+this.outputType:"";return`fn ${e} ( ${this.inputsCode.trim()} ) ${t}`+this.blockCode}}class MN extends GT{parseFunction(e){return new wN(e)}}const BN="undefined"!=typeof self?self.GPUShaderStage:{VERTEX:1,FRAGMENT:2,COMPUTE:4},FN={[Cs.READ_ONLY]:"read",[Cs.WRITE_ONLY]:"write",[Cs.READ_WRITE]:"read_write"},UN={[dr]:"repeat",[cr]:"clamp",[hr]:"mirror"},PN={vertex:BN?BN.VERTEX:1,fragment:BN?BN.FRAGMENT:2,compute:BN?BN.COMPUTE:4},IN={instance:!0,swizzleAssign:!1,storageBuffer:!0},DN={"^^":"tsl_xor"},LN={float:"f32",int:"i32",uint:"u32",bool:"bool",color:"vec3<f32>",vec2:"vec2<f32>",ivec2:"vec2<i32>",uvec2:"vec2<u32>",bvec2:"vec2<bool>",vec3:"vec3<f32>",ivec3:"vec3<i32>",uvec3:"vec3<u32>",bvec3:"vec3<bool>",vec4:"vec4<f32>",ivec4:"vec4<i32>",uvec4:"vec4<u32>",bvec4:"vec4<bool>",mat2:"mat2x2<f32>",mat3:"mat3x3<f32>",mat4:"mat4x4<f32>"},VN={},ON={tsl_xor:new dy("fn tsl_xor( a : bool, b : bool ) -> bool { return ( a || b ) && !( a && b ); }"),mod_float:new dy("fn tsl_mod_float( x : f32, y : f32 ) -> f32 { return x - y * floor( x / y ); }"),mod_vec2:new dy("fn tsl_mod_vec2( x : vec2f, y : vec2f ) -> vec2f { return x - y * floor( x / y ); }"),mod_vec3:new dy("fn tsl_mod_vec3( x : vec3f, y : vec3f ) -> vec3f { return x - y * floor( x / y ); }"),mod_vec4:new dy("fn tsl_mod_vec4( x : vec4f, y : vec4f ) -> vec4f { return x - y * floor( x / y ); }"),equals_bool:new dy("fn tsl_equals_bool( a : bool, b : bool ) -> bool { return a == b; }"),equals_bvec2:new dy("fn tsl_equals_bvec2( a : vec2f, b : vec2f ) -> vec2<bool> { return vec2<bool>( a.x == b.x, a.y == b.y ); }"),equals_bvec3:new dy("fn tsl_equals_bvec3( a : vec3f, b : vec3f ) -> vec3<bool> { return vec3<bool>( a.x == b.x, a.y == b.y, a.z == b.z ); }"),equals_bvec4:new dy("fn tsl_equals_bvec4( a : vec4f, b : vec4f ) -> vec4<bool> { return vec4<bool>( a.x == b.x, a.y == b.y, a.z == b.z, a.w == b.w ); }"),repeatWrapping_float:new dy("fn tsl_repeatWrapping_float( coord: f32 ) -> f32 { return fract( coord ); }"),mirrorWrapping_float:new dy("fn tsl_mirrorWrapping_float( coord: f32 ) -> f32 { let mirrored = fract( coord * 0.5 ) * 2.0; return 1.0 - abs( 1.0 - mirrored ); }"),clampWrapping_float:new dy("fn tsl_clampWrapping_float( coord: f32 ) -> f32 { return clamp( coord, 0.0, 1.0 ); }"),biquadraticTexture:new dy("\nfn tsl_biquadraticTexture( map : texture_2d<f32>, coord : vec2f, iRes : vec2u, level : u32 ) -> vec4f {\n\n\tlet res = vec2f( iRes );\n\n\tlet uvScaled = coord * res;\n\tlet uvWrapping = ( ( uvScaled % res ) + res ) % res;\n\n\t// https://www.shadertoy.com/view/WtyXRy\n\n\tlet uv = uvWrapping - 0.5;\n\tlet iuv = floor( uv );\n\tlet f = fract( uv );\n\n\tlet rg1 = textureLoad( map, vec2u( iuv + vec2( 0.5, 0.5 ) ) % iRes, level );\n\tlet rg2 = textureLoad( map, vec2u( iuv + vec2( 1.5, 0.5 ) ) % iRes, level );\n\tlet rg3 = textureLoad( map, vec2u( iuv + vec2( 0.5, 1.5 ) ) % iRes, level );\n\tlet rg4 = textureLoad( map, vec2u( iuv + vec2( 1.5, 1.5 ) ) % iRes, level );\n\n\treturn mix( mix( rg1, rg2, f.x ), mix( rg3, rg4, f.x ), f.y );\n\n}\n")},GN={dFdx:"dpdx",dFdy:"- dpdy",mod_float:"tsl_mod_float",mod_vec2:"tsl_mod_vec2",mod_vec3:"tsl_mod_vec3",mod_vec4:"tsl_mod_vec4",equals_bool:"tsl_equals_bool",equals_bvec2:"tsl_equals_bvec2",equals_bvec3:"tsl_equals_bvec3",equals_bvec4:"tsl_equals_bvec4",inversesqrt:"inverseSqrt",bitcast:"bitcast<f32>"};"undefined"!=typeof navigator&&/Windows/g.test(navigator.userAgent)&&(ON.pow_float=new dy("fn tsl_pow_float( a : f32, b : f32 ) -> f32 { return select( -pow( -a, b ), pow( a, b ), a > 0.0 ); }"),ON.pow_vec2=new dy("fn tsl_pow_vec2( a : vec2f, b : vec2f ) -> vec2f { return vec2f( tsl_pow_float( a.x, b.x ), tsl_pow_float( a.y, b.y ) ); }",[ON.pow_float]),ON.pow_vec3=new dy("fn tsl_pow_vec3( a : vec3f, b : vec3f ) -> vec3f { return vec3f( tsl_pow_float( a.x, b.x ), tsl_pow_float( a.y, b.y ), tsl_pow_float( a.z, b.z ) ); }",[ON.pow_float]),ON.pow_vec4=new dy("fn tsl_pow_vec4( a : vec4f, b : vec4f ) -> vec4f { return vec4f( tsl_pow_float( a.x, b.x ), tsl_pow_float( a.y, b.y ), tsl_pow_float( a.z, b.z ), tsl_pow_float( a.w, b.w ) ); }",[ON.pow_float]),GN.pow_float="tsl_pow_float",GN.pow_vec2="tsl_pow_vec2",GN.pow_vec3="tsl_pow_vec3",GN.pow_vec4="tsl_pow_vec4");let kN="";!0!==("undefined"!=typeof navigator&&/Firefox|Deno/g.test(navigator.userAgent))&&(kN+="diagnostic( off, derivative_uniformity );\n");class zN extends CT{constructor(e,t){super(e,t,new MN),this.uniformGroups={},this.builtins={},this.directives={},this.scopedArrays=new Map}needsToWorkingColorSpace(e){return!0===e.isVideoTexture&&e.colorSpace!==y}_generateTextureSample(e,t,r,s,i=this.shaderStage){return"fragment"===i?s?`textureSample( ${t}, ${t}_sampler, ${r}, ${s} )`:`textureSample( ${t}, ${t}_sampler, ${r} )`:this.isFilteredTexture(e)?this.generateFilteredTexture(e,t,r):this.generateTextureLod(e,t,r,s,"0")}_generateVideoSample(e,t,r=this.shaderStage){if("fragment"===r)return`textureSampleBaseClampToEdge( ${e}, ${e}_sampler, vec2<f32>( ${t}.x, 1.0 - ${t}.y ) )`;console.error(`WebGPURenderer: THREE.VideoTexture does not support ${r} shader.`)}_generateTextureSampleLevel(e,t,r,s,i,n=this.shaderStage){return"fragment"!==n&&"compute"!==n||!1!==this.isUnfilterable(e)?this.isFilteredTexture(e)?this.generateFilteredTexture(e,t,r,s):this.generateTextureLod(e,t,r,i,s):`textureSampleLevel( ${t}, ${t}_sampler, ${r}, ${s} )`}generateWrapFunction(e){const t=`tsl_coord_${UN[e.wrapS]}S_${UN[e.wrapT]}_${e.isData3DTexture?"3d":"2d"}T`;let r=VN[t];if(void 0===r){const s=[],i=e.isData3DTexture?"vec3f":"vec2f";let n=`fn ${t}( coord : ${i} ) -> ${i} {\n\n\treturn ${i}(\n`;const o=(e,t)=>{e===dr?(s.push(ON.repeatWrapping_float),n+=`\t\ttsl_repeatWrapping_float( coord.${t} )`):e===cr?(s.push(ON.clampWrapping_float),n+=`\t\ttsl_clampWrapping_float( coord.${t} )`):e===hr?(s.push(ON.mirrorWrapping_float),n+=`\t\ttsl_mirrorWrapping_float( coord.${t} )`):(n+=`\t\tcoord.${t}`,console.warn(`WebGPURenderer: Unsupported texture wrap type "${e}" for vertex shader.`))};o(e.wrapS,"x"),n+=",\n",o(e.wrapT,"y"),e.isData3DTexture&&(n+=",\n",o(e.wrapR,"z")),n+="\n\t);\n\n}\n",VN[t]=r=new dy(n,s)}return r.build(this),t}generateTextureDimension(e,t,r){const s=this.getDataFromNode(e,this.shaderStage,this.globalCache);void 0===s.dimensionsSnippet&&(s.dimensionsSnippet={});let i=s.dimensionsSnippet[r];if(void 0===s.dimensionsSnippet[r]){let n,o;const{primarySamples:a}=this.renderer.backend.utils.getTextureSampleData(e),u=a>1;o=e.isData3DTexture?"vec3<u32>":"vec2<u32>",n=u||e.isVideoTexture||e.isStorageTexture?t:`${t}${r?`, u32( ${r} )`:""}`,i=new Ma(new hu(`textureDimensions( ${n} )`,o)),s.dimensionsSnippet[r]=i,(e.isDataArrayTexture||e.isData3DTexture)&&(s.arrayLayerCount=new Ma(new hu(`textureNumLayers(${t})`,"u32"))),e.isTextureCube&&(s.cubeFaceCount=new Ma(new hu("6u","u32")))}return i.build(this)}generateFilteredTexture(e,t,r,s="0u"){this._include("biquadraticTexture");return`tsl_biquadraticTexture( ${t}, ${this.generateWrapFunction(e)}( ${r} ), ${this.generateTextureDimension(e,t,s)}, u32( ${s} ) )`}generateTextureLod(e,t,r,s,i="0u"){const n=this.generateWrapFunction(e),o=this.generateTextureDimension(e,t,i),a=e.isData3DTexture?"vec3":"vec2",u=`${a}<u32>(${n}(${r}) * ${a}<f32>(${o}))`;return this.generateTextureLoad(e,t,u,s,i)}generateTextureLoad(e,t,r,s,i="0u"){return!0===e.isVideoTexture||!0===e.isStorageTexture?`textureLoad( ${t}, ${r} )`:s?`textureLoad( ${t}, ${r}, ${s}, u32( ${i} ) )`:`textureLoad( ${t}, ${r}, u32( ${i} ) )`}generateTextureStore(e,t,r,s){return`textureStore( ${t}, ${r}, ${s} )`}isSampleCompare(e){return!0===e.isDepthTexture&&null!==e.compareFunction}isUnfilterable(e){return"float"!==this.getComponentTypeFromTexture(e)||!this.isAvailable("float32Filterable")&&!0===e.isDataTexture&&e.type===E||!1===this.isSampleCompare(e)&&e.minFilter===pr&&e.magFilter===pr||this.renderer.backend.utils.getTextureSampleData(e).primarySamples>1}generateTexture(e,t,r,s,i=this.shaderStage){let n=null;return n=!0===e.isVideoTexture?this._generateVideoSample(t,r,i):this.isUnfilterable(e)?this.generateTextureLod(e,t,r,s,"0",i):this._generateTextureSample(e,t,r,s,i),n}generateTextureGrad(e,t,r,s,i,n=this.shaderStage){if("fragment"===n)return`textureSampleGrad( ${t}, ${t}_sampler, ${r},  ${s[0]}, ${s[1]} )`;console.error(`WebGPURenderer: THREE.TextureNode.gradient() does not support ${n} shader.`)}generateTextureCompare(e,t,r,s,i,n=this.shaderStage){if("fragment"===n)return`textureSampleCompare( ${t}, ${t}_sampler, ${r}, ${s} )`;console.error(`WebGPURenderer: THREE.DepthTexture.compareFunction() does not support ${n} shader.`)}generateTextureLevel(e,t,r,s,i,n=this.shaderStage){let o=null;return o=!0===e.isVideoTexture?this._generateVideoSample(t,r,n):this._generateTextureSampleLevel(e,t,r,s,i,n),o}generateTextureBias(e,t,r,s,i,n=this.shaderStage){if("fragment"===n)return`textureSampleBias( ${t}, ${t}_sampler, ${r}, ${s} )`;console.error(`WebGPURenderer: THREE.TextureNode.biasNode does not support ${n} shader.`)}getPropertyName(e,t=this.shaderStage){if(!0===e.isNodeVarying&&!0===e.needsInterpolation){if("vertex"===t)return`varyings.${e.name}`}else if(!0===e.isNodeUniform){const t=e.name,r=e.type;return"texture"===r||"cubeTexture"===r||"storageTexture"===r||"texture3D"===r?t:"buffer"===r||"storageBuffer"===r||"indirectStorageBuffer"===r?`NodeBuffer_${e.id}.${t}`:e.groupNode.name+"."+t}return super.getPropertyName(e)}getOutputStructName(){return"output"}_getUniformGroupCount(e){return Object.keys(this.uniforms[e]).length}getFunctionOperator(e){const t=DN[e];return void 0!==t?(this._include(t),t):null}getNodeAccess(e,t){return"compute"!==t?Cs.READ_ONLY:e.access}getStorageAccess(e,t){return FN[this.getNodeAccess(e,t)]}getUniformFromNode(e,t,r,s=null){const i=super.getUniformFromNode(e,t,r,s),n=this.getDataFromNode(e,r,this.globalCache);if(void 0===n.uniformGPU){let s;const o=e.groupNode,a=o.name,u=this.getBindGroupArray(a,r);if("texture"===t||"cubeTexture"===t||"storageTexture"===t||"texture3D"===t){let n=null;const a=this.getNodeAccess(e,r);if("texture"===t||"storageTexture"===t?n=new x_(i.name,i.node,o,a):"cubeTexture"===t?n=new T_(i.name,i.node,o,a):"texture3D"===t&&(n=new __(i.name,i.node,o,a)),n.store=!0===e.isStorageTextureNode,n.setVisibility(PN[r]),"fragment"!==r&&"compute"!==r||!1!==this.isUnfilterable(e.value)||!1!==n.store)u.push(n),s=[n];else{const e=new yN(`${i.name}_sampler`,i.node,o);e.setVisibility(PN[r]),u.push(e,n),s=[e,n]}}else if("buffer"===t||"storageBuffer"===t||"indirectStorageBuffer"===t){const i=new("buffer"===t?p_:TN)(e,o);i.setVisibility(PN[r]),u.push(i),s=i}else{const e=this.uniformGroups[r]||(this.uniformGroups[r]={});let n=e[a];void 0===n&&(n=new f_(a,o),n.setVisibility(PN[r]),e[a]=n,u.push(n)),s=this.getNodeUniform(i,t),n.addUniform(s)}n.uniformGPU=s}return i}getBuiltin(e,t,r,s=this.shaderStage){const i=this.builtins[s]||(this.builtins[s]=new Map);return!1===i.has(e)&&i.set(e,{name:e,property:t,type:r}),t}hasBuiltin(e,t=this.shaderStage){return void 0!==this.builtins[t]&&this.builtins[t].has(e)}getVertexIndex(){return"vertex"===this.shaderStage?this.getBuiltin("vertex_index","vertexIndex","u32","attribute"):"vertexIndex"}buildFunctionCode(e){const t=e.layout,r=this.flowShaderNode(e),s=[];for(const e of t.inputs)s.push(e.name+" : "+this.getType(e.type));let i=`fn ${t.name}( ${s.join(", ")} ) -> ${this.getType(t.type)} {\n${r.vars}\n${r.code}\n`;return r.result&&(i+=`\treturn ${r.result};\n`),i+="\n}\n",i}getInstanceIndex(){return"vertex"===this.shaderStage?this.getBuiltin("instance_index","instanceIndex","u32","attribute"):"instanceIndex"}getInvocationLocalIndex(){return this.getBuiltin("local_invocation_index","invocationLocalIndex","u32","attribute")}getSubgroupSize(){return this.enableSubGroups(),this.getBuiltin("subgroup_size","subgroupSize","u32","attribute")}getInvocationSubgroupIndex(){return this.enableSubGroups(),this.getBuiltin("subgroup_invocation_id","invocationSubgroupIndex","u32","attribute")}getSubgroupIndex(){return this.enableSubGroups(),this.getBuiltin("subgroup_id","subgroupIndex","u32","attribute")}getDrawIndex(){return null}getFrontFacing(){return this.getBuiltin("front_facing","isFront","bool")}getFragCoord(){return this.getBuiltin("position","fragCoord","vec4<f32>")+".xy"}getFragDepth(){return"output."+this.getBuiltin("frag_depth","depth","f32","output")}getClipDistance(){return"varyings.hw_clip_distances"}isFlipY(){return!1}enableDirective(e,t=this.shaderStage){(this.directives[t]||(this.directives[t]=new Set)).add(e)}getDirectives(e){const t=[],r=this.directives[e];if(void 0!==r)for(const e of r)t.push(`enable ${e};`);return t.join("\n")}enableSubGroups(){this.enableDirective("subgroups")}enableSubgroupsF16(){this.enableDirective("subgroups-f16")}enableClipDistances(){this.enableDirective("clip_distances")}enableShaderF16(){this.enableDirective("f16")}enableDualSourceBlending(){this.enableDirective("dual_source_blending")}enableHardwareClipping(e){this.enableClipDistances(),this.getBuiltin("clip_distances","hw_clip_distances",`array<f32, ${e} >`,"vertex")}getBuiltins(e){const t=[],r=this.builtins[e];if(void 0!==r)for(const{name:e,property:s,type:i}of r.values())t.push(`@builtin( ${e} ) ${s} : ${i}`);return t.join(",\n\t")}getScopedArray(e,t,r,s){return!1===this.scopedArrays.has(e)&&this.scopedArrays.set(e,{name:e,scope:t,bufferType:r,bufferCount:s}),e}getScopedArrays(e){if("compute"!==e)return;const t=[];for(const{name:e,scope:r,bufferType:s,bufferCount:i}of this.scopedArrays.values()){const n=this.getType(s);t.push(`var<${r}> ${e}: array< ${n}, ${i} >;`)}return t.join("\n")}getAttributes(e){const t=[];if("compute"===e&&(this.getBuiltin("global_invocation_id","id","vec3<u32>","attribute"),this.getBuiltin("workgroup_id","workgroupId","vec3<u32>","attribute"),this.getBuiltin("local_invocation_id","localId","vec3<u32>","attribute"),this.getBuiltin("num_workgroups","numWorkgroups","vec3<u32>","attribute"),this.renderer.hasFeature("subgroups")&&(this.enableDirective("subgroups",e),this.getBuiltin("subgroup_size","subgroupSize","u32","attribute"))),"vertex"===e||"compute"===e){const e=this.getBuiltins("attribute");e&&t.push(e);const r=this.getAttributesArray();for(let e=0,s=r.length;e<s;e++){const s=r[e],i=s.name,n=this.getType(s.type);t.push(`@location( ${e} ) ${i} : ${n}`)}}return t.join(",\n\t")}getStructMembers(e){const t=[],r=e.getMemberTypes();for(let e=0;e<r.length;e++){const s=r[e];t.push(`\t@location( ${e} ) m${e} : ${s}<f32>`)}const s=this.getBuiltins("output");return s&&t.push("\t"+s),t.join(",\n")}getStructs(e){const t=[],r=this.structs[e];for(let e=0,s=r.length;e<s;e++){const s=r[e],i=s.name;let n=`struct ${i} {\n`;n+=this.getStructMembers(s),n+="\n}",t.push(n),t.push(`\nvar<private> output : ${i};\n\n`)}return t.join("\n\n")}getVar(e,t){return`var ${t} : ${this.getType(e)}`}getVars(e){const t=[],r=this.vars[e];if(void 0!==r)for(const e of r)t.push(`\t${this.getVar(e.type,e.name)};`);return`\n${t.join("\n")}\n`}getVaryings(e){const t=[];if("vertex"===e&&this.getBuiltin("position","Vertex","vec4<f32>","vertex"),"vertex"===e||"fragment"===e){const r=this.varyings,s=this.vars[e];for(let i=0;i<r.length;i++){const n=r[i];if(n.needsInterpolation){let e=`@location( ${i} )`;/^(int|uint|ivec|uvec)/.test(n.type)&&(e+=" @interpolate( flat )"),t.push(`${e} ${n.name} : ${this.getType(n.type)}`)}else"vertex"===e&&!1===s.includes(n)&&s.push(n)}}const r=this.getBuiltins(e);r&&t.push(r);const s=t.join(",\n\t");return"vertex"===e?this._getWGSLStruct("VaryingsStruct","\t"+s):s}getUniforms(e){const t=this.uniforms[e],r=[],s=[],i=[],n={};for(const i of t){const t=i.groupNode.name,o=this.bindingsIndexes[t];if("texture"===i.type||"cubeTexture"===i.type||"storageTexture"===i.type||"texture3D"===i.type){const t=i.node.value;let s;"fragment"!==e&&"compute"!==e||!1!==this.isUnfilterable(t)||!0===i.node.isStorageTextureNode||(this.isSampleCompare(t)?r.push(`@binding( ${o.binding++} ) @group( ${o.group} ) var ${i.name}_sampler : sampler_comparison;`):r.push(`@binding( ${o.binding++} ) @group( ${o.group} ) var ${i.name}_sampler : sampler;`));let n="";const{primarySamples:a}=this.renderer.backend.utils.getTextureSampleData(t);if(a>1&&(n="_multisampled"),!0===t.isCubeTexture)s="texture_cube<f32>";else if(!0===t.isDataArrayTexture||!0===t.isCompressedArrayTexture)s="texture_2d_array<f32>";else if(!0===t.isDepthTexture)s=`texture_depth${n}_2d`;else if(!0===t.isVideoTexture)s="texture_external";else if(!0===t.isData3DTexture)s="texture_3d<f32>";else if(!0===i.node.isStorageTextureNode){s=`texture_storage_2d<${AN(t)}, ${this.getStorageAccess(i.node,e)}>`}else{s=`texture${n}_2d<${this.getComponentTypeFromTexture(t).charAt(0)}32>`}r.push(`@binding( ${o.binding++} ) @group( ${o.group} ) var ${i.name} : ${s};`)}else if("buffer"===i.type||"storageBuffer"===i.type||"indirectStorageBuffer"===i.type){const t=i.node,r=this.getType(t.bufferType),n=t.bufferCount,a=n>0&&"buffer"===i.type?", "+n:"",u=t.isAtomic?`atomic<${r}>`:`${r}`,l=`\t${i.name} : array< ${u}${a} >\n`,d=t.isStorageBufferNode?`storage, ${this.getStorageAccess(t,e)}`:"uniform";s.push(this._getWGSLStructBinding("NodeBuffer_"+t.id,l,d,o.binding++,o.group))}else{const e=this.getType(this.getVectorType(i.type)),t=i.groupNode.name;(n[t]||(n[t]={index:o.binding++,id:o.group,snippets:[]})).snippets.push(`\t${i.name} : ${e}`)}}for(const e in n){const t=n[e];i.push(this._getWGSLStructBinding(e,t.snippets.join(",\n"),"uniform",t.index,t.id))}let o=r.join("\n");return o+=s.join("\n"),o+=i.join("\n"),o}buildCode(){const e=null!==this.material?{fragment:{},vertex:{}}:{compute:{}};this.sortBindingGroups();for(const t in e){const r=e[t];r.uniforms=this.getUniforms(t),r.attributes=this.getAttributes(t),r.varyings=this.getVaryings(t),r.structs=this.getStructs(t),r.vars=this.getVars(t),r.codes=this.getCodes(t),r.directives=this.getDirectives(t),r.scopedArrays=this.getScopedArrays(t);let s="// code\n\n";s+=this.flowCode[t];const i=this.flowNodes[t],n=i[i.length-1],o=n.outputNode,a=void 0!==o&&!0===o.isOutputStructNode;for(const e of i){const i=this.getFlowData(e),u=e.name;if(u&&(s.length>0&&(s+="\n"),s+=`\t// flow -> ${u}\n\t`),s+=`${i.code}\n\t`,e===n&&"compute"!==t)if(s+="// result\n\n\t","vertex"===t)s+=`varyings.Vertex = ${i.result};`;else if("fragment"===t)if(a)r.returnType=o.nodeType,s+=`return ${i.result};`;else{let e="\t@location(0) color: vec4<f32>";const t=this.getBuiltins("output");t&&(e+=",\n\t"+t),r.returnType="OutputStruct",r.structs+=this._getWGSLStruct("OutputStruct",e),r.structs+="\nvar<private> output : OutputStruct;\n\n",s+=`output.color = ${i.result};\n\n\treturn output;`}}r.flow=s}null!==this.material?(this.vertexShader=this._getWGSLVertexCode(e.vertex),this.fragmentShader=this._getWGSLFragmentCode(e.fragment)):this.computeShader=this._getWGSLComputeCode(e.compute,(this.object.workgroupSize||[64]).join(", "))}getMethod(e,t=null){let r;return null!==t&&(r=this._getWGSLMethod(e+"_"+t)),void 0===r&&(r=this._getWGSLMethod(e)),r||e}getType(e){return LN[e]||e}isAvailable(e){let t=IN[e];return void 0===t&&("float32Filterable"===e?t=this.renderer.hasFeature("float32-filterable"):"clipDistance"===e&&(t=this.renderer.hasFeature("clip-distances")),IN[e]=t),t}_getWGSLMethod(e){return void 0!==ON[e]&&this._include(e),GN[e]}_include(e){const t=ON[e];return t.build(this),null!==this.currentFunctionNode&&this.currentFunctionNode.includes.push(t),t}_getWGSLVertexCode(e){return`${this.getSignature()}\n// directives\n${e.directives}\n\n// uniforms\n${e.uniforms}\n\n// varyings\n${e.varyings}\nvar<private> varyings : VaryingsStruct;\n\n// codes\n${e.codes}\n\n@vertex\nfn main( ${e.attributes} ) -> VaryingsStruct {\n\n\t// vars\n\t${e.vars}\n\n\t// flow\n\t${e.flow}\n\n\treturn varyings;\n\n}\n`}_getWGSLFragmentCode(e){return`${this.getSignature()}\n// global\n${kN}\n\n// uniforms\n${e.uniforms}\n\n// structs\n${e.structs}\n\n// codes\n${e.codes}\n\n@fragment\nfn main( ${e.varyings} ) -> ${e.returnType} {\n\n\t// vars\n\t${e.vars}\n\n\t// flow\n\t${e.flow}\n\n}\n`}_getWGSLComputeCode(e,t){return`${this.getSignature()}\n// directives\n${e.directives}\n\n// system\nvar<private> instanceIndex : u32;\n\n// locals\n${e.scopedArrays}\n\n// uniforms\n${e.uniforms}\n\n// codes\n${e.codes}\n\n@compute @workgroup_size( ${t} )\nfn main( ${e.attributes} ) {\n\n\t// system\n\tinstanceIndex = id.x + id.y * numWorkgroups.x * u32(${t}) + id.z * numWorkgroups.x * numWorkgroups.y * u32(${t});\n\n\t// vars\n\t${e.vars}\n\n\t// flow\n\t${e.flow}\n\n}\n`}_getWGSLStruct(e,t){return`\nstruct ${e} {\n${t}\n};`}_getWGSLStructBinding(e,t,r,s=0,i=0){const n=e+"Struct";return`${this._getWGSLStruct(n,t)}\n@binding( ${s} ) @group( ${i} )\nvar<${r}> ${e} : ${n};`}}class $N{constructor(e){this.backend=e}getCurrentDepthStencilFormat(e){let t;return null!==e.depthTexture?t=this.getTextureFormatGPU(e.depthTexture):e.depth&&e.stencil?t=fv.Depth24PlusStencil8:e.depth&&(t=fv.Depth24Plus),t}getTextureFormatGPU(e){return this.backend.get(e).format}getTextureSampleData(e){let t;if(e.isFramebufferTexture)t=1;else if(e.isDepthTexture&&!e.renderTarget){const e=this.backend.renderer,r=e.getRenderTarget();t=r?r.samples:e.samples}else e.renderTarget&&(t=e.renderTarget.samples);t=t||1;const r=t>1&&null!==e.renderTarget&&!0!==e.isDepthTexture&&!0!==e.isFramebufferTexture;return{samples:t,primarySamples:r?1:t,isMSAA:r}}getCurrentColorFormat(e){let t;return t=null!==e.textures?this.getTextureFormatGPU(e.textures[0]):this.getPreferredCanvasFormat(),t}getCurrentColorSpace(e){return null!==e.textures?e.textures[0].colorSpace:this.backend.renderer.outputColorSpace}getPrimitiveTopology(e,t){return e.isPoints?K_:e.isLineSegments||e.isMesh&&!0===t.wireframe?X_:e.isLine?Y_:e.isMesh?Q_:void 0}getSampleCount(e){let t=1;return e>1&&(t=Math.pow(2,Math.floor(Math.log2(e))),2===t&&(t=4)),t}getSampleCountRenderContext(e){return null!==e.textures?this.getSampleCount(e.sampleCount):this.getSampleCount(this.backend.renderer.samples)}getPreferredCanvasFormat(){return navigator.userAgent.includes("Quest")?fv.BGRA8Unorm:navigator.gpu.getPreferredCanvasFormat()}}const HN=new Map([[Int8Array,["sint8","snorm8"]],[Uint8Array,["uint8","unorm8"]],[Int16Array,["sint16","snorm16"]],[Uint16Array,["uint16","unorm16"]],[Int32Array,["sint32","snorm32"]],[Uint32Array,["uint32","unorm32"]],[Float32Array,["float32"]]]),WN=new Map([[De,["float16"]]]),jN=new Map([[Int32Array,"sint32"],[Int16Array,"sint32"],[Uint32Array,"uint32"],[Uint16Array,"uint32"],[Float32Array,"float32"]]);class qN{constructor(e){this.backend=e}createAttribute(e,t){const r=this._getBufferAttribute(e),s=this.backend,i=s.get(r);let n=i.buffer;if(void 0===n){const o=s.device;let a=r.array;if(!1===e.normalized&&(a.constructor===Int16Array||a.constructor===Uint16Array)){const e=new Uint32Array(a.length);for(let t=0;t<a.length;t++)e[t]=a[t];a=e}if(r.array=a,(r.isStorageBufferAttribute||r.isStorageInstancedBufferAttribute)&&3===r.itemSize){a=new a.constructor(4*r.count);for(let e=0;e<r.count;e++)a.set(r.array.subarray(3*e,3*e+3),4*e);r.itemSize=4,r.array=a}const u=a.byteLength+(4-a.byteLength%4)%4;n=o.createBuffer({label:r.name,size:u,usage:t,mappedAtCreation:!0}),new a.constructor(n.getMappedRange()).set(a),n.unmap(),i.buffer=n}}updateAttribute(e){const t=this._getBufferAttribute(e),r=this.backend,s=r.device,i=r.get(t).buffer,n=t.array,o=this._isTypedArray(n),a=t.updateRanges;if(0===a.length)s.queue.writeBuffer(i,0,n,0);else{const e=o?1:n.BYTES_PER_ELEMENT;for(let t=0,r=a.length;t<r;t++){const r=a[t],o=r.start*e,u=r.count*e;s.queue.writeBuffer(i,0,n,o,u)}t.clearUpdateRanges()}}createShaderVertexBuffers(e){const t=e.getAttributes(),r=new Map;for(let e=0;e<t.length;e++){const s=t[e],i=s.array.BYTES_PER_ELEMENT,n=this._getBufferAttribute(s);let o=r.get(n);if(void 0===o){let e,t;!0===s.isInterleavedBufferAttribute?(e=s.data.stride*i,t=s.data.isInstancedInterleavedBuffer?gN:pN):(e=s.itemSize*i,t=s.isInstancedBufferAttribute?gN:pN),!1!==s.normalized||s.array.constructor!==Int16Array&&s.array.constructor!==Uint16Array||(e=4),o={arrayStride:e,attributes:[],stepMode:t},r.set(n,o)}const a=this._getVertexFormat(s),u=!0===s.isInterleavedBufferAttribute?s.offset*i:0;o.attributes.push({shaderLocation:e,offset:u,format:a})}return Array.from(r.values())}destroyAttribute(e){const t=this.backend;t.get(this._getBufferAttribute(e)).buffer.destroy(),t.delete(e)}async getArrayBufferAsync(e){const t=this.backend,r=t.device,s=t.get(this._getBufferAttribute(e)).buffer,i=s.size,n=r.createBuffer({label:`${e.name}_readback`,size:i,usage:GPUBufferUsage.COPY_DST|GPUBufferUsage.MAP_READ}),o=r.createCommandEncoder({label:`readback_encoder_${e.name}`});o.copyBufferToBuffer(s,0,n,0,i);const a=o.finish();r.queue.submit([a]),await n.mapAsync(GPUMapMode.READ);const u=n.getMappedRange(),l=new e.array.constructor(u.slice(0));return n.unmap(),l.buffer}_getVertexFormat(e){const{itemSize:t,normalized:r}=e,s=e.array.constructor,i=e.constructor;let n;if(1===t)n=jN.get(s);else{const e=(WN.get(i)||HN.get(s))[r?1:0];if(e){const r=s.BYTES_PER_ELEMENT*t,i=4*Math.floor((r+3)/4)/s.BYTES_PER_ELEMENT;if(i%1)throw new Error("THREE.WebGPUAttributeUtils: Bad vertex format item size.");n=`${e}x${i}`}}return n||console.error("THREE.WebGPUAttributeUtils: Vertex format not supported yet."),n}_isTypedArray(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}_getBufferAttribute(e){return e.isInterleavedBufferAttribute&&(e=e.data),e}}class KN{constructor(e){this.backend=e,this.bindGroupLayoutCache=new WeakMap}createBindingsLayout(e){const t=this.backend,r=t.device,s=[];let i=0;for(const r of e.bindings){const e={binding:i++,visibility:r.visibility};if(r.isUniformBuffer||r.isStorageBuffer){const t={};r.isStorageBuffer&&(4&r.visibility&&(r.access===Cs.READ_WRITE||r.access===Cs.WRITE_ONLY)?t.type=Yv:t.type=Qv),e.buffer=t}else if(r.isSampler){const t={};r.texture.isDepthTexture&&null!==r.texture.compareFunction&&(t.type="comparison"),e.sampler=t}else if(r.isSampledTexture&&r.texture.isVideoTexture)e.externalTexture={};else if(r.isSampledTexture&&r.store){const t={};t.format=this.backend.get(r.texture).texture.format;const s=r.access;t.access=s===Cs.READ_WRITE?eN:s===Cs.WRITE_ONLY?Zv:Jv,e.storageTexture=t}else if(r.isSampledTexture){const s={},{primarySamples:i}=t.utils.getTextureSampleData(r.texture);if(i>1&&(s.multisampled=!0,r.texture.isDepthTexture||(s.sampleType=rN)),r.texture.isDepthTexture)s.sampleType=sN;else if(r.texture.isDataTexture||r.texture.isDataArrayTexture||r.texture.isData3DTexture){const e=r.texture.type;e===x?s.sampleType=iN:e===b?s.sampleType=nN:e===E&&(this.backend.hasFeature("float32-filterable")?s.sampleType=tN:s.sampleType=rN)}r.isSampledCubeTexture?s.viewDimension=dN:r.texture.isDataArrayTexture||r.texture.isCompressedArrayTexture?s.viewDimension=lN:r.isSampledTexture3D&&(s.viewDimension=cN),e.texture=s}else console.error(`WebGPUBindingUtils: Unsupported binding "${r}".`);s.push(e)}return r.createBindGroupLayout({entries:s})}createBindings(e,t,r,s=0){const{backend:i,bindGroupLayoutCache:n}=this,o=i.get(e);let a,u=n.get(e.bindingsReference);void 0===u&&(u=this.createBindingsLayout(e),n.set(e.bindingsReference,u)),r>0&&(void 0===o.groups&&(o.groups=[],o.versions=[]),o.versions[r]===s&&(a=o.groups[r])),void 0===a&&(a=this.createBindGroup(e,u),r>0&&(o.groups[r]=a,o.versions[r]=s)),o.group=a,o.layout=u}updateBinding(e){const t=this.backend,r=t.device,s=e.buffer,i=t.get(e).buffer;r.queue.writeBuffer(i,0,s,0)}createBindGroup(e,t){const r=this.backend,s=r.device;let i=0;const n=[];for(const t of e.bindings){if(t.isUniformBuffer){const e=r.get(t);if(void 0===e.buffer){const r=t.byteLength,i=GPUBufferUsage.UNIFORM|GPUBufferUsage.COPY_DST,n=s.createBuffer({label:"bindingBuffer_"+t.name,size:r,usage:i});e.buffer=n}n.push({binding:i,resource:{buffer:e.buffer}})}else if(t.isStorageBuffer){const e=r.get(t);if(void 0===e.buffer){const s=t.attribute;e.buffer=r.get(s).buffer}n.push({binding:i,resource:{buffer:e.buffer}})}else if(t.isSampler){const e=r.get(t.texture);n.push({binding:i,resource:e.sampler})}else if(t.isSampledTexture){const e=r.get(t.texture);let o;if(void 0!==e.externalTexture)o=s.importExternalTexture({source:e.externalTexture});else{const r=t.store?1:e.texture.mipLevelCount,s=`view-${e.texture.width}-${e.texture.height}-${r}`;if(o=e[s],void 0===o){const i=hN;let n;n=t.isSampledCubeTexture?dN:t.isSampledTexture3D?cN:t.texture.isDataArrayTexture||t.texture.isCompressedArrayTexture?lN:uN,o=e[s]=e.texture.createView({aspect:i,dimension:n,mipLevelCount:r})}}n.push({binding:i,resource:o})}i++}return s.createBindGroup({label:"bindGroup_"+e.name,layout:t,entries:n})}}class XN{constructor(e){this.backend=e}_getSampleCount(e){return this.backend.utils.getSampleCountRenderContext(e)}createRenderPipeline(e,t){const{object:r,material:s,geometry:i,pipeline:n}=e,{vertexProgram:o,fragmentProgram:a}=n,u=this.backend,l=u.device,d=u.utils,c=u.get(n),h=[];for(const t of e.getBindings()){const e=u.get(t);h.push(e.layout)}const p=u.attributeUtils.createShaderVertexBuffers(e);let g;!0===s.transparent&&s.blending!==L&&(g=this._getBlending(s));let m={};!0===s.stencilWrite&&(m={compare:this._getStencilCompare(s),failOp:this._getStencilOperation(s.stencilFail),depthFailOp:this._getStencilOperation(s.stencilZFail),passOp:this._getStencilOperation(s.stencilZPass)});const f=this._getColorWriteMask(s),y=[];if(null!==e.context.textures){const t=e.context.textures;for(let e=0;e<t.length;e++){const r=d.getTextureFormatGPU(t[e]);y.push({format:r,blend:g,writeMask:f})}}else{const t=d.getCurrentColorFormat(e.context);y.push({format:t,blend:g,writeMask:f})}const b=u.get(o).module,x=u.get(a).module,T=this._getPrimitiveState(r,i,s),_=this._getDepthCompare(s),v=d.getCurrentDepthStencilFormat(e.context),N=this._getSampleCount(e.context),S={label:`renderPipeline_${s.name||s.type}_${s.id}`,vertex:Object.assign({},b,{buffers:p}),fragment:Object.assign({},x,{targets:y}),primitive:T,multisample:{count:N,alphaToCoverageEnabled:s.alphaToCoverage&&N>1},layout:l.createPipelineLayout({bindGroupLayouts:h})},A={},R=e.context.depth,C=e.context.stencil;if(!0!==R&&!0!==C||(!0===R&&(A.format=v,A.depthWriteEnabled=s.depthWrite,A.depthCompare=_),!0===C&&(A.stencilFront=m,A.stencilBack={},A.stencilReadMask=s.stencilFuncMask,A.stencilWriteMask=s.stencilWriteMask),S.depthStencil=A),null===t)c.pipeline=l.createRenderPipeline(S);else{const e=new Promise((e=>{l.createRenderPipelineAsync(S).then((t=>{c.pipeline=t,e()}))}));t.push(e)}}createBundleEncoder(e){const t=this.backend,{utils:r,device:s}=t,i=r.getCurrentDepthStencilFormat(e),n={label:"renderBundleEncoder",colorFormats:[r.getCurrentColorFormat(e)],depthStencilFormat:i,sampleCount:this._getSampleCount(e)};return s.createRenderBundleEncoder(n)}createComputePipeline(e,t){const r=this.backend,s=r.device,i=r.get(e.computeProgram).module,n=r.get(e),o=[];for(const e of t){const t=r.get(e);o.push(t.layout)}n.pipeline=s.createComputePipeline({compute:i,layout:s.createPipelineLayout({bindGroupLayouts:o})})}_getBlending(e){let t,r;const s=e.blending,i=e.blendSrc,n=e.blendDst,o=e.blendEquation;if(s===ft){const s=null!==e.blendSrcAlpha?e.blendSrcAlpha:i,a=null!==e.blendDstAlpha?e.blendDstAlpha:n,u=null!==e.blendEquationAlpha?e.blendEquationAlpha:o;t={srcFactor:this._getBlendFactor(i),dstFactor:this._getBlendFactor(n),operation:this._getBlendOperation(o)},r={srcFactor:this._getBlendFactor(s),dstFactor:this._getBlendFactor(a),operation:this._getBlendOperation(u)}}else{const i=(e,s,i,n)=>{t={srcFactor:e,dstFactor:s,operation:Iv},r={srcFactor:i,dstFactor:n,operation:Iv}};if(e.premultipliedAlpha)switch(s){case U:i(Nv,Cv,Nv,Cv);break;case xt:i(Nv,Nv,Nv,Nv);break;case bt:i(vv,Av,vv,Nv);break;case yt:i(vv,Sv,vv,Rv)}else switch(s){case U:i(Rv,Cv,Nv,Cv);break;case xt:i(Rv,Nv,Rv,Nv);break;case bt:i(vv,Av,vv,Nv);break;case yt:i(vv,Sv,vv,Sv)}}if(void 0!==t&&void 0!==r)return{color:t,alpha:r};console.error("THREE.WebGPURenderer: Invalid blending: ",s)}_getBlendFactor(e){let t;switch(e){case rt:t=vv;break;case st:t=Nv;break;case it:t=Sv;break;case lt:t=Av;break;case nt:t=Rv;break;case dt:t=Cv;break;case at:t=Ev;break;case ct:t=wv;break;case ut:t=Mv;break;case ht:t=Bv;break;case ot:t=Fv;break;case 211:t=Uv;break;case 212:t=Pv;break;default:console.error("THREE.WebGPURenderer: Blend factor not supported.",e)}return t}_getStencilCompare(e){let t;const r=e.stencilFunc;switch(r){case Mr:t=J_;break;case wr:t=ov;break;case Er:t=ev;break;case Cr:t=rv;break;case Rr:t=tv;break;case Ar:t=nv;break;case Sr:t=sv;break;case Nr:t=iv;break;default:console.error("THREE.WebGPURenderer: Invalid stencil function.",r)}return t}_getStencilOperation(e){let t;switch(e){case Vr:t=zv;break;case Lr:t=$v;break;case Dr:t=Hv;break;case Ir:t=Wv;break;case Pr:t=jv;break;case Ur:t=qv;break;case Fr:t=Kv;break;case Br:t=Xv;break;default:console.error("THREE.WebGPURenderer: Invalid stencil operation.",t)}return t}_getBlendOperation(e){let t;switch(e){case Je:t=Iv;break;case et:t=Dv;break;case tt:t=Lv;break;case Gr:t=Vv;break;case Or:t=Ov;break;default:console.error("THREE.WebGPUPipelineUtils: Blend equation not supported.",e)}return t}_getPrimitiveState(e,t,r){const s={},i=this.backend.utils;switch(s.topology=i.getPrimitiveTopology(e,r),null!==t.index&&!0===e.isLine&&!0!==e.isLineSegments&&(s.stripIndexFormat=t.index.array instanceof Uint16Array?gv:mv),r.side){case Ge:s.frontFace=dv,s.cullMode=pv;break;case T:s.frontFace=dv,s.cullMode=hv;break;case le:s.frontFace=dv,s.cullMode=cv;break;default:console.error("THREE.WebGPUPipelineUtils: Unknown material.side value.",r.side)}return s}_getColorWriteMask(e){return!0===e.colorWrite?kv:Gv}_getDepthCompare(e){let t;if(!1===e.depthTest)t=ov;else{const r=e.depthFunc;switch(r){case Ct:t=J_;break;case Rt:t=ov;break;case At:t=ev;break;case St:t=rv;break;case Nt:t=tv;break;case vt:t=nv;break;case _t:t=sv;break;case Tt:t=iv;break;default:console.error("THREE.WebGPUPipelineUtils: Invalid depth function.",r)}}return t}}class YN extends w_{constructor(e={}){super(e),this.isWebGPUBackend=!0,this.parameters.alpha=void 0===e.alpha||e.alpha,this.parameters.requiredLimits=void 0===e.requiredLimits?{}:e.requiredLimits,this.trackTimestamp=!0===e.trackTimestamp,this.device=null,this.context=null,this.colorBuffer=null,this.defaultRenderPassdescriptor=null,this.utils=new $N(this),this.attributeUtils=new qN(this),this.bindingUtils=new KN(this),this.pipelineUtils=new XN(this),this.textureUtils=new SN(this),this.occludedResolveCache=new Map}async init(e){await super.init(e);const t=this.parameters;let r;if(void 0===t.device){const e={powerPreference:t.powerPreference},s="undefined"!=typeof navigator?await navigator.gpu.requestAdapter(e):null;if(null===s)throw new Error("WebGPUBackend: Unable to create WebGPU adapter.");const i=Object.values(mN),n=[];for(const e of i)s.features.has(e)&&n.push(e);const o={requiredFeatures:n,requiredLimits:t.requiredLimits};r=await s.requestDevice(o)}else r=t.device;r.lost.then((t=>{const r={api:"WebGPU",message:t.message||"Unknown reason",reason:t.reason||null,originalEvent:t};e.onDeviceLost(r)}));const s=void 0!==t.context?t.context:e.domElement.getContext("webgpu");this.device=r,this.context=s;const i=t.alpha?"premultiplied":"opaque";this.trackTimestamp=this.trackTimestamp&&this.hasFeature(mN.TimestampQuery),this.context.configure({device:this.device,format:this.utils.getPreferredCanvasFormat(),usage:GPUTextureUsage.RENDER_ATTACHMENT|GPUTextureUsage.COPY_SRC,alphaMode:i}),this.updateSize()}get coordinateSystem(){return l}async getArrayBufferAsync(e){return await this.attributeUtils.getArrayBufferAsync(e)}getContext(){return this.context}_getDefaultRenderPassDescriptor(){let e=this.defaultRenderPassdescriptor;if(null===e){const t=this.renderer;e={colorAttachments:[{view:null}]},!0!==this.renderer.depth&&!0!==this.renderer.stencil||(e.depthStencilAttachment={view:this.textureUtils.getDepthBuffer(t.depth,t.stencil).createView()});const r=e.colorAttachments[0];this.renderer.samples>0?r.view=this.colorBuffer.createView():r.resolveTarget=void 0,this.defaultRenderPassdescriptor=e}const t=e.colorAttachments[0];return this.renderer.samples>0?t.resolveTarget=this.context.getCurrentTexture().createView():t.view=this.context.getCurrentTexture().createView(),e}_getRenderPassDescriptor(e,t={}){const r=e.renderTarget,s=this.get(r);let i=s.descriptors;if(void 0===i||s.width!==r.width||s.height!==r.height||s.dimensions!==r.dimensions||s.activeMipmapLevel!==r.activeMipmapLevel||s.activeCubeFace!==e.activeCubeFace||s.samples!==r.samples||s.loadOp!==t.loadOp){i={},s.descriptors=i;const e=()=>{r.removeEventListener("dispose",e),this.delete(r)};r.addEventListener("dispose",e)}const n=e.getCacheKey();let o=i[n];if(void 0===o){const a=e.textures,u=[];let l;for(let s=0;s<a.length;s++){const i=this.get(a[s]),n={label:`colorAttachment_${s}`,baseMipLevel:e.activeMipmapLevel,mipLevelCount:1,baseArrayLayer:e.activeCubeFace,arrayLayerCount:1,dimension:uN};r.isRenderTarget3D?(l=e.activeCubeFace,n.baseArrayLayer=0,n.dimension=cN,n.depthOrArrayLayers=a[s].image.depth):r.isRenderTargetArray&&(n.dimension=lN,n.depthOrArrayLayers=a[s].image.depth);const o=i.texture.createView(n);let d,c;void 0!==i.msaaTexture?(d=i.msaaTexture.createView(),c=o):(d=o,c=void 0),u.push({view:d,depthSlice:l,resolveTarget:c,loadOp:uv,storeOp:av,...t})}if(o={colorAttachments:u},e.depth){const t={view:this.get(e.depthTexture).texture.createView()};o.depthStencilAttachment=t}i[n]=o,s.width=r.width,s.height=r.height,s.samples=r.samples,s.activeMipmapLevel=e.activeMipmapLevel,s.activeCubeFace=e.activeCubeFace,s.dimensions=r.dimensions,s.depthSlice=l,s.loadOp=u[0].loadOp}return o}beginRender(e){const t=this.get(e),r=this.device,s=e.occlusionQueryCount;let i,n;s>0&&(t.currentOcclusionQuerySet&&t.currentOcclusionQuerySet.destroy(),t.currentOcclusionQueryBuffer&&t.currentOcclusionQueryBuffer.destroy(),t.currentOcclusionQuerySet=t.occlusionQuerySet,t.currentOcclusionQueryBuffer=t.occlusionQueryBuffer,t.currentOcclusionQueryObjects=t.occlusionQueryObjects,i=r.createQuerySet({type:"occlusion",count:s,label:`occlusionQuerySet_${e.id}`}),t.occlusionQuerySet=i,t.occlusionQueryIndex=0,t.occlusionQueryObjects=new Array(s),t.lastOcclusionObject=null),n=null===e.textures?this._getDefaultRenderPassDescriptor():this._getRenderPassDescriptor(e,{loadOp:uv}),this.initTimestampQuery(e,n),n.occlusionQuerySet=i;const o=n.depthStencilAttachment;if(null!==e.textures){const t=n.colorAttachments;for(let r=0;r<t.length;r++){const s=t[r];e.clearColor?(s.clearValue=0===r?e.clearColorValue:{r:0,g:0,b:0,a:1},s.loadOp=lv,s.storeOp=av):(s.loadOp=uv,s.storeOp=av)}}else{const t=n.colorAttachments[0];e.clearColor?(t.clearValue=e.clearColorValue,t.loadOp=lv,t.storeOp=av):(t.loadOp=uv,t.storeOp=av)}e.depth&&(e.clearDepth?(o.depthClearValue=e.clearDepthValue,o.depthLoadOp=lv,o.depthStoreOp=av):(o.depthLoadOp=uv,o.depthStoreOp=av)),e.stencil&&(e.clearStencil?(o.stencilClearValue=e.clearStencilValue,o.stencilLoadOp=lv,o.stencilStoreOp=av):(o.stencilLoadOp=uv,o.stencilStoreOp=av));const a=r.createCommandEncoder({label:"renderContext_"+e.id}),u=a.beginRenderPass(n);if(t.descriptor=n,t.encoder=a,t.currentPass=u,t.currentSets={attributes:{},bindingGroups:[],pipeline:null,index:null},t.renderBundles=[],e.viewport&&this.updateViewport(e),e.scissor){const{x:t,y:r,width:s,height:i}=e.scissorValue;u.setScissorRect(t,r,s,i)}}finishRender(e){const t=this.get(e),r=e.occlusionQueryCount;if(t.renderBundles.length>0&&t.currentPass.executeBundles(t.renderBundles),r>t.occlusionQueryIndex&&t.currentPass.endOcclusionQuery(),t.currentPass.end(),r>0){const s=8*r;let i=this.occludedResolveCache.get(s);void 0===i&&(i=this.device.createBuffer({size:s,usage:GPUBufferUsage.QUERY_RESOLVE|GPUBufferUsage.COPY_SRC}),this.occludedResolveCache.set(s,i));const n=this.device.createBuffer({size:s,usage:GPUBufferUsage.COPY_DST|GPUBufferUsage.MAP_READ});t.encoder.resolveQuerySet(t.occlusionQuerySet,0,r,i,0),t.encoder.copyBufferToBuffer(i,0,n,0,s),t.occlusionQueryBuffer=n,this.resolveOccludedAsync(e)}if(this.prepareTimestampBuffer(e,t.encoder),this.device.queue.submit([t.encoder.finish()]),null!==e.textures){const t=e.textures;for(let e=0;e<t.length;e++){const r=t[e];!0===r.generateMipmaps&&this.textureUtils.generateMipmaps(r)}}}isOccluded(e,t){const r=this.get(e);return r.occluded&&r.occluded.has(t)}async resolveOccludedAsync(e){const t=this.get(e),{currentOcclusionQueryBuffer:r,currentOcclusionQueryObjects:s}=t;if(r&&s){const e=new WeakSet;t.currentOcclusionQueryObjects=null,t.currentOcclusionQueryBuffer=null,await r.mapAsync(GPUMapMode.READ);const i=r.getMappedRange(),n=new BigUint64Array(i);for(let t=0;t<s.length;t++)n[t]!==BigInt(0)&&e.add(s[t]);r.destroy(),t.occluded=e}}updateViewport(e){const{currentPass:t}=this.get(e),{x:r,y:s,width:i,height:n,minDepth:o,maxDepth:a}=e.viewportValue;t.setViewport(r,s,i,n,o,a)}clear(e,t,r,s=null){const i=this.device,n=this.renderer;let o,a,u,l,d=[];if(e){const e=this.getClearColor();if(!0===this.renderer.alpha){const t=e.a;a={r:e.r*t,g:e.g*t,b:e.b*t,a:t}}else a={r:e.r,g:e.g,b:e.b,a:e.a}}if(null===s){u=n.depth,l=n.stencil;const t=this._getDefaultRenderPassDescriptor();if(e){d=t.colorAttachments;const e=d[0];e.clearValue=a,e.loadOp=lv,e.storeOp=av}(u||l)&&(o=t.depthStencilAttachment)}else{if(u=s.depth,l=s.stencil,e){d=this._getRenderPassDescriptor(s,{loadOp:lv}).colorAttachments}if(u||l){o={view:this.get(s.depthTexture).texture.createView()}}}u&&(t?(o.depthLoadOp=lv,o.depthClearValue=n.getClearDepth(),o.depthStoreOp=av):(o.depthLoadOp=uv,o.depthStoreOp=av)),l&&(r?(o.stencilLoadOp=lv,o.stencilClearValue=n.getClearStencil(),o.stencilStoreOp=av):(o.stencilLoadOp=uv,o.stencilStoreOp=av));const c=i.createCommandEncoder({});c.beginRenderPass({colorAttachments:d,depthStencilAttachment:o}).end(),i.queue.submit([c.finish()])}beginCompute(e){const t=this.get(e),r={};this.initTimestampQuery(e,r),t.cmdEncoderGPU=this.device.createCommandEncoder(),t.passEncoderGPU=t.cmdEncoderGPU.beginComputePass(r)}compute(e,t,r,s){const{passEncoderGPU:i}=this.get(e),n=this.get(s).pipeline;i.setPipeline(n);for(let e=0,t=r.length;e<t;e++){const t=r[e],s=this.get(t);i.setBindGroup(e,s.group)}const o=this.device.limits.maxComputeWorkgroupsPerDimension,a=this.get(t);void 0===a.dispatchSize&&(a.dispatchSize={x:0,y:1,z:1});const{dispatchSize:u}=a;t.dispatchCount>o?(u.x=Math.min(t.dispatchCount,o),u.y=Math.ceil(t.dispatchCount/o)):u.x=t.dispatchCount,i.dispatchWorkgroups(u.x,u.y,u.z)}finishCompute(e){const t=this.get(e);t.passEncoderGPU.end(),this.prepareTimestampBuffer(e,t.cmdEncoderGPU),this.device.queue.submit([t.cmdEncoderGPU.finish()])}async waitForGPU(){await this.device.queue.onSubmittedWorkDone()}draw(e,t){const{object:r,context:s,pipeline:i}=e,n=e.getBindings(),o=this.get(s),a=this.get(i).pipeline,u=o.currentSets,l=o.currentPass,d=e.getDrawParameters();if(null===d)return;u.pipeline!==a&&(l.setPipeline(a),u.pipeline=a);const c=u.bindingGroups;for(let e=0,t=n.length;e<t;e++){const t=n[e],r=this.get(t);c[t.index]!==t.id&&(l.setBindGroup(t.index,r.group),c[t.index]=t.id)}const h=e.getIndex(),p=null!==h;if(!0===p&&u.index!==h){const e=this.get(h).buffer,t=h.array instanceof Uint16Array?gv:mv;l.setIndexBuffer(e,t),u.index=h}const g=e.getVertexBuffers();for(let e=0,t=g.length;e<t;e++){const t=g[e];if(u.attributes[e]!==t){const r=this.get(t).buffer;l.setVertexBuffer(e,r),u.attributes[e]=t}}if(void 0!==o.occlusionQuerySet){const e=o.lastOcclusionObject;e!==r&&(null!==e&&!0===e.occlusionTest&&(l.endOcclusionQuery(),o.occlusionQueryIndex++),!0===r.occlusionTest&&(l.beginOcclusionQuery(o.occlusionQueryIndex),o.occlusionQueryObjects[o.occlusionQueryIndex]=r),o.lastOcclusionObject=r)}if(!0===r.isBatchedMesh){const e=r._multiDrawStarts,t=r._multiDrawCounts,s=r._multiDrawCount,i=r._multiDrawInstances;for(let r=0;r<s;r++){const s=i?i[r]:1,n=s>1?0:r;!0===p?l.drawIndexed(t[r],s,e[r]/h.array.BYTES_PER_ELEMENT,0,n):l.draw(t[r],s,e[r],n)}}else if(!0===p){const{vertexCount:s,instanceCount:i,firstVertex:n}=d,o=e.getIndirect();if(null!==o){const e=this.get(o).buffer;l.drawIndexedIndirect(e,0)}else l.drawIndexed(s,i,n,0,0);t.update(r,s,i)}else{const{vertexCount:s,instanceCount:i,firstVertex:n}=d,o=e.getIndirect();if(null!==o){const e=this.get(o).buffer;l.drawIndirect(e,0)}else l.draw(s,i,n,0);t.update(r,s,i)}}needsRenderUpdate(e){const t=this.get(e),{object:r,material:s}=e,i=this.utils,n=i.getSampleCountRenderContext(e.context),o=i.getCurrentColorSpace(e.context),a=i.getCurrentColorFormat(e.context),u=i.getCurrentDepthStencilFormat(e.context),l=i.getPrimitiveTopology(r,s);let d=!1;return t.material===s&&t.materialVersion===s.version&&t.transparent===s.transparent&&t.blending===s.blending&&t.premultipliedAlpha===s.premultipliedAlpha&&t.blendSrc===s.blendSrc&&t.blendDst===s.blendDst&&t.blendEquation===s.blendEquation&&t.blendSrcAlpha===s.blendSrcAlpha&&t.blendDstAlpha===s.blendDstAlpha&&t.blendEquationAlpha===s.blendEquationAlpha&&t.colorWrite===s.colorWrite&&t.depthWrite===s.depthWrite&&t.depthTest===s.depthTest&&t.depthFunc===s.depthFunc&&t.stencilWrite===s.stencilWrite&&t.stencilFunc===s.stencilFunc&&t.stencilFail===s.stencilFail&&t.stencilZFail===s.stencilZFail&&t.stencilZPass===s.stencilZPass&&t.stencilFuncMask===s.stencilFuncMask&&t.stencilWriteMask===s.stencilWriteMask&&t.side===s.side&&t.alphaToCoverage===s.alphaToCoverage&&t.sampleCount===n&&t.colorSpace===o&&t.colorFormat===a&&t.depthStencilFormat===u&&t.primitiveTopology===l&&t.clippingContextCacheKey===e.clippingContextCacheKey||(t.material=s,t.materialVersion=s.version,t.transparent=s.transparent,t.blending=s.blending,t.premultipliedAlpha=s.premultipliedAlpha,t.blendSrc=s.blendSrc,t.blendDst=s.blendDst,t.blendEquation=s.blendEquation,t.blendSrcAlpha=s.blendSrcAlpha,t.blendDstAlpha=s.blendDstAlpha,t.blendEquationAlpha=s.blendEquationAlpha,t.colorWrite=s.colorWrite,t.depthWrite=s.depthWrite,t.depthTest=s.depthTest,t.depthFunc=s.depthFunc,t.stencilWrite=s.stencilWrite,t.stencilFunc=s.stencilFunc,t.stencilFail=s.stencilFail,t.stencilZFail=s.stencilZFail,t.stencilZPass=s.stencilZPass,t.stencilFuncMask=s.stencilFuncMask,t.stencilWriteMask=s.stencilWriteMask,t.side=s.side,t.alphaToCoverage=s.alphaToCoverage,t.sampleCount=n,t.colorSpace=o,t.colorFormat=a,t.depthStencilFormat=u,t.primitiveTopology=l,t.clippingContextCacheKey=e.clippingContextCacheKey,d=!0),d}getRenderCacheKey(e){const{object:t,material:r}=e,s=this.utils,i=e.context;return[r.transparent,r.blending,r.premultipliedAlpha,r.blendSrc,r.blendDst,r.blendEquation,r.blendSrcAlpha,r.blendDstAlpha,r.blendEquationAlpha,r.colorWrite,r.depthWrite,r.depthTest,r.depthFunc,r.stencilWrite,r.stencilFunc,r.stencilFail,r.stencilZFail,r.stencilZPass,r.stencilFuncMask,r.stencilWriteMask,r.side,s.getSampleCountRenderContext(i),s.getCurrentColorSpace(i),s.getCurrentColorFormat(i),s.getCurrentDepthStencilFormat(i),s.getPrimitiveTopology(t,r),e.getGeometryCacheKey(),e.clippingContextCacheKey].join()}createSampler(e){this.textureUtils.createSampler(e)}destroySampler(e){this.textureUtils.destroySampler(e)}createDefaultTexture(e){this.textureUtils.createDefaultTexture(e)}createTexture(e,t){this.textureUtils.createTexture(e,t)}updateTexture(e,t){this.textureUtils.updateTexture(e,t)}generateMipmaps(e){this.textureUtils.generateMipmaps(e)}destroyTexture(e){this.textureUtils.destroyTexture(e)}copyTextureToBuffer(e,t,r,s,i,n){return this.textureUtils.copyTextureToBuffer(e,t,r,s,i,n)}initTimestampQuery(e,t){if(!this.trackTimestamp)return;const r=this.get(e);if(!r.timeStampQuerySet){const s=e.isComputeNode?"compute":"render",i=this.device.createQuerySet({type:"timestamp",count:2,label:`timestamp_${s}_${e.id}`}),n={querySet:i,beginningOfPassWriteIndex:0,endOfPassWriteIndex:1};Object.assign(t,{timestampWrites:n}),r.timeStampQuerySet=i}}prepareTimestampBuffer(e,t){if(!this.trackTimestamp)return;const r=this.get(e),s=2*BigInt64Array.BYTES_PER_ELEMENT;void 0===r.currentTimestampQueryBuffers&&(r.currentTimestampQueryBuffers={resolveBuffer:this.device.createBuffer({label:"timestamp resolve buffer",size:s,usage:GPUBufferUsage.QUERY_RESOLVE|GPUBufferUsage.COPY_SRC}),resultBuffer:this.device.createBuffer({label:"timestamp result buffer",size:s,usage:GPUBufferUsage.COPY_DST|GPUBufferUsage.MAP_READ})});const{resolveBuffer:i,resultBuffer:n}=r.currentTimestampQueryBuffers;t.resolveQuerySet(r.timeStampQuerySet,0,2,i,0),"unmapped"===n.mapState&&t.copyBufferToBuffer(i,0,n,0,s)}async resolveTimestampAsync(e,t="render"){if(!this.trackTimestamp)return;const r=this.get(e);if(void 0===r.currentTimestampQueryBuffers)return;const{resultBuffer:s}=r.currentTimestampQueryBuffers;"unmapped"===s.mapState&&s.mapAsync(GPUMapMode.READ).then((()=>{const e=new BigUint64Array(s.getMappedRange()),r=Number(e[1]-e[0])/1e6;this.renderer.info.updateTimestamp(t,r),s.unmap()}))}createNodeBuilder(e,t){return new zN(e,t)}createProgram(e){this.get(e).module={module:this.device.createShaderModule({code:e.code,label:e.stage+(""!==e.name?`_${e.name}`:"")}),entryPoint:"main"}}destroyProgram(e){this.delete(e)}createRenderPipeline(e,t){this.pipelineUtils.createRenderPipeline(e,t)}createComputePipeline(e,t){this.pipelineUtils.createComputePipeline(e,t)}beginBundle(e){const t=this.get(e);t._currentPass=t.currentPass,t._currentSets=t.currentSets,t.currentSets={attributes:{},bindingGroups:[],pipeline:null,index:null},t.currentPass=this.pipelineUtils.createBundleEncoder(e)}finishBundle(e,t){const r=this.get(e),s=r.currentPass.finish();this.get(t).bundleGPU=s,r.currentSets=r._currentSets,r.currentPass=r._currentPass}addBundle(e,t){this.get(e).renderBundles.push(this.get(t).bundleGPU)}createBindings(e,t,r,s){this.bindingUtils.createBindings(e,t,r,s)}updateBindings(e,t,r,s){this.bindingUtils.createBindings(e,t,r,s)}updateBinding(e){this.bindingUtils.updateBinding(e)}createIndexAttribute(e){this.attributeUtils.createAttribute(e,GPUBufferUsage.INDEX|GPUBufferUsage.COPY_SRC|GPUBufferUsage.COPY_DST)}createAttribute(e){this.attributeUtils.createAttribute(e,GPUBufferUsage.VERTEX|GPUBufferUsage.COPY_SRC|GPUBufferUsage.COPY_DST)}createStorageAttribute(e){this.attributeUtils.createAttribute(e,GPUBufferUsage.STORAGE|GPUBufferUsage.VERTEX|GPUBufferUsage.COPY_SRC|GPUBufferUsage.COPY_DST)}createIndirectStorageAttribute(e){this.attributeUtils.createAttribute(e,GPUBufferUsage.STORAGE|GPUBufferUsage.INDIRECT|GPUBufferUsage.COPY_SRC|GPUBufferUsage.COPY_DST)}updateAttribute(e){this.attributeUtils.updateAttribute(e)}destroyAttribute(e){this.attributeUtils.destroyAttribute(e)}updateSize(){this.colorBuffer=this.textureUtils.getColorBuffer(),this.defaultRenderPassdescriptor=null}getMaxAnisotropy(){return 16}hasFeature(e){return this.device.features.has(e)}copyTextureToTexture(e,t,r=null,s=null,i=0){let n=0,o=0,a=0,u=0,l=0,d=0,c=e.image.width,h=e.image.height;null!==r&&(u=r.x,l=r.y,d=r.z||0,c=r.width,h=r.height),null!==s&&(n=s.x,o=s.y,a=s.z||0);const p=this.device.createCommandEncoder({label:"copyTextureToTexture_"+e.id+"_"+t.id}),g=this.get(e).texture,m=this.get(t).texture;p.copyTextureToTexture({texture:g,mipLevel:i,origin:{x:u,y:l,z:d}},{texture:m,mipLevel:i,origin:{x:n,y:o,z:a}},[c,h,1]),this.device.queue.submit([p.finish()])}copyFramebufferToTexture(e,t,r){const s=this.get(t);let i=null;i=t.renderTarget?e.isDepthTexture?this.get(t.depthTexture).texture:this.get(t.textures[0]).texture:e.isDepthTexture?this.textureUtils.getDepthBuffer(t.depth,t.stencil):this.context.getCurrentTexture();const n=this.get(e).texture;if(i.format!==n.format)return void console.error("WebGPUBackend: copyFramebufferToTexture: Source and destination formats do not match.",i.format,n.format);let o;if(s.currentPass?(s.currentPass.end(),o=s.encoder):o=this.device.createCommandEncoder({label:"copyFramebufferToTexture_"+e.id}),o.copyTextureToTexture({texture:i,origin:[r.x,r.y,0]},{texture:n},[r.z,r.w]),e.generateMipmaps&&this.textureUtils.generateMipmaps(e),s.currentPass){const{descriptor:e}=s;for(let t=0;t<e.colorAttachments.length;t++)e.colorAttachments[t].loadOp=uv;if(t.depth&&(e.depthStencilAttachment.depthLoadOp=uv),t.stencil&&(e.depthStencilAttachment.stencilLoadOp=uv),s.currentPass=o.beginRenderPass(e),s.currentSets={attributes:{},bindingGroups:[],pipeline:null,index:null},t.viewport&&this.updateViewport(t),t.scissor){const{x:e,y:r,width:i,height:n}=t.scissorValue;s.currentPass.setScissorRect(e,r,i,n)}}else this.device.queue.submit([o.finish()])}}class QN extends kr{constructor(e,t,r,s,i,n){super(e,t,r,s,i,n),this.iesMap=null}copy(e,t){return super.copy(e,t),this.iesMap=e.iesMap,this}}class ZN extends JT{constructor(){super(),this.addMaterial(Oh,"MeshPhongMaterial"),this.addMaterial(ng,"MeshStandardMaterial"),this.addMaterial(ag,"MeshPhysicalMaterial"),this.addMaterial(pg,"MeshToonMaterial"),this.addMaterial(Mh,"MeshBasicMaterial"),this.addMaterial(Lh,"MeshLambertMaterial"),this.addMaterial(fh,"MeshNormalMaterial"),this.addMaterial(yg,"MeshMatcapMaterial"),this.addMaterial(oh,"LineBasicMaterial"),this.addMaterial(uh,"LineDashedMaterial"),this.addMaterial(xg,"PointsMaterial"),this.addMaterial(Ng,"SpriteMaterial"),this.addMaterial(Rg,"ShadowMaterial"),this.addLight(Ib,zr),this.addLight(MT,$r),this.addLight(PT,Hr),this.addLight(IT,kr),this.addLight(LT,Wr),this.addLight(VT,jr),this.addLight(OT,qr),this.addLight(DT,QN),this.addToneMapping(ey,Kr),this.addToneMapping(ty,Xr),this.addToneMapping(ry,Yr),this.addToneMapping(iy,Qr),this.addToneMapping(uy,Zr),this.addToneMapping(ly,Jr)}}class JN extends u_{constructor(e={}){let t;e.forceWebGL?t=q_:(t=YN,e.getFallback=()=>(console.warn("THREE.WebGPURenderer: WebGPU is not available, running under WebGL2 backend."),new q_(e)));super(new t(e),e),this.library=new ZN,this.isWebGPURenderer=!0}}class eS extends es{constructor(){super(),this.isBundleGroup=!0,this.type="BundleGroup",this.static=!0,this.version=0}set needsUpdate(e){!0===e&&this.version++}}const tS=new rh,rS=new of(tS);class sS{constructor(e,t=zi(0,0,1,1)){this.renderer=e,this.outputNode=t,this.outputColorTransform=!0,this.needsUpdate=!0,tS.name="PostProcessing"}render(){this._update();const e=this.renderer,t=e.toneMapping,r=e.outputColorSpace;e.toneMapping=h,e.outputColorSpace=Ae,rS.render(e),e.toneMapping=t,e.outputColorSpace=r}_update(){if(!0===this.needsUpdate){const e=this.renderer,t=e.toneMapping,r=e.outputColorSpace;rS.material.fragmentNode=!0===this.outputColorTransform?fu(this.outputNode,t,r):this.outputNode.context({toneMapping:t,outputColorSpace:r}),rS.material.needsUpdate=!0,this.needsUpdate=!1}}async renderAsync(){this._update();const e=this.renderer,t=e.toneMapping,r=e.outputColorSpace;e.toneMapping=h,e.outputColorSpace=Ae,await rS.renderAsync(e),e.toneMapping=t,e.outputColorSpace=r}}class iS extends ee{constructor(e=1,t=1){super(),this.image={width:e,height:t},this.magFilter=$,this.minFilter=$,this.isStorageTexture=!0}}class nS extends gf{constructor(e,t){super(e,t,Uint32Array),this.isIndirectStorageBufferAttribute=!0}}class oS extends ts{constructor(e){super(e),this.textures={},this.nodes={}}load(e,t,r,s){const i=new rs(this.manager);i.setPath(this.path),i.setRequestHeader(this.requestHeader),i.setWithCredentials(this.withCredentials),i.load(e,(r=>{try{t(this.parse(JSON.parse(r)))}catch(t){s?s(t):console.error(t),this.manager.itemError(e)}}),r,s)}parseNodes(e){const t={};if(void 0!==e){for(const r of e){const{uuid:e,type:s}=r;t[e]=this.createNodeFromType(s),t[e].uuid=e}const r={nodes:t,textures:this.textures};for(const s of e){s.meta=r;t[s.uuid].deserialize(s),delete s.meta}}return t}parse(e){const t=this.createNodeFromType(e.type);t.uuid=e.uuid;const r={nodes:this.parseNodes(e.nodes),textures:this.textures};return e.meta=r,t.deserialize(e),delete e.meta,t}setTextures(e){return this.textures=e,this}setNodes(e){return this.nodes=e,this}createNodeFromType(e){return void 0===this.nodes[e]?(console.error("THREE.NodeLoader: Node type not found:",e),Mi()):xi(new this.nodes[e])}}class aS extends ss{constructor(e){super(e),this.nodes={},this.nodeMaterials={}}parse(e){const t=super.parse(e),r=this.nodes,s=e.inputNodes;for(const e in s){const i=s[e];t[e]=r[i]}return t}setNodes(e){return this.nodes=e,this}setNodeMaterials(e){return this.nodeMaterials=e,this}createMaterialFromType(e){const t=this.nodeMaterials[e];return void 0!==t?new t:super.createMaterialFromType(e)}}class uS extends is{constructor(e){super(e),this.nodes={},this.nodeMaterials={},this._nodesJSON=null}setNodes(e){return this.nodes=e,this}setNodeMaterials(e){return this.nodeMaterials=e,this}parse(e,t){this._nodesJSON=e.nodes;const r=super.parse(e,t);return this._nodesJSON=null,r}parseNodes(e,t){if(void 0!==e){const r=new oS;return r.setNodes(this.nodes),r.setTextures(t),r.parseNodes(e)}return{}}parseMaterials(e,t){const r={};if(void 0!==e){const s=this.parseNodes(this._nodesJSON,t),i=new aS;i.setTextures(t),i.setNodes(s),i.setNodeMaterials(this.nodeMaterials);for(let t=0,s=e.length;t<s;t++){const s=e[t];r[s.uuid]=i.parse(s)}}return r}}class lS extends es{constructor(){super(),this.isClippingGroup=!0,this.clippingPlanes=[],this.enabled=!0,this.clipIntersection=!1,this.clipShadows=!1}}export{Qr as ACESFilmicToneMapping,bc as AONode,Je as AddEquation,K as AddOperation,xt as AdditiveBlending,Zr as AgXToneMapping,Bt as AlphaFormat,fr as AlwaysCompare,Rt as AlwaysDepth,wr as AlwaysStencilFunc,Wr as AmbientLight,LT as AmbientLightNode,Nb as AnalyticLightNode,Ps as ArrayElementNode,Ln as AssignNode,yu as AttributeNode,T as BackSide,Ah as BasicEnvironmentNode,we as BasicShadowMap,ic as BatchNode,G as BoxGeometry,ve as BufferAttribute,Za as BufferAttributeNode,Te as BufferGeometry,Rl as BufferNode,id as BumpMapNode,eS as BundleGroup,au as BypassNode,Ye as ByteType,nu as CacheNode,Yr as CineonToneMapping,cr as ClampToEdgeWrapping,lS as ClippingGroup,dy as CodeNode,e as Color,d as ColorManagement,Ga as ColorSpaceNode,su as ComputeNode,$s as ConstNode,Ca as ContextNode,Is as ConvertNode,H as CubeCamera,_ as CubeReflectionMapping,v as CubeRefractionMapping,W as CubeTexture,Sl as CubeTextureNode,Be as CubeUVReflectionMapping,gt as CullFaceBack,mt as CullFaceFront,pt as CullFaceNone,ft as CustomBlending,C as DataArrayTexture,ze as DataTexture,Ur as DecrementStencilOp,Br as DecrementWrapStencilOp,ce as DepthFormat,de as DepthStencilFormat,B as DepthTexture,$r as DirectionalLight,MT as DirectionalLightNode,le as DoubleSide,ut as DstAlphaFactor,at as DstColorFactor,m as DynamicDrawUsage,tg as EnvironmentNode,br as EqualCompare,Nt as EqualDepth,Rr as EqualStencilFunc,yh as EquirectUVNode,j as EquirectangularReflectionMapping,q as EquirectangularRefractionMapping,Se as Euler,o as EventDispatcher,hu as ExpressionNode,rs as FileLoader,De as Float16BufferAttribute,_e as Float32BufferAttribute,E as FloatType,w as FramebufferTexture,ol as FrontFacingNode,Ge as FrontSide,ke as Frustum,On as FunctionCallNode,hy as FunctionNode,Rm as FunctionOverloadingNode,jT as GLSLNodeParser,Tr as GreaterCompare,_t as GreaterDepth,xr as GreaterEqualCompare,vt as GreaterEqualDepth,Ar as GreaterEqualStencilFunc,Sr as GreaterStencilFunc,es as Group,ye as HalfFloatType,jr as HemisphereLight,VT as HemisphereLightNode,QN as IESSpotLight,DT as IESSpotLightNode,Pr as IncrementStencilOp,Fr as IncrementWrapStencilOp,qd as IndexNode,nS as IndirectStorageBufferAttribute,ec as InstanceNode,R as InstancedBufferAttribute,A as InstancedInterleavedBuffer,rc as InstancedMeshNode,ih as InstancedPointsNodeMaterial,x as IntType,g as InterleavedBuffer,f as InterleavedBufferAttribute,Ir as InvertStencilOp,_c as IrradianceNode,Ls as JoinNode,Vr as KeepStencilOp,Re as LessCompare,At as LessDepth,yr as LessEqualCompare,St as LessEqualDepth,Cr as LessEqualStencilFunc,Er as LessStencilFunc,qr as LightProbe,OT as LightProbeNode,t_ as Lighting,xc as LightingContextNode,Ch as LightingModel,yc as LightingNode,Qy as LightsNode,ph as Line2NodeMaterial,I as LineBasicMaterial,oh as LineBasicNodeMaterial,D as LineDashedMaterial,uh as LineDashedNodeMaterial,$ as LinearFilter,be as LinearMipMapLinearFilter,M as LinearMipmapLinearFilter,Pe as LinearMipmapNearestFilter,Ae as LinearSRGBColorSpace,Kr as LinearToneMapping,ts as Loader,lc as LoopNode,Ut as LuminanceAlphaFormat,Ft as LuminanceFormat,xm as MRTNode,gg as MatcapUVNode,F as Material,ss as MaterialLoader,ad as MaterialNode,Il as MaterialReferenceNode,a as MathUtils,i as Matrix3,n as Matrix4,Or as MaxEquation,vu as MaxMipLevelNode,k as Mesh,Q as MeshBasicMaterial,Mh as MeshBasicNodeMaterial,Z as MeshLambertMaterial,Lh as MeshLambertNodeMaterial,ie as MeshMatcapMaterial,yg as MeshMatcapNodeMaterial,V as MeshNormalMaterial,fh as MeshNormalNodeMaterial,J as MeshPhongMaterial,Oh as MeshPhongNodeMaterial,re as MeshPhysicalMaterial,ag as MeshPhysicalNodeMaterial,lg as MeshSSSNodeMaterial,te as MeshStandardMaterial,ng as MeshStandardNodeMaterial,se as MeshToonMaterial,pg as MeshToonNodeMaterial,Gr as MinEquation,hr as MirroredRepeatWrapping,X as MixOperation,ku as ModelNode,mc as MorphNode,yt as MultiplyBlending,Y as MultiplyOperation,pr as NearestFilter,Ie as NearestMipmapLinearFilter,gr as NearestMipmapNearestFilter,Jr as NeutralToneMapping,mr as NeverCompare,Ct as NeverDepth,Mr as NeverStencilFunc,L as NoBlending,y as NoColorSpace,h as NoToneMapping,Us as Node,Cs as NodeAccess,Ix as NodeAttribute,CT as NodeBuilder,kx as NodeCache,Ox as NodeCode,ET as NodeFrame,wT as NodeFunctionInput,oS as NodeLoader,rh as NodeMaterial,aS as NodeMaterialLoader,os as NodeMaterialObserver,uS as NodeObjectLoader,Ss as NodeShaderStage,Rs as NodeType,Dx as NodeUniform,As as NodeUpdateType,Ns as NodeUtils,Lx as NodeVar,Vx as NodeVarying,U as NormalBlending,ed as NormalMapNode,_r as NotEqualCompare,Tt as NotEqualDepth,Nr as NotEqualStencilFunc,fe as Object3D,Iu as Object3DNode,is as ObjectLoader,S as ObjectSpaceNormalMap,st as OneFactor,ht as OneMinusDstAlphaFactor,ct as OneMinusDstColorFactor,dt as OneMinusSrcAlphaFactor,lt as OneMinusSrcColorFactor,xe as OrthographicCamera,fm as OutputStructNode,Oe as PCFShadowMap,bT as PMREMGenerator,Zp as PMREMNode,pm as ParameterNode,Zf as PassNode,Fe as PerspectiveCamera,Ih as PhongLightingModel,Ep as PhysicalLightingModel,me as Plane,zr as PointLight,Ib as PointLightNode,Tf as PointUVNode,P as PointsMaterial,xg as PointsNodeMaterial,sS as PostProcessing,qf as PosterizeNode,on as PropertyNode,of as QuadMesh,ur as RED_GREEN_RGTC2_Format,or as RED_RGTC1_Format,Le as REVISION,Ue as RGBAFormat,Ke as RGBAIntegerFormat,rr as RGBA_ASTC_10x10_Format,Jt as RGBA_ASTC_10x5_Format,er as RGBA_ASTC_10x6_Format,tr as RGBA_ASTC_10x8_Format,sr as RGBA_ASTC_12x10_Format,ir as RGBA_ASTC_12x12_Format,Wt as RGBA_ASTC_4x4_Format,jt as RGBA_ASTC_5x4_Format,qt as RGBA_ASTC_5x5_Format,Kt as RGBA_ASTC_6x5_Format,Xt as RGBA_ASTC_6x6_Format,Yt as RGBA_ASTC_8x5_Format,Qt as RGBA_ASTC_8x6_Format,Zt as RGBA_ASTC_8x8_Format,nr as RGBA_BPTC_Format,Ht as RGBA_ETC2_EAC_Format,kt as RGBA_PVRTC_2BPPV1_Format,Gt as RGBA_PVRTC_4BPPV1_Format,It as RGBA_S3TC_DXT1_Format,Dt as RGBA_S3TC_DXT3_Format,Lt as RGBA_S3TC_DXT5_Format,qe as RGBFormat,je as RGBIntegerFormat,zt as RGB_ETC1_Format,$t as RGB_ETC2_Format,Ot as RGB_PVRTC_2BPPV1_Format,Vt as RGB_PVRTC_4BPPV1_Format,Pt as RGB_S3TC_DXT1_Format,Ee as RGFormat,We as RGIntegerFormat,uf as RTTNode,Cy as RangeNode,Hr as RectAreaLight,PT as RectAreaLightNode,He as RedFormat,$e as RedIntegerFormat,Fl as ReferenceNode,ef as ReflectorNode,Xr as ReinhardToneMapping,lu as RemapNode,mu as RenderOutputNode,ge as RenderTarget,qa as RendererReferenceNode,ub as RendererUtils,dr as RepeatWrapping,Dr as ReplaceStencilOp,tt as ReverseSubtractEquation,Tg as RotateNode,lr as SIGNED_RED_GREEN_RGTC2_Format,ar as SIGNED_RED_RGTC1_Format,Ve as SRGBColorSpace,c as SRGBTransfer,z as Scene,Sf as SceneNode,Sc as ScreenNode,xy as ScriptableNode,gy as ScriptableValueNode,Gs as SetNode,Zy as ShadowBaseNode,oe as ShadowMaterial,_b as ShadowNode,Rg as ShadowNodeMaterial,Qe as ShortType,ac as SkinningNode,Me as SphereGeometry,Os as SplitNode,kr as SpotLight,IT as SpotLightNode,ne as SpriteMaterial,Ng as SpriteNodeMaterial,Dm as SpriteSheetUVNode,nt as SrcAlphaFactor,ot as SrcAlphaSaturateFactor,it as SrcColorFactor,gm as StackNode,p as StaticDrawUsage,mf as StorageArrayElementNode,gf as StorageBufferAttribute,yf as StorageBufferNode,pf as StorageInstancedBufferAttribute,iS as StorageTexture,Ef as StorageTextureNode,et as SubtractEquation,bt as SubtractiveBlending,wx as TSL,N as TangentSpaceNormalMap,Ds as TempNode,ee as Texture,Eg as Texture3DNode,Su as TextureNode,Tu as TextureSizeNode,Xa as ToneMappingNode,Jf as ToonOutlinePassNode,Vm as TriplanarTexturesNode,Ne as UVMapping,ue as Uint16BufferAttribute,ae as Uint32BufferAttribute,wl as UniformArrayNode,Qi as UniformGroupNode,sn as UniformNode,pe as UnsignedByteType,he as UnsignedInt248Type,Mt as UnsignedInt5999Type,b as UnsignedIntType,Et as UnsignedShort4444Type,wt as UnsignedShort5551Type,Xe as UnsignedShortType,Mf as UserDataNode,Ce as VSMShadowMap,Ma as VarNode,Ua as VaryingNode,t as Vector2,r as Vector3,s as Vector4,xf as VertexColorNode,zc as ViewportDepthNode,Gc as ViewportDepthTextureNode,dh as ViewportSharedTextureNode,Dc as ViewportTextureNode,Mg as VolumeNodeMaterial,u as WebGLCoordinateSystem,O as WebGLCubeRenderTarget,l as WebGPUCoordinateSystem,JN as WebGPURenderer,rt as ZeroFactor,Lr as ZeroStencilOp,Ze as createCanvasElement,ws as defaultBuildStages,Es as defaultShaderStages,Ms as shaderStages,Bs as vectorComponents};
