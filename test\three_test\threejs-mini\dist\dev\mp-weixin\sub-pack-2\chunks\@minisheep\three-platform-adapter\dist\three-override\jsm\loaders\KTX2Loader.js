"use strict";
require("../../../../../../three/build/three.module.min.js");
const _mpChunkDeps__minisheep_threePlatformAdapter_dist_threeOverride_jsm_utils_WorkerPool = require("../utils/WorkerPool.js");
const _mpChunkDeps_three_examples_jsm_libs_ktxParse_module = require("../../../../../../three/examples/jsm/libs/ktx-parse.module.js");
const _mpChunkDeps__minisheep_threePlatformAdapter_dist_threeOverride_jsm_libs_zstddec_module = require("../libs/zstddec.module.js");
const _mpChunkDeps__minisheep_threePlatformAdapter_dist_threeOverride_jsm_utils_LocalAsset = require("../utils/LocalAsset.js");
const _mpChunkDeps_three_build_three_core_min = require("../../../../../../three/build/three.core.min.js");
const ee = function() {
  const ee2 = /* @__PURE__ */ new WeakMap();
  let te, re = 0;
  class KTX2Loader extends _mpChunkDeps_three_build_three_core_min.ic {
    constructor(e) {
      super(e), this.transcoderPath = "", this.transcoderBinary = null, this.transcoderPending = null, this.workerPool = new _mpChunkDeps__minisheep_threePlatformAdapter_dist_threeOverride_jsm_utils_WorkerPool.WorkerPool(), this.workerSourceURL = "", this.workerConfig = null, "undefined" != typeof MSC_TRANSCODER && console.warn('THREE.KTX2Loader: Please update to latest "basis_transcoder". "msc_basis_transcoder" is no longer supported in three.js r125+.');
    }
    setTranscoderPath(e) {
      return this.transcoderPath = e, this;
    }
    setWorkerLimit(e) {
      return this.workerPool.setWorkerLimit(e), this;
    }
    async detectSupportAsync(e) {
      return this.workerConfig = { astcSupported: await e.hasFeatureAsync("texture-compression-astc"), etc1Supported: await e.hasFeatureAsync("texture-compression-etc1"), etc2Supported: await e.hasFeatureAsync("texture-compression-etc2"), dxtSupported: await e.hasFeatureAsync("texture-compression-bc"), bptcSupported: await e.hasFeatureAsync("texture-compression-bptc"), pvrtcSupported: await e.hasFeatureAsync("texture-compression-pvrtc") }, this;
    }
    detectSupport(e) {
      return true === e.isWebGPURenderer ? this.workerConfig = { astcSupported: e.hasFeature("texture-compression-astc"), etc1Supported: e.hasFeature("texture-compression-etc1"), etc2Supported: e.hasFeature("texture-compression-etc2"), dxtSupported: e.hasFeature("texture-compression-bc"), bptcSupported: e.hasFeature("texture-compression-bptc"), pvrtcSupported: e.hasFeature("texture-compression-pvrtc") } : this.workerConfig = { astcSupported: e.extensions.has("WEBGL_compressed_texture_astc"), etc1Supported: e.extensions.has("WEBGL_compressed_texture_etc1"), etc2Supported: e.extensions.has("WEBGL_compressed_texture_etc"), dxtSupported: e.extensions.has("WEBGL_compressed_texture_s3tc"), bptcSupported: e.extensions.has("EXT_texture_compression_bptc"), pvrtcSupported: e.extensions.has("WEBGL_compressed_texture_pvrtc") || e.extensions.has("WEBKIT_WEBGL_compressed_texture_pvrtc") }, this;
    }
    init() {
      return this.transcoderPending || (this.transcoderPending = Promise.resolve().then(() => {
        this.transcoderBinary = new ArrayBuffer(1), this.workerPool.setWorkerCreator(() => {
          const e = new THREEGlobals["Worker"](_mpChunkDeps__minisheep_threePlatformAdapter_dist_threeOverride_jsm_utils_LocalAsset.LocalAsset.resolve("worker", "basis/basis_transcoder.js")), t = this.transcoderBinary.slice(0);
          return e.postMessage({ type: "init", config: this.workerConfig, transcoderBinary: t }, [t]), e;
        });
      }), re > 0 && console.warn("THREE.KTX2Loader: Multiple active KTX2 loaders may cause performance issues. Use a single KTX2Loader instance, or call .dispose() on old instances."), re++), this.transcoderPending;
    }
    load(e, t, r, s) {
      if (null === this.workerConfig) throw new Error("THREE.KTX2Loader: Missing initialization with `.detectSupport( renderer )`.");
      const o = new _mpChunkDeps_three_build_three_core_min.oc(this.manager);
      o.setResponseType("arraybuffer"), o.setWithCredentials(this.withCredentials), o.load(e, (e2) => {
        this.parse(e2, t, s);
      }, r, s);
    }
    parse(e, t, r) {
      if (null === this.workerConfig) throw new Error("THREE.KTX2Loader: Missing initialization with `.detectSupport( renderer )`.");
      if (ee2.has(e)) {
        return ee2.get(e).promise.then(t).catch(r);
      }
      this._createTexture(e).then((e2) => t ? t(e2) : null).catch(r);
    }
    _createTextureFrom(e, t) {
      const { faces: r, width: s, height: o, format: a, type: i, error: n, dfdFlags: p } = e;
      if ("error" === i) return Promise.reject(n);
      let c;
      if (6 === t.faceCount) c = new _mpChunkDeps_three_build_three_core_min.Za(r, a, _mpChunkDeps_three_build_three_core_min.Tt);
      else {
        const e2 = r[0].mipmaps;
        c = t.layerCount > 1 ? new _mpChunkDeps_three_build_three_core_min.Ya(e2, s, o, t.layerCount, a, _mpChunkDeps_three_build_three_core_min.Tt) : new _mpChunkDeps_three_build_three_core_min.Xa(e2, s, o, a, _mpChunkDeps_three_build_three_core_min.Tt);
      }
      return c.minFilter = 1 === r[0].mipmaps.length ? _mpChunkDeps_three_build_three_core_min.wt : _mpChunkDeps_three_build_three_core_min._t, c.magFilter = _mpChunkDeps_three_build_three_core_min.wt, c.generateMipmaps = false, c.needsUpdate = true, c.colorSpace = ie(t), c.premultiplyAlpha = !!(p & _mpChunkDeps_three_examples_jsm_libs_ktxParse_module.g), c;
    }
    async _createTexture(e, t = {}) {
      const r = _mpChunkDeps_three_examples_jsm_libs_ktxParse_module.Pi(new Uint8Array(e));
      if (r.vkFormat !== _mpChunkDeps_three_examples_jsm_libs_ktxParse_module.it) return async function(e2) {
        const { vkFormat: t2 } = e2;
        if (void 0 === oe[t2]) throw new Error("THREE.KTX2Loader: Unsupported vkFormat.");
        let r2;
        e2.supercompressionScheme === _mpChunkDeps_three_examples_jsm_libs_ktxParse_module.n && (te || (te = new Promise(async (e3) => {
          const t3 = new _mpChunkDeps__minisheep_threePlatformAdapter_dist_threeOverride_jsm_libs_zstddec_module.Q();
          await t3.init(), e3(t3);
        })), r2 = await te);
        const s2 = [];
        for (let o3 = 0; o3 < e2.levels.length; o3++) {
          const a = Math.max(1, e2.pixelWidth >> o3), i = Math.max(1, e2.pixelHeight >> o3), n = e2.pixelDepth ? Math.max(1, e2.pixelDepth >> o3) : 0, p = e2.levels[o3];
          let c, u;
          if (e2.supercompressionScheme === _mpChunkDeps_three_examples_jsm_libs_ktxParse_module.t) c = p.levelData;
          else {
            if (e2.supercompressionScheme !== _mpChunkDeps_three_examples_jsm_libs_ktxParse_module.n) throw new Error("THREE.KTX2Loader: Unsupported supercompressionScheme.");
            c = r2.decode(p.levelData, p.uncompressedByteLength);
          }
          u = ae[t2] === _mpChunkDeps_three_build_three_core_min.Rt ? new Float32Array(c.buffer, c.byteOffset, c.byteLength / Float32Array.BYTES_PER_ELEMENT) : ae[t2] === _mpChunkDeps_three_build_three_core_min.Et ? new Uint16Array(c.buffer, c.byteOffset, c.byteLength / Uint16Array.BYTES_PER_ELEMENT) : c, s2.push({ data: u, width: a, height: i, depth: n });
        }
        let o2;
        if (se.has(oe[t2])) o2 = 0 === e2.pixelDepth ? new _mpChunkDeps_three_build_three_core_min.Vo(s2[0].data, e2.pixelWidth, e2.pixelHeight) : new _mpChunkDeps_three_build_three_core_min.Ti(s2[0].data, e2.pixelWidth, e2.pixelHeight, e2.pixelDepth);
        else {
          if (e2.pixelDepth > 0) throw new Error("THREE.KTX2Loader: Unsupported pixelDepth.");
          o2 = new _mpChunkDeps_three_build_three_core_min.Xa(s2, e2.pixelWidth, e2.pixelHeight);
        }
        return o2.mipmaps = s2, o2.type = ae[t2], o2.format = oe[t2], o2.colorSpace = ie(e2), o2.needsUpdate = true, Promise.resolve(o2);
      }(r);
      const s = t, o = this.init().then(() => this.workerPool.postMessage({ type: "transcode", buffer: e, taskConfig: s }, [e])).then((e2) => this._createTextureFrom(e2.data, r));
      return ee2.set(e, { promise: o }), o;
    }
    dispose() {
      return this.workerPool.dispose(), this.workerSourceURL && THREEGlobals["URL"].revokeObjectURL(this.workerSourceURL), re--, this;
    }
  }
  KTX2Loader.BasisFormat = { ETC1S: 0, UASTC_4x4: 1 }, KTX2Loader.TranscoderFormat = { ETC1: 0, ETC2: 1, BC1: 2, BC3: 3, BC4: 4, BC5: 5, BC7_M6_OPAQUE_ONLY: 6, BC7_M5: 7, PVRTC1_4_RGB: 8, PVRTC1_4_RGBA: 9, ASTC_4x4: 10, ATC_RGB: 11, ATC_RGBA_INTERPOLATED_ALPHA: 12, RGBA32: 13, RGB565: 14, BGR565: 15, RGBA4444: 16 }, KTX2Loader.EngineFormat = { RGBAFormat: _mpChunkDeps_three_build_three_core_min.Wt, RGBA_ASTC_4x4_Format: _mpChunkDeps_three_build_three_core_min.he, RGBA_BPTC_Format: _mpChunkDeps_three_build_three_core_min.Me, RGBA_ETC2_EAC_Format: _mpChunkDeps_three_build_three_core_min.ae, RGBA_PVRTC_4BPPV1_Format: _mpChunkDeps_three_build_three_core_min.ie, RGBA_S3TC_DXT5_Format: _mpChunkDeps_three_build_three_core_min.te, RGB_ETC1_Format: _mpChunkDeps_three_build_three_core_min.ne, RGB_ETC2_Format: _mpChunkDeps_three_build_three_core_min.oe, RGB_PVRTC_4BPPV1_Format: _mpChunkDeps_three_build_three_core_min.ee, RGBA_S3TC_DXT1_Format: _mpChunkDeps_three_build_three_core_min.Qt };
  const se = /* @__PURE__ */ new Set([_mpChunkDeps_three_build_three_core_min.Wt, _mpChunkDeps_three_build_three_core_min.Xt, _mpChunkDeps_three_build_three_core_min.qt]), oe = { [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.Ae]: _mpChunkDeps_three_build_three_core_min.Wt, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.ge]: _mpChunkDeps_three_build_three_core_min.Wt, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.Ft]: _mpChunkDeps_three_build_three_core_min.Wt, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.Ct]: _mpChunkDeps_three_build_three_core_min.Wt, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.we]: _mpChunkDeps_three_build_three_core_min.Xt, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.ae]: _mpChunkDeps_three_build_three_core_min.Xt, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.xt]: _mpChunkDeps_three_build_three_core_min.Xt, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.wt]: _mpChunkDeps_three_build_three_core_min.Xt, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.ue]: _mpChunkDeps_three_build_three_core_min.qt, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.te]: _mpChunkDeps_three_build_three_core_min.qt, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.yt]: _mpChunkDeps_three_build_three_core_min.qt, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.ct]: _mpChunkDeps_three_build_three_core_min.qt, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.Sn]: _mpChunkDeps_three_build_three_core_min.de, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.In]: _mpChunkDeps_three_build_three_core_min.de }, ae = { [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.Ae]: _mpChunkDeps_three_build_three_core_min.Rt, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.ge]: _mpChunkDeps_three_build_three_core_min.Et, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.Ft]: _mpChunkDeps_three_build_three_core_min.Tt, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.Ct]: _mpChunkDeps_three_build_three_core_min.Tt, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.we]: _mpChunkDeps_three_build_three_core_min.Rt, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.ae]: _mpChunkDeps_three_build_three_core_min.Et, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.xt]: _mpChunkDeps_three_build_three_core_min.Tt, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.wt]: _mpChunkDeps_three_build_three_core_min.Tt, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.ue]: _mpChunkDeps_three_build_three_core_min.Rt, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.te]: _mpChunkDeps_three_build_three_core_min.Et, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.yt]: _mpChunkDeps_three_build_three_core_min.Tt, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.ct]: _mpChunkDeps_three_build_three_core_min.Tt, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.Sn]: _mpChunkDeps_three_build_three_core_min.Tt, [_mpChunkDeps_three_examples_jsm_libs_ktxParse_module.In]: _mpChunkDeps_three_build_three_core_min.Tt };
  function ie(e) {
    const t = e.dataFormatDescriptor[0];
    return t.colorPrimaries === _mpChunkDeps_three_examples_jsm_libs_ktxParse_module.C ? t.transferFunction === _mpChunkDeps_three_examples_jsm_libs_ktxParse_module.u ? _mpChunkDeps_three_build_three_core_min.Ge : _mpChunkDeps_three_build_three_core_min.$e : t.colorPrimaries === _mpChunkDeps_three_examples_jsm_libs_ktxParse_module.R ? t.transferFunction === _mpChunkDeps_three_examples_jsm_libs_ktxParse_module.u ? "display-p3" : "display-p3-linear" : (t.colorPrimaries === _mpChunkDeps_three_examples_jsm_libs_ktxParse_module.T || console.warn(`THREE.KTX2Loader: Unsupported color primaries, "${t.colorPrimaries}"`), _mpChunkDeps_three_build_three_core_min.Ze);
  }
  return KTX2Loader;
}();
exports.ee = ee;
//# sourceMappingURL=../../../../../../../../.sourcemap/mp-weixin/_mpChunkDeps/@minisheep/three-platform-adapter/dist/three-override/jsm/loaders/KTX2Loader.js.map
