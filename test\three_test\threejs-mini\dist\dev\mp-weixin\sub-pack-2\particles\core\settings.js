"use strict";
require("../../chunks/three/build/three.module.min.js");
const _mpChunkDeps_three_build_three_core_min = require("../../chunks/three/build/three.core.min.js");
const amountMap = {
  "4k": [64, 64, 0.29],
  "8k": [128, 64, 0.42],
  "16k": [128, 128, 0.48],
  "32k": [256, 128, 0.55],
  "65k": [256, 256, 0.6],
  "131k": [512, 256, 0.85],
  "252k": [512, 512, 1.2],
  "524k": [1024, 512, 1.4],
  "1m": [1024, 1024, 1.6],
  "2m": [2048, 1024, 2],
  "4m": [2048, 2048, 2.5]
};
const query = {
  amount: "131k"
};
const amountInfo = amountMap[query.amount];
const ray = new _mpChunkDeps_three_build_three_core_min.rr();
const settings = {
  simulatorTextureWidth: amountInfo[0],
  simulatorTextureHeight: amountInfo[1],
  useTriangleParticles: true,
  followMouse: true,
  speed: 1,
  dieSpeed: 0.015,
  radius: amountInfo[2] / 2,
  curlSize: 0.02,
  attraction: 1.1,
  bgColor: "#343434",
  color1: "#ffffff",
  color2: "#ffffff",
  mouse: new _mpChunkDeps_three_build_three_core_min.Zs(0, 0),
  mouse3d: ray.origin,
  camera: null,
  cameraPosition: null
};
exports.ray = ray;
exports.settings = settings;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/sub-pack-2/particles/core/settings.js.map
