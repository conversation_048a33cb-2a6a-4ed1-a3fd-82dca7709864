{"version": 3, "file": "index.js", "sources": ["../node_modules/event-target-shim/index.mjs"], "sourcesContent": ["/**\n * Assert a condition.\n * @param condition The condition that it should satisfy.\n * @param message The error message.\n * @param args The arguments for replacing placeholders in the message.\n */\nfunction assertType(condition, message, ...args) {\n    if (!condition) {\n        throw new TypeError(format(message, args));\n    }\n}\n/**\n * Convert a text and arguments to one string.\n * @param message The formating text\n * @param args The arguments.\n */\nfunction format(message, args) {\n    let i = 0;\n    return message.replace(/%[os]/gu, () => anyToString(args[i++]));\n}\n/**\n * Convert a value to a string representation.\n * @param x The value to get the string representation.\n */\nfunction anyToString(x) {\n    if (typeof x !== \"object\" || x === null) {\n        return String(x);\n    }\n    return Object.prototype.toString.call(x);\n}\n\nlet currentErrorHandler;\n/**\n * Set the error handler.\n * @param value The error handler to set.\n */\nfunction setErrorHandler(value) {\n    assertType(typeof value === \"function\" || value === undefined, \"The error handler must be a function or undefined, but got %o.\", value);\n    currentErrorHandler = value;\n}\n/**\n * Print a error message.\n * @param maybeError The error object.\n */\nfunction reportError(maybeError) {\n    try {\n        const error = maybeError instanceof Error\n            ? maybeError\n            : new Error(anyToString(maybeError));\n        // Call the user-defined error handler if exists.\n        if (currentErrorHandler) {\n            currentErrorHandler(error);\n            return;\n        }\n        // Dispatch an `error` event if this is on a browser.\n        if (typeof dispatchEvent === \"function\" &&\n            typeof ErrorEvent === \"function\") {\n            dispatchEvent(new ErrorEvent(\"error\", { error, message: error.message }));\n        }\n        // Emit an `uncaughtException` event if this is on Node.js.\n        //istanbul ignore else\n        else if (typeof process !== \"undefined\" &&\n            typeof process.emit === \"function\") {\n            process.emit(\"uncaughtException\", error);\n            return;\n        }\n        // Otherwise, print the error.\n        console.error(error);\n    }\n    catch (_a) {\n        // ignore.\n    }\n}\n\n/**\n * The global object.\n */\n//istanbul ignore next\nconst Global = typeof window !== \"undefined\"\n    ? window\n    : typeof self !== \"undefined\"\n        ? self\n        : typeof global !== \"undefined\"\n            ? global\n            : typeof globalThis !== \"undefined\"\n                ? globalThis\n                : undefined;\n\nlet currentWarnHandler;\n/**\n * Set the warning handler.\n * @param value The warning handler to set.\n */\nfunction setWarningHandler(value) {\n    assertType(typeof value === \"function\" || value === undefined, \"The warning handler must be a function or undefined, but got %o.\", value);\n    currentWarnHandler = value;\n}\n/**\n * The warning information.\n */\nclass Warning {\n    constructor(code, message) {\n        this.code = code;\n        this.message = message;\n    }\n    /**\n     * Report this warning.\n     * @param args The arguments of the warning.\n     */\n    warn(...args) {\n        var _a;\n        try {\n            // Call the user-defined warning handler if exists.\n            if (currentWarnHandler) {\n                currentWarnHandler({ ...this, args });\n                return;\n            }\n            // Otherwise, print the warning.\n            const stack = ((_a = new Error().stack) !== null && _a !== void 0 ? _a : \"\").replace(/^(?:.+?\\n){2}/gu, \"\\n\");\n            console.warn(this.message, ...args, stack);\n        }\n        catch (_b) {\n            // Ignore.\n        }\n    }\n}\n\nconst InitEventWasCalledWhileDispatching = new Warning(\"W01\", \"Unable to initialize event under dispatching.\");\nconst FalsyWasAssignedToCancelBubble = new Warning(\"W02\", \"Assigning any falsy value to 'cancelBubble' property has no effect.\");\nconst TruthyWasAssignedToReturnValue = new Warning(\"W03\", \"Assigning any truthy value to 'returnValue' property has no effect.\");\nconst NonCancelableEventWasCanceled = new Warning(\"W04\", \"Unable to preventDefault on non-cancelable events.\");\nconst CanceledInPassiveListener = new Warning(\"W05\", \"Unable to preventDefault inside passive event listener invocation.\");\nconst EventListenerWasDuplicated = new Warning(\"W06\", \"An event listener wasn't added because it has been added already: %o, %o\");\nconst OptionWasIgnored = new Warning(\"W07\", \"The %o option value was abandoned because the event listener wasn't added as duplicated.\");\nconst InvalidEventListener = new Warning(\"W08\", \"The 'callback' argument must be a function or an object that has 'handleEvent' method: %o\");\nconst InvalidAttributeHandler = new Warning(\"W09\", \"Event attribute handler must be a function: %o\");\n\n/*eslint-disable class-methods-use-this */\n/**\n * An implementation of `Event` interface, that wraps a given event object.\n * `EventTarget` shim can control the internal state of this `Event` objects.\n * @see https://dom.spec.whatwg.org/#event\n */\nclass Event {\n    /**\n     * @see https://dom.spec.whatwg.org/#dom-event-none\n     */\n    static get NONE() {\n        return NONE;\n    }\n    /**\n     * @see https://dom.spec.whatwg.org/#dom-event-capturing_phase\n     */\n    static get CAPTURING_PHASE() {\n        return CAPTURING_PHASE;\n    }\n    /**\n     * @see https://dom.spec.whatwg.org/#dom-event-at_target\n     */\n    static get AT_TARGET() {\n        return AT_TARGET;\n    }\n    /**\n     * @see https://dom.spec.whatwg.org/#dom-event-bubbling_phase\n     */\n    static get BUBBLING_PHASE() {\n        return BUBBLING_PHASE;\n    }\n    /**\n     * Initialize this event instance.\n     * @param type The type of this event.\n     * @param eventInitDict Options to initialize.\n     * @see https://dom.spec.whatwg.org/#dom-event-event\n     */\n    constructor(type, eventInitDict) {\n        Object.defineProperty(this, \"isTrusted\", {\n            value: false,\n            enumerable: true,\n        });\n        const opts = eventInitDict !== null && eventInitDict !== void 0 ? eventInitDict : {};\n        internalDataMap.set(this, {\n            type: String(type),\n            bubbles: Boolean(opts.bubbles),\n            cancelable: Boolean(opts.cancelable),\n            composed: Boolean(opts.composed),\n            target: null,\n            currentTarget: null,\n            stopPropagationFlag: false,\n            stopImmediatePropagationFlag: false,\n            canceledFlag: false,\n            inPassiveListenerFlag: false,\n            dispatchFlag: false,\n            timeStamp: Date.now(),\n        });\n    }\n    /**\n     * The type of this event.\n     * @see https://dom.spec.whatwg.org/#dom-event-type\n     */\n    get type() {\n        return $(this).type;\n    }\n    /**\n     * The event target of the current dispatching.\n     * @see https://dom.spec.whatwg.org/#dom-event-target\n     */\n    get target() {\n        return $(this).target;\n    }\n    /**\n     * The event target of the current dispatching.\n     * @deprecated Use the `target` property instead.\n     * @see https://dom.spec.whatwg.org/#dom-event-srcelement\n     */\n    get srcElement() {\n        return $(this).target;\n    }\n    /**\n     * The event target of the current dispatching.\n     * @see https://dom.spec.whatwg.org/#dom-event-currenttarget\n     */\n    get currentTarget() {\n        return $(this).currentTarget;\n    }\n    /**\n     * The event target of the current dispatching.\n     * This doesn't support node tree.\n     * @see https://dom.spec.whatwg.org/#dom-event-composedpath\n     */\n    composedPath() {\n        const currentTarget = $(this).currentTarget;\n        if (currentTarget) {\n            return [currentTarget];\n        }\n        return [];\n    }\n    /**\n     * @see https://dom.spec.whatwg.org/#dom-event-none\n     */\n    get NONE() {\n        return NONE;\n    }\n    /**\n     * @see https://dom.spec.whatwg.org/#dom-event-capturing_phase\n     */\n    get CAPTURING_PHASE() {\n        return CAPTURING_PHASE;\n    }\n    /**\n     * @see https://dom.spec.whatwg.org/#dom-event-at_target\n     */\n    get AT_TARGET() {\n        return AT_TARGET;\n    }\n    /**\n     * @see https://dom.spec.whatwg.org/#dom-event-bubbling_phase\n     */\n    get BUBBLING_PHASE() {\n        return BUBBLING_PHASE;\n    }\n    /**\n     * The current event phase.\n     * @see https://dom.spec.whatwg.org/#dom-event-eventphase\n     */\n    get eventPhase() {\n        return $(this).dispatchFlag ? 2 : 0;\n    }\n    /**\n     * Stop event bubbling.\n     * Because this shim doesn't support node tree, this merely changes the `cancelBubble` property value.\n     * @see https://dom.spec.whatwg.org/#dom-event-stoppropagation\n     */\n    stopPropagation() {\n        $(this).stopPropagationFlag = true;\n    }\n    /**\n     * `true` if event bubbling was stopped.\n     * @deprecated\n     * @see https://dom.spec.whatwg.org/#dom-event-cancelbubble\n     */\n    get cancelBubble() {\n        return $(this).stopPropagationFlag;\n    }\n    /**\n     * Stop event bubbling if `true` is set.\n     * @deprecated Use the `stopPropagation()` method instead.\n     * @see https://dom.spec.whatwg.org/#dom-event-cancelbubble\n     */\n    set cancelBubble(value) {\n        if (value) {\n            $(this).stopPropagationFlag = true;\n        }\n        else {\n            FalsyWasAssignedToCancelBubble.warn();\n        }\n    }\n    /**\n     * Stop event bubbling and subsequent event listener callings.\n     * @see https://dom.spec.whatwg.org/#dom-event-stopimmediatepropagation\n     */\n    stopImmediatePropagation() {\n        const data = $(this);\n        data.stopPropagationFlag = data.stopImmediatePropagationFlag = true;\n    }\n    /**\n     * `true` if this event will bubble.\n     * @see https://dom.spec.whatwg.org/#dom-event-bubbles\n     */\n    get bubbles() {\n        return $(this).bubbles;\n    }\n    /**\n     * `true` if this event can be canceled by the `preventDefault()` method.\n     * @see https://dom.spec.whatwg.org/#dom-event-cancelable\n     */\n    get cancelable() {\n        return $(this).cancelable;\n    }\n    /**\n     * `true` if the default behavior will act.\n     * @deprecated Use the `defaultPrevented` proeprty instead.\n     * @see https://dom.spec.whatwg.org/#dom-event-returnvalue\n     */\n    get returnValue() {\n        return !$(this).canceledFlag;\n    }\n    /**\n     * Cancel the default behavior if `false` is set.\n     * @deprecated Use the `preventDefault()` method instead.\n     * @see https://dom.spec.whatwg.org/#dom-event-returnvalue\n     */\n    set returnValue(value) {\n        if (!value) {\n            setCancelFlag($(this));\n        }\n        else {\n            TruthyWasAssignedToReturnValue.warn();\n        }\n    }\n    /**\n     * Cancel the default behavior.\n     * @see https://dom.spec.whatwg.org/#dom-event-preventdefault\n     */\n    preventDefault() {\n        setCancelFlag($(this));\n    }\n    /**\n     * `true` if the default behavior was canceled.\n     * @see https://dom.spec.whatwg.org/#dom-event-defaultprevented\n     */\n    get defaultPrevented() {\n        return $(this).canceledFlag;\n    }\n    /**\n     * @see https://dom.spec.whatwg.org/#dom-event-composed\n     */\n    get composed() {\n        return $(this).composed;\n    }\n    /**\n     * @see https://dom.spec.whatwg.org/#dom-event-istrusted\n     */\n    //istanbul ignore next\n    get isTrusted() {\n        return false;\n    }\n    /**\n     * @see https://dom.spec.whatwg.org/#dom-event-timestamp\n     */\n    get timeStamp() {\n        return $(this).timeStamp;\n    }\n    /**\n     * @deprecated Don't use this method. The constructor did initialization.\n     */\n    initEvent(type, bubbles = false, cancelable = false) {\n        const data = $(this);\n        if (data.dispatchFlag) {\n            InitEventWasCalledWhileDispatching.warn();\n            return;\n        }\n        internalDataMap.set(this, {\n            ...data,\n            type: String(type),\n            bubbles: Boolean(bubbles),\n            cancelable: Boolean(cancelable),\n            target: null,\n            currentTarget: null,\n            stopPropagationFlag: false,\n            stopImmediatePropagationFlag: false,\n            canceledFlag: false,\n        });\n    }\n}\n//------------------------------------------------------------------------------\n// Helpers\n//------------------------------------------------------------------------------\nconst NONE = 0;\nconst CAPTURING_PHASE = 1;\nconst AT_TARGET = 2;\nconst BUBBLING_PHASE = 3;\n/**\n * Private data for event wrappers.\n */\nconst internalDataMap = new WeakMap();\n/**\n * Get private data.\n * @param event The event object to get private data.\n * @param name The variable name to report.\n * @returns The private data of the event.\n */\nfunction $(event, name = \"this\") {\n    const retv = internalDataMap.get(event);\n    assertType(retv != null, \"'%s' must be an object that Event constructor created, but got another one: %o\", name, event);\n    return retv;\n}\n/**\n * https://dom.spec.whatwg.org/#set-the-canceled-flag\n * @param data private data.\n */\nfunction setCancelFlag(data) {\n    if (data.inPassiveListenerFlag) {\n        CanceledInPassiveListener.warn();\n        return;\n    }\n    if (!data.cancelable) {\n        NonCancelableEventWasCanceled.warn();\n        return;\n    }\n    data.canceledFlag = true;\n}\n// Set enumerable\nObject.defineProperty(Event, \"NONE\", { enumerable: true });\nObject.defineProperty(Event, \"CAPTURING_PHASE\", { enumerable: true });\nObject.defineProperty(Event, \"AT_TARGET\", { enumerable: true });\nObject.defineProperty(Event, \"BUBBLING_PHASE\", { enumerable: true });\nconst keys = Object.getOwnPropertyNames(Event.prototype);\nfor (let i = 0; i < keys.length; ++i) {\n    if (keys[i] === \"constructor\") {\n        continue;\n    }\n    Object.defineProperty(Event.prototype, keys[i], { enumerable: true });\n}\n// Ensure `event instanceof window.Event` is `true`.\nif (typeof Global !== \"undefined\" && typeof Global.Event !== \"undefined\") {\n    Object.setPrototypeOf(Event.prototype, Global.Event.prototype);\n}\n\n/**\n * Create a new InvalidStateError instance.\n * @param message The error message.\n */\nfunction createInvalidStateError(message) {\n    if (Global.DOMException) {\n        return new Global.DOMException(message, \"InvalidStateError\");\n    }\n    if (DOMException == null) {\n        DOMException = class DOMException extends Error {\n            constructor(msg) {\n                super(msg);\n                if (Error.captureStackTrace) {\n                    Error.captureStackTrace(this, DOMException);\n                }\n            }\n            // eslint-disable-next-line class-methods-use-this\n            get code() {\n                return 11;\n            }\n            // eslint-disable-next-line class-methods-use-this\n            get name() {\n                return \"InvalidStateError\";\n            }\n        };\n        Object.defineProperties(DOMException.prototype, {\n            code: { enumerable: true },\n            name: { enumerable: true },\n        });\n        defineErrorCodeProperties(DOMException);\n        defineErrorCodeProperties(DOMException.prototype);\n    }\n    return new DOMException(message);\n}\n//------------------------------------------------------------------------------\n// Helpers\n//------------------------------------------------------------------------------\nlet DOMException;\nconst ErrorCodeMap = {\n    INDEX_SIZE_ERR: 1,\n    DOMSTRING_SIZE_ERR: 2,\n    HIERARCHY_REQUEST_ERR: 3,\n    WRONG_DOCUMENT_ERR: 4,\n    INVALID_CHARACTER_ERR: 5,\n    NO_DATA_ALLOWED_ERR: 6,\n    NO_MODIFICATION_ALLOWED_ERR: 7,\n    NOT_FOUND_ERR: 8,\n    NOT_SUPPORTED_ERR: 9,\n    INUSE_ATTRIBUTE_ERR: 10,\n    INVALID_STATE_ERR: 11,\n    SYNTAX_ERR: 12,\n    INVALID_MODIFICATION_ERR: 13,\n    NAMESPACE_ERR: 14,\n    INVALID_ACCESS_ERR: 15,\n    VALIDATION_ERR: 16,\n    TYPE_MISMATCH_ERR: 17,\n    SECURITY_ERR: 18,\n    NETWORK_ERR: 19,\n    ABORT_ERR: 20,\n    URL_MISMATCH_ERR: 21,\n    QUOTA_EXCEEDED_ERR: 22,\n    TIMEOUT_ERR: 23,\n    INVALID_NODE_TYPE_ERR: 24,\n    DATA_CLONE_ERR: 25,\n};\nfunction defineErrorCodeProperties(obj) {\n    const keys = Object.keys(ErrorCodeMap);\n    for (let i = 0; i < keys.length; ++i) {\n        const key = keys[i];\n        const value = ErrorCodeMap[key];\n        Object.defineProperty(obj, key, {\n            get() {\n                return value;\n            },\n            configurable: true,\n            enumerable: true,\n        });\n    }\n}\n\n/**\n * An implementation of `Event` interface, that wraps a given event object.\n * This class controls the internal state of `Event`.\n * @see https://dom.spec.whatwg.org/#interface-event\n */\nclass EventWrapper extends Event {\n    /**\n     * Wrap a given event object to control states.\n     * @param event The event-like object to wrap.\n     */\n    static wrap(event) {\n        return new (getWrapperClassOf(event))(event);\n    }\n    constructor(event) {\n        super(event.type, {\n            bubbles: event.bubbles,\n            cancelable: event.cancelable,\n            composed: event.composed,\n        });\n        if (event.cancelBubble) {\n            super.stopPropagation();\n        }\n        if (event.defaultPrevented) {\n            super.preventDefault();\n        }\n        internalDataMap$1.set(this, { original: event });\n        // Define accessors\n        const keys = Object.keys(event);\n        for (let i = 0; i < keys.length; ++i) {\n            const key = keys[i];\n            if (!(key in this)) {\n                Object.defineProperty(this, key, defineRedirectDescriptor(event, key));\n            }\n        }\n    }\n    stopPropagation() {\n        super.stopPropagation();\n        const { original } = $$1(this);\n        if (\"stopPropagation\" in original) {\n            original.stopPropagation();\n        }\n    }\n    get cancelBubble() {\n        return super.cancelBubble;\n    }\n    set cancelBubble(value) {\n        super.cancelBubble = value;\n        const { original } = $$1(this);\n        if (\"cancelBubble\" in original) {\n            original.cancelBubble = value;\n        }\n    }\n    stopImmediatePropagation() {\n        super.stopImmediatePropagation();\n        const { original } = $$1(this);\n        if (\"stopImmediatePropagation\" in original) {\n            original.stopImmediatePropagation();\n        }\n    }\n    get returnValue() {\n        return super.returnValue;\n    }\n    set returnValue(value) {\n        super.returnValue = value;\n        const { original } = $$1(this);\n        if (\"returnValue\" in original) {\n            original.returnValue = value;\n        }\n    }\n    preventDefault() {\n        super.preventDefault();\n        const { original } = $$1(this);\n        if (\"preventDefault\" in original) {\n            original.preventDefault();\n        }\n    }\n    get timeStamp() {\n        const { original } = $$1(this);\n        if (\"timeStamp\" in original) {\n            return original.timeStamp;\n        }\n        return super.timeStamp;\n    }\n}\n/**\n * Private data for event wrappers.\n */\nconst internalDataMap$1 = new WeakMap();\n/**\n * Get private data.\n * @param event The event object to get private data.\n * @returns The private data of the event.\n */\nfunction $$1(event) {\n    const retv = internalDataMap$1.get(event);\n    assertType(retv != null, \"'this' is expected an Event object, but got\", event);\n    return retv;\n}\n/**\n * Cache for wrapper classes.\n * @type {WeakMap<Object, Function>}\n * @private\n */\nconst wrapperClassCache = new WeakMap();\n// Make association for wrappers.\nwrapperClassCache.set(Object.prototype, EventWrapper);\nif (typeof Global !== \"undefined\" && typeof Global.Event !== \"undefined\") {\n    wrapperClassCache.set(Global.Event.prototype, EventWrapper);\n}\n/**\n * Get the wrapper class of a given prototype.\n * @param originalEvent The event object to wrap.\n */\nfunction getWrapperClassOf(originalEvent) {\n    const prototype = Object.getPrototypeOf(originalEvent);\n    if (prototype == null) {\n        return EventWrapper;\n    }\n    let wrapper = wrapperClassCache.get(prototype);\n    if (wrapper == null) {\n        wrapper = defineWrapper(getWrapperClassOf(prototype), prototype);\n        wrapperClassCache.set(prototype, wrapper);\n    }\n    return wrapper;\n}\n/**\n * Define new wrapper class.\n * @param BaseEventWrapper The base wrapper class.\n * @param originalPrototype The prototype of the original event.\n */\nfunction defineWrapper(BaseEventWrapper, originalPrototype) {\n    class CustomEventWrapper extends BaseEventWrapper {\n    }\n    const keys = Object.keys(originalPrototype);\n    for (let i = 0; i < keys.length; ++i) {\n        Object.defineProperty(CustomEventWrapper.prototype, keys[i], defineRedirectDescriptor(originalPrototype, keys[i]));\n    }\n    return CustomEventWrapper;\n}\n/**\n * Get the property descriptor to redirect a given property.\n */\nfunction defineRedirectDescriptor(obj, key) {\n    const d = Object.getOwnPropertyDescriptor(obj, key);\n    return {\n        get() {\n            const original = $$1(this).original;\n            const value = original[key];\n            if (typeof value === \"function\") {\n                return value.bind(original);\n            }\n            return value;\n        },\n        set(value) {\n            const original = $$1(this).original;\n            original[key] = value;\n        },\n        configurable: d.configurable,\n        enumerable: d.enumerable,\n    };\n}\n\n/**\n * Create a new listener.\n * @param callback The callback function.\n * @param capture The capture flag.\n * @param passive The passive flag.\n * @param once The once flag.\n * @param signal The abort signal.\n * @param signalListener The abort event listener for the abort signal.\n */\nfunction createListener(callback, capture, passive, once, signal, signalListener) {\n    return {\n        callback,\n        flags: (capture ? 1 /* Capture */ : 0) |\n            (passive ? 2 /* Passive */ : 0) |\n            (once ? 4 /* Once */ : 0),\n        signal,\n        signalListener,\n    };\n}\n/**\n * Set the `removed` flag to the given listener.\n * @param listener The listener to check.\n */\nfunction setRemoved(listener) {\n    listener.flags |= 8 /* Removed */;\n}\n/**\n * Check if the given listener has the `capture` flag or not.\n * @param listener The listener to check.\n */\nfunction isCapture(listener) {\n    return (listener.flags & 1 /* Capture */) === 1 /* Capture */;\n}\n/**\n * Check if the given listener has the `passive` flag or not.\n * @param listener The listener to check.\n */\nfunction isPassive(listener) {\n    return (listener.flags & 2 /* Passive */) === 2 /* Passive */;\n}\n/**\n * Check if the given listener has the `once` flag or not.\n * @param listener The listener to check.\n */\nfunction isOnce(listener) {\n    return (listener.flags & 4 /* Once */) === 4 /* Once */;\n}\n/**\n * Check if the given listener has the `removed` flag or not.\n * @param listener The listener to check.\n */\nfunction isRemoved(listener) {\n    return (listener.flags & 8 /* Removed */) === 8 /* Removed */;\n}\n/**\n * Call an event listener.\n * @param listener The listener to call.\n * @param target The event target object for `thisArg`.\n * @param event The event object for the first argument.\n * @param attribute `true` if this callback is an event attribute handler.\n */\nfunction invokeCallback({ callback }, target, event) {\n    try {\n        if (typeof callback === \"function\") {\n            callback.call(target, event);\n        }\n        else if (typeof callback.handleEvent === \"function\") {\n            callback.handleEvent(event);\n        }\n    }\n    catch (thrownError) {\n        reportError(thrownError);\n    }\n}\n\n/**\n * Find the index of given listener.\n * This returns `-1` if not found.\n * @param list The listener list.\n * @param callback The callback function to find.\n * @param capture The capture flag to find.\n */\nfunction findIndexOfListener({ listeners }, callback, capture) {\n    for (let i = 0; i < listeners.length; ++i) {\n        if (listeners[i].callback === callback &&\n            isCapture(listeners[i]) === capture) {\n            return i;\n        }\n    }\n    return -1;\n}\n/**\n * Add the given listener.\n * Does copy-on-write if needed.\n * @param list The listener list.\n * @param callback The callback function.\n * @param capture The capture flag.\n * @param passive The passive flag.\n * @param once The once flag.\n * @param signal The abort signal.\n */\nfunction addListener(list, callback, capture, passive, once, signal) {\n    let signalListener;\n    if (signal) {\n        signalListener = removeListener.bind(null, list, callback, capture);\n        signal.addEventListener(\"abort\", signalListener);\n    }\n    const listener = createListener(callback, capture, passive, once, signal, signalListener);\n    if (list.cow) {\n        list.cow = false;\n        list.listeners = [...list.listeners, listener];\n    }\n    else {\n        list.listeners.push(listener);\n    }\n    return listener;\n}\n/**\n * Remove a listener.\n * @param list The listener list.\n * @param callback The callback function to find.\n * @param capture The capture flag to find.\n * @returns `true` if it mutated the list directly.\n */\nfunction removeListener(list, callback, capture) {\n    const index = findIndexOfListener(list, callback, capture);\n    if (index !== -1) {\n        return removeListenerAt(list, index);\n    }\n    return false;\n}\n/**\n * Remove a listener.\n * @param list The listener list.\n * @param index The index of the target listener.\n * @param disableCow Disable copy-on-write if true.\n * @returns `true` if it mutated the `listeners` array directly.\n */\nfunction removeListenerAt(list, index, disableCow = false) {\n    const listener = list.listeners[index];\n    // Set the removed flag.\n    setRemoved(listener);\n    // Dispose the abort signal listener if exists.\n    if (listener.signal) {\n        listener.signal.removeEventListener(\"abort\", listener.signalListener);\n    }\n    // Remove it from the array.\n    if (list.cow && !disableCow) {\n        list.cow = false;\n        list.listeners = list.listeners.filter((_, i) => i !== index);\n        return false;\n    }\n    list.listeners.splice(index, 1);\n    return true;\n}\n\n/**\n * Create a new `ListenerListMap` object.\n */\nfunction createListenerListMap() {\n    return Object.create(null);\n}\n/**\n * Get the listener list of the given type.\n * If the listener list has not been initialized, initialize and return it.\n * @param listenerMap The listener list map.\n * @param type The event type to get.\n */\nfunction ensureListenerList(listenerMap, type) {\n    var _a;\n    return ((_a = listenerMap[type]) !== null && _a !== void 0 ? _a : (listenerMap[type] = {\n        attrCallback: undefined,\n        attrListener: undefined,\n        cow: false,\n        listeners: [],\n    }));\n}\n\n/**\n * An implementation of the `EventTarget` interface.\n * @see https://dom.spec.whatwg.org/#eventtarget\n */\nclass EventTarget {\n    /**\n     * Initialize this instance.\n     */\n    constructor() {\n        internalDataMap$2.set(this, createListenerListMap());\n    }\n    // Implementation\n    addEventListener(type0, callback0, options0) {\n        const listenerMap = $$2(this);\n        const { callback, capture, once, passive, signal, type, } = normalizeAddOptions(type0, callback0, options0);\n        if (callback == null || (signal === null || signal === void 0 ? void 0 : signal.aborted)) {\n            return;\n        }\n        const list = ensureListenerList(listenerMap, type);\n        // Find existing listener.\n        const i = findIndexOfListener(list, callback, capture);\n        if (i !== -1) {\n            warnDuplicate(list.listeners[i], passive, once, signal);\n            return;\n        }\n        // Add the new listener.\n        addListener(list, callback, capture, passive, once, signal);\n    }\n    // Implementation\n    removeEventListener(type0, callback0, options0) {\n        const listenerMap = $$2(this);\n        const { callback, capture, type } = normalizeOptions(type0, callback0, options0);\n        const list = listenerMap[type];\n        if (callback != null && list) {\n            removeListener(list, callback, capture);\n        }\n    }\n    // Implementation\n    dispatchEvent(e) {\n        const list = $$2(this)[String(e.type)];\n        if (list == null) {\n            return true;\n        }\n        const event = e instanceof Event ? e : EventWrapper.wrap(e);\n        const eventData = $(event, \"event\");\n        if (eventData.dispatchFlag) {\n            throw createInvalidStateError(\"This event has been in dispatching.\");\n        }\n        eventData.dispatchFlag = true;\n        eventData.target = eventData.currentTarget = this;\n        if (!eventData.stopPropagationFlag) {\n            const { cow, listeners } = list;\n            // Set copy-on-write flag.\n            list.cow = true;\n            // Call listeners.\n            for (let i = 0; i < listeners.length; ++i) {\n                const listener = listeners[i];\n                // Skip if removed.\n                if (isRemoved(listener)) {\n                    continue;\n                }\n                // Remove this listener if has the `once` flag.\n                if (isOnce(listener) && removeListenerAt(list, i, !cow)) {\n                    // Because this listener was removed, the next index is the\n                    // same as the current value.\n                    i -= 1;\n                }\n                // Call this listener with the `passive` flag.\n                eventData.inPassiveListenerFlag = isPassive(listener);\n                invokeCallback(listener, this, event);\n                eventData.inPassiveListenerFlag = false;\n                // Stop if the `event.stopImmediatePropagation()` method was called.\n                if (eventData.stopImmediatePropagationFlag) {\n                    break;\n                }\n            }\n            // Restore copy-on-write flag.\n            if (!cow) {\n                list.cow = false;\n            }\n        }\n        eventData.target = null;\n        eventData.currentTarget = null;\n        eventData.stopImmediatePropagationFlag = false;\n        eventData.stopPropagationFlag = false;\n        eventData.dispatchFlag = false;\n        return !eventData.canceledFlag;\n    }\n}\n/**\n * Internal data.\n */\nconst internalDataMap$2 = new WeakMap();\n/**\n * Get private data.\n * @param target The event target object to get private data.\n * @param name The variable name to report.\n * @returns The private data of the event.\n */\nfunction $$2(target, name = \"this\") {\n    const retv = internalDataMap$2.get(target);\n    assertType(retv != null, \"'%s' must be an object that EventTarget constructor created, but got another one: %o\", name, target);\n    return retv;\n}\n/**\n * Normalize options.\n * @param options The options to normalize.\n */\nfunction normalizeAddOptions(type, callback, options) {\n    var _a;\n    assertCallback(callback);\n    if (typeof options === \"object\" && options !== null) {\n        return {\n            type: String(type),\n            callback: callback !== null && callback !== void 0 ? callback : undefined,\n            capture: Boolean(options.capture),\n            passive: Boolean(options.passive),\n            once: Boolean(options.once),\n            signal: (_a = options.signal) !== null && _a !== void 0 ? _a : undefined,\n        };\n    }\n    return {\n        type: String(type),\n        callback: callback !== null && callback !== void 0 ? callback : undefined,\n        capture: Boolean(options),\n        passive: false,\n        once: false,\n        signal: undefined,\n    };\n}\n/**\n * Normalize options.\n * @param options The options to normalize.\n */\nfunction normalizeOptions(type, callback, options) {\n    assertCallback(callback);\n    if (typeof options === \"object\" && options !== null) {\n        return {\n            type: String(type),\n            callback: callback !== null && callback !== void 0 ? callback : undefined,\n            capture: Boolean(options.capture),\n        };\n    }\n    return {\n        type: String(type),\n        callback: callback !== null && callback !== void 0 ? callback : undefined,\n        capture: Boolean(options),\n    };\n}\n/**\n * Assert the type of 'callback' argument.\n * @param callback The callback to check.\n */\nfunction assertCallback(callback) {\n    if (typeof callback === \"function\" ||\n        (typeof callback === \"object\" &&\n            callback !== null &&\n            typeof callback.handleEvent === \"function\")) {\n        return;\n    }\n    if (callback == null || typeof callback === \"object\") {\n        InvalidEventListener.warn(callback);\n        return;\n    }\n    throw new TypeError(format(InvalidEventListener.message, [callback]));\n}\n/**\n * Print warning for duplicated.\n * @param listener The current listener that is duplicated.\n * @param passive The passive flag of the new duplicated listener.\n * @param once The once flag of the new duplicated listener.\n * @param signal The signal object of the new duplicated listener.\n */\nfunction warnDuplicate(listener, passive, once, signal) {\n    EventListenerWasDuplicated.warn(isCapture(listener) ? \"capture\" : \"bubble\", listener.callback);\n    if (isPassive(listener) !== passive) {\n        OptionWasIgnored.warn(\"passive\");\n    }\n    if (isOnce(listener) !== once) {\n        OptionWasIgnored.warn(\"once\");\n    }\n    if (listener.signal !== signal) {\n        OptionWasIgnored.warn(\"signal\");\n    }\n}\n// Set enumerable\nconst keys$1 = Object.getOwnPropertyNames(EventTarget.prototype);\nfor (let i = 0; i < keys$1.length; ++i) {\n    if (keys$1[i] === \"constructor\") {\n        continue;\n    }\n    Object.defineProperty(EventTarget.prototype, keys$1[i], { enumerable: true });\n}\n// Ensure `eventTarget instanceof window.EventTarget` is `true`.\nif (typeof Global !== \"undefined\" &&\n    typeof Global.EventTarget !== \"undefined\") {\n    Object.setPrototypeOf(EventTarget.prototype, Global.EventTarget.prototype);\n}\n\n/**\n * Get the current value of a given event attribute.\n * @param target The `EventTarget` object to get.\n * @param type The event type.\n */\nfunction getEventAttributeValue(target, type) {\n    var _a, _b;\n    const listMap = $$2(target, \"target\");\n    return (_b = (_a = listMap[type]) === null || _a === void 0 ? void 0 : _a.attrCallback) !== null && _b !== void 0 ? _b : null;\n}\n/**\n * Set an event listener to a given event attribute.\n * @param target The `EventTarget` object to set.\n * @param type The event type.\n * @param callback The event listener.\n */\nfunction setEventAttributeValue(target, type, callback) {\n    if (callback != null && typeof callback !== \"function\") {\n        InvalidAttributeHandler.warn(callback);\n    }\n    if (typeof callback === \"function\" ||\n        (typeof callback === \"object\" && callback !== null)) {\n        upsertEventAttributeListener(target, type, callback);\n    }\n    else {\n        removeEventAttributeListener(target, type);\n    }\n}\n//------------------------------------------------------------------------------\n// Helpers\n//------------------------------------------------------------------------------\n/**\n * Update or insert the given event attribute handler.\n * @param target The `EventTarget` object to set.\n * @param type The event type.\n * @param callback The event listener.\n */\nfunction upsertEventAttributeListener(target, type, callback) {\n    const list = ensureListenerList($$2(target, \"target\"), String(type));\n    list.attrCallback = callback;\n    if (list.attrListener == null) {\n        list.attrListener = addListener(list, defineEventAttributeCallback(list), false, false, false, undefined);\n    }\n}\n/**\n * Remove the given event attribute handler.\n * @param target The `EventTarget` object to remove.\n * @param type The event type.\n * @param callback The event listener.\n */\nfunction removeEventAttributeListener(target, type) {\n    const listMap = $$2(target, \"target\");\n    const list = listMap[String(type)];\n    if (list && list.attrListener) {\n        removeListener(list, list.attrListener.callback, false);\n        list.attrCallback = list.attrListener = undefined;\n    }\n}\n/**\n * Define the callback function for the given listener list object.\n * It calls `attrCallback` property if the property value is a function.\n * @param list The `ListenerList` object.\n */\nfunction defineEventAttributeCallback(list) {\n    return function (event) {\n        const callback = list.attrCallback;\n        if (typeof callback === \"function\") {\n            callback.call(this, event);\n        }\n    };\n}\n\n/**\n * Define an `EventTarget` class that has event attibutes.\n * @param types The types to define event attributes.\n * @deprecated Use `getEventAttributeValue`/`setEventAttributeValue` pair on your derived class instead because of static analysis friendly.\n */\nfunction defineCustomEventTarget(...types) {\n    class CustomEventTarget extends EventTarget {\n    }\n    for (let i = 0; i < types.length; ++i) {\n        defineEventAttribute(CustomEventTarget.prototype, types[i]);\n    }\n    return CustomEventTarget;\n}\n/**\n * Define an event attribute.\n * @param target The `EventTarget` object to define an event attribute.\n * @param type The event type to define.\n * @param _eventClass Unused, but to infer `Event` class type.\n * @deprecated Use `getEventAttributeValue`/`setEventAttributeValue` pair on your derived class instead because of static analysis friendly.\n */\nfunction defineEventAttribute(target, type, _eventClass) {\n    Object.defineProperty(target, `on${type}`, {\n        get() {\n            return getEventAttributeValue(this, type);\n        },\n        set(value) {\n            setEventAttributeValue(this, type, value);\n        },\n        configurable: true,\n        enumerable: true,\n    });\n}\n\nexport default EventTarget;\nexport { Event, EventTarget, defineCustomEventTarget, defineEventAttribute, getEventAttributeValue, setErrorHandler, setEventAttributeValue, setWarningHandler };\n//# sourceMappingURL=index.mjs.map\n"], "names": ["DOMException", "keys"], "mappings": ";AAMA,SAAS,WAAW,WAAW,YAAY,MAAM;AAC7C,MAAI,CAAC,WAAW;AACZ,UAAM,IAAI,UAAU,OAAO,SAAS,IAAI,CAAC;AAAA,EAC7C;AACJ;AAMA,SAAS,OAAO,SAAS,MAAM;AAC3B,MAAI,IAAI;AACR,SAAO,QAAQ,QAAQ,WAAW,MAAM,YAAY,KAAK,GAAG,CAAC,CAAC;AAClE;AAKA,SAAS,YAAY,GAAG;AACpB,MAAI,OAAO,MAAM,YAAY,MAAM,MAAM;AACrC,WAAO,OAAO,CAAC;AAAA,EACnB;AACA,SAAO,OAAO,UAAU,SAAS,KAAK,CAAC;AAC3C;AAEA,IAAI;AAaJ,SAAS,YAAY,YAAY;AAC7B,MAAI;AACA,UAAM,QAAQ,sBAAsB,QAC9B,aACA,IAAI,MAAM,YAAY,UAAU,CAAC;AAEvC,QAAI,oBAAqB;AAKzB,QAAI,OAAO,aAAA,eAAA,MAAkB,cACzB,OAAO,aAAA,YAAA,MAAe,YAAY;AAClC,mBAAA,eAAA,EAAc,IAAI,aAAA,YAAA,EAAW,SAAS,EAAE,OAAO,SAAS,MAAM,QAAO,CAAE,CAAC;AAAA,IAC5E,WAGS,OAAO,YAAY,eACxB,OAAO,QAAQ,SAAS,YAAY;AACpC,cAAQ,KAAK,qBAAqB,KAAK;AACvC;AAAA,IACJ;AAEA,YAAQ,MAAM,KAAK;AAAA,EACvB,SACO,IAAI;AAAA,EAEX;AACJ;AAMA,MAAM,SAAS,OAAO,aAAA,QAAA,MAAW,cAC3B,aAAA,QAAA,IACA,OAAO,aAAA,MAAA,MAAS,cACZ,aAAA,MAAA,IACA,OAAO,WAAW,cACd,SACA,OAAO,eAAe,cAClB,aACA;AAElB,IAAI;AAYJ,MAAM,QAAQ;AAAA,EACV,YAAY,MAAM,SAAS;AACvB,SAAK,OAAO;AACZ,SAAK,UAAU;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,MAAM;AACV,QAAI;AACJ,QAAI;AAEA,UAAI,mBAAoB;AAKxB,YAAM,UAAU,KAAK,IAAI,MAAK,EAAG,WAAW,QAAQ,OAAO,SAAS,KAAK,IAAI,QAAQ,mBAAmB,IAAI;AAC5G,cAAQ,KAAK,KAAK,SAAS,GAAG,MAAM,KAAK;AAAA,IAC7C,SACO,IAAI;AAAA,IAEX;AAAA,EACJ;AACJ;AAEA,MAAM,qCAAqC,IAAI,QAAQ,OAAO,+CAA+C;AAC7G,MAAM,iCAAiC,IAAI,QAAQ,OAAO,qEAAqE;AAC/H,MAAM,iCAAiC,IAAI,QAAQ,OAAO,qEAAqE;AAC/H,MAAM,gCAAgC,IAAI,QAAQ,OAAO,oDAAoD;AAC7G,MAAM,4BAA4B,IAAI,QAAQ,OAAO,oEAAoE;AACzH,MAAM,6BAA6B,IAAI,QAAQ,OAAO,0EAA0E;AAChI,MAAM,mBAAmB,IAAI,QAAQ,OAAO,0FAA0F;AACtI,MAAM,uBAAuB,IAAI,QAAQ,OAAO,2FAA2F;AAC3I,MAAM,0BAA0B,IAAI,QAAQ,OAAO,gDAAgD;AAQnG,MAAM,MAAM;AAAA;AAAA;AAAA;AAAA,EAIR,WAAW,OAAO;AACd,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,kBAAkB;AACzB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,YAAY;AACnB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,iBAAiB;AACxB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,MAAM,eAAe;AAC7B,WAAO,eAAe,MAAM,aAAa;AAAA,MACrC,OAAO;AAAA,MACP,YAAY;AAAA,IACxB,CAAS;AACD,UAAM,OAAO,kBAAkB,QAAQ,kBAAkB,SAAS,gBAAgB,CAAA;AAClF,oBAAgB,IAAI,MAAM;AAAA,MACtB,MAAM,OAAO,IAAI;AAAA,MACjB,SAAS,QAAQ,KAAK,OAAO;AAAA,MAC7B,YAAY,QAAQ,KAAK,UAAU;AAAA,MACnC,UAAU,QAAQ,KAAK,QAAQ;AAAA,MAC/B,QAAQ;AAAA,MACR,eAAe;AAAA,MACf,qBAAqB;AAAA,MACrB,8BAA8B;AAAA,MAC9B,cAAc;AAAA,MACd,uBAAuB;AAAA,MACvB,cAAc;AAAA,MACd,WAAW,KAAK,IAAG;AAAA,IAC/B,CAAS;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO;AACP,WAAO,EAAE,IAAI,EAAE;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SAAS;AACT,WAAO,EAAE,IAAI,EAAE;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,aAAa;AACb,WAAO,EAAE,IAAI,EAAE;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,gBAAgB;AAChB,WAAO,EAAE,IAAI,EAAE;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe;AACX,UAAM,gBAAgB,EAAE,IAAI,EAAE;AAC9B,QAAI,eAAe;AACf,aAAO,CAAC,aAAa;AAAA,IACzB;AACA,WAAO,CAAA;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AACP,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,kBAAkB;AAClB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,YAAY;AACZ,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,iBAAiB;AACjB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,aAAa;AACb,WAAO,EAAE,IAAI,EAAE,eAAe,IAAI;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AACd,MAAE,IAAI,EAAE,sBAAsB;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,eAAe;AACf,WAAO,EAAE,IAAI,EAAE;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,aAAa,OAAO;AACpB,QAAI,OAAO;AACP,QAAE,IAAI,EAAE,sBAAsB;AAAA,IAClC,OACK;AACD,qCAA+B,KAAI;AAAA,IACvC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,2BAA2B;AACvB,UAAM,OAAO,EAAE,IAAI;AACnB,SAAK,sBAAsB,KAAK,+BAA+B;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,UAAU;AACV,WAAO,EAAE,IAAI,EAAE;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,aAAa;AACb,WAAO,EAAE,IAAI,EAAE;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,cAAc;AACd,WAAO,CAAC,EAAE,IAAI,EAAE;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,YAAY,OAAO;AACnB,QAAI,CAAC,OAAO;AACR,oBAAc,EAAE,IAAI,CAAC;AAAA,IACzB,OACK;AACD,qCAA+B,KAAI;AAAA,IACvC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACb,kBAAc,EAAE,IAAI,CAAC;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,mBAAmB;AACnB,WAAO,EAAE,IAAI,EAAE;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,WAAW;AACX,WAAO,EAAE,IAAI,EAAE;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AACZ,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,YAAY;AACZ,WAAO,EAAE,IAAI,EAAE;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,MAAM,UAAU,OAAO,aAAa,OAAO;AACjD,UAAM,OAAO,EAAE,IAAI;AACnB,QAAI,KAAK,cAAc;AACnB,yCAAmC,KAAI;AACvC;AAAA,IACJ;AACA,oBAAgB,IAAI,MAAM;AAAA,MACtB,GAAG;AAAA,MACH,MAAM,OAAO,IAAI;AAAA,MACjB,SAAS,QAAQ,OAAO;AAAA,MACxB,YAAY,QAAQ,UAAU;AAAA,MAC9B,QAAQ;AAAA,MACR,eAAe;AAAA,MACf,qBAAqB;AAAA,MACrB,8BAA8B;AAAA,MAC9B,cAAc;AAAA,IAC1B,CAAS;AAAA,EACL;AACJ;AAIA,MAAM,OAAO;AACb,MAAM,kBAAkB;AACxB,MAAM,YAAY;AAClB,MAAM,iBAAiB;AAIvB,MAAM,kBAAkB,oBAAI,QAAO;AAOnC,SAAS,EAAE,OAAO,OAAO,QAAQ;AAC7B,QAAM,OAAO,gBAAgB,IAAI,KAAK;AACtC,aAAW,QAAQ,MAAM,kFAAkF,MAAM,KAAK;AACtH,SAAO;AACX;AAKA,SAAS,cAAc,MAAM;AACzB,MAAI,KAAK,uBAAuB;AAC5B,8BAA0B,KAAI;AAC9B;AAAA,EACJ;AACA,MAAI,CAAC,KAAK,YAAY;AAClB,kCAA8B,KAAI;AAClC;AAAA,EACJ;AACA,OAAK,eAAe;AACxB;AAEA,OAAO,eAAe,OAAO,QAAQ,EAAE,YAAY,KAAI,CAAE;AACzD,OAAO,eAAe,OAAO,mBAAmB,EAAE,YAAY,KAAI,CAAE;AACpE,OAAO,eAAe,OAAO,aAAa,EAAE,YAAY,KAAI,CAAE;AAC9D,OAAO,eAAe,OAAO,kBAAkB,EAAE,YAAY,KAAI,CAAE;AACnE,MAAM,OAAO,OAAO,oBAAoB,MAAM,SAAS;AACvD,SAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAClC,MAAI,KAAK,CAAC,MAAM,eAAe;AAC3B;AAAA,EACJ;AACA,SAAO,eAAe,MAAM,WAAW,KAAK,CAAC,GAAG,EAAE,YAAY,MAAM;AACxE;AAEA,IAAI,OAAO,WAAW,eAAe,OAAO,OAAO,UAAU,aAAa;AACtE,SAAO,eAAe,MAAM,WAAW,OAAO,MAAM,SAAS;AACjE;AAMA,SAAS,wBAAwB,SAAS;AACtC,MAAI,OAAO,cAAc;AACrB,WAAO,IAAI,OAAO,aAAa,SAAS,mBAAmB;AAAA,EAC/D;AACA,MAAI,gBAAgB,MAAM;AACtB,mBAAe,MAAMA,sBAAqB,MAAM;AAAA,MAC5C,YAAY,KAAK;AACb,cAAM,GAAG;AACT,YAAI,MAAM,mBAAmB;AACzB,gBAAM,kBAAkB,MAAMA,aAAY;AAAA,QAC9C;AAAA,MACJ;AAAA;AAAA,MAEA,IAAI,OAAO;AACP,eAAO;AAAA,MACX;AAAA;AAAA,MAEA,IAAI,OAAO;AACP,eAAO;AAAA,MACX;AAAA,IACZ;AACQ,WAAO,iBAAiB,aAAa,WAAW;AAAA,MAC5C,MAAM,EAAE,YAAY,KAAI;AAAA,MACxB,MAAM,EAAE,YAAY,KAAI;AAAA,IACpC,CAAS;AACD,8BAA0B,YAAY;AACtC,8BAA0B,aAAa,SAAS;AAAA,EACpD;AACA,SAAO,IAAI,aAAa,OAAO;AACnC;AAIA,IAAI;AACJ,MAAM,eAAe;AAAA,EACjB,gBAAgB;AAAA,EAChB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,qBAAqB;AAAA,EACrB,6BAA6B;AAAA,EAC7B,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,0BAA0B;AAAA,EAC1B,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,mBAAmB;AAAA,EACnB,cAAc;AAAA,EACd,aAAa;AAAA,EACb,WAAW;AAAA,EACX,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,aAAa;AAAA,EACb,uBAAuB;AAAA,EACvB,gBAAgB;AACpB;AACA,SAAS,0BAA0B,KAAK;AACpC,QAAMC,QAAO,OAAO,KAAK,YAAY;AACrC,WAAS,IAAI,GAAG,IAAIA,MAAK,QAAQ,EAAE,GAAG;AAClC,UAAM,MAAMA,MAAK,CAAC;AAClB,UAAM,QAAQ,aAAa,GAAG;AAC9B,WAAO,eAAe,KAAK,KAAK;AAAA,MAC5B,MAAM;AACF,eAAO;AAAA,MACX;AAAA,MACA,cAAc;AAAA,MACd,YAAY;AAAA,IACxB,CAAS;AAAA,EACL;AACJ;AAOA,MAAM,qBAAqB,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7B,OAAO,KAAK,OAAO;AACf,WAAO,KAAK,kBAAkB,KAAK,GAAG,KAAK;AAAA,EAC/C;AAAA,EACA,YAAY,OAAO;AACf,UAAM,MAAM,MAAM;AAAA,MACd,SAAS,MAAM;AAAA,MACf,YAAY,MAAM;AAAA,MAClB,UAAU,MAAM;AAAA,IAC5B,CAAS;AACD,QAAI,MAAM,cAAc;AACpB,YAAM,gBAAe;AAAA,IACzB;AACA,QAAI,MAAM,kBAAkB;AACxB,YAAM,eAAc;AAAA,IACxB;AACA,sBAAkB,IAAI,MAAM,EAAE,UAAU,MAAK,CAAE;AAE/C,UAAMA,QAAO,OAAO,KAAK,KAAK;AAC9B,aAAS,IAAI,GAAG,IAAIA,MAAK,QAAQ,EAAE,GAAG;AAClC,YAAM,MAAMA,MAAK,CAAC;AAClB,UAAI,EAAE,OAAO,OAAO;AAChB,eAAO,eAAe,MAAM,KAAK,yBAAyB,OAAO,GAAG,CAAC;AAAA,MACzE;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,kBAAkB;AACd,UAAM,gBAAe;AACrB,UAAM,EAAE,SAAQ,IAAK,IAAI,IAAI;AAC7B,QAAI,qBAAqB,UAAU;AAC/B,eAAS,gBAAe;AAAA,IAC5B;AAAA,EACJ;AAAA,EACA,IAAI,eAAe;AACf,WAAO,MAAM;AAAA,EACjB;AAAA,EACA,IAAI,aAAa,OAAO;AACpB,UAAM,eAAe;AACrB,UAAM,EAAE,SAAQ,IAAK,IAAI,IAAI;AAC7B,QAAI,kBAAkB,UAAU;AAC5B,eAAS,eAAe;AAAA,IAC5B;AAAA,EACJ;AAAA,EACA,2BAA2B;AACvB,UAAM,yBAAwB;AAC9B,UAAM,EAAE,SAAQ,IAAK,IAAI,IAAI;AAC7B,QAAI,8BAA8B,UAAU;AACxC,eAAS,yBAAwB;AAAA,IACrC;AAAA,EACJ;AAAA,EACA,IAAI,cAAc;AACd,WAAO,MAAM;AAAA,EACjB;AAAA,EACA,IAAI,YAAY,OAAO;AACnB,UAAM,cAAc;AACpB,UAAM,EAAE,SAAQ,IAAK,IAAI,IAAI;AAC7B,QAAI,iBAAiB,UAAU;AAC3B,eAAS,cAAc;AAAA,IAC3B;AAAA,EACJ;AAAA,EACA,iBAAiB;AACb,UAAM,eAAc;AACpB,UAAM,EAAE,SAAQ,IAAK,IAAI,IAAI;AAC7B,QAAI,oBAAoB,UAAU;AAC9B,eAAS,eAAc;AAAA,IAC3B;AAAA,EACJ;AAAA,EACA,IAAI,YAAY;AACZ,UAAM,EAAE,SAAQ,IAAK,IAAI,IAAI;AAC7B,QAAI,eAAe,UAAU;AACzB,aAAO,SAAS;AAAA,IACpB;AACA,WAAO,MAAM;AAAA,EACjB;AACJ;AAIA,MAAM,oBAAoB,oBAAI,QAAO;AAMrC,SAAS,IAAI,OAAO;AAChB,QAAM,OAAO,kBAAkB,IAAI,KAAK;AACxC,aAAW,QAAQ,MAAM,+CAA+C,KAAK;AAC7E,SAAO;AACX;AAMA,MAAM,oBAAoB,oBAAI,QAAO;AAErC,kBAAkB,IAAI,OAAO,WAAW,YAAY;AACpD,IAAI,OAAO,WAAW,eAAe,OAAO,OAAO,UAAU,aAAa;AACtE,oBAAkB,IAAI,OAAO,MAAM,WAAW,YAAY;AAC9D;AAKA,SAAS,kBAAkB,eAAe;AACtC,QAAM,YAAY,OAAO,eAAe,aAAa;AACrD,MAAI,aAAa,MAAM;AACnB,WAAO;AAAA,EACX;AACA,MAAI,UAAU,kBAAkB,IAAI,SAAS;AAC7C,MAAI,WAAW,MAAM;AACjB,cAAU,cAAc,kBAAkB,SAAS,GAAG,SAAS;AAC/D,sBAAkB,IAAI,WAAW,OAAO;AAAA,EAC5C;AACA,SAAO;AACX;AAMA,SAAS,cAAc,kBAAkB,mBAAmB;AAAA,EACxD,MAAM,2BAA2B,iBAAiB;AAAA,EACtD;AACI,QAAMA,QAAO,OAAO,KAAK,iBAAiB;AAC1C,WAAS,IAAI,GAAG,IAAIA,MAAK,QAAQ,EAAE,GAAG;AAClC,WAAO,eAAe,mBAAmB,WAAWA,MAAK,CAAC,GAAG,yBAAyB,mBAAmBA,MAAK,CAAC,CAAC,CAAC;AAAA,EACrH;AACA,SAAO;AACX;AAIA,SAAS,yBAAyB,KAAK,KAAK;AACxC,QAAM,IAAI,OAAO,yBAAyB,KAAK,GAAG;AAClD,SAAO;AAAA,IACH,MAAM;AACF,YAAM,WAAW,IAAI,IAAI,EAAE;AAC3B,YAAM,QAAQ,SAAS,GAAG;AAC1B,UAAI,OAAO,UAAU,YAAY;AAC7B,eAAO,MAAM,KAAK,QAAQ;AAAA,MAC9B;AACA,aAAO;AAAA,IACX;AAAA,IACA,IAAI,OAAO;AACP,YAAM,WAAW,IAAI,IAAI,EAAE;AAC3B,eAAS,GAAG,IAAI;AAAA,IACpB;AAAA,IACA,cAAc,EAAE;AAAA,IAChB,YAAY,EAAE;AAAA,EACtB;AACA;AAWA,SAAS,eAAe,UAAU,SAAS,SAAS,MAAM,QAAQ,gBAAgB;AAC9E,SAAO;AAAA,IACH;AAAA,IACA,QAAQ,UAAU,IAAkB,MAC/B,UAAU,IAAkB,MAC5B,OAAO,IAAe;AAAA,IAC3B;AAAA,IACA;AAAA,EACR;AACA;AAKA,SAAS,WAAW,UAAU;AAC1B,WAAS,SAAS;AACtB;AAKA,SAAS,UAAU,UAAU;AACzB,UAAQ,SAAS,QAAQ,OAAqB;AAClD;AAKA,SAAS,UAAU,UAAU;AACzB,UAAQ,SAAS,QAAQ,OAAqB;AAClD;AAKA,SAAS,OAAO,UAAU;AACtB,UAAQ,SAAS,QAAQ,OAAkB;AAC/C;AAKA,SAAS,UAAU,UAAU;AACzB,UAAQ,SAAS,QAAQ,OAAqB;AAClD;AAQA,SAAS,eAAe,EAAE,YAAY,QAAQ,OAAO;AACjD,MAAI;AACA,QAAI,OAAO,aAAa,YAAY;AAChC,eAAS,KAAK,QAAQ,KAAK;AAAA,IAC/B,WACS,OAAO,SAAS,gBAAgB,YAAY;AACjD,eAAS,YAAY,KAAK;AAAA,IAC9B;AAAA,EACJ,SACO,aAAa;AAChB,gBAAY,WAAW;AAAA,EAC3B;AACJ;AASA,SAAS,oBAAoB,EAAE,aAAa,UAAU,SAAS;AAC3D,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,EAAE,GAAG;AACvC,QAAI,UAAU,CAAC,EAAE,aAAa,YAC1B,UAAU,UAAU,CAAC,CAAC,MAAM,SAAS;AACrC,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAWA,SAAS,YAAY,MAAM,UAAU,SAAS,SAAS,MAAM,QAAQ;AACjE,MAAI;AACJ,MAAI,QAAQ;AACR,qBAAiB,eAAe,KAAK,MAAM,MAAM,UAAU,OAAO;AAClE,WAAO,iBAAiB,SAAS,cAAc;AAAA,EACnD;AACA,QAAM,WAAW,eAAe,UAAU,SAAS,SAAS,MAAM,QAAQ,cAAc;AACxF,MAAI,KAAK,KAAK;AACV,SAAK,MAAM;AACX,SAAK,YAAY,CAAC,GAAG,KAAK,WAAW,QAAQ;AAAA,EACjD,OACK;AACD,SAAK,UAAU,KAAK,QAAQ;AAAA,EAChC;AACA,SAAO;AACX;AAQA,SAAS,eAAe,MAAM,UAAU,SAAS;AAC7C,QAAM,QAAQ,oBAAoB,MAAM,UAAU,OAAO;AACzD,MAAI,UAAU,IAAI;AACd,WAAO,iBAAiB,MAAM,KAAK;AAAA,EACvC;AACA,SAAO;AACX;AAQA,SAAS,iBAAiB,MAAM,OAAO,aAAa,OAAO;AACvD,QAAM,WAAW,KAAK,UAAU,KAAK;AAErC,aAAW,QAAQ;AAEnB,MAAI,SAAS,QAAQ;AACjB,aAAS,OAAO,oBAAoB,SAAS,SAAS,cAAc;AAAA,EACxE;AAEA,MAAI,KAAK,OAAO,CAAC,YAAY;AACzB,SAAK,MAAM;AACX,SAAK,YAAY,KAAK,UAAU,OAAO,CAAC,GAAG,MAAM,MAAM,KAAK;AAC5D,WAAO;AAAA,EACX;AACA,OAAK,UAAU,OAAO,OAAO,CAAC;AAC9B,SAAO;AACX;AAKA,SAAS,wBAAwB;AAC7B,SAAO,uBAAO,OAAO,IAAI;AAC7B;AAOA,SAAS,mBAAmB,aAAa,MAAM;AAC3C,MAAI;AACJ,UAAS,KAAK,YAAY,IAAI,OAAO,QAAQ,OAAO,SAAS,KAAM,YAAY,IAAI,IAAI;AAAA,IACnF,cAAc;AAAA,IACd,cAAc;AAAA,IACd,KAAK;AAAA,IACL,WAAW,CAAA;AAAA,EACnB;AACA;AAMA,MAAM,YAAY;AAAA;AAAA;AAAA;AAAA,EAId,cAAc;AACV,sBAAkB,IAAI,MAAM,uBAAuB;AAAA,EACvD;AAAA;AAAA,EAEA,iBAAiB,OAAO,WAAW,UAAU;AACzC,UAAM,cAAc,IAAI,IAAI;AAC5B,UAAM,EAAE,UAAU,SAAS,MAAM,SAAS,QAAQ,KAAI,IAAM,oBAAoB,OAAO,WAAW,QAAQ;AAC1G,QAAI,YAAY,SAAS,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,UAAU;AACtF;AAAA,IACJ;AACA,UAAM,OAAO,mBAAmB,aAAa,IAAI;AAEjD,UAAM,IAAI,oBAAoB,MAAM,UAAU,OAAO;AACrD,QAAI,MAAM,IAAI;AACV,oBAAc,KAAK,UAAU,CAAC,GAAG,SAAS,MAAM,MAAM;AACtD;AAAA,IACJ;AAEA,gBAAY,MAAM,UAAU,SAAS,SAAS,MAAM,MAAM;AAAA,EAC9D;AAAA;AAAA,EAEA,oBAAoB,OAAO,WAAW,UAAU;AAC5C,UAAM,cAAc,IAAI,IAAI;AAC5B,UAAM,EAAE,UAAU,SAAS,KAAI,IAAK,iBAAiB,OAAO,WAAW,QAAQ;AAC/E,UAAM,OAAO,YAAY,IAAI;AAC7B,QAAI,YAAY,QAAQ,MAAM;AAC1B,qBAAe,MAAM,UAAU,OAAO;AAAA,IAC1C;AAAA,EACJ;AAAA;AAAA,EAEA,cAAc,GAAG;AACb,UAAM,OAAO,IAAI,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC;AACrC,QAAI,QAAQ,MAAM;AACd,aAAO;AAAA,IACX;AACA,UAAM,QAAQ,aAAa,QAAQ,IAAI,aAAa,KAAK,CAAC;AAC1D,UAAM,YAAY,EAAE,OAAO,OAAO;AAClC,QAAI,UAAU,cAAc;AACxB,YAAM,wBAAwB,qCAAqC;AAAA,IACvE;AACA,cAAU,eAAe;AACzB,cAAU,SAAS,UAAU,gBAAgB;AAC7C,QAAI,CAAC,UAAU,qBAAqB;AAChC,YAAM,EAAE,KAAK,UAAS,IAAK;AAE3B,WAAK,MAAM;AAEX,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,EAAE,GAAG;AACvC,cAAM,WAAW,UAAU,CAAC;AAE5B,YAAI,UAAU,QAAQ,GAAG;AACrB;AAAA,QACJ;AAEA,YAAI,OAAO,QAAQ,KAAK,iBAAiB,MAAM,GAAG,CAAC,GAAG,GAAG;AAGrD,eAAK;AAAA,QACT;AAEA,kBAAU,wBAAwB,UAAU,QAAQ;AACpD,uBAAe,UAAU,MAAM,KAAK;AACpC,kBAAU,wBAAwB;AAElC,YAAI,UAAU,8BAA8B;AACxC;AAAA,QACJ;AAAA,MACJ;AAEA,UAAI,CAAC,KAAK;AACN,aAAK,MAAM;AAAA,MACf;AAAA,IACJ;AACA,cAAU,SAAS;AACnB,cAAU,gBAAgB;AAC1B,cAAU,+BAA+B;AACzC,cAAU,sBAAsB;AAChC,cAAU,eAAe;AACzB,WAAO,CAAC,UAAU;AAAA,EACtB;AACJ;AAIA,MAAM,oBAAoB,oBAAI,QAAO;AAOrC,SAAS,IAAI,QAAQ,OAAO,QAAQ;AAChC,QAAM,OAAO,kBAAkB,IAAI,MAAM;AACzC,aAAW,QAAQ,MAAM,wFAAwF,MAAM,MAAM;AAC7H,SAAO;AACX;AAKA,SAAS,oBAAoB,MAAM,UAAU,SAAS;AAClD,MAAI;AACJ,iBAAe,QAAQ;AACvB,MAAI,OAAO,YAAY,YAAY,YAAY,MAAM;AACjD,WAAO;AAAA,MACH,MAAM,OAAO,IAAI;AAAA,MACjB,UAAU,aAAa,QAAQ,aAAa,SAAS,WAAW;AAAA,MAChE,SAAS,QAAQ,QAAQ,OAAO;AAAA,MAChC,SAAS,QAAQ,QAAQ,OAAO;AAAA,MAChC,MAAM,QAAQ,QAAQ,IAAI;AAAA,MAC1B,SAAS,KAAK,QAAQ,YAAY,QAAQ,OAAO,SAAS,KAAK;AAAA,IAC3E;AAAA,EACI;AACA,SAAO;AAAA,IACH,MAAM,OAAO,IAAI;AAAA,IACjB,UAAU,aAAa,QAAQ,aAAa,SAAS,WAAW;AAAA,IAChE,SAAS,QAAQ,OAAO;AAAA,IACxB,SAAS;AAAA,IACT,MAAM;AAAA,IACN,QAAQ;AAAA,EAChB;AACA;AAKA,SAAS,iBAAiB,MAAM,UAAU,SAAS;AAC/C,iBAAe,QAAQ;AACvB,MAAI,OAAO,YAAY,YAAY,YAAY,MAAM;AACjD,WAAO;AAAA,MACH,MAAM,OAAO,IAAI;AAAA,MACjB,UAAU,aAAa,QAAQ,aAAa,SAAS,WAAW;AAAA,MAChE,SAAS,QAAQ,QAAQ,OAAO;AAAA,IAC5C;AAAA,EACI;AACA,SAAO;AAAA,IACH,MAAM,OAAO,IAAI;AAAA,IACjB,UAAU,aAAa,QAAQ,aAAa,SAAS,WAAW;AAAA,IAChE,SAAS,QAAQ,OAAO;AAAA,EAChC;AACA;AAKA,SAAS,eAAe,UAAU;AAC9B,MAAI,OAAO,aAAa,cACnB,OAAO,aAAa,YACjB,aAAa,QACb,OAAO,SAAS,gBAAgB,YAAa;AACjD;AAAA,EACJ;AACA,MAAI,YAAY,QAAQ,OAAO,aAAa,UAAU;AAClD,yBAAqB,KAAK,QAAQ;AAClC;AAAA,EACJ;AACA,QAAM,IAAI,UAAU,OAAO,qBAAqB,SAAS,CAAC,QAAQ,CAAC,CAAC;AACxE;AAQA,SAAS,cAAc,UAAU,SAAS,MAAM,QAAQ;AACpD,6BAA2B,KAAK,UAAU,QAAQ,IAAI,YAAY,UAAU,SAAS,QAAQ;AAC7F,MAAI,UAAU,QAAQ,MAAM,SAAS;AACjC,qBAAiB,KAAK,SAAS;AAAA,EACnC;AACA,MAAI,OAAO,QAAQ,MAAM,MAAM;AAC3B,qBAAiB,KAAK,MAAM;AAAA,EAChC;AACA,MAAI,SAAS,WAAW,QAAQ;AAC5B,qBAAiB,KAAK,QAAQ;AAAA,EAClC;AACJ;AAEA,MAAM,SAAS,OAAO,oBAAoB,YAAY,SAAS;AAC/D,SAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACpC,MAAI,OAAO,CAAC,MAAM,eAAe;AAC7B;AAAA,EACJ;AACA,SAAO,eAAe,YAAY,WAAW,OAAO,CAAC,GAAG,EAAE,YAAY,MAAM;AAChF;AAEA,IAAI,OAAO,WAAW,eAClB,OAAO,OAAO,gBAAgB,aAAa;AAC3C,SAAO,eAAe,YAAY,WAAW,OAAO,YAAY,SAAS;AAC7E;AAkBA,SAAS,uBAAuB,QAAQ,MAAM,UAAU;AACpD,MAAI,YAAY,QAAQ,OAAO,aAAa,YAAY;AACpD,4BAAwB,KAAK,QAAQ;AAAA,EACzC;AACA,MAAI,OAAO,aAAa,cACnB,OAAO,aAAa,YAAY,aAAa,MAAO;AACrD,iCAA6B,QAAQ,MAAM,QAAQ;AAAA,EACvD,OACK;AACD,iCAA6B,QAAQ,IAAI;AAAA,EAC7C;AACJ;AAUA,SAAS,6BAA6B,QAAQ,MAAM,UAAU;AAC1D,QAAM,OAAO,mBAAmB,IAAI,QAAQ,QAAQ,GAAG,OAAO,IAAI,CAAC;AACnE,OAAK,eAAe;AACpB,MAAI,KAAK,gBAAgB,MAAM;AAC3B,SAAK,eAAe,YAAY,MAAM,6BAA6B,IAAI,GAAG,OAAO,OAAO,OAAO,MAAS;AAAA,EAC5G;AACJ;AAOA,SAAS,6BAA6B,QAAQ,MAAM;AAChD,QAAM,UAAU,IAAI,QAAQ,QAAQ;AACpC,QAAM,OAAO,QAAQ,OAAO,IAAI,CAAC;AACjC,MAAI,QAAQ,KAAK,cAAc;AAC3B,mBAAe,MAAM,KAAK,aAAa,UAAU,KAAK;AACtD,SAAK,eAAe,KAAK,eAAe;AAAA,EAC5C;AACJ;AAMA,SAAS,6BAA6B,MAAM;AACxC,SAAO,SAAU,OAAO;AACpB,UAAM,WAAW,KAAK;AACtB,QAAI,OAAO,aAAa,YAAY;AAChC,eAAS,KAAK,MAAM,KAAK;AAAA,IAC7B;AAAA,EACJ;AACJ;;;;", "x_google_ignoreList": [0]}