/**
 * @license
 * Copyright 2010-2024 Three.js Authors
 * SPDX-License-Identifier: MIT
 */
const t="172",e={LEFT:0,MIDDLE:1,RIGHT:2,ROTATE:0,DOLLY:1,PAN:2},s={ROTATE:0,PAN:1,DOLLY_PAN:2,DOLLY_ROTATE:3},i=0,r=1,n=2,o=3,a=0,h=1,l=2,c=3,u=0,d=1,p=2,m=0,y=1,f=2,g=3,x=4,b=5,v=100,w=101,M=102,S=103,_=104,A=200,T=201,z=202,C=203,I=204,B=205,k=206,R=207,E=208,P=209,O=210,F=211,N=212,L=213,V=214,W=0,j=1,U=2,D=3,H=4,q=5,J=6,X=7,Y=0,Z=1,G=2,$=0,Q=1,K=2,tt=3,et=4,st=5,it=6,rt=7,nt="attached",ot="detached",at=300,ht=301,lt=302,ct=303,ut=304,dt=306,pt=1e3,mt=1001,yt=1002,ft=1003,gt=1004,xt=1004,bt=1005,vt=1005,wt=1006,Mt=1007,St=1007,_t=1008,At=1008,Tt=1009,zt=1010,Ct=1011,It=1012,Bt=1013,kt=1014,Rt=1015,Et=1016,Pt=1017,Ot=1018,Ft=1020,Nt=35902,Lt=1021,Vt=1022,Wt=1023,jt=1024,Ut=1025,Dt=1026,Ht=1027,qt=1028,Jt=1029,Xt=1030,Yt=1031,Zt=1032,Gt=1033,$t=33776,Qt=33777,Kt=33778,te=33779,ee=35840,se=35841,ie=35842,re=35843,ne=36196,oe=37492,ae=37496,he=37808,le=37809,ce=37810,ue=37811,de=37812,pe=37813,me=37814,ye=37815,fe=37816,ge=37817,xe=37818,be=37819,ve=37820,we=37821,Me=36492,Se=36494,_e=36495,Ae=36283,Te=36284,ze=36285,Ce=36286,Ie=2200,Be=2201,ke=2202,Re=2300,Ee=2301,Pe=2302,Oe=2400,Fe=2401,Ne=2402,Le=2500,Ve=2501,We=0,je=1,Ue=2,De=3200,He=3201,qe=3202,Je=3203,Xe=0,Ye=1,Ze="",Ge="srgb",$e="srgb-linear",Qe="linear",Ke="srgb",ts=0,es=7680,ss=7681,is=7682,rs=7683,ns=34055,os=34056,as=5386,hs=512,ls=513,cs=514,us=515,ds=516,ps=517,ms=518,ys=519,fs=512,gs=513,xs=514,bs=515,vs=516,ws=517,Ms=518,Ss=519,_s=35044,As=35048,Ts=35040,zs=35045,Cs=35049,Is=35041,Bs=35046,ks=35050,Rs=35042,Es="100",Ps="300 es",Os=2e3,Fs=2001;class Ns{addEventListener(t,e){void 0===this._listeners&&(this._listeners={});const s=this._listeners;void 0===s[t]&&(s[t]=[]),-1===s[t].indexOf(e)&&s[t].push(e)}hasEventListener(t,e){if(void 0===this._listeners)return!1;const s=this._listeners;return void 0!==s[t]&&-1!==s[t].indexOf(e)}removeEventListener(t,e){if(void 0===this._listeners)return;const s=this._listeners[t];if(void 0!==s){const t=s.indexOf(e);-1!==t&&s.splice(t,1)}}dispatchEvent(t){if(void 0===this._listeners)return;const e=this._listeners[t.type];if(void 0!==e){t.target=this;const s=e.slice(0);for(let e=0,i=s.length;e<i;e++)s[e].call(this,t);t.target=null}}}const Ls=["00","01","02","03","04","05","06","07","08","09","0a","0b","0c","0d","0e","0f","10","11","12","13","14","15","16","17","18","19","1a","1b","1c","1d","1e","1f","20","21","22","23","24","25","26","27","28","29","2a","2b","2c","2d","2e","2f","30","31","32","33","34","35","36","37","38","39","3a","3b","3c","3d","3e","3f","40","41","42","43","44","45","46","47","48","49","4a","4b","4c","4d","4e","4f","50","51","52","53","54","55","56","57","58","59","5a","5b","5c","5d","5e","5f","60","61","62","63","64","65","66","67","68","69","6a","6b","6c","6d","6e","6f","70","71","72","73","74","75","76","77","78","79","7a","7b","7c","7d","7e","7f","80","81","82","83","84","85","86","87","88","89","8a","8b","8c","8d","8e","8f","90","91","92","93","94","95","96","97","98","99","9a","9b","9c","9d","9e","9f","a0","a1","a2","a3","a4","a5","a6","a7","a8","a9","aa","ab","ac","ad","ae","af","b0","b1","b2","b3","b4","b5","b6","b7","b8","b9","ba","bb","bc","bd","be","bf","c0","c1","c2","c3","c4","c5","c6","c7","c8","c9","ca","cb","cc","cd","ce","cf","d0","d1","d2","d3","d4","d5","d6","d7","d8","d9","da","db","dc","dd","de","df","e0","e1","e2","e3","e4","e5","e6","e7","e8","e9","ea","eb","ec","ed","ee","ef","f0","f1","f2","f3","f4","f5","f6","f7","f8","f9","fa","fb","fc","fd","fe","ff"];let Vs=1234567;const Ws=Math.PI/180,js=180/Math.PI;function Us(){const t=4294967295*Math.random()|0,e=4294967295*Math.random()|0,s=4294967295*Math.random()|0,i=4294967295*Math.random()|0;return(Ls[255&t]+Ls[t>>8&255]+Ls[t>>16&255]+Ls[t>>24&255]+"-"+Ls[255&e]+Ls[e>>8&255]+"-"+Ls[e>>16&15|64]+Ls[e>>24&255]+"-"+Ls[63&s|128]+Ls[s>>8&255]+"-"+Ls[s>>16&255]+Ls[s>>24&255]+Ls[255&i]+Ls[i>>8&255]+Ls[i>>16&255]+Ls[i>>24&255]).toLowerCase()}function Ds(t,e,s){return Math.max(e,Math.min(s,t))}function Hs(t,e){return(t%e+e)%e}function qs(t,e,s){return(1-s)*t+s*e}function Js(t,e){switch(e.constructor){case Float32Array:return t;case Uint32Array:return t/4294967295;case Uint16Array:return t/65535;case Uint8Array:return t/255;case Int32Array:return Math.max(t/2147483647,-1);case Int16Array:return Math.max(t/32767,-1);case Int8Array:return Math.max(t/127,-1);default:throw new Error("Invalid component type.")}}function Xs(t,e){switch(e.constructor){case Float32Array:return t;case Uint32Array:return Math.round(4294967295*t);case Uint16Array:return Math.round(65535*t);case Uint8Array:return Math.round(255*t);case Int32Array:return Math.round(2147483647*t);case Int16Array:return Math.round(32767*t);case Int8Array:return Math.round(127*t);default:throw new Error("Invalid component type.")}}const Ys={DEG2RAD:Ws,RAD2DEG:js,generateUUID:Us,clamp:Ds,euclideanModulo:Hs,mapLinear:function(t,e,s,i,r){return i+(t-e)*(r-i)/(s-e)},inverseLerp:function(t,e,s){return t!==e?(s-t)/(e-t):0},lerp:qs,damp:function(t,e,s,i){return qs(t,e,1-Math.exp(-s*i))},pingpong:function(t,e=1){return e-Math.abs(Hs(t,2*e)-e)},smoothstep:function(t,e,s){return t<=e?0:t>=s?1:(t=(t-e)/(s-e))*t*(3-2*t)},smootherstep:function(t,e,s){return t<=e?0:t>=s?1:(t=(t-e)/(s-e))*t*t*(t*(6*t-15)+10)},randInt:function(t,e){return t+Math.floor(Math.random()*(e-t+1))},randFloat:function(t,e){return t+Math.random()*(e-t)},randFloatSpread:function(t){return t*(.5-Math.random())},seededRandom:function(t){void 0!==t&&(Vs=t);let e=Vs+=1831565813;return e=Math.imul(e^e>>>15,1|e),e^=e+Math.imul(e^e>>>7,61|e),((e^e>>>14)>>>0)/4294967296},degToRad:function(t){return t*Ws},radToDeg:function(t){return t*js},isPowerOfTwo:function(t){return!(t&t-1)&&0!==t},ceilPowerOfTwo:function(t){return Math.pow(2,Math.ceil(Math.log(t)/Math.LN2))},floorPowerOfTwo:function(t){return Math.pow(2,Math.floor(Math.log(t)/Math.LN2))},setQuaternionFromProperEuler:function(t,e,s,i,r){const n=Math.cos,o=Math.sin,a=n(s/2),h=o(s/2),l=n((e+i)/2),c=o((e+i)/2),u=n((e-i)/2),d=o((e-i)/2),p=n((i-e)/2),m=o((i-e)/2);switch(r){case"XYX":t.set(a*c,h*u,h*d,a*l);break;case"YZY":t.set(h*d,a*c,h*u,a*l);break;case"ZXZ":t.set(h*u,h*d,a*c,a*l);break;case"XZX":t.set(a*c,h*m,h*p,a*l);break;case"YXY":t.set(h*p,a*c,h*m,a*l);break;case"ZYZ":t.set(h*m,h*p,a*c,a*l);break;default:console.warn("THREE.MathUtils: .setQuaternionFromProperEuler() encountered an unknown order: "+r)}},normalize:Xs,denormalize:Js};class Zs{constructor(t=0,e=0){Zs.prototype.isVector2=!0,this.x=t,this.y=e}get width(){return this.x}set width(t){this.x=t}get height(){return this.y}set height(t){this.y=t}set(t,e){return this.x=t,this.y=e,this}setScalar(t){return this.x=t,this.y=t,this}setX(t){return this.x=t,this}setY(t){return this.y=t,this}setComponent(t,e){switch(t){case 0:this.x=e;break;case 1:this.y=e;break;default:throw new Error("index is out of range: "+t)}return this}getComponent(t){switch(t){case 0:return this.x;case 1:return this.y;default:throw new Error("index is out of range: "+t)}}clone(){return new this.constructor(this.x,this.y)}copy(t){return this.x=t.x,this.y=t.y,this}add(t){return this.x+=t.x,this.y+=t.y,this}addScalar(t){return this.x+=t,this.y+=t,this}addVectors(t,e){return this.x=t.x+e.x,this.y=t.y+e.y,this}addScaledVector(t,e){return this.x+=t.x*e,this.y+=t.y*e,this}sub(t){return this.x-=t.x,this.y-=t.y,this}subScalar(t){return this.x-=t,this.y-=t,this}subVectors(t,e){return this.x=t.x-e.x,this.y=t.y-e.y,this}multiply(t){return this.x*=t.x,this.y*=t.y,this}multiplyScalar(t){return this.x*=t,this.y*=t,this}divide(t){return this.x/=t.x,this.y/=t.y,this}divideScalar(t){return this.multiplyScalar(1/t)}applyMatrix3(t){const e=this.x,s=this.y,i=t.elements;return this.x=i[0]*e+i[3]*s+i[6],this.y=i[1]*e+i[4]*s+i[7],this}min(t){return this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this}max(t){return this.x=Math.max(this.x,t.x),this.y=Math.max(this.y,t.y),this}clamp(t,e){return this.x=Ds(this.x,t.x,e.x),this.y=Ds(this.y,t.y,e.y),this}clampScalar(t,e){return this.x=Ds(this.x,t,e),this.y=Ds(this.y,t,e),this}clampLength(t,e){const s=this.length();return this.divideScalar(s||1).multiplyScalar(Ds(s,t,e))}floor(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this}ceil(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this}round(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this}roundToZero(){return this.x=Math.trunc(this.x),this.y=Math.trunc(this.y),this}negate(){return this.x=-this.x,this.y=-this.y,this}dot(t){return this.x*t.x+this.y*t.y}cross(t){return this.x*t.y-this.y*t.x}lengthSq(){return this.x*this.x+this.y*this.y}length(){return Math.sqrt(this.x*this.x+this.y*this.y)}manhattanLength(){return Math.abs(this.x)+Math.abs(this.y)}normalize(){return this.divideScalar(this.length()||1)}angle(){return Math.atan2(-this.y,-this.x)+Math.PI}angleTo(t){const e=Math.sqrt(this.lengthSq()*t.lengthSq());if(0===e)return Math.PI/2;const s=this.dot(t)/e;return Math.acos(Ds(s,-1,1))}distanceTo(t){return Math.sqrt(this.distanceToSquared(t))}distanceToSquared(t){const e=this.x-t.x,s=this.y-t.y;return e*e+s*s}manhattanDistanceTo(t){return Math.abs(this.x-t.x)+Math.abs(this.y-t.y)}setLength(t){return this.normalize().multiplyScalar(t)}lerp(t,e){return this.x+=(t.x-this.x)*e,this.y+=(t.y-this.y)*e,this}lerpVectors(t,e,s){return this.x=t.x+(e.x-t.x)*s,this.y=t.y+(e.y-t.y)*s,this}equals(t){return t.x===this.x&&t.y===this.y}fromArray(t,e=0){return this.x=t[e],this.y=t[e+1],this}toArray(t=[],e=0){return t[e]=this.x,t[e+1]=this.y,t}fromBufferAttribute(t,e){return this.x=t.getX(e),this.y=t.getY(e),this}rotateAround(t,e){const s=Math.cos(e),i=Math.sin(e),r=this.x-t.x,n=this.y-t.y;return this.x=r*s-n*i+t.x,this.y=r*i+n*s+t.y,this}random(){return this.x=Math.random(),this.y=Math.random(),this}*[Symbol.iterator](){yield this.x,yield this.y}}class Gs{constructor(t,e,s,i,r,n,o,a,h){Gs.prototype.isMatrix3=!0,this.elements=[1,0,0,0,1,0,0,0,1],void 0!==t&&this.set(t,e,s,i,r,n,o,a,h)}set(t,e,s,i,r,n,o,a,h){const l=this.elements;return l[0]=t,l[1]=i,l[2]=o,l[3]=e,l[4]=r,l[5]=a,l[6]=s,l[7]=n,l[8]=h,this}identity(){return this.set(1,0,0,0,1,0,0,0,1),this}copy(t){const e=this.elements,s=t.elements;return e[0]=s[0],e[1]=s[1],e[2]=s[2],e[3]=s[3],e[4]=s[4],e[5]=s[5],e[6]=s[6],e[7]=s[7],e[8]=s[8],this}extractBasis(t,e,s){return t.setFromMatrix3Column(this,0),e.setFromMatrix3Column(this,1),s.setFromMatrix3Column(this,2),this}setFromMatrix4(t){const e=t.elements;return this.set(e[0],e[4],e[8],e[1],e[5],e[9],e[2],e[6],e[10]),this}multiply(t){return this.multiplyMatrices(this,t)}premultiply(t){return this.multiplyMatrices(t,this)}multiplyMatrices(t,e){const s=t.elements,i=e.elements,r=this.elements,n=s[0],o=s[3],a=s[6],h=s[1],l=s[4],c=s[7],u=s[2],d=s[5],p=s[8],m=i[0],y=i[3],f=i[6],g=i[1],x=i[4],b=i[7],v=i[2],w=i[5],M=i[8];return r[0]=n*m+o*g+a*v,r[3]=n*y+o*x+a*w,r[6]=n*f+o*b+a*M,r[1]=h*m+l*g+c*v,r[4]=h*y+l*x+c*w,r[7]=h*f+l*b+c*M,r[2]=u*m+d*g+p*v,r[5]=u*y+d*x+p*w,r[8]=u*f+d*b+p*M,this}multiplyScalar(t){const e=this.elements;return e[0]*=t,e[3]*=t,e[6]*=t,e[1]*=t,e[4]*=t,e[7]*=t,e[2]*=t,e[5]*=t,e[8]*=t,this}determinant(){const t=this.elements,e=t[0],s=t[1],i=t[2],r=t[3],n=t[4],o=t[5],a=t[6],h=t[7],l=t[8];return e*n*l-e*o*h-s*r*l+s*o*a+i*r*h-i*n*a}invert(){const t=this.elements,e=t[0],s=t[1],i=t[2],r=t[3],n=t[4],o=t[5],a=t[6],h=t[7],l=t[8],c=l*n-o*h,u=o*a-l*r,d=h*r-n*a,p=e*c+s*u+i*d;if(0===p)return this.set(0,0,0,0,0,0,0,0,0);const m=1/p;return t[0]=c*m,t[1]=(i*h-l*s)*m,t[2]=(o*s-i*n)*m,t[3]=u*m,t[4]=(l*e-i*a)*m,t[5]=(i*r-o*e)*m,t[6]=d*m,t[7]=(s*a-h*e)*m,t[8]=(n*e-s*r)*m,this}transpose(){let t;const e=this.elements;return t=e[1],e[1]=e[3],e[3]=t,t=e[2],e[2]=e[6],e[6]=t,t=e[5],e[5]=e[7],e[7]=t,this}getNormalMatrix(t){return this.setFromMatrix4(t).invert().transpose()}transposeIntoArray(t){const e=this.elements;return t[0]=e[0],t[1]=e[3],t[2]=e[6],t[3]=e[1],t[4]=e[4],t[5]=e[7],t[6]=e[2],t[7]=e[5],t[8]=e[8],this}setUvTransform(t,e,s,i,r,n,o){const a=Math.cos(r),h=Math.sin(r);return this.set(s*a,s*h,-s*(a*n+h*o)+n+t,-i*h,i*a,-i*(-h*n+a*o)+o+e,0,0,1),this}scale(t,e){return this.premultiply($s.makeScale(t,e)),this}rotate(t){return this.premultiply($s.makeRotation(-t)),this}translate(t,e){return this.premultiply($s.makeTranslation(t,e)),this}makeTranslation(t,e){return t.isVector2?this.set(1,0,t.x,0,1,t.y,0,0,1):this.set(1,0,t,0,1,e,0,0,1),this}makeRotation(t){const e=Math.cos(t),s=Math.sin(t);return this.set(e,-s,0,s,e,0,0,0,1),this}makeScale(t,e){return this.set(t,0,0,0,e,0,0,0,1),this}equals(t){const e=this.elements,s=t.elements;for(let t=0;t<9;t++)if(e[t]!==s[t])return!1;return!0}fromArray(t,e=0){for(let s=0;s<9;s++)this.elements[s]=t[s+e];return this}toArray(t=[],e=0){const s=this.elements;return t[e]=s[0],t[e+1]=s[1],t[e+2]=s[2],t[e+3]=s[3],t[e+4]=s[4],t[e+5]=s[5],t[e+6]=s[6],t[e+7]=s[7],t[e+8]=s[8],t}clone(){return(new this.constructor).fromArray(this.elements)}}const $s=new Gs;function Qs(t){for(let e=t.length-1;e>=0;--e)if(t[e]>=65535)return!0;return!1}const Ks={Int8Array:Int8Array,Uint8Array:Uint8Array,Uint8ClampedArray:Uint8ClampedArray,Int16Array:Int16Array,Uint16Array:Uint16Array,Int32Array:Int32Array,Uint32Array:Uint32Array,Float32Array:Float32Array,Float64Array:Float64Array};function ti(t,e){return new Ks[t](e)}function ei(t){return document.createElementNS("http://www.w3.org/1999/xhtml",t)}function si(){const t=ei("canvas");return t.style.display="block",t}const ii={};function ri(t){t in ii||(ii[t]=!0,console.warn(t))}function ni(t,e,s){return new Promise((function(i,r){setTimeout((function n(){switch(t.clientWaitSync(e,t.SYNC_FLUSH_COMMANDS_BIT,0)){case t.WAIT_FAILED:r();break;case t.TIMEOUT_EXPIRED:setTimeout(n,s);break;default:i()}}),s)}))}function oi(t){const e=t.elements;e[2]=.5*e[2]+.5*e[3],e[6]=.5*e[6]+.5*e[7],e[10]=.5*e[10]+.5*e[11],e[14]=.5*e[14]+.5*e[15]}function ai(t){const e=t.elements;-1===e[11]?(e[10]=-e[10]-1,e[14]=-e[14]):(e[10]=-e[10],e[14]=1-e[14])}const hi=(new Gs).set(.4123908,.3575843,.1804808,.212639,.7151687,.0721923,.0193308,.1191948,.9505322),li=(new Gs).set(3.2409699,-1.5373832,-.4986108,-.9692436,1.8759675,.0415551,.0556301,-.203977,1.0569715);function ci(){const t={enabled:!0,workingColorSpace:$e,spaces:{},convert:function(t,e,s){return!1!==this.enabled&&e!==s&&e&&s?(this.spaces[e].transfer===Ke&&(t.r=di(t.r),t.g=di(t.g),t.b=di(t.b)),this.spaces[e].primaries!==this.spaces[s].primaries&&(t.applyMatrix3(this.spaces[e].toXYZ),t.applyMatrix3(this.spaces[s].fromXYZ)),this.spaces[s].transfer===Ke&&(t.r=pi(t.r),t.g=pi(t.g),t.b=pi(t.b)),t):t},fromWorkingColorSpace:function(t,e){return this.convert(t,this.workingColorSpace,e)},toWorkingColorSpace:function(t,e){return this.convert(t,e,this.workingColorSpace)},getPrimaries:function(t){return this.spaces[t].primaries},getTransfer:function(t){return""===t?Qe:this.spaces[t].transfer},getLuminanceCoefficients:function(t,e=this.workingColorSpace){return t.fromArray(this.spaces[e].luminanceCoefficients)},define:function(t){Object.assign(this.spaces,t)},_getMatrix:function(t,e,s){return t.copy(this.spaces[e].toXYZ).multiply(this.spaces[s].fromXYZ)},_getDrawingBufferColorSpace:function(t){return this.spaces[t].outputColorSpaceConfig.drawingBufferColorSpace},_getUnpackColorSpace:function(t=this.workingColorSpace){return this.spaces[t].workingColorSpaceConfig.unpackColorSpace}},e=[.64,.33,.3,.6,.15,.06],s=[.2126,.7152,.0722],i=[.3127,.329];return t.define({[$e]:{primaries:e,whitePoint:i,transfer:Qe,toXYZ:hi,fromXYZ:li,luminanceCoefficients:s,workingColorSpaceConfig:{unpackColorSpace:Ge},outputColorSpaceConfig:{drawingBufferColorSpace:Ge}},[Ge]:{primaries:e,whitePoint:i,transfer:Ke,toXYZ:hi,fromXYZ:li,luminanceCoefficients:s,outputColorSpaceConfig:{drawingBufferColorSpace:Ge}}}),t}const ui=ci();function di(t){return t<.04045?.0773993808*t:Math.pow(.9478672986*t+.0521327014,2.4)}function pi(t){return t<.0031308?12.92*t:1.055*Math.pow(t,.41666)-.055}let mi;class yi{static getDataURL(t){if(/^data:/i.test(t.src))return t.src;if("undefined"==typeof HTMLCanvasElement)return t.src;let e;if(t instanceof HTMLCanvasElement)e=t;else{void 0===mi&&(mi=ei("canvas")),mi.width=t.width,mi.height=t.height;const s=mi.getContext("2d");t instanceof ImageData?s.putImageData(t,0,0):s.drawImage(t,0,0,t.width,t.height),e=mi}return e.width>2048||e.height>2048?(console.warn("THREE.ImageUtils.getDataURL: Image converted to jpg for performance reasons",t),e.toDataURL("image/jpeg",.6)):e.toDataURL("image/png")}static sRGBToLinear(t){if("undefined"!=typeof HTMLImageElement&&t instanceof HTMLImageElement||"undefined"!=typeof HTMLCanvasElement&&t instanceof HTMLCanvasElement||"undefined"!=typeof ImageBitmap&&t instanceof ImageBitmap){const e=ei("canvas");e.width=t.width,e.height=t.height;const s=e.getContext("2d");s.drawImage(t,0,0,t.width,t.height);const i=s.getImageData(0,0,t.width,t.height),r=i.data;for(let t=0;t<r.length;t++)r[t]=255*di(r[t]/255);return s.putImageData(i,0,0),e}if(t.data){const e=t.data.slice(0);for(let t=0;t<e.length;t++)e instanceof Uint8Array||e instanceof Uint8ClampedArray?e[t]=Math.floor(255*di(e[t]/255)):e[t]=di(e[t]);return{data:e,width:t.width,height:t.height}}return console.warn("THREE.ImageUtils.sRGBToLinear(): Unsupported image type. No color space conversion applied."),t}}let fi=0;class gi{constructor(t=null){this.isSource=!0,Object.defineProperty(this,"id",{value:fi++}),this.uuid=Us(),this.data=t,this.dataReady=!0,this.version=0}set needsUpdate(t){!0===t&&this.version++}toJSON(t){const e=void 0===t||"string"==typeof t;if(!e&&void 0!==t.images[this.uuid])return t.images[this.uuid];const s={uuid:this.uuid,url:""},i=this.data;if(null!==i){let t;if(Array.isArray(i)){t=[];for(let e=0,s=i.length;e<s;e++)i[e].isDataTexture?t.push(xi(i[e].image)):t.push(xi(i[e]))}else t=xi(i);s.url=t}return e||(t.images[this.uuid]=s),s}}function xi(t){return"undefined"!=typeof HTMLImageElement&&t instanceof HTMLImageElement||"undefined"!=typeof HTMLCanvasElement&&t instanceof HTMLCanvasElement||"undefined"!=typeof ImageBitmap&&t instanceof ImageBitmap?yi.getDataURL(t):t.data?{data:Array.from(t.data),width:t.width,height:t.height,type:t.data.constructor.name}:(console.warn("THREE.Texture: Unable to serialize Texture."),{})}let bi=0;class vi extends Ns{constructor(t=vi.DEFAULT_IMAGE,e=vi.DEFAULT_MAPPING,s=1001,i=1001,r=1006,n=1008,o=1023,a=1009,h=vi.DEFAULT_ANISOTROPY,l=""){super(),this.isTexture=!0,Object.defineProperty(this,"id",{value:bi++}),this.uuid=Us(),this.name="",this.source=new gi(t),this.mipmaps=[],this.mapping=e,this.channel=0,this.wrapS=s,this.wrapT=i,this.magFilter=r,this.minFilter=n,this.anisotropy=h,this.format=o,this.internalFormat=null,this.type=a,this.offset=new Zs(0,0),this.repeat=new Zs(1,1),this.center=new Zs(0,0),this.rotation=0,this.matrixAutoUpdate=!0,this.matrix=new Gs,this.generateMipmaps=!0,this.premultiplyAlpha=!1,this.flipY=!0,this.unpackAlignment=4,this.colorSpace=l,this.userData={},this.version=0,this.onUpdate=null,this.renderTarget=null,this.isRenderTargetTexture=!1,this.pmremVersion=0}get image(){return this.source.data}set image(t=null){this.source.data=t}updateMatrix(){this.matrix.setUvTransform(this.offset.x,this.offset.y,this.repeat.x,this.repeat.y,this.rotation,this.center.x,this.center.y)}clone(){return(new this.constructor).copy(this)}copy(t){return this.name=t.name,this.source=t.source,this.mipmaps=t.mipmaps.slice(0),this.mapping=t.mapping,this.channel=t.channel,this.wrapS=t.wrapS,this.wrapT=t.wrapT,this.magFilter=t.magFilter,this.minFilter=t.minFilter,this.anisotropy=t.anisotropy,this.format=t.format,this.internalFormat=t.internalFormat,this.type=t.type,this.offset.copy(t.offset),this.repeat.copy(t.repeat),this.center.copy(t.center),this.rotation=t.rotation,this.matrixAutoUpdate=t.matrixAutoUpdate,this.matrix.copy(t.matrix),this.generateMipmaps=t.generateMipmaps,this.premultiplyAlpha=t.premultiplyAlpha,this.flipY=t.flipY,this.unpackAlignment=t.unpackAlignment,this.colorSpace=t.colorSpace,this.renderTarget=t.renderTarget,this.isRenderTargetTexture=t.isRenderTargetTexture,this.userData=JSON.parse(JSON.stringify(t.userData)),this.needsUpdate=!0,this}toJSON(t){const e=void 0===t||"string"==typeof t;if(!e&&void 0!==t.textures[this.uuid])return t.textures[this.uuid];const s={metadata:{version:4.6,type:"Texture",generator:"Texture.toJSON"},uuid:this.uuid,name:this.name,image:this.source.toJSON(t).uuid,mapping:this.mapping,channel:this.channel,repeat:[this.repeat.x,this.repeat.y],offset:[this.offset.x,this.offset.y],center:[this.center.x,this.center.y],rotation:this.rotation,wrap:[this.wrapS,this.wrapT],format:this.format,internalFormat:this.internalFormat,type:this.type,colorSpace:this.colorSpace,minFilter:this.minFilter,magFilter:this.magFilter,anisotropy:this.anisotropy,flipY:this.flipY,generateMipmaps:this.generateMipmaps,premultiplyAlpha:this.premultiplyAlpha,unpackAlignment:this.unpackAlignment};return Object.keys(this.userData).length>0&&(s.userData=this.userData),e||(t.textures[this.uuid]=s),s}dispose(){this.dispatchEvent({type:"dispose"})}transformUv(t){if(this.mapping!==at)return t;if(t.applyMatrix3(this.matrix),t.x<0||t.x>1)switch(this.wrapS){case pt:t.x=t.x-Math.floor(t.x);break;case mt:t.x=t.x<0?0:1;break;case yt:1===Math.abs(Math.floor(t.x)%2)?t.x=Math.ceil(t.x)-t.x:t.x=t.x-Math.floor(t.x)}if(t.y<0||t.y>1)switch(this.wrapT){case pt:t.y=t.y-Math.floor(t.y);break;case mt:t.y=t.y<0?0:1;break;case yt:1===Math.abs(Math.floor(t.y)%2)?t.y=Math.ceil(t.y)-t.y:t.y=t.y-Math.floor(t.y)}return this.flipY&&(t.y=1-t.y),t}set needsUpdate(t){!0===t&&(this.version++,this.source.needsUpdate=!0)}set needsPMREMUpdate(t){!0===t&&this.pmremVersion++}}vi.DEFAULT_IMAGE=null,vi.DEFAULT_MAPPING=at,vi.DEFAULT_ANISOTROPY=1;class wi{constructor(t=0,e=0,s=0,i=1){wi.prototype.isVector4=!0,this.x=t,this.y=e,this.z=s,this.w=i}get width(){return this.z}set width(t){this.z=t}get height(){return this.w}set height(t){this.w=t}set(t,e,s,i){return this.x=t,this.y=e,this.z=s,this.w=i,this}setScalar(t){return this.x=t,this.y=t,this.z=t,this.w=t,this}setX(t){return this.x=t,this}setY(t){return this.y=t,this}setZ(t){return this.z=t,this}setW(t){return this.w=t,this}setComponent(t,e){switch(t){case 0:this.x=e;break;case 1:this.y=e;break;case 2:this.z=e;break;case 3:this.w=e;break;default:throw new Error("index is out of range: "+t)}return this}getComponent(t){switch(t){case 0:return this.x;case 1:return this.y;case 2:return this.z;case 3:return this.w;default:throw new Error("index is out of range: "+t)}}clone(){return new this.constructor(this.x,this.y,this.z,this.w)}copy(t){return this.x=t.x,this.y=t.y,this.z=t.z,this.w=void 0!==t.w?t.w:1,this}add(t){return this.x+=t.x,this.y+=t.y,this.z+=t.z,this.w+=t.w,this}addScalar(t){return this.x+=t,this.y+=t,this.z+=t,this.w+=t,this}addVectors(t,e){return this.x=t.x+e.x,this.y=t.y+e.y,this.z=t.z+e.z,this.w=t.w+e.w,this}addScaledVector(t,e){return this.x+=t.x*e,this.y+=t.y*e,this.z+=t.z*e,this.w+=t.w*e,this}sub(t){return this.x-=t.x,this.y-=t.y,this.z-=t.z,this.w-=t.w,this}subScalar(t){return this.x-=t,this.y-=t,this.z-=t,this.w-=t,this}subVectors(t,e){return this.x=t.x-e.x,this.y=t.y-e.y,this.z=t.z-e.z,this.w=t.w-e.w,this}multiply(t){return this.x*=t.x,this.y*=t.y,this.z*=t.z,this.w*=t.w,this}multiplyScalar(t){return this.x*=t,this.y*=t,this.z*=t,this.w*=t,this}applyMatrix4(t){const e=this.x,s=this.y,i=this.z,r=this.w,n=t.elements;return this.x=n[0]*e+n[4]*s+n[8]*i+n[12]*r,this.y=n[1]*e+n[5]*s+n[9]*i+n[13]*r,this.z=n[2]*e+n[6]*s+n[10]*i+n[14]*r,this.w=n[3]*e+n[7]*s+n[11]*i+n[15]*r,this}divide(t){return this.x/=t.x,this.y/=t.y,this.z/=t.z,this.w/=t.w,this}divideScalar(t){return this.multiplyScalar(1/t)}setAxisAngleFromQuaternion(t){this.w=2*Math.acos(t.w);const e=Math.sqrt(1-t.w*t.w);return e<1e-4?(this.x=1,this.y=0,this.z=0):(this.x=t.x/e,this.y=t.y/e,this.z=t.z/e),this}setAxisAngleFromRotationMatrix(t){let e,s,i,r;const n=.01,o=.1,a=t.elements,h=a[0],l=a[4],c=a[8],u=a[1],d=a[5],p=a[9],m=a[2],y=a[6],f=a[10];if(Math.abs(l-u)<n&&Math.abs(c-m)<n&&Math.abs(p-y)<n){if(Math.abs(l+u)<o&&Math.abs(c+m)<o&&Math.abs(p+y)<o&&Math.abs(h+d+f-3)<o)return this.set(1,0,0,0),this;e=Math.PI;const t=(h+1)/2,a=(d+1)/2,g=(f+1)/2,x=(l+u)/4,b=(c+m)/4,v=(p+y)/4;return t>a&&t>g?t<n?(s=0,i=.*********,r=.*********):(s=Math.sqrt(t),i=x/s,r=b/s):a>g?a<n?(s=.*********,i=0,r=.*********):(i=Math.sqrt(a),s=x/i,r=v/i):g<n?(s=.*********,i=.*********,r=0):(r=Math.sqrt(g),s=b/r,i=v/r),this.set(s,i,r,e),this}let g=Math.sqrt((y-p)*(y-p)+(c-m)*(c-m)+(u-l)*(u-l));return Math.abs(g)<.001&&(g=1),this.x=(y-p)/g,this.y=(c-m)/g,this.z=(u-l)/g,this.w=Math.acos((h+d+f-1)/2),this}setFromMatrixPosition(t){const e=t.elements;return this.x=e[12],this.y=e[13],this.z=e[14],this.w=e[15],this}min(t){return this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this.z=Math.min(this.z,t.z),this.w=Math.min(this.w,t.w),this}max(t){return this.x=Math.max(this.x,t.x),this.y=Math.max(this.y,t.y),this.z=Math.max(this.z,t.z),this.w=Math.max(this.w,t.w),this}clamp(t,e){return this.x=Ds(this.x,t.x,e.x),this.y=Ds(this.y,t.y,e.y),this.z=Ds(this.z,t.z,e.z),this.w=Ds(this.w,t.w,e.w),this}clampScalar(t,e){return this.x=Ds(this.x,t,e),this.y=Ds(this.y,t,e),this.z=Ds(this.z,t,e),this.w=Ds(this.w,t,e),this}clampLength(t,e){const s=this.length();return this.divideScalar(s||1).multiplyScalar(Ds(s,t,e))}floor(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this.z=Math.floor(this.z),this.w=Math.floor(this.w),this}ceil(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this.z=Math.ceil(this.z),this.w=Math.ceil(this.w),this}round(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this.z=Math.round(this.z),this.w=Math.round(this.w),this}roundToZero(){return this.x=Math.trunc(this.x),this.y=Math.trunc(this.y),this.z=Math.trunc(this.z),this.w=Math.trunc(this.w),this}negate(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this.w=-this.w,this}dot(t){return this.x*t.x+this.y*t.y+this.z*t.z+this.w*t.w}lengthSq(){return this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w}length(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w)}manhattanLength(){return Math.abs(this.x)+Math.abs(this.y)+Math.abs(this.z)+Math.abs(this.w)}normalize(){return this.divideScalar(this.length()||1)}setLength(t){return this.normalize().multiplyScalar(t)}lerp(t,e){return this.x+=(t.x-this.x)*e,this.y+=(t.y-this.y)*e,this.z+=(t.z-this.z)*e,this.w+=(t.w-this.w)*e,this}lerpVectors(t,e,s){return this.x=t.x+(e.x-t.x)*s,this.y=t.y+(e.y-t.y)*s,this.z=t.z+(e.z-t.z)*s,this.w=t.w+(e.w-t.w)*s,this}equals(t){return t.x===this.x&&t.y===this.y&&t.z===this.z&&t.w===this.w}fromArray(t,e=0){return this.x=t[e],this.y=t[e+1],this.z=t[e+2],this.w=t[e+3],this}toArray(t=[],e=0){return t[e]=this.x,t[e+1]=this.y,t[e+2]=this.z,t[e+3]=this.w,t}fromBufferAttribute(t,e){return this.x=t.getX(e),this.y=t.getY(e),this.z=t.getZ(e),this.w=t.getW(e),this}random(){return this.x=Math.random(),this.y=Math.random(),this.z=Math.random(),this.w=Math.random(),this}*[Symbol.iterator](){yield this.x,yield this.y,yield this.z,yield this.w}}class Mi extends Ns{constructor(t=1,e=1,s={}){super(),this.isRenderTarget=!0,this.width=t,this.height=e,this.depth=1,this.scissor=new wi(0,0,t,e),this.scissorTest=!1,this.viewport=new wi(0,0,t,e);const i={width:t,height:e,depth:1};s=Object.assign({generateMipmaps:!1,internalFormat:null,minFilter:wt,depthBuffer:!0,stencilBuffer:!1,resolveDepthBuffer:!0,resolveStencilBuffer:!0,depthTexture:null,samples:0,count:1},s);const r=new vi(i,s.mapping,s.wrapS,s.wrapT,s.magFilter,s.minFilter,s.format,s.type,s.anisotropy,s.colorSpace);r.flipY=!1,r.generateMipmaps=s.generateMipmaps,r.internalFormat=s.internalFormat,this.textures=[];const n=s.count;for(let t=0;t<n;t++)this.textures[t]=r.clone(),this.textures[t].isRenderTargetTexture=!0,this.textures[t].renderTarget=this;this.depthBuffer=s.depthBuffer,this.stencilBuffer=s.stencilBuffer,this.resolveDepthBuffer=s.resolveDepthBuffer,this.resolveStencilBuffer=s.resolveStencilBuffer,this._depthTexture=null,this.depthTexture=s.depthTexture,this.samples=s.samples}get texture(){return this.textures[0]}set texture(t){this.textures[0]=t}set depthTexture(t){null!==this._depthTexture&&(this._depthTexture.renderTarget=null),null!==t&&(t.renderTarget=this),this._depthTexture=t}get depthTexture(){return this._depthTexture}setSize(t,e,s=1){if(this.width!==t||this.height!==e||this.depth!==s){this.width=t,this.height=e,this.depth=s;for(let i=0,r=this.textures.length;i<r;i++)this.textures[i].image.width=t,this.textures[i].image.height=e,this.textures[i].image.depth=s;this.dispose()}this.viewport.set(0,0,t,e),this.scissor.set(0,0,t,e)}clone(){return(new this.constructor).copy(this)}copy(t){this.width=t.width,this.height=t.height,this.depth=t.depth,this.scissor.copy(t.scissor),this.scissorTest=t.scissorTest,this.viewport.copy(t.viewport),this.textures.length=0;for(let e=0,s=t.textures.length;e<s;e++)this.textures[e]=t.textures[e].clone(),this.textures[e].isRenderTargetTexture=!0,this.textures[e].renderTarget=this;const e=Object.assign({},t.texture.image);return this.texture.source=new gi(e),this.depthBuffer=t.depthBuffer,this.stencilBuffer=t.stencilBuffer,this.resolveDepthBuffer=t.resolveDepthBuffer,this.resolveStencilBuffer=t.resolveStencilBuffer,null!==t.depthTexture&&(this.depthTexture=t.depthTexture.clone()),this.samples=t.samples,this}dispose(){this.dispatchEvent({type:"dispose"})}}class Si extends Mi{constructor(t=1,e=1,s={}){super(t,e,s),this.isWebGLRenderTarget=!0}}class _i extends vi{constructor(t=null,e=1,s=1,i=1){super(null),this.isDataArrayTexture=!0,this.image={data:t,width:e,height:s,depth:i},this.magFilter=ft,this.minFilter=ft,this.wrapR=mt,this.generateMipmaps=!1,this.flipY=!1,this.unpackAlignment=1,this.layerUpdates=new Set}addLayerUpdate(t){this.layerUpdates.add(t)}clearLayerUpdates(){this.layerUpdates.clear()}}class Ai extends Si{constructor(t=1,e=1,s=1,i={}){super(t,e,i),this.isWebGLArrayRenderTarget=!0,this.depth=s,this.texture=new _i(null,t,e,s),this.texture.isRenderTargetTexture=!0}}class Ti extends vi{constructor(t=null,e=1,s=1,i=1){super(null),this.isData3DTexture=!0,this.image={data:t,width:e,height:s,depth:i},this.magFilter=ft,this.minFilter=ft,this.wrapR=mt,this.generateMipmaps=!1,this.flipY=!1,this.unpackAlignment=1}}class zi extends Si{constructor(t=1,e=1,s=1,i={}){super(t,e,i),this.isWebGL3DRenderTarget=!0,this.depth=s,this.texture=new Ti(null,t,e,s),this.texture.isRenderTargetTexture=!0}}class Ci{constructor(t=0,e=0,s=0,i=1){this.isQuaternion=!0,this._x=t,this._y=e,this._z=s,this._w=i}static slerpFlat(t,e,s,i,r,n,o){let a=s[i+0],h=s[i+1],l=s[i+2],c=s[i+3];const u=r[n+0],d=r[n+1],p=r[n+2],m=r[n+3];if(0===o)return t[e+0]=a,t[e+1]=h,t[e+2]=l,void(t[e+3]=c);if(1===o)return t[e+0]=u,t[e+1]=d,t[e+2]=p,void(t[e+3]=m);if(c!==m||a!==u||h!==d||l!==p){let t=1-o;const e=a*u+h*d+l*p+c*m,s=e>=0?1:-1,i=1-e*e;if(i>Number.EPSILON){const r=Math.sqrt(i),n=Math.atan2(r,e*s);t=Math.sin(t*n)/r,o=Math.sin(o*n)/r}const r=o*s;if(a=a*t+u*r,h=h*t+d*r,l=l*t+p*r,c=c*t+m*r,t===1-o){const t=1/Math.sqrt(a*a+h*h+l*l+c*c);a*=t,h*=t,l*=t,c*=t}}t[e]=a,t[e+1]=h,t[e+2]=l,t[e+3]=c}static multiplyQuaternionsFlat(t,e,s,i,r,n){const o=s[i],a=s[i+1],h=s[i+2],l=s[i+3],c=r[n],u=r[n+1],d=r[n+2],p=r[n+3];return t[e]=o*p+l*c+a*d-h*u,t[e+1]=a*p+l*u+h*c-o*d,t[e+2]=h*p+l*d+o*u-a*c,t[e+3]=l*p-o*c-a*u-h*d,t}get x(){return this._x}set x(t){this._x=t,this._onChangeCallback()}get y(){return this._y}set y(t){this._y=t,this._onChangeCallback()}get z(){return this._z}set z(t){this._z=t,this._onChangeCallback()}get w(){return this._w}set w(t){this._w=t,this._onChangeCallback()}set(t,e,s,i){return this._x=t,this._y=e,this._z=s,this._w=i,this._onChangeCallback(),this}clone(){return new this.constructor(this._x,this._y,this._z,this._w)}copy(t){return this._x=t.x,this._y=t.y,this._z=t.z,this._w=t.w,this._onChangeCallback(),this}setFromEuler(t,e=!0){const s=t._x,i=t._y,r=t._z,n=t._order,o=Math.cos,a=Math.sin,h=o(s/2),l=o(i/2),c=o(r/2),u=a(s/2),d=a(i/2),p=a(r/2);switch(n){case"XYZ":this._x=u*l*c+h*d*p,this._y=h*d*c-u*l*p,this._z=h*l*p+u*d*c,this._w=h*l*c-u*d*p;break;case"YXZ":this._x=u*l*c+h*d*p,this._y=h*d*c-u*l*p,this._z=h*l*p-u*d*c,this._w=h*l*c+u*d*p;break;case"ZXY":this._x=u*l*c-h*d*p,this._y=h*d*c+u*l*p,this._z=h*l*p+u*d*c,this._w=h*l*c-u*d*p;break;case"ZYX":this._x=u*l*c-h*d*p,this._y=h*d*c+u*l*p,this._z=h*l*p-u*d*c,this._w=h*l*c+u*d*p;break;case"YZX":this._x=u*l*c+h*d*p,this._y=h*d*c+u*l*p,this._z=h*l*p-u*d*c,this._w=h*l*c-u*d*p;break;case"XZY":this._x=u*l*c-h*d*p,this._y=h*d*c-u*l*p,this._z=h*l*p+u*d*c,this._w=h*l*c+u*d*p;break;default:console.warn("THREE.Quaternion: .setFromEuler() encountered an unknown order: "+n)}return!0===e&&this._onChangeCallback(),this}setFromAxisAngle(t,e){const s=e/2,i=Math.sin(s);return this._x=t.x*i,this._y=t.y*i,this._z=t.z*i,this._w=Math.cos(s),this._onChangeCallback(),this}setFromRotationMatrix(t){const e=t.elements,s=e[0],i=e[4],r=e[8],n=e[1],o=e[5],a=e[9],h=e[2],l=e[6],c=e[10],u=s+o+c;if(u>0){const t=.5/Math.sqrt(u+1);this._w=.25/t,this._x=(l-a)*t,this._y=(r-h)*t,this._z=(n-i)*t}else if(s>o&&s>c){const t=2*Math.sqrt(1+s-o-c);this._w=(l-a)/t,this._x=.25*t,this._y=(i+n)/t,this._z=(r+h)/t}else if(o>c){const t=2*Math.sqrt(1+o-s-c);this._w=(r-h)/t,this._x=(i+n)/t,this._y=.25*t,this._z=(a+l)/t}else{const t=2*Math.sqrt(1+c-s-o);this._w=(n-i)/t,this._x=(r+h)/t,this._y=(a+l)/t,this._z=.25*t}return this._onChangeCallback(),this}setFromUnitVectors(t,e){let s=t.dot(e)+1;return s<Number.EPSILON?(s=0,Math.abs(t.x)>Math.abs(t.z)?(this._x=-t.y,this._y=t.x,this._z=0,this._w=s):(this._x=0,this._y=-t.z,this._z=t.y,this._w=s)):(this._x=t.y*e.z-t.z*e.y,this._y=t.z*e.x-t.x*e.z,this._z=t.x*e.y-t.y*e.x,this._w=s),this.normalize()}angleTo(t){return 2*Math.acos(Math.abs(Ds(this.dot(t),-1,1)))}rotateTowards(t,e){const s=this.angleTo(t);if(0===s)return this;const i=Math.min(1,e/s);return this.slerp(t,i),this}identity(){return this.set(0,0,0,1)}invert(){return this.conjugate()}conjugate(){return this._x*=-1,this._y*=-1,this._z*=-1,this._onChangeCallback(),this}dot(t){return this._x*t._x+this._y*t._y+this._z*t._z+this._w*t._w}lengthSq(){return this._x*this._x+this._y*this._y+this._z*this._z+this._w*this._w}length(){return Math.sqrt(this._x*this._x+this._y*this._y+this._z*this._z+this._w*this._w)}normalize(){let t=this.length();return 0===t?(this._x=0,this._y=0,this._z=0,this._w=1):(t=1/t,this._x=this._x*t,this._y=this._y*t,this._z=this._z*t,this._w=this._w*t),this._onChangeCallback(),this}multiply(t){return this.multiplyQuaternions(this,t)}premultiply(t){return this.multiplyQuaternions(t,this)}multiplyQuaternions(t,e){const s=t._x,i=t._y,r=t._z,n=t._w,o=e._x,a=e._y,h=e._z,l=e._w;return this._x=s*l+n*o+i*h-r*a,this._y=i*l+n*a+r*o-s*h,this._z=r*l+n*h+s*a-i*o,this._w=n*l-s*o-i*a-r*h,this._onChangeCallback(),this}slerp(t,e){if(0===e)return this;if(1===e)return this.copy(t);const s=this._x,i=this._y,r=this._z,n=this._w;let o=n*t._w+s*t._x+i*t._y+r*t._z;if(o<0?(this._w=-t._w,this._x=-t._x,this._y=-t._y,this._z=-t._z,o=-o):this.copy(t),o>=1)return this._w=n,this._x=s,this._y=i,this._z=r,this;const a=1-o*o;if(a<=Number.EPSILON){const t=1-e;return this._w=t*n+e*this._w,this._x=t*s+e*this._x,this._y=t*i+e*this._y,this._z=t*r+e*this._z,this.normalize(),this}const h=Math.sqrt(a),l=Math.atan2(h,o),c=Math.sin((1-e)*l)/h,u=Math.sin(e*l)/h;return this._w=n*c+this._w*u,this._x=s*c+this._x*u,this._y=i*c+this._y*u,this._z=r*c+this._z*u,this._onChangeCallback(),this}slerpQuaternions(t,e,s){return this.copy(t).slerp(e,s)}random(){const t=2*Math.PI*Math.random(),e=2*Math.PI*Math.random(),s=Math.random(),i=Math.sqrt(1-s),r=Math.sqrt(s);return this.set(i*Math.sin(t),i*Math.cos(t),r*Math.sin(e),r*Math.cos(e))}equals(t){return t._x===this._x&&t._y===this._y&&t._z===this._z&&t._w===this._w}fromArray(t,e=0){return this._x=t[e],this._y=t[e+1],this._z=t[e+2],this._w=t[e+3],this._onChangeCallback(),this}toArray(t=[],e=0){return t[e]=this._x,t[e+1]=this._y,t[e+2]=this._z,t[e+3]=this._w,t}fromBufferAttribute(t,e){return this._x=t.getX(e),this._y=t.getY(e),this._z=t.getZ(e),this._w=t.getW(e),this._onChangeCallback(),this}toJSON(){return this.toArray()}_onChange(t){return this._onChangeCallback=t,this}_onChangeCallback(){}*[Symbol.iterator](){yield this._x,yield this._y,yield this._z,yield this._w}}class Ii{constructor(t=0,e=0,s=0){Ii.prototype.isVector3=!0,this.x=t,this.y=e,this.z=s}set(t,e,s){return void 0===s&&(s=this.z),this.x=t,this.y=e,this.z=s,this}setScalar(t){return this.x=t,this.y=t,this.z=t,this}setX(t){return this.x=t,this}setY(t){return this.y=t,this}setZ(t){return this.z=t,this}setComponent(t,e){switch(t){case 0:this.x=e;break;case 1:this.y=e;break;case 2:this.z=e;break;default:throw new Error("index is out of range: "+t)}return this}getComponent(t){switch(t){case 0:return this.x;case 1:return this.y;case 2:return this.z;default:throw new Error("index is out of range: "+t)}}clone(){return new this.constructor(this.x,this.y,this.z)}copy(t){return this.x=t.x,this.y=t.y,this.z=t.z,this}add(t){return this.x+=t.x,this.y+=t.y,this.z+=t.z,this}addScalar(t){return this.x+=t,this.y+=t,this.z+=t,this}addVectors(t,e){return this.x=t.x+e.x,this.y=t.y+e.y,this.z=t.z+e.z,this}addScaledVector(t,e){return this.x+=t.x*e,this.y+=t.y*e,this.z+=t.z*e,this}sub(t){return this.x-=t.x,this.y-=t.y,this.z-=t.z,this}subScalar(t){return this.x-=t,this.y-=t,this.z-=t,this}subVectors(t,e){return this.x=t.x-e.x,this.y=t.y-e.y,this.z=t.z-e.z,this}multiply(t){return this.x*=t.x,this.y*=t.y,this.z*=t.z,this}multiplyScalar(t){return this.x*=t,this.y*=t,this.z*=t,this}multiplyVectors(t,e){return this.x=t.x*e.x,this.y=t.y*e.y,this.z=t.z*e.z,this}applyEuler(t){return this.applyQuaternion(ki.setFromEuler(t))}applyAxisAngle(t,e){return this.applyQuaternion(ki.setFromAxisAngle(t,e))}applyMatrix3(t){const e=this.x,s=this.y,i=this.z,r=t.elements;return this.x=r[0]*e+r[3]*s+r[6]*i,this.y=r[1]*e+r[4]*s+r[7]*i,this.z=r[2]*e+r[5]*s+r[8]*i,this}applyNormalMatrix(t){return this.applyMatrix3(t).normalize()}applyMatrix4(t){const e=this.x,s=this.y,i=this.z,r=t.elements,n=1/(r[3]*e+r[7]*s+r[11]*i+r[15]);return this.x=(r[0]*e+r[4]*s+r[8]*i+r[12])*n,this.y=(r[1]*e+r[5]*s+r[9]*i+r[13])*n,this.z=(r[2]*e+r[6]*s+r[10]*i+r[14])*n,this}applyQuaternion(t){const e=this.x,s=this.y,i=this.z,r=t.x,n=t.y,o=t.z,a=t.w,h=2*(n*i-o*s),l=2*(o*e-r*i),c=2*(r*s-n*e);return this.x=e+a*h+n*c-o*l,this.y=s+a*l+o*h-r*c,this.z=i+a*c+r*l-n*h,this}project(t){return this.applyMatrix4(t.matrixWorldInverse).applyMatrix4(t.projectionMatrix)}unproject(t){return this.applyMatrix4(t.projectionMatrixInverse).applyMatrix4(t.matrixWorld)}transformDirection(t){const e=this.x,s=this.y,i=this.z,r=t.elements;return this.x=r[0]*e+r[4]*s+r[8]*i,this.y=r[1]*e+r[5]*s+r[9]*i,this.z=r[2]*e+r[6]*s+r[10]*i,this.normalize()}divide(t){return this.x/=t.x,this.y/=t.y,this.z/=t.z,this}divideScalar(t){return this.multiplyScalar(1/t)}min(t){return this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this.z=Math.min(this.z,t.z),this}max(t){return this.x=Math.max(this.x,t.x),this.y=Math.max(this.y,t.y),this.z=Math.max(this.z,t.z),this}clamp(t,e){return this.x=Ds(this.x,t.x,e.x),this.y=Ds(this.y,t.y,e.y),this.z=Ds(this.z,t.z,e.z),this}clampScalar(t,e){return this.x=Ds(this.x,t,e),this.y=Ds(this.y,t,e),this.z=Ds(this.z,t,e),this}clampLength(t,e){const s=this.length();return this.divideScalar(s||1).multiplyScalar(Ds(s,t,e))}floor(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this.z=Math.floor(this.z),this}ceil(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this.z=Math.ceil(this.z),this}round(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this.z=Math.round(this.z),this}roundToZero(){return this.x=Math.trunc(this.x),this.y=Math.trunc(this.y),this.z=Math.trunc(this.z),this}negate(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this}dot(t){return this.x*t.x+this.y*t.y+this.z*t.z}lengthSq(){return this.x*this.x+this.y*this.y+this.z*this.z}length(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)}manhattanLength(){return Math.abs(this.x)+Math.abs(this.y)+Math.abs(this.z)}normalize(){return this.divideScalar(this.length()||1)}setLength(t){return this.normalize().multiplyScalar(t)}lerp(t,e){return this.x+=(t.x-this.x)*e,this.y+=(t.y-this.y)*e,this.z+=(t.z-this.z)*e,this}lerpVectors(t,e,s){return this.x=t.x+(e.x-t.x)*s,this.y=t.y+(e.y-t.y)*s,this.z=t.z+(e.z-t.z)*s,this}cross(t){return this.crossVectors(this,t)}crossVectors(t,e){const s=t.x,i=t.y,r=t.z,n=e.x,o=e.y,a=e.z;return this.x=i*a-r*o,this.y=r*n-s*a,this.z=s*o-i*n,this}projectOnVector(t){const e=t.lengthSq();if(0===e)return this.set(0,0,0);const s=t.dot(this)/e;return this.copy(t).multiplyScalar(s)}projectOnPlane(t){return Bi.copy(this).projectOnVector(t),this.sub(Bi)}reflect(t){return this.sub(Bi.copy(t).multiplyScalar(2*this.dot(t)))}angleTo(t){const e=Math.sqrt(this.lengthSq()*t.lengthSq());if(0===e)return Math.PI/2;const s=this.dot(t)/e;return Math.acos(Ds(s,-1,1))}distanceTo(t){return Math.sqrt(this.distanceToSquared(t))}distanceToSquared(t){const e=this.x-t.x,s=this.y-t.y,i=this.z-t.z;return e*e+s*s+i*i}manhattanDistanceTo(t){return Math.abs(this.x-t.x)+Math.abs(this.y-t.y)+Math.abs(this.z-t.z)}setFromSpherical(t){return this.setFromSphericalCoords(t.radius,t.phi,t.theta)}setFromSphericalCoords(t,e,s){const i=Math.sin(e)*t;return this.x=i*Math.sin(s),this.y=Math.cos(e)*t,this.z=i*Math.cos(s),this}setFromCylindrical(t){return this.setFromCylindricalCoords(t.radius,t.theta,t.y)}setFromCylindricalCoords(t,e,s){return this.x=t*Math.sin(e),this.y=s,this.z=t*Math.cos(e),this}setFromMatrixPosition(t){const e=t.elements;return this.x=e[12],this.y=e[13],this.z=e[14],this}setFromMatrixScale(t){const e=this.setFromMatrixColumn(t,0).length(),s=this.setFromMatrixColumn(t,1).length(),i=this.setFromMatrixColumn(t,2).length();return this.x=e,this.y=s,this.z=i,this}setFromMatrixColumn(t,e){return this.fromArray(t.elements,4*e)}setFromMatrix3Column(t,e){return this.fromArray(t.elements,3*e)}setFromEuler(t){return this.x=t._x,this.y=t._y,this.z=t._z,this}setFromColor(t){return this.x=t.r,this.y=t.g,this.z=t.b,this}equals(t){return t.x===this.x&&t.y===this.y&&t.z===this.z}fromArray(t,e=0){return this.x=t[e],this.y=t[e+1],this.z=t[e+2],this}toArray(t=[],e=0){return t[e]=this.x,t[e+1]=this.y,t[e+2]=this.z,t}fromBufferAttribute(t,e){return this.x=t.getX(e),this.y=t.getY(e),this.z=t.getZ(e),this}random(){return this.x=Math.random(),this.y=Math.random(),this.z=Math.random(),this}randomDirection(){const t=Math.random()*Math.PI*2,e=2*Math.random()-1,s=Math.sqrt(1-e*e);return this.x=s*Math.cos(t),this.y=e,this.z=s*Math.sin(t),this}*[Symbol.iterator](){yield this.x,yield this.y,yield this.z}}const Bi=new Ii,ki=new Ci;class Ri{constructor(t=new Ii(1/0,1/0,1/0),e=new Ii(-1/0,-1/0,-1/0)){this.isBox3=!0,this.min=t,this.max=e}set(t,e){return this.min.copy(t),this.max.copy(e),this}setFromArray(t){this.makeEmpty();for(let e=0,s=t.length;e<s;e+=3)this.expandByPoint(Pi.fromArray(t,e));return this}setFromBufferAttribute(t){this.makeEmpty();for(let e=0,s=t.count;e<s;e++)this.expandByPoint(Pi.fromBufferAttribute(t,e));return this}setFromPoints(t){this.makeEmpty();for(let e=0,s=t.length;e<s;e++)this.expandByPoint(t[e]);return this}setFromCenterAndSize(t,e){const s=Pi.copy(e).multiplyScalar(.5);return this.min.copy(t).sub(s),this.max.copy(t).add(s),this}setFromObject(t,e=!1){return this.makeEmpty(),this.expandByObject(t,e)}clone(){return(new this.constructor).copy(this)}copy(t){return this.min.copy(t.min),this.max.copy(t.max),this}makeEmpty(){return this.min.x=this.min.y=this.min.z=1/0,this.max.x=this.max.y=this.max.z=-1/0,this}isEmpty(){return this.max.x<this.min.x||this.max.y<this.min.y||this.max.z<this.min.z}getCenter(t){return this.isEmpty()?t.set(0,0,0):t.addVectors(this.min,this.max).multiplyScalar(.5)}getSize(t){return this.isEmpty()?t.set(0,0,0):t.subVectors(this.max,this.min)}expandByPoint(t){return this.min.min(t),this.max.max(t),this}expandByVector(t){return this.min.sub(t),this.max.add(t),this}expandByScalar(t){return this.min.addScalar(-t),this.max.addScalar(t),this}expandByObject(t,e=!1){t.updateWorldMatrix(!1,!1);const s=t.geometry;if(void 0!==s){const i=s.getAttribute("position");if(!0===e&&void 0!==i&&!0!==t.isInstancedMesh)for(let e=0,s=i.count;e<s;e++)!0===t.isMesh?t.getVertexPosition(e,Pi):Pi.fromBufferAttribute(i,e),Pi.applyMatrix4(t.matrixWorld),this.expandByPoint(Pi);else void 0!==t.boundingBox?(null===t.boundingBox&&t.computeBoundingBox(),Oi.copy(t.boundingBox)):(null===s.boundingBox&&s.computeBoundingBox(),Oi.copy(s.boundingBox)),Oi.applyMatrix4(t.matrixWorld),this.union(Oi)}const i=t.children;for(let t=0,s=i.length;t<s;t++)this.expandByObject(i[t],e);return this}containsPoint(t){return t.x>=this.min.x&&t.x<=this.max.x&&t.y>=this.min.y&&t.y<=this.max.y&&t.z>=this.min.z&&t.z<=this.max.z}containsBox(t){return this.min.x<=t.min.x&&t.max.x<=this.max.x&&this.min.y<=t.min.y&&t.max.y<=this.max.y&&this.min.z<=t.min.z&&t.max.z<=this.max.z}getParameter(t,e){return e.set((t.x-this.min.x)/(this.max.x-this.min.x),(t.y-this.min.y)/(this.max.y-this.min.y),(t.z-this.min.z)/(this.max.z-this.min.z))}intersectsBox(t){return t.max.x>=this.min.x&&t.min.x<=this.max.x&&t.max.y>=this.min.y&&t.min.y<=this.max.y&&t.max.z>=this.min.z&&t.min.z<=this.max.z}intersectsSphere(t){return this.clampPoint(t.center,Pi),Pi.distanceToSquared(t.center)<=t.radius*t.radius}intersectsPlane(t){let e,s;return t.normal.x>0?(e=t.normal.x*this.min.x,s=t.normal.x*this.max.x):(e=t.normal.x*this.max.x,s=t.normal.x*this.min.x),t.normal.y>0?(e+=t.normal.y*this.min.y,s+=t.normal.y*this.max.y):(e+=t.normal.y*this.max.y,s+=t.normal.y*this.min.y),t.normal.z>0?(e+=t.normal.z*this.min.z,s+=t.normal.z*this.max.z):(e+=t.normal.z*this.max.z,s+=t.normal.z*this.min.z),e<=-t.constant&&s>=-t.constant}intersectsTriangle(t){if(this.isEmpty())return!1;this.getCenter(Ui),Di.subVectors(this.max,Ui),Fi.subVectors(t.a,Ui),Ni.subVectors(t.b,Ui),Li.subVectors(t.c,Ui),Vi.subVectors(Ni,Fi),Wi.subVectors(Li,Ni),ji.subVectors(Fi,Li);let e=[0,-Vi.z,Vi.y,0,-Wi.z,Wi.y,0,-ji.z,ji.y,Vi.z,0,-Vi.x,Wi.z,0,-Wi.x,ji.z,0,-ji.x,-Vi.y,Vi.x,0,-Wi.y,Wi.x,0,-ji.y,ji.x,0];return!!Ji(e,Fi,Ni,Li,Di)&&(e=[1,0,0,0,1,0,0,0,1],!!Ji(e,Fi,Ni,Li,Di)&&(Hi.crossVectors(Vi,Wi),e=[Hi.x,Hi.y,Hi.z],Ji(e,Fi,Ni,Li,Di)))}clampPoint(t,e){return e.copy(t).clamp(this.min,this.max)}distanceToPoint(t){return this.clampPoint(t,Pi).distanceTo(t)}getBoundingSphere(t){return this.isEmpty()?t.makeEmpty():(this.getCenter(t.center),t.radius=.5*this.getSize(Pi).length()),t}intersect(t){return this.min.max(t.min),this.max.min(t.max),this.isEmpty()&&this.makeEmpty(),this}union(t){return this.min.min(t.min),this.max.max(t.max),this}applyMatrix4(t){return this.isEmpty()||(Ei[0].set(this.min.x,this.min.y,this.min.z).applyMatrix4(t),Ei[1].set(this.min.x,this.min.y,this.max.z).applyMatrix4(t),Ei[2].set(this.min.x,this.max.y,this.min.z).applyMatrix4(t),Ei[3].set(this.min.x,this.max.y,this.max.z).applyMatrix4(t),Ei[4].set(this.max.x,this.min.y,this.min.z).applyMatrix4(t),Ei[5].set(this.max.x,this.min.y,this.max.z).applyMatrix4(t),Ei[6].set(this.max.x,this.max.y,this.min.z).applyMatrix4(t),Ei[7].set(this.max.x,this.max.y,this.max.z).applyMatrix4(t),this.setFromPoints(Ei)),this}translate(t){return this.min.add(t),this.max.add(t),this}equals(t){return t.min.equals(this.min)&&t.max.equals(this.max)}}const Ei=[new Ii,new Ii,new Ii,new Ii,new Ii,new Ii,new Ii,new Ii],Pi=new Ii,Oi=new Ri,Fi=new Ii,Ni=new Ii,Li=new Ii,Vi=new Ii,Wi=new Ii,ji=new Ii,Ui=new Ii,Di=new Ii,Hi=new Ii,qi=new Ii;function Ji(t,e,s,i,r){for(let n=0,o=t.length-3;n<=o;n+=3){qi.fromArray(t,n);const o=r.x*Math.abs(qi.x)+r.y*Math.abs(qi.y)+r.z*Math.abs(qi.z),a=e.dot(qi),h=s.dot(qi),l=i.dot(qi);if(Math.max(-Math.max(a,h,l),Math.min(a,h,l))>o)return!1}return!0}const Xi=new Ri,Yi=new Ii,Zi=new Ii;class Gi{constructor(t=new Ii,e=-1){this.isSphere=!0,this.center=t,this.radius=e}set(t,e){return this.center.copy(t),this.radius=e,this}setFromPoints(t,e){const s=this.center;void 0!==e?s.copy(e):Xi.setFromPoints(t).getCenter(s);let i=0;for(let e=0,r=t.length;e<r;e++)i=Math.max(i,s.distanceToSquared(t[e]));return this.radius=Math.sqrt(i),this}copy(t){return this.center.copy(t.center),this.radius=t.radius,this}isEmpty(){return this.radius<0}makeEmpty(){return this.center.set(0,0,0),this.radius=-1,this}containsPoint(t){return t.distanceToSquared(this.center)<=this.radius*this.radius}distanceToPoint(t){return t.distanceTo(this.center)-this.radius}intersectsSphere(t){const e=this.radius+t.radius;return t.center.distanceToSquared(this.center)<=e*e}intersectsBox(t){return t.intersectsSphere(this)}intersectsPlane(t){return Math.abs(t.distanceToPoint(this.center))<=this.radius}clampPoint(t,e){const s=this.center.distanceToSquared(t);return e.copy(t),s>this.radius*this.radius&&(e.sub(this.center).normalize(),e.multiplyScalar(this.radius).add(this.center)),e}getBoundingBox(t){return this.isEmpty()?(t.makeEmpty(),t):(t.set(this.center,this.center),t.expandByScalar(this.radius),t)}applyMatrix4(t){return this.center.applyMatrix4(t),this.radius=this.radius*t.getMaxScaleOnAxis(),this}translate(t){return this.center.add(t),this}expandByPoint(t){if(this.isEmpty())return this.center.copy(t),this.radius=0,this;Yi.subVectors(t,this.center);const e=Yi.lengthSq();if(e>this.radius*this.radius){const t=Math.sqrt(e),s=.5*(t-this.radius);this.center.addScaledVector(Yi,s/t),this.radius+=s}return this}union(t){return t.isEmpty()?this:this.isEmpty()?(this.copy(t),this):(!0===this.center.equals(t.center)?this.radius=Math.max(this.radius,t.radius):(Zi.subVectors(t.center,this.center).setLength(t.radius),this.expandByPoint(Yi.copy(t.center).add(Zi)),this.expandByPoint(Yi.copy(t.center).sub(Zi))),this)}equals(t){return t.center.equals(this.center)&&t.radius===this.radius}clone(){return(new this.constructor).copy(this)}}const $i=new Ii,Qi=new Ii,Ki=new Ii,tr=new Ii,er=new Ii,sr=new Ii,ir=new Ii;class rr{constructor(t=new Ii,e=new Ii(0,0,-1)){this.origin=t,this.direction=e}set(t,e){return this.origin.copy(t),this.direction.copy(e),this}copy(t){return this.origin.copy(t.origin),this.direction.copy(t.direction),this}at(t,e){return e.copy(this.origin).addScaledVector(this.direction,t)}lookAt(t){return this.direction.copy(t).sub(this.origin).normalize(),this}recast(t){return this.origin.copy(this.at(t,$i)),this}closestPointToPoint(t,e){e.subVectors(t,this.origin);const s=e.dot(this.direction);return s<0?e.copy(this.origin):e.copy(this.origin).addScaledVector(this.direction,s)}distanceToPoint(t){return Math.sqrt(this.distanceSqToPoint(t))}distanceSqToPoint(t){const e=$i.subVectors(t,this.origin).dot(this.direction);return e<0?this.origin.distanceToSquared(t):($i.copy(this.origin).addScaledVector(this.direction,e),$i.distanceToSquared(t))}distanceSqToSegment(t,e,s,i){Qi.copy(t).add(e).multiplyScalar(.5),Ki.copy(e).sub(t).normalize(),tr.copy(this.origin).sub(Qi);const r=.5*t.distanceTo(e),n=-this.direction.dot(Ki),o=tr.dot(this.direction),a=-tr.dot(Ki),h=tr.lengthSq(),l=Math.abs(1-n*n);let c,u,d,p;if(l>0)if(c=n*a-o,u=n*o-a,p=r*l,c>=0)if(u>=-p)if(u<=p){const t=1/l;c*=t,u*=t,d=c*(c+n*u+2*o)+u*(n*c+u+2*a)+h}else u=r,c=Math.max(0,-(n*u+o)),d=-c*c+u*(u+2*a)+h;else u=-r,c=Math.max(0,-(n*u+o)),d=-c*c+u*(u+2*a)+h;else u<=-p?(c=Math.max(0,-(-n*r+o)),u=c>0?-r:Math.min(Math.max(-r,-a),r),d=-c*c+u*(u+2*a)+h):u<=p?(c=0,u=Math.min(Math.max(-r,-a),r),d=u*(u+2*a)+h):(c=Math.max(0,-(n*r+o)),u=c>0?r:Math.min(Math.max(-r,-a),r),d=-c*c+u*(u+2*a)+h);else u=n>0?-r:r,c=Math.max(0,-(n*u+o)),d=-c*c+u*(u+2*a)+h;return s&&s.copy(this.origin).addScaledVector(this.direction,c),i&&i.copy(Qi).addScaledVector(Ki,u),d}intersectSphere(t,e){$i.subVectors(t.center,this.origin);const s=$i.dot(this.direction),i=$i.dot($i)-s*s,r=t.radius*t.radius;if(i>r)return null;const n=Math.sqrt(r-i),o=s-n,a=s+n;return a<0?null:o<0?this.at(a,e):this.at(o,e)}intersectsSphere(t){return this.distanceSqToPoint(t.center)<=t.radius*t.radius}distanceToPlane(t){const e=t.normal.dot(this.direction);if(0===e)return 0===t.distanceToPoint(this.origin)?0:null;const s=-(this.origin.dot(t.normal)+t.constant)/e;return s>=0?s:null}intersectPlane(t,e){const s=this.distanceToPlane(t);return null===s?null:this.at(s,e)}intersectsPlane(t){const e=t.distanceToPoint(this.origin);if(0===e)return!0;return t.normal.dot(this.direction)*e<0}intersectBox(t,e){let s,i,r,n,o,a;const h=1/this.direction.x,l=1/this.direction.y,c=1/this.direction.z,u=this.origin;return h>=0?(s=(t.min.x-u.x)*h,i=(t.max.x-u.x)*h):(s=(t.max.x-u.x)*h,i=(t.min.x-u.x)*h),l>=0?(r=(t.min.y-u.y)*l,n=(t.max.y-u.y)*l):(r=(t.max.y-u.y)*l,n=(t.min.y-u.y)*l),s>n||r>i?null:((r>s||isNaN(s))&&(s=r),(n<i||isNaN(i))&&(i=n),c>=0?(o=(t.min.z-u.z)*c,a=(t.max.z-u.z)*c):(o=(t.max.z-u.z)*c,a=(t.min.z-u.z)*c),s>a||o>i?null:((o>s||s!=s)&&(s=o),(a<i||i!=i)&&(i=a),i<0?null:this.at(s>=0?s:i,e)))}intersectsBox(t){return null!==this.intersectBox(t,$i)}intersectTriangle(t,e,s,i,r){er.subVectors(e,t),sr.subVectors(s,t),ir.crossVectors(er,sr);let n,o=this.direction.dot(ir);if(o>0){if(i)return null;n=1}else{if(!(o<0))return null;n=-1,o=-o}tr.subVectors(this.origin,t);const a=n*this.direction.dot(sr.crossVectors(tr,sr));if(a<0)return null;const h=n*this.direction.dot(er.cross(tr));if(h<0)return null;if(a+h>o)return null;const l=-n*tr.dot(ir);return l<0?null:this.at(l/o,r)}applyMatrix4(t){return this.origin.applyMatrix4(t),this.direction.transformDirection(t),this}equals(t){return t.origin.equals(this.origin)&&t.direction.equals(this.direction)}clone(){return(new this.constructor).copy(this)}}class nr{constructor(t,e,s,i,r,n,o,a,h,l,c,u,d,p,m,y){nr.prototype.isMatrix4=!0,this.elements=[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1],void 0!==t&&this.set(t,e,s,i,r,n,o,a,h,l,c,u,d,p,m,y)}set(t,e,s,i,r,n,o,a,h,l,c,u,d,p,m,y){const f=this.elements;return f[0]=t,f[4]=e,f[8]=s,f[12]=i,f[1]=r,f[5]=n,f[9]=o,f[13]=a,f[2]=h,f[6]=l,f[10]=c,f[14]=u,f[3]=d,f[7]=p,f[11]=m,f[15]=y,this}identity(){return this.set(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1),this}clone(){return(new nr).fromArray(this.elements)}copy(t){const e=this.elements,s=t.elements;return e[0]=s[0],e[1]=s[1],e[2]=s[2],e[3]=s[3],e[4]=s[4],e[5]=s[5],e[6]=s[6],e[7]=s[7],e[8]=s[8],e[9]=s[9],e[10]=s[10],e[11]=s[11],e[12]=s[12],e[13]=s[13],e[14]=s[14],e[15]=s[15],this}copyPosition(t){const e=this.elements,s=t.elements;return e[12]=s[12],e[13]=s[13],e[14]=s[14],this}setFromMatrix3(t){const e=t.elements;return this.set(e[0],e[3],e[6],0,e[1],e[4],e[7],0,e[2],e[5],e[8],0,0,0,0,1),this}extractBasis(t,e,s){return t.setFromMatrixColumn(this,0),e.setFromMatrixColumn(this,1),s.setFromMatrixColumn(this,2),this}makeBasis(t,e,s){return this.set(t.x,e.x,s.x,0,t.y,e.y,s.y,0,t.z,e.z,s.z,0,0,0,0,1),this}extractRotation(t){const e=this.elements,s=t.elements,i=1/or.setFromMatrixColumn(t,0).length(),r=1/or.setFromMatrixColumn(t,1).length(),n=1/or.setFromMatrixColumn(t,2).length();return e[0]=s[0]*i,e[1]=s[1]*i,e[2]=s[2]*i,e[3]=0,e[4]=s[4]*r,e[5]=s[5]*r,e[6]=s[6]*r,e[7]=0,e[8]=s[8]*n,e[9]=s[9]*n,e[10]=s[10]*n,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,this}makeRotationFromEuler(t){const e=this.elements,s=t.x,i=t.y,r=t.z,n=Math.cos(s),o=Math.sin(s),a=Math.cos(i),h=Math.sin(i),l=Math.cos(r),c=Math.sin(r);if("XYZ"===t.order){const t=n*l,s=n*c,i=o*l,r=o*c;e[0]=a*l,e[4]=-a*c,e[8]=h,e[1]=s+i*h,e[5]=t-r*h,e[9]=-o*a,e[2]=r-t*h,e[6]=i+s*h,e[10]=n*a}else if("YXZ"===t.order){const t=a*l,s=a*c,i=h*l,r=h*c;e[0]=t+r*o,e[4]=i*o-s,e[8]=n*h,e[1]=n*c,e[5]=n*l,e[9]=-o,e[2]=s*o-i,e[6]=r+t*o,e[10]=n*a}else if("ZXY"===t.order){const t=a*l,s=a*c,i=h*l,r=h*c;e[0]=t-r*o,e[4]=-n*c,e[8]=i+s*o,e[1]=s+i*o,e[5]=n*l,e[9]=r-t*o,e[2]=-n*h,e[6]=o,e[10]=n*a}else if("ZYX"===t.order){const t=n*l,s=n*c,i=o*l,r=o*c;e[0]=a*l,e[4]=i*h-s,e[8]=t*h+r,e[1]=a*c,e[5]=r*h+t,e[9]=s*h-i,e[2]=-h,e[6]=o*a,e[10]=n*a}else if("YZX"===t.order){const t=n*a,s=n*h,i=o*a,r=o*h;e[0]=a*l,e[4]=r-t*c,e[8]=i*c+s,e[1]=c,e[5]=n*l,e[9]=-o*l,e[2]=-h*l,e[6]=s*c+i,e[10]=t-r*c}else if("XZY"===t.order){const t=n*a,s=n*h,i=o*a,r=o*h;e[0]=a*l,e[4]=-c,e[8]=h*l,e[1]=t*c+r,e[5]=n*l,e[9]=s*c-i,e[2]=i*c-s,e[6]=o*l,e[10]=r*c+t}return e[3]=0,e[7]=0,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,this}makeRotationFromQuaternion(t){return this.compose(hr,t,lr)}lookAt(t,e,s){const i=this.elements;return dr.subVectors(t,e),0===dr.lengthSq()&&(dr.z=1),dr.normalize(),cr.crossVectors(s,dr),0===cr.lengthSq()&&(1===Math.abs(s.z)?dr.x+=1e-4:dr.z+=1e-4,dr.normalize(),cr.crossVectors(s,dr)),cr.normalize(),ur.crossVectors(dr,cr),i[0]=cr.x,i[4]=ur.x,i[8]=dr.x,i[1]=cr.y,i[5]=ur.y,i[9]=dr.y,i[2]=cr.z,i[6]=ur.z,i[10]=dr.z,this}multiply(t){return this.multiplyMatrices(this,t)}premultiply(t){return this.multiplyMatrices(t,this)}multiplyMatrices(t,e){const s=t.elements,i=e.elements,r=this.elements,n=s[0],o=s[4],a=s[8],h=s[12],l=s[1],c=s[5],u=s[9],d=s[13],p=s[2],m=s[6],y=s[10],f=s[14],g=s[3],x=s[7],b=s[11],v=s[15],w=i[0],M=i[4],S=i[8],_=i[12],A=i[1],T=i[5],z=i[9],C=i[13],I=i[2],B=i[6],k=i[10],R=i[14],E=i[3],P=i[7],O=i[11],F=i[15];return r[0]=n*w+o*A+a*I+h*E,r[4]=n*M+o*T+a*B+h*P,r[8]=n*S+o*z+a*k+h*O,r[12]=n*_+o*C+a*R+h*F,r[1]=l*w+c*A+u*I+d*E,r[5]=l*M+c*T+u*B+d*P,r[9]=l*S+c*z+u*k+d*O,r[13]=l*_+c*C+u*R+d*F,r[2]=p*w+m*A+y*I+f*E,r[6]=p*M+m*T+y*B+f*P,r[10]=p*S+m*z+y*k+f*O,r[14]=p*_+m*C+y*R+f*F,r[3]=g*w+x*A+b*I+v*E,r[7]=g*M+x*T+b*B+v*P,r[11]=g*S+x*z+b*k+v*O,r[15]=g*_+x*C+b*R+v*F,this}multiplyScalar(t){const e=this.elements;return e[0]*=t,e[4]*=t,e[8]*=t,e[12]*=t,e[1]*=t,e[5]*=t,e[9]*=t,e[13]*=t,e[2]*=t,e[6]*=t,e[10]*=t,e[14]*=t,e[3]*=t,e[7]*=t,e[11]*=t,e[15]*=t,this}determinant(){const t=this.elements,e=t[0],s=t[4],i=t[8],r=t[12],n=t[1],o=t[5],a=t[9],h=t[13],l=t[2],c=t[6],u=t[10],d=t[14];return t[3]*(+r*a*c-i*h*c-r*o*u+s*h*u+i*o*d-s*a*d)+t[7]*(+e*a*d-e*h*u+r*n*u-i*n*d+i*h*l-r*a*l)+t[11]*(+e*h*c-e*o*d-r*n*c+s*n*d+r*o*l-s*h*l)+t[15]*(-i*o*l-e*a*c+e*o*u+i*n*c-s*n*u+s*a*l)}transpose(){const t=this.elements;let e;return e=t[1],t[1]=t[4],t[4]=e,e=t[2],t[2]=t[8],t[8]=e,e=t[6],t[6]=t[9],t[9]=e,e=t[3],t[3]=t[12],t[12]=e,e=t[7],t[7]=t[13],t[13]=e,e=t[11],t[11]=t[14],t[14]=e,this}setPosition(t,e,s){const i=this.elements;return t.isVector3?(i[12]=t.x,i[13]=t.y,i[14]=t.z):(i[12]=t,i[13]=e,i[14]=s),this}invert(){const t=this.elements,e=t[0],s=t[1],i=t[2],r=t[3],n=t[4],o=t[5],a=t[6],h=t[7],l=t[8],c=t[9],u=t[10],d=t[11],p=t[12],m=t[13],y=t[14],f=t[15],g=c*y*h-m*u*h+m*a*d-o*y*d-c*a*f+o*u*f,x=p*u*h-l*y*h-p*a*d+n*y*d+l*a*f-n*u*f,b=l*m*h-p*c*h+p*o*d-n*m*d-l*o*f+n*c*f,v=p*c*a-l*m*a-p*o*u+n*m*u+l*o*y-n*c*y,w=e*g+s*x+i*b+r*v;if(0===w)return this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0);const M=1/w;return t[0]=g*M,t[1]=(m*u*r-c*y*r-m*i*d+s*y*d+c*i*f-s*u*f)*M,t[2]=(o*y*r-m*a*r+m*i*h-s*y*h-o*i*f+s*a*f)*M,t[3]=(c*a*r-o*u*r-c*i*h+s*u*h+o*i*d-s*a*d)*M,t[4]=x*M,t[5]=(l*y*r-p*u*r+p*i*d-e*y*d-l*i*f+e*u*f)*M,t[6]=(p*a*r-n*y*r-p*i*h+e*y*h+n*i*f-e*a*f)*M,t[7]=(n*u*r-l*a*r+l*i*h-e*u*h-n*i*d+e*a*d)*M,t[8]=b*M,t[9]=(p*c*r-l*m*r-p*s*d+e*m*d+l*s*f-e*c*f)*M,t[10]=(n*m*r-p*o*r+p*s*h-e*m*h-n*s*f+e*o*f)*M,t[11]=(l*o*r-n*c*r-l*s*h+e*c*h+n*s*d-e*o*d)*M,t[12]=v*M,t[13]=(l*m*i-p*c*i+p*s*u-e*m*u-l*s*y+e*c*y)*M,t[14]=(p*o*i-n*m*i-p*s*a+e*m*a+n*s*y-e*o*y)*M,t[15]=(n*c*i-l*o*i+l*s*a-e*c*a-n*s*u+e*o*u)*M,this}scale(t){const e=this.elements,s=t.x,i=t.y,r=t.z;return e[0]*=s,e[4]*=i,e[8]*=r,e[1]*=s,e[5]*=i,e[9]*=r,e[2]*=s,e[6]*=i,e[10]*=r,e[3]*=s,e[7]*=i,e[11]*=r,this}getMaxScaleOnAxis(){const t=this.elements,e=t[0]*t[0]+t[1]*t[1]+t[2]*t[2],s=t[4]*t[4]+t[5]*t[5]+t[6]*t[6],i=t[8]*t[8]+t[9]*t[9]+t[10]*t[10];return Math.sqrt(Math.max(e,s,i))}makeTranslation(t,e,s){return t.isVector3?this.set(1,0,0,t.x,0,1,0,t.y,0,0,1,t.z,0,0,0,1):this.set(1,0,0,t,0,1,0,e,0,0,1,s,0,0,0,1),this}makeRotationX(t){const e=Math.cos(t),s=Math.sin(t);return this.set(1,0,0,0,0,e,-s,0,0,s,e,0,0,0,0,1),this}makeRotationY(t){const e=Math.cos(t),s=Math.sin(t);return this.set(e,0,s,0,0,1,0,0,-s,0,e,0,0,0,0,1),this}makeRotationZ(t){const e=Math.cos(t),s=Math.sin(t);return this.set(e,-s,0,0,s,e,0,0,0,0,1,0,0,0,0,1),this}makeRotationAxis(t,e){const s=Math.cos(e),i=Math.sin(e),r=1-s,n=t.x,o=t.y,a=t.z,h=r*n,l=r*o;return this.set(h*n+s,h*o-i*a,h*a+i*o,0,h*o+i*a,l*o+s,l*a-i*n,0,h*a-i*o,l*a+i*n,r*a*a+s,0,0,0,0,1),this}makeScale(t,e,s){return this.set(t,0,0,0,0,e,0,0,0,0,s,0,0,0,0,1),this}makeShear(t,e,s,i,r,n){return this.set(1,s,r,0,t,1,n,0,e,i,1,0,0,0,0,1),this}compose(t,e,s){const i=this.elements,r=e._x,n=e._y,o=e._z,a=e._w,h=r+r,l=n+n,c=o+o,u=r*h,d=r*l,p=r*c,m=n*l,y=n*c,f=o*c,g=a*h,x=a*l,b=a*c,v=s.x,w=s.y,M=s.z;return i[0]=(1-(m+f))*v,i[1]=(d+b)*v,i[2]=(p-x)*v,i[3]=0,i[4]=(d-b)*w,i[5]=(1-(u+f))*w,i[6]=(y+g)*w,i[7]=0,i[8]=(p+x)*M,i[9]=(y-g)*M,i[10]=(1-(u+m))*M,i[11]=0,i[12]=t.x,i[13]=t.y,i[14]=t.z,i[15]=1,this}decompose(t,e,s){const i=this.elements;let r=or.set(i[0],i[1],i[2]).length();const n=or.set(i[4],i[5],i[6]).length(),o=or.set(i[8],i[9],i[10]).length();this.determinant()<0&&(r=-r),t.x=i[12],t.y=i[13],t.z=i[14],ar.copy(this);const a=1/r,h=1/n,l=1/o;return ar.elements[0]*=a,ar.elements[1]*=a,ar.elements[2]*=a,ar.elements[4]*=h,ar.elements[5]*=h,ar.elements[6]*=h,ar.elements[8]*=l,ar.elements[9]*=l,ar.elements[10]*=l,e.setFromRotationMatrix(ar),s.x=r,s.y=n,s.z=o,this}makePerspective(t,e,s,i,r,n,o=2e3){const a=this.elements,h=2*r/(e-t),l=2*r/(s-i),c=(e+t)/(e-t),u=(s+i)/(s-i);let d,p;if(o===Os)d=-(n+r)/(n-r),p=-2*n*r/(n-r);else{if(o!==Fs)throw new Error("THREE.Matrix4.makePerspective(): Invalid coordinate system: "+o);d=-n/(n-r),p=-n*r/(n-r)}return a[0]=h,a[4]=0,a[8]=c,a[12]=0,a[1]=0,a[5]=l,a[9]=u,a[13]=0,a[2]=0,a[6]=0,a[10]=d,a[14]=p,a[3]=0,a[7]=0,a[11]=-1,a[15]=0,this}makeOrthographic(t,e,s,i,r,n,o=2e3){const a=this.elements,h=1/(e-t),l=1/(s-i),c=1/(n-r),u=(e+t)*h,d=(s+i)*l;let p,m;if(o===Os)p=(n+r)*c,m=-2*c;else{if(o!==Fs)throw new Error("THREE.Matrix4.makeOrthographic(): Invalid coordinate system: "+o);p=r*c,m=-1*c}return a[0]=2*h,a[4]=0,a[8]=0,a[12]=-u,a[1]=0,a[5]=2*l,a[9]=0,a[13]=-d,a[2]=0,a[6]=0,a[10]=m,a[14]=-p,a[3]=0,a[7]=0,a[11]=0,a[15]=1,this}equals(t){const e=this.elements,s=t.elements;for(let t=0;t<16;t++)if(e[t]!==s[t])return!1;return!0}fromArray(t,e=0){for(let s=0;s<16;s++)this.elements[s]=t[s+e];return this}toArray(t=[],e=0){const s=this.elements;return t[e]=s[0],t[e+1]=s[1],t[e+2]=s[2],t[e+3]=s[3],t[e+4]=s[4],t[e+5]=s[5],t[e+6]=s[6],t[e+7]=s[7],t[e+8]=s[8],t[e+9]=s[9],t[e+10]=s[10],t[e+11]=s[11],t[e+12]=s[12],t[e+13]=s[13],t[e+14]=s[14],t[e+15]=s[15],t}}const or=new Ii,ar=new nr,hr=new Ii(0,0,0),lr=new Ii(1,1,1),cr=new Ii,ur=new Ii,dr=new Ii,pr=new nr,mr=new Ci;class yr{constructor(t=0,e=0,s=0,i=yr.DEFAULT_ORDER){this.isEuler=!0,this._x=t,this._y=e,this._z=s,this._order=i}get x(){return this._x}set x(t){this._x=t,this._onChangeCallback()}get y(){return this._y}set y(t){this._y=t,this._onChangeCallback()}get z(){return this._z}set z(t){this._z=t,this._onChangeCallback()}get order(){return this._order}set order(t){this._order=t,this._onChangeCallback()}set(t,e,s,i=this._order){return this._x=t,this._y=e,this._z=s,this._order=i,this._onChangeCallback(),this}clone(){return new this.constructor(this._x,this._y,this._z,this._order)}copy(t){return this._x=t._x,this._y=t._y,this._z=t._z,this._order=t._order,this._onChangeCallback(),this}setFromRotationMatrix(t,e=this._order,s=!0){const i=t.elements,r=i[0],n=i[4],o=i[8],a=i[1],h=i[5],l=i[9],c=i[2],u=i[6],d=i[10];switch(e){case"XYZ":this._y=Math.asin(Ds(o,-1,1)),Math.abs(o)<.9999999?(this._x=Math.atan2(-l,d),this._z=Math.atan2(-n,r)):(this._x=Math.atan2(u,h),this._z=0);break;case"YXZ":this._x=Math.asin(-Ds(l,-1,1)),Math.abs(l)<.9999999?(this._y=Math.atan2(o,d),this._z=Math.atan2(a,h)):(this._y=Math.atan2(-c,r),this._z=0);break;case"ZXY":this._x=Math.asin(Ds(u,-1,1)),Math.abs(u)<.9999999?(this._y=Math.atan2(-c,d),this._z=Math.atan2(-n,h)):(this._y=0,this._z=Math.atan2(a,r));break;case"ZYX":this._y=Math.asin(-Ds(c,-1,1)),Math.abs(c)<.9999999?(this._x=Math.atan2(u,d),this._z=Math.atan2(a,r)):(this._x=0,this._z=Math.atan2(-n,h));break;case"YZX":this._z=Math.asin(Ds(a,-1,1)),Math.abs(a)<.9999999?(this._x=Math.atan2(-l,h),this._y=Math.atan2(-c,r)):(this._x=0,this._y=Math.atan2(o,d));break;case"XZY":this._z=Math.asin(-Ds(n,-1,1)),Math.abs(n)<.9999999?(this._x=Math.atan2(u,h),this._y=Math.atan2(o,r)):(this._x=Math.atan2(-l,d),this._y=0);break;default:console.warn("THREE.Euler: .setFromRotationMatrix() encountered an unknown order: "+e)}return this._order=e,!0===s&&this._onChangeCallback(),this}setFromQuaternion(t,e,s){return pr.makeRotationFromQuaternion(t),this.setFromRotationMatrix(pr,e,s)}setFromVector3(t,e=this._order){return this.set(t.x,t.y,t.z,e)}reorder(t){return mr.setFromEuler(this),this.setFromQuaternion(mr,t)}equals(t){return t._x===this._x&&t._y===this._y&&t._z===this._z&&t._order===this._order}fromArray(t){return this._x=t[0],this._y=t[1],this._z=t[2],void 0!==t[3]&&(this._order=t[3]),this._onChangeCallback(),this}toArray(t=[],e=0){return t[e]=this._x,t[e+1]=this._y,t[e+2]=this._z,t[e+3]=this._order,t}_onChange(t){return this._onChangeCallback=t,this}_onChangeCallback(){}*[Symbol.iterator](){yield this._x,yield this._y,yield this._z,yield this._order}}yr.DEFAULT_ORDER="XYZ";class fr{constructor(){this.mask=1}set(t){this.mask=1<<t>>>0}enable(t){this.mask|=1<<t}enableAll(){this.mask=-1}toggle(t){this.mask^=1<<t}disable(t){this.mask&=~(1<<t)}disableAll(){this.mask=0}test(t){return!!(this.mask&t.mask)}isEnabled(t){return!!(this.mask&1<<t)}}let gr=0;const xr=new Ii,br=new Ci,vr=new nr,wr=new Ii,Mr=new Ii,Sr=new Ii,_r=new Ci,Ar=new Ii(1,0,0),Tr=new Ii(0,1,0),zr=new Ii(0,0,1),Cr={type:"added"},Ir={type:"removed"},Br={type:"childadded",child:null},kr={type:"childremoved",child:null};class Rr extends Ns{constructor(){super(),this.isObject3D=!0,Object.defineProperty(this,"id",{value:gr++}),this.uuid=Us(),this.name="",this.type="Object3D",this.parent=null,this.children=[],this.up=Rr.DEFAULT_UP.clone();const t=new Ii,e=new yr,s=new Ci,i=new Ii(1,1,1);e._onChange((function(){s.setFromEuler(e,!1)})),s._onChange((function(){e.setFromQuaternion(s,void 0,!1)})),Object.defineProperties(this,{position:{configurable:!0,enumerable:!0,value:t},rotation:{configurable:!0,enumerable:!0,value:e},quaternion:{configurable:!0,enumerable:!0,value:s},scale:{configurable:!0,enumerable:!0,value:i},modelViewMatrix:{value:new nr},normalMatrix:{value:new Gs}}),this.matrix=new nr,this.matrixWorld=new nr,this.matrixAutoUpdate=Rr.DEFAULT_MATRIX_AUTO_UPDATE,this.matrixWorldAutoUpdate=Rr.DEFAULT_MATRIX_WORLD_AUTO_UPDATE,this.matrixWorldNeedsUpdate=!1,this.layers=new fr,this.visible=!0,this.castShadow=!1,this.receiveShadow=!1,this.frustumCulled=!0,this.renderOrder=0,this.animations=[],this.userData={}}onBeforeShadow(){}onAfterShadow(){}onBeforeRender(){}onAfterRender(){}applyMatrix4(t){this.matrixAutoUpdate&&this.updateMatrix(),this.matrix.premultiply(t),this.matrix.decompose(this.position,this.quaternion,this.scale)}applyQuaternion(t){return this.quaternion.premultiply(t),this}setRotationFromAxisAngle(t,e){this.quaternion.setFromAxisAngle(t,e)}setRotationFromEuler(t){this.quaternion.setFromEuler(t,!0)}setRotationFromMatrix(t){this.quaternion.setFromRotationMatrix(t)}setRotationFromQuaternion(t){this.quaternion.copy(t)}rotateOnAxis(t,e){return br.setFromAxisAngle(t,e),this.quaternion.multiply(br),this}rotateOnWorldAxis(t,e){return br.setFromAxisAngle(t,e),this.quaternion.premultiply(br),this}rotateX(t){return this.rotateOnAxis(Ar,t)}rotateY(t){return this.rotateOnAxis(Tr,t)}rotateZ(t){return this.rotateOnAxis(zr,t)}translateOnAxis(t,e){return xr.copy(t).applyQuaternion(this.quaternion),this.position.add(xr.multiplyScalar(e)),this}translateX(t){return this.translateOnAxis(Ar,t)}translateY(t){return this.translateOnAxis(Tr,t)}translateZ(t){return this.translateOnAxis(zr,t)}localToWorld(t){return this.updateWorldMatrix(!0,!1),t.applyMatrix4(this.matrixWorld)}worldToLocal(t){return this.updateWorldMatrix(!0,!1),t.applyMatrix4(vr.copy(this.matrixWorld).invert())}lookAt(t,e,s){t.isVector3?wr.copy(t):wr.set(t,e,s);const i=this.parent;this.updateWorldMatrix(!0,!1),Mr.setFromMatrixPosition(this.matrixWorld),this.isCamera||this.isLight?vr.lookAt(Mr,wr,this.up):vr.lookAt(wr,Mr,this.up),this.quaternion.setFromRotationMatrix(vr),i&&(vr.extractRotation(i.matrixWorld),br.setFromRotationMatrix(vr),this.quaternion.premultiply(br.invert()))}add(t){if(arguments.length>1){for(let t=0;t<arguments.length;t++)this.add(arguments[t]);return this}return t===this?(console.error("THREE.Object3D.add: object can't be added as a child of itself.",t),this):(t&&t.isObject3D?(t.removeFromParent(),t.parent=this,this.children.push(t),t.dispatchEvent(Cr),Br.child=t,this.dispatchEvent(Br),Br.child=null):console.error("THREE.Object3D.add: object not an instance of THREE.Object3D.",t),this)}remove(t){if(arguments.length>1){for(let t=0;t<arguments.length;t++)this.remove(arguments[t]);return this}const e=this.children.indexOf(t);return-1!==e&&(t.parent=null,this.children.splice(e,1),t.dispatchEvent(Ir),kr.child=t,this.dispatchEvent(kr),kr.child=null),this}removeFromParent(){const t=this.parent;return null!==t&&t.remove(this),this}clear(){return this.remove(...this.children)}attach(t){return this.updateWorldMatrix(!0,!1),vr.copy(this.matrixWorld).invert(),null!==t.parent&&(t.parent.updateWorldMatrix(!0,!1),vr.multiply(t.parent.matrixWorld)),t.applyMatrix4(vr),t.removeFromParent(),t.parent=this,this.children.push(t),t.updateWorldMatrix(!1,!0),t.dispatchEvent(Cr),Br.child=t,this.dispatchEvent(Br),Br.child=null,this}getObjectById(t){return this.getObjectByProperty("id",t)}getObjectByName(t){return this.getObjectByProperty("name",t)}getObjectByProperty(t,e){if(this[t]===e)return this;for(let s=0,i=this.children.length;s<i;s++){const i=this.children[s].getObjectByProperty(t,e);if(void 0!==i)return i}}getObjectsByProperty(t,e,s=[]){this[t]===e&&s.push(this);const i=this.children;for(let r=0,n=i.length;r<n;r++)i[r].getObjectsByProperty(t,e,s);return s}getWorldPosition(t){return this.updateWorldMatrix(!0,!1),t.setFromMatrixPosition(this.matrixWorld)}getWorldQuaternion(t){return this.updateWorldMatrix(!0,!1),this.matrixWorld.decompose(Mr,t,Sr),t}getWorldScale(t){return this.updateWorldMatrix(!0,!1),this.matrixWorld.decompose(Mr,_r,t),t}getWorldDirection(t){this.updateWorldMatrix(!0,!1);const e=this.matrixWorld.elements;return t.set(e[8],e[9],e[10]).normalize()}raycast(){}traverse(t){t(this);const e=this.children;for(let s=0,i=e.length;s<i;s++)e[s].traverse(t)}traverseVisible(t){if(!1===this.visible)return;t(this);const e=this.children;for(let s=0,i=e.length;s<i;s++)e[s].traverseVisible(t)}traverseAncestors(t){const e=this.parent;null!==e&&(t(e),e.traverseAncestors(t))}updateMatrix(){this.matrix.compose(this.position,this.quaternion,this.scale),this.matrixWorldNeedsUpdate=!0}updateMatrixWorld(t){this.matrixAutoUpdate&&this.updateMatrix(),(this.matrixWorldNeedsUpdate||t)&&(!0===this.matrixWorldAutoUpdate&&(null===this.parent?this.matrixWorld.copy(this.matrix):this.matrixWorld.multiplyMatrices(this.parent.matrixWorld,this.matrix)),this.matrixWorldNeedsUpdate=!1,t=!0);const e=this.children;for(let s=0,i=e.length;s<i;s++){e[s].updateMatrixWorld(t)}}updateWorldMatrix(t,e){const s=this.parent;if(!0===t&&null!==s&&s.updateWorldMatrix(!0,!1),this.matrixAutoUpdate&&this.updateMatrix(),!0===this.matrixWorldAutoUpdate&&(null===this.parent?this.matrixWorld.copy(this.matrix):this.matrixWorld.multiplyMatrices(this.parent.matrixWorld,this.matrix)),!0===e){const t=this.children;for(let e=0,s=t.length;e<s;e++){t[e].updateWorldMatrix(!1,!0)}}}toJSON(t){const e=void 0===t||"string"==typeof t,s={};e&&(t={geometries:{},materials:{},textures:{},images:{},shapes:{},skeletons:{},animations:{},nodes:{}},s.metadata={version:4.6,type:"Object",generator:"Object3D.toJSON"});const i={};function r(e,s){return void 0===e[s.uuid]&&(e[s.uuid]=s.toJSON(t)),s.uuid}if(i.uuid=this.uuid,i.type=this.type,""!==this.name&&(i.name=this.name),!0===this.castShadow&&(i.castShadow=!0),!0===this.receiveShadow&&(i.receiveShadow=!0),!1===this.visible&&(i.visible=!1),!1===this.frustumCulled&&(i.frustumCulled=!1),0!==this.renderOrder&&(i.renderOrder=this.renderOrder),Object.keys(this.userData).length>0&&(i.userData=this.userData),i.layers=this.layers.mask,i.matrix=this.matrix.toArray(),i.up=this.up.toArray(),!1===this.matrixAutoUpdate&&(i.matrixAutoUpdate=!1),this.isInstancedMesh&&(i.type="InstancedMesh",i.count=this.count,i.instanceMatrix=this.instanceMatrix.toJSON(),null!==this.instanceColor&&(i.instanceColor=this.instanceColor.toJSON())),this.isBatchedMesh&&(i.type="BatchedMesh",i.perObjectFrustumCulled=this.perObjectFrustumCulled,i.sortObjects=this.sortObjects,i.drawRanges=this._drawRanges,i.reservedRanges=this._reservedRanges,i.visibility=this._visibility,i.active=this._active,i.bounds=this._bounds.map((t=>({boxInitialized:t.boxInitialized,boxMin:t.box.min.toArray(),boxMax:t.box.max.toArray(),sphereInitialized:t.sphereInitialized,sphereRadius:t.sphere.radius,sphereCenter:t.sphere.center.toArray()}))),i.maxInstanceCount=this._maxInstanceCount,i.maxVertexCount=this._maxVertexCount,i.maxIndexCount=this._maxIndexCount,i.geometryInitialized=this._geometryInitialized,i.geometryCount=this._geometryCount,i.matricesTexture=this._matricesTexture.toJSON(t),null!==this._colorsTexture&&(i.colorsTexture=this._colorsTexture.toJSON(t)),null!==this.boundingSphere&&(i.boundingSphere={center:i.boundingSphere.center.toArray(),radius:i.boundingSphere.radius}),null!==this.boundingBox&&(i.boundingBox={min:i.boundingBox.min.toArray(),max:i.boundingBox.max.toArray()})),this.isScene)this.background&&(this.background.isColor?i.background=this.background.toJSON():this.background.isTexture&&(i.background=this.background.toJSON(t).uuid)),this.environment&&this.environment.isTexture&&!0!==this.environment.isRenderTargetTexture&&(i.environment=this.environment.toJSON(t).uuid);else if(this.isMesh||this.isLine||this.isPoints){i.geometry=r(t.geometries,this.geometry);const e=this.geometry.parameters;if(void 0!==e&&void 0!==e.shapes){const s=e.shapes;if(Array.isArray(s))for(let e=0,i=s.length;e<i;e++){const i=s[e];r(t.shapes,i)}else r(t.shapes,s)}}if(this.isSkinnedMesh&&(i.bindMode=this.bindMode,i.bindMatrix=this.bindMatrix.toArray(),void 0!==this.skeleton&&(r(t.skeletons,this.skeleton),i.skeleton=this.skeleton.uuid)),void 0!==this.material)if(Array.isArray(this.material)){const e=[];for(let s=0,i=this.material.length;s<i;s++)e.push(r(t.materials,this.material[s]));i.material=e}else i.material=r(t.materials,this.material);if(this.children.length>0){i.children=[];for(let e=0;e<this.children.length;e++)i.children.push(this.children[e].toJSON(t).object)}if(this.animations.length>0){i.animations=[];for(let e=0;e<this.animations.length;e++){const s=this.animations[e];i.animations.push(r(t.animations,s))}}if(e){const e=n(t.geometries),i=n(t.materials),r=n(t.textures),o=n(t.images),a=n(t.shapes),h=n(t.skeletons),l=n(t.animations),c=n(t.nodes);e.length>0&&(s.geometries=e),i.length>0&&(s.materials=i),r.length>0&&(s.textures=r),o.length>0&&(s.images=o),a.length>0&&(s.shapes=a),h.length>0&&(s.skeletons=h),l.length>0&&(s.animations=l),c.length>0&&(s.nodes=c)}return s.object=i,s;function n(t){const e=[];for(const s in t){const i=t[s];delete i.metadata,e.push(i)}return e}}clone(t){return(new this.constructor).copy(this,t)}copy(t,e=!0){if(this.name=t.name,this.up.copy(t.up),this.position.copy(t.position),this.rotation.order=t.rotation.order,this.quaternion.copy(t.quaternion),this.scale.copy(t.scale),this.matrix.copy(t.matrix),this.matrixWorld.copy(t.matrixWorld),this.matrixAutoUpdate=t.matrixAutoUpdate,this.matrixWorldAutoUpdate=t.matrixWorldAutoUpdate,this.matrixWorldNeedsUpdate=t.matrixWorldNeedsUpdate,this.layers.mask=t.layers.mask,this.visible=t.visible,this.castShadow=t.castShadow,this.receiveShadow=t.receiveShadow,this.frustumCulled=t.frustumCulled,this.renderOrder=t.renderOrder,this.animations=t.animations.slice(),this.userData=JSON.parse(JSON.stringify(t.userData)),!0===e)for(let e=0;e<t.children.length;e++){const s=t.children[e];this.add(s.clone())}return this}}Rr.DEFAULT_UP=new Ii(0,1,0),Rr.DEFAULT_MATRIX_AUTO_UPDATE=!0,Rr.DEFAULT_MATRIX_WORLD_AUTO_UPDATE=!0;const Er=new Ii,Pr=new Ii,Or=new Ii,Fr=new Ii,Nr=new Ii,Lr=new Ii,Vr=new Ii,Wr=new Ii,jr=new Ii,Ur=new Ii,Dr=new wi,Hr=new wi,qr=new wi;class Jr{constructor(t=new Ii,e=new Ii,s=new Ii){this.a=t,this.b=e,this.c=s}static getNormal(t,e,s,i){i.subVectors(s,e),Er.subVectors(t,e),i.cross(Er);const r=i.lengthSq();return r>0?i.multiplyScalar(1/Math.sqrt(r)):i.set(0,0,0)}static getBarycoord(t,e,s,i,r){Er.subVectors(i,e),Pr.subVectors(s,e),Or.subVectors(t,e);const n=Er.dot(Er),o=Er.dot(Pr),a=Er.dot(Or),h=Pr.dot(Pr),l=Pr.dot(Or),c=n*h-o*o;if(0===c)return r.set(0,0,0),null;const u=1/c,d=(h*a-o*l)*u,p=(n*l-o*a)*u;return r.set(1-d-p,p,d)}static containsPoint(t,e,s,i){return null!==this.getBarycoord(t,e,s,i,Fr)&&(Fr.x>=0&&Fr.y>=0&&Fr.x+Fr.y<=1)}static getInterpolation(t,e,s,i,r,n,o,a){return null===this.getBarycoord(t,e,s,i,Fr)?(a.x=0,a.y=0,"z"in a&&(a.z=0),"w"in a&&(a.w=0),null):(a.setScalar(0),a.addScaledVector(r,Fr.x),a.addScaledVector(n,Fr.y),a.addScaledVector(o,Fr.z),a)}static getInterpolatedAttribute(t,e,s,i,r,n){return Dr.setScalar(0),Hr.setScalar(0),qr.setScalar(0),Dr.fromBufferAttribute(t,e),Hr.fromBufferAttribute(t,s),qr.fromBufferAttribute(t,i),n.setScalar(0),n.addScaledVector(Dr,r.x),n.addScaledVector(Hr,r.y),n.addScaledVector(qr,r.z),n}static isFrontFacing(t,e,s,i){return Er.subVectors(s,e),Pr.subVectors(t,e),Er.cross(Pr).dot(i)<0}set(t,e,s){return this.a.copy(t),this.b.copy(e),this.c.copy(s),this}setFromPointsAndIndices(t,e,s,i){return this.a.copy(t[e]),this.b.copy(t[s]),this.c.copy(t[i]),this}setFromAttributeAndIndices(t,e,s,i){return this.a.fromBufferAttribute(t,e),this.b.fromBufferAttribute(t,s),this.c.fromBufferAttribute(t,i),this}clone(){return(new this.constructor).copy(this)}copy(t){return this.a.copy(t.a),this.b.copy(t.b),this.c.copy(t.c),this}getArea(){return Er.subVectors(this.c,this.b),Pr.subVectors(this.a,this.b),.5*Er.cross(Pr).length()}getMidpoint(t){return t.addVectors(this.a,this.b).add(this.c).multiplyScalar(1/3)}getNormal(t){return Jr.getNormal(this.a,this.b,this.c,t)}getPlane(t){return t.setFromCoplanarPoints(this.a,this.b,this.c)}getBarycoord(t,e){return Jr.getBarycoord(t,this.a,this.b,this.c,e)}getInterpolation(t,e,s,i,r){return Jr.getInterpolation(t,this.a,this.b,this.c,e,s,i,r)}containsPoint(t){return Jr.containsPoint(t,this.a,this.b,this.c)}isFrontFacing(t){return Jr.isFrontFacing(this.a,this.b,this.c,t)}intersectsBox(t){return t.intersectsTriangle(this)}closestPointToPoint(t,e){const s=this.a,i=this.b,r=this.c;let n,o;Nr.subVectors(i,s),Lr.subVectors(r,s),Wr.subVectors(t,s);const a=Nr.dot(Wr),h=Lr.dot(Wr);if(a<=0&&h<=0)return e.copy(s);jr.subVectors(t,i);const l=Nr.dot(jr),c=Lr.dot(jr);if(l>=0&&c<=l)return e.copy(i);const u=a*c-l*h;if(u<=0&&a>=0&&l<=0)return n=a/(a-l),e.copy(s).addScaledVector(Nr,n);Ur.subVectors(t,r);const d=Nr.dot(Ur),p=Lr.dot(Ur);if(p>=0&&d<=p)return e.copy(r);const m=d*h-a*p;if(m<=0&&h>=0&&p<=0)return o=h/(h-p),e.copy(s).addScaledVector(Lr,o);const y=l*p-d*c;if(y<=0&&c-l>=0&&d-p>=0)return Vr.subVectors(r,i),o=(c-l)/(c-l+(d-p)),e.copy(i).addScaledVector(Vr,o);const f=1/(y+m+u);return n=m*f,o=u*f,e.copy(s).addScaledVector(Nr,n).addScaledVector(Lr,o)}equals(t){return t.a.equals(this.a)&&t.b.equals(this.b)&&t.c.equals(this.c)}}const Xr={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074},Yr={h:0,s:0,l:0},Zr={h:0,s:0,l:0};function Gr(t,e,s){return s<0&&(s+=1),s>1&&(s-=1),s<1/6?t+6*(e-t)*s:s<.5?e:s<2/3?t+6*(e-t)*(2/3-s):t}class $r{constructor(t,e,s){return this.isColor=!0,this.r=1,this.g=1,this.b=1,this.set(t,e,s)}set(t,e,s){if(void 0===e&&void 0===s){const e=t;e&&e.isColor?this.copy(e):"number"==typeof e?this.setHex(e):"string"==typeof e&&this.setStyle(e)}else this.setRGB(t,e,s);return this}setScalar(t){return this.r=t,this.g=t,this.b=t,this}setHex(t,e=Ge){return t=Math.floor(t),this.r=(t>>16&255)/255,this.g=(t>>8&255)/255,this.b=(255&t)/255,ui.toWorkingColorSpace(this,e),this}setRGB(t,e,s,i=ui.workingColorSpace){return this.r=t,this.g=e,this.b=s,ui.toWorkingColorSpace(this,i),this}setHSL(t,e,s,i=ui.workingColorSpace){if(t=Hs(t,1),e=Ds(e,0,1),s=Ds(s,0,1),0===e)this.r=this.g=this.b=s;else{const i=s<=.5?s*(1+e):s+e-s*e,r=2*s-i;this.r=Gr(r,i,t+1/3),this.g=Gr(r,i,t),this.b=Gr(r,i,t-1/3)}return ui.toWorkingColorSpace(this,i),this}setStyle(t,e=Ge){function s(e){void 0!==e&&parseFloat(e)<1&&console.warn("THREE.Color: Alpha component of "+t+" will be ignored.")}let i;if(i=/^(\w+)\(([^\)]*)\)/.exec(t)){let r;const n=i[1],o=i[2];switch(n){case"rgb":case"rgba":if(r=/^\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(o))return s(r[4]),this.setRGB(Math.min(255,parseInt(r[1],10))/255,Math.min(255,parseInt(r[2],10))/255,Math.min(255,parseInt(r[3],10))/255,e);if(r=/^\s*(\d+)\%\s*,\s*(\d+)\%\s*,\s*(\d+)\%\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(o))return s(r[4]),this.setRGB(Math.min(100,parseInt(r[1],10))/100,Math.min(100,parseInt(r[2],10))/100,Math.min(100,parseInt(r[3],10))/100,e);break;case"hsl":case"hsla":if(r=/^\s*(\d*\.?\d+)\s*,\s*(\d*\.?\d+)\%\s*,\s*(\d*\.?\d+)\%\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(o))return s(r[4]),this.setHSL(parseFloat(r[1])/360,parseFloat(r[2])/100,parseFloat(r[3])/100,e);break;default:console.warn("THREE.Color: Unknown color model "+t)}}else if(i=/^\#([A-Fa-f\d]+)$/.exec(t)){const s=i[1],r=s.length;if(3===r)return this.setRGB(parseInt(s.charAt(0),16)/15,parseInt(s.charAt(1),16)/15,parseInt(s.charAt(2),16)/15,e);if(6===r)return this.setHex(parseInt(s,16),e);console.warn("THREE.Color: Invalid hex color "+t)}else if(t&&t.length>0)return this.setColorName(t,e);return this}setColorName(t,e=Ge){const s=Xr[t.toLowerCase()];return void 0!==s?this.setHex(s,e):console.warn("THREE.Color: Unknown color "+t),this}clone(){return new this.constructor(this.r,this.g,this.b)}copy(t){return this.r=t.r,this.g=t.g,this.b=t.b,this}copySRGBToLinear(t){return this.r=di(t.r),this.g=di(t.g),this.b=di(t.b),this}copyLinearToSRGB(t){return this.r=pi(t.r),this.g=pi(t.g),this.b=pi(t.b),this}convertSRGBToLinear(){return this.copySRGBToLinear(this),this}convertLinearToSRGB(){return this.copyLinearToSRGB(this),this}getHex(t=Ge){return ui.fromWorkingColorSpace(Qr.copy(this),t),65536*Math.round(Ds(255*Qr.r,0,255))+256*Math.round(Ds(255*Qr.g,0,255))+Math.round(Ds(255*Qr.b,0,255))}getHexString(t=Ge){return("000000"+this.getHex(t).toString(16)).slice(-6)}getHSL(t,e=ui.workingColorSpace){ui.fromWorkingColorSpace(Qr.copy(this),e);const s=Qr.r,i=Qr.g,r=Qr.b,n=Math.max(s,i,r),o=Math.min(s,i,r);let a,h;const l=(o+n)/2;if(o===n)a=0,h=0;else{const t=n-o;switch(h=l<=.5?t/(n+o):t/(2-n-o),n){case s:a=(i-r)/t+(i<r?6:0);break;case i:a=(r-s)/t+2;break;case r:a=(s-i)/t+4}a/=6}return t.h=a,t.s=h,t.l=l,t}getRGB(t,e=ui.workingColorSpace){return ui.fromWorkingColorSpace(Qr.copy(this),e),t.r=Qr.r,t.g=Qr.g,t.b=Qr.b,t}getStyle(t=Ge){ui.fromWorkingColorSpace(Qr.copy(this),t);const e=Qr.r,s=Qr.g,i=Qr.b;return t!==Ge?`color(${t} ${e.toFixed(3)} ${s.toFixed(3)} ${i.toFixed(3)})`:`rgb(${Math.round(255*e)},${Math.round(255*s)},${Math.round(255*i)})`}offsetHSL(t,e,s){return this.getHSL(Yr),this.setHSL(Yr.h+t,Yr.s+e,Yr.l+s)}add(t){return this.r+=t.r,this.g+=t.g,this.b+=t.b,this}addColors(t,e){return this.r=t.r+e.r,this.g=t.g+e.g,this.b=t.b+e.b,this}addScalar(t){return this.r+=t,this.g+=t,this.b+=t,this}sub(t){return this.r=Math.max(0,this.r-t.r),this.g=Math.max(0,this.g-t.g),this.b=Math.max(0,this.b-t.b),this}multiply(t){return this.r*=t.r,this.g*=t.g,this.b*=t.b,this}multiplyScalar(t){return this.r*=t,this.g*=t,this.b*=t,this}lerp(t,e){return this.r+=(t.r-this.r)*e,this.g+=(t.g-this.g)*e,this.b+=(t.b-this.b)*e,this}lerpColors(t,e,s){return this.r=t.r+(e.r-t.r)*s,this.g=t.g+(e.g-t.g)*s,this.b=t.b+(e.b-t.b)*s,this}lerpHSL(t,e){this.getHSL(Yr),t.getHSL(Zr);const s=qs(Yr.h,Zr.h,e),i=qs(Yr.s,Zr.s,e),r=qs(Yr.l,Zr.l,e);return this.setHSL(s,i,r),this}setFromVector3(t){return this.r=t.x,this.g=t.y,this.b=t.z,this}applyMatrix3(t){const e=this.r,s=this.g,i=this.b,r=t.elements;return this.r=r[0]*e+r[3]*s+r[6]*i,this.g=r[1]*e+r[4]*s+r[7]*i,this.b=r[2]*e+r[5]*s+r[8]*i,this}equals(t){return t.r===this.r&&t.g===this.g&&t.b===this.b}fromArray(t,e=0){return this.r=t[e],this.g=t[e+1],this.b=t[e+2],this}toArray(t=[],e=0){return t[e]=this.r,t[e+1]=this.g,t[e+2]=this.b,t}fromBufferAttribute(t,e){return this.r=t.getX(e),this.g=t.getY(e),this.b=t.getZ(e),this}toJSON(){return this.getHex()}*[Symbol.iterator](){yield this.r,yield this.g,yield this.b}}const Qr=new $r;$r.NAMES=Xr;let Kr=0;class tn extends Ns{constructor(){super(),this.isMaterial=!0,Object.defineProperty(this,"id",{value:Kr++}),this.uuid=Us(),this.name="",this.type="Material",this.blending=1,this.side=0,this.vertexColors=!1,this.opacity=1,this.transparent=!1,this.alphaHash=!1,this.blendSrc=204,this.blendDst=205,this.blendEquation=100,this.blendSrcAlpha=null,this.blendDstAlpha=null,this.blendEquationAlpha=null,this.blendColor=new $r(0,0,0),this.blendAlpha=0,this.depthFunc=3,this.depthTest=!0,this.depthWrite=!0,this.stencilWriteMask=255,this.stencilFunc=519,this.stencilRef=0,this.stencilFuncMask=255,this.stencilFail=es,this.stencilZFail=es,this.stencilZPass=es,this.stencilWrite=!1,this.clippingPlanes=null,this.clipIntersection=!1,this.clipShadows=!1,this.shadowSide=null,this.colorWrite=!0,this.precision=null,this.polygonOffset=!1,this.polygonOffsetFactor=0,this.polygonOffsetUnits=0,this.dithering=!1,this.alphaToCoverage=!1,this.premultipliedAlpha=!1,this.forceSinglePass=!1,this.visible=!0,this.toneMapped=!0,this.userData={},this.version=0,this._alphaTest=0}get alphaTest(){return this._alphaTest}set alphaTest(t){this._alphaTest>0!=t>0&&this.version++,this._alphaTest=t}onBeforeRender(){}onBeforeCompile(){}customProgramCacheKey(){return this.onBeforeCompile.toString()}setValues(t){if(void 0!==t)for(const e in t){const s=t[e];if(void 0===s){console.warn(`THREE.Material: parameter '${e}' has value of undefined.`);continue}const i=this[e];void 0!==i?i&&i.isColor?i.set(s):i&&i.isVector3&&s&&s.isVector3?i.copy(s):this[e]=s:console.warn(`THREE.Material: '${e}' is not a property of THREE.${this.type}.`)}}toJSON(t){const e=void 0===t||"string"==typeof t;e&&(t={textures:{},images:{}});const s={metadata:{version:4.6,type:"Material",generator:"Material.toJSON"}};function i(t){const e=[];for(const s in t){const i=t[s];delete i.metadata,e.push(i)}return e}if(s.uuid=this.uuid,s.type=this.type,""!==this.name&&(s.name=this.name),this.color&&this.color.isColor&&(s.color=this.color.getHex()),void 0!==this.roughness&&(s.roughness=this.roughness),void 0!==this.metalness&&(s.metalness=this.metalness),void 0!==this.sheen&&(s.sheen=this.sheen),this.sheenColor&&this.sheenColor.isColor&&(s.sheenColor=this.sheenColor.getHex()),void 0!==this.sheenRoughness&&(s.sheenRoughness=this.sheenRoughness),this.emissive&&this.emissive.isColor&&(s.emissive=this.emissive.getHex()),void 0!==this.emissiveIntensity&&1!==this.emissiveIntensity&&(s.emissiveIntensity=this.emissiveIntensity),this.specular&&this.specular.isColor&&(s.specular=this.specular.getHex()),void 0!==this.specularIntensity&&(s.specularIntensity=this.specularIntensity),this.specularColor&&this.specularColor.isColor&&(s.specularColor=this.specularColor.getHex()),void 0!==this.shininess&&(s.shininess=this.shininess),void 0!==this.clearcoat&&(s.clearcoat=this.clearcoat),void 0!==this.clearcoatRoughness&&(s.clearcoatRoughness=this.clearcoatRoughness),this.clearcoatMap&&this.clearcoatMap.isTexture&&(s.clearcoatMap=this.clearcoatMap.toJSON(t).uuid),this.clearcoatRoughnessMap&&this.clearcoatRoughnessMap.isTexture&&(s.clearcoatRoughnessMap=this.clearcoatRoughnessMap.toJSON(t).uuid),this.clearcoatNormalMap&&this.clearcoatNormalMap.isTexture&&(s.clearcoatNormalMap=this.clearcoatNormalMap.toJSON(t).uuid,s.clearcoatNormalScale=this.clearcoatNormalScale.toArray()),void 0!==this.dispersion&&(s.dispersion=this.dispersion),void 0!==this.iridescence&&(s.iridescence=this.iridescence),void 0!==this.iridescenceIOR&&(s.iridescenceIOR=this.iridescenceIOR),void 0!==this.iridescenceThicknessRange&&(s.iridescenceThicknessRange=this.iridescenceThicknessRange),this.iridescenceMap&&this.iridescenceMap.isTexture&&(s.iridescenceMap=this.iridescenceMap.toJSON(t).uuid),this.iridescenceThicknessMap&&this.iridescenceThicknessMap.isTexture&&(s.iridescenceThicknessMap=this.iridescenceThicknessMap.toJSON(t).uuid),void 0!==this.anisotropy&&(s.anisotropy=this.anisotropy),void 0!==this.anisotropyRotation&&(s.anisotropyRotation=this.anisotropyRotation),this.anisotropyMap&&this.anisotropyMap.isTexture&&(s.anisotropyMap=this.anisotropyMap.toJSON(t).uuid),this.map&&this.map.isTexture&&(s.map=this.map.toJSON(t).uuid),this.matcap&&this.matcap.isTexture&&(s.matcap=this.matcap.toJSON(t).uuid),this.alphaMap&&this.alphaMap.isTexture&&(s.alphaMap=this.alphaMap.toJSON(t).uuid),this.lightMap&&this.lightMap.isTexture&&(s.lightMap=this.lightMap.toJSON(t).uuid,s.lightMapIntensity=this.lightMapIntensity),this.aoMap&&this.aoMap.isTexture&&(s.aoMap=this.aoMap.toJSON(t).uuid,s.aoMapIntensity=this.aoMapIntensity),this.bumpMap&&this.bumpMap.isTexture&&(s.bumpMap=this.bumpMap.toJSON(t).uuid,s.bumpScale=this.bumpScale),this.normalMap&&this.normalMap.isTexture&&(s.normalMap=this.normalMap.toJSON(t).uuid,s.normalMapType=this.normalMapType,s.normalScale=this.normalScale.toArray()),this.displacementMap&&this.displacementMap.isTexture&&(s.displacementMap=this.displacementMap.toJSON(t).uuid,s.displacementScale=this.displacementScale,s.displacementBias=this.displacementBias),this.roughnessMap&&this.roughnessMap.isTexture&&(s.roughnessMap=this.roughnessMap.toJSON(t).uuid),this.metalnessMap&&this.metalnessMap.isTexture&&(s.metalnessMap=this.metalnessMap.toJSON(t).uuid),this.emissiveMap&&this.emissiveMap.isTexture&&(s.emissiveMap=this.emissiveMap.toJSON(t).uuid),this.specularMap&&this.specularMap.isTexture&&(s.specularMap=this.specularMap.toJSON(t).uuid),this.specularIntensityMap&&this.specularIntensityMap.isTexture&&(s.specularIntensityMap=this.specularIntensityMap.toJSON(t).uuid),this.specularColorMap&&this.specularColorMap.isTexture&&(s.specularColorMap=this.specularColorMap.toJSON(t).uuid),this.envMap&&this.envMap.isTexture&&(s.envMap=this.envMap.toJSON(t).uuid,void 0!==this.combine&&(s.combine=this.combine)),void 0!==this.envMapRotation&&(s.envMapRotation=this.envMapRotation.toArray()),void 0!==this.envMapIntensity&&(s.envMapIntensity=this.envMapIntensity),void 0!==this.reflectivity&&(s.reflectivity=this.reflectivity),void 0!==this.refractionRatio&&(s.refractionRatio=this.refractionRatio),this.gradientMap&&this.gradientMap.isTexture&&(s.gradientMap=this.gradientMap.toJSON(t).uuid),void 0!==this.transmission&&(s.transmission=this.transmission),this.transmissionMap&&this.transmissionMap.isTexture&&(s.transmissionMap=this.transmissionMap.toJSON(t).uuid),void 0!==this.thickness&&(s.thickness=this.thickness),this.thicknessMap&&this.thicknessMap.isTexture&&(s.thicknessMap=this.thicknessMap.toJSON(t).uuid),void 0!==this.attenuationDistance&&this.attenuationDistance!==1/0&&(s.attenuationDistance=this.attenuationDistance),void 0!==this.attenuationColor&&(s.attenuationColor=this.attenuationColor.getHex()),void 0!==this.size&&(s.size=this.size),null!==this.shadowSide&&(s.shadowSide=this.shadowSide),void 0!==this.sizeAttenuation&&(s.sizeAttenuation=this.sizeAttenuation),1!==this.blending&&(s.blending=this.blending),0!==this.side&&(s.side=this.side),!0===this.vertexColors&&(s.vertexColors=!0),this.opacity<1&&(s.opacity=this.opacity),!0===this.transparent&&(s.transparent=!0),204!==this.blendSrc&&(s.blendSrc=this.blendSrc),205!==this.blendDst&&(s.blendDst=this.blendDst),100!==this.blendEquation&&(s.blendEquation=this.blendEquation),null!==this.blendSrcAlpha&&(s.blendSrcAlpha=this.blendSrcAlpha),null!==this.blendDstAlpha&&(s.blendDstAlpha=this.blendDstAlpha),null!==this.blendEquationAlpha&&(s.blendEquationAlpha=this.blendEquationAlpha),this.blendColor&&this.blendColor.isColor&&(s.blendColor=this.blendColor.getHex()),0!==this.blendAlpha&&(s.blendAlpha=this.blendAlpha),3!==this.depthFunc&&(s.depthFunc=this.depthFunc),!1===this.depthTest&&(s.depthTest=this.depthTest),!1===this.depthWrite&&(s.depthWrite=this.depthWrite),!1===this.colorWrite&&(s.colorWrite=this.colorWrite),255!==this.stencilWriteMask&&(s.stencilWriteMask=this.stencilWriteMask),519!==this.stencilFunc&&(s.stencilFunc=this.stencilFunc),0!==this.stencilRef&&(s.stencilRef=this.stencilRef),255!==this.stencilFuncMask&&(s.stencilFuncMask=this.stencilFuncMask),this.stencilFail!==es&&(s.stencilFail=this.stencilFail),this.stencilZFail!==es&&(s.stencilZFail=this.stencilZFail),this.stencilZPass!==es&&(s.stencilZPass=this.stencilZPass),!0===this.stencilWrite&&(s.stencilWrite=this.stencilWrite),void 0!==this.rotation&&0!==this.rotation&&(s.rotation=this.rotation),!0===this.polygonOffset&&(s.polygonOffset=!0),0!==this.polygonOffsetFactor&&(s.polygonOffsetFactor=this.polygonOffsetFactor),0!==this.polygonOffsetUnits&&(s.polygonOffsetUnits=this.polygonOffsetUnits),void 0!==this.linewidth&&1!==this.linewidth&&(s.linewidth=this.linewidth),void 0!==this.dashSize&&(s.dashSize=this.dashSize),void 0!==this.gapSize&&(s.gapSize=this.gapSize),void 0!==this.scale&&(s.scale=this.scale),!0===this.dithering&&(s.dithering=!0),this.alphaTest>0&&(s.alphaTest=this.alphaTest),!0===this.alphaHash&&(s.alphaHash=!0),!0===this.alphaToCoverage&&(s.alphaToCoverage=!0),!0===this.premultipliedAlpha&&(s.premultipliedAlpha=!0),!0===this.forceSinglePass&&(s.forceSinglePass=!0),!0===this.wireframe&&(s.wireframe=!0),this.wireframeLinewidth>1&&(s.wireframeLinewidth=this.wireframeLinewidth),"round"!==this.wireframeLinecap&&(s.wireframeLinecap=this.wireframeLinecap),"round"!==this.wireframeLinejoin&&(s.wireframeLinejoin=this.wireframeLinejoin),!0===this.flatShading&&(s.flatShading=!0),!1===this.visible&&(s.visible=!1),!1===this.toneMapped&&(s.toneMapped=!1),!1===this.fog&&(s.fog=!1),Object.keys(this.userData).length>0&&(s.userData=this.userData),e){const e=i(t.textures),r=i(t.images);e.length>0&&(s.textures=e),r.length>0&&(s.images=r)}return s}clone(){return(new this.constructor).copy(this)}copy(t){this.name=t.name,this.blending=t.blending,this.side=t.side,this.vertexColors=t.vertexColors,this.opacity=t.opacity,this.transparent=t.transparent,this.blendSrc=t.blendSrc,this.blendDst=t.blendDst,this.blendEquation=t.blendEquation,this.blendSrcAlpha=t.blendSrcAlpha,this.blendDstAlpha=t.blendDstAlpha,this.blendEquationAlpha=t.blendEquationAlpha,this.blendColor.copy(t.blendColor),this.blendAlpha=t.blendAlpha,this.depthFunc=t.depthFunc,this.depthTest=t.depthTest,this.depthWrite=t.depthWrite,this.stencilWriteMask=t.stencilWriteMask,this.stencilFunc=t.stencilFunc,this.stencilRef=t.stencilRef,this.stencilFuncMask=t.stencilFuncMask,this.stencilFail=t.stencilFail,this.stencilZFail=t.stencilZFail,this.stencilZPass=t.stencilZPass,this.stencilWrite=t.stencilWrite;const e=t.clippingPlanes;let s=null;if(null!==e){const t=e.length;s=new Array(t);for(let i=0;i!==t;++i)s[i]=e[i].clone()}return this.clippingPlanes=s,this.clipIntersection=t.clipIntersection,this.clipShadows=t.clipShadows,this.shadowSide=t.shadowSide,this.colorWrite=t.colorWrite,this.precision=t.precision,this.polygonOffset=t.polygonOffset,this.polygonOffsetFactor=t.polygonOffsetFactor,this.polygonOffsetUnits=t.polygonOffsetUnits,this.dithering=t.dithering,this.alphaTest=t.alphaTest,this.alphaHash=t.alphaHash,this.alphaToCoverage=t.alphaToCoverage,this.premultipliedAlpha=t.premultipliedAlpha,this.forceSinglePass=t.forceSinglePass,this.visible=t.visible,this.toneMapped=t.toneMapped,this.userData=JSON.parse(JSON.stringify(t.userData)),this}dispose(){this.dispatchEvent({type:"dispose"})}set needsUpdate(t){!0===t&&this.version++}onBuild(){console.warn("Material: onBuild() has been removed.")}}class en extends tn{constructor(t){super(),this.isMeshBasicMaterial=!0,this.type="MeshBasicMaterial",this.color=new $r(16777215),this.map=null,this.lightMap=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.specularMap=null,this.alphaMap=null,this.envMap=null,this.envMapRotation=new yr,this.combine=0,this.reflectivity=1,this.refractionRatio=.98,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.specularMap=t.specularMap,this.alphaMap=t.alphaMap,this.envMap=t.envMap,this.envMapRotation.copy(t.envMapRotation),this.combine=t.combine,this.reflectivity=t.reflectivity,this.refractionRatio=t.refractionRatio,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.fog=t.fog,this}}const sn=rn();function rn(){const t=new ArrayBuffer(4),e=new Float32Array(t),s=new Uint32Array(t),i=new Uint32Array(512),r=new Uint32Array(512);for(let t=0;t<256;++t){const e=t-127;e<-27?(i[t]=0,i[256|t]=32768,r[t]=24,r[256|t]=24):e<-14?(i[t]=1024>>-e-14,i[256|t]=1024>>-e-14|32768,r[t]=-e-1,r[256|t]=-e-1):e<=15?(i[t]=e+15<<10,i[256|t]=e+15<<10|32768,r[t]=13,r[256|t]=13):e<128?(i[t]=31744,i[256|t]=64512,r[t]=24,r[256|t]=24):(i[t]=31744,i[256|t]=64512,r[t]=13,r[256|t]=13)}const n=new Uint32Array(2048),o=new Uint32Array(64),a=new Uint32Array(64);for(let t=1;t<1024;++t){let e=t<<13,s=0;for(;!(8388608&e);)e<<=1,s-=8388608;e&=-8388609,s+=947912704,n[t]=e|s}for(let t=1024;t<2048;++t)n[t]=939524096+(t-1024<<13);for(let t=1;t<31;++t)o[t]=t<<23;o[31]=1199570944,o[32]=2147483648;for(let t=33;t<63;++t)o[t]=2147483648+(t-32<<23);o[63]=3347054592;for(let t=1;t<64;++t)32!==t&&(a[t]=1024);return{floatView:e,uint32View:s,baseTable:i,shiftTable:r,mantissaTable:n,exponentTable:o,offsetTable:a}}function nn(t){Math.abs(t)>65504&&console.warn("THREE.DataUtils.toHalfFloat(): Value out of range."),t=Ds(t,-65504,65504),sn.floatView[0]=t;const e=sn.uint32View[0],s=e>>23&511;return sn.baseTable[s]+((8388607&e)>>sn.shiftTable[s])}function on(t){const e=t>>10;return sn.uint32View[0]=sn.mantissaTable[sn.offsetTable[e]+(1023&t)]+sn.exponentTable[e],sn.floatView[0]}const an={toHalfFloat:nn,fromHalfFloat:on},hn=new Ii,ln=new Zs;class cn{constructor(t,e,s=!1){if(Array.isArray(t))throw new TypeError("THREE.BufferAttribute: array should be a Typed Array.");this.isBufferAttribute=!0,this.name="",this.array=t,this.itemSize=e,this.count=void 0!==t?t.length/e:0,this.normalized=s,this.usage=_s,this.updateRanges=[],this.gpuType=Rt,this.version=0}onUploadCallback(){}set needsUpdate(t){!0===t&&this.version++}setUsage(t){return this.usage=t,this}addUpdateRange(t,e){this.updateRanges.push({start:t,count:e})}clearUpdateRanges(){this.updateRanges.length=0}copy(t){return this.name=t.name,this.array=new t.array.constructor(t.array),this.itemSize=t.itemSize,this.count=t.count,this.normalized=t.normalized,this.usage=t.usage,this.gpuType=t.gpuType,this}copyAt(t,e,s){t*=this.itemSize,s*=e.itemSize;for(let i=0,r=this.itemSize;i<r;i++)this.array[t+i]=e.array[s+i];return this}copyArray(t){return this.array.set(t),this}applyMatrix3(t){if(2===this.itemSize)for(let e=0,s=this.count;e<s;e++)ln.fromBufferAttribute(this,e),ln.applyMatrix3(t),this.setXY(e,ln.x,ln.y);else if(3===this.itemSize)for(let e=0,s=this.count;e<s;e++)hn.fromBufferAttribute(this,e),hn.applyMatrix3(t),this.setXYZ(e,hn.x,hn.y,hn.z);return this}applyMatrix4(t){for(let e=0,s=this.count;e<s;e++)hn.fromBufferAttribute(this,e),hn.applyMatrix4(t),this.setXYZ(e,hn.x,hn.y,hn.z);return this}applyNormalMatrix(t){for(let e=0,s=this.count;e<s;e++)hn.fromBufferAttribute(this,e),hn.applyNormalMatrix(t),this.setXYZ(e,hn.x,hn.y,hn.z);return this}transformDirection(t){for(let e=0,s=this.count;e<s;e++)hn.fromBufferAttribute(this,e),hn.transformDirection(t),this.setXYZ(e,hn.x,hn.y,hn.z);return this}set(t,e=0){return this.array.set(t,e),this}getComponent(t,e){let s=this.array[t*this.itemSize+e];return this.normalized&&(s=Js(s,this.array)),s}setComponent(t,e,s){return this.normalized&&(s=Xs(s,this.array)),this.array[t*this.itemSize+e]=s,this}getX(t){let e=this.array[t*this.itemSize];return this.normalized&&(e=Js(e,this.array)),e}setX(t,e){return this.normalized&&(e=Xs(e,this.array)),this.array[t*this.itemSize]=e,this}getY(t){let e=this.array[t*this.itemSize+1];return this.normalized&&(e=Js(e,this.array)),e}setY(t,e){return this.normalized&&(e=Xs(e,this.array)),this.array[t*this.itemSize+1]=e,this}getZ(t){let e=this.array[t*this.itemSize+2];return this.normalized&&(e=Js(e,this.array)),e}setZ(t,e){return this.normalized&&(e=Xs(e,this.array)),this.array[t*this.itemSize+2]=e,this}getW(t){let e=this.array[t*this.itemSize+3];return this.normalized&&(e=Js(e,this.array)),e}setW(t,e){return this.normalized&&(e=Xs(e,this.array)),this.array[t*this.itemSize+3]=e,this}setXY(t,e,s){return t*=this.itemSize,this.normalized&&(e=Xs(e,this.array),s=Xs(s,this.array)),this.array[t+0]=e,this.array[t+1]=s,this}setXYZ(t,e,s,i){return t*=this.itemSize,this.normalized&&(e=Xs(e,this.array),s=Xs(s,this.array),i=Xs(i,this.array)),this.array[t+0]=e,this.array[t+1]=s,this.array[t+2]=i,this}setXYZW(t,e,s,i,r){return t*=this.itemSize,this.normalized&&(e=Xs(e,this.array),s=Xs(s,this.array),i=Xs(i,this.array),r=Xs(r,this.array)),this.array[t+0]=e,this.array[t+1]=s,this.array[t+2]=i,this.array[t+3]=r,this}onUpload(t){return this.onUploadCallback=t,this}clone(){return new this.constructor(this.array,this.itemSize).copy(this)}toJSON(){const t={itemSize:this.itemSize,type:this.array.constructor.name,array:Array.from(this.array),normalized:this.normalized};return""!==this.name&&(t.name=this.name),this.usage!==_s&&(t.usage=this.usage),t}}class un extends cn{constructor(t,e,s){super(new Int8Array(t),e,s)}}class dn extends cn{constructor(t,e,s){super(new Uint8Array(t),e,s)}}class pn extends cn{constructor(t,e,s){super(new Uint8ClampedArray(t),e,s)}}class mn extends cn{constructor(t,e,s){super(new Int16Array(t),e,s)}}class yn extends cn{constructor(t,e,s){super(new Uint16Array(t),e,s)}}class fn extends cn{constructor(t,e,s){super(new Int32Array(t),e,s)}}class gn extends cn{constructor(t,e,s){super(new Uint32Array(t),e,s)}}class xn extends cn{constructor(t,e,s){super(new Uint16Array(t),e,s),this.isFloat16BufferAttribute=!0}getX(t){let e=on(this.array[t*this.itemSize]);return this.normalized&&(e=Js(e,this.array)),e}setX(t,e){return this.normalized&&(e=Xs(e,this.array)),this.array[t*this.itemSize]=nn(e),this}getY(t){let e=on(this.array[t*this.itemSize+1]);return this.normalized&&(e=Js(e,this.array)),e}setY(t,e){return this.normalized&&(e=Xs(e,this.array)),this.array[t*this.itemSize+1]=nn(e),this}getZ(t){let e=on(this.array[t*this.itemSize+2]);return this.normalized&&(e=Js(e,this.array)),e}setZ(t,e){return this.normalized&&(e=Xs(e,this.array)),this.array[t*this.itemSize+2]=nn(e),this}getW(t){let e=on(this.array[t*this.itemSize+3]);return this.normalized&&(e=Js(e,this.array)),e}setW(t,e){return this.normalized&&(e=Xs(e,this.array)),this.array[t*this.itemSize+3]=nn(e),this}setXY(t,e,s){return t*=this.itemSize,this.normalized&&(e=Xs(e,this.array),s=Xs(s,this.array)),this.array[t+0]=nn(e),this.array[t+1]=nn(s),this}setXYZ(t,e,s,i){return t*=this.itemSize,this.normalized&&(e=Xs(e,this.array),s=Xs(s,this.array),i=Xs(i,this.array)),this.array[t+0]=nn(e),this.array[t+1]=nn(s),this.array[t+2]=nn(i),this}setXYZW(t,e,s,i,r){return t*=this.itemSize,this.normalized&&(e=Xs(e,this.array),s=Xs(s,this.array),i=Xs(i,this.array),r=Xs(r,this.array)),this.array[t+0]=nn(e),this.array[t+1]=nn(s),this.array[t+2]=nn(i),this.array[t+3]=nn(r),this}}class bn extends cn{constructor(t,e,s){super(new Float32Array(t),e,s)}}let vn=0;const wn=new nr,Mn=new Rr,Sn=new Ii,_n=new Ri,An=new Ri,Tn=new Ii;class zn extends Ns{constructor(){super(),this.isBufferGeometry=!0,Object.defineProperty(this,"id",{value:vn++}),this.uuid=Us(),this.name="",this.type="BufferGeometry",this.index=null,this.indirect=null,this.attributes={},this.morphAttributes={},this.morphTargetsRelative=!1,this.groups=[],this.boundingBox=null,this.boundingSphere=null,this.drawRange={start:0,count:1/0},this.userData={}}getIndex(){return this.index}setIndex(t){return Array.isArray(t)?this.index=new(Qs(t)?gn:yn)(t,1):this.index=t,this}setIndirect(t){return this.indirect=t,this}getIndirect(){return this.indirect}getAttribute(t){return this.attributes[t]}setAttribute(t,e){return this.attributes[t]=e,this}deleteAttribute(t){return delete this.attributes[t],this}hasAttribute(t){return void 0!==this.attributes[t]}addGroup(t,e,s=0){this.groups.push({start:t,count:e,materialIndex:s})}clearGroups(){this.groups=[]}setDrawRange(t,e){this.drawRange.start=t,this.drawRange.count=e}applyMatrix4(t){const e=this.attributes.position;void 0!==e&&(e.applyMatrix4(t),e.needsUpdate=!0);const s=this.attributes.normal;if(void 0!==s){const e=(new Gs).getNormalMatrix(t);s.applyNormalMatrix(e),s.needsUpdate=!0}const i=this.attributes.tangent;return void 0!==i&&(i.transformDirection(t),i.needsUpdate=!0),null!==this.boundingBox&&this.computeBoundingBox(),null!==this.boundingSphere&&this.computeBoundingSphere(),this}applyQuaternion(t){return wn.makeRotationFromQuaternion(t),this.applyMatrix4(wn),this}rotateX(t){return wn.makeRotationX(t),this.applyMatrix4(wn),this}rotateY(t){return wn.makeRotationY(t),this.applyMatrix4(wn),this}rotateZ(t){return wn.makeRotationZ(t),this.applyMatrix4(wn),this}translate(t,e,s){return wn.makeTranslation(t,e,s),this.applyMatrix4(wn),this}scale(t,e,s){return wn.makeScale(t,e,s),this.applyMatrix4(wn),this}lookAt(t){return Mn.lookAt(t),Mn.updateMatrix(),this.applyMatrix4(Mn.matrix),this}center(){return this.computeBoundingBox(),this.boundingBox.getCenter(Sn).negate(),this.translate(Sn.x,Sn.y,Sn.z),this}setFromPoints(t){const e=this.getAttribute("position");if(void 0===e){const e=[];for(let s=0,i=t.length;s<i;s++){const i=t[s];e.push(i.x,i.y,i.z||0)}this.setAttribute("position",new bn(e,3))}else{const s=Math.min(t.length,e.count);for(let i=0;i<s;i++){const s=t[i];e.setXYZ(i,s.x,s.y,s.z||0)}t.length>e.count&&console.warn("THREE.BufferGeometry: Buffer size too small for points data. Use .dispose() and create a new geometry."),e.needsUpdate=!0}return this}computeBoundingBox(){null===this.boundingBox&&(this.boundingBox=new Ri);const t=this.attributes.position,e=this.morphAttributes.position;if(t&&t.isGLBufferAttribute)return console.error("THREE.BufferGeometry.computeBoundingBox(): GLBufferAttribute requires a manual bounding box.",this),void this.boundingBox.set(new Ii(-1/0,-1/0,-1/0),new Ii(1/0,1/0,1/0));if(void 0!==t){if(this.boundingBox.setFromBufferAttribute(t),e)for(let t=0,s=e.length;t<s;t++){const s=e[t];_n.setFromBufferAttribute(s),this.morphTargetsRelative?(Tn.addVectors(this.boundingBox.min,_n.min),this.boundingBox.expandByPoint(Tn),Tn.addVectors(this.boundingBox.max,_n.max),this.boundingBox.expandByPoint(Tn)):(this.boundingBox.expandByPoint(_n.min),this.boundingBox.expandByPoint(_n.max))}}else this.boundingBox.makeEmpty();(isNaN(this.boundingBox.min.x)||isNaN(this.boundingBox.min.y)||isNaN(this.boundingBox.min.z))&&console.error('THREE.BufferGeometry.computeBoundingBox(): Computed min/max have NaN values. The "position" attribute is likely to have NaN values.',this)}computeBoundingSphere(){null===this.boundingSphere&&(this.boundingSphere=new Gi);const t=this.attributes.position,e=this.morphAttributes.position;if(t&&t.isGLBufferAttribute)return console.error("THREE.BufferGeometry.computeBoundingSphere(): GLBufferAttribute requires a manual bounding sphere.",this),void this.boundingSphere.set(new Ii,1/0);if(t){const s=this.boundingSphere.center;if(_n.setFromBufferAttribute(t),e)for(let t=0,s=e.length;t<s;t++){const s=e[t];An.setFromBufferAttribute(s),this.morphTargetsRelative?(Tn.addVectors(_n.min,An.min),_n.expandByPoint(Tn),Tn.addVectors(_n.max,An.max),_n.expandByPoint(Tn)):(_n.expandByPoint(An.min),_n.expandByPoint(An.max))}_n.getCenter(s);let i=0;for(let e=0,r=t.count;e<r;e++)Tn.fromBufferAttribute(t,e),i=Math.max(i,s.distanceToSquared(Tn));if(e)for(let r=0,n=e.length;r<n;r++){const n=e[r],o=this.morphTargetsRelative;for(let e=0,r=n.count;e<r;e++)Tn.fromBufferAttribute(n,e),o&&(Sn.fromBufferAttribute(t,e),Tn.add(Sn)),i=Math.max(i,s.distanceToSquared(Tn))}this.boundingSphere.radius=Math.sqrt(i),isNaN(this.boundingSphere.radius)&&console.error('THREE.BufferGeometry.computeBoundingSphere(): Computed radius is NaN. The "position" attribute is likely to have NaN values.',this)}}computeTangents(){const t=this.index,e=this.attributes;if(null===t||void 0===e.position||void 0===e.normal||void 0===e.uv)return void console.error("THREE.BufferGeometry: .computeTangents() failed. Missing required attributes (index, position, normal or uv)");const s=e.position,i=e.normal,r=e.uv;!1===this.hasAttribute("tangent")&&this.setAttribute("tangent",new cn(new Float32Array(4*s.count),4));const n=this.getAttribute("tangent"),o=[],a=[];for(let t=0;t<s.count;t++)o[t]=new Ii,a[t]=new Ii;const h=new Ii,l=new Ii,c=new Ii,u=new Zs,d=new Zs,p=new Zs,m=new Ii,y=new Ii;function f(t,e,i){h.fromBufferAttribute(s,t),l.fromBufferAttribute(s,e),c.fromBufferAttribute(s,i),u.fromBufferAttribute(r,t),d.fromBufferAttribute(r,e),p.fromBufferAttribute(r,i),l.sub(h),c.sub(h),d.sub(u),p.sub(u);const n=1/(d.x*p.y-p.x*d.y);isFinite(n)&&(m.copy(l).multiplyScalar(p.y).addScaledVector(c,-d.y).multiplyScalar(n),y.copy(c).multiplyScalar(d.x).addScaledVector(l,-p.x).multiplyScalar(n),o[t].add(m),o[e].add(m),o[i].add(m),a[t].add(y),a[e].add(y),a[i].add(y))}let g=this.groups;0===g.length&&(g=[{start:0,count:t.count}]);for(let e=0,s=g.length;e<s;++e){const s=g[e],i=s.start;for(let e=i,r=i+s.count;e<r;e+=3)f(t.getX(e+0),t.getX(e+1),t.getX(e+2))}const x=new Ii,b=new Ii,v=new Ii,w=new Ii;function M(t){v.fromBufferAttribute(i,t),w.copy(v);const e=o[t];x.copy(e),x.sub(v.multiplyScalar(v.dot(e))).normalize(),b.crossVectors(w,e);const s=b.dot(a[t])<0?-1:1;n.setXYZW(t,x.x,x.y,x.z,s)}for(let e=0,s=g.length;e<s;++e){const s=g[e],i=s.start;for(let e=i,r=i+s.count;e<r;e+=3)M(t.getX(e+0)),M(t.getX(e+1)),M(t.getX(e+2))}}computeVertexNormals(){const t=this.index,e=this.getAttribute("position");if(void 0!==e){let s=this.getAttribute("normal");if(void 0===s)s=new cn(new Float32Array(3*e.count),3),this.setAttribute("normal",s);else for(let t=0,e=s.count;t<e;t++)s.setXYZ(t,0,0,0);const i=new Ii,r=new Ii,n=new Ii,o=new Ii,a=new Ii,h=new Ii,l=new Ii,c=new Ii;if(t)for(let u=0,d=t.count;u<d;u+=3){const d=t.getX(u+0),p=t.getX(u+1),m=t.getX(u+2);i.fromBufferAttribute(e,d),r.fromBufferAttribute(e,p),n.fromBufferAttribute(e,m),l.subVectors(n,r),c.subVectors(i,r),l.cross(c),o.fromBufferAttribute(s,d),a.fromBufferAttribute(s,p),h.fromBufferAttribute(s,m),o.add(l),a.add(l),h.add(l),s.setXYZ(d,o.x,o.y,o.z),s.setXYZ(p,a.x,a.y,a.z),s.setXYZ(m,h.x,h.y,h.z)}else for(let t=0,o=e.count;t<o;t+=3)i.fromBufferAttribute(e,t+0),r.fromBufferAttribute(e,t+1),n.fromBufferAttribute(e,t+2),l.subVectors(n,r),c.subVectors(i,r),l.cross(c),s.setXYZ(t+0,l.x,l.y,l.z),s.setXYZ(t+1,l.x,l.y,l.z),s.setXYZ(t+2,l.x,l.y,l.z);this.normalizeNormals(),s.needsUpdate=!0}}normalizeNormals(){const t=this.attributes.normal;for(let e=0,s=t.count;e<s;e++)Tn.fromBufferAttribute(t,e),Tn.normalize(),t.setXYZ(e,Tn.x,Tn.y,Tn.z)}toNonIndexed(){function t(t,e){const s=t.array,i=t.itemSize,r=t.normalized,n=new s.constructor(e.length*i);let o=0,a=0;for(let r=0,h=e.length;r<h;r++){o=t.isInterleavedBufferAttribute?e[r]*t.data.stride+t.offset:e[r]*i;for(let t=0;t<i;t++)n[a++]=s[o++]}return new cn(n,i,r)}if(null===this.index)return console.warn("THREE.BufferGeometry.toNonIndexed(): BufferGeometry is already non-indexed."),this;const e=new zn,s=this.index.array,i=this.attributes;for(const r in i){const n=t(i[r],s);e.setAttribute(r,n)}const r=this.morphAttributes;for(const i in r){const n=[],o=r[i];for(let e=0,i=o.length;e<i;e++){const i=t(o[e],s);n.push(i)}e.morphAttributes[i]=n}e.morphTargetsRelative=this.morphTargetsRelative;const n=this.groups;for(let t=0,s=n.length;t<s;t++){const s=n[t];e.addGroup(s.start,s.count,s.materialIndex)}return e}toJSON(){const t={metadata:{version:4.6,type:"BufferGeometry",generator:"BufferGeometry.toJSON"}};if(t.uuid=this.uuid,t.type=this.type,""!==this.name&&(t.name=this.name),Object.keys(this.userData).length>0&&(t.userData=this.userData),void 0!==this.parameters){const e=this.parameters;for(const s in e)void 0!==e[s]&&(t[s]=e[s]);return t}t.data={attributes:{}};const e=this.index;null!==e&&(t.data.index={type:e.array.constructor.name,array:Array.prototype.slice.call(e.array)});const s=this.attributes;for(const e in s){const i=s[e];t.data.attributes[e]=i.toJSON(t.data)}const i={};let r=!1;for(const e in this.morphAttributes){const s=this.morphAttributes[e],n=[];for(let e=0,i=s.length;e<i;e++){const i=s[e];n.push(i.toJSON(t.data))}n.length>0&&(i[e]=n,r=!0)}r&&(t.data.morphAttributes=i,t.data.morphTargetsRelative=this.morphTargetsRelative);const n=this.groups;n.length>0&&(t.data.groups=JSON.parse(JSON.stringify(n)));const o=this.boundingSphere;return null!==o&&(t.data.boundingSphere={center:o.center.toArray(),radius:o.radius}),t}clone(){return(new this.constructor).copy(this)}copy(t){this.index=null,this.attributes={},this.morphAttributes={},this.groups=[],this.boundingBox=null,this.boundingSphere=null;const e={};this.name=t.name;const s=t.index;null!==s&&this.setIndex(s.clone(e));const i=t.attributes;for(const t in i){const s=i[t];this.setAttribute(t,s.clone(e))}const r=t.morphAttributes;for(const t in r){const s=[],i=r[t];for(let t=0,r=i.length;t<r;t++)s.push(i[t].clone(e));this.morphAttributes[t]=s}this.morphTargetsRelative=t.morphTargetsRelative;const n=t.groups;for(let t=0,e=n.length;t<e;t++){const e=n[t];this.addGroup(e.start,e.count,e.materialIndex)}const o=t.boundingBox;null!==o&&(this.boundingBox=o.clone());const a=t.boundingSphere;return null!==a&&(this.boundingSphere=a.clone()),this.drawRange.start=t.drawRange.start,this.drawRange.count=t.drawRange.count,this.userData=t.userData,this}dispose(){this.dispatchEvent({type:"dispose"})}}const Cn=new nr,In=new rr,Bn=new Gi,kn=new Ii,Rn=new Ii,En=new Ii,Pn=new Ii,On=new Ii,Fn=new Ii,Nn=new Ii,Ln=new Ii;class Vn extends Rr{constructor(t=new zn,e=new en){super(),this.isMesh=!0,this.type="Mesh",this.geometry=t,this.material=e,this.updateMorphTargets()}copy(t,e){return super.copy(t,e),void 0!==t.morphTargetInfluences&&(this.morphTargetInfluences=t.morphTargetInfluences.slice()),void 0!==t.morphTargetDictionary&&(this.morphTargetDictionary=Object.assign({},t.morphTargetDictionary)),this.material=Array.isArray(t.material)?t.material.slice():t.material,this.geometry=t.geometry,this}updateMorphTargets(){const t=this.geometry.morphAttributes,e=Object.keys(t);if(e.length>0){const s=t[e[0]];if(void 0!==s){this.morphTargetInfluences=[],this.morphTargetDictionary={};for(let t=0,e=s.length;t<e;t++){const e=s[t].name||String(t);this.morphTargetInfluences.push(0),this.morphTargetDictionary[e]=t}}}}getVertexPosition(t,e){const s=this.geometry,i=s.attributes.position,r=s.morphAttributes.position,n=s.morphTargetsRelative;e.fromBufferAttribute(i,t);const o=this.morphTargetInfluences;if(r&&o){Fn.set(0,0,0);for(let s=0,i=r.length;s<i;s++){const i=o[s],a=r[s];0!==i&&(On.fromBufferAttribute(a,t),n?Fn.addScaledVector(On,i):Fn.addScaledVector(On.sub(e),i))}e.add(Fn)}return e}raycast(t,e){const s=this.geometry,i=this.material,r=this.matrixWorld;if(void 0!==i){if(null===s.boundingSphere&&s.computeBoundingSphere(),Bn.copy(s.boundingSphere),Bn.applyMatrix4(r),In.copy(t.ray).recast(t.near),!1===Bn.containsPoint(In.origin)){if(null===In.intersectSphere(Bn,kn))return;if(In.origin.distanceToSquared(kn)>(t.far-t.near)**2)return}Cn.copy(r).invert(),In.copy(t.ray).applyMatrix4(Cn),null!==s.boundingBox&&!1===In.intersectsBox(s.boundingBox)||this._computeIntersections(t,e,In)}}_computeIntersections(t,e,s){let i;const r=this.geometry,n=this.material,o=r.index,a=r.attributes.position,h=r.attributes.uv,l=r.attributes.uv1,c=r.attributes.normal,u=r.groups,d=r.drawRange;if(null!==o)if(Array.isArray(n))for(let r=0,a=u.length;r<a;r++){const a=u[r],p=n[a.materialIndex];for(let r=Math.max(a.start,d.start),n=Math.min(o.count,Math.min(a.start+a.count,d.start+d.count));r<n;r+=3){i=Wn(this,p,t,s,h,l,c,o.getX(r),o.getX(r+1),o.getX(r+2)),i&&(i.faceIndex=Math.floor(r/3),i.face.materialIndex=a.materialIndex,e.push(i))}}else{for(let r=Math.max(0,d.start),a=Math.min(o.count,d.start+d.count);r<a;r+=3){i=Wn(this,n,t,s,h,l,c,o.getX(r),o.getX(r+1),o.getX(r+2)),i&&(i.faceIndex=Math.floor(r/3),e.push(i))}}else if(void 0!==a)if(Array.isArray(n))for(let r=0,o=u.length;r<o;r++){const o=u[r],p=n[o.materialIndex];for(let r=Math.max(o.start,d.start),n=Math.min(a.count,Math.min(o.start+o.count,d.start+d.count));r<n;r+=3){i=Wn(this,p,t,s,h,l,c,r,r+1,r+2),i&&(i.faceIndex=Math.floor(r/3),i.face.materialIndex=o.materialIndex,e.push(i))}}else{for(let r=Math.max(0,d.start),o=Math.min(a.count,d.start+d.count);r<o;r+=3){i=Wn(this,n,t,s,h,l,c,r,r+1,r+2),i&&(i.faceIndex=Math.floor(r/3),e.push(i))}}}}function Wn(t,e,s,i,r,n,o,a,h,l){t.getVertexPosition(a,Rn),t.getVertexPosition(h,En),t.getVertexPosition(l,Pn);const c=function(t,e,s,i,r,n,o,a){let h;if(h=1===e.side?i.intersectTriangle(o,n,r,!0,a):i.intersectTriangle(r,n,o,0===e.side,a),null===h)return null;Ln.copy(a),Ln.applyMatrix4(t.matrixWorld);const l=s.ray.origin.distanceTo(Ln);return l<s.near||l>s.far?null:{distance:l,point:Ln.clone(),object:t}}(t,e,s,i,Rn,En,Pn,Nn);if(c){const t=new Ii;Jr.getBarycoord(Nn,Rn,En,Pn,t),r&&(c.uv=Jr.getInterpolatedAttribute(r,a,h,l,t,new Zs)),n&&(c.uv1=Jr.getInterpolatedAttribute(n,a,h,l,t,new Zs)),o&&(c.normal=Jr.getInterpolatedAttribute(o,a,h,l,t,new Ii),c.normal.dot(i.direction)>0&&c.normal.multiplyScalar(-1));const e={a:a,b:h,c:l,normal:new Ii,materialIndex:0};Jr.getNormal(Rn,En,Pn,e.normal),c.face=e,c.barycoord=t}return c}class jn extends zn{constructor(t=1,e=1,s=1,i=1,r=1,n=1){super(),this.type="BoxGeometry",this.parameters={width:t,height:e,depth:s,widthSegments:i,heightSegments:r,depthSegments:n};const o=this;i=Math.floor(i),r=Math.floor(r),n=Math.floor(n);const a=[],h=[],l=[],c=[];let u=0,d=0;function p(t,e,s,i,r,n,p,m,y,f,g){const x=n/y,b=p/f,v=n/2,w=p/2,M=m/2,S=y+1,_=f+1;let A=0,T=0;const z=new Ii;for(let n=0;n<_;n++){const o=n*b-w;for(let a=0;a<S;a++){const u=a*x-v;z[t]=u*i,z[e]=o*r,z[s]=M,h.push(z.x,z.y,z.z),z[t]=0,z[e]=0,z[s]=m>0?1:-1,l.push(z.x,z.y,z.z),c.push(a/y),c.push(1-n/f),A+=1}}for(let t=0;t<f;t++)for(let e=0;e<y;e++){const s=u+e+S*t,i=u+e+S*(t+1),r=u+(e+1)+S*(t+1),n=u+(e+1)+S*t;a.push(s,i,n),a.push(i,r,n),T+=6}o.addGroup(d,T,g),d+=T,u+=A}p("z","y","x",-1,-1,s,e,t,n,r,0),p("z","y","x",1,-1,s,e,-t,n,r,1),p("x","z","y",1,1,t,s,e,i,n,2),p("x","z","y",1,-1,t,s,-e,i,n,3),p("x","y","z",1,-1,t,e,s,i,r,4),p("x","y","z",-1,-1,t,e,-s,i,r,5),this.setIndex(a),this.setAttribute("position",new bn(h,3)),this.setAttribute("normal",new bn(l,3)),this.setAttribute("uv",new bn(c,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new jn(t.width,t.height,t.depth,t.widthSegments,t.heightSegments,t.depthSegments)}}function Un(t){const e={};for(const s in t){e[s]={};for(const i in t[s]){const r=t[s][i];r&&(r.isColor||r.isMatrix3||r.isMatrix4||r.isVector2||r.isVector3||r.isVector4||r.isTexture||r.isQuaternion)?r.isRenderTargetTexture?(console.warn("UniformsUtils: Textures of render targets cannot be cloned via cloneUniforms() or mergeUniforms()."),e[s][i]=null):e[s][i]=r.clone():Array.isArray(r)?e[s][i]=r.slice():e[s][i]=r}}return e}function Dn(t){const e={};for(let s=0;s<t.length;s++){const i=Un(t[s]);for(const t in i)e[t]=i[t]}return e}function Hn(t){const e=t.getRenderTarget();return null===e?t.outputColorSpace:!0===e.isXRRenderTarget?e.texture.colorSpace:ui.workingColorSpace}const qn={clone:Un,merge:Dn};class Jn extends tn{constructor(t){super(),this.isShaderMaterial=!0,this.type="ShaderMaterial",this.defines={},this.uniforms={},this.uniformsGroups=[],this.vertexShader="void main() {\n\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n}",this.fragmentShader="void main() {\n\tgl_FragColor = vec4( 1.0, 0.0, 0.0, 1.0 );\n}",this.linewidth=1,this.wireframe=!1,this.wireframeLinewidth=1,this.fog=!1,this.lights=!1,this.clipping=!1,this.forceSinglePass=!0,this.extensions={clipCullDistance:!1,multiDraw:!1},this.defaultAttributeValues={color:[1,1,1],uv:[0,0],uv1:[0,0]},this.index0AttributeName=void 0,this.uniformsNeedUpdate=!1,this.glslVersion=null,void 0!==t&&this.setValues(t)}copy(t){return super.copy(t),this.fragmentShader=t.fragmentShader,this.vertexShader=t.vertexShader,this.uniforms=Un(t.uniforms),this.uniformsGroups=function(t){const e=[];for(let s=0;s<t.length;s++)e.push(t[s].clone());return e}(t.uniformsGroups),this.defines=Object.assign({},t.defines),this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.fog=t.fog,this.lights=t.lights,this.clipping=t.clipping,this.extensions=Object.assign({},t.extensions),this.glslVersion=t.glslVersion,this}toJSON(t){const e=super.toJSON(t);e.glslVersion=this.glslVersion,e.uniforms={};for(const s in this.uniforms){const i=this.uniforms[s].value;i&&i.isTexture?e.uniforms[s]={type:"t",value:i.toJSON(t).uuid}:i&&i.isColor?e.uniforms[s]={type:"c",value:i.getHex()}:i&&i.isVector2?e.uniforms[s]={type:"v2",value:i.toArray()}:i&&i.isVector3?e.uniforms[s]={type:"v3",value:i.toArray()}:i&&i.isVector4?e.uniforms[s]={type:"v4",value:i.toArray()}:i&&i.isMatrix3?e.uniforms[s]={type:"m3",value:i.toArray()}:i&&i.isMatrix4?e.uniforms[s]={type:"m4",value:i.toArray()}:e.uniforms[s]={value:i}}Object.keys(this.defines).length>0&&(e.defines=this.defines),e.vertexShader=this.vertexShader,e.fragmentShader=this.fragmentShader,e.lights=this.lights,e.clipping=this.clipping;const s={};for(const t in this.extensions)!0===this.extensions[t]&&(s[t]=!0);return Object.keys(s).length>0&&(e.extensions=s),e}}class Xn extends Rr{constructor(){super(),this.isCamera=!0,this.type="Camera",this.matrixWorldInverse=new nr,this.projectionMatrix=new nr,this.projectionMatrixInverse=new nr,this.coordinateSystem=Os}copy(t,e){return super.copy(t,e),this.matrixWorldInverse.copy(t.matrixWorldInverse),this.projectionMatrix.copy(t.projectionMatrix),this.projectionMatrixInverse.copy(t.projectionMatrixInverse),this.coordinateSystem=t.coordinateSystem,this}getWorldDirection(t){return super.getWorldDirection(t).negate()}updateMatrixWorld(t){super.updateMatrixWorld(t),this.matrixWorldInverse.copy(this.matrixWorld).invert()}updateWorldMatrix(t,e){super.updateWorldMatrix(t,e),this.matrixWorldInverse.copy(this.matrixWorld).invert()}clone(){return(new this.constructor).copy(this)}}const Yn=new Ii,Zn=new Zs,Gn=new Zs;class $n extends Xn{constructor(t=50,e=1,s=.1,i=2e3){super(),this.isPerspectiveCamera=!0,this.type="PerspectiveCamera",this.fov=t,this.zoom=1,this.near=s,this.far=i,this.focus=10,this.aspect=e,this.view=null,this.filmGauge=35,this.filmOffset=0,this.updateProjectionMatrix()}copy(t,e){return super.copy(t,e),this.fov=t.fov,this.zoom=t.zoom,this.near=t.near,this.far=t.far,this.focus=t.focus,this.aspect=t.aspect,this.view=null===t.view?null:Object.assign({},t.view),this.filmGauge=t.filmGauge,this.filmOffset=t.filmOffset,this}setFocalLength(t){const e=.5*this.getFilmHeight()/t;this.fov=2*js*Math.atan(e),this.updateProjectionMatrix()}getFocalLength(){const t=Math.tan(.5*Ws*this.fov);return.5*this.getFilmHeight()/t}getEffectiveFOV(){return 2*js*Math.atan(Math.tan(.5*Ws*this.fov)/this.zoom)}getFilmWidth(){return this.filmGauge*Math.min(this.aspect,1)}getFilmHeight(){return this.filmGauge/Math.max(this.aspect,1)}getViewBounds(t,e,s){Yn.set(-1,-1,.5).applyMatrix4(this.projectionMatrixInverse),e.set(Yn.x,Yn.y).multiplyScalar(-t/Yn.z),Yn.set(1,1,.5).applyMatrix4(this.projectionMatrixInverse),s.set(Yn.x,Yn.y).multiplyScalar(-t/Yn.z)}getViewSize(t,e){return this.getViewBounds(t,Zn,Gn),e.subVectors(Gn,Zn)}setViewOffset(t,e,s,i,r,n){this.aspect=t/e,null===this.view&&(this.view={enabled:!0,fullWidth:1,fullHeight:1,offsetX:0,offsetY:0,width:1,height:1}),this.view.enabled=!0,this.view.fullWidth=t,this.view.fullHeight=e,this.view.offsetX=s,this.view.offsetY=i,this.view.width=r,this.view.height=n,this.updateProjectionMatrix()}clearViewOffset(){null!==this.view&&(this.view.enabled=!1),this.updateProjectionMatrix()}updateProjectionMatrix(){const t=this.near;let e=t*Math.tan(.5*Ws*this.fov)/this.zoom,s=2*e,i=this.aspect*s,r=-.5*i;const n=this.view;if(null!==this.view&&this.view.enabled){const t=n.fullWidth,o=n.fullHeight;r+=n.offsetX*i/t,e-=n.offsetY*s/o,i*=n.width/t,s*=n.height/o}const o=this.filmOffset;0!==o&&(r+=t*o/this.getFilmWidth()),this.projectionMatrix.makePerspective(r,r+i,e,e-s,t,this.far,this.coordinateSystem),this.projectionMatrixInverse.copy(this.projectionMatrix).invert()}toJSON(t){const e=super.toJSON(t);return e.object.fov=this.fov,e.object.zoom=this.zoom,e.object.near=this.near,e.object.far=this.far,e.object.focus=this.focus,e.object.aspect=this.aspect,null!==this.view&&(e.object.view=Object.assign({},this.view)),e.object.filmGauge=this.filmGauge,e.object.filmOffset=this.filmOffset,e}}const Qn=-90;class Kn extends Rr{constructor(t,e,s){super(),this.type="CubeCamera",this.renderTarget=s,this.coordinateSystem=null,this.activeMipmapLevel=0;const i=new $n(Qn,1,t,e);i.layers=this.layers,this.add(i);const r=new $n(Qn,1,t,e);r.layers=this.layers,this.add(r);const n=new $n(Qn,1,t,e);n.layers=this.layers,this.add(n);const o=new $n(Qn,1,t,e);o.layers=this.layers,this.add(o);const a=new $n(Qn,1,t,e);a.layers=this.layers,this.add(a);const h=new $n(Qn,1,t,e);h.layers=this.layers,this.add(h)}updateCoordinateSystem(){const t=this.coordinateSystem,e=this.children.concat(),[s,i,r,n,o,a]=e;for(const t of e)this.remove(t);if(t===Os)s.up.set(0,1,0),s.lookAt(1,0,0),i.up.set(0,1,0),i.lookAt(-1,0,0),r.up.set(0,0,-1),r.lookAt(0,1,0),n.up.set(0,0,1),n.lookAt(0,-1,0),o.up.set(0,1,0),o.lookAt(0,0,1),a.up.set(0,1,0),a.lookAt(0,0,-1);else{if(t!==Fs)throw new Error("THREE.CubeCamera.updateCoordinateSystem(): Invalid coordinate system: "+t);s.up.set(0,-1,0),s.lookAt(-1,0,0),i.up.set(0,-1,0),i.lookAt(1,0,0),r.up.set(0,0,1),r.lookAt(0,1,0),n.up.set(0,0,-1),n.lookAt(0,-1,0),o.up.set(0,-1,0),o.lookAt(0,0,1),a.up.set(0,-1,0),a.lookAt(0,0,-1)}for(const t of e)this.add(t),t.updateMatrixWorld()}update(t,e){null===this.parent&&this.updateMatrixWorld();const{renderTarget:s,activeMipmapLevel:i}=this;this.coordinateSystem!==t.coordinateSystem&&(this.coordinateSystem=t.coordinateSystem,this.updateCoordinateSystem());const[r,n,o,a,h,l]=this.children,c=t.getRenderTarget(),u=t.getActiveCubeFace(),d=t.getActiveMipmapLevel(),p=t.xr.enabled;t.xr.enabled=!1;const m=s.texture.generateMipmaps;s.texture.generateMipmaps=!1,t.setRenderTarget(s,0,i),t.render(e,r),t.setRenderTarget(s,1,i),t.render(e,n),t.setRenderTarget(s,2,i),t.render(e,o),t.setRenderTarget(s,3,i),t.render(e,a),t.setRenderTarget(s,4,i),t.render(e,h),s.texture.generateMipmaps=m,t.setRenderTarget(s,5,i),t.render(e,l),t.setRenderTarget(c,u,d),t.xr.enabled=p,s.texture.needsPMREMUpdate=!0}}class to extends vi{constructor(t,e,s,i,r,n,o,a,h,l){super(t=void 0!==t?t:[],e=void 0!==e?e:ht,s,i,r,n,o,a,h,l),this.isCubeTexture=!0,this.flipY=!1}get images(){return this.image}set images(t){this.image=t}}class eo extends Si{constructor(t=1,e={}){super(t,t,e),this.isWebGLCubeRenderTarget=!0;const s={width:t,height:t,depth:1},i=[s,s,s,s,s,s];this.texture=new to(i,e.mapping,e.wrapS,e.wrapT,e.magFilter,e.minFilter,e.format,e.type,e.anisotropy,e.colorSpace),this.texture.isRenderTargetTexture=!0,this.texture.generateMipmaps=void 0!==e.generateMipmaps&&e.generateMipmaps,this.texture.minFilter=void 0!==e.minFilter?e.minFilter:wt}fromEquirectangularTexture(t,e){this.texture.type=e.type,this.texture.colorSpace=e.colorSpace,this.texture.generateMipmaps=e.generateMipmaps,this.texture.minFilter=e.minFilter,this.texture.magFilter=e.magFilter;const s={uniforms:{tEquirect:{value:null}},vertexShader:"\n\n\t\t\t\tvarying vec3 vWorldDirection;\n\n\t\t\t\tvec3 transformDirection( in vec3 dir, in mat4 matrix ) {\n\n\t\t\t\t\treturn normalize( ( matrix * vec4( dir, 0.0 ) ).xyz );\n\n\t\t\t\t}\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tvWorldDirection = transformDirection( position, modelMatrix );\n\n\t\t\t\t\t#include <begin_vertex>\n\t\t\t\t\t#include <project_vertex>\n\n\t\t\t\t}\n\t\t\t",fragmentShader:"\n\n\t\t\t\tuniform sampler2D tEquirect;\n\n\t\t\t\tvarying vec3 vWorldDirection;\n\n\t\t\t\t#include <common>\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tvec3 direction = normalize( vWorldDirection );\n\n\t\t\t\t\tvec2 sampleUV = equirectUv( direction );\n\n\t\t\t\t\tgl_FragColor = texture2D( tEquirect, sampleUV );\n\n\t\t\t\t}\n\t\t\t"},i=new jn(5,5,5),r=new Jn({name:"CubemapFromEquirect",uniforms:Un(s.uniforms),vertexShader:s.vertexShader,fragmentShader:s.fragmentShader,side:1,blending:0});r.uniforms.tEquirect.value=e;const n=new Vn(i,r),o=e.minFilter;e.minFilter===_t&&(e.minFilter=wt);return new Kn(1,10,this).update(t,n),e.minFilter=o,n.geometry.dispose(),n.material.dispose(),this}clear(t,e,s,i){const r=t.getRenderTarget();for(let r=0;r<6;r++)t.setRenderTarget(this,r),t.clear(e,s,i);t.setRenderTarget(r)}}class so{constructor(t,e=25e-5){this.isFogExp2=!0,this.name="",this.color=new $r(t),this.density=e}clone(){return new so(this.color,this.density)}toJSON(){return{type:"FogExp2",name:this.name,color:this.color.getHex(),density:this.density}}}class io{constructor(t,e=1,s=1e3){this.isFog=!0,this.name="",this.color=new $r(t),this.near=e,this.far=s}clone(){return new io(this.color,this.near,this.far)}toJSON(){return{type:"Fog",name:this.name,color:this.color.getHex(),near:this.near,far:this.far}}}class ro extends Rr{constructor(){super(),this.isScene=!0,this.type="Scene",this.background=null,this.environment=null,this.fog=null,this.backgroundBlurriness=0,this.backgroundIntensity=1,this.backgroundRotation=new yr,this.environmentIntensity=1,this.environmentRotation=new yr,this.overrideMaterial=null,"undefined"!=typeof __THREE_DEVTOOLS__&&__THREE_DEVTOOLS__.dispatchEvent(new CustomEvent("observe",{detail:this}))}copy(t,e){return super.copy(t,e),null!==t.background&&(this.background=t.background.clone()),null!==t.environment&&(this.environment=t.environment.clone()),null!==t.fog&&(this.fog=t.fog.clone()),this.backgroundBlurriness=t.backgroundBlurriness,this.backgroundIntensity=t.backgroundIntensity,this.backgroundRotation.copy(t.backgroundRotation),this.environmentIntensity=t.environmentIntensity,this.environmentRotation.copy(t.environmentRotation),null!==t.overrideMaterial&&(this.overrideMaterial=t.overrideMaterial.clone()),this.matrixAutoUpdate=t.matrixAutoUpdate,this}toJSON(t){const e=super.toJSON(t);return null!==this.fog&&(e.object.fog=this.fog.toJSON()),this.backgroundBlurriness>0&&(e.object.backgroundBlurriness=this.backgroundBlurriness),1!==this.backgroundIntensity&&(e.object.backgroundIntensity=this.backgroundIntensity),e.object.backgroundRotation=this.backgroundRotation.toArray(),1!==this.environmentIntensity&&(e.object.environmentIntensity=this.environmentIntensity),e.object.environmentRotation=this.environmentRotation.toArray(),e}}class no{constructor(t,e){this.isInterleavedBuffer=!0,this.array=t,this.stride=e,this.count=void 0!==t?t.length/e:0,this.usage=_s,this.updateRanges=[],this.version=0,this.uuid=Us()}onUploadCallback(){}set needsUpdate(t){!0===t&&this.version++}setUsage(t){return this.usage=t,this}addUpdateRange(t,e){this.updateRanges.push({start:t,count:e})}clearUpdateRanges(){this.updateRanges.length=0}copy(t){return this.array=new t.array.constructor(t.array),this.count=t.count,this.stride=t.stride,this.usage=t.usage,this}copyAt(t,e,s){t*=this.stride,s*=e.stride;for(let i=0,r=this.stride;i<r;i++)this.array[t+i]=e.array[s+i];return this}set(t,e=0){return this.array.set(t,e),this}clone(t){void 0===t.arrayBuffers&&(t.arrayBuffers={}),void 0===this.array.buffer._uuid&&(this.array.buffer._uuid=Us()),void 0===t.arrayBuffers[this.array.buffer._uuid]&&(t.arrayBuffers[this.array.buffer._uuid]=this.array.slice(0).buffer);const e=new this.array.constructor(t.arrayBuffers[this.array.buffer._uuid]),s=new this.constructor(e,this.stride);return s.setUsage(this.usage),s}onUpload(t){return this.onUploadCallback=t,this}toJSON(t){return void 0===t.arrayBuffers&&(t.arrayBuffers={}),void 0===this.array.buffer._uuid&&(this.array.buffer._uuid=Us()),void 0===t.arrayBuffers[this.array.buffer._uuid]&&(t.arrayBuffers[this.array.buffer._uuid]=Array.from(new Uint32Array(this.array.buffer))),{uuid:this.uuid,buffer:this.array.buffer._uuid,type:this.array.constructor.name,stride:this.stride}}}const oo=new Ii;class ao{constructor(t,e,s,i=!1){this.isInterleavedBufferAttribute=!0,this.name="",this.data=t,this.itemSize=e,this.offset=s,this.normalized=i}get count(){return this.data.count}get array(){return this.data.array}set needsUpdate(t){this.data.needsUpdate=t}applyMatrix4(t){for(let e=0,s=this.data.count;e<s;e++)oo.fromBufferAttribute(this,e),oo.applyMatrix4(t),this.setXYZ(e,oo.x,oo.y,oo.z);return this}applyNormalMatrix(t){for(let e=0,s=this.count;e<s;e++)oo.fromBufferAttribute(this,e),oo.applyNormalMatrix(t),this.setXYZ(e,oo.x,oo.y,oo.z);return this}transformDirection(t){for(let e=0,s=this.count;e<s;e++)oo.fromBufferAttribute(this,e),oo.transformDirection(t),this.setXYZ(e,oo.x,oo.y,oo.z);return this}getComponent(t,e){let s=this.array[t*this.data.stride+this.offset+e];return this.normalized&&(s=Js(s,this.array)),s}setComponent(t,e,s){return this.normalized&&(s=Xs(s,this.array)),this.data.array[t*this.data.stride+this.offset+e]=s,this}setX(t,e){return this.normalized&&(e=Xs(e,this.array)),this.data.array[t*this.data.stride+this.offset]=e,this}setY(t,e){return this.normalized&&(e=Xs(e,this.array)),this.data.array[t*this.data.stride+this.offset+1]=e,this}setZ(t,e){return this.normalized&&(e=Xs(e,this.array)),this.data.array[t*this.data.stride+this.offset+2]=e,this}setW(t,e){return this.normalized&&(e=Xs(e,this.array)),this.data.array[t*this.data.stride+this.offset+3]=e,this}getX(t){let e=this.data.array[t*this.data.stride+this.offset];return this.normalized&&(e=Js(e,this.array)),e}getY(t){let e=this.data.array[t*this.data.stride+this.offset+1];return this.normalized&&(e=Js(e,this.array)),e}getZ(t){let e=this.data.array[t*this.data.stride+this.offset+2];return this.normalized&&(e=Js(e,this.array)),e}getW(t){let e=this.data.array[t*this.data.stride+this.offset+3];return this.normalized&&(e=Js(e,this.array)),e}setXY(t,e,s){return t=t*this.data.stride+this.offset,this.normalized&&(e=Xs(e,this.array),s=Xs(s,this.array)),this.data.array[t+0]=e,this.data.array[t+1]=s,this}setXYZ(t,e,s,i){return t=t*this.data.stride+this.offset,this.normalized&&(e=Xs(e,this.array),s=Xs(s,this.array),i=Xs(i,this.array)),this.data.array[t+0]=e,this.data.array[t+1]=s,this.data.array[t+2]=i,this}setXYZW(t,e,s,i,r){return t=t*this.data.stride+this.offset,this.normalized&&(e=Xs(e,this.array),s=Xs(s,this.array),i=Xs(i,this.array),r=Xs(r,this.array)),this.data.array[t+0]=e,this.data.array[t+1]=s,this.data.array[t+2]=i,this.data.array[t+3]=r,this}clone(t){if(void 0===t){console.log("THREE.InterleavedBufferAttribute.clone(): Cloning an interleaved buffer attribute will de-interleave buffer data.");const t=[];for(let e=0;e<this.count;e++){const s=e*this.data.stride+this.offset;for(let e=0;e<this.itemSize;e++)t.push(this.data.array[s+e])}return new cn(new this.array.constructor(t),this.itemSize,this.normalized)}return void 0===t.interleavedBuffers&&(t.interleavedBuffers={}),void 0===t.interleavedBuffers[this.data.uuid]&&(t.interleavedBuffers[this.data.uuid]=this.data.clone(t)),new ao(t.interleavedBuffers[this.data.uuid],this.itemSize,this.offset,this.normalized)}toJSON(t){if(void 0===t){console.log("THREE.InterleavedBufferAttribute.toJSON(): Serializing an interleaved buffer attribute will de-interleave buffer data.");const t=[];for(let e=0;e<this.count;e++){const s=e*this.data.stride+this.offset;for(let e=0;e<this.itemSize;e++)t.push(this.data.array[s+e])}return{itemSize:this.itemSize,type:this.array.constructor.name,array:t,normalized:this.normalized}}return void 0===t.interleavedBuffers&&(t.interleavedBuffers={}),void 0===t.interleavedBuffers[this.data.uuid]&&(t.interleavedBuffers[this.data.uuid]=this.data.toJSON(t)),{isInterleavedBufferAttribute:!0,itemSize:this.itemSize,data:this.data.uuid,offset:this.offset,normalized:this.normalized}}}class ho extends tn{constructor(t){super(),this.isSpriteMaterial=!0,this.type="SpriteMaterial",this.color=new $r(16777215),this.map=null,this.alphaMap=null,this.rotation=0,this.sizeAttenuation=!0,this.transparent=!0,this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.alphaMap=t.alphaMap,this.rotation=t.rotation,this.sizeAttenuation=t.sizeAttenuation,this.fog=t.fog,this}}let lo;const co=new Ii,uo=new Ii,po=new Ii,mo=new Zs,yo=new Zs,fo=new nr,go=new Ii,xo=new Ii,bo=new Ii,vo=new Zs,wo=new Zs,Mo=new Zs;class So extends Rr{constructor(t=new ho){if(super(),this.isSprite=!0,this.type="Sprite",void 0===lo){lo=new zn;const t=new Float32Array([-.5,-.5,0,0,0,.5,-.5,0,1,0,.5,.5,0,1,1,-.5,.5,0,0,1]),e=new no(t,5);lo.setIndex([0,1,2,0,2,3]),lo.setAttribute("position",new ao(e,3,0,!1)),lo.setAttribute("uv",new ao(e,2,3,!1))}this.geometry=lo,this.material=t,this.center=new Zs(.5,.5)}raycast(t,e){null===t.camera&&console.error('THREE.Sprite: "Raycaster.camera" needs to be set in order to raycast against sprites.'),uo.setFromMatrixScale(this.matrixWorld),fo.copy(t.camera.matrixWorld),this.modelViewMatrix.multiplyMatrices(t.camera.matrixWorldInverse,this.matrixWorld),po.setFromMatrixPosition(this.modelViewMatrix),t.camera.isPerspectiveCamera&&!1===this.material.sizeAttenuation&&uo.multiplyScalar(-po.z);const s=this.material.rotation;let i,r;0!==s&&(r=Math.cos(s),i=Math.sin(s));const n=this.center;_o(go.set(-.5,-.5,0),po,n,uo,i,r),_o(xo.set(.5,-.5,0),po,n,uo,i,r),_o(bo.set(.5,.5,0),po,n,uo,i,r),vo.set(0,0),wo.set(1,0),Mo.set(1,1);let o=t.ray.intersectTriangle(go,xo,bo,!1,co);if(null===o&&(_o(xo.set(-.5,.5,0),po,n,uo,i,r),wo.set(0,1),o=t.ray.intersectTriangle(go,bo,xo,!1,co),null===o))return;const a=t.ray.origin.distanceTo(co);a<t.near||a>t.far||e.push({distance:a,point:co.clone(),uv:Jr.getInterpolation(co,go,xo,bo,vo,wo,Mo,new Zs),face:null,object:this})}copy(t,e){return super.copy(t,e),void 0!==t.center&&this.center.copy(t.center),this.material=t.material,this}}function _o(t,e,s,i,r,n){mo.subVectors(t,s).addScalar(.5).multiply(i),void 0!==r?(yo.x=n*mo.x-r*mo.y,yo.y=r*mo.x+n*mo.y):yo.copy(mo),t.copy(e),t.x+=yo.x,t.y+=yo.y,t.applyMatrix4(fo)}const Ao=new Ii,To=new Ii;class zo extends Rr{constructor(){super(),this._currentLevel=0,this.type="LOD",Object.defineProperties(this,{levels:{enumerable:!0,value:[]},isLOD:{value:!0}}),this.autoUpdate=!0}copy(t){super.copy(t,!1);const e=t.levels;for(let t=0,s=e.length;t<s;t++){const s=e[t];this.addLevel(s.object.clone(),s.distance,s.hysteresis)}return this.autoUpdate=t.autoUpdate,this}addLevel(t,e=0,s=0){e=Math.abs(e);const i=this.levels;let r;for(r=0;r<i.length&&!(e<i[r].distance);r++);return i.splice(r,0,{distance:e,hysteresis:s,object:t}),this.add(t),this}removeLevel(t){const e=this.levels;for(let s=0;s<e.length;s++)if(e[s].distance===t){const t=e.splice(s,1);return this.remove(t[0].object),!0}return!1}getCurrentLevel(){return this._currentLevel}getObjectForDistance(t){const e=this.levels;if(e.length>0){let s,i;for(s=1,i=e.length;s<i;s++){let i=e[s].distance;if(e[s].object.visible&&(i-=i*e[s].hysteresis),t<i)break}return e[s-1].object}return null}raycast(t,e){if(this.levels.length>0){Ao.setFromMatrixPosition(this.matrixWorld);const s=t.ray.origin.distanceTo(Ao);this.getObjectForDistance(s).raycast(t,e)}}update(t){const e=this.levels;if(e.length>1){Ao.setFromMatrixPosition(t.matrixWorld),To.setFromMatrixPosition(this.matrixWorld);const s=Ao.distanceTo(To)/t.zoom;let i,r;for(e[0].object.visible=!0,i=1,r=e.length;i<r;i++){let t=e[i].distance;if(e[i].object.visible&&(t-=t*e[i].hysteresis),!(s>=t))break;e[i-1].object.visible=!1,e[i].object.visible=!0}for(this._currentLevel=i-1;i<r;i++)e[i].object.visible=!1}}toJSON(t){const e=super.toJSON(t);!1===this.autoUpdate&&(e.object.autoUpdate=!1),e.object.levels=[];const s=this.levels;for(let t=0,i=s.length;t<i;t++){const i=s[t];e.object.levels.push({object:i.object.uuid,distance:i.distance,hysteresis:i.hysteresis})}return e}}const Co=new Ii,Io=new wi,Bo=new wi,ko=new Ii,Ro=new nr,Eo=new Ii,Po=new Gi,Oo=new nr,Fo=new rr;class No extends Vn{constructor(t,e){super(t,e),this.isSkinnedMesh=!0,this.type="SkinnedMesh",this.bindMode=nt,this.bindMatrix=new nr,this.bindMatrixInverse=new nr,this.boundingBox=null,this.boundingSphere=null}computeBoundingBox(){const t=this.geometry;null===this.boundingBox&&(this.boundingBox=new Ri),this.boundingBox.makeEmpty();const e=t.getAttribute("position");for(let t=0;t<e.count;t++)this.getVertexPosition(t,Eo),this.boundingBox.expandByPoint(Eo)}computeBoundingSphere(){const t=this.geometry;null===this.boundingSphere&&(this.boundingSphere=new Gi),this.boundingSphere.makeEmpty();const e=t.getAttribute("position");for(let t=0;t<e.count;t++)this.getVertexPosition(t,Eo),this.boundingSphere.expandByPoint(Eo)}copy(t,e){return super.copy(t,e),this.bindMode=t.bindMode,this.bindMatrix.copy(t.bindMatrix),this.bindMatrixInverse.copy(t.bindMatrixInverse),this.skeleton=t.skeleton,null!==t.boundingBox&&(this.boundingBox=t.boundingBox.clone()),null!==t.boundingSphere&&(this.boundingSphere=t.boundingSphere.clone()),this}raycast(t,e){const s=this.material,i=this.matrixWorld;void 0!==s&&(null===this.boundingSphere&&this.computeBoundingSphere(),Po.copy(this.boundingSphere),Po.applyMatrix4(i),!1!==t.ray.intersectsSphere(Po)&&(Oo.copy(i).invert(),Fo.copy(t.ray).applyMatrix4(Oo),null!==this.boundingBox&&!1===Fo.intersectsBox(this.boundingBox)||this._computeIntersections(t,e,Fo)))}getVertexPosition(t,e){return super.getVertexPosition(t,e),this.applyBoneTransform(t,e),e}bind(t,e){this.skeleton=t,void 0===e&&(this.updateMatrixWorld(!0),this.skeleton.calculateInverses(),e=this.matrixWorld),this.bindMatrix.copy(e),this.bindMatrixInverse.copy(e).invert()}pose(){this.skeleton.pose()}normalizeSkinWeights(){const t=new wi,e=this.geometry.attributes.skinWeight;for(let s=0,i=e.count;s<i;s++){t.fromBufferAttribute(e,s);const i=1/t.manhattanLength();i!==1/0?t.multiplyScalar(i):t.set(1,0,0,0),e.setXYZW(s,t.x,t.y,t.z,t.w)}}updateMatrixWorld(t){super.updateMatrixWorld(t),this.bindMode===nt?this.bindMatrixInverse.copy(this.matrixWorld).invert():this.bindMode===ot?this.bindMatrixInverse.copy(this.bindMatrix).invert():console.warn("THREE.SkinnedMesh: Unrecognized bindMode: "+this.bindMode)}applyBoneTransform(t,e){const s=this.skeleton,i=this.geometry;Io.fromBufferAttribute(i.attributes.skinIndex,t),Bo.fromBufferAttribute(i.attributes.skinWeight,t),Co.copy(e).applyMatrix4(this.bindMatrix),e.set(0,0,0);for(let t=0;t<4;t++){const i=Bo.getComponent(t);if(0!==i){const r=Io.getComponent(t);Ro.multiplyMatrices(s.bones[r].matrixWorld,s.boneInverses[r]),e.addScaledVector(ko.copy(Co).applyMatrix4(Ro),i)}}return e.applyMatrix4(this.bindMatrixInverse)}}class Lo extends Rr{constructor(){super(),this.isBone=!0,this.type="Bone"}}class Vo extends vi{constructor(t=null,e=1,s=1,i,r,n,o,a,h=1003,l=1003,c,u){super(null,n,o,a,h,l,i,r,c,u),this.isDataTexture=!0,this.image={data:t,width:e,height:s},this.generateMipmaps=!1,this.flipY=!1,this.unpackAlignment=1}}const Wo=new nr,jo=new nr;class Uo{constructor(t=[],e=[]){this.uuid=Us(),this.bones=t.slice(0),this.boneInverses=e,this.boneMatrices=null,this.boneTexture=null,this.init()}init(){const t=this.bones,e=this.boneInverses;if(this.boneMatrices=new Float32Array(16*t.length),0===e.length)this.calculateInverses();else if(t.length!==e.length){console.warn("THREE.Skeleton: Number of inverse bone matrices does not match amount of bones."),this.boneInverses=[];for(let t=0,e=this.bones.length;t<e;t++)this.boneInverses.push(new nr)}}calculateInverses(){this.boneInverses.length=0;for(let t=0,e=this.bones.length;t<e;t++){const e=new nr;this.bones[t]&&e.copy(this.bones[t].matrixWorld).invert(),this.boneInverses.push(e)}}pose(){for(let t=0,e=this.bones.length;t<e;t++){const e=this.bones[t];e&&e.matrixWorld.copy(this.boneInverses[t]).invert()}for(let t=0,e=this.bones.length;t<e;t++){const e=this.bones[t];e&&(e.parent&&e.parent.isBone?(e.matrix.copy(e.parent.matrixWorld).invert(),e.matrix.multiply(e.matrixWorld)):e.matrix.copy(e.matrixWorld),e.matrix.decompose(e.position,e.quaternion,e.scale))}}update(){const t=this.bones,e=this.boneInverses,s=this.boneMatrices,i=this.boneTexture;for(let i=0,r=t.length;i<r;i++){const r=t[i]?t[i].matrixWorld:jo;Wo.multiplyMatrices(r,e[i]),Wo.toArray(s,16*i)}null!==i&&(i.needsUpdate=!0)}clone(){return new Uo(this.bones,this.boneInverses)}computeBoneTexture(){let t=Math.sqrt(4*this.bones.length);t=4*Math.ceil(t/4),t=Math.max(t,4);const e=new Float32Array(t*t*4);e.set(this.boneMatrices);const s=new Vo(e,t,t,Wt,Rt);return s.needsUpdate=!0,this.boneMatrices=e,this.boneTexture=s,this}getBoneByName(t){for(let e=0,s=this.bones.length;e<s;e++){const s=this.bones[e];if(s.name===t)return s}}dispose(){null!==this.boneTexture&&(this.boneTexture.dispose(),this.boneTexture=null)}fromJSON(t,e){this.uuid=t.uuid;for(let s=0,i=t.bones.length;s<i;s++){const i=t.bones[s];let r=e[i];void 0===r&&(console.warn("THREE.Skeleton: No bone found with UUID:",i),r=new Lo),this.bones.push(r),this.boneInverses.push((new nr).fromArray(t.boneInverses[s]))}return this.init(),this}toJSON(){const t={metadata:{version:4.6,type:"Skeleton",generator:"Skeleton.toJSON"},bones:[],boneInverses:[]};t.uuid=this.uuid;const e=this.bones,s=this.boneInverses;for(let i=0,r=e.length;i<r;i++){const r=e[i];t.bones.push(r.uuid);const n=s[i];t.boneInverses.push(n.toArray())}return t}}class Do extends cn{constructor(t,e,s,i=1){super(t,e,s),this.isInstancedBufferAttribute=!0,this.meshPerAttribute=i}copy(t){return super.copy(t),this.meshPerAttribute=t.meshPerAttribute,this}toJSON(){const t=super.toJSON();return t.meshPerAttribute=this.meshPerAttribute,t.isInstancedBufferAttribute=!0,t}}const Ho=new nr,qo=new nr,Jo=[],Xo=new Ri,Yo=new nr,Zo=new Vn,Go=new Gi;class $o extends Vn{constructor(t,e,s){super(t,e),this.isInstancedMesh=!0,this.instanceMatrix=new Do(new Float32Array(16*s),16),this.instanceColor=null,this.morphTexture=null,this.count=s,this.boundingBox=null,this.boundingSphere=null;for(let t=0;t<s;t++)this.setMatrixAt(t,Yo)}computeBoundingBox(){const t=this.geometry,e=this.count;null===this.boundingBox&&(this.boundingBox=new Ri),null===t.boundingBox&&t.computeBoundingBox(),this.boundingBox.makeEmpty();for(let s=0;s<e;s++)this.getMatrixAt(s,Ho),Xo.copy(t.boundingBox).applyMatrix4(Ho),this.boundingBox.union(Xo)}computeBoundingSphere(){const t=this.geometry,e=this.count;null===this.boundingSphere&&(this.boundingSphere=new Gi),null===t.boundingSphere&&t.computeBoundingSphere(),this.boundingSphere.makeEmpty();for(let s=0;s<e;s++)this.getMatrixAt(s,Ho),Go.copy(t.boundingSphere).applyMatrix4(Ho),this.boundingSphere.union(Go)}copy(t,e){return super.copy(t,e),this.instanceMatrix.copy(t.instanceMatrix),null!==t.morphTexture&&(this.morphTexture=t.morphTexture.clone()),null!==t.instanceColor&&(this.instanceColor=t.instanceColor.clone()),this.count=t.count,null!==t.boundingBox&&(this.boundingBox=t.boundingBox.clone()),null!==t.boundingSphere&&(this.boundingSphere=t.boundingSphere.clone()),this}getColorAt(t,e){e.fromArray(this.instanceColor.array,3*t)}getMatrixAt(t,e){e.fromArray(this.instanceMatrix.array,16*t)}getMorphAt(t,e){const s=e.morphTargetInfluences,i=this.morphTexture.source.data.data,r=t*(s.length+1)+1;for(let t=0;t<s.length;t++)s[t]=i[r+t]}raycast(t,e){const s=this.matrixWorld,i=this.count;if(Zo.geometry=this.geometry,Zo.material=this.material,void 0!==Zo.material&&(null===this.boundingSphere&&this.computeBoundingSphere(),Go.copy(this.boundingSphere),Go.applyMatrix4(s),!1!==t.ray.intersectsSphere(Go)))for(let r=0;r<i;r++){this.getMatrixAt(r,Ho),qo.multiplyMatrices(s,Ho),Zo.matrixWorld=qo,Zo.raycast(t,Jo);for(let t=0,s=Jo.length;t<s;t++){const s=Jo[t];s.instanceId=r,s.object=this,e.push(s)}Jo.length=0}}setColorAt(t,e){null===this.instanceColor&&(this.instanceColor=new Do(new Float32Array(3*this.instanceMatrix.count).fill(1),3)),e.toArray(this.instanceColor.array,3*t)}setMatrixAt(t,e){e.toArray(this.instanceMatrix.array,16*t)}setMorphAt(t,e){const s=e.morphTargetInfluences,i=s.length+1;null===this.morphTexture&&(this.morphTexture=new Vo(new Float32Array(i*this.count),i,this.count,qt,Rt));const r=this.morphTexture.source.data.data;let n=0;for(let t=0;t<s.length;t++)n+=s[t];const o=this.geometry.morphTargetsRelative?1:1-n,a=i*t;r[a]=o,r.set(s,a+1)}updateMorphTargets(){}dispose(){return this.dispatchEvent({type:"dispose"}),null!==this.morphTexture&&(this.morphTexture.dispose(),this.morphTexture=null),this}}const Qo=new Ii,Ko=new Ii,ta=new Gs;class ea{constructor(t=new Ii(1,0,0),e=0){this.isPlane=!0,this.normal=t,this.constant=e}set(t,e){return this.normal.copy(t),this.constant=e,this}setComponents(t,e,s,i){return this.normal.set(t,e,s),this.constant=i,this}setFromNormalAndCoplanarPoint(t,e){return this.normal.copy(t),this.constant=-e.dot(this.normal),this}setFromCoplanarPoints(t,e,s){const i=Qo.subVectors(s,e).cross(Ko.subVectors(t,e)).normalize();return this.setFromNormalAndCoplanarPoint(i,t),this}copy(t){return this.normal.copy(t.normal),this.constant=t.constant,this}normalize(){const t=1/this.normal.length();return this.normal.multiplyScalar(t),this.constant*=t,this}negate(){return this.constant*=-1,this.normal.negate(),this}distanceToPoint(t){return this.normal.dot(t)+this.constant}distanceToSphere(t){return this.distanceToPoint(t.center)-t.radius}projectPoint(t,e){return e.copy(t).addScaledVector(this.normal,-this.distanceToPoint(t))}intersectLine(t,e){const s=t.delta(Qo),i=this.normal.dot(s);if(0===i)return 0===this.distanceToPoint(t.start)?e.copy(t.start):null;const r=-(t.start.dot(this.normal)+this.constant)/i;return r<0||r>1?null:e.copy(t.start).addScaledVector(s,r)}intersectsLine(t){const e=this.distanceToPoint(t.start),s=this.distanceToPoint(t.end);return e<0&&s>0||s<0&&e>0}intersectsBox(t){return t.intersectsPlane(this)}intersectsSphere(t){return t.intersectsPlane(this)}coplanarPoint(t){return t.copy(this.normal).multiplyScalar(-this.constant)}applyMatrix4(t,e){const s=e||ta.getNormalMatrix(t),i=this.coplanarPoint(Qo).applyMatrix4(t),r=this.normal.applyMatrix3(s).normalize();return this.constant=-i.dot(r),this}translate(t){return this.constant-=t.dot(this.normal),this}equals(t){return t.normal.equals(this.normal)&&t.constant===this.constant}clone(){return(new this.constructor).copy(this)}}const sa=new Gi,ia=new Ii;class ra{constructor(t=new ea,e=new ea,s=new ea,i=new ea,r=new ea,n=new ea){this.planes=[t,e,s,i,r,n]}set(t,e,s,i,r,n){const o=this.planes;return o[0].copy(t),o[1].copy(e),o[2].copy(s),o[3].copy(i),o[4].copy(r),o[5].copy(n),this}copy(t){const e=this.planes;for(let s=0;s<6;s++)e[s].copy(t.planes[s]);return this}setFromProjectionMatrix(t,e=2e3){const s=this.planes,i=t.elements,r=i[0],n=i[1],o=i[2],a=i[3],h=i[4],l=i[5],c=i[6],u=i[7],d=i[8],p=i[9],m=i[10],y=i[11],f=i[12],g=i[13],x=i[14],b=i[15];if(s[0].setComponents(a-r,u-h,y-d,b-f).normalize(),s[1].setComponents(a+r,u+h,y+d,b+f).normalize(),s[2].setComponents(a+n,u+l,y+p,b+g).normalize(),s[3].setComponents(a-n,u-l,y-p,b-g).normalize(),s[4].setComponents(a-o,u-c,y-m,b-x).normalize(),e===Os)s[5].setComponents(a+o,u+c,y+m,b+x).normalize();else{if(e!==Fs)throw new Error("THREE.Frustum.setFromProjectionMatrix(): Invalid coordinate system: "+e);s[5].setComponents(o,c,m,x).normalize()}return this}intersectsObject(t){if(void 0!==t.boundingSphere)null===t.boundingSphere&&t.computeBoundingSphere(),sa.copy(t.boundingSphere).applyMatrix4(t.matrixWorld);else{const e=t.geometry;null===e.boundingSphere&&e.computeBoundingSphere(),sa.copy(e.boundingSphere).applyMatrix4(t.matrixWorld)}return this.intersectsSphere(sa)}intersectsSprite(t){return sa.center.set(0,0,0),sa.radius=.*********1865476,sa.applyMatrix4(t.matrixWorld),this.intersectsSphere(sa)}intersectsSphere(t){const e=this.planes,s=t.center,i=-t.radius;for(let t=0;t<6;t++){if(e[t].distanceToPoint(s)<i)return!1}return!0}intersectsBox(t){const e=this.planes;for(let s=0;s<6;s++){const i=e[s];if(ia.x=i.normal.x>0?t.max.x:t.min.x,ia.y=i.normal.y>0?t.max.y:t.min.y,ia.z=i.normal.z>0?t.max.z:t.min.z,i.distanceToPoint(ia)<0)return!1}return!0}containsPoint(t){const e=this.planes;for(let s=0;s<6;s++)if(e[s].distanceToPoint(t)<0)return!1;return!0}clone(){return(new this.constructor).copy(this)}}function na(t,e){return t-e}function oa(t,e){return t.z-e.z}function aa(t,e){return e.z-t.z}class ha{constructor(){this.index=0,this.pool=[],this.list=[]}push(t,e,s,i){const r=this.pool,n=this.list;this.index>=r.length&&r.push({start:-1,count:-1,z:-1,index:-1});const o=r[this.index];n.push(o),this.index++,o.start=t,o.count=e,o.z=s,o.index=i}reset(){this.list.length=0,this.index=0}}const la=new nr,ca=new $r(1,1,1),ua=new ra,da=new Ri,pa=new Gi,ma=new Ii,ya=new Ii,fa=new Ii,ga=new ha,xa=new Vn,ba=[];function va(t,e,s=0){const i=e.itemSize;if(t.isInterleavedBufferAttribute||t.array.constructor!==e.array.constructor){const r=t.count;for(let n=0;n<r;n++)for(let r=0;r<i;r++)e.setComponent(n+s,r,t.getComponent(n,r))}else e.array.set(t.array,s*i);e.needsUpdate=!0}function wa(t,e){if(t.constructor!==e.constructor){const s=Math.min(t.length,e.length);for(let i=0;i<s;i++)e[i]=t[i]}else{const s=Math.min(t.length,e.length);e.set(new t.constructor(t.buffer,0,s))}}class Ma extends Vn{get maxInstanceCount(){return this._maxInstanceCount}get instanceCount(){return this._instanceInfo.length-this._availableInstanceIds.length}get unusedVertexCount(){return this._maxVertexCount-this._nextVertexStart}get unusedIndexCount(){return this._maxIndexCount-this._nextIndexStart}constructor(t,e,s=2*e,i){super(new zn,i),this.isBatchedMesh=!0,this.perObjectFrustumCulled=!0,this.sortObjects=!0,this.boundingBox=null,this.boundingSphere=null,this.customSort=null,this._instanceInfo=[],this._geometryInfo=[],this._availableInstanceIds=[],this._availableGeometryIds=[],this._nextIndexStart=0,this._nextVertexStart=0,this._geometryCount=0,this._visibilityChanged=!0,this._geometryInitialized=!1,this._maxInstanceCount=t,this._maxVertexCount=e,this._maxIndexCount=s,this._multiDrawCounts=new Int32Array(t),this._multiDrawStarts=new Int32Array(t),this._multiDrawCount=0,this._multiDrawInstances=null,this._matricesTexture=null,this._indirectTexture=null,this._colorsTexture=null,this._initMatricesTexture(),this._initIndirectTexture()}_initMatricesTexture(){let t=Math.sqrt(4*this._maxInstanceCount);t=4*Math.ceil(t/4),t=Math.max(t,4);const e=new Float32Array(t*t*4),s=new Vo(e,t,t,Wt,Rt);this._matricesTexture=s}_initIndirectTexture(){let t=Math.sqrt(this._maxInstanceCount);t=Math.ceil(t);const e=new Uint32Array(t*t),s=new Vo(e,t,t,Jt,kt);this._indirectTexture=s}_initColorsTexture(){let t=Math.sqrt(this._maxInstanceCount);t=Math.ceil(t);const e=new Float32Array(t*t*4).fill(1),s=new Vo(e,t,t,Wt,Rt);s.colorSpace=ui.workingColorSpace,this._colorsTexture=s}_initializeGeometry(t){const e=this.geometry,s=this._maxVertexCount,i=this._maxIndexCount;if(!1===this._geometryInitialized){for(const i in t.attributes){const r=t.getAttribute(i),{array:n,itemSize:o,normalized:a}=r,h=new n.constructor(s*o),l=new cn(h,o,a);e.setAttribute(i,l)}if(null!==t.getIndex()){const t=s>65535?new Uint32Array(i):new Uint16Array(i);e.setIndex(new cn(t,1))}this._geometryInitialized=!0}}_validateGeometry(t){const e=this.geometry;if(Boolean(t.getIndex())!==Boolean(e.getIndex()))throw new Error('THREE.BatchedMesh: All geometries must consistently have "index".');for(const s in e.attributes){if(!t.hasAttribute(s))throw new Error(`THREE.BatchedMesh: Added geometry missing "${s}". All geometries must have consistent attributes.`);const i=t.getAttribute(s),r=e.getAttribute(s);if(i.itemSize!==r.itemSize||i.normalized!==r.normalized)throw new Error("THREE.BatchedMesh: All attributes must have a consistent itemSize and normalized value.")}}validateInstanceId(t){const e=this._instanceInfo;if(t<0||t>=e.length||!1===e[t].active)throw new Error(`THREE.BatchedMesh: Invalid instanceId ${t}. Instance is either out of range or has been deleted.`)}validateGeometryId(t){const e=this._geometryInfo;if(t<0||t>=e.length||!1===e[t].active)throw new Error(`THREE.BatchedMesh: Invalid geometryId ${t}. Geometry is either out of range or has been deleted.`)}setCustomSort(t){return this.customSort=t,this}computeBoundingBox(){null===this.boundingBox&&(this.boundingBox=new Ri);const t=this.boundingBox,e=this._instanceInfo;t.makeEmpty();for(let s=0,i=e.length;s<i;s++){if(!1===e[s].active)continue;const i=e[s].geometryIndex;this.getMatrixAt(s,la),this.getBoundingBoxAt(i,da).applyMatrix4(la),t.union(da)}}computeBoundingSphere(){null===this.boundingSphere&&(this.boundingSphere=new Gi);const t=this.boundingSphere,e=this._instanceInfo;t.makeEmpty();for(let s=0,i=e.length;s<i;s++){if(!1===e[s].active)continue;const i=e[s].geometryIndex;this.getMatrixAt(s,la),this.getBoundingSphereAt(i,pa).applyMatrix4(la),t.union(pa)}}addInstance(t){if(this._instanceInfo.length>=this.maxInstanceCount&&0===this._availableInstanceIds.length)throw new Error("THREE.BatchedMesh: Maximum item count reached.");const e={visible:!0,active:!0,geometryIndex:t};let s=null;this._availableInstanceIds.length>0?(this._availableInstanceIds.sort(na),s=this._availableInstanceIds.shift(),this._instanceInfo[s]=e):(s=this._instanceInfo.length,this._instanceInfo.push(e));const i=this._matricesTexture;la.identity().toArray(i.image.data,16*s),i.needsUpdate=!0;const r=this._colorsTexture;return r&&(ca.toArray(r.image.data,4*s),r.needsUpdate=!0),this._visibilityChanged=!0,s}addGeometry(t,e=-1,s=-1){this._initializeGeometry(t),this._validateGeometry(t);const i={vertexStart:-1,vertexCount:-1,reservedVertexCount:-1,indexStart:-1,indexCount:-1,reservedIndexCount:-1,start:-1,count:-1,boundingBox:null,boundingSphere:null,active:!0},r=this._geometryInfo;i.vertexStart=this._nextVertexStart,i.reservedVertexCount=-1===e?t.getAttribute("position").count:e;const n=t.getIndex();if(null!==n&&(i.indexStart=this._nextIndexStart,i.reservedIndexCount=-1===s?n.count:s),-1!==i.indexStart&&i.indexStart+i.reservedIndexCount>this._maxIndexCount||i.vertexStart+i.reservedVertexCount>this._maxVertexCount)throw new Error("THREE.BatchedMesh: Reserved space request exceeds the maximum buffer size.");let o;return this._availableGeometryIds.length>0?(this._availableGeometryIds.sort(na),o=this._availableGeometryIds.shift(),r[o]=i):(o=this._geometryCount,this._geometryCount++,r.push(i)),this.setGeometryAt(o,t),this._nextIndexStart=i.indexStart+i.reservedIndexCount,this._nextVertexStart=i.vertexStart+i.reservedVertexCount,o}setGeometryAt(t,e){if(t>=this._geometryCount)throw new Error("THREE.BatchedMesh: Maximum geometry count reached.");this._validateGeometry(e);const s=this.geometry,i=null!==s.getIndex(),r=s.getIndex(),n=e.getIndex(),o=this._geometryInfo[t];if(i&&n.count>o.reservedIndexCount||e.attributes.position.count>o.reservedVertexCount)throw new Error("THREE.BatchedMesh: Reserved space not large enough for provided geometry.");const a=o.vertexStart,h=o.reservedVertexCount;o.vertexCount=e.getAttribute("position").count;for(const t in s.attributes){const i=e.getAttribute(t),r=s.getAttribute(t);va(i,r,a);const n=i.itemSize;for(let t=i.count,e=h;t<e;t++){const e=a+t;for(let t=0;t<n;t++)r.setComponent(e,t,0)}r.needsUpdate=!0,r.addUpdateRange(a*n,h*n)}if(i){const t=o.indexStart,s=o.reservedIndexCount;o.indexCount=e.getIndex().count;for(let e=0;e<n.count;e++)r.setX(t+e,a+n.getX(e));for(let e=n.count,i=s;e<i;e++)r.setX(t+e,a);r.needsUpdate=!0,r.addUpdateRange(t,o.reservedIndexCount)}return o.start=i?o.indexStart:o.vertexStart,o.count=i?o.indexCount:o.vertexCount,o.boundingBox=null,null!==e.boundingBox&&(o.boundingBox=e.boundingBox.clone()),o.boundingSphere=null,null!==e.boundingSphere&&(o.boundingSphere=e.boundingSphere.clone()),this._visibilityChanged=!0,t}deleteGeometry(t){const e=this._geometryInfo;if(t>=e.length||!1===e[t].active)return this;const s=this._instanceInfo;for(let e=0,i=s.length;e<i;e++)s[e].geometryIndex===t&&this.deleteInstance(e);return e[t].active=!1,this._availableGeometryIds.push(t),this._visibilityChanged=!0,this}deleteInstance(t){return this.validateInstanceId(t),this._instanceInfo[t].active=!1,this._availableInstanceIds.push(t),this._visibilityChanged=!0,this}optimize(){let t=0,e=0;const s=this._geometryInfo,i=s.map(((t,e)=>e)).sort(((t,e)=>s[t].vertexStart-s[e].vertexStart)),r=this.geometry;for(let n=0,o=s.length;n<o;n++){const o=i[n],a=s[o];if(!1!==a.active){if(null!==r.index){if(a.indexStart!==e){const{indexStart:s,vertexStart:i,reservedIndexCount:n}=a,o=r.index,h=o.array,l=t-i;for(let t=s;t<s+n;t++)h[t]=h[t]+l;o.array.copyWithin(e,s,s+n),o.addUpdateRange(e,n),a.indexStart=e}e+=a.reservedIndexCount}if(a.vertexStart!==t){const{vertexStart:e,reservedVertexCount:s}=a,i=r.attributes;for(const r in i){const n=i[r],{array:o,itemSize:a}=n;o.copyWithin(t*a,e*a,(e+s)*a),n.addUpdateRange(t*a,s*a)}a.vertexStart=t}t+=a.reservedVertexCount,a.start=r.index?a.indexStart:a.vertexStart,this._nextIndexStart=r.index?a.indexStart+a.reservedIndexCount:0,this._nextVertexStart=a.vertexStart+a.reservedVertexCount}}return this}getBoundingBoxAt(t,e){if(t>=this._geometryCount)return null;const s=this.geometry,i=this._geometryInfo[t];if(null===i.boundingBox){const t=new Ri,e=s.index,r=s.attributes.position;for(let s=i.start,n=i.start+i.count;s<n;s++){let i=s;e&&(i=e.getX(i)),t.expandByPoint(ma.fromBufferAttribute(r,i))}i.boundingBox=t}return e.copy(i.boundingBox),e}getBoundingSphereAt(t,e){if(t>=this._geometryCount)return null;const s=this.geometry,i=this._geometryInfo[t];if(null===i.boundingSphere){const e=new Gi;this.getBoundingBoxAt(t,da),da.getCenter(e.center);const r=s.index,n=s.attributes.position;let o=0;for(let t=i.start,s=i.start+i.count;t<s;t++){let s=t;r&&(s=r.getX(s)),ma.fromBufferAttribute(n,s),o=Math.max(o,e.center.distanceToSquared(ma))}e.radius=Math.sqrt(o),i.boundingSphere=e}return e.copy(i.boundingSphere),e}setMatrixAt(t,e){this.validateInstanceId(t);const s=this._matricesTexture,i=this._matricesTexture.image.data;return e.toArray(i,16*t),s.needsUpdate=!0,this}getMatrixAt(t,e){return this.validateInstanceId(t),e.fromArray(this._matricesTexture.image.data,16*t)}setColorAt(t,e){return this.validateInstanceId(t),null===this._colorsTexture&&this._initColorsTexture(),e.toArray(this._colorsTexture.image.data,4*t),this._colorsTexture.needsUpdate=!0,this}getColorAt(t,e){return this.validateInstanceId(t),e.fromArray(this._colorsTexture.image.data,4*t)}setVisibleAt(t,e){return this.validateInstanceId(t),this._instanceInfo[t].visible===e||(this._instanceInfo[t].visible=e,this._visibilityChanged=!0),this}getVisibleAt(t){return this.validateInstanceId(t),this._instanceInfo[t].visible}setGeometryIdAt(t,e){return this.validateInstanceId(t),this.validateGeometryId(e),this._instanceInfo[t].geometryIndex=e,this}getGeometryIdAt(t){return this.validateInstanceId(t),this._instanceInfo[t].geometryIndex}getGeometryRangeAt(t,e={}){this.validateGeometryId(t);const s=this._geometryInfo[t];return e.vertexStart=s.vertexStart,e.vertexCount=s.vertexCount,e.reservedVertexCount=s.reservedVertexCount,e.indexStart=s.indexStart,e.indexCount=s.indexCount,e.reservedIndexCount=s.reservedIndexCount,e.start=s.start,e.count=s.count,e}setInstanceCount(t){const e=this._availableInstanceIds,s=this._instanceInfo;for(e.sort(na);e[e.length-1]===s.length;)s.pop(),e.pop();if(t<s.length)throw new Error(`BatchedMesh: Instance ids outside the range ${t} are being used. Cannot shrink instance count.`);const i=new Int32Array(t),r=new Int32Array(t);wa(this._multiDrawCounts,i),wa(this._multiDrawStarts,r),this._multiDrawCounts=i,this._multiDrawStarts=r,this._maxInstanceCount=t;const n=this._indirectTexture,o=this._matricesTexture,a=this._colorsTexture;n.dispose(),this._initIndirectTexture(),wa(n.image.data,this._indirectTexture.image.data),o.dispose(),this._initMatricesTexture(),wa(o.image.data,this._matricesTexture.image.data),a&&(a.dispose(),this._initColorsTexture(),wa(a.image.data,this._colorsTexture.image.data))}setGeometrySize(t,e){const s=[...this._geometryInfo].filter((t=>t.active));if(Math.max(...s.map((t=>t.vertexStart+t.reservedVertexCount)))>t)throw new Error(`BatchedMesh: Geometry vertex values are being used outside the range ${e}. Cannot shrink further.`);if(this.geometry.index){if(Math.max(...s.map((t=>t.indexStart+t.reservedIndexCount)))>e)throw new Error(`BatchedMesh: Geometry index values are being used outside the range ${e}. Cannot shrink further.`)}const i=this.geometry;i.dispose(),this._maxVertexCount=t,this._maxIndexCount=e,this._geometryInitialized&&(this._geometryInitialized=!1,this.geometry=new zn,this._initializeGeometry(i));const r=this.geometry;i.index&&wa(i.index.array,r.index.array);for(const t in i.attributes)wa(i.attributes[t].array,r.attributes[t].array)}raycast(t,e){const s=this._instanceInfo,i=this._geometryInfo,r=this.matrixWorld,n=this.geometry;xa.material=this.material,xa.geometry.index=n.index,xa.geometry.attributes=n.attributes,null===xa.geometry.boundingBox&&(xa.geometry.boundingBox=new Ri),null===xa.geometry.boundingSphere&&(xa.geometry.boundingSphere=new Gi);for(let n=0,o=s.length;n<o;n++){if(!s[n].visible||!s[n].active)continue;const o=s[n].geometryIndex,a=i[o];xa.geometry.setDrawRange(a.start,a.count),this.getMatrixAt(n,xa.matrixWorld).premultiply(r),this.getBoundingBoxAt(o,xa.geometry.boundingBox),this.getBoundingSphereAt(o,xa.geometry.boundingSphere),xa.raycast(t,ba);for(let t=0,s=ba.length;t<s;t++){const s=ba[t];s.object=this,s.batchId=n,e.push(s)}ba.length=0}xa.material=null,xa.geometry.index=null,xa.geometry.attributes={},xa.geometry.setDrawRange(0,1/0)}copy(t){return super.copy(t),this.geometry=t.geometry.clone(),this.perObjectFrustumCulled=t.perObjectFrustumCulled,this.sortObjects=t.sortObjects,this.boundingBox=null!==t.boundingBox?t.boundingBox.clone():null,this.boundingSphere=null!==t.boundingSphere?t.boundingSphere.clone():null,this._geometryInfo=t._geometryInfo.map((t=>({...t,boundingBox:null!==t.boundingBox?t.boundingBox.clone():null,boundingSphere:null!==t.boundingSphere?t.boundingSphere.clone():null}))),this._instanceInfo=t._instanceInfo.map((t=>({...t}))),this._maxInstanceCount=t._maxInstanceCount,this._maxVertexCount=t._maxVertexCount,this._maxIndexCount=t._maxIndexCount,this._geometryInitialized=t._geometryInitialized,this._geometryCount=t._geometryCount,this._multiDrawCounts=t._multiDrawCounts.slice(),this._multiDrawStarts=t._multiDrawStarts.slice(),this._matricesTexture=t._matricesTexture.clone(),this._matricesTexture.image.data=this._matricesTexture.image.data.slice(),null!==this._colorsTexture&&(this._colorsTexture=t._colorsTexture.clone(),this._colorsTexture.image.data=this._colorsTexture.image.data.slice()),this}dispose(){return this.geometry.dispose(),this._matricesTexture.dispose(),this._matricesTexture=null,this._indirectTexture.dispose(),this._indirectTexture=null,null!==this._colorsTexture&&(this._colorsTexture.dispose(),this._colorsTexture=null),this}onBeforeRender(t,e,s,i,r){if(!this._visibilityChanged&&!this.perObjectFrustumCulled&&!this.sortObjects)return;const n=i.getIndex(),o=null===n?1:n.array.BYTES_PER_ELEMENT,a=this._instanceInfo,h=this._multiDrawStarts,l=this._multiDrawCounts,c=this._geometryInfo,u=this.perObjectFrustumCulled,d=this._indirectTexture,p=d.image.data;u&&(la.multiplyMatrices(s.projectionMatrix,s.matrixWorldInverse).multiply(this.matrixWorld),ua.setFromProjectionMatrix(la,t.coordinateSystem));let m=0;if(this.sortObjects){la.copy(this.matrixWorld).invert(),ma.setFromMatrixPosition(s.matrixWorld).applyMatrix4(la),ya.set(0,0,-1).transformDirection(s.matrixWorld).transformDirection(la);for(let t=0,e=a.length;t<e;t++)if(a[t].visible&&a[t].active){const e=a[t].geometryIndex;this.getMatrixAt(t,la),this.getBoundingSphereAt(e,pa).applyMatrix4(la);let s=!1;if(u&&(s=!ua.intersectsSphere(pa)),!s){const s=c[e],i=fa.subVectors(pa.center,ma).dot(ya);ga.push(s.start,s.count,i,t)}}const t=ga.list,e=this.customSort;null===e?t.sort(r.transparent?aa:oa):e.call(this,t,s);for(let e=0,s=t.length;e<s;e++){const s=t[e];h[m]=s.start*o,l[m]=s.count,p[m]=s.index,m++}ga.reset()}else for(let t=0,e=a.length;t<e;t++)if(a[t].visible&&a[t].active){const e=a[t].geometryIndex;let s=!1;if(u&&(this.getMatrixAt(t,la),this.getBoundingSphereAt(e,pa).applyMatrix4(la),s=!ua.intersectsSphere(pa)),!s){const s=c[e];h[m]=s.start*o,l[m]=s.count,p[m]=t,m++}}d.needsUpdate=!0,this._multiDrawCount=m,this._visibilityChanged=!1}onBeforeShadow(t,e,s,i,r,n){this.onBeforeRender(t,null,i,r,n)}}class Sa extends tn{constructor(t){super(),this.isLineBasicMaterial=!0,this.type="LineBasicMaterial",this.color=new $r(16777215),this.map=null,this.linewidth=1,this.linecap="round",this.linejoin="round",this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.linewidth=t.linewidth,this.linecap=t.linecap,this.linejoin=t.linejoin,this.fog=t.fog,this}}const _a=new Ii,Aa=new Ii,Ta=new nr,za=new rr,Ca=new Gi,Ia=new Ii,Ba=new Ii;class ka extends Rr{constructor(t=new zn,e=new Sa){super(),this.isLine=!0,this.type="Line",this.geometry=t,this.material=e,this.updateMorphTargets()}copy(t,e){return super.copy(t,e),this.material=Array.isArray(t.material)?t.material.slice():t.material,this.geometry=t.geometry,this}computeLineDistances(){const t=this.geometry;if(null===t.index){const e=t.attributes.position,s=[0];for(let t=1,i=e.count;t<i;t++)_a.fromBufferAttribute(e,t-1),Aa.fromBufferAttribute(e,t),s[t]=s[t-1],s[t]+=_a.distanceTo(Aa);t.setAttribute("lineDistance",new bn(s,1))}else console.warn("THREE.Line.computeLineDistances(): Computation only possible with non-indexed BufferGeometry.");return this}raycast(t,e){const s=this.geometry,i=this.matrixWorld,r=t.params.Line.threshold,n=s.drawRange;if(null===s.boundingSphere&&s.computeBoundingSphere(),Ca.copy(s.boundingSphere),Ca.applyMatrix4(i),Ca.radius+=r,!1===t.ray.intersectsSphere(Ca))return;Ta.copy(i).invert(),za.copy(t.ray).applyMatrix4(Ta);const o=r/((this.scale.x+this.scale.y+this.scale.z)/3),a=o*o,h=this.isLineSegments?2:1,l=s.index,c=s.attributes.position;if(null!==l){const s=Math.max(0,n.start),i=Math.min(l.count,n.start+n.count);for(let r=s,n=i-1;r<n;r+=h){const s=l.getX(r),i=l.getX(r+1),n=Ra(this,t,za,a,s,i);n&&e.push(n)}if(this.isLineLoop){const r=l.getX(i-1),n=l.getX(s),o=Ra(this,t,za,a,r,n);o&&e.push(o)}}else{const s=Math.max(0,n.start),i=Math.min(c.count,n.start+n.count);for(let r=s,n=i-1;r<n;r+=h){const s=Ra(this,t,za,a,r,r+1);s&&e.push(s)}if(this.isLineLoop){const r=Ra(this,t,za,a,i-1,s);r&&e.push(r)}}}updateMorphTargets(){const t=this.geometry.morphAttributes,e=Object.keys(t);if(e.length>0){const s=t[e[0]];if(void 0!==s){this.morphTargetInfluences=[],this.morphTargetDictionary={};for(let t=0,e=s.length;t<e;t++){const e=s[t].name||String(t);this.morphTargetInfluences.push(0),this.morphTargetDictionary[e]=t}}}}}function Ra(t,e,s,i,r,n){const o=t.geometry.attributes.position;_a.fromBufferAttribute(o,r),Aa.fromBufferAttribute(o,n);if(s.distanceSqToSegment(_a,Aa,Ia,Ba)>i)return;Ia.applyMatrix4(t.matrixWorld);const a=e.ray.origin.distanceTo(Ia);return a<e.near||a>e.far?void 0:{distance:a,point:Ba.clone().applyMatrix4(t.matrixWorld),index:r,face:null,faceIndex:null,barycoord:null,object:t}}const Ea=new Ii,Pa=new Ii;class Oa extends ka{constructor(t,e){super(t,e),this.isLineSegments=!0,this.type="LineSegments"}computeLineDistances(){const t=this.geometry;if(null===t.index){const e=t.attributes.position,s=[];for(let t=0,i=e.count;t<i;t+=2)Ea.fromBufferAttribute(e,t),Pa.fromBufferAttribute(e,t+1),s[t]=0===t?0:s[t-1],s[t+1]=s[t]+Ea.distanceTo(Pa);t.setAttribute("lineDistance",new bn(s,1))}else console.warn("THREE.LineSegments.computeLineDistances(): Computation only possible with non-indexed BufferGeometry.");return this}}class Fa extends ka{constructor(t,e){super(t,e),this.isLineLoop=!0,this.type="LineLoop"}}class Na extends tn{constructor(t){super(),this.isPointsMaterial=!0,this.type="PointsMaterial",this.color=new $r(16777215),this.map=null,this.alphaMap=null,this.size=1,this.sizeAttenuation=!0,this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.alphaMap=t.alphaMap,this.size=t.size,this.sizeAttenuation=t.sizeAttenuation,this.fog=t.fog,this}}const La=new nr,Va=new rr,Wa=new Gi,ja=new Ii;class Ua extends Rr{constructor(t=new zn,e=new Na){super(),this.isPoints=!0,this.type="Points",this.geometry=t,this.material=e,this.updateMorphTargets()}copy(t,e){return super.copy(t,e),this.material=Array.isArray(t.material)?t.material.slice():t.material,this.geometry=t.geometry,this}raycast(t,e){const s=this.geometry,i=this.matrixWorld,r=t.params.Points.threshold,n=s.drawRange;if(null===s.boundingSphere&&s.computeBoundingSphere(),Wa.copy(s.boundingSphere),Wa.applyMatrix4(i),Wa.radius+=r,!1===t.ray.intersectsSphere(Wa))return;La.copy(i).invert(),Va.copy(t.ray).applyMatrix4(La);const o=r/((this.scale.x+this.scale.y+this.scale.z)/3),a=o*o,h=s.index,l=s.attributes.position;if(null!==h){for(let s=Math.max(0,n.start),r=Math.min(h.count,n.start+n.count);s<r;s++){const r=h.getX(s);ja.fromBufferAttribute(l,r),Da(ja,r,a,i,t,e,this)}}else{for(let s=Math.max(0,n.start),r=Math.min(l.count,n.start+n.count);s<r;s++)ja.fromBufferAttribute(l,s),Da(ja,s,a,i,t,e,this)}}updateMorphTargets(){const t=this.geometry.morphAttributes,e=Object.keys(t);if(e.length>0){const s=t[e[0]];if(void 0!==s){this.morphTargetInfluences=[],this.morphTargetDictionary={};for(let t=0,e=s.length;t<e;t++){const e=s[t].name||String(t);this.morphTargetInfluences.push(0),this.morphTargetDictionary[e]=t}}}}}function Da(t,e,s,i,r,n,o){const a=Va.distanceSqToPoint(t);if(a<s){const s=new Ii;Va.closestPointToPoint(t,s),s.applyMatrix4(i);const h=r.ray.origin.distanceTo(s);if(h<r.near||h>r.far)return;n.push({distance:h,distanceToRay:Math.sqrt(a),point:s,index:e,face:null,faceIndex:null,barycoord:null,object:o})}}class Ha extends Rr{constructor(){super(),this.isGroup=!0,this.type="Group"}}class qa extends vi{constructor(t,e,s,i,r,n,o,a,h){super(t,e,s,i,r,n,o,a,h),this.isVideoTexture=!0,this.minFilter=void 0!==n?n:wt,this.magFilter=void 0!==r?r:wt,this.generateMipmaps=!1;const l=this;"requestVideoFrameCallback"in t&&t.requestVideoFrameCallback((function e(){l.needsUpdate=!0,t.requestVideoFrameCallback(e)}))}clone(){return new this.constructor(this.image).copy(this)}update(){const t=this.image;!1==="requestVideoFrameCallback"in t&&t.readyState>=t.HAVE_CURRENT_DATA&&(this.needsUpdate=!0)}}class Ja extends vi{constructor(t,e){super({width:t,height:e}),this.isFramebufferTexture=!0,this.magFilter=ft,this.minFilter=ft,this.generateMipmaps=!1,this.needsUpdate=!0}}class Xa extends vi{constructor(t,e,s,i,r,n,o,a,h,l,c,u){super(null,n,o,a,h,l,i,r,c,u),this.isCompressedTexture=!0,this.image={width:e,height:s},this.mipmaps=t,this.flipY=!1,this.generateMipmaps=!1}}class Ya extends Xa{constructor(t,e,s,i,r,n){super(t,e,s,r,n),this.isCompressedArrayTexture=!0,this.image.depth=i,this.wrapR=mt,this.layerUpdates=new Set}addLayerUpdate(t){this.layerUpdates.add(t)}clearLayerUpdates(){this.layerUpdates.clear()}}class Za extends Xa{constructor(t,e,s){super(void 0,t[0].width,t[0].height,e,s,ht),this.isCompressedCubeTexture=!0,this.isCubeTexture=!0,this.image=t}}class Ga extends vi{constructor(t,e,s,i,r,n,o,a,h){super(t,e,s,i,r,n,o,a,h),this.isCanvasTexture=!0,this.needsUpdate=!0}}class $a extends vi{constructor(t,e,s,i,r,n,o,a,h,l=1026){if(l!==Dt&&l!==Ht)throw new Error("DepthTexture format must be either THREE.DepthFormat or THREE.DepthStencilFormat");void 0===s&&l===Dt&&(s=kt),void 0===s&&l===Ht&&(s=1020),super(null,i,r,n,o,a,l,s,h),this.isDepthTexture=!0,this.image={width:t,height:e},this.magFilter=void 0!==o?o:ft,this.minFilter=void 0!==a?a:ft,this.flipY=!1,this.generateMipmaps=!1,this.compareFunction=null}copy(t){return super.copy(t),this.compareFunction=t.compareFunction,this}toJSON(t){const e=super.toJSON(t);return null!==this.compareFunction&&(e.compareFunction=this.compareFunction),e}}class Qa{constructor(){this.type="Curve",this.arcLengthDivisions=200}getPoint(){return console.warn("THREE.Curve: .getPoint() not implemented."),null}getPointAt(t,e){const s=this.getUtoTmapping(t);return this.getPoint(s,e)}getPoints(t=5){const e=[];for(let s=0;s<=t;s++)e.push(this.getPoint(s/t));return e}getSpacedPoints(t=5){const e=[];for(let s=0;s<=t;s++)e.push(this.getPointAt(s/t));return e}getLength(){const t=this.getLengths();return t[t.length-1]}getLengths(t=this.arcLengthDivisions){if(this.cacheArcLengths&&this.cacheArcLengths.length===t+1&&!this.needsUpdate)return this.cacheArcLengths;this.needsUpdate=!1;const e=[];let s,i=this.getPoint(0),r=0;e.push(0);for(let n=1;n<=t;n++)s=this.getPoint(n/t),r+=s.distanceTo(i),e.push(r),i=s;return this.cacheArcLengths=e,e}updateArcLengths(){this.needsUpdate=!0,this.getLengths()}getUtoTmapping(t,e){const s=this.getLengths();let i=0;const r=s.length;let n;n=e||t*s[r-1];let o,a=0,h=r-1;for(;a<=h;)if(i=Math.floor(a+(h-a)/2),o=s[i]-n,o<0)a=i+1;else{if(!(o>0)){h=i;break}h=i-1}if(i=h,s[i]===n)return i/(r-1);const l=s[i];return(i+(n-l)/(s[i+1]-l))/(r-1)}getTangent(t,e){const s=1e-4;let i=t-s,r=t+s;i<0&&(i=0),r>1&&(r=1);const n=this.getPoint(i),o=this.getPoint(r),a=e||(n.isVector2?new Zs:new Ii);return a.copy(o).sub(n).normalize(),a}getTangentAt(t,e){const s=this.getUtoTmapping(t);return this.getTangent(s,e)}computeFrenetFrames(t,e){const s=new Ii,i=[],r=[],n=[],o=new Ii,a=new nr;for(let e=0;e<=t;e++){const s=e/t;i[e]=this.getTangentAt(s,new Ii)}r[0]=new Ii,n[0]=new Ii;let h=Number.MAX_VALUE;const l=Math.abs(i[0].x),c=Math.abs(i[0].y),u=Math.abs(i[0].z);l<=h&&(h=l,s.set(1,0,0)),c<=h&&(h=c,s.set(0,1,0)),u<=h&&s.set(0,0,1),o.crossVectors(i[0],s).normalize(),r[0].crossVectors(i[0],o),n[0].crossVectors(i[0],r[0]);for(let e=1;e<=t;e++){if(r[e]=r[e-1].clone(),n[e]=n[e-1].clone(),o.crossVectors(i[e-1],i[e]),o.length()>Number.EPSILON){o.normalize();const t=Math.acos(Ds(i[e-1].dot(i[e]),-1,1));r[e].applyMatrix4(a.makeRotationAxis(o,t))}n[e].crossVectors(i[e],r[e])}if(!0===e){let e=Math.acos(Ds(r[0].dot(r[t]),-1,1));e/=t,i[0].dot(o.crossVectors(r[0],r[t]))>0&&(e=-e);for(let s=1;s<=t;s++)r[s].applyMatrix4(a.makeRotationAxis(i[s],e*s)),n[s].crossVectors(i[s],r[s])}return{tangents:i,normals:r,binormals:n}}clone(){return(new this.constructor).copy(this)}copy(t){return this.arcLengthDivisions=t.arcLengthDivisions,this}toJSON(){const t={metadata:{version:4.6,type:"Curve",generator:"Curve.toJSON"}};return t.arcLengthDivisions=this.arcLengthDivisions,t.type=this.type,t}fromJSON(t){return this.arcLengthDivisions=t.arcLengthDivisions,this}}class Ka extends Qa{constructor(t=0,e=0,s=1,i=1,r=0,n=2*Math.PI,o=!1,a=0){super(),this.isEllipseCurve=!0,this.type="EllipseCurve",this.aX=t,this.aY=e,this.xRadius=s,this.yRadius=i,this.aStartAngle=r,this.aEndAngle=n,this.aClockwise=o,this.aRotation=a}getPoint(t,e=new Zs){const s=e,i=2*Math.PI;let r=this.aEndAngle-this.aStartAngle;const n=Math.abs(r)<Number.EPSILON;for(;r<0;)r+=i;for(;r>i;)r-=i;r<Number.EPSILON&&(r=n?0:i),!0!==this.aClockwise||n||(r===i?r=-i:r-=i);const o=this.aStartAngle+t*r;let a=this.aX+this.xRadius*Math.cos(o),h=this.aY+this.yRadius*Math.sin(o);if(0!==this.aRotation){const t=Math.cos(this.aRotation),e=Math.sin(this.aRotation),s=a-this.aX,i=h-this.aY;a=s*t-i*e+this.aX,h=s*e+i*t+this.aY}return s.set(a,h)}copy(t){return super.copy(t),this.aX=t.aX,this.aY=t.aY,this.xRadius=t.xRadius,this.yRadius=t.yRadius,this.aStartAngle=t.aStartAngle,this.aEndAngle=t.aEndAngle,this.aClockwise=t.aClockwise,this.aRotation=t.aRotation,this}toJSON(){const t=super.toJSON();return t.aX=this.aX,t.aY=this.aY,t.xRadius=this.xRadius,t.yRadius=this.yRadius,t.aStartAngle=this.aStartAngle,t.aEndAngle=this.aEndAngle,t.aClockwise=this.aClockwise,t.aRotation=this.aRotation,t}fromJSON(t){return super.fromJSON(t),this.aX=t.aX,this.aY=t.aY,this.xRadius=t.xRadius,this.yRadius=t.yRadius,this.aStartAngle=t.aStartAngle,this.aEndAngle=t.aEndAngle,this.aClockwise=t.aClockwise,this.aRotation=t.aRotation,this}}class th extends Ka{constructor(t,e,s,i,r,n){super(t,e,s,s,i,r,n),this.isArcCurve=!0,this.type="ArcCurve"}}function eh(){let t=0,e=0,s=0,i=0;function r(r,n,o,a){t=r,e=o,s=-3*r+3*n-2*o-a,i=2*r-2*n+o+a}return{initCatmullRom:function(t,e,s,i,n){r(e,s,n*(s-t),n*(i-e))},initNonuniformCatmullRom:function(t,e,s,i,n,o,a){let h=(e-t)/n-(s-t)/(n+o)+(s-e)/o,l=(s-e)/o-(i-e)/(o+a)+(i-s)/a;h*=o,l*=o,r(e,s,h,l)},calc:function(r){const n=r*r;return t+e*r+s*n+i*(n*r)}}}const sh=new Ii,ih=new eh,rh=new eh,nh=new eh;class oh extends Qa{constructor(t=[],e=!1,s="centripetal",i=.5){super(),this.isCatmullRomCurve3=!0,this.type="CatmullRomCurve3",this.points=t,this.closed=e,this.curveType=s,this.tension=i}getPoint(t,e=new Ii){const s=e,i=this.points,r=i.length,n=(r-(this.closed?0:1))*t;let o,a,h=Math.floor(n),l=n-h;this.closed?h+=h>0?0:(Math.floor(Math.abs(h)/r)+1)*r:0===l&&h===r-1&&(h=r-2,l=1),this.closed||h>0?o=i[(h-1)%r]:(sh.subVectors(i[0],i[1]).add(i[0]),o=sh);const c=i[h%r],u=i[(h+1)%r];if(this.closed||h+2<r?a=i[(h+2)%r]:(sh.subVectors(i[r-1],i[r-2]).add(i[r-1]),a=sh),"centripetal"===this.curveType||"chordal"===this.curveType){const t="chordal"===this.curveType?.5:.25;let e=Math.pow(o.distanceToSquared(c),t),s=Math.pow(c.distanceToSquared(u),t),i=Math.pow(u.distanceToSquared(a),t);s<1e-4&&(s=1),e<1e-4&&(e=s),i<1e-4&&(i=s),ih.initNonuniformCatmullRom(o.x,c.x,u.x,a.x,e,s,i),rh.initNonuniformCatmullRom(o.y,c.y,u.y,a.y,e,s,i),nh.initNonuniformCatmullRom(o.z,c.z,u.z,a.z,e,s,i)}else"catmullrom"===this.curveType&&(ih.initCatmullRom(o.x,c.x,u.x,a.x,this.tension),rh.initCatmullRom(o.y,c.y,u.y,a.y,this.tension),nh.initCatmullRom(o.z,c.z,u.z,a.z,this.tension));return s.set(ih.calc(l),rh.calc(l),nh.calc(l)),s}copy(t){super.copy(t),this.points=[];for(let e=0,s=t.points.length;e<s;e++){const s=t.points[e];this.points.push(s.clone())}return this.closed=t.closed,this.curveType=t.curveType,this.tension=t.tension,this}toJSON(){const t=super.toJSON();t.points=[];for(let e=0,s=this.points.length;e<s;e++){const s=this.points[e];t.points.push(s.toArray())}return t.closed=this.closed,t.curveType=this.curveType,t.tension=this.tension,t}fromJSON(t){super.fromJSON(t),this.points=[];for(let e=0,s=t.points.length;e<s;e++){const s=t.points[e];this.points.push((new Ii).fromArray(s))}return this.closed=t.closed,this.curveType=t.curveType,this.tension=t.tension,this}}function ah(t,e,s,i,r){const n=.5*(i-e),o=.5*(r-s),a=t*t;return(2*s-2*i+n+o)*(t*a)+(-3*s+3*i-2*n-o)*a+n*t+s}function hh(t,e,s,i){return function(t,e){const s=1-t;return s*s*e}(t,e)+function(t,e){return 2*(1-t)*t*e}(t,s)+function(t,e){return t*t*e}(t,i)}function lh(t,e,s,i,r){return function(t,e){const s=1-t;return s*s*s*e}(t,e)+function(t,e){const s=1-t;return 3*s*s*t*e}(t,s)+function(t,e){return 3*(1-t)*t*t*e}(t,i)+function(t,e){return t*t*t*e}(t,r)}class ch extends Qa{constructor(t=new Zs,e=new Zs,s=new Zs,i=new Zs){super(),this.isCubicBezierCurve=!0,this.type="CubicBezierCurve",this.v0=t,this.v1=e,this.v2=s,this.v3=i}getPoint(t,e=new Zs){const s=e,i=this.v0,r=this.v1,n=this.v2,o=this.v3;return s.set(lh(t,i.x,r.x,n.x,o.x),lh(t,i.y,r.y,n.y,o.y)),s}copy(t){return super.copy(t),this.v0.copy(t.v0),this.v1.copy(t.v1),this.v2.copy(t.v2),this.v3.copy(t.v3),this}toJSON(){const t=super.toJSON();return t.v0=this.v0.toArray(),t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t.v3=this.v3.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v0.fromArray(t.v0),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this.v3.fromArray(t.v3),this}}class uh extends Qa{constructor(t=new Ii,e=new Ii,s=new Ii,i=new Ii){super(),this.isCubicBezierCurve3=!0,this.type="CubicBezierCurve3",this.v0=t,this.v1=e,this.v2=s,this.v3=i}getPoint(t,e=new Ii){const s=e,i=this.v0,r=this.v1,n=this.v2,o=this.v3;return s.set(lh(t,i.x,r.x,n.x,o.x),lh(t,i.y,r.y,n.y,o.y),lh(t,i.z,r.z,n.z,o.z)),s}copy(t){return super.copy(t),this.v0.copy(t.v0),this.v1.copy(t.v1),this.v2.copy(t.v2),this.v3.copy(t.v3),this}toJSON(){const t=super.toJSON();return t.v0=this.v0.toArray(),t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t.v3=this.v3.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v0.fromArray(t.v0),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this.v3.fromArray(t.v3),this}}class dh extends Qa{constructor(t=new Zs,e=new Zs){super(),this.isLineCurve=!0,this.type="LineCurve",this.v1=t,this.v2=e}getPoint(t,e=new Zs){const s=e;return 1===t?s.copy(this.v2):(s.copy(this.v2).sub(this.v1),s.multiplyScalar(t).add(this.v1)),s}getPointAt(t,e){return this.getPoint(t,e)}getTangent(t,e=new Zs){return e.subVectors(this.v2,this.v1).normalize()}getTangentAt(t,e){return this.getTangent(t,e)}copy(t){return super.copy(t),this.v1.copy(t.v1),this.v2.copy(t.v2),this}toJSON(){const t=super.toJSON();return t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this}}class ph extends Qa{constructor(t=new Ii,e=new Ii){super(),this.isLineCurve3=!0,this.type="LineCurve3",this.v1=t,this.v2=e}getPoint(t,e=new Ii){const s=e;return 1===t?s.copy(this.v2):(s.copy(this.v2).sub(this.v1),s.multiplyScalar(t).add(this.v1)),s}getPointAt(t,e){return this.getPoint(t,e)}getTangent(t,e=new Ii){return e.subVectors(this.v2,this.v1).normalize()}getTangentAt(t,e){return this.getTangent(t,e)}copy(t){return super.copy(t),this.v1.copy(t.v1),this.v2.copy(t.v2),this}toJSON(){const t=super.toJSON();return t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this}}class mh extends Qa{constructor(t=new Zs,e=new Zs,s=new Zs){super(),this.isQuadraticBezierCurve=!0,this.type="QuadraticBezierCurve",this.v0=t,this.v1=e,this.v2=s}getPoint(t,e=new Zs){const s=e,i=this.v0,r=this.v1,n=this.v2;return s.set(hh(t,i.x,r.x,n.x),hh(t,i.y,r.y,n.y)),s}copy(t){return super.copy(t),this.v0.copy(t.v0),this.v1.copy(t.v1),this.v2.copy(t.v2),this}toJSON(){const t=super.toJSON();return t.v0=this.v0.toArray(),t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v0.fromArray(t.v0),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this}}class yh extends Qa{constructor(t=new Ii,e=new Ii,s=new Ii){super(),this.isQuadraticBezierCurve3=!0,this.type="QuadraticBezierCurve3",this.v0=t,this.v1=e,this.v2=s}getPoint(t,e=new Ii){const s=e,i=this.v0,r=this.v1,n=this.v2;return s.set(hh(t,i.x,r.x,n.x),hh(t,i.y,r.y,n.y),hh(t,i.z,r.z,n.z)),s}copy(t){return super.copy(t),this.v0.copy(t.v0),this.v1.copy(t.v1),this.v2.copy(t.v2),this}toJSON(){const t=super.toJSON();return t.v0=this.v0.toArray(),t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v0.fromArray(t.v0),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this}}class fh extends Qa{constructor(t=[]){super(),this.isSplineCurve=!0,this.type="SplineCurve",this.points=t}getPoint(t,e=new Zs){const s=e,i=this.points,r=(i.length-1)*t,n=Math.floor(r),o=r-n,a=i[0===n?n:n-1],h=i[n],l=i[n>i.length-2?i.length-1:n+1],c=i[n>i.length-3?i.length-1:n+2];return s.set(ah(o,a.x,h.x,l.x,c.x),ah(o,a.y,h.y,l.y,c.y)),s}copy(t){super.copy(t),this.points=[];for(let e=0,s=t.points.length;e<s;e++){const s=t.points[e];this.points.push(s.clone())}return this}toJSON(){const t=super.toJSON();t.points=[];for(let e=0,s=this.points.length;e<s;e++){const s=this.points[e];t.points.push(s.toArray())}return t}fromJSON(t){super.fromJSON(t),this.points=[];for(let e=0,s=t.points.length;e<s;e++){const s=t.points[e];this.points.push((new Zs).fromArray(s))}return this}}var gh=Object.freeze({__proto__:null,ArcCurve:th,CatmullRomCurve3:oh,CubicBezierCurve:ch,CubicBezierCurve3:uh,EllipseCurve:Ka,LineCurve:dh,LineCurve3:ph,QuadraticBezierCurve:mh,QuadraticBezierCurve3:yh,SplineCurve:fh});class xh extends Qa{constructor(){super(),this.type="CurvePath",this.curves=[],this.autoClose=!1}add(t){this.curves.push(t)}closePath(){const t=this.curves[0].getPoint(0),e=this.curves[this.curves.length-1].getPoint(1);if(!t.equals(e)){const s=!0===t.isVector2?"LineCurve":"LineCurve3";this.curves.push(new gh[s](e,t))}return this}getPoint(t,e){const s=t*this.getLength(),i=this.getCurveLengths();let r=0;for(;r<i.length;){if(i[r]>=s){const t=i[r]-s,n=this.curves[r],o=n.getLength(),a=0===o?0:1-t/o;return n.getPointAt(a,e)}r++}return null}getLength(){const t=this.getCurveLengths();return t[t.length-1]}updateArcLengths(){this.needsUpdate=!0,this.cacheLengths=null,this.getCurveLengths()}getCurveLengths(){if(this.cacheLengths&&this.cacheLengths.length===this.curves.length)return this.cacheLengths;const t=[];let e=0;for(let s=0,i=this.curves.length;s<i;s++)e+=this.curves[s].getLength(),t.push(e);return this.cacheLengths=t,t}getSpacedPoints(t=40){const e=[];for(let s=0;s<=t;s++)e.push(this.getPoint(s/t));return this.autoClose&&e.push(e[0]),e}getPoints(t=12){const e=[];let s;for(let i=0,r=this.curves;i<r.length;i++){const n=r[i],o=n.isEllipseCurve?2*t:n.isLineCurve||n.isLineCurve3?1:n.isSplineCurve?t*n.points.length:t,a=n.getPoints(o);for(let t=0;t<a.length;t++){const i=a[t];s&&s.equals(i)||(e.push(i),s=i)}}return this.autoClose&&e.length>1&&!e[e.length-1].equals(e[0])&&e.push(e[0]),e}copy(t){super.copy(t),this.curves=[];for(let e=0,s=t.curves.length;e<s;e++){const s=t.curves[e];this.curves.push(s.clone())}return this.autoClose=t.autoClose,this}toJSON(){const t=super.toJSON();t.autoClose=this.autoClose,t.curves=[];for(let e=0,s=this.curves.length;e<s;e++){const s=this.curves[e];t.curves.push(s.toJSON())}return t}fromJSON(t){super.fromJSON(t),this.autoClose=t.autoClose,this.curves=[];for(let e=0,s=t.curves.length;e<s;e++){const s=t.curves[e];this.curves.push((new gh[s.type]).fromJSON(s))}return this}}class bh extends xh{constructor(t){super(),this.type="Path",this.currentPoint=new Zs,t&&this.setFromPoints(t)}setFromPoints(t){this.moveTo(t[0].x,t[0].y);for(let e=1,s=t.length;e<s;e++)this.lineTo(t[e].x,t[e].y);return this}moveTo(t,e){return this.currentPoint.set(t,e),this}lineTo(t,e){const s=new dh(this.currentPoint.clone(),new Zs(t,e));return this.curves.push(s),this.currentPoint.set(t,e),this}quadraticCurveTo(t,e,s,i){const r=new mh(this.currentPoint.clone(),new Zs(t,e),new Zs(s,i));return this.curves.push(r),this.currentPoint.set(s,i),this}bezierCurveTo(t,e,s,i,r,n){const o=new ch(this.currentPoint.clone(),new Zs(t,e),new Zs(s,i),new Zs(r,n));return this.curves.push(o),this.currentPoint.set(r,n),this}splineThru(t){const e=[this.currentPoint.clone()].concat(t),s=new fh(e);return this.curves.push(s),this.currentPoint.copy(t[t.length-1]),this}arc(t,e,s,i,r,n){const o=this.currentPoint.x,a=this.currentPoint.y;return this.absarc(t+o,e+a,s,i,r,n),this}absarc(t,e,s,i,r,n){return this.absellipse(t,e,s,s,i,r,n),this}ellipse(t,e,s,i,r,n,o,a){const h=this.currentPoint.x,l=this.currentPoint.y;return this.absellipse(t+h,e+l,s,i,r,n,o,a),this}absellipse(t,e,s,i,r,n,o,a){const h=new Ka(t,e,s,i,r,n,o,a);if(this.curves.length>0){const t=h.getPoint(0);t.equals(this.currentPoint)||this.lineTo(t.x,t.y)}this.curves.push(h);const l=h.getPoint(1);return this.currentPoint.copy(l),this}copy(t){return super.copy(t),this.currentPoint.copy(t.currentPoint),this}toJSON(){const t=super.toJSON();return t.currentPoint=this.currentPoint.toArray(),t}fromJSON(t){return super.fromJSON(t),this.currentPoint.fromArray(t.currentPoint),this}}class vh extends zn{constructor(t=[new Zs(0,-.5),new Zs(.5,0),new Zs(0,.5)],e=12,s=0,i=2*Math.PI){super(),this.type="LatheGeometry",this.parameters={points:t,segments:e,phiStart:s,phiLength:i},e=Math.floor(e),i=Ds(i,0,2*Math.PI);const r=[],n=[],o=[],a=[],h=[],l=1/e,c=new Ii,u=new Zs,d=new Ii,p=new Ii,m=new Ii;let y=0,f=0;for(let e=0;e<=t.length-1;e++)switch(e){case 0:y=t[e+1].x-t[e].x,f=t[e+1].y-t[e].y,d.x=1*f,d.y=-y,d.z=0*f,m.copy(d),d.normalize(),a.push(d.x,d.y,d.z);break;case t.length-1:a.push(m.x,m.y,m.z);break;default:y=t[e+1].x-t[e].x,f=t[e+1].y-t[e].y,d.x=1*f,d.y=-y,d.z=0*f,p.copy(d),d.x+=m.x,d.y+=m.y,d.z+=m.z,d.normalize(),a.push(d.x,d.y,d.z),m.copy(p)}for(let r=0;r<=e;r++){const d=s+r*l*i,p=Math.sin(d),m=Math.cos(d);for(let s=0;s<=t.length-1;s++){c.x=t[s].x*p,c.y=t[s].y,c.z=t[s].x*m,n.push(c.x,c.y,c.z),u.x=r/e,u.y=s/(t.length-1),o.push(u.x,u.y);const i=a[3*s+0]*p,l=a[3*s+1],d=a[3*s+0]*m;h.push(i,l,d)}}for(let s=0;s<e;s++)for(let e=0;e<t.length-1;e++){const i=e+s*t.length,n=i,o=i+t.length,a=i+t.length+1,h=i+1;r.push(n,o,h),r.push(a,h,o)}this.setIndex(r),this.setAttribute("position",new bn(n,3)),this.setAttribute("uv",new bn(o,2)),this.setAttribute("normal",new bn(h,3))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new vh(t.points,t.segments,t.phiStart,t.phiLength)}}class wh extends vh{constructor(t=1,e=1,s=4,i=8){const r=new bh;r.absarc(0,-e/2,t,1.5*Math.PI,0),r.absarc(0,e/2,t,0,.5*Math.PI),super(r.getPoints(s),i),this.type="CapsuleGeometry",this.parameters={radius:t,length:e,capSegments:s,radialSegments:i}}static fromJSON(t){return new wh(t.radius,t.length,t.capSegments,t.radialSegments)}}class Mh extends zn{constructor(t=1,e=32,s=0,i=2*Math.PI){super(),this.type="CircleGeometry",this.parameters={radius:t,segments:e,thetaStart:s,thetaLength:i},e=Math.max(3,e);const r=[],n=[],o=[],a=[],h=new Ii,l=new Zs;n.push(0,0,0),o.push(0,0,1),a.push(.5,.5);for(let r=0,c=3;r<=e;r++,c+=3){const u=s+r/e*i;h.x=t*Math.cos(u),h.y=t*Math.sin(u),n.push(h.x,h.y,h.z),o.push(0,0,1),l.x=(n[c]/t+1)/2,l.y=(n[c+1]/t+1)/2,a.push(l.x,l.y)}for(let t=1;t<=e;t++)r.push(t,t+1,0);this.setIndex(r),this.setAttribute("position",new bn(n,3)),this.setAttribute("normal",new bn(o,3)),this.setAttribute("uv",new bn(a,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new Mh(t.radius,t.segments,t.thetaStart,t.thetaLength)}}class Sh extends zn{constructor(t=1,e=1,s=1,i=32,r=1,n=!1,o=0,a=2*Math.PI){super(),this.type="CylinderGeometry",this.parameters={radiusTop:t,radiusBottom:e,height:s,radialSegments:i,heightSegments:r,openEnded:n,thetaStart:o,thetaLength:a};const h=this;i=Math.floor(i),r=Math.floor(r);const l=[],c=[],u=[],d=[];let p=0;const m=[],y=s/2;let f=0;function g(s){const r=p,n=new Zs,m=new Ii;let g=0;const x=!0===s?t:e,b=!0===s?1:-1;for(let t=1;t<=i;t++)c.push(0,y*b,0),u.push(0,b,0),d.push(.5,.5),p++;const v=p;for(let t=0;t<=i;t++){const e=t/i*a+o,s=Math.cos(e),r=Math.sin(e);m.x=x*r,m.y=y*b,m.z=x*s,c.push(m.x,m.y,m.z),u.push(0,b,0),n.x=.5*s+.5,n.y=.5*r*b+.5,d.push(n.x,n.y),p++}for(let t=0;t<i;t++){const e=r+t,i=v+t;!0===s?l.push(i,i+1,e):l.push(i+1,i,e),g+=3}h.addGroup(f,g,!0===s?1:2),f+=g}!function(){const n=new Ii,g=new Ii;let x=0;const b=(e-t)/s;for(let h=0;h<=r;h++){const l=[],f=h/r,x=f*(e-t)+t;for(let t=0;t<=i;t++){const e=t/i,r=e*a+o,h=Math.sin(r),m=Math.cos(r);g.x=x*h,g.y=-f*s+y,g.z=x*m,c.push(g.x,g.y,g.z),n.set(h,b,m).normalize(),u.push(n.x,n.y,n.z),d.push(e,1-f),l.push(p++)}m.push(l)}for(let s=0;s<i;s++)for(let i=0;i<r;i++){const n=m[i][s],o=m[i+1][s],a=m[i+1][s+1],h=m[i][s+1];(t>0||0!==i)&&(l.push(n,o,h),x+=3),(e>0||i!==r-1)&&(l.push(o,a,h),x+=3)}h.addGroup(f,x,0),f+=x}(),!1===n&&(t>0&&g(!0),e>0&&g(!1)),this.setIndex(l),this.setAttribute("position",new bn(c,3)),this.setAttribute("normal",new bn(u,3)),this.setAttribute("uv",new bn(d,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new Sh(t.radiusTop,t.radiusBottom,t.height,t.radialSegments,t.heightSegments,t.openEnded,t.thetaStart,t.thetaLength)}}class _h extends Sh{constructor(t=1,e=1,s=32,i=1,r=!1,n=0,o=2*Math.PI){super(0,t,e,s,i,r,n,o),this.type="ConeGeometry",this.parameters={radius:t,height:e,radialSegments:s,heightSegments:i,openEnded:r,thetaStart:n,thetaLength:o}}static fromJSON(t){return new _h(t.radius,t.height,t.radialSegments,t.heightSegments,t.openEnded,t.thetaStart,t.thetaLength)}}class Ah extends zn{constructor(t=[],e=[],s=1,i=0){super(),this.type="PolyhedronGeometry",this.parameters={vertices:t,indices:e,radius:s,detail:i};const r=[],n=[];function o(t,e,s,i){const r=i+1,n=[];for(let i=0;i<=r;i++){n[i]=[];const o=t.clone().lerp(s,i/r),a=e.clone().lerp(s,i/r),h=r-i;for(let t=0;t<=h;t++)n[i][t]=0===t&&i===r?o:o.clone().lerp(a,t/h)}for(let t=0;t<r;t++)for(let e=0;e<2*(r-t)-1;e++){const s=Math.floor(e/2);e%2==0?(a(n[t][s+1]),a(n[t+1][s]),a(n[t][s])):(a(n[t][s+1]),a(n[t+1][s+1]),a(n[t+1][s]))}}function a(t){r.push(t.x,t.y,t.z)}function h(e,s){const i=3*e;s.x=t[i+0],s.y=t[i+1],s.z=t[i+2]}function l(t,e,s,i){i<0&&1===t.x&&(n[e]=t.x-1),0===s.x&&0===s.z&&(n[e]=i/2/Math.PI+.5)}function c(t){return Math.atan2(t.z,-t.x)}!function(t){const s=new Ii,i=new Ii,r=new Ii;for(let n=0;n<e.length;n+=3)h(e[n+0],s),h(e[n+1],i),h(e[n+2],r),o(s,i,r,t)}(i),function(t){const e=new Ii;for(let s=0;s<r.length;s+=3)e.x=r[s+0],e.y=r[s+1],e.z=r[s+2],e.normalize().multiplyScalar(t),r[s+0]=e.x,r[s+1]=e.y,r[s+2]=e.z}(s),function(){const t=new Ii;for(let s=0;s<r.length;s+=3){t.x=r[s+0],t.y=r[s+1],t.z=r[s+2];const i=c(t)/2/Math.PI+.5,o=(e=t,Math.atan2(-e.y,Math.sqrt(e.x*e.x+e.z*e.z))/Math.PI+.5);n.push(i,1-o)}var e;(function(){const t=new Ii,e=new Ii,s=new Ii,i=new Ii,o=new Zs,a=new Zs,h=new Zs;for(let u=0,d=0;u<r.length;u+=9,d+=6){t.set(r[u+0],r[u+1],r[u+2]),e.set(r[u+3],r[u+4],r[u+5]),s.set(r[u+6],r[u+7],r[u+8]),o.set(n[d+0],n[d+1]),a.set(n[d+2],n[d+3]),h.set(n[d+4],n[d+5]),i.copy(t).add(e).add(s).divideScalar(3);const p=c(i);l(o,d+0,t,p),l(a,d+2,e,p),l(h,d+4,s,p)}})(),function(){for(let t=0;t<n.length;t+=6){const e=n[t+0],s=n[t+2],i=n[t+4],r=Math.max(e,s,i),o=Math.min(e,s,i);r>.9&&o<.1&&(e<.2&&(n[t+0]+=1),s<.2&&(n[t+2]+=1),i<.2&&(n[t+4]+=1))}}()}(),this.setAttribute("position",new bn(r,3)),this.setAttribute("normal",new bn(r.slice(),3)),this.setAttribute("uv",new bn(n,2)),0===i?this.computeVertexNormals():this.normalizeNormals()}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new Ah(t.vertices,t.indices,t.radius,t.details)}}class Th extends Ah{constructor(t=1,e=0){const s=(1+Math.sqrt(5))/2,i=1/s;super([-1,-1,-1,-1,-1,1,-1,1,-1,-1,1,1,1,-1,-1,1,-1,1,1,1,-1,1,1,1,0,-i,-s,0,-i,s,0,i,-s,0,i,s,-i,-s,0,-i,s,0,i,-s,0,i,s,0,-s,0,-i,s,0,-i,-s,0,i,s,0,i],[3,11,7,3,7,15,3,15,13,7,19,17,7,17,6,7,6,15,17,4,8,17,8,10,17,10,6,8,0,16,8,16,2,8,2,10,0,12,1,0,1,18,0,18,16,6,10,2,6,2,13,6,13,15,2,16,18,2,18,3,2,3,13,18,1,9,18,9,11,18,11,3,4,14,12,4,12,0,4,0,8,11,9,5,11,5,19,11,19,7,19,5,14,19,14,4,19,4,17,1,12,14,1,14,5,1,5,9],t,e),this.type="DodecahedronGeometry",this.parameters={radius:t,detail:e}}static fromJSON(t){return new Th(t.radius,t.detail)}}const zh=new Ii,Ch=new Ii,Ih=new Ii,Bh=new Jr;class kh extends zn{constructor(t=null,e=1){if(super(),this.type="EdgesGeometry",this.parameters={geometry:t,thresholdAngle:e},null!==t){const s=4,i=Math.pow(10,s),r=Math.cos(Ws*e),n=t.getIndex(),o=t.getAttribute("position"),a=n?n.count:o.count,h=[0,0,0],l=["a","b","c"],c=new Array(3),u={},d=[];for(let t=0;t<a;t+=3){n?(h[0]=n.getX(t),h[1]=n.getX(t+1),h[2]=n.getX(t+2)):(h[0]=t,h[1]=t+1,h[2]=t+2);const{a:e,b:s,c:a}=Bh;if(e.fromBufferAttribute(o,h[0]),s.fromBufferAttribute(o,h[1]),a.fromBufferAttribute(o,h[2]),Bh.getNormal(Ih),c[0]=`${Math.round(e.x*i)},${Math.round(e.y*i)},${Math.round(e.z*i)}`,c[1]=`${Math.round(s.x*i)},${Math.round(s.y*i)},${Math.round(s.z*i)}`,c[2]=`${Math.round(a.x*i)},${Math.round(a.y*i)},${Math.round(a.z*i)}`,c[0]!==c[1]&&c[1]!==c[2]&&c[2]!==c[0])for(let t=0;t<3;t++){const e=(t+1)%3,s=c[t],i=c[e],n=Bh[l[t]],o=Bh[l[e]],a=`${s}_${i}`,p=`${i}_${s}`;p in u&&u[p]?(Ih.dot(u[p].normal)<=r&&(d.push(n.x,n.y,n.z),d.push(o.x,o.y,o.z)),u[p]=null):a in u||(u[a]={index0:h[t],index1:h[e],normal:Ih.clone()})}}for(const t in u)if(u[t]){const{index0:e,index1:s}=u[t];zh.fromBufferAttribute(o,e),Ch.fromBufferAttribute(o,s),d.push(zh.x,zh.y,zh.z),d.push(Ch.x,Ch.y,Ch.z)}this.setAttribute("position",new bn(d,3))}}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}}class Rh extends bh{constructor(t){super(t),this.uuid=Us(),this.type="Shape",this.holes=[]}getPointsHoles(t){const e=[];for(let s=0,i=this.holes.length;s<i;s++)e[s]=this.holes[s].getPoints(t);return e}extractPoints(t){return{shape:this.getPoints(t),holes:this.getPointsHoles(t)}}copy(t){super.copy(t),this.holes=[];for(let e=0,s=t.holes.length;e<s;e++){const s=t.holes[e];this.holes.push(s.clone())}return this}toJSON(){const t=super.toJSON();t.uuid=this.uuid,t.holes=[];for(let e=0,s=this.holes.length;e<s;e++){const s=this.holes[e];t.holes.push(s.toJSON())}return t}fromJSON(t){super.fromJSON(t),this.uuid=t.uuid,this.holes=[];for(let e=0,s=t.holes.length;e<s;e++){const s=t.holes[e];this.holes.push((new bh).fromJSON(s))}return this}}const Eh=function(t,e,s=2){const i=e&&e.length,r=i?e[0]*s:t.length;let n=Ph(t,0,r,s,!0);const o=[];if(!n||n.next===n.prev)return o;let a,h,l,c,u,d,p;if(i&&(n=function(t,e,s,i){const r=[];let n,o,a,h,l;for(n=0,o=e.length;n<o;n++)a=e[n]*i,h=n<o-1?e[n+1]*i:t.length,l=Ph(t,a,h,i,!1),l===l.next&&(l.steiner=!0),r.push(qh(l));for(r.sort(jh),n=0;n<r.length;n++)s=Uh(r[n],s);return s}(t,e,n,s)),t.length>80*s){a=l=t[0],h=c=t[1];for(let e=s;e<r;e+=s)u=t[e],d=t[e+1],u<a&&(a=u),d<h&&(h=d),u>l&&(l=u),d>c&&(c=d);p=Math.max(l-a,c-h),p=0!==p?32767/p:0}return Fh(n,o,s,a,h,p,0),o};function Ph(t,e,s,i,r){let n,o;if(r===function(t,e,s,i){let r=0;for(let n=e,o=s-i;n<s;n+=i)r+=(t[o]-t[n])*(t[n+1]+t[o+1]),o=n;return r}(t,e,s,i)>0)for(n=e;n<s;n+=i)o=el(n,t[n],t[n+1],o);else for(n=s-i;n>=e;n-=i)o=el(n,t[n],t[n+1],o);return o&&Zh(o,o.next)&&(sl(o),o=o.next),o}function Oh(t,e){if(!t)return t;e||(e=t);let s,i=t;do{if(s=!1,i.steiner||!Zh(i,i.next)&&0!==Yh(i.prev,i,i.next))i=i.next;else{if(sl(i),i=e=i.prev,i===i.next)break;s=!0}}while(s||i!==e);return e}function Fh(t,e,s,i,r,n,o){if(!t)return;!o&&n&&function(t,e,s,i){let r=t;do{0===r.z&&(r.z=Hh(r.x,r.y,e,s,i)),r.prevZ=r.prev,r.nextZ=r.next,r=r.next}while(r!==t);r.prevZ.nextZ=null,r.prevZ=null,function(t){let e,s,i,r,n,o,a,h,l=1;do{for(s=t,t=null,n=null,o=0;s;){for(o++,i=s,a=0,e=0;e<l&&(a++,i=i.nextZ,i);e++);for(h=l;a>0||h>0&&i;)0!==a&&(0===h||!i||s.z<=i.z)?(r=s,s=s.nextZ,a--):(r=i,i=i.nextZ,h--),n?n.nextZ=r:t=r,r.prevZ=n,n=r;s=i}n.nextZ=null,l*=2}while(o>1)}(r)}(t,i,r,n);let a,h,l=t;for(;t.prev!==t.next;)if(a=t.prev,h=t.next,n?Lh(t,i,r,n):Nh(t))e.push(a.i/s|0),e.push(t.i/s|0),e.push(h.i/s|0),sl(t),t=h.next,l=h.next;else if((t=h)===l){o?1===o?Fh(t=Vh(Oh(t),e,s),e,s,i,r,n,2):2===o&&Wh(t,e,s,i,r,n):Fh(Oh(t),e,s,i,r,n,1);break}}function Nh(t){const e=t.prev,s=t,i=t.next;if(Yh(e,s,i)>=0)return!1;const r=e.x,n=s.x,o=i.x,a=e.y,h=s.y,l=i.y,c=r<n?r<o?r:o:n<o?n:o,u=a<h?a<l?a:l:h<l?h:l,d=r>n?r>o?r:o:n>o?n:o,p=a>h?a>l?a:l:h>l?h:l;let m=i.next;for(;m!==e;){if(m.x>=c&&m.x<=d&&m.y>=u&&m.y<=p&&Jh(r,a,n,h,o,l,m.x,m.y)&&Yh(m.prev,m,m.next)>=0)return!1;m=m.next}return!0}function Lh(t,e,s,i){const r=t.prev,n=t,o=t.next;if(Yh(r,n,o)>=0)return!1;const a=r.x,h=n.x,l=o.x,c=r.y,u=n.y,d=o.y,p=a<h?a<l?a:l:h<l?h:l,m=c<u?c<d?c:d:u<d?u:d,y=a>h?a>l?a:l:h>l?h:l,f=c>u?c>d?c:d:u>d?u:d,g=Hh(p,m,e,s,i),x=Hh(y,f,e,s,i);let b=t.prevZ,v=t.nextZ;for(;b&&b.z>=g&&v&&v.z<=x;){if(b.x>=p&&b.x<=y&&b.y>=m&&b.y<=f&&b!==r&&b!==o&&Jh(a,c,h,u,l,d,b.x,b.y)&&Yh(b.prev,b,b.next)>=0)return!1;if(b=b.prevZ,v.x>=p&&v.x<=y&&v.y>=m&&v.y<=f&&v!==r&&v!==o&&Jh(a,c,h,u,l,d,v.x,v.y)&&Yh(v.prev,v,v.next)>=0)return!1;v=v.nextZ}for(;b&&b.z>=g;){if(b.x>=p&&b.x<=y&&b.y>=m&&b.y<=f&&b!==r&&b!==o&&Jh(a,c,h,u,l,d,b.x,b.y)&&Yh(b.prev,b,b.next)>=0)return!1;b=b.prevZ}for(;v&&v.z<=x;){if(v.x>=p&&v.x<=y&&v.y>=m&&v.y<=f&&v!==r&&v!==o&&Jh(a,c,h,u,l,d,v.x,v.y)&&Yh(v.prev,v,v.next)>=0)return!1;v=v.nextZ}return!0}function Vh(t,e,s){let i=t;do{const r=i.prev,n=i.next.next;!Zh(r,n)&&Gh(r,i,i.next,n)&&Kh(r,n)&&Kh(n,r)&&(e.push(r.i/s|0),e.push(i.i/s|0),e.push(n.i/s|0),sl(i),sl(i.next),i=t=n),i=i.next}while(i!==t);return Oh(i)}function Wh(t,e,s,i,r,n){let o=t;do{let t=o.next.next;for(;t!==o.prev;){if(o.i!==t.i&&Xh(o,t)){let a=tl(o,t);return o=Oh(o,o.next),a=Oh(a,a.next),Fh(o,e,s,i,r,n,0),void Fh(a,e,s,i,r,n,0)}t=t.next}o=o.next}while(o!==t)}function jh(t,e){return t.x-e.x}function Uh(t,e){const s=function(t,e){let s,i=e,r=-1/0;const n=t.x,o=t.y;do{if(o<=i.y&&o>=i.next.y&&i.next.y!==i.y){const t=i.x+(o-i.y)*(i.next.x-i.x)/(i.next.y-i.y);if(t<=n&&t>r&&(r=t,s=i.x<i.next.x?i:i.next,t===n))return s}i=i.next}while(i!==e);if(!s)return null;const a=s,h=s.x,l=s.y;let c,u=1/0;i=s;do{n>=i.x&&i.x>=h&&n!==i.x&&Jh(o<l?n:r,o,h,l,o<l?r:n,o,i.x,i.y)&&(c=Math.abs(o-i.y)/(n-i.x),Kh(i,t)&&(c<u||c===u&&(i.x>s.x||i.x===s.x&&Dh(s,i)))&&(s=i,u=c)),i=i.next}while(i!==a);return s}(t,e);if(!s)return e;const i=tl(s,t);return Oh(i,i.next),Oh(s,s.next)}function Dh(t,e){return Yh(t.prev,t,e.prev)<0&&Yh(e.next,t,t.next)<0}function Hh(t,e,s,i,r){return(t=1431655765&((t=858993459&((t=252645135&((t=16711935&((t=(t-s)*r|0)|t<<8))|t<<4))|t<<2))|t<<1))|(e=1431655765&((e=858993459&((e=252645135&((e=16711935&((e=(e-i)*r|0)|e<<8))|e<<4))|e<<2))|e<<1))<<1}function qh(t){let e=t,s=t;do{(e.x<s.x||e.x===s.x&&e.y<s.y)&&(s=e),e=e.next}while(e!==t);return s}function Jh(t,e,s,i,r,n,o,a){return(r-o)*(e-a)>=(t-o)*(n-a)&&(t-o)*(i-a)>=(s-o)*(e-a)&&(s-o)*(n-a)>=(r-o)*(i-a)}function Xh(t,e){return t.next.i!==e.i&&t.prev.i!==e.i&&!function(t,e){let s=t;do{if(s.i!==t.i&&s.next.i!==t.i&&s.i!==e.i&&s.next.i!==e.i&&Gh(s,s.next,t,e))return!0;s=s.next}while(s!==t);return!1}(t,e)&&(Kh(t,e)&&Kh(e,t)&&function(t,e){let s=t,i=!1;const r=(t.x+e.x)/2,n=(t.y+e.y)/2;do{s.y>n!=s.next.y>n&&s.next.y!==s.y&&r<(s.next.x-s.x)*(n-s.y)/(s.next.y-s.y)+s.x&&(i=!i),s=s.next}while(s!==t);return i}(t,e)&&(Yh(t.prev,t,e.prev)||Yh(t,e.prev,e))||Zh(t,e)&&Yh(t.prev,t,t.next)>0&&Yh(e.prev,e,e.next)>0)}function Yh(t,e,s){return(e.y-t.y)*(s.x-e.x)-(e.x-t.x)*(s.y-e.y)}function Zh(t,e){return t.x===e.x&&t.y===e.y}function Gh(t,e,s,i){const r=Qh(Yh(t,e,s)),n=Qh(Yh(t,e,i)),o=Qh(Yh(s,i,t)),a=Qh(Yh(s,i,e));return r!==n&&o!==a||(!(0!==r||!$h(t,s,e))||(!(0!==n||!$h(t,i,e))||(!(0!==o||!$h(s,t,i))||!(0!==a||!$h(s,e,i)))))}function $h(t,e,s){return e.x<=Math.max(t.x,s.x)&&e.x>=Math.min(t.x,s.x)&&e.y<=Math.max(t.y,s.y)&&e.y>=Math.min(t.y,s.y)}function Qh(t){return t>0?1:t<0?-1:0}function Kh(t,e){return Yh(t.prev,t,t.next)<0?Yh(t,e,t.next)>=0&&Yh(t,t.prev,e)>=0:Yh(t,e,t.prev)<0||Yh(t,t.next,e)<0}function tl(t,e){const s=new il(t.i,t.x,t.y),i=new il(e.i,e.x,e.y),r=t.next,n=e.prev;return t.next=e,e.prev=t,s.next=r,r.prev=s,i.next=s,s.prev=i,n.next=i,i.prev=n,i}function el(t,e,s,i){const r=new il(t,e,s);return i?(r.next=i.next,r.prev=i,i.next.prev=r,i.next=r):(r.prev=r,r.next=r),r}function sl(t){t.next.prev=t.prev,t.prev.next=t.next,t.prevZ&&(t.prevZ.nextZ=t.nextZ),t.nextZ&&(t.nextZ.prevZ=t.prevZ)}function il(t,e,s){this.i=t,this.x=e,this.y=s,this.prev=null,this.next=null,this.z=0,this.prevZ=null,this.nextZ=null,this.steiner=!1}class rl{static area(t){const e=t.length;let s=0;for(let i=e-1,r=0;r<e;i=r++)s+=t[i].x*t[r].y-t[r].x*t[i].y;return.5*s}static isClockWise(t){return rl.area(t)<0}static triangulateShape(t,e){const s=[],i=[],r=[];nl(t),ol(s,t);let n=t.length;e.forEach(nl);for(let t=0;t<e.length;t++)i.push(n),n+=e[t].length,ol(s,e[t]);const o=Eh(s,i);for(let t=0;t<o.length;t+=3)r.push(o.slice(t,t+3));return r}}function nl(t){const e=t.length;e>2&&t[e-1].equals(t[0])&&t.pop()}function ol(t,e){for(let s=0;s<e.length;s++)t.push(e[s].x),t.push(e[s].y)}class al extends zn{constructor(t=new Rh([new Zs(.5,.5),new Zs(-.5,.5),new Zs(-.5,-.5),new Zs(.5,-.5)]),e={}){super(),this.type="ExtrudeGeometry",this.parameters={shapes:t,options:e},t=Array.isArray(t)?t:[t];const s=this,i=[],r=[];for(let e=0,s=t.length;e<s;e++){n(t[e])}function n(t){const n=[],o=void 0!==e.curveSegments?e.curveSegments:12,a=void 0!==e.steps?e.steps:1,h=void 0!==e.depth?e.depth:1;let l=void 0===e.bevelEnabled||e.bevelEnabled,c=void 0!==e.bevelThickness?e.bevelThickness:.2,u=void 0!==e.bevelSize?e.bevelSize:c-.1,d=void 0!==e.bevelOffset?e.bevelOffset:0,p=void 0!==e.bevelSegments?e.bevelSegments:3;const m=e.extrudePath,y=void 0!==e.UVGenerator?e.UVGenerator:hl;let f,g,x,b,v,w=!1;m&&(f=m.getSpacedPoints(a),w=!0,l=!1,g=m.computeFrenetFrames(a,!1),x=new Ii,b=new Ii,v=new Ii),l||(p=0,c=0,u=0,d=0);const M=t.extractPoints(o);let S=M.shape;const _=M.holes;if(!rl.isClockWise(S)){S=S.reverse();for(let t=0,e=_.length;t<e;t++){const e=_[t];rl.isClockWise(e)&&(_[t]=e.reverse())}}const A=rl.triangulateShape(S,_),T=S;for(let t=0,e=_.length;t<e;t++){const e=_[t];S=S.concat(e)}function z(t,e,s){return e||console.error("THREE.ExtrudeGeometry: vec does not exist"),t.clone().addScaledVector(e,s)}const C=S.length,I=A.length;function B(t,e,s){let i,r,n;const o=t.x-e.x,a=t.y-e.y,h=s.x-t.x,l=s.y-t.y,c=o*o+a*a,u=o*l-a*h;if(Math.abs(u)>Number.EPSILON){const u=Math.sqrt(c),d=Math.sqrt(h*h+l*l),p=e.x-a/u,m=e.y+o/u,y=((s.x-l/d-p)*l-(s.y+h/d-m)*h)/(o*l-a*h);i=p+o*y-t.x,r=m+a*y-t.y;const f=i*i+r*r;if(f<=2)return new Zs(i,r);n=Math.sqrt(f/2)}else{let t=!1;o>Number.EPSILON?h>Number.EPSILON&&(t=!0):o<-Number.EPSILON?h<-Number.EPSILON&&(t=!0):Math.sign(a)===Math.sign(l)&&(t=!0),t?(i=-a,r=o,n=Math.sqrt(c)):(i=o,r=a,n=Math.sqrt(c/2))}return new Zs(i/n,r/n)}const k=[];for(let t=0,e=T.length,s=e-1,i=t+1;t<e;t++,s++,i++)s===e&&(s=0),i===e&&(i=0),k[t]=B(T[t],T[s],T[i]);const R=[];let E,P=k.concat();for(let t=0,e=_.length;t<e;t++){const e=_[t];E=[];for(let t=0,s=e.length,i=s-1,r=t+1;t<s;t++,i++,r++)i===s&&(i=0),r===s&&(r=0),E[t]=B(e[t],e[i],e[r]);R.push(E),P=P.concat(E)}for(let t=0;t<p;t++){const e=t/p,s=c*Math.cos(e*Math.PI/2),i=u*Math.sin(e*Math.PI/2)+d;for(let t=0,e=T.length;t<e;t++){const e=z(T[t],k[t],i);N(e.x,e.y,-s)}for(let t=0,e=_.length;t<e;t++){const e=_[t];E=R[t];for(let t=0,r=e.length;t<r;t++){const r=z(e[t],E[t],i);N(r.x,r.y,-s)}}}const O=u+d;for(let t=0;t<C;t++){const e=l?z(S[t],P[t],O):S[t];w?(b.copy(g.normals[0]).multiplyScalar(e.x),x.copy(g.binormals[0]).multiplyScalar(e.y),v.copy(f[0]).add(b).add(x),N(v.x,v.y,v.z)):N(e.x,e.y,0)}for(let t=1;t<=a;t++)for(let e=0;e<C;e++){const s=l?z(S[e],P[e],O):S[e];w?(b.copy(g.normals[t]).multiplyScalar(s.x),x.copy(g.binormals[t]).multiplyScalar(s.y),v.copy(f[t]).add(b).add(x),N(v.x,v.y,v.z)):N(s.x,s.y,h/a*t)}for(let t=p-1;t>=0;t--){const e=t/p,s=c*Math.cos(e*Math.PI/2),i=u*Math.sin(e*Math.PI/2)+d;for(let t=0,e=T.length;t<e;t++){const e=z(T[t],k[t],i);N(e.x,e.y,h+s)}for(let t=0,e=_.length;t<e;t++){const e=_[t];E=R[t];for(let t=0,r=e.length;t<r;t++){const r=z(e[t],E[t],i);w?N(r.x,r.y+f[a-1].y,f[a-1].x+s):N(r.x,r.y,h+s)}}}function F(t,e){let s=t.length;for(;--s>=0;){const i=s;let r=s-1;r<0&&(r=t.length-1);for(let t=0,s=a+2*p;t<s;t++){const s=C*t,n=C*(t+1);V(e+i+s,e+r+s,e+r+n,e+i+n)}}}function N(t,e,s){n.push(t),n.push(e),n.push(s)}function L(t,e,r){W(t),W(e),W(r);const n=i.length/3,o=y.generateTopUV(s,i,n-3,n-2,n-1);j(o[0]),j(o[1]),j(o[2])}function V(t,e,r,n){W(t),W(e),W(n),W(e),W(r),W(n);const o=i.length/3,a=y.generateSideWallUV(s,i,o-6,o-3,o-2,o-1);j(a[0]),j(a[1]),j(a[3]),j(a[1]),j(a[2]),j(a[3])}function W(t){i.push(n[3*t+0]),i.push(n[3*t+1]),i.push(n[3*t+2])}function j(t){r.push(t.x),r.push(t.y)}!function(){const t=i.length/3;if(l){let t=0,e=C*t;for(let t=0;t<I;t++){const s=A[t];L(s[2]+e,s[1]+e,s[0]+e)}t=a+2*p,e=C*t;for(let t=0;t<I;t++){const s=A[t];L(s[0]+e,s[1]+e,s[2]+e)}}else{for(let t=0;t<I;t++){const e=A[t];L(e[2],e[1],e[0])}for(let t=0;t<I;t++){const e=A[t];L(e[0]+C*a,e[1]+C*a,e[2]+C*a)}}s.addGroup(t,i.length/3-t,0)}(),function(){const t=i.length/3;let e=0;F(T,e),e+=T.length;for(let t=0,s=_.length;t<s;t++){const s=_[t];F(s,e),e+=s.length}s.addGroup(t,i.length/3-t,1)}()}this.setAttribute("position",new bn(i,3)),this.setAttribute("uv",new bn(r,2)),this.computeVertexNormals()}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}toJSON(){const t=super.toJSON();return function(t,e,s){if(s.shapes=[],Array.isArray(t))for(let e=0,i=t.length;e<i;e++){const i=t[e];s.shapes.push(i.uuid)}else s.shapes.push(t.uuid);s.options=Object.assign({},e),void 0!==e.extrudePath&&(s.options.extrudePath=e.extrudePath.toJSON());return s}(this.parameters.shapes,this.parameters.options,t)}static fromJSON(t,e){const s=[];for(let i=0,r=t.shapes.length;i<r;i++){const r=e[t.shapes[i]];s.push(r)}const i=t.options.extrudePath;return void 0!==i&&(t.options.extrudePath=(new gh[i.type]).fromJSON(i)),new al(s,t.options)}}const hl={generateTopUV:function(t,e,s,i,r){const n=e[3*s],o=e[3*s+1],a=e[3*i],h=e[3*i+1],l=e[3*r],c=e[3*r+1];return[new Zs(n,o),new Zs(a,h),new Zs(l,c)]},generateSideWallUV:function(t,e,s,i,r,n){const o=e[3*s],a=e[3*s+1],h=e[3*s+2],l=e[3*i],c=e[3*i+1],u=e[3*i+2],d=e[3*r],p=e[3*r+1],m=e[3*r+2],y=e[3*n],f=e[3*n+1],g=e[3*n+2];return Math.abs(a-c)<Math.abs(o-l)?[new Zs(o,1-h),new Zs(l,1-u),new Zs(d,1-m),new Zs(y,1-g)]:[new Zs(a,1-h),new Zs(c,1-u),new Zs(p,1-m),new Zs(f,1-g)]}};class ll extends Ah{constructor(t=1,e=0){const s=(1+Math.sqrt(5))/2;super([-1,s,0,1,s,0,-1,-s,0,1,-s,0,0,-1,s,0,1,s,0,-1,-s,0,1,-s,s,0,-1,s,0,1,-s,0,-1,-s,0,1],[0,11,5,0,5,1,0,1,7,0,7,10,0,10,11,1,5,9,5,11,4,11,10,2,10,7,6,7,1,8,3,9,4,3,4,2,3,2,6,3,6,8,3,8,9,4,9,5,2,4,11,6,2,10,8,6,7,9,8,1],t,e),this.type="IcosahedronGeometry",this.parameters={radius:t,detail:e}}static fromJSON(t){return new ll(t.radius,t.detail)}}class cl extends Ah{constructor(t=1,e=0){super([1,0,0,-1,0,0,0,1,0,0,-1,0,0,0,1,0,0,-1],[0,2,4,0,4,3,0,3,5,0,5,2,1,2,5,1,5,3,1,3,4,1,4,2],t,e),this.type="OctahedronGeometry",this.parameters={radius:t,detail:e}}static fromJSON(t){return new cl(t.radius,t.detail)}}class ul extends zn{constructor(t=1,e=1,s=1,i=1){super(),this.type="PlaneGeometry",this.parameters={width:t,height:e,widthSegments:s,heightSegments:i};const r=t/2,n=e/2,o=Math.floor(s),a=Math.floor(i),h=o+1,l=a+1,c=t/o,u=e/a,d=[],p=[],m=[],y=[];for(let t=0;t<l;t++){const e=t*u-n;for(let s=0;s<h;s++){const i=s*c-r;p.push(i,-e,0),m.push(0,0,1),y.push(s/o),y.push(1-t/a)}}for(let t=0;t<a;t++)for(let e=0;e<o;e++){const s=e+h*t,i=e+h*(t+1),r=e+1+h*(t+1),n=e+1+h*t;d.push(s,i,n),d.push(i,r,n)}this.setIndex(d),this.setAttribute("position",new bn(p,3)),this.setAttribute("normal",new bn(m,3)),this.setAttribute("uv",new bn(y,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new ul(t.width,t.height,t.widthSegments,t.heightSegments)}}class dl extends zn{constructor(t=.5,e=1,s=32,i=1,r=0,n=2*Math.PI){super(),this.type="RingGeometry",this.parameters={innerRadius:t,outerRadius:e,thetaSegments:s,phiSegments:i,thetaStart:r,thetaLength:n},s=Math.max(3,s);const o=[],a=[],h=[],l=[];let c=t;const u=(e-t)/(i=Math.max(1,i)),d=new Ii,p=new Zs;for(let t=0;t<=i;t++){for(let t=0;t<=s;t++){const i=r+t/s*n;d.x=c*Math.cos(i),d.y=c*Math.sin(i),a.push(d.x,d.y,d.z),h.push(0,0,1),p.x=(d.x/e+1)/2,p.y=(d.y/e+1)/2,l.push(p.x,p.y)}c+=u}for(let t=0;t<i;t++){const e=t*(s+1);for(let t=0;t<s;t++){const i=t+e,r=i,n=i+s+1,a=i+s+2,h=i+1;o.push(r,n,h),o.push(n,a,h)}}this.setIndex(o),this.setAttribute("position",new bn(a,3)),this.setAttribute("normal",new bn(h,3)),this.setAttribute("uv",new bn(l,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new dl(t.innerRadius,t.outerRadius,t.thetaSegments,t.phiSegments,t.thetaStart,t.thetaLength)}}class pl extends zn{constructor(t=new Rh([new Zs(0,.5),new Zs(-.5,-.5),new Zs(.5,-.5)]),e=12){super(),this.type="ShapeGeometry",this.parameters={shapes:t,curveSegments:e};const s=[],i=[],r=[],n=[];let o=0,a=0;if(!1===Array.isArray(t))h(t);else for(let e=0;e<t.length;e++)h(t[e]),this.addGroup(o,a,e),o+=a,a=0;function h(t){const o=i.length/3,h=t.extractPoints(e);let l=h.shape;const c=h.holes;!1===rl.isClockWise(l)&&(l=l.reverse());for(let t=0,e=c.length;t<e;t++){const e=c[t];!0===rl.isClockWise(e)&&(c[t]=e.reverse())}const u=rl.triangulateShape(l,c);for(let t=0,e=c.length;t<e;t++){const e=c[t];l=l.concat(e)}for(let t=0,e=l.length;t<e;t++){const e=l[t];i.push(e.x,e.y,0),r.push(0,0,1),n.push(e.x,e.y)}for(let t=0,e=u.length;t<e;t++){const e=u[t],i=e[0]+o,r=e[1]+o,n=e[2]+o;s.push(i,r,n),a+=3}}this.setIndex(s),this.setAttribute("position",new bn(i,3)),this.setAttribute("normal",new bn(r,3)),this.setAttribute("uv",new bn(n,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}toJSON(){const t=super.toJSON();return function(t,e){if(e.shapes=[],Array.isArray(t))for(let s=0,i=t.length;s<i;s++){const i=t[s];e.shapes.push(i.uuid)}else e.shapes.push(t.uuid);return e}(this.parameters.shapes,t)}static fromJSON(t,e){const s=[];for(let i=0,r=t.shapes.length;i<r;i++){const r=e[t.shapes[i]];s.push(r)}return new pl(s,t.curveSegments)}}class ml extends zn{constructor(t=1,e=32,s=16,i=0,r=2*Math.PI,n=0,o=Math.PI){super(),this.type="SphereGeometry",this.parameters={radius:t,widthSegments:e,heightSegments:s,phiStart:i,phiLength:r,thetaStart:n,thetaLength:o},e=Math.max(3,Math.floor(e)),s=Math.max(2,Math.floor(s));const a=Math.min(n+o,Math.PI);let h=0;const l=[],c=new Ii,u=new Ii,d=[],p=[],m=[],y=[];for(let d=0;d<=s;d++){const f=[],g=d/s;let x=0;0===d&&0===n?x=.5/e:d===s&&a===Math.PI&&(x=-.5/e);for(let s=0;s<=e;s++){const a=s/e;c.x=-t*Math.cos(i+a*r)*Math.sin(n+g*o),c.y=t*Math.cos(n+g*o),c.z=t*Math.sin(i+a*r)*Math.sin(n+g*o),p.push(c.x,c.y,c.z),u.copy(c).normalize(),m.push(u.x,u.y,u.z),y.push(a+x,1-g),f.push(h++)}l.push(f)}for(let t=0;t<s;t++)for(let i=0;i<e;i++){const e=l[t][i+1],r=l[t][i],o=l[t+1][i],h=l[t+1][i+1];(0!==t||n>0)&&d.push(e,r,h),(t!==s-1||a<Math.PI)&&d.push(r,o,h)}this.setIndex(d),this.setAttribute("position",new bn(p,3)),this.setAttribute("normal",new bn(m,3)),this.setAttribute("uv",new bn(y,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new ml(t.radius,t.widthSegments,t.heightSegments,t.phiStart,t.phiLength,t.thetaStart,t.thetaLength)}}class yl extends Ah{constructor(t=1,e=0){super([1,1,1,-1,-1,1,-1,1,-1,1,-1,-1],[2,1,0,0,3,2,1,3,0,2,3,1],t,e),this.type="TetrahedronGeometry",this.parameters={radius:t,detail:e}}static fromJSON(t){return new yl(t.radius,t.detail)}}class fl extends zn{constructor(t=1,e=.4,s=12,i=48,r=2*Math.PI){super(),this.type="TorusGeometry",this.parameters={radius:t,tube:e,radialSegments:s,tubularSegments:i,arc:r},s=Math.floor(s),i=Math.floor(i);const n=[],o=[],a=[],h=[],l=new Ii,c=new Ii,u=new Ii;for(let n=0;n<=s;n++)for(let d=0;d<=i;d++){const p=d/i*r,m=n/s*Math.PI*2;c.x=(t+e*Math.cos(m))*Math.cos(p),c.y=(t+e*Math.cos(m))*Math.sin(p),c.z=e*Math.sin(m),o.push(c.x,c.y,c.z),l.x=t*Math.cos(p),l.y=t*Math.sin(p),u.subVectors(c,l).normalize(),a.push(u.x,u.y,u.z),h.push(d/i),h.push(n/s)}for(let t=1;t<=s;t++)for(let e=1;e<=i;e++){const s=(i+1)*t+e-1,r=(i+1)*(t-1)+e-1,o=(i+1)*(t-1)+e,a=(i+1)*t+e;n.push(s,r,a),n.push(r,o,a)}this.setIndex(n),this.setAttribute("position",new bn(o,3)),this.setAttribute("normal",new bn(a,3)),this.setAttribute("uv",new bn(h,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new fl(t.radius,t.tube,t.radialSegments,t.tubularSegments,t.arc)}}class gl extends zn{constructor(t=1,e=.4,s=64,i=8,r=2,n=3){super(),this.type="TorusKnotGeometry",this.parameters={radius:t,tube:e,tubularSegments:s,radialSegments:i,p:r,q:n},s=Math.floor(s),i=Math.floor(i);const o=[],a=[],h=[],l=[],c=new Ii,u=new Ii,d=new Ii,p=new Ii,m=new Ii,y=new Ii,f=new Ii;for(let o=0;o<=s;++o){const x=o/s*r*Math.PI*2;g(x,r,n,t,d),g(x+.01,r,n,t,p),y.subVectors(p,d),f.addVectors(p,d),m.crossVectors(y,f),f.crossVectors(m,y),m.normalize(),f.normalize();for(let t=0;t<=i;++t){const r=t/i*Math.PI*2,n=-e*Math.cos(r),p=e*Math.sin(r);c.x=d.x+(n*f.x+p*m.x),c.y=d.y+(n*f.y+p*m.y),c.z=d.z+(n*f.z+p*m.z),a.push(c.x,c.y,c.z),u.subVectors(c,d).normalize(),h.push(u.x,u.y,u.z),l.push(o/s),l.push(t/i)}}for(let t=1;t<=s;t++)for(let e=1;e<=i;e++){const s=(i+1)*(t-1)+(e-1),r=(i+1)*t+(e-1),n=(i+1)*t+e,a=(i+1)*(t-1)+e;o.push(s,r,a),o.push(r,n,a)}function g(t,e,s,i,r){const n=Math.cos(t),o=Math.sin(t),a=s/e*t,h=Math.cos(a);r.x=i*(2+h)*.5*n,r.y=i*(2+h)*o*.5,r.z=i*Math.sin(a)*.5}this.setIndex(o),this.setAttribute("position",new bn(a,3)),this.setAttribute("normal",new bn(h,3)),this.setAttribute("uv",new bn(l,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new gl(t.radius,t.tube,t.tubularSegments,t.radialSegments,t.p,t.q)}}class xl extends zn{constructor(t=new yh(new Ii(-1,-1,0),new Ii(-1,1,0),new Ii(1,1,0)),e=64,s=1,i=8,r=!1){super(),this.type="TubeGeometry",this.parameters={path:t,tubularSegments:e,radius:s,radialSegments:i,closed:r};const n=t.computeFrenetFrames(e,r);this.tangents=n.tangents,this.normals=n.normals,this.binormals=n.binormals;const o=new Ii,a=new Ii,h=new Zs;let l=new Ii;const c=[],u=[],d=[],p=[];function m(r){l=t.getPointAt(r/e,l);const h=n.normals[r],d=n.binormals[r];for(let t=0;t<=i;t++){const e=t/i*Math.PI*2,r=Math.sin(e),n=-Math.cos(e);a.x=n*h.x+r*d.x,a.y=n*h.y+r*d.y,a.z=n*h.z+r*d.z,a.normalize(),u.push(a.x,a.y,a.z),o.x=l.x+s*a.x,o.y=l.y+s*a.y,o.z=l.z+s*a.z,c.push(o.x,o.y,o.z)}}!function(){for(let t=0;t<e;t++)m(t);m(!1===r?e:0),function(){for(let t=0;t<=e;t++)for(let s=0;s<=i;s++)h.x=t/e,h.y=s/i,d.push(h.x,h.y)}(),function(){for(let t=1;t<=e;t++)for(let e=1;e<=i;e++){const s=(i+1)*(t-1)+(e-1),r=(i+1)*t+(e-1),n=(i+1)*t+e,o=(i+1)*(t-1)+e;p.push(s,r,o),p.push(r,n,o)}}()}(),this.setIndex(p),this.setAttribute("position",new bn(c,3)),this.setAttribute("normal",new bn(u,3)),this.setAttribute("uv",new bn(d,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}toJSON(){const t=super.toJSON();return t.path=this.parameters.path.toJSON(),t}static fromJSON(t){return new xl((new gh[t.path.type]).fromJSON(t.path),t.tubularSegments,t.radius,t.radialSegments,t.closed)}}class bl extends zn{constructor(t=null){if(super(),this.type="WireframeGeometry",this.parameters={geometry:t},null!==t){const e=[],s=new Set,i=new Ii,r=new Ii;if(null!==t.index){const n=t.attributes.position,o=t.index;let a=t.groups;0===a.length&&(a=[{start:0,count:o.count,materialIndex:0}]);for(let t=0,h=a.length;t<h;++t){const h=a[t],l=h.start;for(let t=l,a=l+h.count;t<a;t+=3)for(let a=0;a<3;a++){const h=o.getX(t+a),l=o.getX(t+(a+1)%3);i.fromBufferAttribute(n,h),r.fromBufferAttribute(n,l),!0===vl(i,r,s)&&(e.push(i.x,i.y,i.z),e.push(r.x,r.y,r.z))}}}else{const n=t.attributes.position;for(let t=0,o=n.count/3;t<o;t++)for(let o=0;o<3;o++){const a=3*t+o,h=3*t+(o+1)%3;i.fromBufferAttribute(n,a),r.fromBufferAttribute(n,h),!0===vl(i,r,s)&&(e.push(i.x,i.y,i.z),e.push(r.x,r.y,r.z))}}this.setAttribute("position",new bn(e,3))}}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}}function vl(t,e,s){const i=`${t.x},${t.y},${t.z}-${e.x},${e.y},${e.z}`,r=`${e.x},${e.y},${e.z}-${t.x},${t.y},${t.z}`;return!0!==s.has(i)&&!0!==s.has(r)&&(s.add(i),s.add(r),!0)}var wl=Object.freeze({__proto__:null,BoxGeometry:jn,CapsuleGeometry:wh,CircleGeometry:Mh,ConeGeometry:_h,CylinderGeometry:Sh,DodecahedronGeometry:Th,EdgesGeometry:kh,ExtrudeGeometry:al,IcosahedronGeometry:ll,LatheGeometry:vh,OctahedronGeometry:cl,PlaneGeometry:ul,PolyhedronGeometry:Ah,RingGeometry:dl,ShapeGeometry:pl,SphereGeometry:ml,TetrahedronGeometry:yl,TorusGeometry:fl,TorusKnotGeometry:gl,TubeGeometry:xl,WireframeGeometry:bl});class Ml extends tn{constructor(t){super(),this.isShadowMaterial=!0,this.type="ShadowMaterial",this.color=new $r(0),this.transparent=!0,this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.fog=t.fog,this}}class Sl extends Jn{constructor(t){super(t),this.isRawShaderMaterial=!0,this.type="RawShaderMaterial"}}class _l extends tn{constructor(t){super(),this.isMeshStandardMaterial=!0,this.type="MeshStandardMaterial",this.defines={STANDARD:""},this.color=new $r(16777215),this.roughness=1,this.metalness=0,this.map=null,this.lightMap=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.emissive=new $r(0),this.emissiveIntensity=1,this.emissiveMap=null,this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=0,this.normalScale=new Zs(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.roughnessMap=null,this.metalnessMap=null,this.alphaMap=null,this.envMap=null,this.envMapRotation=new yr,this.envMapIntensity=1,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.flatShading=!1,this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.defines={STANDARD:""},this.color.copy(t.color),this.roughness=t.roughness,this.metalness=t.metalness,this.map=t.map,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.emissive.copy(t.emissive),this.emissiveMap=t.emissiveMap,this.emissiveIntensity=t.emissiveIntensity,this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.roughnessMap=t.roughnessMap,this.metalnessMap=t.metalnessMap,this.alphaMap=t.alphaMap,this.envMap=t.envMap,this.envMapRotation.copy(t.envMapRotation),this.envMapIntensity=t.envMapIntensity,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.flatShading=t.flatShading,this.fog=t.fog,this}}class Al extends _l{constructor(t){super(),this.isMeshPhysicalMaterial=!0,this.defines={STANDARD:"",PHYSICAL:""},this.type="MeshPhysicalMaterial",this.anisotropyRotation=0,this.anisotropyMap=null,this.clearcoatMap=null,this.clearcoatRoughness=0,this.clearcoatRoughnessMap=null,this.clearcoatNormalScale=new Zs(1,1),this.clearcoatNormalMap=null,this.ior=1.5,Object.defineProperty(this,"reflectivity",{get:function(){return Ds(2.5*(this.ior-1)/(this.ior+1),0,1)},set:function(t){this.ior=(1+.4*t)/(1-.4*t)}}),this.iridescenceMap=null,this.iridescenceIOR=1.3,this.iridescenceThicknessRange=[100,400],this.iridescenceThicknessMap=null,this.sheenColor=new $r(0),this.sheenColorMap=null,this.sheenRoughness=1,this.sheenRoughnessMap=null,this.transmissionMap=null,this.thickness=0,this.thicknessMap=null,this.attenuationDistance=1/0,this.attenuationColor=new $r(1,1,1),this.specularIntensity=1,this.specularIntensityMap=null,this.specularColor=new $r(1,1,1),this.specularColorMap=null,this._anisotropy=0,this._clearcoat=0,this._dispersion=0,this._iridescence=0,this._sheen=0,this._transmission=0,this.setValues(t)}get anisotropy(){return this._anisotropy}set anisotropy(t){this._anisotropy>0!=t>0&&this.version++,this._anisotropy=t}get clearcoat(){return this._clearcoat}set clearcoat(t){this._clearcoat>0!=t>0&&this.version++,this._clearcoat=t}get iridescence(){return this._iridescence}set iridescence(t){this._iridescence>0!=t>0&&this.version++,this._iridescence=t}get dispersion(){return this._dispersion}set dispersion(t){this._dispersion>0!=t>0&&this.version++,this._dispersion=t}get sheen(){return this._sheen}set sheen(t){this._sheen>0!=t>0&&this.version++,this._sheen=t}get transmission(){return this._transmission}set transmission(t){this._transmission>0!=t>0&&this.version++,this._transmission=t}copy(t){return super.copy(t),this.defines={STANDARD:"",PHYSICAL:""},this.anisotropy=t.anisotropy,this.anisotropyRotation=t.anisotropyRotation,this.anisotropyMap=t.anisotropyMap,this.clearcoat=t.clearcoat,this.clearcoatMap=t.clearcoatMap,this.clearcoatRoughness=t.clearcoatRoughness,this.clearcoatRoughnessMap=t.clearcoatRoughnessMap,this.clearcoatNormalMap=t.clearcoatNormalMap,this.clearcoatNormalScale.copy(t.clearcoatNormalScale),this.dispersion=t.dispersion,this.ior=t.ior,this.iridescence=t.iridescence,this.iridescenceMap=t.iridescenceMap,this.iridescenceIOR=t.iridescenceIOR,this.iridescenceThicknessRange=[...t.iridescenceThicknessRange],this.iridescenceThicknessMap=t.iridescenceThicknessMap,this.sheen=t.sheen,this.sheenColor.copy(t.sheenColor),this.sheenColorMap=t.sheenColorMap,this.sheenRoughness=t.sheenRoughness,this.sheenRoughnessMap=t.sheenRoughnessMap,this.transmission=t.transmission,this.transmissionMap=t.transmissionMap,this.thickness=t.thickness,this.thicknessMap=t.thicknessMap,this.attenuationDistance=t.attenuationDistance,this.attenuationColor.copy(t.attenuationColor),this.specularIntensity=t.specularIntensity,this.specularIntensityMap=t.specularIntensityMap,this.specularColor.copy(t.specularColor),this.specularColorMap=t.specularColorMap,this}}class Tl extends tn{constructor(t){super(),this.isMeshPhongMaterial=!0,this.type="MeshPhongMaterial",this.color=new $r(16777215),this.specular=new $r(1118481),this.shininess=30,this.map=null,this.lightMap=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.emissive=new $r(0),this.emissiveIntensity=1,this.emissiveMap=null,this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=0,this.normalScale=new Zs(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.specularMap=null,this.alphaMap=null,this.envMap=null,this.envMapRotation=new yr,this.combine=0,this.reflectivity=1,this.refractionRatio=.98,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.flatShading=!1,this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.specular.copy(t.specular),this.shininess=t.shininess,this.map=t.map,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.emissive.copy(t.emissive),this.emissiveMap=t.emissiveMap,this.emissiveIntensity=t.emissiveIntensity,this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.specularMap=t.specularMap,this.alphaMap=t.alphaMap,this.envMap=t.envMap,this.envMapRotation.copy(t.envMapRotation),this.combine=t.combine,this.reflectivity=t.reflectivity,this.refractionRatio=t.refractionRatio,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.flatShading=t.flatShading,this.fog=t.fog,this}}class zl extends tn{constructor(t){super(),this.isMeshToonMaterial=!0,this.defines={TOON:""},this.type="MeshToonMaterial",this.color=new $r(16777215),this.map=null,this.gradientMap=null,this.lightMap=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.emissive=new $r(0),this.emissiveIntensity=1,this.emissiveMap=null,this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=0,this.normalScale=new Zs(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.alphaMap=null,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.gradientMap=t.gradientMap,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.emissive.copy(t.emissive),this.emissiveMap=t.emissiveMap,this.emissiveIntensity=t.emissiveIntensity,this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.alphaMap=t.alphaMap,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.fog=t.fog,this}}class Cl extends tn{constructor(t){super(),this.isMeshNormalMaterial=!0,this.type="MeshNormalMaterial",this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=0,this.normalScale=new Zs(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.wireframe=!1,this.wireframeLinewidth=1,this.flatShading=!1,this.setValues(t)}copy(t){return super.copy(t),this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.flatShading=t.flatShading,this}}class Il extends tn{constructor(t){super(),this.isMeshLambertMaterial=!0,this.type="MeshLambertMaterial",this.color=new $r(16777215),this.map=null,this.lightMap=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.emissive=new $r(0),this.emissiveIntensity=1,this.emissiveMap=null,this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=0,this.normalScale=new Zs(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.specularMap=null,this.alphaMap=null,this.envMap=null,this.envMapRotation=new yr,this.combine=0,this.reflectivity=1,this.refractionRatio=.98,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.flatShading=!1,this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.emissive.copy(t.emissive),this.emissiveMap=t.emissiveMap,this.emissiveIntensity=t.emissiveIntensity,this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.specularMap=t.specularMap,this.alphaMap=t.alphaMap,this.envMap=t.envMap,this.envMapRotation.copy(t.envMapRotation),this.combine=t.combine,this.reflectivity=t.reflectivity,this.refractionRatio=t.refractionRatio,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.flatShading=t.flatShading,this.fog=t.fog,this}}class Bl extends tn{constructor(t){super(),this.isMeshDepthMaterial=!0,this.type="MeshDepthMaterial",this.depthPacking=3200,this.map=null,this.alphaMap=null,this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.wireframe=!1,this.wireframeLinewidth=1,this.setValues(t)}copy(t){return super.copy(t),this.depthPacking=t.depthPacking,this.map=t.map,this.alphaMap=t.alphaMap,this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this}}class kl extends tn{constructor(t){super(),this.isMeshDistanceMaterial=!0,this.type="MeshDistanceMaterial",this.map=null,this.alphaMap=null,this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.setValues(t)}copy(t){return super.copy(t),this.map=t.map,this.alphaMap=t.alphaMap,this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this}}class Rl extends tn{constructor(t){super(),this.isMeshMatcapMaterial=!0,this.defines={MATCAP:""},this.type="MeshMatcapMaterial",this.color=new $r(16777215),this.matcap=null,this.map=null,this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=0,this.normalScale=new Zs(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.alphaMap=null,this.flatShading=!1,this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.defines={MATCAP:""},this.color.copy(t.color),this.matcap=t.matcap,this.map=t.map,this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.alphaMap=t.alphaMap,this.flatShading=t.flatShading,this.fog=t.fog,this}}class El extends Sa{constructor(t){super(),this.isLineDashedMaterial=!0,this.type="LineDashedMaterial",this.scale=1,this.dashSize=3,this.gapSize=1,this.setValues(t)}copy(t){return super.copy(t),this.scale=t.scale,this.dashSize=t.dashSize,this.gapSize=t.gapSize,this}}function Pl(t,e,s){return!t||!s&&t.constructor===e?t:"number"==typeof e.BYTES_PER_ELEMENT?new e(t):Array.prototype.slice.call(t)}function Ol(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}function Fl(t){const e=t.length,s=new Array(e);for(let t=0;t!==e;++t)s[t]=t;return s.sort((function(e,s){return t[e]-t[s]})),s}function Nl(t,e,s){const i=t.length,r=new t.constructor(i);for(let n=0,o=0;o!==i;++n){const i=s[n]*e;for(let s=0;s!==e;++s)r[o++]=t[i+s]}return r}function Ll(t,e,s,i){let r=1,n=t[0];for(;void 0!==n&&void 0===n[i];)n=t[r++];if(void 0===n)return;let o=n[i];if(void 0!==o)if(Array.isArray(o))do{o=n[i],void 0!==o&&(e.push(n.time),s.push.apply(s,o)),n=t[r++]}while(void 0!==n);else if(void 0!==o.toArray)do{o=n[i],void 0!==o&&(e.push(n.time),o.toArray(s,s.length)),n=t[r++]}while(void 0!==n);else do{o=n[i],void 0!==o&&(e.push(n.time),s.push(o)),n=t[r++]}while(void 0!==n)}const Vl={convertArray:Pl,isTypedArray:Ol,getKeyframeOrder:Fl,sortedArray:Nl,flattenJSON:Ll,subclip:function(t,e,s,i,r=30){const n=t.clone();n.name=e;const o=[];for(let t=0;t<n.tracks.length;++t){const e=n.tracks[t],a=e.getValueSize(),h=[],l=[];for(let t=0;t<e.times.length;++t){const n=e.times[t]*r;if(!(n<s||n>=i)){h.push(e.times[t]);for(let s=0;s<a;++s)l.push(e.values[t*a+s])}}0!==h.length&&(e.times=Pl(h,e.times.constructor),e.values=Pl(l,e.values.constructor),o.push(e))}n.tracks=o;let a=1/0;for(let t=0;t<n.tracks.length;++t)a>n.tracks[t].times[0]&&(a=n.tracks[t].times[0]);for(let t=0;t<n.tracks.length;++t)n.tracks[t].shift(-1*a);return n.resetDuration(),n},makeClipAdditive:function(t,e=0,s=t,i=30){i<=0&&(i=30);const r=s.tracks.length,n=e/i;for(let e=0;e<r;++e){const i=s.tracks[e],r=i.ValueTypeName;if("bool"===r||"string"===r)continue;const o=t.tracks.find((function(t){return t.name===i.name&&t.ValueTypeName===r}));if(void 0===o)continue;let a=0;const h=i.getValueSize();i.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline&&(a=h/3);let l=0;const c=o.getValueSize();o.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline&&(l=c/3);const u=i.times.length-1;let d;if(n<=i.times[0]){const t=a,e=h-a;d=i.values.slice(t,e)}else if(n>=i.times[u]){const t=u*h+a,e=t+h-a;d=i.values.slice(t,e)}else{const t=i.createInterpolant(),e=a,s=h-a;t.evaluate(n),d=t.resultBuffer.slice(e,s)}if("quaternion"===r){(new Ci).fromArray(d).normalize().conjugate().toArray(d)}const p=o.times.length;for(let t=0;t<p;++t){const e=t*c+l;if("quaternion"===r)Ci.multiplyQuaternionsFlat(o.values,e,d,0,o.values,e);else{const t=c-2*l;for(let s=0;s<t;++s)o.values[e+s]-=d[s]}}}return t.blendMode=Ve,t}};class Wl{constructor(t,e,s,i){this.parameterPositions=t,this._cachedIndex=0,this.resultBuffer=void 0!==i?i:new e.constructor(s),this.sampleValues=e,this.valueSize=s,this.settings=null,this.DefaultSettings_={}}evaluate(t){const e=this.parameterPositions;let s=this._cachedIndex,i=e[s],r=e[s-1];t:{e:{let n;s:{i:if(!(t<i)){for(let n=s+2;;){if(void 0===i){if(t<r)break i;return s=e.length,this._cachedIndex=s,this.copySampleValue_(s-1)}if(s===n)break;if(r=i,i=e[++s],t<i)break e}n=e.length;break s}if(t>=r)break t;{const o=e[1];t<o&&(s=2,r=o);for(let n=s-2;;){if(void 0===r)return this._cachedIndex=0,this.copySampleValue_(0);if(s===n)break;if(i=r,r=e[--s-1],t>=r)break e}n=s,s=0}}for(;s<n;){const i=s+n>>>1;t<e[i]?n=i:s=i+1}if(i=e[s],r=e[s-1],void 0===r)return this._cachedIndex=0,this.copySampleValue_(0);if(void 0===i)return s=e.length,this._cachedIndex=s,this.copySampleValue_(s-1)}this._cachedIndex=s,this.intervalChanged_(s,r,i)}return this.interpolate_(s,r,t,i)}getSettings_(){return this.settings||this.DefaultSettings_}copySampleValue_(t){const e=this.resultBuffer,s=this.sampleValues,i=this.valueSize,r=t*i;for(let t=0;t!==i;++t)e[t]=s[r+t];return e}interpolate_(){throw new Error("call to abstract method")}intervalChanged_(){}}class jl extends Wl{constructor(t,e,s,i){super(t,e,s,i),this._weightPrev=-0,this._offsetPrev=-0,this._weightNext=-0,this._offsetNext=-0,this.DefaultSettings_={endingStart:Oe,endingEnd:Oe}}intervalChanged_(t,e,s){const i=this.parameterPositions;let r=t-2,n=t+1,o=i[r],a=i[n];if(void 0===o)switch(this.getSettings_().endingStart){case Fe:r=t,o=2*e-s;break;case Ne:r=i.length-2,o=e+i[r]-i[r+1];break;default:r=t,o=s}if(void 0===a)switch(this.getSettings_().endingEnd){case Fe:n=t,a=2*s-e;break;case Ne:n=1,a=s+i[1]-i[0];break;default:n=t-1,a=e}const h=.5*(s-e),l=this.valueSize;this._weightPrev=h/(e-o),this._weightNext=h/(a-s),this._offsetPrev=r*l,this._offsetNext=n*l}interpolate_(t,e,s,i){const r=this.resultBuffer,n=this.sampleValues,o=this.valueSize,a=t*o,h=a-o,l=this._offsetPrev,c=this._offsetNext,u=this._weightPrev,d=this._weightNext,p=(s-e)/(i-e),m=p*p,y=m*p,f=-u*y+2*u*m-u*p,g=(1+u)*y+(-1.5-2*u)*m+(-.5+u)*p+1,x=(-1-d)*y+(1.5+d)*m+.5*p,b=d*y-d*m;for(let t=0;t!==o;++t)r[t]=f*n[l+t]+g*n[h+t]+x*n[a+t]+b*n[c+t];return r}}class Ul extends Wl{constructor(t,e,s,i){super(t,e,s,i)}interpolate_(t,e,s,i){const r=this.resultBuffer,n=this.sampleValues,o=this.valueSize,a=t*o,h=a-o,l=(s-e)/(i-e),c=1-l;for(let t=0;t!==o;++t)r[t]=n[h+t]*c+n[a+t]*l;return r}}class Dl extends Wl{constructor(t,e,s,i){super(t,e,s,i)}interpolate_(t){return this.copySampleValue_(t-1)}}class Hl{constructor(t,e,s,i){if(void 0===t)throw new Error("THREE.KeyframeTrack: track name is undefined");if(void 0===e||0===e.length)throw new Error("THREE.KeyframeTrack: no keyframes in track named "+t);this.name=t,this.times=Pl(e,this.TimeBufferType),this.values=Pl(s,this.ValueBufferType),this.setInterpolation(i||this.DefaultInterpolation)}static toJSON(t){const e=t.constructor;let s;if(e.toJSON!==this.toJSON)s=e.toJSON(t);else{s={name:t.name,times:Pl(t.times,Array),values:Pl(t.values,Array)};const e=t.getInterpolation();e!==t.DefaultInterpolation&&(s.interpolation=e)}return s.type=t.ValueTypeName,s}InterpolantFactoryMethodDiscrete(t){return new Dl(this.times,this.values,this.getValueSize(),t)}InterpolantFactoryMethodLinear(t){return new Ul(this.times,this.values,this.getValueSize(),t)}InterpolantFactoryMethodSmooth(t){return new jl(this.times,this.values,this.getValueSize(),t)}setInterpolation(t){let e;switch(t){case Re:e=this.InterpolantFactoryMethodDiscrete;break;case Ee:e=this.InterpolantFactoryMethodLinear;break;case Pe:e=this.InterpolantFactoryMethodSmooth}if(void 0===e){const e="unsupported interpolation for "+this.ValueTypeName+" keyframe track named "+this.name;if(void 0===this.createInterpolant){if(t===this.DefaultInterpolation)throw new Error(e);this.setInterpolation(this.DefaultInterpolation)}return console.warn("THREE.KeyframeTrack:",e),this}return this.createInterpolant=e,this}getInterpolation(){switch(this.createInterpolant){case this.InterpolantFactoryMethodDiscrete:return Re;case this.InterpolantFactoryMethodLinear:return Ee;case this.InterpolantFactoryMethodSmooth:return Pe}}getValueSize(){return this.values.length/this.times.length}shift(t){if(0!==t){const e=this.times;for(let s=0,i=e.length;s!==i;++s)e[s]+=t}return this}scale(t){if(1!==t){const e=this.times;for(let s=0,i=e.length;s!==i;++s)e[s]*=t}return this}trim(t,e){const s=this.times,i=s.length;let r=0,n=i-1;for(;r!==i&&s[r]<t;)++r;for(;-1!==n&&s[n]>e;)--n;if(++n,0!==r||n!==i){r>=n&&(n=Math.max(n,1),r=n-1);const t=this.getValueSize();this.times=s.slice(r,n),this.values=this.values.slice(r*t,n*t)}return this}validate(){let t=!0;const e=this.getValueSize();e-Math.floor(e)!=0&&(console.error("THREE.KeyframeTrack: Invalid value size in track.",this),t=!1);const s=this.times,i=this.values,r=s.length;0===r&&(console.error("THREE.KeyframeTrack: Track is empty.",this),t=!1);let n=null;for(let e=0;e!==r;e++){const i=s[e];if("number"==typeof i&&isNaN(i)){console.error("THREE.KeyframeTrack: Time is not a valid number.",this,e,i),t=!1;break}if(null!==n&&n>i){console.error("THREE.KeyframeTrack: Out of order keys.",this,e,i,n),t=!1;break}n=i}if(void 0!==i&&Ol(i))for(let e=0,s=i.length;e!==s;++e){const s=i[e];if(isNaN(s)){console.error("THREE.KeyframeTrack: Value is not a valid number.",this,e,s),t=!1;break}}return t}optimize(){const t=this.times.slice(),e=this.values.slice(),s=this.getValueSize(),i=this.getInterpolation()===Pe,r=t.length-1;let n=1;for(let o=1;o<r;++o){let r=!1;const a=t[o];if(a!==t[o+1]&&(1!==o||a!==t[0]))if(i)r=!0;else{const t=o*s,i=t-s,n=t+s;for(let o=0;o!==s;++o){const s=e[t+o];if(s!==e[i+o]||s!==e[n+o]){r=!0;break}}}if(r){if(o!==n){t[n]=t[o];const i=o*s,r=n*s;for(let t=0;t!==s;++t)e[r+t]=e[i+t]}++n}}if(r>0){t[n]=t[r];for(let t=r*s,i=n*s,o=0;o!==s;++o)e[i+o]=e[t+o];++n}return n!==t.length?(this.times=t.slice(0,n),this.values=e.slice(0,n*s)):(this.times=t,this.values=e),this}clone(){const t=this.times.slice(),e=this.values.slice(),s=new(0,this.constructor)(this.name,t,e);return s.createInterpolant=this.createInterpolant,s}}Hl.prototype.TimeBufferType=Float32Array,Hl.prototype.ValueBufferType=Float32Array,Hl.prototype.DefaultInterpolation=Ee;class ql extends Hl{constructor(t,e,s){super(t,e,s)}}ql.prototype.ValueTypeName="bool",ql.prototype.ValueBufferType=Array,ql.prototype.DefaultInterpolation=Re,ql.prototype.InterpolantFactoryMethodLinear=void 0,ql.prototype.InterpolantFactoryMethodSmooth=void 0;class Jl extends Hl{}Jl.prototype.ValueTypeName="color";class Xl extends Hl{}Xl.prototype.ValueTypeName="number";class Yl extends Wl{constructor(t,e,s,i){super(t,e,s,i)}interpolate_(t,e,s,i){const r=this.resultBuffer,n=this.sampleValues,o=this.valueSize,a=(s-e)/(i-e);let h=t*o;for(let t=h+o;h!==t;h+=4)Ci.slerpFlat(r,0,n,h-o,n,h,a);return r}}class Zl extends Hl{InterpolantFactoryMethodLinear(t){return new Yl(this.times,this.values,this.getValueSize(),t)}}Zl.prototype.ValueTypeName="quaternion",Zl.prototype.InterpolantFactoryMethodSmooth=void 0;class Gl extends Hl{constructor(t,e,s){super(t,e,s)}}Gl.prototype.ValueTypeName="string",Gl.prototype.ValueBufferType=Array,Gl.prototype.DefaultInterpolation=Re,Gl.prototype.InterpolantFactoryMethodLinear=void 0,Gl.prototype.InterpolantFactoryMethodSmooth=void 0;class $l extends Hl{}$l.prototype.ValueTypeName="vector";class Ql{constructor(t="",e=-1,s=[],i=2500){this.name=t,this.tracks=s,this.duration=e,this.blendMode=i,this.uuid=Us(),this.duration<0&&this.resetDuration()}static parse(t){const e=[],s=t.tracks,i=1/(t.fps||1);for(let t=0,r=s.length;t!==r;++t)e.push(Kl(s[t]).scale(i));const r=new this(t.name,t.duration,e,t.blendMode);return r.uuid=t.uuid,r}static toJSON(t){const e=[],s=t.tracks,i={name:t.name,duration:t.duration,tracks:e,uuid:t.uuid,blendMode:t.blendMode};for(let t=0,i=s.length;t!==i;++t)e.push(Hl.toJSON(s[t]));return i}static CreateFromMorphTargetSequence(t,e,s,i){const r=e.length,n=[];for(let t=0;t<r;t++){let o=[],a=[];o.push((t+r-1)%r,t,(t+1)%r),a.push(0,1,0);const h=Fl(o);o=Nl(o,1,h),a=Nl(a,1,h),i||0!==o[0]||(o.push(r),a.push(a[0])),n.push(new Xl(".morphTargetInfluences["+e[t].name+"]",o,a).scale(1/s))}return new this(t,-1,n)}static findByName(t,e){let s=t;if(!Array.isArray(t)){const e=t;s=e.geometry&&e.geometry.animations||e.animations}for(let t=0;t<s.length;t++)if(s[t].name===e)return s[t];return null}static CreateClipsFromMorphTargetSequences(t,e,s){const i={},r=/^([\w-]*?)([\d]+)$/;for(let e=0,s=t.length;e<s;e++){const s=t[e],n=s.name.match(r);if(n&&n.length>1){const t=n[1];let e=i[t];e||(i[t]=e=[]),e.push(s)}}const n=[];for(const t in i)n.push(this.CreateFromMorphTargetSequence(t,i[t],e,s));return n}static parseAnimation(t,e){if(!t)return console.error("THREE.AnimationClip: No animation in JSONLoader data."),null;const s=function(t,e,s,i,r){if(0!==s.length){const n=[],o=[];Ll(s,n,o,i),0!==n.length&&r.push(new t(e,n,o))}},i=[],r=t.name||"default",n=t.fps||30,o=t.blendMode;let a=t.length||-1;const h=t.hierarchy||[];for(let t=0;t<h.length;t++){const r=h[t].keys;if(r&&0!==r.length)if(r[0].morphTargets){const t={};let e;for(e=0;e<r.length;e++)if(r[e].morphTargets)for(let s=0;s<r[e].morphTargets.length;s++)t[r[e].morphTargets[s]]=-1;for(const s in t){const t=[],n=[];for(let i=0;i!==r[e].morphTargets.length;++i){const i=r[e];t.push(i.time),n.push(i.morphTarget===s?1:0)}i.push(new Xl(".morphTargetInfluence["+s+"]",t,n))}a=t.length*n}else{const n=".bones["+e[t].name+"]";s($l,n+".position",r,"pos",i),s(Zl,n+".quaternion",r,"rot",i),s($l,n+".scale",r,"scl",i)}}if(0===i.length)return null;return new this(r,a,i,o)}resetDuration(){let t=0;for(let e=0,s=this.tracks.length;e!==s;++e){const s=this.tracks[e];t=Math.max(t,s.times[s.times.length-1])}return this.duration=t,this}trim(){for(let t=0;t<this.tracks.length;t++)this.tracks[t].trim(0,this.duration);return this}validate(){let t=!0;for(let e=0;e<this.tracks.length;e++)t=t&&this.tracks[e].validate();return t}optimize(){for(let t=0;t<this.tracks.length;t++)this.tracks[t].optimize();return this}clone(){const t=[];for(let e=0;e<this.tracks.length;e++)t.push(this.tracks[e].clone());return new this.constructor(this.name,this.duration,t,this.blendMode)}toJSON(){return this.constructor.toJSON(this)}}function Kl(t){if(void 0===t.type)throw new Error("THREE.KeyframeTrack: track type undefined, can not parse");const e=function(t){switch(t.toLowerCase()){case"scalar":case"double":case"float":case"number":case"integer":return Xl;case"vector":case"vector2":case"vector3":case"vector4":return $l;case"color":return Jl;case"quaternion":return Zl;case"bool":case"boolean":return ql;case"string":return Gl}throw new Error("THREE.KeyframeTrack: Unsupported typeName: "+t)}(t.type);if(void 0===t.times){const e=[],s=[];Ll(t.keys,e,s,"value"),t.times=e,t.values=s}return void 0!==e.parse?e.parse(t):new e(t.name,t.times,t.values,t.interpolation)}const tc={enabled:!1,files:{},add:function(t,e){!1!==this.enabled&&(this.files[t]=e)},get:function(t){if(!1!==this.enabled)return this.files[t]},remove:function(t){delete this.files[t]},clear:function(){this.files={}}};class ec{constructor(t,e,s){const i=this;let r,n=!1,o=0,a=0;const h=[];this.onStart=void 0,this.onLoad=t,this.onProgress=e,this.onError=s,this.itemStart=function(t){a++,!1===n&&void 0!==i.onStart&&i.onStart(t,o,a),n=!0},this.itemEnd=function(t){o++,void 0!==i.onProgress&&i.onProgress(t,o,a),o===a&&(n=!1,void 0!==i.onLoad&&i.onLoad())},this.itemError=function(t){void 0!==i.onError&&i.onError(t)},this.resolveURL=function(t){return r?r(t):t},this.setURLModifier=function(t){return r=t,this},this.addHandler=function(t,e){return h.push(t,e),this},this.removeHandler=function(t){const e=h.indexOf(t);return-1!==e&&h.splice(e,2),this},this.getHandler=function(t){for(let e=0,s=h.length;e<s;e+=2){const s=h[e],i=h[e+1];if(s.global&&(s.lastIndex=0),s.test(t))return i}return null}}}const sc=new ec;class ic{constructor(t){this.manager=void 0!==t?t:sc,this.crossOrigin="anonymous",this.withCredentials=!1,this.path="",this.resourcePath="",this.requestHeader={}}load(){}loadAsync(t,e){const s=this;return new Promise((function(i,r){s.load(t,i,e,r)}))}parse(){}setCrossOrigin(t){return this.crossOrigin=t,this}setWithCredentials(t){return this.withCredentials=t,this}setPath(t){return this.path=t,this}setResourcePath(t){return this.resourcePath=t,this}setRequestHeader(t){return this.requestHeader=t,this}}ic.DEFAULT_MATERIAL_NAME="__DEFAULT";const rc={};class nc extends Error{constructor(t,e){super(t),this.response=e}}class oc extends ic{constructor(t){super(t)}load(t,e,s,i){void 0===t&&(t=""),void 0!==this.path&&(t=this.path+t),t=this.manager.resolveURL(t);const r=tc.get(t);if(void 0!==r)return this.manager.itemStart(t),setTimeout((()=>{e&&e(r),this.manager.itemEnd(t)}),0),r;if(void 0!==rc[t])return void rc[t].push({onLoad:e,onProgress:s,onError:i});rc[t]=[],rc[t].push({onLoad:e,onProgress:s,onError:i});const n=new Request(t,{headers:new Headers(this.requestHeader),credentials:this.withCredentials?"include":"same-origin"}),o=this.mimeType,a=this.responseType;fetch(n).then((e=>{if(200===e.status||0===e.status){if(0===e.status&&console.warn("THREE.FileLoader: HTTP Status 0 received."),"undefined"==typeof ReadableStream||void 0===e.body||void 0===e.body.getReader)return e;const s=rc[t],i=e.body.getReader(),r=e.headers.get("X-File-Size")||e.headers.get("Content-Length"),n=r?parseInt(r):0,o=0!==n;let a=0;const h=new ReadableStream({start(t){!function e(){i.read().then((({done:i,value:r})=>{if(i)t.close();else{a+=r.byteLength;const i=new ProgressEvent("progress",{lengthComputable:o,loaded:a,total:n});for(let t=0,e=s.length;t<e;t++){const e=s[t];e.onProgress&&e.onProgress(i)}t.enqueue(r),e()}}),(e=>{t.error(e)}))}()}});return new Response(h)}throw new nc(`fetch for "${e.url}" responded with ${e.status}: ${e.statusText}`,e)})).then((t=>{switch(a){case"arraybuffer":return t.arrayBuffer();case"blob":return t.blob();case"document":return t.text().then((t=>(new DOMParser).parseFromString(t,o)));case"json":return t.json();default:if(void 0===o)return t.text();{const e=/charset="?([^;"\s]*)"?/i.exec(o),s=e&&e[1]?e[1].toLowerCase():void 0,i=new TextDecoder(s);return t.arrayBuffer().then((t=>i.decode(t)))}}})).then((e=>{tc.add(t,e);const s=rc[t];delete rc[t];for(let t=0,i=s.length;t<i;t++){const i=s[t];i.onLoad&&i.onLoad(e)}})).catch((e=>{const s=rc[t];if(void 0===s)throw this.manager.itemError(t),e;delete rc[t];for(let t=0,i=s.length;t<i;t++){const i=s[t];i.onError&&i.onError(e)}this.manager.itemError(t)})).finally((()=>{this.manager.itemEnd(t)})),this.manager.itemStart(t)}setResponseType(t){return this.responseType=t,this}setMimeType(t){return this.mimeType=t,this}}class ac extends ic{constructor(t){super(t)}load(t,e,s,i){const r=this,n=new oc(this.manager);n.setPath(this.path),n.setRequestHeader(this.requestHeader),n.setWithCredentials(this.withCredentials),n.load(t,(function(s){try{e(r.parse(JSON.parse(s)))}catch(e){i?i(e):console.error(e),r.manager.itemError(t)}}),s,i)}parse(t){const e=[];for(let s=0;s<t.length;s++){const i=Ql.parse(t[s]);e.push(i)}return e}}class hc extends ic{constructor(t){super(t)}load(t,e,s,i){const r=this,n=[],o=new Xa,a=new oc(this.manager);a.setPath(this.path),a.setResponseType("arraybuffer"),a.setRequestHeader(this.requestHeader),a.setWithCredentials(r.withCredentials);let h=0;function l(l){a.load(t[l],(function(t){const s=r.parse(t,!0);n[l]={width:s.width,height:s.height,format:s.format,mipmaps:s.mipmaps},h+=1,6===h&&(1===s.mipmapCount&&(o.minFilter=wt),o.image=n,o.format=s.format,o.needsUpdate=!0,e&&e(o))}),s,i)}if(Array.isArray(t))for(let e=0,s=t.length;e<s;++e)l(e);else a.load(t,(function(t){const s=r.parse(t,!0);if(s.isCubemap){const t=s.mipmaps.length/s.mipmapCount;for(let e=0;e<t;e++){n[e]={mipmaps:[]};for(let t=0;t<s.mipmapCount;t++)n[e].mipmaps.push(s.mipmaps[e*s.mipmapCount+t]),n[e].format=s.format,n[e].width=s.width,n[e].height=s.height}o.image=n}else o.image.width=s.width,o.image.height=s.height,o.mipmaps=s.mipmaps;1===s.mipmapCount&&(o.minFilter=wt),o.format=s.format,o.needsUpdate=!0,e&&e(o)}),s,i);return o}}class lc extends ic{constructor(t){super(t)}load(t,e,s,i){void 0!==this.path&&(t=this.path+t),t=this.manager.resolveURL(t);const r=this,n=tc.get(t);if(void 0!==n)return r.manager.itemStart(t),setTimeout((function(){e&&e(n),r.manager.itemEnd(t)}),0),n;const o=ei("img");function a(){l(),tc.add(t,this),e&&e(this),r.manager.itemEnd(t)}function h(e){l(),i&&i(e),r.manager.itemError(t),r.manager.itemEnd(t)}function l(){o.removeEventListener("load",a,!1),o.removeEventListener("error",h,!1)}return o.addEventListener("load",a,!1),o.addEventListener("error",h,!1),"data:"!==t.slice(0,5)&&void 0!==this.crossOrigin&&(o.crossOrigin=this.crossOrigin),r.manager.itemStart(t),o.src=t,o}}class cc extends ic{constructor(t){super(t)}load(t,e,s,i){const r=new to;r.colorSpace=Ge;const n=new lc(this.manager);n.setCrossOrigin(this.crossOrigin),n.setPath(this.path);let o=0;function a(s){n.load(t[s],(function(t){r.images[s]=t,o++,6===o&&(r.needsUpdate=!0,e&&e(r))}),void 0,i)}for(let e=0;e<t.length;++e)a(e);return r}}class uc extends ic{constructor(t){super(t)}load(t,e,s,i){const r=this,n=new Vo,o=new oc(this.manager);return o.setResponseType("arraybuffer"),o.setRequestHeader(this.requestHeader),o.setPath(this.path),o.setWithCredentials(r.withCredentials),o.load(t,(function(t){let s;try{s=r.parse(t)}catch(t){if(void 0===i)return void console.error(t);i(t)}void 0!==s.image?n.image=s.image:void 0!==s.data&&(n.image.width=s.width,n.image.height=s.height,n.image.data=s.data),n.wrapS=void 0!==s.wrapS?s.wrapS:mt,n.wrapT=void 0!==s.wrapT?s.wrapT:mt,n.magFilter=void 0!==s.magFilter?s.magFilter:wt,n.minFilter=void 0!==s.minFilter?s.minFilter:wt,n.anisotropy=void 0!==s.anisotropy?s.anisotropy:1,void 0!==s.colorSpace&&(n.colorSpace=s.colorSpace),void 0!==s.flipY&&(n.flipY=s.flipY),void 0!==s.format&&(n.format=s.format),void 0!==s.type&&(n.type=s.type),void 0!==s.mipmaps&&(n.mipmaps=s.mipmaps,n.minFilter=_t),1===s.mipmapCount&&(n.minFilter=wt),void 0!==s.generateMipmaps&&(n.generateMipmaps=s.generateMipmaps),n.needsUpdate=!0,e&&e(n,s)}),s,i),n}}class dc extends ic{constructor(t){super(t)}load(t,e,s,i){const r=new vi,n=new lc(this.manager);return n.setCrossOrigin(this.crossOrigin),n.setPath(this.path),n.load(t,(function(t){r.image=t,r.needsUpdate=!0,void 0!==e&&e(r)}),s,i),r}}class pc extends Rr{constructor(t,e=1){super(),this.isLight=!0,this.type="Light",this.color=new $r(t),this.intensity=e}dispose(){}copy(t,e){return super.copy(t,e),this.color.copy(t.color),this.intensity=t.intensity,this}toJSON(t){const e=super.toJSON(t);return e.object.color=this.color.getHex(),e.object.intensity=this.intensity,void 0!==this.groundColor&&(e.object.groundColor=this.groundColor.getHex()),void 0!==this.distance&&(e.object.distance=this.distance),void 0!==this.angle&&(e.object.angle=this.angle),void 0!==this.decay&&(e.object.decay=this.decay),void 0!==this.penumbra&&(e.object.penumbra=this.penumbra),void 0!==this.shadow&&(e.object.shadow=this.shadow.toJSON()),void 0!==this.target&&(e.object.target=this.target.uuid),e}}class mc extends pc{constructor(t,e,s){super(t,s),this.isHemisphereLight=!0,this.type="HemisphereLight",this.position.copy(Rr.DEFAULT_UP),this.updateMatrix(),this.groundColor=new $r(e)}copy(t,e){return super.copy(t,e),this.groundColor.copy(t.groundColor),this}}const yc=new nr,fc=new Ii,gc=new Ii;class xc{constructor(t){this.camera=t,this.intensity=1,this.bias=0,this.normalBias=0,this.radius=1,this.blurSamples=8,this.mapSize=new Zs(512,512),this.map=null,this.mapPass=null,this.matrix=new nr,this.autoUpdate=!0,this.needsUpdate=!1,this._frustum=new ra,this._frameExtents=new Zs(1,1),this._viewportCount=1,this._viewports=[new wi(0,0,1,1)]}getViewportCount(){return this._viewportCount}getFrustum(){return this._frustum}updateMatrices(t){const e=this.camera,s=this.matrix;fc.setFromMatrixPosition(t.matrixWorld),e.position.copy(fc),gc.setFromMatrixPosition(t.target.matrixWorld),e.lookAt(gc),e.updateMatrixWorld(),yc.multiplyMatrices(e.projectionMatrix,e.matrixWorldInverse),this._frustum.setFromProjectionMatrix(yc),s.set(.5,0,0,.5,0,.5,0,.5,0,0,.5,.5,0,0,0,1),s.multiply(yc)}getViewport(t){return this._viewports[t]}getFrameExtents(){return this._frameExtents}dispose(){this.map&&this.map.dispose(),this.mapPass&&this.mapPass.dispose()}copy(t){return this.camera=t.camera.clone(),this.intensity=t.intensity,this.bias=t.bias,this.radius=t.radius,this.mapSize.copy(t.mapSize),this}clone(){return(new this.constructor).copy(this)}toJSON(){const t={};return 1!==this.intensity&&(t.intensity=this.intensity),0!==this.bias&&(t.bias=this.bias),0!==this.normalBias&&(t.normalBias=this.normalBias),1!==this.radius&&(t.radius=this.radius),512===this.mapSize.x&&512===this.mapSize.y||(t.mapSize=this.mapSize.toArray()),t.camera=this.camera.toJSON(!1).object,delete t.camera.matrix,t}}class bc extends xc{constructor(){super(new $n(50,1,.5,500)),this.isSpotLightShadow=!0,this.focus=1}updateMatrices(t){const e=this.camera,s=2*js*t.angle*this.focus,i=this.mapSize.width/this.mapSize.height,r=t.distance||e.far;s===e.fov&&i===e.aspect&&r===e.far||(e.fov=s,e.aspect=i,e.far=r,e.updateProjectionMatrix()),super.updateMatrices(t)}copy(t){return super.copy(t),this.focus=t.focus,this}}class vc extends pc{constructor(t,e,s=0,i=Math.PI/3,r=0,n=2){super(t,e),this.isSpotLight=!0,this.type="SpotLight",this.position.copy(Rr.DEFAULT_UP),this.updateMatrix(),this.target=new Rr,this.distance=s,this.angle=i,this.penumbra=r,this.decay=n,this.map=null,this.shadow=new bc}get power(){return this.intensity*Math.PI}set power(t){this.intensity=t/Math.PI}dispose(){this.shadow.dispose()}copy(t,e){return super.copy(t,e),this.distance=t.distance,this.angle=t.angle,this.penumbra=t.penumbra,this.decay=t.decay,this.target=t.target.clone(),this.shadow=t.shadow.clone(),this}}const wc=new nr,Mc=new Ii,Sc=new Ii;class _c extends xc{constructor(){super(new $n(90,1,.5,500)),this.isPointLightShadow=!0,this._frameExtents=new Zs(4,2),this._viewportCount=6,this._viewports=[new wi(2,1,1,1),new wi(0,1,1,1),new wi(3,1,1,1),new wi(1,1,1,1),new wi(3,0,1,1),new wi(1,0,1,1)],this._cubeDirections=[new Ii(1,0,0),new Ii(-1,0,0),new Ii(0,0,1),new Ii(0,0,-1),new Ii(0,1,0),new Ii(0,-1,0)],this._cubeUps=[new Ii(0,1,0),new Ii(0,1,0),new Ii(0,1,0),new Ii(0,1,0),new Ii(0,0,1),new Ii(0,0,-1)]}updateMatrices(t,e=0){const s=this.camera,i=this.matrix,r=t.distance||s.far;r!==s.far&&(s.far=r,s.updateProjectionMatrix()),Mc.setFromMatrixPosition(t.matrixWorld),s.position.copy(Mc),Sc.copy(s.position),Sc.add(this._cubeDirections[e]),s.up.copy(this._cubeUps[e]),s.lookAt(Sc),s.updateMatrixWorld(),i.makeTranslation(-Mc.x,-Mc.y,-Mc.z),wc.multiplyMatrices(s.projectionMatrix,s.matrixWorldInverse),this._frustum.setFromProjectionMatrix(wc)}}class Ac extends pc{constructor(t,e,s=0,i=2){super(t,e),this.isPointLight=!0,this.type="PointLight",this.distance=s,this.decay=i,this.shadow=new _c}get power(){return 4*this.intensity*Math.PI}set power(t){this.intensity=t/(4*Math.PI)}dispose(){this.shadow.dispose()}copy(t,e){return super.copy(t,e),this.distance=t.distance,this.decay=t.decay,this.shadow=t.shadow.clone(),this}}class Tc extends Xn{constructor(t=-1,e=1,s=1,i=-1,r=.1,n=2e3){super(),this.isOrthographicCamera=!0,this.type="OrthographicCamera",this.zoom=1,this.view=null,this.left=t,this.right=e,this.top=s,this.bottom=i,this.near=r,this.far=n,this.updateProjectionMatrix()}copy(t,e){return super.copy(t,e),this.left=t.left,this.right=t.right,this.top=t.top,this.bottom=t.bottom,this.near=t.near,this.far=t.far,this.zoom=t.zoom,this.view=null===t.view?null:Object.assign({},t.view),this}setViewOffset(t,e,s,i,r,n){null===this.view&&(this.view={enabled:!0,fullWidth:1,fullHeight:1,offsetX:0,offsetY:0,width:1,height:1}),this.view.enabled=!0,this.view.fullWidth=t,this.view.fullHeight=e,this.view.offsetX=s,this.view.offsetY=i,this.view.width=r,this.view.height=n,this.updateProjectionMatrix()}clearViewOffset(){null!==this.view&&(this.view.enabled=!1),this.updateProjectionMatrix()}updateProjectionMatrix(){const t=(this.right-this.left)/(2*this.zoom),e=(this.top-this.bottom)/(2*this.zoom),s=(this.right+this.left)/2,i=(this.top+this.bottom)/2;let r=s-t,n=s+t,o=i+e,a=i-e;if(null!==this.view&&this.view.enabled){const t=(this.right-this.left)/this.view.fullWidth/this.zoom,e=(this.top-this.bottom)/this.view.fullHeight/this.zoom;r+=t*this.view.offsetX,n=r+t*this.view.width,o-=e*this.view.offsetY,a=o-e*this.view.height}this.projectionMatrix.makeOrthographic(r,n,o,a,this.near,this.far,this.coordinateSystem),this.projectionMatrixInverse.copy(this.projectionMatrix).invert()}toJSON(t){const e=super.toJSON(t);return e.object.zoom=this.zoom,e.object.left=this.left,e.object.right=this.right,e.object.top=this.top,e.object.bottom=this.bottom,e.object.near=this.near,e.object.far=this.far,null!==this.view&&(e.object.view=Object.assign({},this.view)),e}}class zc extends xc{constructor(){super(new Tc(-5,5,5,-5,.5,500)),this.isDirectionalLightShadow=!0}}class Cc extends pc{constructor(t,e){super(t,e),this.isDirectionalLight=!0,this.type="DirectionalLight",this.position.copy(Rr.DEFAULT_UP),this.updateMatrix(),this.target=new Rr,this.shadow=new zc}dispose(){this.shadow.dispose()}copy(t){return super.copy(t),this.target=t.target.clone(),this.shadow=t.shadow.clone(),this}}class Ic extends pc{constructor(t,e){super(t,e),this.isAmbientLight=!0,this.type="AmbientLight"}}class Bc extends pc{constructor(t,e,s=10,i=10){super(t,e),this.isRectAreaLight=!0,this.type="RectAreaLight",this.width=s,this.height=i}get power(){return this.intensity*this.width*this.height*Math.PI}set power(t){this.intensity=t/(this.width*this.height*Math.PI)}copy(t){return super.copy(t),this.width=t.width,this.height=t.height,this}toJSON(t){const e=super.toJSON(t);return e.object.width=this.width,e.object.height=this.height,e}}class kc{constructor(){this.isSphericalHarmonics3=!0,this.coefficients=[];for(let t=0;t<9;t++)this.coefficients.push(new Ii)}set(t){for(let e=0;e<9;e++)this.coefficients[e].copy(t[e]);return this}zero(){for(let t=0;t<9;t++)this.coefficients[t].set(0,0,0);return this}getAt(t,e){const s=t.x,i=t.y,r=t.z,n=this.coefficients;return e.copy(n[0]).multiplyScalar(.282095),e.addScaledVector(n[1],.488603*i),e.addScaledVector(n[2],.488603*r),e.addScaledVector(n[3],.488603*s),e.addScaledVector(n[4],s*i*1.092548),e.addScaledVector(n[5],i*r*1.092548),e.addScaledVector(n[6],.315392*(3*r*r-1)),e.addScaledVector(n[7],s*r*1.092548),e.addScaledVector(n[8],.546274*(s*s-i*i)),e}getIrradianceAt(t,e){const s=t.x,i=t.y,r=t.z,n=this.coefficients;return e.copy(n[0]).multiplyScalar(.886227),e.addScaledVector(n[1],1.023328*i),e.addScaledVector(n[2],1.023328*r),e.addScaledVector(n[3],1.023328*s),e.addScaledVector(n[4],.858086*s*i),e.addScaledVector(n[5],.858086*i*r),e.addScaledVector(n[6],.743125*r*r-.247708),e.addScaledVector(n[7],.858086*s*r),e.addScaledVector(n[8],.429043*(s*s-i*i)),e}add(t){for(let e=0;e<9;e++)this.coefficients[e].add(t.coefficients[e]);return this}addScaledSH(t,e){for(let s=0;s<9;s++)this.coefficients[s].addScaledVector(t.coefficients[s],e);return this}scale(t){for(let e=0;e<9;e++)this.coefficients[e].multiplyScalar(t);return this}lerp(t,e){for(let s=0;s<9;s++)this.coefficients[s].lerp(t.coefficients[s],e);return this}equals(t){for(let e=0;e<9;e++)if(!this.coefficients[e].equals(t.coefficients[e]))return!1;return!0}copy(t){return this.set(t.coefficients)}clone(){return(new this.constructor).copy(this)}fromArray(t,e=0){const s=this.coefficients;for(let i=0;i<9;i++)s[i].fromArray(t,e+3*i);return this}toArray(t=[],e=0){const s=this.coefficients;for(let i=0;i<9;i++)s[i].toArray(t,e+3*i);return t}static getBasisAt(t,e){const s=t.x,i=t.y,r=t.z;e[0]=.282095,e[1]=.488603*i,e[2]=.488603*r,e[3]=.488603*s,e[4]=1.092548*s*i,e[5]=1.092548*i*r,e[6]=.315392*(3*r*r-1),e[7]=1.092548*s*r,e[8]=.546274*(s*s-i*i)}}class Rc extends pc{constructor(t=new kc,e=1){super(void 0,e),this.isLightProbe=!0,this.sh=t}copy(t){return super.copy(t),this.sh.copy(t.sh),this}fromJSON(t){return this.intensity=t.intensity,this.sh.fromArray(t.sh),this}toJSON(t){const e=super.toJSON(t);return e.object.sh=this.sh.toArray(),e}}class Ec extends ic{constructor(t){super(t),this.textures={}}load(t,e,s,i){const r=this,n=new oc(r.manager);n.setPath(r.path),n.setRequestHeader(r.requestHeader),n.setWithCredentials(r.withCredentials),n.load(t,(function(s){try{e(r.parse(JSON.parse(s)))}catch(e){i?i(e):console.error(e),r.manager.itemError(t)}}),s,i)}parse(t){const e=this.textures;function s(t){return void 0===e[t]&&console.warn("THREE.MaterialLoader: Undefined texture",t),e[t]}const i=this.createMaterialFromType(t.type);if(void 0!==t.uuid&&(i.uuid=t.uuid),void 0!==t.name&&(i.name=t.name),void 0!==t.color&&void 0!==i.color&&i.color.setHex(t.color),void 0!==t.roughness&&(i.roughness=t.roughness),void 0!==t.metalness&&(i.metalness=t.metalness),void 0!==t.sheen&&(i.sheen=t.sheen),void 0!==t.sheenColor&&(i.sheenColor=(new $r).setHex(t.sheenColor)),void 0!==t.sheenRoughness&&(i.sheenRoughness=t.sheenRoughness),void 0!==t.emissive&&void 0!==i.emissive&&i.emissive.setHex(t.emissive),void 0!==t.specular&&void 0!==i.specular&&i.specular.setHex(t.specular),void 0!==t.specularIntensity&&(i.specularIntensity=t.specularIntensity),void 0!==t.specularColor&&void 0!==i.specularColor&&i.specularColor.setHex(t.specularColor),void 0!==t.shininess&&(i.shininess=t.shininess),void 0!==t.clearcoat&&(i.clearcoat=t.clearcoat),void 0!==t.clearcoatRoughness&&(i.clearcoatRoughness=t.clearcoatRoughness),void 0!==t.dispersion&&(i.dispersion=t.dispersion),void 0!==t.iridescence&&(i.iridescence=t.iridescence),void 0!==t.iridescenceIOR&&(i.iridescenceIOR=t.iridescenceIOR),void 0!==t.iridescenceThicknessRange&&(i.iridescenceThicknessRange=t.iridescenceThicknessRange),void 0!==t.transmission&&(i.transmission=t.transmission),void 0!==t.thickness&&(i.thickness=t.thickness),void 0!==t.attenuationDistance&&(i.attenuationDistance=t.attenuationDistance),void 0!==t.attenuationColor&&void 0!==i.attenuationColor&&i.attenuationColor.setHex(t.attenuationColor),void 0!==t.anisotropy&&(i.anisotropy=t.anisotropy),void 0!==t.anisotropyRotation&&(i.anisotropyRotation=t.anisotropyRotation),void 0!==t.fog&&(i.fog=t.fog),void 0!==t.flatShading&&(i.flatShading=t.flatShading),void 0!==t.blending&&(i.blending=t.blending),void 0!==t.combine&&(i.combine=t.combine),void 0!==t.side&&(i.side=t.side),void 0!==t.shadowSide&&(i.shadowSide=t.shadowSide),void 0!==t.opacity&&(i.opacity=t.opacity),void 0!==t.transparent&&(i.transparent=t.transparent),void 0!==t.alphaTest&&(i.alphaTest=t.alphaTest),void 0!==t.alphaHash&&(i.alphaHash=t.alphaHash),void 0!==t.depthFunc&&(i.depthFunc=t.depthFunc),void 0!==t.depthTest&&(i.depthTest=t.depthTest),void 0!==t.depthWrite&&(i.depthWrite=t.depthWrite),void 0!==t.colorWrite&&(i.colorWrite=t.colorWrite),void 0!==t.blendSrc&&(i.blendSrc=t.blendSrc),void 0!==t.blendDst&&(i.blendDst=t.blendDst),void 0!==t.blendEquation&&(i.blendEquation=t.blendEquation),void 0!==t.blendSrcAlpha&&(i.blendSrcAlpha=t.blendSrcAlpha),void 0!==t.blendDstAlpha&&(i.blendDstAlpha=t.blendDstAlpha),void 0!==t.blendEquationAlpha&&(i.blendEquationAlpha=t.blendEquationAlpha),void 0!==t.blendColor&&void 0!==i.blendColor&&i.blendColor.setHex(t.blendColor),void 0!==t.blendAlpha&&(i.blendAlpha=t.blendAlpha),void 0!==t.stencilWriteMask&&(i.stencilWriteMask=t.stencilWriteMask),void 0!==t.stencilFunc&&(i.stencilFunc=t.stencilFunc),void 0!==t.stencilRef&&(i.stencilRef=t.stencilRef),void 0!==t.stencilFuncMask&&(i.stencilFuncMask=t.stencilFuncMask),void 0!==t.stencilFail&&(i.stencilFail=t.stencilFail),void 0!==t.stencilZFail&&(i.stencilZFail=t.stencilZFail),void 0!==t.stencilZPass&&(i.stencilZPass=t.stencilZPass),void 0!==t.stencilWrite&&(i.stencilWrite=t.stencilWrite),void 0!==t.wireframe&&(i.wireframe=t.wireframe),void 0!==t.wireframeLinewidth&&(i.wireframeLinewidth=t.wireframeLinewidth),void 0!==t.wireframeLinecap&&(i.wireframeLinecap=t.wireframeLinecap),void 0!==t.wireframeLinejoin&&(i.wireframeLinejoin=t.wireframeLinejoin),void 0!==t.rotation&&(i.rotation=t.rotation),void 0!==t.linewidth&&(i.linewidth=t.linewidth),void 0!==t.dashSize&&(i.dashSize=t.dashSize),void 0!==t.gapSize&&(i.gapSize=t.gapSize),void 0!==t.scale&&(i.scale=t.scale),void 0!==t.polygonOffset&&(i.polygonOffset=t.polygonOffset),void 0!==t.polygonOffsetFactor&&(i.polygonOffsetFactor=t.polygonOffsetFactor),void 0!==t.polygonOffsetUnits&&(i.polygonOffsetUnits=t.polygonOffsetUnits),void 0!==t.dithering&&(i.dithering=t.dithering),void 0!==t.alphaToCoverage&&(i.alphaToCoverage=t.alphaToCoverage),void 0!==t.premultipliedAlpha&&(i.premultipliedAlpha=t.premultipliedAlpha),void 0!==t.forceSinglePass&&(i.forceSinglePass=t.forceSinglePass),void 0!==t.visible&&(i.visible=t.visible),void 0!==t.toneMapped&&(i.toneMapped=t.toneMapped),void 0!==t.userData&&(i.userData=t.userData),void 0!==t.vertexColors&&("number"==typeof t.vertexColors?i.vertexColors=t.vertexColors>0:i.vertexColors=t.vertexColors),void 0!==t.uniforms)for(const e in t.uniforms){const r=t.uniforms[e];switch(i.uniforms[e]={},r.type){case"t":i.uniforms[e].value=s(r.value);break;case"c":i.uniforms[e].value=(new $r).setHex(r.value);break;case"v2":i.uniforms[e].value=(new Zs).fromArray(r.value);break;case"v3":i.uniforms[e].value=(new Ii).fromArray(r.value);break;case"v4":i.uniforms[e].value=(new wi).fromArray(r.value);break;case"m3":i.uniforms[e].value=(new Gs).fromArray(r.value);break;case"m4":i.uniforms[e].value=(new nr).fromArray(r.value);break;default:i.uniforms[e].value=r.value}}if(void 0!==t.defines&&(i.defines=t.defines),void 0!==t.vertexShader&&(i.vertexShader=t.vertexShader),void 0!==t.fragmentShader&&(i.fragmentShader=t.fragmentShader),void 0!==t.glslVersion&&(i.glslVersion=t.glslVersion),void 0!==t.extensions)for(const e in t.extensions)i.extensions[e]=t.extensions[e];if(void 0!==t.lights&&(i.lights=t.lights),void 0!==t.clipping&&(i.clipping=t.clipping),void 0!==t.size&&(i.size=t.size),void 0!==t.sizeAttenuation&&(i.sizeAttenuation=t.sizeAttenuation),void 0!==t.map&&(i.map=s(t.map)),void 0!==t.matcap&&(i.matcap=s(t.matcap)),void 0!==t.alphaMap&&(i.alphaMap=s(t.alphaMap)),void 0!==t.bumpMap&&(i.bumpMap=s(t.bumpMap)),void 0!==t.bumpScale&&(i.bumpScale=t.bumpScale),void 0!==t.normalMap&&(i.normalMap=s(t.normalMap)),void 0!==t.normalMapType&&(i.normalMapType=t.normalMapType),void 0!==t.normalScale){let e=t.normalScale;!1===Array.isArray(e)&&(e=[e,e]),i.normalScale=(new Zs).fromArray(e)}return void 0!==t.displacementMap&&(i.displacementMap=s(t.displacementMap)),void 0!==t.displacementScale&&(i.displacementScale=t.displacementScale),void 0!==t.displacementBias&&(i.displacementBias=t.displacementBias),void 0!==t.roughnessMap&&(i.roughnessMap=s(t.roughnessMap)),void 0!==t.metalnessMap&&(i.metalnessMap=s(t.metalnessMap)),void 0!==t.emissiveMap&&(i.emissiveMap=s(t.emissiveMap)),void 0!==t.emissiveIntensity&&(i.emissiveIntensity=t.emissiveIntensity),void 0!==t.specularMap&&(i.specularMap=s(t.specularMap)),void 0!==t.specularIntensityMap&&(i.specularIntensityMap=s(t.specularIntensityMap)),void 0!==t.specularColorMap&&(i.specularColorMap=s(t.specularColorMap)),void 0!==t.envMap&&(i.envMap=s(t.envMap)),void 0!==t.envMapRotation&&i.envMapRotation.fromArray(t.envMapRotation),void 0!==t.envMapIntensity&&(i.envMapIntensity=t.envMapIntensity),void 0!==t.reflectivity&&(i.reflectivity=t.reflectivity),void 0!==t.refractionRatio&&(i.refractionRatio=t.refractionRatio),void 0!==t.lightMap&&(i.lightMap=s(t.lightMap)),void 0!==t.lightMapIntensity&&(i.lightMapIntensity=t.lightMapIntensity),void 0!==t.aoMap&&(i.aoMap=s(t.aoMap)),void 0!==t.aoMapIntensity&&(i.aoMapIntensity=t.aoMapIntensity),void 0!==t.gradientMap&&(i.gradientMap=s(t.gradientMap)),void 0!==t.clearcoatMap&&(i.clearcoatMap=s(t.clearcoatMap)),void 0!==t.clearcoatRoughnessMap&&(i.clearcoatRoughnessMap=s(t.clearcoatRoughnessMap)),void 0!==t.clearcoatNormalMap&&(i.clearcoatNormalMap=s(t.clearcoatNormalMap)),void 0!==t.clearcoatNormalScale&&(i.clearcoatNormalScale=(new Zs).fromArray(t.clearcoatNormalScale)),void 0!==t.iridescenceMap&&(i.iridescenceMap=s(t.iridescenceMap)),void 0!==t.iridescenceThicknessMap&&(i.iridescenceThicknessMap=s(t.iridescenceThicknessMap)),void 0!==t.transmissionMap&&(i.transmissionMap=s(t.transmissionMap)),void 0!==t.thicknessMap&&(i.thicknessMap=s(t.thicknessMap)),void 0!==t.anisotropyMap&&(i.anisotropyMap=s(t.anisotropyMap)),void 0!==t.sheenColorMap&&(i.sheenColorMap=s(t.sheenColorMap)),void 0!==t.sheenRoughnessMap&&(i.sheenRoughnessMap=s(t.sheenRoughnessMap)),i}setTextures(t){return this.textures=t,this}createMaterialFromType(t){return Ec.createMaterialFromType(t)}static createMaterialFromType(t){return new{ShadowMaterial:Ml,SpriteMaterial:ho,RawShaderMaterial:Sl,ShaderMaterial:Jn,PointsMaterial:Na,MeshPhysicalMaterial:Al,MeshStandardMaterial:_l,MeshPhongMaterial:Tl,MeshToonMaterial:zl,MeshNormalMaterial:Cl,MeshLambertMaterial:Il,MeshDepthMaterial:Bl,MeshDistanceMaterial:kl,MeshBasicMaterial:en,MeshMatcapMaterial:Rl,LineDashedMaterial:El,LineBasicMaterial:Sa,Material:tn}[t]}}class Pc{static decodeText(t){if(console.warn("THREE.LoaderUtils: decodeText() has been deprecated with r165 and will be removed with r175. Use TextDecoder instead."),"undefined"!=typeof TextDecoder)return(new TextDecoder).decode(t);let e="";for(let s=0,i=t.length;s<i;s++)e+=String.fromCharCode(t[s]);try{return decodeURIComponent(escape(e))}catch(t){return e}}static extractUrlBase(t){const e=t.lastIndexOf("/");return-1===e?"./":t.slice(0,e+1)}static resolveURL(t,e){return"string"!=typeof t||""===t?"":(/^https?:\/\//i.test(e)&&/^\//.test(t)&&(e=e.replace(/(^https?:\/\/[^\/]+).*/i,"$1")),/^(https?:)?\/\//i.test(t)||/^data:.*,.*$/i.test(t)||/^blob:.*$/i.test(t)?t:e+t)}}class Oc extends zn{constructor(){super(),this.isInstancedBufferGeometry=!0,this.type="InstancedBufferGeometry",this.instanceCount=1/0}copy(t){return super.copy(t),this.instanceCount=t.instanceCount,this}toJSON(){const t=super.toJSON();return t.instanceCount=this.instanceCount,t.isInstancedBufferGeometry=!0,t}}class Fc extends ic{constructor(t){super(t)}load(t,e,s,i){const r=this,n=new oc(r.manager);n.setPath(r.path),n.setRequestHeader(r.requestHeader),n.setWithCredentials(r.withCredentials),n.load(t,(function(s){try{e(r.parse(JSON.parse(s)))}catch(e){i?i(e):console.error(e),r.manager.itemError(t)}}),s,i)}parse(t){const e={},s={};function i(t,i){if(void 0!==e[i])return e[i];const r=t.interleavedBuffers[i],n=function(t,e){if(void 0!==s[e])return s[e];const i=t.arrayBuffers,r=i[e],n=new Uint32Array(r).buffer;return s[e]=n,n}(t,r.buffer),o=ti(r.type,n),a=new no(o,r.stride);return a.uuid=r.uuid,e[i]=a,a}const r=t.isInstancedBufferGeometry?new Oc:new zn,n=t.data.index;if(void 0!==n){const t=ti(n.type,n.array);r.setIndex(new cn(t,1))}const o=t.data.attributes;for(const e in o){const s=o[e];let n;if(s.isInterleavedBufferAttribute){const e=i(t.data,s.data);n=new ao(e,s.itemSize,s.offset,s.normalized)}else{const t=ti(s.type,s.array);n=new(s.isInstancedBufferAttribute?Do:cn)(t,s.itemSize,s.normalized)}void 0!==s.name&&(n.name=s.name),void 0!==s.usage&&n.setUsage(s.usage),r.setAttribute(e,n)}const a=t.data.morphAttributes;if(a)for(const e in a){const s=a[e],n=[];for(let e=0,r=s.length;e<r;e++){const r=s[e];let o;if(r.isInterleavedBufferAttribute){const e=i(t.data,r.data);o=new ao(e,r.itemSize,r.offset,r.normalized)}else{const t=ti(r.type,r.array);o=new cn(t,r.itemSize,r.normalized)}void 0!==r.name&&(o.name=r.name),n.push(o)}r.morphAttributes[e]=n}t.data.morphTargetsRelative&&(r.morphTargetsRelative=!0);const h=t.data.groups||t.data.drawcalls||t.data.offsets;if(void 0!==h)for(let t=0,e=h.length;t!==e;++t){const e=h[t];r.addGroup(e.start,e.count,e.materialIndex)}const l=t.data.boundingSphere;if(void 0!==l){const t=new Ii;void 0!==l.center&&t.fromArray(l.center),r.boundingSphere=new Gi(t,l.radius)}return t.name&&(r.name=t.name),t.userData&&(r.userData=t.userData),r}}class Nc extends ic{constructor(t){super(t)}load(t,e,s,i){const r=this,n=""===this.path?Pc.extractUrlBase(t):this.path;this.resourcePath=this.resourcePath||n;const o=new oc(this.manager);o.setPath(this.path),o.setRequestHeader(this.requestHeader),o.setWithCredentials(this.withCredentials),o.load(t,(function(s){let n=null;try{n=JSON.parse(s)}catch(e){return void 0!==i&&i(e),void console.error("THREE:ObjectLoader: Can't parse "+t+".",e.message)}const o=n.metadata;if(void 0===o||void 0===o.type||"geometry"===o.type.toLowerCase())return void 0!==i&&i(new Error("THREE.ObjectLoader: Can't load "+t)),void console.error("THREE.ObjectLoader: Can't load "+t);r.parse(n,e)}),s,i)}async loadAsync(t,e){const s=""===this.path?Pc.extractUrlBase(t):this.path;this.resourcePath=this.resourcePath||s;const i=new oc(this.manager);i.setPath(this.path),i.setRequestHeader(this.requestHeader),i.setWithCredentials(this.withCredentials);const r=await i.loadAsync(t,e),n=JSON.parse(r),o=n.metadata;if(void 0===o||void 0===o.type||"geometry"===o.type.toLowerCase())throw new Error("THREE.ObjectLoader: Can't load "+t);return await this.parseAsync(n)}parse(t,e){const s=this.parseAnimations(t.animations),i=this.parseShapes(t.shapes),r=this.parseGeometries(t.geometries,i),n=this.parseImages(t.images,(function(){void 0!==e&&e(h)})),o=this.parseTextures(t.textures,n),a=this.parseMaterials(t.materials,o),h=this.parseObject(t.object,r,a,o,s),l=this.parseSkeletons(t.skeletons,h);if(this.bindSkeletons(h,l),this.bindLightTargets(h),void 0!==e){let t=!1;for(const e in n)if(n[e].data instanceof HTMLImageElement){t=!0;break}!1===t&&e(h)}return h}async parseAsync(t){const e=this.parseAnimations(t.animations),s=this.parseShapes(t.shapes),i=this.parseGeometries(t.geometries,s),r=await this.parseImagesAsync(t.images),n=this.parseTextures(t.textures,r),o=this.parseMaterials(t.materials,n),a=this.parseObject(t.object,i,o,n,e),h=this.parseSkeletons(t.skeletons,a);return this.bindSkeletons(a,h),this.bindLightTargets(a),a}parseShapes(t){const e={};if(void 0!==t)for(let s=0,i=t.length;s<i;s++){const i=(new Rh).fromJSON(t[s]);e[i.uuid]=i}return e}parseSkeletons(t,e){const s={},i={};if(e.traverse((function(t){t.isBone&&(i[t.uuid]=t)})),void 0!==t)for(let e=0,r=t.length;e<r;e++){const r=(new Uo).fromJSON(t[e],i);s[r.uuid]=r}return s}parseGeometries(t,e){const s={};if(void 0!==t){const i=new Fc;for(let r=0,n=t.length;r<n;r++){let n;const o=t[r];switch(o.type){case"BufferGeometry":case"InstancedBufferGeometry":n=i.parse(o);break;default:o.type in wl?n=wl[o.type].fromJSON(o,e):console.warn(`THREE.ObjectLoader: Unsupported geometry type "${o.type}"`)}n.uuid=o.uuid,void 0!==o.name&&(n.name=o.name),void 0!==o.userData&&(n.userData=o.userData),s[o.uuid]=n}}return s}parseMaterials(t,e){const s={},i={};if(void 0!==t){const r=new Ec;r.setTextures(e);for(let e=0,n=t.length;e<n;e++){const n=t[e];void 0===s[n.uuid]&&(s[n.uuid]=r.parse(n)),i[n.uuid]=s[n.uuid]}}return i}parseAnimations(t){const e={};if(void 0!==t)for(let s=0;s<t.length;s++){const i=t[s],r=Ql.parse(i);e[r.uuid]=r}return e}parseImages(t,e){const s=this,i={};let r;function n(t){if("string"==typeof t){const e=t;return function(t){return s.manager.itemStart(t),r.load(t,(function(){s.manager.itemEnd(t)}),void 0,(function(){s.manager.itemError(t),s.manager.itemEnd(t)}))}(/^(\/\/)|([a-z]+:(\/\/)?)/i.test(e)?e:s.resourcePath+e)}return t.data?{data:ti(t.type,t.data),width:t.width,height:t.height}:null}if(void 0!==t&&t.length>0){const s=new ec(e);r=new lc(s),r.setCrossOrigin(this.crossOrigin);for(let e=0,s=t.length;e<s;e++){const s=t[e],r=s.url;if(Array.isArray(r)){const t=[];for(let e=0,s=r.length;e<s;e++){const s=n(r[e]);null!==s&&(s instanceof HTMLImageElement?t.push(s):t.push(new Vo(s.data,s.width,s.height)))}i[s.uuid]=new gi(t)}else{const t=n(s.url);i[s.uuid]=new gi(t)}}}return i}async parseImagesAsync(t){const e=this,s={};let i;async function r(t){if("string"==typeof t){const s=t,r=/^(\/\/)|([a-z]+:(\/\/)?)/i.test(s)?s:e.resourcePath+s;return await i.loadAsync(r)}return t.data?{data:ti(t.type,t.data),width:t.width,height:t.height}:null}if(void 0!==t&&t.length>0){i=new lc(this.manager),i.setCrossOrigin(this.crossOrigin);for(let e=0,i=t.length;e<i;e++){const i=t[e],n=i.url;if(Array.isArray(n)){const t=[];for(let e=0,s=n.length;e<s;e++){const s=n[e],i=await r(s);null!==i&&(i instanceof HTMLImageElement?t.push(i):t.push(new Vo(i.data,i.width,i.height)))}s[i.uuid]=new gi(t)}else{const t=await r(i.url);s[i.uuid]=new gi(t)}}}return s}parseTextures(t,e){function s(t,e){return"number"==typeof t?t:(console.warn("THREE.ObjectLoader.parseTexture: Constant should be in numeric form.",t),e[t])}const i={};if(void 0!==t)for(let r=0,n=t.length;r<n;r++){const n=t[r];void 0===n.image&&console.warn('THREE.ObjectLoader: No "image" specified for',n.uuid),void 0===e[n.image]&&console.warn("THREE.ObjectLoader: Undefined image",n.image);const o=e[n.image],a=o.data;let h;Array.isArray(a)?(h=new to,6===a.length&&(h.needsUpdate=!0)):(h=a&&a.data?new Vo:new vi,a&&(h.needsUpdate=!0)),h.source=o,h.uuid=n.uuid,void 0!==n.name&&(h.name=n.name),void 0!==n.mapping&&(h.mapping=s(n.mapping,Lc)),void 0!==n.channel&&(h.channel=n.channel),void 0!==n.offset&&h.offset.fromArray(n.offset),void 0!==n.repeat&&h.repeat.fromArray(n.repeat),void 0!==n.center&&h.center.fromArray(n.center),void 0!==n.rotation&&(h.rotation=n.rotation),void 0!==n.wrap&&(h.wrapS=s(n.wrap[0],Vc),h.wrapT=s(n.wrap[1],Vc)),void 0!==n.format&&(h.format=n.format),void 0!==n.internalFormat&&(h.internalFormat=n.internalFormat),void 0!==n.type&&(h.type=n.type),void 0!==n.colorSpace&&(h.colorSpace=n.colorSpace),void 0!==n.minFilter&&(h.minFilter=s(n.minFilter,Wc)),void 0!==n.magFilter&&(h.magFilter=s(n.magFilter,Wc)),void 0!==n.anisotropy&&(h.anisotropy=n.anisotropy),void 0!==n.flipY&&(h.flipY=n.flipY),void 0!==n.generateMipmaps&&(h.generateMipmaps=n.generateMipmaps),void 0!==n.premultiplyAlpha&&(h.premultiplyAlpha=n.premultiplyAlpha),void 0!==n.unpackAlignment&&(h.unpackAlignment=n.unpackAlignment),void 0!==n.compareFunction&&(h.compareFunction=n.compareFunction),void 0!==n.userData&&(h.userData=n.userData),i[n.uuid]=h}return i}parseObject(t,e,s,i,r){let n,o,a;function h(t){return void 0===e[t]&&console.warn("THREE.ObjectLoader: Undefined geometry",t),e[t]}function l(t){if(void 0!==t){if(Array.isArray(t)){const e=[];for(let i=0,r=t.length;i<r;i++){const r=t[i];void 0===s[r]&&console.warn("THREE.ObjectLoader: Undefined material",r),e.push(s[r])}return e}return void 0===s[t]&&console.warn("THREE.ObjectLoader: Undefined material",t),s[t]}}function c(t){return void 0===i[t]&&console.warn("THREE.ObjectLoader: Undefined texture",t),i[t]}switch(t.type){case"Scene":n=new ro,void 0!==t.background&&(Number.isInteger(t.background)?n.background=new $r(t.background):n.background=c(t.background)),void 0!==t.environment&&(n.environment=c(t.environment)),void 0!==t.fog&&("Fog"===t.fog.type?n.fog=new io(t.fog.color,t.fog.near,t.fog.far):"FogExp2"===t.fog.type&&(n.fog=new so(t.fog.color,t.fog.density)),""!==t.fog.name&&(n.fog.name=t.fog.name)),void 0!==t.backgroundBlurriness&&(n.backgroundBlurriness=t.backgroundBlurriness),void 0!==t.backgroundIntensity&&(n.backgroundIntensity=t.backgroundIntensity),void 0!==t.backgroundRotation&&n.backgroundRotation.fromArray(t.backgroundRotation),void 0!==t.environmentIntensity&&(n.environmentIntensity=t.environmentIntensity),void 0!==t.environmentRotation&&n.environmentRotation.fromArray(t.environmentRotation);break;case"PerspectiveCamera":n=new $n(t.fov,t.aspect,t.near,t.far),void 0!==t.focus&&(n.focus=t.focus),void 0!==t.zoom&&(n.zoom=t.zoom),void 0!==t.filmGauge&&(n.filmGauge=t.filmGauge),void 0!==t.filmOffset&&(n.filmOffset=t.filmOffset),void 0!==t.view&&(n.view=Object.assign({},t.view));break;case"OrthographicCamera":n=new Tc(t.left,t.right,t.top,t.bottom,t.near,t.far),void 0!==t.zoom&&(n.zoom=t.zoom),void 0!==t.view&&(n.view=Object.assign({},t.view));break;case"AmbientLight":n=new Ic(t.color,t.intensity);break;case"DirectionalLight":n=new Cc(t.color,t.intensity),n.target=t.target||"";break;case"PointLight":n=new Ac(t.color,t.intensity,t.distance,t.decay);break;case"RectAreaLight":n=new Bc(t.color,t.intensity,t.width,t.height);break;case"SpotLight":n=new vc(t.color,t.intensity,t.distance,t.angle,t.penumbra,t.decay),n.target=t.target||"";break;case"HemisphereLight":n=new mc(t.color,t.groundColor,t.intensity);break;case"LightProbe":n=(new Rc).fromJSON(t);break;case"SkinnedMesh":o=h(t.geometry),a=l(t.material),n=new No(o,a),void 0!==t.bindMode&&(n.bindMode=t.bindMode),void 0!==t.bindMatrix&&n.bindMatrix.fromArray(t.bindMatrix),void 0!==t.skeleton&&(n.skeleton=t.skeleton);break;case"Mesh":o=h(t.geometry),a=l(t.material),n=new Vn(o,a);break;case"InstancedMesh":o=h(t.geometry),a=l(t.material);const e=t.count,s=t.instanceMatrix,i=t.instanceColor;n=new $o(o,a,e),n.instanceMatrix=new Do(new Float32Array(s.array),16),void 0!==i&&(n.instanceColor=new Do(new Float32Array(i.array),i.itemSize));break;case"BatchedMesh":o=h(t.geometry),a=l(t.material),n=new Ma(t.maxInstanceCount,t.maxVertexCount,t.maxIndexCount,a),n.geometry=o,n.perObjectFrustumCulled=t.perObjectFrustumCulled,n.sortObjects=t.sortObjects,n._drawRanges=t.drawRanges,n._reservedRanges=t.reservedRanges,n._visibility=t.visibility,n._active=t.active,n._bounds=t.bounds.map((t=>{const e=new Ri;e.min.fromArray(t.boxMin),e.max.fromArray(t.boxMax);const s=new Gi;return s.radius=t.sphereRadius,s.center.fromArray(t.sphereCenter),{boxInitialized:t.boxInitialized,box:e,sphereInitialized:t.sphereInitialized,sphere:s}})),n._maxInstanceCount=t.maxInstanceCount,n._maxVertexCount=t.maxVertexCount,n._maxIndexCount=t.maxIndexCount,n._geometryInitialized=t.geometryInitialized,n._geometryCount=t.geometryCount,n._matricesTexture=c(t.matricesTexture.uuid),void 0!==t.colorsTexture&&(n._colorsTexture=c(t.colorsTexture.uuid));break;case"LOD":n=new zo;break;case"Line":n=new ka(h(t.geometry),l(t.material));break;case"LineLoop":n=new Fa(h(t.geometry),l(t.material));break;case"LineSegments":n=new Oa(h(t.geometry),l(t.material));break;case"PointCloud":case"Points":n=new Ua(h(t.geometry),l(t.material));break;case"Sprite":n=new So(l(t.material));break;case"Group":n=new Ha;break;case"Bone":n=new Lo;break;default:n=new Rr}if(n.uuid=t.uuid,void 0!==t.name&&(n.name=t.name),void 0!==t.matrix?(n.matrix.fromArray(t.matrix),void 0!==t.matrixAutoUpdate&&(n.matrixAutoUpdate=t.matrixAutoUpdate),n.matrixAutoUpdate&&n.matrix.decompose(n.position,n.quaternion,n.scale)):(void 0!==t.position&&n.position.fromArray(t.position),void 0!==t.rotation&&n.rotation.fromArray(t.rotation),void 0!==t.quaternion&&n.quaternion.fromArray(t.quaternion),void 0!==t.scale&&n.scale.fromArray(t.scale)),void 0!==t.up&&n.up.fromArray(t.up),void 0!==t.castShadow&&(n.castShadow=t.castShadow),void 0!==t.receiveShadow&&(n.receiveShadow=t.receiveShadow),t.shadow&&(void 0!==t.shadow.intensity&&(n.shadow.intensity=t.shadow.intensity),void 0!==t.shadow.bias&&(n.shadow.bias=t.shadow.bias),void 0!==t.shadow.normalBias&&(n.shadow.normalBias=t.shadow.normalBias),void 0!==t.shadow.radius&&(n.shadow.radius=t.shadow.radius),void 0!==t.shadow.mapSize&&n.shadow.mapSize.fromArray(t.shadow.mapSize),void 0!==t.shadow.camera&&(n.shadow.camera=this.parseObject(t.shadow.camera))),void 0!==t.visible&&(n.visible=t.visible),void 0!==t.frustumCulled&&(n.frustumCulled=t.frustumCulled),void 0!==t.renderOrder&&(n.renderOrder=t.renderOrder),void 0!==t.userData&&(n.userData=t.userData),void 0!==t.layers&&(n.layers.mask=t.layers),void 0!==t.children){const o=t.children;for(let t=0;t<o.length;t++)n.add(this.parseObject(o[t],e,s,i,r))}if(void 0!==t.animations){const e=t.animations;for(let t=0;t<e.length;t++){const s=e[t];n.animations.push(r[s])}}if("LOD"===t.type){void 0!==t.autoUpdate&&(n.autoUpdate=t.autoUpdate);const e=t.levels;for(let t=0;t<e.length;t++){const s=e[t],i=n.getObjectByProperty("uuid",s.object);void 0!==i&&n.addLevel(i,s.distance,s.hysteresis)}}return n}bindSkeletons(t,e){0!==Object.keys(e).length&&t.traverse((function(t){if(!0===t.isSkinnedMesh&&void 0!==t.skeleton){const s=e[t.skeleton];void 0===s?console.warn("THREE.ObjectLoader: No skeleton found with UUID:",t.skeleton):t.bind(s,t.bindMatrix)}}))}bindLightTargets(t){t.traverse((function(e){if(e.isDirectionalLight||e.isSpotLight){const s=e.target,i=t.getObjectByProperty("uuid",s);e.target=void 0!==i?i:new Rr}}))}}const Lc={UVMapping:at,CubeReflectionMapping:ht,CubeRefractionMapping:302,EquirectangularReflectionMapping:303,EquirectangularRefractionMapping:304,CubeUVReflectionMapping:306},Vc={RepeatWrapping:pt,ClampToEdgeWrapping:mt,MirroredRepeatWrapping:yt},Wc={NearestFilter:ft,NearestMipmapNearestFilter:1004,NearestMipmapLinearFilter:1005,LinearFilter:wt,LinearMipmapNearestFilter:1007,LinearMipmapLinearFilter:_t};class jc extends ic{constructor(t){super(t),this.isImageBitmapLoader=!0,"undefined"==typeof createImageBitmap&&console.warn("THREE.ImageBitmapLoader: createImageBitmap() not supported."),"undefined"==typeof fetch&&console.warn("THREE.ImageBitmapLoader: fetch() not supported."),this.options={premultiplyAlpha:"none"}}setOptions(t){return this.options=t,this}load(t,e,s,i){void 0===t&&(t=""),void 0!==this.path&&(t=this.path+t),t=this.manager.resolveURL(t);const r=this,n=tc.get(t);if(void 0!==n)return r.manager.itemStart(t),n.then?void n.then((s=>{e&&e(s),r.manager.itemEnd(t)})).catch((t=>{i&&i(t)})):(setTimeout((function(){e&&e(n),r.manager.itemEnd(t)}),0),n);const o={};o.credentials="anonymous"===this.crossOrigin?"same-origin":"include",o.headers=this.requestHeader;const a=fetch(t,o).then((function(t){return t.blob()})).then((function(t){return createImageBitmap(t,Object.assign(r.options,{colorSpaceConversion:"none"}))})).then((function(s){return tc.add(t,s),e&&e(s),r.manager.itemEnd(t),s})).catch((function(e){i&&i(e),tc.remove(t),r.manager.itemError(t),r.manager.itemEnd(t)}));tc.add(t,a),r.manager.itemStart(t)}}let Uc;class Dc{static getContext(){return void 0===Uc&&(Uc=new(window.AudioContext||window.webkitAudioContext)),Uc}static setContext(t){Uc=t}}class Hc extends ic{constructor(t){super(t)}load(t,e,s,i){const r=this,n=new oc(this.manager);function o(e){i?i(e):console.error(e),r.manager.itemError(t)}n.setResponseType("arraybuffer"),n.setPath(this.path),n.setRequestHeader(this.requestHeader),n.setWithCredentials(this.withCredentials),n.load(t,(function(t){try{const s=t.slice(0);Dc.getContext().decodeAudioData(s,(function(t){e(t)})).catch(o)}catch(t){o(t)}}),s,i)}}const qc=new nr,Jc=new nr,Xc=new nr;class Yc{constructor(){this.type="StereoCamera",this.aspect=1,this.eyeSep=.064,this.cameraL=new $n,this.cameraL.layers.enable(1),this.cameraL.matrixAutoUpdate=!1,this.cameraR=new $n,this.cameraR.layers.enable(2),this.cameraR.matrixAutoUpdate=!1,this._cache={focus:null,fov:null,aspect:null,near:null,far:null,zoom:null,eyeSep:null}}update(t){const e=this._cache;if(e.focus!==t.focus||e.fov!==t.fov||e.aspect!==t.aspect*this.aspect||e.near!==t.near||e.far!==t.far||e.zoom!==t.zoom||e.eyeSep!==this.eyeSep){e.focus=t.focus,e.fov=t.fov,e.aspect=t.aspect*this.aspect,e.near=t.near,e.far=t.far,e.zoom=t.zoom,e.eyeSep=this.eyeSep,Xc.copy(t.projectionMatrix);const s=e.eyeSep/2,i=s*e.near/e.focus,r=e.near*Math.tan(Ws*e.fov*.5)/e.zoom;let n,o;Jc.elements[12]=-s,qc.elements[12]=s,n=-r*e.aspect+i,o=r*e.aspect+i,Xc.elements[0]=2*e.near/(o-n),Xc.elements[8]=(o+n)/(o-n),this.cameraL.projectionMatrix.copy(Xc),n=-r*e.aspect-i,o=r*e.aspect-i,Xc.elements[0]=2*e.near/(o-n),Xc.elements[8]=(o+n)/(o-n),this.cameraR.projectionMatrix.copy(Xc)}this.cameraL.matrixWorld.copy(t.matrixWorld).multiply(Jc),this.cameraR.matrixWorld.copy(t.matrixWorld).multiply(qc)}}class Zc extends $n{constructor(t=[]){super(),this.isArrayCamera=!0,this.cameras=t}}class Gc{constructor(t=!0){this.autoStart=t,this.startTime=0,this.oldTime=0,this.elapsedTime=0,this.running=!1}start(){this.startTime=$c(),this.oldTime=this.startTime,this.elapsedTime=0,this.running=!0}stop(){this.getElapsedTime(),this.running=!1,this.autoStart=!1}getElapsedTime(){return this.getDelta(),this.elapsedTime}getDelta(){let t=0;if(this.autoStart&&!this.running)return this.start(),0;if(this.running){const e=$c();t=(e-this.oldTime)/1e3,this.oldTime=e,this.elapsedTime+=t}return t}}function $c(){return performance.now()}const Qc=new Ii,Kc=new Ci,tu=new Ii,eu=new Ii;class su extends Rr{constructor(){super(),this.type="AudioListener",this.context=Dc.getContext(),this.gain=this.context.createGain(),this.gain.connect(this.context.destination),this.filter=null,this.timeDelta=0,this._clock=new Gc}getInput(){return this.gain}removeFilter(){return null!==this.filter&&(this.gain.disconnect(this.filter),this.filter.disconnect(this.context.destination),this.gain.connect(this.context.destination),this.filter=null),this}getFilter(){return this.filter}setFilter(t){return null!==this.filter?(this.gain.disconnect(this.filter),this.filter.disconnect(this.context.destination)):this.gain.disconnect(this.context.destination),this.filter=t,this.gain.connect(this.filter),this.filter.connect(this.context.destination),this}getMasterVolume(){return this.gain.gain.value}setMasterVolume(t){return this.gain.gain.setTargetAtTime(t,this.context.currentTime,.01),this}updateMatrixWorld(t){super.updateMatrixWorld(t);const e=this.context.listener,s=this.up;if(this.timeDelta=this._clock.getDelta(),this.matrixWorld.decompose(Qc,Kc,tu),eu.set(0,0,-1).applyQuaternion(Kc),e.positionX){const t=this.context.currentTime+this.timeDelta;e.positionX.linearRampToValueAtTime(Qc.x,t),e.positionY.linearRampToValueAtTime(Qc.y,t),e.positionZ.linearRampToValueAtTime(Qc.z,t),e.forwardX.linearRampToValueAtTime(eu.x,t),e.forwardY.linearRampToValueAtTime(eu.y,t),e.forwardZ.linearRampToValueAtTime(eu.z,t),e.upX.linearRampToValueAtTime(s.x,t),e.upY.linearRampToValueAtTime(s.y,t),e.upZ.linearRampToValueAtTime(s.z,t)}else e.setPosition(Qc.x,Qc.y,Qc.z),e.setOrientation(eu.x,eu.y,eu.z,s.x,s.y,s.z)}}class iu extends Rr{constructor(t){super(),this.type="Audio",this.listener=t,this.context=t.context,this.gain=this.context.createGain(),this.gain.connect(t.getInput()),this.autoplay=!1,this.buffer=null,this.detune=0,this.loop=!1,this.loopStart=0,this.loopEnd=0,this.offset=0,this.duration=void 0,this.playbackRate=1,this.isPlaying=!1,this.hasPlaybackControl=!0,this.source=null,this.sourceType="empty",this._startedAt=0,this._progress=0,this._connected=!1,this.filters=[]}getOutput(){return this.gain}setNodeSource(t){return this.hasPlaybackControl=!1,this.sourceType="audioNode",this.source=t,this.connect(),this}setMediaElementSource(t){return this.hasPlaybackControl=!1,this.sourceType="mediaNode",this.source=this.context.createMediaElementSource(t),this.connect(),this}setMediaStreamSource(t){return this.hasPlaybackControl=!1,this.sourceType="mediaStreamNode",this.source=this.context.createMediaStreamSource(t),this.connect(),this}setBuffer(t){return this.buffer=t,this.sourceType="buffer",this.autoplay&&this.play(),this}play(t=0){if(!0===this.isPlaying)return void console.warn("THREE.Audio: Audio is already playing.");if(!1===this.hasPlaybackControl)return void console.warn("THREE.Audio: this Audio has no playback control.");this._startedAt=this.context.currentTime+t;const e=this.context.createBufferSource();return e.buffer=this.buffer,e.loop=this.loop,e.loopStart=this.loopStart,e.loopEnd=this.loopEnd,e.onended=this.onEnded.bind(this),e.start(this._startedAt,this._progress+this.offset,this.duration),this.isPlaying=!0,this.source=e,this.setDetune(this.detune),this.setPlaybackRate(this.playbackRate),this.connect()}pause(){if(!1!==this.hasPlaybackControl)return!0===this.isPlaying&&(this._progress+=Math.max(this.context.currentTime-this._startedAt,0)*this.playbackRate,!0===this.loop&&(this._progress=this._progress%(this.duration||this.buffer.duration)),this.source.stop(),this.source.onended=null,this.isPlaying=!1),this;console.warn("THREE.Audio: this Audio has no playback control.")}stop(t=0){if(!1!==this.hasPlaybackControl)return this._progress=0,null!==this.source&&(this.source.stop(this.context.currentTime+t),this.source.onended=null),this.isPlaying=!1,this;console.warn("THREE.Audio: this Audio has no playback control.")}connect(){if(this.filters.length>0){this.source.connect(this.filters[0]);for(let t=1,e=this.filters.length;t<e;t++)this.filters[t-1].connect(this.filters[t]);this.filters[this.filters.length-1].connect(this.getOutput())}else this.source.connect(this.getOutput());return this._connected=!0,this}disconnect(){if(!1!==this._connected){if(this.filters.length>0){this.source.disconnect(this.filters[0]);for(let t=1,e=this.filters.length;t<e;t++)this.filters[t-1].disconnect(this.filters[t]);this.filters[this.filters.length-1].disconnect(this.getOutput())}else this.source.disconnect(this.getOutput());return this._connected=!1,this}}getFilters(){return this.filters}setFilters(t){return t||(t=[]),!0===this._connected?(this.disconnect(),this.filters=t.slice(),this.connect()):this.filters=t.slice(),this}setDetune(t){return this.detune=t,!0===this.isPlaying&&void 0!==this.source.detune&&this.source.detune.setTargetAtTime(this.detune,this.context.currentTime,.01),this}getDetune(){return this.detune}getFilter(){return this.getFilters()[0]}setFilter(t){return this.setFilters(t?[t]:[])}setPlaybackRate(t){if(!1!==this.hasPlaybackControl)return this.playbackRate=t,!0===this.isPlaying&&this.source.playbackRate.setTargetAtTime(this.playbackRate,this.context.currentTime,.01),this;console.warn("THREE.Audio: this Audio has no playback control.")}getPlaybackRate(){return this.playbackRate}onEnded(){this.isPlaying=!1,this._progress=0}getLoop(){return!1===this.hasPlaybackControl?(console.warn("THREE.Audio: this Audio has no playback control."),!1):this.loop}setLoop(t){if(!1!==this.hasPlaybackControl)return this.loop=t,!0===this.isPlaying&&(this.source.loop=this.loop),this;console.warn("THREE.Audio: this Audio has no playback control.")}setLoopStart(t){return this.loopStart=t,this}setLoopEnd(t){return this.loopEnd=t,this}getVolume(){return this.gain.gain.value}setVolume(t){return this.gain.gain.setTargetAtTime(t,this.context.currentTime,.01),this}copy(t,e){return super.copy(t,e),"buffer"!==t.sourceType?(console.warn("THREE.Audio: Audio source type cannot be copied."),this):(this.autoplay=t.autoplay,this.buffer=t.buffer,this.detune=t.detune,this.loop=t.loop,this.loopStart=t.loopStart,this.loopEnd=t.loopEnd,this.offset=t.offset,this.duration=t.duration,this.playbackRate=t.playbackRate,this.hasPlaybackControl=t.hasPlaybackControl,this.sourceType=t.sourceType,this.filters=t.filters.slice(),this)}clone(t){return new this.constructor(this.listener).copy(this,t)}}const ru=new Ii,nu=new Ci,ou=new Ii,au=new Ii;class hu extends iu{constructor(t){super(t),this.panner=this.context.createPanner(),this.panner.panningModel="HRTF",this.panner.connect(this.gain)}connect(){super.connect(),this.panner.connect(this.gain)}disconnect(){super.disconnect(),this.panner.disconnect(this.gain)}getOutput(){return this.panner}getRefDistance(){return this.panner.refDistance}setRefDistance(t){return this.panner.refDistance=t,this}getRolloffFactor(){return this.panner.rolloffFactor}setRolloffFactor(t){return this.panner.rolloffFactor=t,this}getDistanceModel(){return this.panner.distanceModel}setDistanceModel(t){return this.panner.distanceModel=t,this}getMaxDistance(){return this.panner.maxDistance}setMaxDistance(t){return this.panner.maxDistance=t,this}setDirectionalCone(t,e,s){return this.panner.coneInnerAngle=t,this.panner.coneOuterAngle=e,this.panner.coneOuterGain=s,this}updateMatrixWorld(t){if(super.updateMatrixWorld(t),!0===this.hasPlaybackControl&&!1===this.isPlaying)return;this.matrixWorld.decompose(ru,nu,ou),au.set(0,0,1).applyQuaternion(nu);const e=this.panner;if(e.positionX){const t=this.context.currentTime+this.listener.timeDelta;e.positionX.linearRampToValueAtTime(ru.x,t),e.positionY.linearRampToValueAtTime(ru.y,t),e.positionZ.linearRampToValueAtTime(ru.z,t),e.orientationX.linearRampToValueAtTime(au.x,t),e.orientationY.linearRampToValueAtTime(au.y,t),e.orientationZ.linearRampToValueAtTime(au.z,t)}else e.setPosition(ru.x,ru.y,ru.z),e.setOrientation(au.x,au.y,au.z)}}class lu{constructor(t,e=2048){this.analyser=t.context.createAnalyser(),this.analyser.fftSize=e,this.data=new Uint8Array(this.analyser.frequencyBinCount),t.getOutput().connect(this.analyser)}getFrequencyData(){return this.analyser.getByteFrequencyData(this.data),this.data}getAverageFrequency(){let t=0;const e=this.getFrequencyData();for(let s=0;s<e.length;s++)t+=e[s];return t/e.length}}class cu{constructor(t,e,s){let i,r,n;switch(this.binding=t,this.valueSize=s,e){case"quaternion":i=this._slerp,r=this._slerpAdditive,n=this._setAdditiveIdentityQuaternion,this.buffer=new Float64Array(6*s),this._workIndex=5;break;case"string":case"bool":i=this._select,r=this._select,n=this._setAdditiveIdentityOther,this.buffer=new Array(5*s);break;default:i=this._lerp,r=this._lerpAdditive,n=this._setAdditiveIdentityNumeric,this.buffer=new Float64Array(5*s)}this._mixBufferRegion=i,this._mixBufferRegionAdditive=r,this._setIdentity=n,this._origIndex=3,this._addIndex=4,this.cumulativeWeight=0,this.cumulativeWeightAdditive=0,this.useCount=0,this.referenceCount=0}accumulate(t,e){const s=this.buffer,i=this.valueSize,r=t*i+i;let n=this.cumulativeWeight;if(0===n){for(let t=0;t!==i;++t)s[r+t]=s[t];n=e}else{n+=e;const t=e/n;this._mixBufferRegion(s,r,0,t,i)}this.cumulativeWeight=n}accumulateAdditive(t){const e=this.buffer,s=this.valueSize,i=s*this._addIndex;0===this.cumulativeWeightAdditive&&this._setIdentity(),this._mixBufferRegionAdditive(e,i,0,t,s),this.cumulativeWeightAdditive+=t}apply(t){const e=this.valueSize,s=this.buffer,i=t*e+e,r=this.cumulativeWeight,n=this.cumulativeWeightAdditive,o=this.binding;if(this.cumulativeWeight=0,this.cumulativeWeightAdditive=0,r<1){const t=e*this._origIndex;this._mixBufferRegion(s,i,t,1-r,e)}n>0&&this._mixBufferRegionAdditive(s,i,this._addIndex*e,1,e);for(let t=e,r=e+e;t!==r;++t)if(s[t]!==s[t+e]){o.setValue(s,i);break}}saveOriginalState(){const t=this.binding,e=this.buffer,s=this.valueSize,i=s*this._origIndex;t.getValue(e,i);for(let t=s,r=i;t!==r;++t)e[t]=e[i+t%s];this._setIdentity(),this.cumulativeWeight=0,this.cumulativeWeightAdditive=0}restoreOriginalState(){const t=3*this.valueSize;this.binding.setValue(this.buffer,t)}_setAdditiveIdentityNumeric(){const t=this._addIndex*this.valueSize,e=t+this.valueSize;for(let s=t;s<e;s++)this.buffer[s]=0}_setAdditiveIdentityQuaternion(){this._setAdditiveIdentityNumeric(),this.buffer[this._addIndex*this.valueSize+3]=1}_setAdditiveIdentityOther(){const t=this._origIndex*this.valueSize,e=this._addIndex*this.valueSize;for(let s=0;s<this.valueSize;s++)this.buffer[e+s]=this.buffer[t+s]}_select(t,e,s,i,r){if(i>=.5)for(let i=0;i!==r;++i)t[e+i]=t[s+i]}_slerp(t,e,s,i){Ci.slerpFlat(t,e,t,e,t,s,i)}_slerpAdditive(t,e,s,i,r){const n=this._workIndex*r;Ci.multiplyQuaternionsFlat(t,n,t,e,t,s),Ci.slerpFlat(t,e,t,e,t,n,i)}_lerp(t,e,s,i,r){const n=1-i;for(let o=0;o!==r;++o){const r=e+o;t[r]=t[r]*n+t[s+o]*i}}_lerpAdditive(t,e,s,i,r){for(let n=0;n!==r;++n){const r=e+n;t[r]=t[r]+t[s+n]*i}}}const uu="\\[\\]\\.:\\/",du=new RegExp("["+uu+"]","g"),pu="[^"+uu+"]",mu="[^"+uu.replace("\\.","")+"]",yu=new RegExp("^"+/((?:WC+[\/:])*)/.source.replace("WC",pu)+/(WCOD+)?/.source.replace("WCOD",mu)+/(?:\.(WC+)(?:\[(.+)\])?)?/.source.replace("WC",pu)+/\.(WC+)(?:\[(.+)\])?/.source.replace("WC",pu)+"$"),fu=["material","materials","bones","map"];class gu{constructor(t,e,s){this.path=e,this.parsedPath=s||gu.parseTrackName(e),this.node=gu.findNode(t,this.parsedPath.nodeName),this.rootNode=t,this.getValue=this._getValue_unbound,this.setValue=this._setValue_unbound}static create(t,e,s){return t&&t.isAnimationObjectGroup?new gu.Composite(t,e,s):new gu(t,e,s)}static sanitizeNodeName(t){return t.replace(/\s/g,"_").replace(du,"")}static parseTrackName(t){const e=yu.exec(t);if(null===e)throw new Error("PropertyBinding: Cannot parse trackName: "+t);const s={nodeName:e[2],objectName:e[3],objectIndex:e[4],propertyName:e[5],propertyIndex:e[6]},i=s.nodeName&&s.nodeName.lastIndexOf(".");if(void 0!==i&&-1!==i){const t=s.nodeName.substring(i+1);-1!==fu.indexOf(t)&&(s.nodeName=s.nodeName.substring(0,i),s.objectName=t)}if(null===s.propertyName||0===s.propertyName.length)throw new Error("PropertyBinding: can not parse propertyName from trackName: "+t);return s}static findNode(t,e){if(void 0===e||""===e||"."===e||-1===e||e===t.name||e===t.uuid)return t;if(t.skeleton){const s=t.skeleton.getBoneByName(e);if(void 0!==s)return s}if(t.children){const s=function(t){for(let i=0;i<t.length;i++){const r=t[i];if(r.name===e||r.uuid===e)return r;const n=s(r.children);if(n)return n}return null},i=s(t.children);if(i)return i}return null}_getValue_unavailable(){}_setValue_unavailable(){}_getValue_direct(t,e){t[e]=this.targetObject[this.propertyName]}_getValue_array(t,e){const s=this.resolvedProperty;for(let i=0,r=s.length;i!==r;++i)t[e++]=s[i]}_getValue_arrayElement(t,e){t[e]=this.resolvedProperty[this.propertyIndex]}_getValue_toArray(t,e){this.resolvedProperty.toArray(t,e)}_setValue_direct(t,e){this.targetObject[this.propertyName]=t[e]}_setValue_direct_setNeedsUpdate(t,e){this.targetObject[this.propertyName]=t[e],this.targetObject.needsUpdate=!0}_setValue_direct_setMatrixWorldNeedsUpdate(t,e){this.targetObject[this.propertyName]=t[e],this.targetObject.matrixWorldNeedsUpdate=!0}_setValue_array(t,e){const s=this.resolvedProperty;for(let i=0,r=s.length;i!==r;++i)s[i]=t[e++]}_setValue_array_setNeedsUpdate(t,e){const s=this.resolvedProperty;for(let i=0,r=s.length;i!==r;++i)s[i]=t[e++];this.targetObject.needsUpdate=!0}_setValue_array_setMatrixWorldNeedsUpdate(t,e){const s=this.resolvedProperty;for(let i=0,r=s.length;i!==r;++i)s[i]=t[e++];this.targetObject.matrixWorldNeedsUpdate=!0}_setValue_arrayElement(t,e){this.resolvedProperty[this.propertyIndex]=t[e]}_setValue_arrayElement_setNeedsUpdate(t,e){this.resolvedProperty[this.propertyIndex]=t[e],this.targetObject.needsUpdate=!0}_setValue_arrayElement_setMatrixWorldNeedsUpdate(t,e){this.resolvedProperty[this.propertyIndex]=t[e],this.targetObject.matrixWorldNeedsUpdate=!0}_setValue_fromArray(t,e){this.resolvedProperty.fromArray(t,e)}_setValue_fromArray_setNeedsUpdate(t,e){this.resolvedProperty.fromArray(t,e),this.targetObject.needsUpdate=!0}_setValue_fromArray_setMatrixWorldNeedsUpdate(t,e){this.resolvedProperty.fromArray(t,e),this.targetObject.matrixWorldNeedsUpdate=!0}_getValue_unbound(t,e){this.bind(),this.getValue(t,e)}_setValue_unbound(t,e){this.bind(),this.setValue(t,e)}bind(){let t=this.node;const e=this.parsedPath,s=e.objectName,i=e.propertyName;let r=e.propertyIndex;if(t||(t=gu.findNode(this.rootNode,e.nodeName),this.node=t),this.getValue=this._getValue_unavailable,this.setValue=this._setValue_unavailable,!t)return void console.warn("THREE.PropertyBinding: No target node found for track: "+this.path+".");if(s){let i=e.objectIndex;switch(s){case"materials":if(!t.material)return void console.error("THREE.PropertyBinding: Can not bind to material as node does not have a material.",this);if(!t.material.materials)return void console.error("THREE.PropertyBinding: Can not bind to material.materials as node.material does not have a materials array.",this);t=t.material.materials;break;case"bones":if(!t.skeleton)return void console.error("THREE.PropertyBinding: Can not bind to bones as node does not have a skeleton.",this);t=t.skeleton.bones;for(let e=0;e<t.length;e++)if(t[e].name===i){i=e;break}break;case"map":if("map"in t){t=t.map;break}if(!t.material)return void console.error("THREE.PropertyBinding: Can not bind to material as node does not have a material.",this);if(!t.material.map)return void console.error("THREE.PropertyBinding: Can not bind to material.map as node.material does not have a map.",this);t=t.material.map;break;default:if(void 0===t[s])return void console.error("THREE.PropertyBinding: Can not bind to objectName of node undefined.",this);t=t[s]}if(void 0!==i){if(void 0===t[i])return void console.error("THREE.PropertyBinding: Trying to bind to objectIndex of objectName, but is undefined.",this,t);t=t[i]}}const n=t[i];if(void 0===n){const s=e.nodeName;return void console.error("THREE.PropertyBinding: Trying to update property for track: "+s+"."+i+" but it wasn't found.",t)}let o=this.Versioning.None;this.targetObject=t,!0===t.isMaterial?o=this.Versioning.NeedsUpdate:!0===t.isObject3D&&(o=this.Versioning.MatrixWorldNeedsUpdate);let a=this.BindingType.Direct;if(void 0!==r){if("morphTargetInfluences"===i){if(!t.geometry)return void console.error("THREE.PropertyBinding: Can not bind to morphTargetInfluences because node does not have a geometry.",this);if(!t.geometry.morphAttributes)return void console.error("THREE.PropertyBinding: Can not bind to morphTargetInfluences because node does not have a geometry.morphAttributes.",this);void 0!==t.morphTargetDictionary[r]&&(r=t.morphTargetDictionary[r])}a=this.BindingType.ArrayElement,this.resolvedProperty=n,this.propertyIndex=r}else void 0!==n.fromArray&&void 0!==n.toArray?(a=this.BindingType.HasFromToArray,this.resolvedProperty=n):Array.isArray(n)?(a=this.BindingType.EntireArray,this.resolvedProperty=n):this.propertyName=i;this.getValue=this.GetterByBindingType[a],this.setValue=this.SetterByBindingTypeAndVersioning[a][o]}unbind(){this.node=null,this.getValue=this._getValue_unbound,this.setValue=this._setValue_unbound}}gu.Composite=class{constructor(t,e,s){const i=s||gu.parseTrackName(e);this._targetGroup=t,this._bindings=t.subscribe_(e,i)}getValue(t,e){this.bind();const s=this._targetGroup.nCachedObjects_,i=this._bindings[s];void 0!==i&&i.getValue(t,e)}setValue(t,e){const s=this._bindings;for(let i=this._targetGroup.nCachedObjects_,r=s.length;i!==r;++i)s[i].setValue(t,e)}bind(){const t=this._bindings;for(let e=this._targetGroup.nCachedObjects_,s=t.length;e!==s;++e)t[e].bind()}unbind(){const t=this._bindings;for(let e=this._targetGroup.nCachedObjects_,s=t.length;e!==s;++e)t[e].unbind()}},gu.prototype.BindingType={Direct:0,EntireArray:1,ArrayElement:2,HasFromToArray:3},gu.prototype.Versioning={None:0,NeedsUpdate:1,MatrixWorldNeedsUpdate:2},gu.prototype.GetterByBindingType=[gu.prototype._getValue_direct,gu.prototype._getValue_array,gu.prototype._getValue_arrayElement,gu.prototype._getValue_toArray],gu.prototype.SetterByBindingTypeAndVersioning=[[gu.prototype._setValue_direct,gu.prototype._setValue_direct_setNeedsUpdate,gu.prototype._setValue_direct_setMatrixWorldNeedsUpdate],[gu.prototype._setValue_array,gu.prototype._setValue_array_setNeedsUpdate,gu.prototype._setValue_array_setMatrixWorldNeedsUpdate],[gu.prototype._setValue_arrayElement,gu.prototype._setValue_arrayElement_setNeedsUpdate,gu.prototype._setValue_arrayElement_setMatrixWorldNeedsUpdate],[gu.prototype._setValue_fromArray,gu.prototype._setValue_fromArray_setNeedsUpdate,gu.prototype._setValue_fromArray_setMatrixWorldNeedsUpdate]];class xu{constructor(){this.isAnimationObjectGroup=!0,this.uuid=Us(),this._objects=Array.prototype.slice.call(arguments),this.nCachedObjects_=0;const t={};this._indicesByUUID=t;for(let e=0,s=arguments.length;e!==s;++e)t[arguments[e].uuid]=e;this._paths=[],this._parsedPaths=[],this._bindings=[],this._bindingsIndicesByPath={};const e=this;this.stats={objects:{get total(){return e._objects.length},get inUse(){return this.total-e.nCachedObjects_}},get bindingsPerObject(){return e._bindings.length}}}add(){const t=this._objects,e=this._indicesByUUID,s=this._paths,i=this._parsedPaths,r=this._bindings,n=r.length;let o,a=t.length,h=this.nCachedObjects_;for(let l=0,c=arguments.length;l!==c;++l){const c=arguments[l],u=c.uuid;let d=e[u];if(void 0===d){d=a++,e[u]=d,t.push(c);for(let t=0,e=n;t!==e;++t)r[t].push(new gu(c,s[t],i[t]))}else if(d<h){o=t[d];const a=--h,l=t[a];e[l.uuid]=d,t[d]=l,e[u]=a,t[a]=c;for(let t=0,e=n;t!==e;++t){const e=r[t],n=e[a];let o=e[d];e[d]=n,void 0===o&&(o=new gu(c,s[t],i[t])),e[a]=o}}else t[d]!==o&&console.error("THREE.AnimationObjectGroup: Different objects with the same UUID detected. Clean the caches or recreate your infrastructure when reloading scenes.")}this.nCachedObjects_=h}remove(){const t=this._objects,e=this._indicesByUUID,s=this._bindings,i=s.length;let r=this.nCachedObjects_;for(let n=0,o=arguments.length;n!==o;++n){const o=arguments[n],a=o.uuid,h=e[a];if(void 0!==h&&h>=r){const n=r++,l=t[n];e[l.uuid]=h,t[h]=l,e[a]=n,t[n]=o;for(let t=0,e=i;t!==e;++t){const e=s[t],i=e[n],r=e[h];e[h]=i,e[n]=r}}}this.nCachedObjects_=r}uncache(){const t=this._objects,e=this._indicesByUUID,s=this._bindings,i=s.length;let r=this.nCachedObjects_,n=t.length;for(let o=0,a=arguments.length;o!==a;++o){const a=arguments[o].uuid,h=e[a];if(void 0!==h)if(delete e[a],h<r){const o=--r,a=t[o],l=--n,c=t[l];e[a.uuid]=h,t[h]=a,e[c.uuid]=o,t[o]=c,t.pop();for(let t=0,e=i;t!==e;++t){const e=s[t],i=e[o],r=e[l];e[h]=i,e[o]=r,e.pop()}}else{const r=--n,o=t[r];r>0&&(e[o.uuid]=h),t[h]=o,t.pop();for(let t=0,e=i;t!==e;++t){const e=s[t];e[h]=e[r],e.pop()}}}this.nCachedObjects_=r}subscribe_(t,e){const s=this._bindingsIndicesByPath;let i=s[t];const r=this._bindings;if(void 0!==i)return r[i];const n=this._paths,o=this._parsedPaths,a=this._objects,h=a.length,l=this.nCachedObjects_,c=new Array(h);i=r.length,s[t]=i,n.push(t),o.push(e),r.push(c);for(let s=l,i=a.length;s!==i;++s){const i=a[s];c[s]=new gu(i,t,e)}return c}unsubscribe_(t){const e=this._bindingsIndicesByPath,s=e[t];if(void 0!==s){const i=this._paths,r=this._parsedPaths,n=this._bindings,o=n.length-1,a=n[o];e[t[o]]=s,n[s]=a,n.pop(),r[s]=r[o],r.pop(),i[s]=i[o],i.pop()}}}class bu{constructor(t,e,s=null,i=e.blendMode){this._mixer=t,this._clip=e,this._localRoot=s,this.blendMode=i;const r=e.tracks,n=r.length,o=new Array(n),a={endingStart:Oe,endingEnd:Oe};for(let t=0;t!==n;++t){const e=r[t].createInterpolant(null);o[t]=e,e.settings=a}this._interpolantSettings=a,this._interpolants=o,this._propertyBindings=new Array(n),this._cacheIndex=null,this._byClipCacheIndex=null,this._timeScaleInterpolant=null,this._weightInterpolant=null,this.loop=2201,this._loopCount=-1,this._startTime=null,this.time=0,this.timeScale=1,this._effectiveTimeScale=1,this.weight=1,this._effectiveWeight=1,this.repetitions=1/0,this.paused=!1,this.enabled=!0,this.clampWhenFinished=!1,this.zeroSlopeAtStart=!0,this.zeroSlopeAtEnd=!0}play(){return this._mixer._activateAction(this),this}stop(){return this._mixer._deactivateAction(this),this.reset()}reset(){return this.paused=!1,this.enabled=!0,this.time=0,this._loopCount=-1,this._startTime=null,this.stopFading().stopWarping()}isRunning(){return this.enabled&&!this.paused&&0!==this.timeScale&&null===this._startTime&&this._mixer._isActiveAction(this)}isScheduled(){return this._mixer._isActiveAction(this)}startAt(t){return this._startTime=t,this}setLoop(t,e){return this.loop=t,this.repetitions=e,this}setEffectiveWeight(t){return this.weight=t,this._effectiveWeight=this.enabled?t:0,this.stopFading()}getEffectiveWeight(){return this._effectiveWeight}fadeIn(t){return this._scheduleFading(t,0,1)}fadeOut(t){return this._scheduleFading(t,1,0)}crossFadeFrom(t,e,s){if(t.fadeOut(e),this.fadeIn(e),s){const s=this._clip.duration,i=t._clip.duration,r=i/s,n=s/i;t.warp(1,r,e),this.warp(n,1,e)}return this}crossFadeTo(t,e,s){return t.crossFadeFrom(this,e,s)}stopFading(){const t=this._weightInterpolant;return null!==t&&(this._weightInterpolant=null,this._mixer._takeBackControlInterpolant(t)),this}setEffectiveTimeScale(t){return this.timeScale=t,this._effectiveTimeScale=this.paused?0:t,this.stopWarping()}getEffectiveTimeScale(){return this._effectiveTimeScale}setDuration(t){return this.timeScale=this._clip.duration/t,this.stopWarping()}syncWith(t){return this.time=t.time,this.timeScale=t.timeScale,this.stopWarping()}halt(t){return this.warp(this._effectiveTimeScale,0,t)}warp(t,e,s){const i=this._mixer,r=i.time,n=this.timeScale;let o=this._timeScaleInterpolant;null===o&&(o=i._lendControlInterpolant(),this._timeScaleInterpolant=o);const a=o.parameterPositions,h=o.sampleValues;return a[0]=r,a[1]=r+s,h[0]=t/n,h[1]=e/n,this}stopWarping(){const t=this._timeScaleInterpolant;return null!==t&&(this._timeScaleInterpolant=null,this._mixer._takeBackControlInterpolant(t)),this}getMixer(){return this._mixer}getClip(){return this._clip}getRoot(){return this._localRoot||this._mixer._root}_update(t,e,s,i){if(!this.enabled)return void this._updateWeight(t);const r=this._startTime;if(null!==r){const i=(t-r)*s;i<0||0===s?e=0:(this._startTime=null,e=s*i)}e*=this._updateTimeScale(t);const n=this._updateTime(e),o=this._updateWeight(t);if(o>0){const t=this._interpolants,e=this._propertyBindings;if(this.blendMode===Ve)for(let s=0,i=t.length;s!==i;++s)t[s].evaluate(n),e[s].accumulateAdditive(o);else for(let s=0,r=t.length;s!==r;++s)t[s].evaluate(n),e[s].accumulate(i,o)}}_updateWeight(t){let e=0;if(this.enabled){e=this.weight;const s=this._weightInterpolant;if(null!==s){const i=s.evaluate(t)[0];e*=i,t>s.parameterPositions[1]&&(this.stopFading(),0===i&&(this.enabled=!1))}}return this._effectiveWeight=e,e}_updateTimeScale(t){let e=0;if(!this.paused){e=this.timeScale;const s=this._timeScaleInterpolant;if(null!==s){e*=s.evaluate(t)[0],t>s.parameterPositions[1]&&(this.stopWarping(),0===e?this.paused=!0:this.timeScale=e)}}return this._effectiveTimeScale=e,e}_updateTime(t){const e=this._clip.duration,s=this.loop;let i=this.time+t,r=this._loopCount;const n=2202===s;if(0===t)return-1===r||!n||1&~r?i:e-i;if(2200===s){-1===r&&(this._loopCount=0,this._setEndings(!0,!0,!1));t:{if(i>=e)i=e;else{if(!(i<0)){this.time=i;break t}i=0}this.clampWhenFinished?this.paused=!0:this.enabled=!1,this.time=i,this._mixer.dispatchEvent({type:"finished",action:this,direction:t<0?-1:1})}}else{if(-1===r&&(t>=0?(r=0,this._setEndings(!0,0===this.repetitions,n)):this._setEndings(0===this.repetitions,!0,n)),i>=e||i<0){const s=Math.floor(i/e);i-=e*s,r+=Math.abs(s);const o=this.repetitions-r;if(o<=0)this.clampWhenFinished?this.paused=!0:this.enabled=!1,i=t>0?e:0,this.time=i,this._mixer.dispatchEvent({type:"finished",action:this,direction:t>0?1:-1});else{if(1===o){const e=t<0;this._setEndings(e,!e,n)}else this._setEndings(!1,!1,n);this._loopCount=r,this.time=i,this._mixer.dispatchEvent({type:"loop",action:this,loopDelta:s})}}else this.time=i;if(n&&!(1&~r))return e-i}return i}_setEndings(t,e,s){const i=this._interpolantSettings;s?(i.endingStart=Fe,i.endingEnd=Fe):(i.endingStart=t?this.zeroSlopeAtStart?Fe:Oe:Ne,i.endingEnd=e?this.zeroSlopeAtEnd?Fe:Oe:Ne)}_scheduleFading(t,e,s){const i=this._mixer,r=i.time;let n=this._weightInterpolant;null===n&&(n=i._lendControlInterpolant(),this._weightInterpolant=n);const o=n.parameterPositions,a=n.sampleValues;return o[0]=r,a[0]=e,o[1]=r+t,a[1]=s,this}}const vu=new Float32Array(1);class wu extends Ns{constructor(t){super(),this._root=t,this._initMemoryManager(),this._accuIndex=0,this.time=0,this.timeScale=1}_bindAction(t,e){const s=t._localRoot||this._root,i=t._clip.tracks,r=i.length,n=t._propertyBindings,o=t._interpolants,a=s.uuid,h=this._bindingsByRootAndName;let l=h[a];void 0===l&&(l={},h[a]=l);for(let t=0;t!==r;++t){const r=i[t],h=r.name;let c=l[h];if(void 0!==c)++c.referenceCount,n[t]=c;else{if(c=n[t],void 0!==c){null===c._cacheIndex&&(++c.referenceCount,this._addInactiveBinding(c,a,h));continue}const i=e&&e._propertyBindings[t].binding.parsedPath;c=new cu(gu.create(s,h,i),r.ValueTypeName,r.getValueSize()),++c.referenceCount,this._addInactiveBinding(c,a,h),n[t]=c}o[t].resultBuffer=c.buffer}}_activateAction(t){if(!this._isActiveAction(t)){if(null===t._cacheIndex){const e=(t._localRoot||this._root).uuid,s=t._clip.uuid,i=this._actionsByClip[s];this._bindAction(t,i&&i.knownActions[0]),this._addInactiveAction(t,s,e)}const e=t._propertyBindings;for(let t=0,s=e.length;t!==s;++t){const s=e[t];0==s.useCount++&&(this._lendBinding(s),s.saveOriginalState())}this._lendAction(t)}}_deactivateAction(t){if(this._isActiveAction(t)){const e=t._propertyBindings;for(let t=0,s=e.length;t!==s;++t){const s=e[t];0==--s.useCount&&(s.restoreOriginalState(),this._takeBackBinding(s))}this._takeBackAction(t)}}_initMemoryManager(){this._actions=[],this._nActiveActions=0,this._actionsByClip={},this._bindings=[],this._nActiveBindings=0,this._bindingsByRootAndName={},this._controlInterpolants=[],this._nActiveControlInterpolants=0;const t=this;this.stats={actions:{get total(){return t._actions.length},get inUse(){return t._nActiveActions}},bindings:{get total(){return t._bindings.length},get inUse(){return t._nActiveBindings}},controlInterpolants:{get total(){return t._controlInterpolants.length},get inUse(){return t._nActiveControlInterpolants}}}}_isActiveAction(t){const e=t._cacheIndex;return null!==e&&e<this._nActiveActions}_addInactiveAction(t,e,s){const i=this._actions,r=this._actionsByClip;let n=r[e];if(void 0===n)n={knownActions:[t],actionByRoot:{}},t._byClipCacheIndex=0,r[e]=n;else{const e=n.knownActions;t._byClipCacheIndex=e.length,e.push(t)}t._cacheIndex=i.length,i.push(t),n.actionByRoot[s]=t}_removeInactiveAction(t){const e=this._actions,s=e[e.length-1],i=t._cacheIndex;s._cacheIndex=i,e[i]=s,e.pop(),t._cacheIndex=null;const r=t._clip.uuid,n=this._actionsByClip,o=n[r],a=o.knownActions,h=a[a.length-1],l=t._byClipCacheIndex;h._byClipCacheIndex=l,a[l]=h,a.pop(),t._byClipCacheIndex=null;delete o.actionByRoot[(t._localRoot||this._root).uuid],0===a.length&&delete n[r],this._removeInactiveBindingsForAction(t)}_removeInactiveBindingsForAction(t){const e=t._propertyBindings;for(let t=0,s=e.length;t!==s;++t){const s=e[t];0==--s.referenceCount&&this._removeInactiveBinding(s)}}_lendAction(t){const e=this._actions,s=t._cacheIndex,i=this._nActiveActions++,r=e[i];t._cacheIndex=i,e[i]=t,r._cacheIndex=s,e[s]=r}_takeBackAction(t){const e=this._actions,s=t._cacheIndex,i=--this._nActiveActions,r=e[i];t._cacheIndex=i,e[i]=t,r._cacheIndex=s,e[s]=r}_addInactiveBinding(t,e,s){const i=this._bindingsByRootAndName,r=this._bindings;let n=i[e];void 0===n&&(n={},i[e]=n),n[s]=t,t._cacheIndex=r.length,r.push(t)}_removeInactiveBinding(t){const e=this._bindings,s=t.binding,i=s.rootNode.uuid,r=s.path,n=this._bindingsByRootAndName,o=n[i],a=e[e.length-1],h=t._cacheIndex;a._cacheIndex=h,e[h]=a,e.pop(),delete o[r],0===Object.keys(o).length&&delete n[i]}_lendBinding(t){const e=this._bindings,s=t._cacheIndex,i=this._nActiveBindings++,r=e[i];t._cacheIndex=i,e[i]=t,r._cacheIndex=s,e[s]=r}_takeBackBinding(t){const e=this._bindings,s=t._cacheIndex,i=--this._nActiveBindings,r=e[i];t._cacheIndex=i,e[i]=t,r._cacheIndex=s,e[s]=r}_lendControlInterpolant(){const t=this._controlInterpolants,e=this._nActiveControlInterpolants++;let s=t[e];return void 0===s&&(s=new Ul(new Float32Array(2),new Float32Array(2),1,vu),s.__cacheIndex=e,t[e]=s),s}_takeBackControlInterpolant(t){const e=this._controlInterpolants,s=t.__cacheIndex,i=--this._nActiveControlInterpolants,r=e[i];t.__cacheIndex=i,e[i]=t,r.__cacheIndex=s,e[s]=r}clipAction(t,e,s){const i=e||this._root,r=i.uuid;let n="string"==typeof t?Ql.findByName(i,t):t;const o=null!==n?n.uuid:t,a=this._actionsByClip[o];let h=null;if(void 0===s&&(s=null!==n?n.blendMode:Le),void 0!==a){const t=a.actionByRoot[r];if(void 0!==t&&t.blendMode===s)return t;h=a.knownActions[0],null===n&&(n=h._clip)}if(null===n)return null;const l=new bu(this,n,e,s);return this._bindAction(l,h),this._addInactiveAction(l,o,r),l}existingAction(t,e){const s=e||this._root,i=s.uuid,r="string"==typeof t?Ql.findByName(s,t):t,n=r?r.uuid:t,o=this._actionsByClip[n];return void 0!==o&&o.actionByRoot[i]||null}stopAllAction(){const t=this._actions;for(let e=this._nActiveActions-1;e>=0;--e)t[e].stop();return this}update(t){t*=this.timeScale;const e=this._actions,s=this._nActiveActions,i=this.time+=t,r=Math.sign(t),n=this._accuIndex^=1;for(let o=0;o!==s;++o){e[o]._update(i,t,r,n)}const o=this._bindings,a=this._nActiveBindings;for(let t=0;t!==a;++t)o[t].apply(n);return this}setTime(t){this.time=0;for(let t=0;t<this._actions.length;t++)this._actions[t].time=0;return this.update(t)}getRoot(){return this._root}uncacheClip(t){const e=this._actions,s=t.uuid,i=this._actionsByClip,r=i[s];if(void 0!==r){const t=r.knownActions;for(let s=0,i=t.length;s!==i;++s){const i=t[s];this._deactivateAction(i);const r=i._cacheIndex,n=e[e.length-1];i._cacheIndex=null,i._byClipCacheIndex=null,n._cacheIndex=r,e[r]=n,e.pop(),this._removeInactiveBindingsForAction(i)}delete i[s]}}uncacheRoot(t){const e=t.uuid,s=this._actionsByClip;for(const t in s){const i=s[t].actionByRoot[e];void 0!==i&&(this._deactivateAction(i),this._removeInactiveAction(i))}const i=this._bindingsByRootAndName[e];if(void 0!==i)for(const t in i){const e=i[t];e.restoreOriginalState(),this._removeInactiveBinding(e)}}uncacheAction(t,e){const s=this.existingAction(t,e);null!==s&&(this._deactivateAction(s),this._removeInactiveAction(s))}}class Mu extends Mi{constructor(t=1,e=1,s=1,i={}){super(t,e,i),this.isRenderTarget3D=!0,this.depth=s,this.texture=new Ti(null,t,e,s),this.texture.isRenderTargetTexture=!0}}class Su extends Mi{constructor(t=1,e=1,s=1,i={}){super(t,e,i),this.isRenderTargetArray=!0,this.depth=s,this.texture=new _i(null,t,e,s),this.texture.isRenderTargetTexture=!0}}class _u{constructor(t){this.value=t}clone(){return new _u(void 0===this.value.clone?this.value:this.value.clone())}}let Au=0;class Tu extends Ns{constructor(){super(),this.isUniformsGroup=!0,Object.defineProperty(this,"id",{value:Au++}),this.name="",this.usage=_s,this.uniforms=[]}add(t){return this.uniforms.push(t),this}remove(t){const e=this.uniforms.indexOf(t);return-1!==e&&this.uniforms.splice(e,1),this}setName(t){return this.name=t,this}setUsage(t){return this.usage=t,this}dispose(){return this.dispatchEvent({type:"dispose"}),this}copy(t){this.name=t.name,this.usage=t.usage;const e=t.uniforms;this.uniforms.length=0;for(let t=0,s=e.length;t<s;t++){const s=Array.isArray(e[t])?e[t]:[e[t]];for(let t=0;t<s.length;t++)this.uniforms.push(s[t].clone())}return this}clone(){return(new this.constructor).copy(this)}}class zu extends no{constructor(t,e,s=1){super(t,e),this.isInstancedInterleavedBuffer=!0,this.meshPerAttribute=s}copy(t){return super.copy(t),this.meshPerAttribute=t.meshPerAttribute,this}clone(t){const e=super.clone(t);return e.meshPerAttribute=this.meshPerAttribute,e}toJSON(t){const e=super.toJSON(t);return e.isInstancedInterleavedBuffer=!0,e.meshPerAttribute=this.meshPerAttribute,e}}class Cu{constructor(t,e,s,i,r){this.isGLBufferAttribute=!0,this.name="",this.buffer=t,this.type=e,this.itemSize=s,this.elementSize=i,this.count=r,this.version=0}set needsUpdate(t){!0===t&&this.version++}setBuffer(t){return this.buffer=t,this}setType(t,e){return this.type=t,this.elementSize=e,this}setItemSize(t){return this.itemSize=t,this}setCount(t){return this.count=t,this}}const Iu=new nr;class Bu{constructor(t,e,s=0,i=1/0){this.ray=new rr(t,e),this.near=s,this.far=i,this.camera=null,this.layers=new fr,this.params={Mesh:{},Line:{threshold:1},LOD:{},Points:{threshold:1},Sprite:{}}}set(t,e){this.ray.set(t,e)}setFromCamera(t,e){e.isPerspectiveCamera?(this.ray.origin.setFromMatrixPosition(e.matrixWorld),this.ray.direction.set(t.x,t.y,.5).unproject(e).sub(this.ray.origin).normalize(),this.camera=e):e.isOrthographicCamera?(this.ray.origin.set(t.x,t.y,(e.near+e.far)/(e.near-e.far)).unproject(e),this.ray.direction.set(0,0,-1).transformDirection(e.matrixWorld),this.camera=e):console.error("THREE.Raycaster: Unsupported camera type: "+e.type)}setFromXRController(t){return Iu.identity().extractRotation(t.matrixWorld),this.ray.origin.setFromMatrixPosition(t.matrixWorld),this.ray.direction.set(0,0,-1).applyMatrix4(Iu),this}intersectObject(t,e=!0,s=[]){return Ru(t,this,s,e),s.sort(ku),s}intersectObjects(t,e=!0,s=[]){for(let i=0,r=t.length;i<r;i++)Ru(t[i],this,s,e);return s.sort(ku),s}}function ku(t,e){return t.distance-e.distance}function Ru(t,e,s,i){let r=!0;if(t.layers.test(e.layers)){!1===t.raycast(e,s)&&(r=!1)}if(!0===r&&!0===i){const i=t.children;for(let t=0,r=i.length;t<r;t++)Ru(i[t],e,s,!0)}}class Eu{constructor(t=1,e=0,s=0){return this.radius=t,this.phi=e,this.theta=s,this}set(t,e,s){return this.radius=t,this.phi=e,this.theta=s,this}copy(t){return this.radius=t.radius,this.phi=t.phi,this.theta=t.theta,this}makeSafe(){const t=1e-6;return this.phi=Ds(this.phi,t,Math.PI-t),this}setFromVector3(t){return this.setFromCartesianCoords(t.x,t.y,t.z)}setFromCartesianCoords(t,e,s){return this.radius=Math.sqrt(t*t+e*e+s*s),0===this.radius?(this.theta=0,this.phi=0):(this.theta=Math.atan2(t,s),this.phi=Math.acos(Ds(e/this.radius,-1,1))),this}clone(){return(new this.constructor).copy(this)}}class Pu{constructor(t=1,e=0,s=0){return this.radius=t,this.theta=e,this.y=s,this}set(t,e,s){return this.radius=t,this.theta=e,this.y=s,this}copy(t){return this.radius=t.radius,this.theta=t.theta,this.y=t.y,this}setFromVector3(t){return this.setFromCartesianCoords(t.x,t.y,t.z)}setFromCartesianCoords(t,e,s){return this.radius=Math.sqrt(t*t+s*s),this.theta=Math.atan2(t,s),this.y=e,this}clone(){return(new this.constructor).copy(this)}}class Ou{constructor(t,e,s,i){Ou.prototype.isMatrix2=!0,this.elements=[1,0,0,1],void 0!==t&&this.set(t,e,s,i)}identity(){return this.set(1,0,0,1),this}fromArray(t,e=0){for(let s=0;s<4;s++)this.elements[s]=t[s+e];return this}set(t,e,s,i){const r=this.elements;return r[0]=t,r[2]=e,r[1]=s,r[3]=i,this}}const Fu=new Zs;class Nu{constructor(t=new Zs(1/0,1/0),e=new Zs(-1/0,-1/0)){this.isBox2=!0,this.min=t,this.max=e}set(t,e){return this.min.copy(t),this.max.copy(e),this}setFromPoints(t){this.makeEmpty();for(let e=0,s=t.length;e<s;e++)this.expandByPoint(t[e]);return this}setFromCenterAndSize(t,e){const s=Fu.copy(e).multiplyScalar(.5);return this.min.copy(t).sub(s),this.max.copy(t).add(s),this}clone(){return(new this.constructor).copy(this)}copy(t){return this.min.copy(t.min),this.max.copy(t.max),this}makeEmpty(){return this.min.x=this.min.y=1/0,this.max.x=this.max.y=-1/0,this}isEmpty(){return this.max.x<this.min.x||this.max.y<this.min.y}getCenter(t){return this.isEmpty()?t.set(0,0):t.addVectors(this.min,this.max).multiplyScalar(.5)}getSize(t){return this.isEmpty()?t.set(0,0):t.subVectors(this.max,this.min)}expandByPoint(t){return this.min.min(t),this.max.max(t),this}expandByVector(t){return this.min.sub(t),this.max.add(t),this}expandByScalar(t){return this.min.addScalar(-t),this.max.addScalar(t),this}containsPoint(t){return t.x>=this.min.x&&t.x<=this.max.x&&t.y>=this.min.y&&t.y<=this.max.y}containsBox(t){return this.min.x<=t.min.x&&t.max.x<=this.max.x&&this.min.y<=t.min.y&&t.max.y<=this.max.y}getParameter(t,e){return e.set((t.x-this.min.x)/(this.max.x-this.min.x),(t.y-this.min.y)/(this.max.y-this.min.y))}intersectsBox(t){return t.max.x>=this.min.x&&t.min.x<=this.max.x&&t.max.y>=this.min.y&&t.min.y<=this.max.y}clampPoint(t,e){return e.copy(t).clamp(this.min,this.max)}distanceToPoint(t){return this.clampPoint(t,Fu).distanceTo(t)}intersect(t){return this.min.max(t.min),this.max.min(t.max),this.isEmpty()&&this.makeEmpty(),this}union(t){return this.min.min(t.min),this.max.max(t.max),this}translate(t){return this.min.add(t),this.max.add(t),this}equals(t){return t.min.equals(this.min)&&t.max.equals(this.max)}}const Lu=new Ii,Vu=new Ii;class Wu{constructor(t=new Ii,e=new Ii){this.start=t,this.end=e}set(t,e){return this.start.copy(t),this.end.copy(e),this}copy(t){return this.start.copy(t.start),this.end.copy(t.end),this}getCenter(t){return t.addVectors(this.start,this.end).multiplyScalar(.5)}delta(t){return t.subVectors(this.end,this.start)}distanceSq(){return this.start.distanceToSquared(this.end)}distance(){return this.start.distanceTo(this.end)}at(t,e){return this.delta(e).multiplyScalar(t).add(this.start)}closestPointToPointParameter(t,e){Lu.subVectors(t,this.start),Vu.subVectors(this.end,this.start);const s=Vu.dot(Vu);let i=Vu.dot(Lu)/s;return e&&(i=Ds(i,0,1)),i}closestPointToPoint(t,e,s){const i=this.closestPointToPointParameter(t,e);return this.delta(s).multiplyScalar(i).add(this.start)}applyMatrix4(t){return this.start.applyMatrix4(t),this.end.applyMatrix4(t),this}equals(t){return t.start.equals(this.start)&&t.end.equals(this.end)}clone(){return(new this.constructor).copy(this)}}const ju=new Ii;class Uu extends Rr{constructor(t,e){super(),this.light=t,this.matrixAutoUpdate=!1,this.color=e,this.type="SpotLightHelper";const s=new zn,i=[0,0,0,0,0,1,0,0,0,1,0,1,0,0,0,-1,0,1,0,0,0,0,1,1,0,0,0,0,-1,1];for(let t=0,e=1,s=32;t<s;t++,e++){const r=t/s*Math.PI*2,n=e/s*Math.PI*2;i.push(Math.cos(r),Math.sin(r),1,Math.cos(n),Math.sin(n),1)}s.setAttribute("position",new bn(i,3));const r=new Sa({fog:!1,toneMapped:!1});this.cone=new Oa(s,r),this.add(this.cone),this.update()}dispose(){this.cone.geometry.dispose(),this.cone.material.dispose()}update(){this.light.updateWorldMatrix(!0,!1),this.light.target.updateWorldMatrix(!0,!1),this.parent?(this.parent.updateWorldMatrix(!0),this.matrix.copy(this.parent.matrixWorld).invert().multiply(this.light.matrixWorld)):this.matrix.copy(this.light.matrixWorld),this.matrixWorld.copy(this.light.matrixWorld);const t=this.light.distance?this.light.distance:1e3,e=t*Math.tan(this.light.angle);this.cone.scale.set(e,e,t),ju.setFromMatrixPosition(this.light.target.matrixWorld),this.cone.lookAt(ju),void 0!==this.color?this.cone.material.color.set(this.color):this.cone.material.color.copy(this.light.color)}}const Du=new Ii,Hu=new nr,qu=new nr;class Ju extends Oa{constructor(t){const e=Xu(t),s=new zn,i=[],r=[],n=new $r(0,0,1),o=new $r(0,1,0);for(let t=0;t<e.length;t++){const s=e[t];s.parent&&s.parent.isBone&&(i.push(0,0,0),i.push(0,0,0),r.push(n.r,n.g,n.b),r.push(o.r,o.g,o.b))}s.setAttribute("position",new bn(i,3)),s.setAttribute("color",new bn(r,3));super(s,new Sa({vertexColors:!0,depthTest:!1,depthWrite:!1,toneMapped:!1,transparent:!0})),this.isSkeletonHelper=!0,this.type="SkeletonHelper",this.root=t,this.bones=e,this.matrix=t.matrixWorld,this.matrixAutoUpdate=!1}updateMatrixWorld(t){const e=this.bones,s=this.geometry,i=s.getAttribute("position");qu.copy(this.root.matrixWorld).invert();for(let t=0,s=0;t<e.length;t++){const r=e[t];r.parent&&r.parent.isBone&&(Hu.multiplyMatrices(qu,r.matrixWorld),Du.setFromMatrixPosition(Hu),i.setXYZ(s,Du.x,Du.y,Du.z),Hu.multiplyMatrices(qu,r.parent.matrixWorld),Du.setFromMatrixPosition(Hu),i.setXYZ(s+1,Du.x,Du.y,Du.z),s+=2)}s.getAttribute("position").needsUpdate=!0,super.updateMatrixWorld(t)}dispose(){this.geometry.dispose(),this.material.dispose()}}function Xu(t){const e=[];!0===t.isBone&&e.push(t);for(let s=0;s<t.children.length;s++)e.push.apply(e,Xu(t.children[s]));return e}class Yu extends Vn{constructor(t,e,s){super(new ml(e,4,2),new en({wireframe:!0,fog:!1,toneMapped:!1})),this.light=t,this.color=s,this.type="PointLightHelper",this.matrix=this.light.matrixWorld,this.matrixAutoUpdate=!1,this.update()}dispose(){this.geometry.dispose(),this.material.dispose()}update(){this.light.updateWorldMatrix(!0,!1),void 0!==this.color?this.material.color.set(this.color):this.material.color.copy(this.light.color)}}const Zu=new Ii,Gu=new $r,$u=new $r;class Qu extends Rr{constructor(t,e,s){super(),this.light=t,this.matrix=t.matrixWorld,this.matrixAutoUpdate=!1,this.color=s,this.type="HemisphereLightHelper";const i=new cl(e);i.rotateY(.5*Math.PI),this.material=new en({wireframe:!0,fog:!1,toneMapped:!1}),void 0===this.color&&(this.material.vertexColors=!0);const r=i.getAttribute("position"),n=new Float32Array(3*r.count);i.setAttribute("color",new cn(n,3)),this.add(new Vn(i,this.material)),this.update()}dispose(){this.children[0].geometry.dispose(),this.children[0].material.dispose()}update(){const t=this.children[0];if(void 0!==this.color)this.material.color.set(this.color);else{const e=t.geometry.getAttribute("color");Gu.copy(this.light.color),$u.copy(this.light.groundColor);for(let t=0,s=e.count;t<s;t++){const i=t<s/2?Gu:$u;e.setXYZ(t,i.r,i.g,i.b)}e.needsUpdate=!0}this.light.updateWorldMatrix(!0,!1),t.lookAt(Zu.setFromMatrixPosition(this.light.matrixWorld).negate())}}class Ku extends Oa{constructor(t=10,e=10,s=4473924,i=8947848){s=new $r(s),i=new $r(i);const r=e/2,n=t/e,o=t/2,a=[],h=[];for(let t=0,l=0,c=-o;t<=e;t++,c+=n){a.push(-o,0,c,o,0,c),a.push(c,0,-o,c,0,o);const e=t===r?s:i;e.toArray(h,l),l+=3,e.toArray(h,l),l+=3,e.toArray(h,l),l+=3,e.toArray(h,l),l+=3}const l=new zn;l.setAttribute("position",new bn(a,3)),l.setAttribute("color",new bn(h,3));super(l,new Sa({vertexColors:!0,toneMapped:!1})),this.type="GridHelper"}dispose(){this.geometry.dispose(),this.material.dispose()}}class td extends Oa{constructor(t=10,e=16,s=8,i=64,r=4473924,n=8947848){r=new $r(r),n=new $r(n);const o=[],a=[];if(e>1)for(let s=0;s<e;s++){const i=s/e*(2*Math.PI),h=Math.sin(i)*t,l=Math.cos(i)*t;o.push(0,0,0),o.push(h,0,l);const c=1&s?r:n;a.push(c.r,c.g,c.b),a.push(c.r,c.g,c.b)}for(let e=0;e<s;e++){const h=1&e?r:n,l=t-t/s*e;for(let t=0;t<i;t++){let e=t/i*(2*Math.PI),s=Math.sin(e)*l,r=Math.cos(e)*l;o.push(s,0,r),a.push(h.r,h.g,h.b),e=(t+1)/i*(2*Math.PI),s=Math.sin(e)*l,r=Math.cos(e)*l,o.push(s,0,r),a.push(h.r,h.g,h.b)}}const h=new zn;h.setAttribute("position",new bn(o,3)),h.setAttribute("color",new bn(a,3));super(h,new Sa({vertexColors:!0,toneMapped:!1})),this.type="PolarGridHelper"}dispose(){this.geometry.dispose(),this.material.dispose()}}const ed=new Ii,sd=new Ii,id=new Ii;class rd extends Rr{constructor(t,e,s){super(),this.light=t,this.matrix=t.matrixWorld,this.matrixAutoUpdate=!1,this.color=s,this.type="DirectionalLightHelper",void 0===e&&(e=1);let i=new zn;i.setAttribute("position",new bn([-e,e,0,e,e,0,e,-e,0,-e,-e,0,-e,e,0],3));const r=new Sa({fog:!1,toneMapped:!1});this.lightPlane=new ka(i,r),this.add(this.lightPlane),i=new zn,i.setAttribute("position",new bn([0,0,0,0,0,1],3)),this.targetLine=new ka(i,r),this.add(this.targetLine),this.update()}dispose(){this.lightPlane.geometry.dispose(),this.lightPlane.material.dispose(),this.targetLine.geometry.dispose(),this.targetLine.material.dispose()}update(){this.light.updateWorldMatrix(!0,!1),this.light.target.updateWorldMatrix(!0,!1),ed.setFromMatrixPosition(this.light.matrixWorld),sd.setFromMatrixPosition(this.light.target.matrixWorld),id.subVectors(sd,ed),this.lightPlane.lookAt(sd),void 0!==this.color?(this.lightPlane.material.color.set(this.color),this.targetLine.material.color.set(this.color)):(this.lightPlane.material.color.copy(this.light.color),this.targetLine.material.color.copy(this.light.color)),this.targetLine.lookAt(sd),this.targetLine.scale.z=id.length()}}const nd=new Ii,od=new Xn;class ad extends Oa{constructor(t){const e=new zn,s=new Sa({color:16777215,vertexColors:!0,toneMapped:!1}),i=[],r=[],n={};function o(t,e){a(t),a(e)}function a(t){i.push(0,0,0),r.push(0,0,0),void 0===n[t]&&(n[t]=[]),n[t].push(i.length/3-1)}o("n1","n2"),o("n2","n4"),o("n4","n3"),o("n3","n1"),o("f1","f2"),o("f2","f4"),o("f4","f3"),o("f3","f1"),o("n1","f1"),o("n2","f2"),o("n3","f3"),o("n4","f4"),o("p","n1"),o("p","n2"),o("p","n3"),o("p","n4"),o("u1","u2"),o("u2","u3"),o("u3","u1"),o("c","t"),o("p","c"),o("cn1","cn2"),o("cn3","cn4"),o("cf1","cf2"),o("cf3","cf4"),e.setAttribute("position",new bn(i,3)),e.setAttribute("color",new bn(r,3)),super(e,s),this.type="CameraHelper",this.camera=t,this.camera.updateProjectionMatrix&&this.camera.updateProjectionMatrix(),this.matrix=t.matrixWorld,this.matrixAutoUpdate=!1,this.pointMap=n,this.update();const h=new $r(16755200),l=new $r(16711680),c=new $r(43775),u=new $r(16777215),d=new $r(3355443);this.setColors(h,l,c,u,d)}setColors(t,e,s,i,r){const n=this.geometry.getAttribute("color");n.setXYZ(0,t.r,t.g,t.b),n.setXYZ(1,t.r,t.g,t.b),n.setXYZ(2,t.r,t.g,t.b),n.setXYZ(3,t.r,t.g,t.b),n.setXYZ(4,t.r,t.g,t.b),n.setXYZ(5,t.r,t.g,t.b),n.setXYZ(6,t.r,t.g,t.b),n.setXYZ(7,t.r,t.g,t.b),n.setXYZ(8,t.r,t.g,t.b),n.setXYZ(9,t.r,t.g,t.b),n.setXYZ(10,t.r,t.g,t.b),n.setXYZ(11,t.r,t.g,t.b),n.setXYZ(12,t.r,t.g,t.b),n.setXYZ(13,t.r,t.g,t.b),n.setXYZ(14,t.r,t.g,t.b),n.setXYZ(15,t.r,t.g,t.b),n.setXYZ(16,t.r,t.g,t.b),n.setXYZ(17,t.r,t.g,t.b),n.setXYZ(18,t.r,t.g,t.b),n.setXYZ(19,t.r,t.g,t.b),n.setXYZ(20,t.r,t.g,t.b),n.setXYZ(21,t.r,t.g,t.b),n.setXYZ(22,t.r,t.g,t.b),n.setXYZ(23,t.r,t.g,t.b),n.setXYZ(24,e.r,e.g,e.b),n.setXYZ(25,e.r,e.g,e.b),n.setXYZ(26,e.r,e.g,e.b),n.setXYZ(27,e.r,e.g,e.b),n.setXYZ(28,e.r,e.g,e.b),n.setXYZ(29,e.r,e.g,e.b),n.setXYZ(30,e.r,e.g,e.b),n.setXYZ(31,e.r,e.g,e.b),n.setXYZ(32,s.r,s.g,s.b),n.setXYZ(33,s.r,s.g,s.b),n.setXYZ(34,s.r,s.g,s.b),n.setXYZ(35,s.r,s.g,s.b),n.setXYZ(36,s.r,s.g,s.b),n.setXYZ(37,s.r,s.g,s.b),n.setXYZ(38,i.r,i.g,i.b),n.setXYZ(39,i.r,i.g,i.b),n.setXYZ(40,r.r,r.g,r.b),n.setXYZ(41,r.r,r.g,r.b),n.setXYZ(42,r.r,r.g,r.b),n.setXYZ(43,r.r,r.g,r.b),n.setXYZ(44,r.r,r.g,r.b),n.setXYZ(45,r.r,r.g,r.b),n.setXYZ(46,r.r,r.g,r.b),n.setXYZ(47,r.r,r.g,r.b),n.setXYZ(48,r.r,r.g,r.b),n.setXYZ(49,r.r,r.g,r.b),n.needsUpdate=!0}update(){const t=this.geometry,e=this.pointMap;od.projectionMatrixInverse.copy(this.camera.projectionMatrixInverse);const s=this.camera.coordinateSystem===Os?-1:0;hd("c",e,t,od,0,0,s),hd("t",e,t,od,0,0,1),hd("n1",e,t,od,-1,-1,s),hd("n2",e,t,od,1,-1,s),hd("n3",e,t,od,-1,1,s),hd("n4",e,t,od,1,1,s),hd("f1",e,t,od,-1,-1,1),hd("f2",e,t,od,1,-1,1),hd("f3",e,t,od,-1,1,1),hd("f4",e,t,od,1,1,1),hd("u1",e,t,od,.7,1.1,s),hd("u2",e,t,od,-.7,1.1,s),hd("u3",e,t,od,0,2,s),hd("cf1",e,t,od,-1,0,1),hd("cf2",e,t,od,1,0,1),hd("cf3",e,t,od,0,-1,1),hd("cf4",e,t,od,0,1,1),hd("cn1",e,t,od,-1,0,s),hd("cn2",e,t,od,1,0,s),hd("cn3",e,t,od,0,-1,s),hd("cn4",e,t,od,0,1,s),t.getAttribute("position").needsUpdate=!0}dispose(){this.geometry.dispose(),this.material.dispose()}}function hd(t,e,s,i,r,n,o){nd.set(r,n,o).unproject(i);const a=e[t];if(void 0!==a){const t=s.getAttribute("position");for(let e=0,s=a.length;e<s;e++)t.setXYZ(a[e],nd.x,nd.y,nd.z)}}const ld=new Ri;class cd extends Oa{constructor(t,e=16776960){const s=new Uint16Array([0,1,1,2,2,3,3,0,4,5,5,6,6,7,7,4,0,4,1,5,2,6,3,7]),i=new Float32Array(24),r=new zn;r.setIndex(new cn(s,1)),r.setAttribute("position",new cn(i,3)),super(r,new Sa({color:e,toneMapped:!1})),this.object=t,this.type="BoxHelper",this.matrixAutoUpdate=!1,this.update()}update(t){if(void 0!==t&&console.warn("THREE.BoxHelper: .update() has no longer arguments."),void 0!==this.object&&ld.setFromObject(this.object),ld.isEmpty())return;const e=ld.min,s=ld.max,i=this.geometry.attributes.position,r=i.array;r[0]=s.x,r[1]=s.y,r[2]=s.z,r[3]=e.x,r[4]=s.y,r[5]=s.z,r[6]=e.x,r[7]=e.y,r[8]=s.z,r[9]=s.x,r[10]=e.y,r[11]=s.z,r[12]=s.x,r[13]=s.y,r[14]=e.z,r[15]=e.x,r[16]=s.y,r[17]=e.z,r[18]=e.x,r[19]=e.y,r[20]=e.z,r[21]=s.x,r[22]=e.y,r[23]=e.z,i.needsUpdate=!0,this.geometry.computeBoundingSphere()}setFromObject(t){return this.object=t,this.update(),this}copy(t,e){return super.copy(t,e),this.object=t.object,this}dispose(){this.geometry.dispose(),this.material.dispose()}}class ud extends Oa{constructor(t,e=16776960){const s=new Uint16Array([0,1,1,2,2,3,3,0,4,5,5,6,6,7,7,4,0,4,1,5,2,6,3,7]),i=new zn;i.setIndex(new cn(s,1)),i.setAttribute("position",new bn([1,1,1,-1,1,1,-1,-1,1,1,-1,1,1,1,-1,-1,1,-1,-1,-1,-1,1,-1,-1],3)),super(i,new Sa({color:e,toneMapped:!1})),this.box=t,this.type="Box3Helper",this.geometry.computeBoundingSphere()}updateMatrixWorld(t){const e=this.box;e.isEmpty()||(e.getCenter(this.position),e.getSize(this.scale),this.scale.multiplyScalar(.5),super.updateMatrixWorld(t))}dispose(){this.geometry.dispose(),this.material.dispose()}}class dd extends ka{constructor(t,e=1,s=16776960){const i=s,r=new zn;r.setAttribute("position",new bn([1,-1,0,-1,1,0,-1,-1,0,1,1,0,-1,1,0,-1,-1,0,1,-1,0,1,1,0],3)),r.computeBoundingSphere(),super(r,new Sa({color:i,toneMapped:!1})),this.type="PlaneHelper",this.plane=t,this.size=e;const n=new zn;n.setAttribute("position",new bn([1,1,0,-1,1,0,-1,-1,0,1,1,0,-1,-1,0,1,-1,0],3)),n.computeBoundingSphere(),this.add(new Vn(n,new en({color:i,opacity:.2,transparent:!0,depthWrite:!1,toneMapped:!1})))}updateMatrixWorld(t){this.position.set(0,0,0),this.scale.set(.5*this.size,.5*this.size,1),this.lookAt(this.plane.normal),this.translateZ(-this.plane.constant),super.updateMatrixWorld(t)}dispose(){this.geometry.dispose(),this.material.dispose(),this.children[0].geometry.dispose(),this.children[0].material.dispose()}}const pd=new Ii;let md,yd;class fd extends Rr{constructor(t=new Ii(0,0,1),e=new Ii(0,0,0),s=1,i=16776960,r=.2*s,n=.2*r){super(),this.type="ArrowHelper",void 0===md&&(md=new zn,md.setAttribute("position",new bn([0,0,0,0,1,0],3)),yd=new Sh(0,.5,1,5,1),yd.translate(0,-.5,0)),this.position.copy(e),this.line=new ka(md,new Sa({color:i,toneMapped:!1})),this.line.matrixAutoUpdate=!1,this.add(this.line),this.cone=new Vn(yd,new en({color:i,toneMapped:!1})),this.cone.matrixAutoUpdate=!1,this.add(this.cone),this.setDirection(t),this.setLength(s,r,n)}setDirection(t){if(t.y>.99999)this.quaternion.set(0,0,0,1);else if(t.y<-.99999)this.quaternion.set(1,0,0,0);else{pd.set(t.z,0,-t.x).normalize();const e=Math.acos(t.y);this.quaternion.setFromAxisAngle(pd,e)}}setLength(t,e=.2*t,s=.2*e){this.line.scale.set(1,Math.max(1e-4,t-e),1),this.line.updateMatrix(),this.cone.scale.set(s,e,s),this.cone.position.y=t,this.cone.updateMatrix()}setColor(t){this.line.material.color.set(t),this.cone.material.color.set(t)}copy(t){return super.copy(t,!1),this.line.copy(t.line),this.cone.copy(t.cone),this}dispose(){this.line.geometry.dispose(),this.line.material.dispose(),this.cone.geometry.dispose(),this.cone.material.dispose()}}class gd extends Oa{constructor(t=1){const e=[0,0,0,t,0,0,0,0,0,0,t,0,0,0,0,0,0,t],s=new zn;s.setAttribute("position",new bn(e,3)),s.setAttribute("color",new bn([1,0,0,1,.6,0,0,1,0,.6,1,0,0,0,1,0,.6,1],3));super(s,new Sa({vertexColors:!0,toneMapped:!1})),this.type="AxesHelper"}setColors(t,e,s){const i=new $r,r=this.geometry.attributes.color.array;return i.set(t),i.toArray(r,0),i.toArray(r,3),i.set(e),i.toArray(r,6),i.toArray(r,9),i.set(s),i.toArray(r,12),i.toArray(r,15),this.geometry.attributes.color.needsUpdate=!0,this}dispose(){this.geometry.dispose(),this.material.dispose()}}class xd{constructor(){this.type="ShapePath",this.color=new $r,this.subPaths=[],this.currentPath=null}moveTo(t,e){return this.currentPath=new bh,this.subPaths.push(this.currentPath),this.currentPath.moveTo(t,e),this}lineTo(t,e){return this.currentPath.lineTo(t,e),this}quadraticCurveTo(t,e,s,i){return this.currentPath.quadraticCurveTo(t,e,s,i),this}bezierCurveTo(t,e,s,i,r,n){return this.currentPath.bezierCurveTo(t,e,s,i,r,n),this}splineThru(t){return this.currentPath.splineThru(t),this}toShapes(t){function e(t,e){const s=e.length;let i=!1;for(let r=s-1,n=0;n<s;r=n++){let s=e[r],o=e[n],a=o.x-s.x,h=o.y-s.y;if(Math.abs(h)>Number.EPSILON){if(h<0&&(s=e[n],a=-a,o=e[r],h=-h),t.y<s.y||t.y>o.y)continue;if(t.y===s.y){if(t.x===s.x)return!0}else{const e=h*(t.x-s.x)-a*(t.y-s.y);if(0===e)return!0;if(e<0)continue;i=!i}}else{if(t.y!==s.y)continue;if(o.x<=t.x&&t.x<=s.x||s.x<=t.x&&t.x<=o.x)return!0}}return i}const s=rl.isClockWise,i=this.subPaths;if(0===i.length)return[];let r,n,o;const a=[];if(1===i.length)return n=i[0],o=new Rh,o.curves=n.curves,a.push(o),a;let h=!s(i[0].getPoints());h=t?!h:h;const l=[],c=[];let u,d,p=[],m=0;c[m]=void 0,p[m]=[];for(let e=0,o=i.length;e<o;e++)n=i[e],u=n.getPoints(),r=s(u),r=t?!r:r,r?(!h&&c[m]&&m++,c[m]={s:new Rh,p:u},c[m].s.curves=n.curves,h&&m++,p[m]=[]):p[m].push({h:n,p:u[0]});if(!c[0])return function(t){const e=[];for(let s=0,i=t.length;s<i;s++){const i=t[s],r=new Rh;r.curves=i.curves,e.push(r)}return e}(i);if(c.length>1){let t=!1,s=0;for(let t=0,e=c.length;t<e;t++)l[t]=[];for(let i=0,r=c.length;i<r;i++){const r=p[i];for(let n=0;n<r.length;n++){const o=r[n];let a=!0;for(let r=0;r<c.length;r++)e(o.p,c[r].p)&&(i!==r&&s++,a?(a=!1,l[r].push(o)):t=!0);a&&l[i].push(o)}}s>0&&!1===t&&(p=l)}for(let t=0,e=c.length;t<e;t++){o=c[t].s,a.push(o),d=p[t];for(let t=0,e=d.length;t<e;t++)o.holes.push(d[t].h)}return a}}class bd extends Ns{constructor(t,e=null){super(),this.object=t,this.domElement=e,this.enabled=!0,this.state=-1,this.keys={},this.mouseButtons={LEFT:null,MIDDLE:null,RIGHT:null},this.touches={ONE:null,TWO:null}}connect(){}disconnect(){}dispose(){}update(){}}function vd(t,e,s,i){const r=function(t){switch(t){case Tt:case zt:return{byteLength:1,components:1};case It:case Ct:case Et:return{byteLength:2,components:1};case Pt:case Ot:return{byteLength:2,components:4};case kt:case Bt:case Rt:return{byteLength:4,components:1};case Nt:return{byteLength:4,components:3}}throw new Error(`Unknown texture type ${t}.`)}(i);switch(s){case 1021:case 1024:return t*e;case 1025:return t*e*2;case qt:case Jt:return t*e/r.components*r.byteLength;case 1030:case 1031:return t*e*2/r.components*r.byteLength;case 1022:return t*e*3/r.components*r.byteLength;case Wt:case 1033:return t*e*4/r.components*r.byteLength;case 33776:case 33777:return Math.floor((t+3)/4)*Math.floor((e+3)/4)*8;case 33778:case 33779:return Math.floor((t+3)/4)*Math.floor((e+3)/4)*16;case 35841:case 35843:return Math.max(t,16)*Math.max(e,8)/4;case 35840:case 35842:return Math.max(t,8)*Math.max(e,8)/2;case 36196:case 37492:return Math.floor((t+3)/4)*Math.floor((e+3)/4)*8;case 37496:case 37808:return Math.floor((t+3)/4)*Math.floor((e+3)/4)*16;case 37809:return Math.floor((t+4)/5)*Math.floor((e+3)/4)*16;case 37810:return Math.floor((t+4)/5)*Math.floor((e+4)/5)*16;case 37811:return Math.floor((t+5)/6)*Math.floor((e+4)/5)*16;case 37812:return Math.floor((t+5)/6)*Math.floor((e+5)/6)*16;case 37813:return Math.floor((t+7)/8)*Math.floor((e+4)/5)*16;case 37814:return Math.floor((t+7)/8)*Math.floor((e+5)/6)*16;case 37815:return Math.floor((t+7)/8)*Math.floor((e+7)/8)*16;case 37816:return Math.floor((t+9)/10)*Math.floor((e+4)/5)*16;case 37817:return Math.floor((t+9)/10)*Math.floor((e+5)/6)*16;case 37818:return Math.floor((t+9)/10)*Math.floor((e+7)/8)*16;case 37819:return Math.floor((t+9)/10)*Math.floor((e+9)/10)*16;case 37820:return Math.floor((t+11)/12)*Math.floor((e+9)/10)*16;case 37821:return Math.floor((t+11)/12)*Math.floor((e+11)/12)*16;case 36492:case 36494:case 36495:return Math.ceil(t/4)*Math.ceil(e/4)*16;case 36283:case 36284:return Math.ceil(t/4)*Math.ceil(e/4)*8;case 36285:case 36286:return Math.ceil(t/4)*Math.ceil(e/4)*16}throw new Error(`Unable to determine texture byte length for ${s} format.`)}const wd={contain:function(t,e){const s=t.image&&t.image.width?t.image.width/t.image.height:1;return s>e?(t.repeat.x=1,t.repeat.y=s/e,t.offset.x=0,t.offset.y=(1-t.repeat.y)/2):(t.repeat.x=e/s,t.repeat.y=1,t.offset.x=(1-t.repeat.x)/2,t.offset.y=0),t},cover:function(t,e){const s=t.image&&t.image.width?t.image.width/t.image.height:1;return s>e?(t.repeat.x=e/s,t.repeat.y=1,t.offset.x=(1-t.repeat.x)/2,t.offset.y=0):(t.repeat.x=1,t.repeat.y=s/e,t.offset.x=0,t.offset.y=(1-t.repeat.y)/2),t},fill:function(t){return t.repeat.x=1,t.repeat.y=1,t.offset.x=0,t.offset.y=0,t},getByteLength:vd};"undefined"!=typeof __THREE_DEVTOOLS__&&__THREE_DEVTOOLS__.dispatchEvent(new CustomEvent("register",{detail:{revision:t}})),"undefined"!=typeof window&&(window.__THREE__?console.warn("WARNING: Multiple instances of Three.js being imported."):window.__THREE__=t);export{et as ACESFilmicToneMapping,v as AddEquation,G as AddOperation,Ve as AdditiveAnimationBlendMode,f as AdditiveBlending,it as AgXToneMapping,Lt as AlphaFormat,Ss as AlwaysCompare,j as AlwaysDepth,ys as AlwaysStencilFunc,Ic as AmbientLight,bu as AnimationAction,Ql as AnimationClip,ac as AnimationLoader,wu as AnimationMixer,xu as AnimationObjectGroup,Vl as AnimationUtils,th as ArcCurve,Zc as ArrayCamera,fd as ArrowHelper,nt as AttachedBindMode,iu as Audio,lu as AudioAnalyser,Dc as AudioContext,su as AudioListener,Hc as AudioLoader,gd as AxesHelper,d as BackSide,De as BasicDepthPacking,a as BasicShadowMap,Ma as BatchedMesh,Lo as Bone,ql as BooleanKeyframeTrack,Nu as Box2,Ri as Box3,ud as Box3Helper,jn as BoxGeometry,cd as BoxHelper,cn as BufferAttribute,zn as BufferGeometry,Fc as BufferGeometryLoader,zt as ByteType,tc as Cache,Xn as Camera,ad as CameraHelper,Ga as CanvasTexture,wh as CapsuleGeometry,oh as CatmullRomCurve3,tt as CineonToneMapping,Mh as CircleGeometry,mt as ClampToEdgeWrapping,Gc as Clock,$r as Color,Jl as ColorKeyframeTrack,ui as ColorManagement,Ya as CompressedArrayTexture,Za as CompressedCubeTexture,Xa as CompressedTexture,hc as CompressedTextureLoader,_h as ConeGeometry,L as ConstantAlphaFactor,F as ConstantColorFactor,bd as Controls,Kn as CubeCamera,ht as CubeReflectionMapping,lt as CubeRefractionMapping,to as CubeTexture,cc as CubeTextureLoader,dt as CubeUVReflectionMapping,ch as CubicBezierCurve,uh as CubicBezierCurve3,jl as CubicInterpolant,r as CullFaceBack,n as CullFaceFront,o as CullFaceFrontBack,i as CullFaceNone,Qa as Curve,xh as CurvePath,b as CustomBlending,st as CustomToneMapping,Sh as CylinderGeometry,Pu as Cylindrical,Ti as Data3DTexture,_i as DataArrayTexture,Vo as DataTexture,uc as DataTextureLoader,an as DataUtils,rs as DecrementStencilOp,os as DecrementWrapStencilOp,sc as DefaultLoadingManager,Dt as DepthFormat,Ht as DepthStencilFormat,$a as DepthTexture,ot as DetachedBindMode,Cc as DirectionalLight,rd as DirectionalLightHelper,Dl as DiscreteInterpolant,Th as DodecahedronGeometry,p as DoubleSide,k as DstAlphaFactor,E as DstColorFactor,ks as DynamicCopyUsage,As as DynamicDrawUsage,Cs as DynamicReadUsage,kh as EdgesGeometry,Ka as EllipseCurve,xs as EqualCompare,H as EqualDepth,cs as EqualStencilFunc,ct as EquirectangularReflectionMapping,ut as EquirectangularRefractionMapping,yr as Euler,Ns as EventDispatcher,al as ExtrudeGeometry,oc as FileLoader,xn as Float16BufferAttribute,bn as Float32BufferAttribute,Rt as FloatType,io as Fog,so as FogExp2,Ja as FramebufferTexture,u as FrontSide,ra as Frustum,Cu as GLBufferAttribute,Es as GLSL1,Ps as GLSL3,vs as GreaterCompare,J as GreaterDepth,Ms as GreaterEqualCompare,q as GreaterEqualDepth,ms as GreaterEqualStencilFunc,ds as GreaterStencilFunc,Ku as GridHelper,Ha as Group,Et as HalfFloatType,mc as HemisphereLight,Qu as HemisphereLightHelper,ll as IcosahedronGeometry,jc as ImageBitmapLoader,lc as ImageLoader,yi as ImageUtils,is as IncrementStencilOp,ns as IncrementWrapStencilOp,Do as InstancedBufferAttribute,Oc as InstancedBufferGeometry,zu as InstancedInterleavedBuffer,$o as InstancedMesh,mn as Int16BufferAttribute,fn as Int32BufferAttribute,un as Int8BufferAttribute,Bt as IntType,no as InterleavedBuffer,ao as InterleavedBufferAttribute,Wl as Interpolant,Re as InterpolateDiscrete,Ee as InterpolateLinear,Pe as InterpolateSmooth,as as InvertStencilOp,es as KeepStencilOp,Hl as KeyframeTrack,zo as LOD,vh as LatheGeometry,fr as Layers,gs as LessCompare,U as LessDepth,bs as LessEqualCompare,D as LessEqualDepth,us as LessEqualStencilFunc,ls as LessStencilFunc,pc as Light,Rc as LightProbe,ka as Line,Wu as Line3,Sa as LineBasicMaterial,dh as LineCurve,ph as LineCurve3,El as LineDashedMaterial,Fa as LineLoop,Oa as LineSegments,wt as LinearFilter,Ul as LinearInterpolant,At as LinearMipMapLinearFilter,St as LinearMipMapNearestFilter,_t as LinearMipmapLinearFilter,Mt as LinearMipmapNearestFilter,$e as LinearSRGBColorSpace,Q as LinearToneMapping,Qe as LinearTransfer,ic as Loader,Pc as LoaderUtils,ec as LoadingManager,Ie as LoopOnce,ke as LoopPingPong,Be as LoopRepeat,Ut as LuminanceAlphaFormat,jt as LuminanceFormat,e as MOUSE,tn as Material,Ec as MaterialLoader,Ys as MathUtils,Ou as Matrix2,Gs as Matrix3,nr as Matrix4,_ as MaxEquation,Vn as Mesh,en as MeshBasicMaterial,Bl as MeshDepthMaterial,kl as MeshDistanceMaterial,Il as MeshLambertMaterial,Rl as MeshMatcapMaterial,Cl as MeshNormalMaterial,Tl as MeshPhongMaterial,Al as MeshPhysicalMaterial,_l as MeshStandardMaterial,zl as MeshToonMaterial,S as MinEquation,yt as MirroredRepeatWrapping,Z as MixOperation,x as MultiplyBlending,Y as MultiplyOperation,ft as NearestFilter,vt as NearestMipMapLinearFilter,xt as NearestMipMapNearestFilter,bt as NearestMipmapLinearFilter,gt as NearestMipmapNearestFilter,rt as NeutralToneMapping,fs as NeverCompare,W as NeverDepth,hs as NeverStencilFunc,m as NoBlending,Ze as NoColorSpace,$ as NoToneMapping,Le as NormalAnimationBlendMode,y as NormalBlending,ws as NotEqualCompare,X as NotEqualDepth,ps as NotEqualStencilFunc,Xl as NumberKeyframeTrack,Rr as Object3D,Nc as ObjectLoader,Ye as ObjectSpaceNormalMap,cl as OctahedronGeometry,T as OneFactor,V as OneMinusConstantAlphaFactor,N as OneMinusConstantColorFactor,R as OneMinusDstAlphaFactor,P as OneMinusDstColorFactor,B as OneMinusSrcAlphaFactor,C as OneMinusSrcColorFactor,Tc as OrthographicCamera,h as PCFShadowMap,l as PCFSoftShadowMap,bh as Path,$n as PerspectiveCamera,ea as Plane,ul as PlaneGeometry,dd as PlaneHelper,Ac as PointLight,Yu as PointLightHelper,Ua as Points,Na as PointsMaterial,td as PolarGridHelper,Ah as PolyhedronGeometry,hu as PositionalAudio,gu as PropertyBinding,cu as PropertyMixer,mh as QuadraticBezierCurve,yh as QuadraticBezierCurve3,Ci as Quaternion,Zl as QuaternionKeyframeTrack,Yl as QuaternionLinearInterpolant,js as RAD2DEG,ze as RED_GREEN_RGTC2_Format,Ae as RED_RGTC1_Format,t as REVISION,He as RGBADepthPacking,Wt as RGBAFormat,Gt as RGBAIntegerFormat,be as RGBA_ASTC_10x10_Format,fe as RGBA_ASTC_10x5_Format,ge as RGBA_ASTC_10x6_Format,xe as RGBA_ASTC_10x8_Format,ve as RGBA_ASTC_12x10_Format,we as RGBA_ASTC_12x12_Format,he as RGBA_ASTC_4x4_Format,le as RGBA_ASTC_5x4_Format,ce as RGBA_ASTC_5x5_Format,ue as RGBA_ASTC_6x5_Format,de as RGBA_ASTC_6x6_Format,pe as RGBA_ASTC_8x5_Format,me as RGBA_ASTC_8x6_Format,ye as RGBA_ASTC_8x8_Format,Me as RGBA_BPTC_Format,ae as RGBA_ETC2_EAC_Format,re as RGBA_PVRTC_2BPPV1_Format,ie as RGBA_PVRTC_4BPPV1_Format,Qt as RGBA_S3TC_DXT1_Format,Kt as RGBA_S3TC_DXT3_Format,te as RGBA_S3TC_DXT5_Format,qe as RGBDepthPacking,Vt as RGBFormat,Zt as RGBIntegerFormat,Se as RGB_BPTC_SIGNED_Format,_e as RGB_BPTC_UNSIGNED_Format,ne as RGB_ETC1_Format,oe as RGB_ETC2_Format,se as RGB_PVRTC_2BPPV1_Format,ee as RGB_PVRTC_4BPPV1_Format,$t as RGB_S3TC_DXT1_Format,Je as RGDepthPacking,Xt as RGFormat,Yt as RGIntegerFormat,Sl as RawShaderMaterial,rr as Ray,Bu as Raycaster,Bc as RectAreaLight,qt as RedFormat,Jt as RedIntegerFormat,K as ReinhardToneMapping,Mi as RenderTarget,Mu as RenderTarget3D,Su as RenderTargetArray,pt as RepeatWrapping,ss as ReplaceStencilOp,M as ReverseSubtractEquation,dl as RingGeometry,Ce as SIGNED_RED_GREEN_RGTC2_Format,Te as SIGNED_RED_RGTC1_Format,Ge as SRGBColorSpace,Ke as SRGBTransfer,ro as Scene,Jn as ShaderMaterial,Ml as ShadowMaterial,Rh as Shape,pl as ShapeGeometry,xd as ShapePath,rl as ShapeUtils,Ct as ShortType,Uo as Skeleton,Ju as SkeletonHelper,No as SkinnedMesh,gi as Source,Gi as Sphere,ml as SphereGeometry,Eu as Spherical,kc as SphericalHarmonics3,fh as SplineCurve,vc as SpotLight,Uu as SpotLightHelper,So as Sprite,ho as SpriteMaterial,I as SrcAlphaFactor,O as SrcAlphaSaturateFactor,z as SrcColorFactor,Bs as StaticCopyUsage,_s as StaticDrawUsage,zs as StaticReadUsage,Yc as StereoCamera,Rs as StreamCopyUsage,Ts as StreamDrawUsage,Is as StreamReadUsage,Gl as StringKeyframeTrack,w as SubtractEquation,g as SubtractiveBlending,s as TOUCH,Xe as TangentSpaceNormalMap,yl as TetrahedronGeometry,vi as Texture,dc as TextureLoader,wd as TextureUtils,fl as TorusGeometry,gl as TorusKnotGeometry,Jr as Triangle,Ue as TriangleFanDrawMode,je as TriangleStripDrawMode,We as TrianglesDrawMode,xl as TubeGeometry,at as UVMapping,yn as Uint16BufferAttribute,gn as Uint32BufferAttribute,dn as Uint8BufferAttribute,pn as Uint8ClampedBufferAttribute,_u as Uniform,Tu as UniformsGroup,qn as UniformsUtils,Tt as UnsignedByteType,Ft as UnsignedInt248Type,Nt as UnsignedInt5999Type,kt as UnsignedIntType,Pt as UnsignedShort4444Type,Ot as UnsignedShort5551Type,It as UnsignedShortType,c as VSMShadowMap,Zs as Vector2,Ii as Vector3,wi as Vector4,$l as VectorKeyframeTrack,qa as VideoTexture,zi as WebGL3DRenderTarget,Ai as WebGLArrayRenderTarget,Os as WebGLCoordinateSystem,eo as WebGLCubeRenderTarget,Si as WebGLRenderTarget,Fs as WebGPUCoordinateSystem,bl as WireframeGeometry,Ne as WrapAroundEnding,Oe as ZeroCurvatureEnding,A as ZeroFactor,Fe as ZeroSlopeEnding,ts as ZeroStencilOp,Qs as arrayNeedsUint32,Un as cloneUniforms,si as createCanvasElement,ei as createElementNS,vd as getByteLength,Hn as getUnlitUniformColorSpace,Dn as mergeUniforms,ni as probeAsync,oi as toNormalizedProjectionMatrix,ai as toReversedProjectionMatrix,ri as warnOnce};
