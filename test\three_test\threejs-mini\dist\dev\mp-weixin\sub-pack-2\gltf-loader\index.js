"use strict";
const _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm = require("../../common/chunks/@dcloudio/uni-mp-weixin/dist/uni.api.esm.js");
const _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm = require("../../common/chunks/@dcloudio/uni-mp-vue/dist/vue.runtime.esm.js");
const _mpChunkDeps_three_build_three_module_min = require("../chunks/three/build/three.module.min.js");
const _mpChunkDeps_three_examples_jsm_controls_OrbitControls = require("../chunks/three/examples/jsm/controls/OrbitControls.js");
const _mpChunkDeps__minisheep_threePlatformAdapter_dist_threeOverride_jsm_loaders_KTX2Loader = require("../chunks/@minisheep/three-platform-adapter/dist/three-override/jsm/loaders/KTX2Loader.js");
const _mpChunkDeps_three_examples_jsm_loaders_GLTFLoader = require("../chunks/three/examples/jsm/loaders/GLTFLoader.js");
const _mpChunkDeps__minisheep_threePlatformAdapter_dist_threeOverride_jsm_libs_meshopt_decoder_module = require("../chunks/@minisheep/three-platform-adapter/dist/three-override/jsm/libs/meshopt_decoder.module.js");
const _mpChunkDeps_three_examples_jsm_environments_RoomEnvironment = require("../chunks/three/examples/jsm/environments/RoomEnvironment.js");
const _mpChunkDeps_three_build_three_core_min = require("../chunks/three/build/three.core.min.js");
if (!Math) {
  PlatformCanvas();
}
const PlatformCanvas = () => "../../components/PlatformCanvas.js";
const _sfc_main = {
  __name: "index",
  setup(__props) {
    function useCanvas({ canvas, useFrame, recomputeSize }) {
      const CANVAS_WIDTH = canvas.width;
      const CANVAS_HEIGHT = canvas.height;
      let camera, scene, renderer;
      init();
      render();
      function init() {
        renderer = new _mpChunkDeps_three_build_three_module_min.WebGLRenderer({ canvas, antialias: true });
        renderer.setPixelRatio(THREEGlobals.devicePixelRatio);
        renderer.setSize(CANVAS_WIDTH, CANVAS_HEIGHT);
        renderer.toneMapping = _mpChunkDeps_three_build_three_core_min.et;
        renderer.toneMappingExposure = 1;
        camera = new _mpChunkDeps_three_build_three_core_min.$n(45, CANVAS_WIDTH / CANVAS_HEIGHT, 1, 2e3);
        camera.position.set(0, 100, 0);
        const environment = new _mpChunkDeps_three_examples_jsm_environments_RoomEnvironment.RoomEnvironment();
        const pmremGenerator = new _mpChunkDeps_three_build_three_module_min.ei(renderer);
        scene = new _mpChunkDeps_three_build_three_core_min.ro();
        scene.background = new _mpChunkDeps_three_build_three_core_min.$r(12303291);
        scene.environment = pmremGenerator.fromScene(environment).texture;
        environment.dispose();
        const grid = new _mpChunkDeps_three_build_three_core_min.Ku(500, 10, 16777215, 16777215);
        grid.material.opacity = 0.5;
        grid.material.depthWrite = false;
        grid.material.transparent = true;
        scene.add(grid);
        const controls = new _mpChunkDeps_three_examples_jsm_controls_OrbitControls.OrbitControls(camera, renderer.domElement);
        controls.addEventListener("change", render);
        controls.minDistance = 400;
        controls.maxDistance = 1e3;
        controls.target.set(10, 90, -16);
        controls.update();
        loadGLBModel("http://minhandash.edu.izdax.cn/Teacher_001.glb");
        canvas.addEventListener("resize", onWindowResize);
      }
      function loadGLBModel(modelUrl) {
        scene.children.forEach((child) => {
          if (child.isScene || child.type === "Group") {
            scene.remove(child);
          }
        });
        const ktx2Loader = new _mpChunkDeps__minisheep_threePlatformAdapter_dist_threeOverride_jsm_loaders_KTX2Loader.ee().setWorkerLimit(1).setTranscoderPath("https://threejs.org/examples/jsm/libs/basis/").detectSupport(renderer);
        const loader = new _mpChunkDeps_three_examples_jsm_loaders_GLTFLoader.GLTFLoader();
        loader.setKTX2Loader(ktx2Loader);
        loader.setMeshoptDecoder(_mpChunkDeps__minisheep_threePlatformAdapter_dist_threeOverride_jsm_libs_meshopt_decoder_module.o);
        const url = modelUrl instanceof Blob ? URL.createObjectURL(modelUrl) : modelUrl;
        loader.load(url, function(gltf) {
          const model = gltf.scene;
          const animations = gltf.animations;
          _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.index.__f__("log", "at sub-pack-2/gltf-loader/index.vue:95", "animations", animations);
          _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.index.__f__("log", "at sub-pack-2/gltf-loader/index.vue:96", "gltf", gltf);
          model.scale.set(220, 220, 220);
          model.position.set(10, 50, 0);
          scene.add(model);
          render();
          if (modelUrl instanceof Blob) {
            URL.revokeObjectURL(url);
          }
        }, void 0, (error) => {
          _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.index.__f__("error", "at sub-pack-2/gltf-loader/index.vue:111", "加载模型失败:", error);
        });
      }
      function onWindowResize() {
        camera.aspect = canvas.clientWidth / canvas.clientHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(canvas.clientWidth, canvas.clientHeight);
        render();
      }
      function render() {
        renderer.render(scene, camera);
      }
    }
    return (_ctx, _cache) => {
      return {
        a: _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.o(useCanvas),
        b: _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.p({
          type: "webgl2",
          ["canvas-id"]: "webgl_loader_gltf_compressed"
        })
      };
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/sub-pack-2/gltf-loader/index.js.map
